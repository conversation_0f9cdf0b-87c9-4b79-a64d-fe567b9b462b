// For format details, see https://containers.dev/implementors/json_reference/
{
    "name": "conformii_dev",
    "dockerComposeFile": [
        "../local.yml"
    ],
    "init": true,
    "mounts": [
        {
            "source": "./.devcontainer/bash_history",
            "target": "/home/<USER>/.bash_history",
            "type": "bind"
        },
        {
            "source": "~/.ssh",
            "target": "/home/<USER>/.ssh",
            "type": "bind"
        }
    ],
    // Tells devcontainer.json supporting services / tools whether they should run
    // /bin/sh -c "while sleep 1000; do :; done" when starting the container instead of the container’s default command
    "overrideCommand": false,
    "service": "django",
    // "remoteEnv": {"PATH": "/home/<USER>/.local/bin:${containerEnv:PATH}"},
    "remoteUser": "dev-user",
    "workspaceFolder": "/app",
    // Set *default* container specific settings.json values on container create.
    "customizations": {
        "vscode": {
            "settings": {
                "editor.formatOnSave": true,
                "[python]": {
                    "analysis.autoImportCompletions": true,
                    "analysis.typeCheckingMode": "basic",
                    "defaultInterpreterPath": "/usr/local/bin/python",
                    "editor.codeActionsOnSave": {
                        "source.organizeImports": "always"
                    },
                    "editor.defaultFormatter": "charliermarsh.ruff",
                    "languageServer": "Pylance",
                    "linting.enabled": true,
                    "linting.mypyEnabled": true,
                    "linting.mypyPath": "/usr/local/bin/mypy",
                }
            },
            // https://code.visualstudio.com/docs/remote/devcontainerjson-reference#_vs-code-specific-properties
            // Add the IDs of extensions you want installed when the container is created.
            "extensions": [
                "davidanson.vscode-markdownlint",
                "mrmlnc.vscode-duplicate",
                "visualstudioexptteam.vscodeintellicode",
                "visualstudioexptteam.intellicode-api-usage-examples",
                // python
                "ms-python.python",
                "ms-python.vscode-pylance",
                "charliermarsh.ruff",
                // django
                "batisteo.vscode-django"
            ]
        }
    },
    // Uncomment the next line if you want start specific services in your Docker Compose config.
    // "runServices": [],
    // Uncomment the next line if you want to keep your containers running after VS Code shuts down.
    // "shutdownAction": "none",
    // Uncomment the next line to run commands after the container is created.
    "postCreateCommand": "cat .devcontainer/bashrc.override.sh >> ~/.bashrc"
}
