{
  // Use IntelliSense to learn about possible attributes.
  // Hover to view descriptions of existing attributes.
  // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Run Server",
      "type": "python",
      "request": "launch",
      "program": "${workspaceFolder}/manage.py",
      "justMyCode": false,
      "args": ["runserver"],
      "django": true
    },
    {
      "name": "Migrate Data from Complishield",
      "type": "python",
      "request": "launch",
      "program": "${workspaceFolder}/manage.py",
      "justMyCode": false,
      "args": ["migrate_complishield"],
      "django": true
    },
    {
      "name": "Publish Draft Complishield",
      "type": "python",
      "request": "launch",
      "program": "${workspaceFolder}/manage.py",
      "justMyCode": false,
      "args": ["complishield_publish_draft"],
      "django": true
    },
  ]
}
