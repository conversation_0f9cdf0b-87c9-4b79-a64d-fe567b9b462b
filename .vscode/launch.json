{
  // Use IntelliSense to learn about possible attributes.
  // Hover to view descriptions of existing attributes.
  // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Run Server",
      "type": "python",
      "request": "launch",
      "program": "${workspaceFolder}/manage.py",
      "justMyCode": false,
      "args": ["runserver"],
      "django": true
    },
    {
      "name": "Run Trips",
      "type": "python",
      "request": "launch",
      "program": "${workspaceFolder}/manage.py",
      "justMyCode": false,
      "args": ["trip"],
      "django": true
    },
    {
      "name": "Run Process EDI",
      "type": "python",
      "request": "launch",
      "program": "${workspaceFolder}/manage.py",
      "justMyCode": false,
      "args": ["update_edi"],
      "django": true
    },
    {
      "name": "Run Waybills",
      "type": "python",
      "request": "launch",
      "program": "${workspaceFolder}/manage.py",
      "justMyCode": false,
      "args": ["waybill"],
      "django": true
    },
    {
      "name": "Run Detention",
      "type": "python",
      "request": "launch",
      "program": "${workspaceFolder}/manage.py",
      "justMyCode": false,
      "args": ["detention", "12", "2022-01-01"],
      "django": true
    },
    {
      "name": "Run Prep Demo",
      "type": "python",
      "request": "launch",
      "program": "${workspaceFolder}/manage.py",
      "justMyCode": false,
      "args": ["prep_demo", "--update_tbossv3"],
      "django": true
    }
  ]
}
