#%%
import os
os.environ["DJANGO_ALLOW_ASYNC_UNSAFE"] = "true"
from safedelete.models import SOFT_DELETE_CASCADE
import pandas as pd
#%%
key = '/TEDS/Published/6000 Marine Terminal Operations/6900 Vendor Manuals/6900-0001'
i = Integration.objects.first()
i.controldocumentintegration_set.exclude(
                control_document__duplicate_of__isnull=False).filter(integration_ref=key)
#%%
c = ControlDocumentIntegration.objects.get(id=1586)
c.control_document.code
#%%
qs = ApplicableLegislationAssessment.objects.all().values('id','compliance_status')
df = pd.DataFrame.from_records(qs)
#%%
df
#%%
df[df['compliance_status']=='EO'].describe()
#%%
Document
#%%
Integration.poo(1)
#%%
