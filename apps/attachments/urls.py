from django.urls import path

from .views import AttachmentDeleteView
from .views import AttachmentListView
from .views import AttachmentUpdateView
from .views import download_attachment

app_name = "attachments"

urlpatterns = [
    path("", AttachmentListView.as_view(), name="list"),
    path("download_attachment/<int:id>/", download_attachment, name="download-attachment"),
    path("<int:id>/", AttachmentUpdateView.as_view(), name="detail"),
    path("delete/<int:id>/", AttachmentDeleteView.as_view(), name="delete"),
]

