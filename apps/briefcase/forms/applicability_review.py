import json

from bootstrap_daterangepicker import fields
from bootstrap_daterangepicker import widgets
from crispy_forms.bootstrap import InlineRadios
from crispy_forms.layout import HTML
from crispy_forms.layout import Column
from crispy_forms.layout import Div
from crispy_forms.layout import Field
from crispy_forms.layout import Layout
from crispy_forms.layout import Row
from crispy_forms.layout import Submit
from dal import forward
from django import forms
from django.urls import reverse
from django.urls import reverse_lazy
from django.utils.translation import gettext_lazy as _

from apps.briefcase.enum import KeepInBriefcaseChangeType
from apps.briefcase.models import ApplicabilityReview
from apps.briefcase.models import ApplicabilityReviewUser
from apps.briefcase.views.widgets import BulkAddSelect2MultipleWidget
from apps.comments.utils import parse_mention
from apps.common.forms import CrispyFormTemplate
from apps.common.forms import QuillMentionWidget
from apps.common.forms import choices_with_empty
from apps.common.forms.filters import OPERATOR_CHOICES
from apps.document.models import Document
from apps.document.models import DocumentRevision
from apps.search.forms import DocumentSearchForm
from apps.users.models import User
from apps.utils.enums import enum_to_choices_tuple


class ApplicabilityReviewSearchForm(DocumentSearchForm, forms.ModelForm):
    title = _("Search Applicability Review")

    revision_type = forms.ChoiceField(
        choices=choices_with_empty(DocumentRevision.RevisionType.choices),
        label="Reason for Review",
        required=False,
    )
    suggestion = forms.ChoiceField(
        choices=choices_with_empty(ApplicabilityReview.Suggestions.choices),
        label="Suggested Actions",
        required=False,
    )
    review_action = forms.ChoiceField(
        choices=choices_with_empty(ApplicabilityReview.SearchReviewActions.choices),
        label="Review Action",
        required=False,
        initial=ApplicabilityReview.SearchReviewActions.NO_ACTION,
    )
    document = forms.ModelMultipleChoiceField(
        queryset=Document.objects.library_current_set(),
        required=False,
        label="",
        widget=BulkAddSelect2MultipleWidget(
            url=reverse_lazy("document:document-autocomplete"),
            attrs={
                "data-placeholder": "Select a document",
                "class": "text-bg-secondary",
            },
        ),
    )
    document_operator = forms.ChoiceField(
        choices=OPERATOR_CHOICES,
        label="",
        widget=forms.RadioSelect(attrs={"class": "dense-inline-radio"}),
    )
    reviewers = forms.ModelMultipleChoiceField(
        queryset=User.objects.all(),
        required=False,
        label="",
        widget=BulkAddSelect2MultipleWidget(
            url=reverse_lazy("users:autocomplete"),
            attrs={
                "data-placeholder": "Select a reviewer",
                "class": "text-bg-secondary",
            },
        ),
    )
    reviewers__isnull = forms.BooleanField(
        required=False,
        label="None",
    )
    reviewers_operator = forms.ChoiceField(
        choices=OPERATOR_CHOICES,
        label="",
        widget=forms.RadioSelect(attrs={"class": "dense-inline-radio"}),
    )
    published_date = fields.DateRangeField(
        label="Publication Date",
        required=False,
        clearable=True,
        input_formats=["%Y-%m-%d"],
        widget=widgets.DateRangeWidget(
            format="%Y-%m-%d",
            attrs={
                "placeholder": "Select a date",
                "type": "search",
                "value": "",
            },
        ),
    )
    effective_date = fields.DateRangeField(
        label="Effective Date",
        required=False,
        clearable=True,
        input_formats=["%Y-%m-%d"],
        widget=widgets.DateRangeWidget(
            format="%Y-%m-%d",
            attrs={
                "placeholder": "Select a date",
                "type": "search",
                "value": "",
            },
        ),
    )
    user_review_date = fields.DateRangeField(
        label="User Review Date",
        required=False,
        clearable=True,
        input_formats=["%Y-%m-%d"],
        widget=widgets.DateRangeWidget(
            format="%Y-%m-%d",
            attrs={
                "placeholder": "Select a date",
                "type": "search",
                "value": "",
            },
        ),
    )
    applicable_to_tenant = forms.BooleanField(
        label="Clause Updates In Briefcase Only",
        required=False,
    )

    class Meta:
        model = ApplicabilityReview
        fields = ["jurisdiction", "suggestion", "review_action", "reviewers"]

    def get_form_id(self):
        return "applicability-review-form"

    def get_results_id(self):
        return "applicability-review-results"

    def get_form_class(self):
        return "row"

    def get_form_attributes(self):
        return {
            "method": "post",
            "action": reverse("briefcase:applicability-review:search"),
        }

    def get_form_layout_extra(self):
        return [
            HTML(
                """<div class='card-header'><h6>
                   <i class="align-middle mx-2 fa-solid fa-filter"></i>
                   <span class="align-middle">Review Filters</span>
                   </h6></div>""",
            ),
            Field("revision_type", wrapper_class="col-12 pt-2"),
            Field("suggestion", wrapper_class="col-12 pt-2"),
            Field("review_action", wrapper_class="col-12 pt-2"),
            Field("published_date", wrapper_class="col-12 pt-2"),
            HTML("<h6><b>Documents</b></h6>"),
            InlineRadios("document_operator", wrapper_class="crispy-dense dense-inline-radio"),

            Field("document", wrapper_class="col-12 pt-2 row"),
            Field("effective_date", wrapper_class="col-12 pt-2"),
             HTML("<h6><b>Reviewers</b></h6>"),
            Field("reviewers__isnull", wrapper_class="crispy-dense dense-inline-radio"),
            InlineRadios("reviewers_operator", wrapper_class="crispy-dense dense-inline-radio"),

            Field("reviewers", wrapper_class="col-12 pt-2 row"),
            Field("user_review_date", wrapper_class="col-12 pt-2"),
            Field("applicable_to_tenant", wrapper_class="col-12 pt-2"),
        ]

    def get_form_layout_browse(self):
        return []

    def clean(self):
        cleaned_data = super().clean()
        for field in ["jurisdiction", "published_date"]:
            if field in cleaned_data:
                value = cleaned_data.get(field)
                if not value or value == (None, None):
                    cleaned_data.pop(field)

        # drop the fields if False
        for field in ["applicable_to_tenant"]:
            if field in cleaned_data and not cleaned_data.get(field):
                cleaned_data.pop(field)
        return cleaned_data


class ApplicabilityReviewUserForm(CrispyFormTemplate, forms.ModelForm):
    title = _("Assign Reviewers")

    reviewers = forms.ModelMultipleChoiceField(
        queryset=User.objects.all(),
        required=False,
        widget=BulkAddSelect2MultipleWidget(
            url=reverse_lazy("users:autocomplete"),
            attrs={
                "data-placeholder": "Select a reviewer",
                "class": "text-bg-secondary w-100",
            },
        ),
    )

    comment = forms.CharField(
        label="Comment",
        required=False,
        widget=QuillMentionWidget(attrs={"rows": 1}),
    )

    class Meta:
        model = ApplicabilityReview
        fields = ["reviewers", "comment"]

    def get_form_id(self):
        return "applicability-review-user-form"

    def get_results_id(self):
        return f"applicability-review-{self.instance.id}-reviewers"

    def get_form_class(self):
        return "row"

    def get_form_parent_id(self):
        return f"{self.get_form_id()}-parent"

    def get_form_layout(self):

        change_applicability_review_users = self.request.user.has_perm(
            "briefcase.change_applicabilityreviewuser",
        )
        save_button = []
        if change_applicability_review_users:
            save_button.append(
                Submit(
                    "submit", "Save",
                    css_class="input-group-text btn btn-sm shadow btn-warning",
                ),
            )

        return Layout(
            Row(
                Column(
                    Field("reviewers"),
                    Field("comment"),
                    css_class="col-9",
                ),
                Column(
                    Div(
                        *save_button,
                        css_class="m-4 pt-2",
                    ),
                    css_class="col-3 d-flex justify-content-start",
                ),
            ),
        )

    def get_form_attributes(self):
        # setup htmx
        return {
            "hx-post": reverse_lazy(
                "briefcase:applicability-review:reviewer-list",
                kwargs={"id": self.instance.id},
            ),
            "hx-target": "this",
            "hx-swap": "outerHTML",
            "hx-indicator": ".progress",
            "hx-trigger-after": "submit",
            "data-loading": "block",
            "id": self.get_form_id(),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # setup htmx

        if self.instance and self.instance.id:
            self.fields["reviewers"].widget.attrs["hx-get"] = reverse(
                "briefcase:applicability-review:reviewer-list",
                kwargs={"id": self.instance.id},
            )
        self.fields["reviewers"].widget.attrs["hx-trigger"] = "changed"
        self.fields["reviewers"].widget.attrs["hx-target"] = f"#applicability-review-{self.instance.id}-reviewers"
        self.fields["reviewers"].widget.attrs["hx-indicator"] = ".progress"
        self.fields["reviewers"].widget.attrs["hx-vals"] = json.dumps({"partial": True})
        self.fields["reviewers"].widget.attrs["type"] = "search"

        if (
            not self.request.user.has_perm("briefcase.change_applicabilityreviewuser")
            or self.instance.is_already_evaluated
        ):
            self.fields["reviewers"].widget.attrs["disabled"] = True

    def clean(self):
        cleaned_data = super().clean()
        if self.request.method == "POST" and self.instance.id:
            # Check if a reviewer has been removed with no comment
            comment = parse_mention(cleaned_data.get("comment"))
            print("Comment: ", comment)
            reviewer_removed = False
            for reviewer in self.instance.get_active_applicability_review_users():
                if reviewer.user not in cleaned_data.get("reviewers", []):
                    reviewer_removed = True

            if reviewer_removed and not comment:
                self.add_error(
                    "comment", "A comment is required when removing a reviewer.",
                )
        return cleaned_data


class ApplicabilityReviewUserUpdateForm(forms.ModelForm):
    class Meta:
        model = ApplicabilityReviewUser
        fields = ["user_review_action", "comment"]

    comment = forms.CharField(required=True)

    def __init__(self, *args, **kwargs):
        self.title = kwargs.pop("title")
        self.request = kwargs.pop("request")
        self.review_action = kwargs.pop("review_action")
        self.show_keep_in_briefcase_change_type = kwargs.pop(
            "show_keep_in_briefcase_change_type", False,
        )
        super().__init__(*args, **kwargs)
        self.fields["user_review_action"].label = ""
        self.fields["user_review_action"].widget = forms.HiddenInput()

        if self.show_keep_in_briefcase_change_type:
            self.fields["keep_in_briefcase_change_type"] = forms.ChoiceField(
                choices=enum_to_choices_tuple(KeepInBriefcaseChangeType),
                initial=KeepInBriefcaseChangeType.MATERIAL_CHANGE.value,
                label="Clause Change Evaluation",
            )


class ApplicabilityReviewBulkUserForm(ApplicabilityReviewUserForm):
    reviewers = forms.ModelMultipleChoiceField(
        queryset=User.objects.all(),
        label="Add Reviewers",
        required=False,
        widget=BulkAddSelect2MultipleWidget(
            url=reverse_lazy("briefcase:applicability-review:autocomplete"),
            attrs={
                "data-placeholder": "Select a reviewer",
                "class": "text-bg-secondary",
            },
        ),
    )

    remove_reviewers = forms.ModelMultipleChoiceField(
        queryset=User.objects.all(),
        required=False,
        label="Remove Reviewers",
        widget=BulkAddSelect2MultipleWidget(
            url=reverse_lazy("briefcase:applicability-review:autocomplete"),
            attrs={
                "data-placeholder": "Select a reviewer",
                "class": "text-bg-secondary",
            },
            forward=(forward.Const(True, "include_inactive"),),
        ),
    )

    def get_form_id(self):
        return "applicability-review-user-bulk-form"

    def get_results_id(self):
        return "applicability-review-bulk-reviewers"

    def get_form_class(self):
        return "row"

    def get_form_parent_id(self):
        return f"{self.get_form_id()}-parent"

    def get_form_attributes(self):
        # setup htmx
        return {
            "hx-post": reverse_lazy(
                "briefcase:applicability-review:bulk-selection",
                kwargs={"revision_id": self.revision_id},
            ),
            "hx-target": f"#{self.get_form_parent_id()}",
            "hx-swap": "outerHTML",
            "hx-indicator": ".progress",
            "hx-vals": "js:{session_key:" + f"'{self.session_key}'" + ",action:'reviewers'}",
            "hx-trigger-after": "submit",
            "data-loading": "block",
        }

    def __init__(self, *args, **kwargs):
        self.session_key = kwargs.pop("session_key")
        self.revision_id = kwargs.pop("revision_id")
        self.title = kwargs.pop("title")
        super(ApplicabilityReviewBulkUserForm, self).__init__(*args, **kwargs)

        self.fields["comment"].required = True

        self.fields["reviewers"].widget.url += "?session_key=%s&field=%s" % (
            self.session_key,
            "reviewers",
        )
        self.fields["remove_reviewers"].widget.url += "?session_key=%s&field=%s" % (
            self.session_key,
            "remove_reviewers",
        )

        # setup htmx
        self.fields["reviewers"].widget.attrs["hx-get"] = reverse(
            "briefcase:applicability-review:update-bulk-selection",
        )
        self.fields["reviewers"].widget.attrs["hx-trigger"] = "changed"
        self.fields["reviewers"].widget.attrs["hx-swap"] = None
        self.fields["reviewers"].widget.attrs["hx-indicator"] = ".progress"
        self.fields["reviewers"].widget.attrs["hx-vals"] = json.dumps(
            {"session_key": self.session_key, "field": "reviewers"},
        )
        self.fields["reviewers"].widget.attrs["type"] = "search"

        if not self.request.user.has_perm("briefcase.change_applicabilityreviewuser"):
            self.fields["reviewers"].widget.attrs["disabled"] = True

        self.fields["remove_reviewers"].widget.attrs["hx-get"] = reverse(
            "briefcase:applicability-review:update-bulk-selection",
        )
        self.fields["remove_reviewers"].widget.attrs["hx-trigger"] = "changed"
        self.fields["remove_reviewers"].widget.attrs["hx-target"] = None
        self.fields["remove_reviewers"].widget.attrs["hx-indicator"] = ".progress"
        self.fields["remove_reviewers"].widget.attrs["hx-vals"] = json.dumps(
            {"session_key": self.session_key, "field": "remove_reviewers"},
        )
        self.fields["remove_reviewers"].widget.attrs["type"] = "search"

        if not self.request.user.has_perm("briefcase.change_applicabilityreviewuser"):
            self.fields["remove_reviewers"].widget.attrs["disabled"] = True

    def get_form_layout(self):
        return Layout(
            Field("reviewers"),
            Field("remove_reviewers"),
            Field("comment"),
        )

    def clean(self):
        cleaned_data = super().clean()
        is_for_submit = self.request.method == "POST"
        if (
            is_for_submit
            and not cleaned_data.get("reviewers")
            and not cleaned_data.get("remove_reviewers")
        ):
            raise forms.ValidationError(
                "At least one reviewer must be selected to be added or removed.",
            )

        return cleaned_data


class ApplicabilityReviewBulkActionForm(ApplicabilityReviewUserUpdateForm):
    def __init__(self, *args, **kwargs):
        self.session_key = kwargs.pop("session_key", None)
        self.revision_id = kwargs.pop("revision_id", None)
        self.document_id = kwargs.pop("document_id", None)
        super().__init__(*args, **kwargs)
