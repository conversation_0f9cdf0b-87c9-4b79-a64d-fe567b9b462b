from apps.common.forms import CrispyFormTemplate
from bootstrap_daterangepicker import widgets, fields
from django import forms

class BriefcaseListBaseForm(CrispyFormTemplate):

    query = forms.CharField(
        label="",
        required=False,
        widget=forms.TextInput(
            attrs={
                "placeholder": "Enter a keyword for search",
                "type": "search"
            }
        )
    )
    published_date = fields.DateRangeField(
        label="",
        required=False,
        clearable=True,
        input_formats=['%Y-%m-%d'],
        widget=widgets.DateRangeWidget(
            format='%Y-%m-%d',
            attrs={
                'placeholder': 'Publication Date',
                'type': "search",
                'value': ""
            }
        )
    )
