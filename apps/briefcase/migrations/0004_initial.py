# Generated by Django 5.0.3 on 2024-10-26 07:52

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('briefcase', '0003_initial'),
        ('comments', '0002_initial'),
        ('contenttypes', '0002_remove_content_type_name'),
        ('document', '0001_initial'),
        ('integration', '0002_initial'),
        ('tenants', '0002_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name='applicabilityreview',
            name='created_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_%(class)s', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='applicabilityreview',
            name='fragment',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='document.documentfragment'),
        ),
        migrations.AddField(
            model_name='applicabilityreview',
            name='layout',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='document.documentlayout'),
        ),
        migrations.AddField(
            model_name='applicabilityreview',
            name='modified_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='modified_%(class)s', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='applicabilityreview',
            name='previous_fragment',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.PROTECT, related_name='previous_fragments', to='document.documentfragment'),
        ),
        migrations.AddField(
            model_name='applicabilityreview',
            name='revision',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='document.documentrevision'),
        ),
        migrations.AddField(
            model_name='applicabilityreview',
            name='tenant',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_set', to='tenants.tenant'),
        ),
        migrations.AddField(
            model_name='applicabilityreviewuser',
            name='applicability_review',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='briefcase.applicabilityreview'),
        ),
        migrations.AddField(
            model_name='applicabilityreviewuser',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='applicabilityreview',
            name='reviewers',
            field=models.ManyToManyField(through='briefcase.ApplicabilityReviewUser', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='applicablelegislation',
            name='created_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_%(class)s', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='applicablelegislation',
            name='layout',
            field=models.ForeignKey(limit_choices_to={'fragment__isnull': False}, on_delete=django.db.models.deletion.PROTECT, to='document.documentlayout'),
        ),
        migrations.AddField(
            model_name='applicablelegislation',
            name='modified_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='modified_%(class)s', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='applicablelegislation',
            name='tenant',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_set', to='tenants.tenant'),
        ),
        migrations.AddField(
            model_name='applicablelegislationassessment',
            name='activities',
            field=models.ManyToManyField(limit_choices_to={'tenant': models.F('tenant')}, to='briefcase.activity'),
        ),
        migrations.AddField(
            model_name='applicablelegislationassessment',
            name='created_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_%(class)s', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='applicablelegislationassessment',
            name='modified_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='modified_%(class)s', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='applicablelegislationassessment',
            name='tenant',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_set', to='tenants.tenant'),
        ),
        migrations.AddField(
            model_name='applicablelegislationassessment',
            name='user_comments',
            field=models.ManyToManyField(to='comments.usercomment'),
        ),
        migrations.AddField(
            model_name='applicablelegislation',
            name='assessment',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.PROTECT, to='briefcase.applicablelegislationassessment'),
        ),
        migrations.AddField(
            model_name='applicablelegislationassessmentuser',
            name='assessment',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='briefcase.applicablelegislationassessment'),
        ),
        migrations.AddField(
            model_name='applicablelegislationassessmentuser',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='applicablelegislationassessment',
            name='assigned_to',
            field=models.ManyToManyField(related_name='assigned_assessments', through='briefcase.ApplicableLegislationAssessmentUser', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='calendareventtype',
            name='tenant',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_set', to='tenants.tenant'),
        ),
        migrations.AddField(
            model_name='controldocument',
            name='archived_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='archived_%(class)ss', to=settings.AUTH_USER_MODEL, verbose_name='Archived By'),
        ),
        migrations.AddField(
            model_name='controldocument',
            name='duplicate_of',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='duplicate_set', to='briefcase.controldocument'),
        ),
        migrations.AddField(
            model_name='controldocument',
            name='polymorphic_ctype',
            field=models.ForeignKey(editable=False, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='polymorphic_%(app_label)s.%(class)s_set+', to='contenttypes.contenttype'),
        ),
        migrations.AddField(
            model_name='controldocument',
            name='tenant',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_set', to='tenants.tenant'),
        ),
        migrations.AddField(
            model_name='controldocumentintegration',
            name='control_document',
            field=models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='integration_data', to='briefcase.controldocument'),
        ),
        migrations.AddField(
            model_name='controldocumentintegration',
            name='integration',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='integration.integration'),
        ),
        migrations.AddField(
            model_name='correctiveactionplanattachment',
            name='cap',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='attachments', to='briefcase.correctiveactionplan'),
        ),
        migrations.AddField(
            model_name='internalcontrol',
            name='activities',
            field=models.ManyToManyField(limit_choices_to={'tenant': models.F('tenant')}, to='briefcase.activity'),
        ),
        migrations.AddField(
            model_name='internalcontrol',
            name='approved_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.PROTECT, related_name='approved_%(class)ss', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='internalcontrol',
            name='assessment',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='briefcase.applicablelegislationassessment'),
        ),
        migrations.AddField(
            model_name='internalcontrol',
            name='control_document',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.PROTECT, to='briefcase.controldocument'),
        ),
        migrations.AddField(
            model_name='internalcontrol',
            name='created_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_%(class)s', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='internalcontrol',
            name='modified_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='modified_%(class)s', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='internalcontrol',
            name='reviewed_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.PROTECT, related_name='reviewed_%(class)ss', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='operationalarea',
            name='tenant',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_set', to='tenants.tenant'),
        ),
        migrations.AddField(
            model_name='internalcontrol',
            name='operational_areas',
            field=models.ManyToManyField(limit_choices_to={'tenant': models.F('tenant')}, to='briefcase.operationalarea'),
        ),
        migrations.AddField(
            model_name='applicablelegislationassessment',
            name='operational_areas',
            field=models.ManyToManyField(limit_choices_to={'tenant': models.F('tenant')}, to='briefcase.operationalarea'),
        ),
        migrations.AddField(
            model_name='program',
            name='tenant',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_set', to='tenants.tenant'),
        ),
        migrations.AddField(
            model_name='internalcontrol',
            name='programs',
            field=models.ManyToManyField(limit_choices_to={'tenant': models.F('tenant')}, to='briefcase.program'),
        ),
        migrations.AddField(
            model_name='applicablelegislationassessment',
            name='programs',
            field=models.ManyToManyField(limit_choices_to={'tenant': models.F('tenant')}, to='briefcase.program'),
        ),
        migrations.AddConstraint(
            model_name='activity',
            constraint=models.UniqueConstraint(condition=models.Q(('deleted__isnull', False)), fields=('name', 'tenant'), name='activities_unique_name_per_tenant'),
        ),
        migrations.AddConstraint(
            model_name='activity',
            constraint=models.UniqueConstraint(fields=('code',), name='briefcase_activity_unique_code'),
        ),
        migrations.AlterUniqueTogether(
            name='applicabilityreviewuser',
            unique_together={('applicability_review', 'user')},
        ),
        migrations.AlterUniqueTogether(
            name='applicablelegislation',
            unique_together={('tenant', 'layout')},
        ),
        migrations.AlterUniqueTogether(
            name='applicablelegislationassessmentuser',
            unique_together={('user', 'assessment')},
        ),
        migrations.AddConstraint(
            model_name='operationalarea',
            constraint=models.UniqueConstraint(condition=models.Q(('deleted__isnull', False)), fields=('name', 'tenant'), name='operational_area_unique_name_per_tenant'),
        ),
        migrations.AddConstraint(
            model_name='operationalarea',
            constraint=models.UniqueConstraint(fields=('code',), name='briefcase_operationalarea_unique_code'),
        ),
        migrations.AddConstraint(
            model_name='program',
            constraint=models.UniqueConstraint(condition=models.Q(('deleted__isnull', False)), fields=('name', 'tenant'), name='program_unique_name_per_tenant'),
        ),
        migrations.AddConstraint(
            model_name='program',
            constraint=models.UniqueConstraint(fields=('code',), name='briefcase_program_unique_code'),
        ),
    ]
