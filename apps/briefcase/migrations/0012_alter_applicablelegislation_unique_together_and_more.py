# Generated by Django 5.0.3 on 2024-11-27 22:12

from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('briefcase', '0011_correctiveactionplan_priority_level'),
        ('document', '0006_documentlayout_path_layout_type_array_and_more'),
        ('tenants', '0003_remove_tenant_enforce_login_via_sso'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AlterUniqueTogether(
            name='applicablelegislation',
            unique_together=set(),
        ),
        migrations.AddConstraint(
            model_name='applicablelegislation',
            constraint=models.UniqueConstraint(condition=models.Q(('deleted__isnull', True)), fields=('layout', 'tenant'), name='applicable_legislation_layout_unique_per_tenant'),
        ),
    ]
