# Generated by Django 5.0.3 on 2024-12-17 16:11

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('briefcase', '0017_update_applicability_review_enums'),
    ]

    operations = [
        migrations.AlterField(
            model_name='applicabilityreview',
            name='reason',
            field=models.CharField(choices=[('N', 'New Document Published'), ('V', 'Version Published'), ('A', 'Amendment Published'), ('C', 'Correction Published'), ('CA', 'Company Attributes Updated'), ('LA', 'Legislation Archived')], max_length=2),
        ),
        migrations.AlterField(
            model_name='applicabilityreview',
            name='review_action',
            field=models.CharField(choices=[('A', 'Accepted'), ('D', 'Declined'), ('S', 'Dismissed by System'), ('I', 'Ignored'), ('N', 'No Action')], default='N', max_length=2),
        ),
        migrations.AlterField(
            model_name='applicabilityreview',
            name='suggestion',
            field=models.CharField(choices=[('A', 'Add to Briefcase'), ('K', 'Keep in Briefcase'), ('R', 'Remove from Briefcase'), ('MR', "Must Remove from Briefcase")], max_length=2),
        ),
        migrations.AlterField(
            model_name='applicabilityreviewuser',
            name='user_review_action',
            field=models.CharField(choices=[('A', 'Accepted'), ('D', 'Declined'), ('S', 'Dismissed by System'), ('I', 'Ignored'), ('N', 'No Action')], default='N', max_length=2),
        ),
    ]
