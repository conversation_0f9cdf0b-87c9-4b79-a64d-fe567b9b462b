# Generated by Django 5.0.3 on 2025-02-10 03:03

import django.db.models.deletion
import django.utils.timezone
import model_utils.fields
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('briefcase', '0022_remove_activity_activities_unique_name_per_tenant_and_more'),
        ('document', '0015_remove_document_search_data_and_more'),
        ('tenants', '0006_tenantfeatureflag'),
    ]

    operations = [
        migrations.AlterField(
            model_name='applicablelegislationassessment',
            name='assessment_status',
            field=models.CharField(choices=[('NA', 'Not Assessed'), ('UA', 'Under Assessment'), ('C', 'Complete')], db_index=True, default='NA', max_length=3),
        ),
        migrations.AlterField(
            model_name='applicablelegislationassessment',
            name='compliance_status',
            field=models.CharField(choices=[('CPL', 'Compliant'), ('PG', 'Partial Gap'), ('GAP', 'Gap'), ('NA', 'Not Required'), ('EO', 'Evaluation Outstanding')], db_index=True, default='EO', max_length=3),
        ),
        migrations.CreateModel(
            name='TenantDocument',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', model_utils.fields.AutoCreatedField(default=django.utils.timezone.now, editable=False, verbose_name='created')),
                ('modified', model_utils.fields.AutoLastModifiedField(default=django.utils.timezone.now, editable=False, verbose_name='modified')),
                ('document', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='document.document')),
                ('tenant', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='%(class)s_set', to='tenants.tenant')),
            ],
            options={
                'verbose_name': 'Tenant Document',
                'verbose_name_plural': 'Tenant Documents',
                'unique_together': {('tenant', 'document')},
            },
        ),
    ]
