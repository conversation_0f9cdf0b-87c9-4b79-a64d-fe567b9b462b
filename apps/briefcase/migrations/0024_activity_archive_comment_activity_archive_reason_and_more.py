# Generated by Django 5.0.3 on 2025-02-26 21:41

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('briefcase', '0023_alter_applicablelegislationassessment_assessment_status_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name='activity',
            name='archive_comment',
            field=models.TextField(blank=True, null=True, verbose_name='Archive Comment'),
        ),
        migrations.AddField(
            model_name='activity',
            name='archive_reason',
            field=models.TextField(blank=True, null=True, verbose_name='Archive Reason'),
        ),
        migrations.AddField(
            model_name='activity',
            name='archived',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='activity',
            name='archived_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='archived_%(class)ss', to=settings.AUTH_USER_MODEL, verbose_name='Archived By'),
        ),
        migrations.AddField(
            model_name='activity',
            name='archived_date',
            field=models.DateTimeField(blank=True, default=None, null=True, verbose_name='Archived Date'),
        ),
        migrations.AddField(
            model_name='operationalarea',
            name='archive_comment',
            field=models.TextField(blank=True, null=True, verbose_name='Archive Comment'),
        ),
        migrations.AddField(
            model_name='operationalarea',
            name='archive_reason',
            field=models.TextField(blank=True, null=True, verbose_name='Archive Reason'),
        ),
        migrations.AddField(
            model_name='operationalarea',
            name='archived',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='operationalarea',
            name='archived_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='archived_%(class)ss', to=settings.AUTH_USER_MODEL, verbose_name='Archived By'),
        ),
        migrations.AddField(
            model_name='operationalarea',
            name='archived_date',
            field=models.DateTimeField(blank=True, default=None, null=True, verbose_name='Archived Date'),
        ),
        migrations.AddField(
            model_name='program',
            name='archive_comment',
            field=models.TextField(blank=True, null=True, verbose_name='Archive Comment'),
        ),
        migrations.AddField(
            model_name='program',
            name='archive_reason',
            field=models.TextField(blank=True, null=True, verbose_name='Archive Reason'),
        ),
        migrations.AddField(
            model_name='program',
            name='archived',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='program',
            name='archived_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='archived_%(class)ss', to=settings.AUTH_USER_MODEL, verbose_name='Archived By'),
        ),
        migrations.AddField(
            model_name='program',
            name='archived_date',
            field=models.DateTimeField(blank=True, default=None, null=True, verbose_name='Archived Date'),
        ),
    ]
