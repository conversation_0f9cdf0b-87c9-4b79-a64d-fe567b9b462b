
import uuid

import factory
from factory.django import DjangoModelFactory

from apps.briefcase.models import Activity
from apps.briefcase.models import ControlDocument
from apps.briefcase.models import CorrectiveActionPlan
from apps.briefcase.models import CorrectiveActionPlanAttachment
from apps.briefcase.models import InternalControl
from apps.briefcase.models import OperationalArea
from apps.briefcase.models import Program
from apps.calendar.models import CalendarEventSeries
from apps.tenants.tests.factories import TenantFactory


def get_uuid():
    return str(uuid.uuid4())

class ProgramFactory(DjangoModelFactory):
    class Meta:
        model = Program

    tenant = factory.SubFactory(TenantFactory)

class ActivityFactory(DjangoModelFactory):
    class Meta:
        model = Activity

    tenant = factory.SubFactory(TenantFactory)

class OperationalAreaFactory(DjangoModelFactory):
    class Meta:
        model = OperationalArea

    tenant = factory.SubFactory(TenantFactory)

class InternalControlFactory(DjangoModelFactory):
    class Meta:
        model = InternalControl

class CalendarEventSeriesFactory(DjangoModelFactory):
    class Meta:
        model = CalendarEventSeries

    tenant = factory.SubFactory(TenantFactory)

class CorrectiveActionPlanFactory(DjangoModelFactory):
    class Meta:
        model = CorrectiveActionPlan

    series = factory.SubFactory(CalendarEventSeriesFactory)
    name = factory.Faker("word")

class CorrectiveActionPlanAttachmentFactory(DjangoModelFactory):
    class Meta:
        model = CorrectiveActionPlanAttachment

    cap = factory.SubFactory(CorrectiveActionPlanFactory)

class ControlDocumentFactory(DjangoModelFactory):
    class Meta:
        model = ControlDocument

    tenant = factory.SubFactory(TenantFactory)
