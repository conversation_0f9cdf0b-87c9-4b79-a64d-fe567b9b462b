import arrow
import django_filters as filters
import django_tables2 as tables
from django.contrib import messages
from django.contrib.auth.mixins import LoginRequiredMixin
from django.contrib.messages.views import SuccessMessageMixin
from django.db import transaction
from django.db.models import F
from django.http import HttpResponseRedirect
from django.urls import reverse
from django.urls import reverse_lazy
from django.utils.translation import gettext_lazy as _
from django.views.generic import CreateView
from django.views.generic import DeleteView

# Third-party imports
from django_filters.views import FilterView
from view_breadcrumbs.generic import BaseBreadcrumbMixin
from view_breadcrumbs.generic import CreateBreadcrumbMixin
from view_breadcrumbs.generic import ListBreadcrumbMixin
from view_breadcrumbs.generic import UpdateBreadcrumbMixin

from apps.briefcase.models import Activity
from apps.common.forms import CrispyFormFilter
from apps.common.views import CustomPermissionRequiredMixin
from apps.common.views import UpdatePermissionRequiredMixin
from apps.exports.views import ExportMixinViewSet
from apps.history.schemas.data import HistoryData
from apps.history.schemas.data import StateAction
from apps.utils import StateHandler
from apps.utils.pagination import HTMXPaginationMixin


class ActivityViewSetMixin(
    BaseBreadcrumbMixin,
    LoginRequiredMixin,
    CustomPermissionRequiredMixin,
):
    model = Activity
    permission_required = "briefcase.view_activity"
    slug_field = "id"
    slug_url_kwarg = "id"

    def get_queryset(self):
        queryset = super().get_queryset()
        return queryset.filter(tenant=self.request.user.tenant)



class ActivityCreateView(
    CreateBreadcrumbMixin,
    SuccessMessageMixin,
    ActivityViewSetMixin,
    CreateView,
):
    permission_required = "briefcase.add_activity"
    template_name = "briefcase/activities/create.html"
    add_home = False
    crumbs = [
        ("Activities", reverse_lazy("briefcase:activities:list")),
        ("Create", reverse_lazy("briefcase:activities:create")),
    ]
    success_message = _("Activity successfully created")
    fields = [
        "name",
        "archived",
    ]

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context["form_cancel_url"] = self.get_success_url()
        return context

    def get_form(self):
        form = super().get_form()
        form.form_title = _("Create Activity")
        return form

    def get_success_url(self):
        return reverse("briefcase:activities:list")

    def form_valid(self, form):
        name = form.cleaned_data.get("name")
        archived = form.cleaned_data.get("archived")
        user = self.request.user
        tenant = user.tenant
        name_already_exists = tenant.activity_set.filter(name=name).exists()
        if name_already_exists:
            form.add_error("name", _("An Activity with the same name already exists."))
            return self.form_invalid(form)

        self.object = form.save(commit=False)
        self.object.tenant = tenant
        now = arrow.now().datetime
        if archived:
            self.object.archive(
                user=user,
                archive_date=now,
                commit=False,
            )

        self.object.save_history(
            tenant=user.tenant,
            user=user,
            action="Activity Created",
            comment="",
            documents=[],
            data=HistoryData(
                action=StateAction.CREATE.value,
                state=StateHandler.serialize_state(
                    self.object,
                    exclude=["code"],
                ),
            ),
        )

        self.object.save()
        messages.success(self.request, self.success_message)
        return HttpResponseRedirect(self.get_success_url())



class ActivityUpdateView(
    UpdateBreadcrumbMixin,
    SuccessMessageMixin,
    ActivityViewSetMixin,
    UpdatePermissionRequiredMixin,
):
    add_home = False
    permission_required_get = "briefcase.view_activity"
    permission_required_post = "briefcase.change_activity"
    list_view_url = reverse_lazy("briefcase:activities:list")
    success_message = _("Activity successfully updated")
    template_name = "briefcase/activities/detail.html"
    fields = [
        "name",
        "archived",
    ]

    def get_form(self):
        form = super().get_form()
        form.form_title = _("Update Activity")
        return form

    def detail_view_url(self, instance):
        return reverse(
            "briefcase:activities:update",
            kwargs={"id": instance.id},
        )

    def update_view_url(self, instance):
        return reverse(
            "briefcase:activities:update",
            kwargs={"id": instance.id},
        )

    def get_success_url(self):
        return reverse("briefcase:activities:list")

    def form_valid(self, form):
        cleaned_data = form.cleaned_data
        name = cleaned_data.get("name")
        tenant = self.request.user.tenant
        archived = cleaned_data.get("archived")
        now = arrow.now().datetime
        user = self.request.user
        name_already_exists = (
            tenant.activity_set.exclude(id=self.kwargs.get("id"))
            .filter(name=name)
            .exists()
        )
        if name_already_exists:
            form.add_error("name", _("An Activity with the same name already exists."))
            return self.form_invalid(form)

        obj_copy = self.get_object()

        previous_state = StateHandler.serialize_state(
            obj_copy,
        )

        self.object = form.save(commit=False)
        archive_status_changed = archived != obj_copy.archived
        if archive_status_changed:
            if archived:
                self.object.archive(
                    user=user,
                    archive_date=now,
                    commit=False,
                )
            else:
                self.object.set_active(
                    commit=False,
                )

        current_state = StateHandler.serialize_state(
            self.object,
            exclude=["code"],
        )
        state_update = StateHandler.normalizer.delta(previous_state, current_state)
        history_data = HistoryData(
            previous_state=previous_state,
            state=current_state,
            state_update=state_update,
            action=StateAction.UPDATE.value,
        )

        self.object.save_history(
            tenant=self.request.user.tenant,
            user=self.request.user,
            action="Activity Updated",
            comment="",
            documents=[],
            data=history_data,
        )

        self.object.save()
        messages.success(self.request, self.success_message)
        return HttpResponseRedirect(self.get_success_url())



class ActivityDeleteView(ActivityViewSetMixin, SuccessMessageMixin, DeleteView):
    crumbs = []
    permission_required = "briefcase.delete_activity"
    success_url = reverse_lazy("briefcase:activities:list")
    success_message = _("Activity successfully deleted")

    @transaction.atomic
    def post(self, request, *args, **kwargs):
        self.object = self.get_object()

        # **Serialize Current State Before Deletion**
        serialized_state = StateHandler.serialize_state(
            self.object,
        )

        self.object.save_history(
            tenant=request.user.tenant,
            user=request.user,
            action="Activity Deleted",
            comment="",
            documents=[],
            data=HistoryData(
                action=StateAction.DELETE.value,
                state=serialized_state,
            ),
        )

        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, self.success_message)
        return response



class ActivityFilter(CrispyFormFilter):
    class Meta:
        model = Activity
        fields = ["query"]

    status = filters.ChoiceFilter(method="filter_status", label="Status", choices=[
        (False, "Active"),
        (True, "Archived"),
    ])

    def query_filter(self, queryset, name, value):
        return queryset.filter(name__icontains=value)

    def filter_status(self, queryset, name, value):
        if value is not None:
            return queryset.filter(archived=value)
        return queryset


class ActivityTable(tables.Table):
    class Meta:
        model = Activity
        template_name = "django_tables2/bootstrap5.html"
        fields = ["name", "archived", "archived_date", "created", "modified"]
        attrs = {"class": "table table-striped table-hover text-sm"}
        empty_text = _("No results found.")

    name = tables.TemplateColumn(
        """<a href='{% url "briefcase:activities:update" record.id %}'><i class="fa-solid fa-pen-to-square"></i>{{ record.name }}</a>""",
        verbose_name="Name",
    )

    archived = tables.TemplateColumn(
        """<span>{% if record.archived %}Archived{% else %}Active{% endif %}</span>""",
        verbose_name="Status",
    )


class ActivityListView(
    ListBreadcrumbMixin,
    HTMXPaginationMixin,
    ActivityViewSetMixin,
    FilterView,
    tables.SingleTableView,
):
    add_home = False
    paginate_by = 25
    crumbs = []
    table_class = ActivityTable
    filterset_class = ActivityFilter
    list_view_url = htmx_pagination_submit_url = reverse_lazy(
        "briefcase:activities:list",
    )

    def get_template_names(self):
        if self.request.htmx:
            template_name = "common/list.partial.html"
        else:
            template_name = "briefcase/activities/list.html"
        return template_name

    def get_export_url(self, export_format):
        return reverse("briefcase:activities:export", kwargs={"export_format": export_format})

    def get_export_urls(self):
        export_urls = []
        for export_format in ExportMixinViewSet.export_formats:
            export_url = self.get_export_url(export_format)
            export_urls.append(
                f"""<a href="{export_url}" target="_blank" type="button" class="dropdown-item">.{export_format}</a>""",
            )
        return export_urls

    def get_context_data(self, **kwargs):
        context_data = super().get_context_data(**kwargs)
        context_data["title"] = _("Activities")
        context_data["export_urls"] = self.get_export_urls()
        return context_data


class ActivityExportView(ExportMixinViewSet, ActivityListView):
    def get_export_columns(self):
        return [
            {"column_name": "df_name", "annotation": F("name"), "label": "Name"},
            {
                "column_name": "df_created",
                "annotation": F("created"),
                "label": "Created",
                "date_format": "%Y-%m-%d",
            },
            {
                "column_name": "df_archived",
                "annotation": F("archived"),
                "label": "Archived",
            },
            {
                "column_name": "df_archived_date",
                "annotation": F("archived_date"),
                "label": "Archived Date",
                "date_format": "%Y-%m-%d %H:%M:%S",
            },
            {
                "column_name": "df_modified",
                "annotation": F("modified"),
                "label": "Modified",
                "date_format": "%Y-%m-%d",
            },
        ]
