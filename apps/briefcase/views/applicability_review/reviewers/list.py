

from django.db import models
from django.shortcuts import render
from django_filters.views import FilterView
from django_htmx.http import retarget
from django_htmx.http import trigger_client_event

from apps.briefcase.forms import ApplicabilityReviewUserForm
from apps.briefcase.models import ApplicabilityReview
from apps.briefcase.models import ApplicabilityReviewUser
from apps.comments.utils import parse_mention
from apps.common.views import UpdatePermissionRequiredMixin
from apps.document.models import DocumentLayout
from apps.history.schemas.data import HistoryData
from apps.history.schemas.data import StateAction
from apps.users.models import User
from apps.utils import StateHandler
from apps.utils.pagination import HTMXPaginationMixin

from .base import ApplicabilityReviewUserFilter
from .base import ApplicabilityReviewUserViewSetMixin


class ApplicabilityReviewUserListView(
    HTMXPaginationMixin,
    ApplicabilityReviewUserViewSetMixin,
    FilterView,
    UpdatePermissionRequiredMixin,
):
    filterset_class = ApplicabilityReviewUserFilter
    crumbs = []
    ordering = ["user__name"]
    permission_required_get = "briefcase.view_applicabilityreview"
    permission_required_post = "briefcase.change_applicabilityreviewuser"

    def get_object(self):
        return self.get_applicability_review()

    def get_template_names(self):
        request_data = self.get_request_data()
        if request_data.get("partial"):
            return "briefcase/applicability_review/reviewers/list.partial.html"
        return "briefcase/applicability_review/reviewers/list.html"

    def get_request_data(self):
        if self.request.method == "GET":
            return self.request.GET
        elif self.request.method == "POST":
            return self.request.POST

    def get_queryset(self, include_deleted=False):
        applicability_review_filter = models.Q(applicability_review_id=self.kwargs.get("id"))
        if include_deleted:
            queryset = ApplicabilityReviewUser.objects.filter(applicability_review_filter)
        else:
            queryset = super().get_queryset().filter(applicability_review_filter)
        return queryset

    def get_applicability_review(self):
        if not hasattr(self, "_applicability_review"):
            self._applicability_review = ApplicabilityReview.objects.get(id=self.kwargs.get("id"))
            # set applicable to tenant attribute
            self._applicability_review.applicable_to_tenant = self._applicability_review.layout.applicablelegislation_set.filter(
                tenant=self.request.user.tenant,
            ).count()
        return self._applicability_review

    def get_form(self):
        applicability_review = self.get_applicability_review()
        active_applicability_reviews = applicability_review.get_active_applicability_review_users()
        reviewers_initial =  User.objects.filter(id__in=active_applicability_reviews.values_list("user_id", flat=True)).order_by("name").values_list("id", flat=True)
        request_data = self.get_request_data()
        if "partial" in request_data or self.request.method == "POST":
            reviewers_initial = request_data.getlist("reviewers")
        return ApplicabilityReviewUserForm(
            data={"reviewers": reviewers_initial, "comment": request_data.get("comment")},
            request=self.request,
            instance=self.get_applicability_review(),
        )

    def form_valid(self, form):
        cleaned_data = form.cleaned_data
        user = self.request.user
        tenant = user.tenant

        previous_state = StateHandler.serialize_state(self.object)

        self.object = form.save(commit=False)
        self.object.set_reviewers(cleaned_data["reviewers"], user, tenant)
        self.object.modified_by = user
        self.object.save()

        current_state = StateHandler.serialize_state(
            self.object,
        )

        state_update = StateHandler.normalizer.delta(
            previous_state,
            current_state,
        )

        current_reviewers = list(map(lambda u: u.id, cleaned_data["reviewers"]))
        removed_reviewers = list(set(previous_state.get("reviewers", [])) - set(current_reviewers))

        if removed_reviewers:
            description = f"Assigned {len(current_reviewers)} & removed {len(removed_reviewers)} reviewers"
        else:
            description = f"Assigned {len(current_reviewers)} reviewers"

        documents = []
        if self.object.layout:
            documents.append(self.object.layout.document)

        comment = parse_mention(cleaned_data.get("comment"))

        self.object.save_history(
            tenant=self.request.user.tenant,
            user=self.request.user,
            action="Assign Reviewers",
            object_str_repr=description,
            comment=comment,
            documents=documents,
            data=HistoryData(
                previous_state=previous_state,
                state=current_state,
                state_update=state_update,
                action=StateAction.UPDATE.value,
            ),
        )

        template = self.get_template_names()
        response = render(self.request, template, self.get_context_data())
        return trigger_client_event(response, "showToastMessageAndCloseModal", params={"message": "Reviewers successfully updated", "toastId": "toast", "toastBodyId": "toast-body"})

    def form_invalid(self, form):
        response = super().form_invalid(form)
        if form.errors:
            print(form.errors)
        return retarget(
            render(
                self.request, "common/form.partial.html", {"form": form},
            ),
            target=f"#{form.get_form_id()}",
        )

    def get_add_users(self):
        added_users = self.request.user.tenant.user_set.none()
        request_data = self.get_request_data()
        partial = request_data.get("partial")
        if not partial:
            return added_users

        applicability_review = self.get_applicability_review()
        reviewers = request_data.getlist("reviewers", [])
        current_reviewers = self.object_list.filter(applicability_review=applicability_review).values_list("user_id", flat=True)
        tenant_users = self.request.user.tenant.user_set.filter(is_active=True)
        added_users = tenant_users.filter(id__in=reviewers).exclude(id__in=current_reviewers)
        return [ApplicabilityReviewUser(applicability_review=applicability_review, user=user) for user in added_users.order_by("name")]


    def get_remove_users(self):
        removed_users = []
        request_data = self.get_request_data()
        partial = request_data.get("partial")
        if not partial:
            return removed_users

        applicability_review = self.get_applicability_review()
        reviewers =request_data.getlist("reviewers", [])
        current_reviewers = self.object_list.filter(applicability_review=applicability_review)
        tenant_users = self.request.user.tenant.user_set.all()

        tenant_users_and_current_reviewers = User.objects.filter(
            id__in=current_reviewers.values_list("user_id", flat=True),
        ) | tenant_users

        removed_users = tenant_users_and_current_reviewers.filter(
            id__in=current_reviewers.values_list(
                "user_id",
            ),
        ).exclude(
            id__in=reviewers,
        ).values_list("id", flat=True)

        return removed_users

    def get_context_data(self, **kwargs):
        self.object = self.get_object()
        self.object_list = self.get_queryset()
        context_data = super().get_context_data(**kwargs)
        context_data["form"] = self.get_form()
        context_data["filter"] = self.get_filterset(self.get_filterset_class())
        context_data["applicability_review"] = self.get_applicability_review()
        context_data["remove_users"] = self.get_remove_users()
        context_data["add_users"] = self.get_add_users()
        context_data["RenderOption"] = DocumentLayout.RenderOption
        return context_data
