
import django_filters
from django.contrib.auth.mixins import LoginRequiredMixin
from django.db.models import Q
from django.utils.translation import gettext_lazy as _
from django_filters.widgets import RangeWidget

from apps.briefcase.models import ApplicableLegislation
from apps.briefcase.models import ApplicableLegislationAssessment
from apps.briefcase.models import TenantDocument
from apps.common.forms.filters import AndOrModelMultipleChoiceFilter
from apps.common.forms.filters import OperatorFilterSetMixin
from apps.common.views import CustomPermissionRequiredMixin
from apps.common.views.mixins import CustomWaffleFlagMixin
from apps.document.models import Document
from apps.document.models import Tag
from apps.search.forms import DocumentSearchForm
from apps.search.postgres.query import EnhancedSearchQuery
from apps.tenants.models import Tenant
from apps.tenants.models import TenantFeatureFlag


class ApplicableLegislationViewSetMixin(LoginRequiredMixin, CustomWaffleFlagMixin, CustomPermissionRequiredMixin):
    model = ApplicableLegislation
    permission_required = "briefcase.view_applicablelegislation"
    waffle_flag = TenantFeatureFlag.Features.APPLICABLE_LEGISLATION.value

class ApplicableLegislationAssessmentViewSetMixin(LoginRequiredMixin, CustomWaffleFlagMixin, CustomPermissionRequiredMixin):
    model = ApplicableLegislationAssessment
    permission_required = "briefcase.view_applicablelegislationassessment"
    waffle_flag = TenantFeatureFlag.Features.ASSESSMENT.value

class TenantDocumentFilter(django_filters.FilterSet):
    title = _("Applicable Legislation")
    instructions = _("Documents and clauses that are applicable to your organization")

    class Meta:
        model = TenantDocument
        fields = "__all__"

    search_input = django_filters.CharFilter(method="full_text_search_filter", label="Search input")
    search_criteria = django_filters.ChoiceFilter(method="search_criteria_filter", label="Search Criteria",
                                                  choices=DocumentSearchForm.CATEGORY_CHOICES)
    archived = django_filters.BooleanFilter(field_name="document__archived", label="Archived", required=False)
    assessment_status = django_filters.MultipleChoiceFilter(
        field_name="applicable_search_data__assessment_status",
        label="Assessment Status",
        choices=ApplicableLegislationAssessment.AssessmentStatuses,
    )
    compliance_status = django_filters.MultipleChoiceFilter(
        field_name="applicable_search_data__compliance_status",
        label="Compliance Status",
        choices=ApplicableLegislationAssessment.ComplianceStatuses,
    )
    priority_levels = django_filters.MultipleChoiceFilter(
        field_name="applicable_search_data__priority_level",
        label="Priority Level",
        choices=ApplicableLegislation.PriorityLevels,
    )
    date_added = django_filters.DateFromToRangeFilter(
        field_name="applicable_search_data__date_added",
        widget=RangeWidget(
            attrs={"type": "date", "class": "form-control"},
        ),
        label="Date Added",
    )

    no_controls_required = django_filters.BooleanFilter(
        field_name="applicable_search_data__no_controls_required",
        label="No Controls Required",
        required=False,
    )

    no_assignees = django_filters.BooleanFilter(
        field_name="search_assigned_to",
        lookup_expr="isnull",
        label="No Assignees",
        required=False,
    )
    assessment_outstanding = django_filters.BooleanFilter(
        field_name="applicable_search_data__assessment_outstanding",
        label="Assessment Outstanding",
        required=False,
    )
    completed = django_filters.BooleanFilter(
        field_name="applicable_search_data__completed",
        label="Completed",
        required=False,
    )

    operational_areas__isnull = django_filters.BooleanFilter(
        field_name="search_operational_areas",
        lookup_expr="isnull",
        label="No Operational Areas",
        required=False,
    )
    programs__isnull = django_filters.BooleanFilter(
        field_name="search_programs",
        lookup_expr="isnull",
        label="No Programs",
        required=False,
    )
    activities__isnull = django_filters.BooleanFilter(
        field_name="search_activities",
        lookup_expr="isnull",
        label="No Activities",
        required=False,
    )
    control_documents__isnull = django_filters.BooleanFilter(
        field_name="search_control_documents",
        lookup_expr="isnull",
        label="No Control Documents",
        required=False,
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        user = self.request.user
        tenant = user.tenant

        self.filters["jurisdiction"] = django_filters.ModelMultipleChoiceFilter(
            queryset=tenant.jurisdictions.all(),
            field_name="document__jurisdiction",
            to_field_name="id",
            label="Jurisdiction",
        )

        self.filters["documents"] = django_filters.ModelMultipleChoiceFilter(
            field_name="document",
            queryset=Document.objects.tenant_and_library_current_set(include_archived=True, tenant=tenant),
            to_field_name="id",
            label="Documents",
        )

        self.filters["operational_areas"] = AndOrModelMultipleChoiceFilter(
            field_name="search_operational_areas__operational_area",
            label="Operational Areas",
            to_field_name="id",
            queryset=tenant.operationalarea_set.all(),
            operator_param_name="operational_areas_operator",
        )
        self.filters["programs"] = AndOrModelMultipleChoiceFilter(
            field_name="search_programs__program",
            label="Programs",
            to_field_name="id",
            queryset=tenant.program_set.all(),
            operator_param_name="programs_operator",
        )
        self.filters["activities"] = AndOrModelMultipleChoiceFilter(
            field_name="search_activities__activity",
            label="Activities",
            to_field_name="id",
            queryset=tenant.activity_set.all(),
            operator_param_name="activities_operator",
        )
        self.filters["control_documents"] = AndOrModelMultipleChoiceFilter(
            field_name="search_control_documents__control_document",
            label="Control Documents",
            to_field_name="id",
            queryset=tenant.controldocument_set.all(),
            operator_param_name="control_documents_operator",
        )

        self.filters["assignees"] = AndOrModelMultipleChoiceFilter(
            field_name="search_assigned_to__user",
            label="Assignees",
            to_field_name="id",
            queryset=tenant.user_set.all(),
            operator_param_name="assignees_operator",
        )

        self.filters["tenant"] = django_filters.ModelMultipleChoiceFilter(
            queryset=Tenant.objects.filter(id=tenant.id),
            field_name="tenant",
            to_field_name="id",
            label="Tenant",
        )

        tag_types = Tag.get_root_nodes()
        for tag_type in tag_types:
            tag_type_code = f"tag-{tag_type.code}"
            tags = tag_type.get_descendants(include_self=True)

            self.filters[tag_type_code] = django_filters.ModelMultipleChoiceFilter(
                queryset=tags,
                field_name="search_tags__tag",
                to_field_name="id",
                label=tag_type.name,
            )

    def full_text_search_filter(self, queryset, name, value):
        search_criteria = self.form.cleaned_data.get("search_criteria")
        document_search_filter = Q(
            search_data__search_vector_title=EnhancedSearchQuery(
                value,
                search_type="conformii",
                config="english",
            ),
        )
        content_search_filter = Q(
            search_data__search_vector_content=EnhancedSearchQuery(
                value,
                search_type="conformii",
                config="english",
            ),
        )
        text_search_filter = document_search_filter | content_search_filter
        if search_criteria == DocumentSearchForm.CLAUSES_ONLY:
            text_search_filter = content_search_filter
        elif search_criteria == DocumentSearchForm.TITLES_ONLY:
            text_search_filter = document_search_filter

        return queryset.filter(text_search_filter)

    def search_criteria_filter(self, queryset, name, value):
        return queryset

class ApplicableLegislationFilter(OperatorFilterSetMixin, django_filters.FilterSet):
    title = _("Applicable Legislation")
    instructions = _("Documents and clauses that are applicable to your organization")
    class Meta:
        model = ApplicableLegislation
        fields = "__all__"

    search_input = django_filters.CharFilter(method="full_text_search_filter", label="Search input")
    search_criteria = django_filters.ChoiceFilter(method="search_criteria_filter", label="Search Criteria",
                                                  choices=DocumentSearchForm.CATEGORY_CHOICES)
    archived = django_filters.BooleanFilter(field_name="layout__document__archived", label="Archived", required=False)
    no_controls_required = django_filters.BooleanFilter(field_name="search_data__no_controls_required", label="No Controls Required", required=False)

    assessment_status = django_filters.MultipleChoiceFilter(field_name="assessment__assessment_status", label="Assessment Status", choices=ApplicableLegislationAssessment.AssessmentStatuses)
    compliance_status = django_filters.MultipleChoiceFilter(field_name="assessment__compliance_status", label="Compliance Status", choices=ApplicableLegislationAssessment.ComplianceStatuses)
    priority_levels = django_filters.MultipleChoiceFilter(field_name="priority_level", label="Priority Level", choices=ApplicableLegislation.PriorityLevels)
    no_assignees = django_filters.BooleanFilter(field_name="assessment__assigned_to", label="No Assignees", required=False)
    assessment_outstanding = django_filters.BooleanFilter(field_name="search_data__assessment_outstanding", method="assessment_outstanding_filter", label="Assessment Outstanding", required=False)
    completed = django_filters.BooleanFilter(field_name="search_data__completed", method="completed_filter", label="Completed", required=False)

    operational_areas__isnull = django_filters.BooleanFilter(field_name="assessment__operational_areas", lookup_expr="isnull", label="No Operational Areas", required=False)
    programs__isnull = django_filters.BooleanFilter(field_name="assessment__programs", lookup_expr="isnull", label="No Programs", required=False)
    activities__isnull = django_filters.BooleanFilter(field_name="assessment__activities", lookup_expr="isnull", label="No Activities", required=False)
    control_documents__isnull = django_filters.BooleanFilter(field_name="search_control_documents", lookup_expr="isnull", label="No Control Documents", required=False)
    date_added = django_filters.DateFromToRangeFilter(
        field_name="created",
        widget=RangeWidget(
            attrs={"type": "date", "class": "form-control"},
        ),
        label="Date Added",
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        user = self.request.user
        tenant = user.tenant

        self.filters["jurisdiction"] = django_filters.ModelMultipleChoiceFilter(
            queryset=tenant.jurisdictions.all(),
            field_name="layout__document__jurisdiction",
            to_field_name="id",
            label="Jurisdiction",
        )

        self.filters["documents"] = django_filters.ModelMultipleChoiceFilter(
            field_name="layout__document",
            queryset=Document.objects.tenant_and_library_current_set(include_archived=True, tenant=tenant),
            to_field_name="id",
            label="Documents",
        )

        self.filters["operational_areas"] = AndOrModelMultipleChoiceFilter(
            field_name="assessment__operational_areas",
            to_field_name="id",
            label="Operational Areas",
            queryset=tenant.operationalarea_set.all(),
            operator_param_name="operational_areas_operator",
        )
        self.filters["programs"] = AndOrModelMultipleChoiceFilter(
            field_name="assessment__programs",
            to_field_name="id",
            label="Programs",
            queryset=tenant.program_set.all(),
            operator_param_name="programs_operator",
        )
        self.filters["activities"] = AndOrModelMultipleChoiceFilter(
            field_name="assessment__activities",
            to_field_name="id",
            label="Activities",
            queryset=tenant.activity_set.all(),
            operator_param_name="activities_operator",
        )
        self.filters["control_documents"] = AndOrModelMultipleChoiceFilter(
            field_name="search_control_documents__control_document",
            label="Control Documents",
            to_field_name="id",
            queryset=tenant.controldocument_set.all(),
            operator_param_name="control_documents_operator",
        )

        self.filters["assignees"] = AndOrModelMultipleChoiceFilter(
            field_name="assessment__assigned_to",
            label="Assignees",
            to_field_name="id",
            queryset=tenant.user_set.all(),
            operator_param_name="assignees_operator",
        )



        self.filters["tenant"] = django_filters.ModelMultipleChoiceFilter(
            queryset=Tenant.objects.filter(id=tenant.id),
            field_name="tenant",
            to_field_name="id",
            label="Tenant",
        )

        tag_types = Tag.get_root_nodes()
        for tag_type in tag_types:
            tag_type_code = f"tag-{tag_type.code}"
            tags = tag_type.get_descendants(include_self=True)

            self.filters[tag_type_code] = django_filters.ModelMultipleChoiceFilter(
                queryset=tags,
                field_name="layout__tags",
                to_field_name="id",
                label=tag_type.name,
            )

    def full_text_search_filter(self, queryset, name, value):
        search_criteria = self.form.cleaned_data.get("search_criteria")
        document_search_filter = Q(
            search_data__search_vector_title=EnhancedSearchQuery(
                value,
                search_type="conformii",
                config="english",
            ),
        )
        content_search_filter = Q(
            search_data__search_vector_content=EnhancedSearchQuery(
                value,
                search_type="conformii",
                config="english",
            ),
        )
        text_search_filter = document_search_filter | content_search_filter
        if search_criteria == DocumentSearchForm.CLAUSES_ONLY:
            text_search_filter = content_search_filter
        elif search_criteria == DocumentSearchForm.TITLES_ONLY:
            text_search_filter = document_search_filter

        return queryset.filter(text_search_filter)

    def search_criteria_filter(self, queryset, name, value):
        return queryset

    def completed_filter(self, queryset, name, value):
        assignees = self.data.getlist("assignees")
        if assignees and value:
            return queryset.filter(
                assessment__applicablelegislationassessmentuser__user__in=assignees,
                assessment__applicablelegislationassessmentuser__checked=True,
            )
        return queryset.filter(**{name: value}).exclude(assessment__applicablelegislationassessmentuser__checked=False)

    def assessment_outstanding_filter(self, queryset, name, value):
        assignees = self.data.getlist("assignees")
        if assignees and value:
            return queryset.filter(
                assessment__applicablelegislationassessmentuser__user__in=assignees,
                assessment__applicablelegislationassessmentuser__checked=False,
            )
        return queryset.filter(**{name: value})
