import re

import arrow
from django.db.models import Case
from django.db.models import <PERSON><PERSON><PERSON><PERSON>
from django.db.models import F
from django.db.models import Q
from django.db.models import Value
from django.db.models import When
from django.db.models.functions import Concat
from django.urls import reverse

from apps.briefcase.models.applicable_legislation import ApplicableLegislationAssessment
from apps.document.models import Document
from apps.document.templatetags.layout import layout_render_path
from apps.exports.views import ExportMixinViewSet
from apps.tenants.models import TenantFeatureFlag

from .list import ApplicableLegislationListView


class ApplicableLegislationExportView(
    ExportMixinViewSet,
    ApplicableLegislationListView,
):
    permission_required = "briefcase.export_applicablelegislation"

    def clear_session(self):
        # skip clearing sessions
        return

    def get_dataframe_queryset(self):
        return super().get_dataframe_queryset().select_related(
            "layout",
            "layout__document",
            "layout__document__tenant",
            "layout__document__jurisdiction",
            "layout__fragment",
            "layout__previous_fragment",
        ).distinct()

    def get_export_filename(self):
        model = self.get_dataframe_model()
        export_filename = super().get_export_filename()

        if "documents" in self.request.GET:
            document_ids = self.request.GET.getlist("documents", [])
            documents = Document.objects.filter(id__in=document_ids)
            if documents.count() == 1:
                export_filename = export_filename.replace(
                    model._meta.verbose_name,
                    documents.first().name,
                )

        # Allow only alphanumeric characters, underscores, and hyphens
        return re.sub(r"[^\w\-_]", " ", export_filename)

    def get_layout_path(self, path_name_array):
        return layout_render_path(path_name_array)

    def get_export_columns(self):
        return [
            {
                "column_name": "df_path_name_array",
                "annotation" : F("layout__path_name_array"),
                "label"      : "Path",
                "apply_func" : lambda row: self.get_layout_path(row),
            },
            {
                "column_name": "df_content_plain_text",
                "annotation" : Case(
                    When(
                        layout__document__requires_license=True,
                        then=Value("Please refer to your licensed version of this document"),
                    ),
                    output_field=CharField(),
                    default=F("layout__fragment__content_plain_text"),
                ),
                "label"      : "Content",
                "apply_func" : lambda text: self.ensure_text_can_be_printed(text),
            },
            {
                "column_name": "df_document_name",
                "annotation" : F("layout__document__name"),
                "label"      : "Document",
            },
            {
                "column_name": "df_document_version_name",
                "annotation" : F("layout__document__version_name"),
                "label"      : "Document Citation",
            },
            {
                "column_name": "df_effective_date",
                "annotation" : F("layout__document__effective_date"),
                "label"      : "Effective Date",
                "date_format": "%Y-%m-%d",
            },
            {
                "column_name": "df_jurisdiction",
                "annotation" : F("layout__document__jurisdiction__name"),
                "label"      : "Jurisdiction",
            },
            {
                "column_name": "df_source_url",
                "annotation" : F("layout__document__source_url"),
                "label"      : "Source URL",
            },
            {
                "column_name": "df_document_url",
                "annotation" : F("layout__document_id"),
                "label"      : "Red Line URL",
                "apply_func" : lambda row: self.request.build_absolute_uri(
                    reverse("document:current:detail", kwargs={"id": row}),
                ),
            },
            {
                "column_name": "df_document_id",
                "annotation" : F("layout__document_id"),
                "label"      : "Document ID",
            },
            {
                "column_name": "df_content_layout_url",
                "annotation" : Concat(
                    F("layout__document_id"),
                    Value(","),
                    F("layout_id"),
                    output_field=CharField(),
                ),
                "label"      : "Content URL",
                "apply_func" : lambda row: self.request.build_absolute_uri(
                    reverse(
                        "document:current:detail",
                        kwargs={"id": row.split(",")[0]},
                    ),
                )
                                           + f"#layout-{row.split(',')[1]}",
            },
        ]

    def get_search_results(self, queryset):
        return queryset

class ApplicableLegislationDocumentExportView(ApplicableLegislationExportView):

    def extra_dataframe_manipulation(self, dataframe, queryset):
        if not dataframe.empty:
            dataframe = dataframe.drop_duplicates(subset=["Document ID"], keep="first", inplace=False)
        return dataframe

    def get_export_filename(self):
        model = self.get_dataframe_model()
        export_filename = super().get_export_filename()
        verbose_name = model._meta.verbose_name.title()
        if verbose_name in export_filename:
            return export_filename.replace("Legislation", "Document")
        return export_filename

    def get_export_columns(self):
        return [
            {
                "column_name": "df_document_name",
                "annotation" : F("layout__document__name"),
                "label"      : "Document",
            },
            {
                "column_name": "df_jurisdiction",
                "annotation": F("layout__document__jurisdiction__name"),
                "label": "Jurisdiction",
            },
            {
                "column_name": "df_document_type",
                "annotation": F("layout__document__document_type__name"),
                "label": "Document Type",
            },
            {
                "column_name": "df_document_version_name",
                "annotation" : F("layout__document__version_name"),
                "label"      : "Document Citation",
            },
            {
                "column_name": "df_amendment_name",
                "annotation": F("layout__document__amendment_name"),
                "label": "Amendment Name",
            },
            {
                "column_name": "df_source_url",
                "annotation" : F("layout__document__source_url"),
                "label"      : "Source URL",
            },
            {
                "column_name": "df_amendment_url",
                "annotation": F("layout__document__amendment_url"),
                "label": "Amendment URL",
            },
            {
                "column_name": "df_effective_date",
                "annotation": F("layout__document__effective_date"),
                "label": "Effective Date",
                "date_format": "%Y-%m-%d",
            },
            {
                "column_name": "df_document_url",
                "annotation" : F("layout__document_id"),
                "label"      : "Red Line URL",
                "apply_func" : lambda row: self.request.build_absolute_uri(
                    reverse("document:current:detail", kwargs={"id": row}),
                ),
            },
            {
                "column_name": "df_document_id",
                "annotation" : F("layout__document_id"),
                "label"      : "Document ID",
            },
            {
                "column_name": "df_requires_license",
                "annotation": F("layout__document__requires_license"),
                "label": "Requires License",
            },
            {
                "column_name": "df_description",
                "annotation": F("layout__document__description"),
                "label": "Description",
            },
        ]

class ApplicableLegislationAssessmentExportView(ApplicableLegislationExportView):
    permission_required = "briefcase.export_applicablelegislationassessment"
    waffle_flag = TenantFeatureFlag.Features.ASSESSMENT.value

    def get_export_cache_accessor_field(self, cls_model=None):
        return f"asssessment__{ApplicableLegislationAssessment.get_export_relation_field_name()}"

    def get_dataframe_model(self):
        return ApplicableLegislationAssessment

    def get_outdated_export_queryset(self, cls_model, queryset):
        assessment_ids = queryset.filter(
            Q(
            assessment__assessment_export_cache__isnull=True,
        ) | Q(assessment__assessment_export_cache__recalculate_export_fields=True)).values_list(
            "assessment_id", flat=True,
        )
        return ApplicableLegislationAssessment.objects.filter(id__in=assessment_ids)

    def get_dataframe_queryset(self):
        df_queryset = super().get_dataframe_queryset()
        cls_model = df_queryset.model
        return df_queryset.select_related(
            "layout",
            "layout__document",
            "layout__document__tenant",
            "layout__document__jurisdiction",
            "layout__fragment",
            "layout__previous_fragment",
        ).prefetch_related(
            "assessment__assessment_export_cache",
        ).distinct()

    def get_export_filename(self):
        now = arrow.now().datetime
        model = ApplicableLegislationAssessment
        export_filename = f"{model._meta.verbose_name}-Export-{now.strftime('%Y:%m:%dT%H:%M:%S')}"

        if "documents" in self.request.GET:
            document_ids = self.request.GET.getlist("documents", [])
            documents = Document.objects.filter(id__in=document_ids)
            if documents.count() == 1:
                export_filename = export_filename.replace(
                    model._meta.verbose_name,
                    f"{documents.first().name} Assessment",
                )

        # Allow only alphanumeric characters, underscores, and hyphens
        return re.sub(r"[^\w\-_]", " ", export_filename)

    def get_export_columns(self):
        applicable_legislation_export_columns = super().get_export_columns()
        assessment_status_choices = dict(ApplicableLegislationAssessment.AssessmentStatuses.choices)
        compliance_status_choices = dict(ApplicableLegislationAssessment.ComplianceStatuses.choices)
        export_cache_accessor_field = self.get_export_cache_accessor_field()
        return applicable_legislation_export_columns + [
            {
                "column_name": "df_assessment_status",
                "annotation" : F("assessment__assessment_status"),
                "label"      : "Assessment Status",
                "apply_func" : lambda row: assessment_status_choices.get(row, row),
            },
            {
                "column_name": "df_compliance_status",
                "annotation" : F("assessment__compliance_status"),
                "label"      : "Compliance Status",
                "apply_func" : lambda row: compliance_status_choices.get(row, row),
            },
            {
                "column_name": "df_assessment_programs",
                "annotation" : F("assessment__assessment_export_cache__programs"),
                "label"      : "Assessment: Programs",
            },
            {
                "column_name": "df_assessment_operational_areas",
                "annotation" : F("assessment__assessment_export_cache__operational_areas"),
                "label"      : "Assessment: Operational Areas",
            },
            {
                "column_name": "df_assessment_activities",
                "annotation" : F("assessment__assessment_export_cache__activities"),
                "label"      : "Assessment: Activities",
            },
            {
                "column_name": "df_assessment_assigned_to",
                "annotation" : F("assessment__assessment_export_cache__assigned_to"),
                "label"      : "Assessment: Assigned To",
            },
            {
                "column_name": "df_assessment_comments",
                "annotation": F("assessment__assessment_export_cache__user_comments_str"),
                "label": "Assessment: Comments",
            },
            {
                "column_name": "df_assessment_comments_author",
                "annotation": F("assessment__assessment_export_cache__user_comments_author_str"),
                "label"      : "Assessment: Comments Author",
            },
            {
                "column_name": "df_uses_internal_control",
                "annotation" : F("assessment__assessment_export_cache__has_controls"),
                "label"      : "Internal Control",
            },
            {
                "column_name": "df_uses_internal_control_no_controls_required",
                "annotation": F("assessment__assessment_export_cache__no_controls_required"),
                "label"      : "Internal Control: No Controls Required",
            },
            {
                "column_name": "df_internal_control_control_document",
                "annotation" : F("assessment__assessment_export_cache__controls_str"),
                "label"      : "Internal Control: Control Document",
            },
            {
                "column_name": "df_internal_control_programs",
                "annotation" : F("assessment__assessment_export_cache__controls_programs_str"),
                "label"      : "Internal Control: Programs",
            },
            {
                "column_name": "df_internal_control_operational_areas",
                "annotation" : F("assessment__assessment_export_cache__controls_operational_areas_str"),
                "label"      : "Internal Control: Operational Areas",
            },
            {
                "column_name": "df_internal_control_activities",
                "annotation" : F("assessment__assessment_export_cache__controls_activities_str"),
                "label"      : "Internal Control: Activities",
            },
            {
                "column_name": "df_internal_control_comment",
                "annotation" : F("assessment__assessment_export_cache__controls_comments"),
                "label": "Internal Control: Comment",
            },
            {
                "column_name": "df_internal_control_comment_user",
                "annotation" : F("assessment__assessment_export_cache__controls_comments_author_str"),
                "label"      : "Internal Control: Comment By",
            },
            {
                "column_name": "df_internal_control_review",
                "annotation" : F("assessment__assessment_export_cache__controls_reviewed"),
                "label"      : "Internal Control: Reviewed",
            },
            {
                "column_name": "df_internal_control_reviewed_by",
                "annotation": F("assessment__assessment_export_cache__controls_reviewers_str"),
                "label": "Internal Control: Reviewed By",
            },
            {
                "column_name": "df_internal_control_reviewed_date",
                "annotation" : F("assessment__assessment_export_cache__controls_reviewed_dates"),
                "label"      : "Internal Control: Reviewed Date",
            },
            {
                "column_name": "df_internal_control_reviewed_reason",
                "annotation" : F("assessment__assessment_export_cache__controls_reviewed_reasons"),
                "label"      : "Internal Control: Reviewed Reason",
            },
            {
                "column_name": "df_internal_control_approved",
                "annotation" : F("assessment__assessment_export_cache__controls_approved"),
                "label"      : "Internal Control: Approved",
            },
            {
                "column_name": "df_internal_control_approved_by",
                "annotation" : F("assessment__assessment_export_cache__controls_approvers_str"),
                "label"      : "Internal Control: Approved By",
            },
            {
                "column_name": "df_internal_control_approved_date",
                "annotation" : F("assessment__assessment_export_cache__controls_approved_dates"),
                "label"      : "Internal Control: Approved Date",
            },
            {
                "column_name": "df_calendar_events_count",
                "annotation": F("assessment__assessment_export_cache__calendar_events_count"),
                "label": "Calendar Event Count",
            },
            {
                "column_name": "df_calendar_events",
                "annotation": F("assessment__assessment_export_cache__calendar_events_str"),
                "label": "Calendar Events",
            },
            {
                "column_name": "df_corrective_action_plans_count",
                "annotation": F("assessment__assessment_export_cache__corrective_action_plans_count"),
                "label": "Corrective Action Plan Count",
            },
            {
                "column_name": "df_corrective_action_plans",
                "annotation": F("assessment__assessment_export_cache__corrective_action_plans_str"),
                "label": "Corrective Action Plans",
            },
        ]

