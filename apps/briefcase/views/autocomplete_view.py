from dal import autocomplete
from django.contrib.postgres.search import SearchRank
from django.db.models import Q
from django.db.models.expressions import RawSQL

from apps.briefcase.models import Activity
from apps.briefcase.models import ApplicableLegislation
from apps.briefcase.models import ApplicableLegislationAssessment
from apps.briefcase.models import ApplicableLegislationAssessmentUser
from apps.briefcase.models import ControlDocument
from apps.briefcase.models import InternalControl
from apps.briefcase.models import OperationalArea
from apps.briefcase.models import Program
from apps.search.postgres.query import EnhancedSearchQuery
from apps.users.models import User


def handle_text_search_ordering(search_input: str):
    """
    Returns the text search filters, annotations, and ordering of queryset
    If there are more than one word, use full text search for all words stopped by a space and use icontains pattern matching for the last pattern provided by the user
    Otherwise, retain ordering of the queryset by name and use icontains pattern matching
    """
    search_filter = Q()
    ordering = ("name",)
    annotations = {}
    if search_input:
        words = search_input.split(" ")
        if len(words) > 1:
            ordering = []
            for idx, word in enumerate(words, 1):
                key = f"match_position_{idx}"
                annotations[key] = RawSQL(
                    "strpos(LOWER(name), %s)",
                    (word,),
                )
                ordering += [key]

            pattern = words[-1]
            fts_words = " ".join(words[:-1])
            query = EnhancedSearchQuery(
                fts_words,
                search_type="conformii",
                config="english",
            )
            search_filter = Q(
                name__icontains=pattern,
                search_data__search_vector_content=query,
            )
            annotations["rank"] = SearchRank("search_data__search_vector_content", query)
            ordering += ["rank", "name"]
        else:
            annotations["match_position"] = RawSQL(
                "strpos(LOWER(name), %s)",
                (search_input,),
            )
            ordering = ("match_position", "name")
            search_filter = Q(name__icontains=search_input)

    return search_filter, annotations, ordering

class OperationalAreaAutocomplete(autocomplete.Select2QuerySetView):
    paginate_by = None

    def get_queryset(self):
        if not self.request.user.is_authenticated:
            return OperationalArea.objects.none()

        qs = self.request.user.tenant.operationalarea_set.all()
        active = self.forwarded.get("active", None)
        inactive = self.forwarded.get("inactive")

        if active:
            qs = qs.filter(archived=False)
        elif inactive:
            qs = qs.filter(archived=True)

        # Filter based on selected values for assessment and internal controls retrieved from session
        session_filters = Q()
        assessment_id = self.request.GET.get("assessment_id")
        if assessment_id:
            assessment_selected = self.request.session.get(
                f"assessment-{assessment_id}-operational_areas",
                None,
            )
            if assessment_selected is not None:
                session_filters |= Q(id__in=assessment_selected)

        internal_control_id = self.request.GET.get("internal_control_id")
        if internal_control_id:
            internal_control_selected = self.request.session.get(
                f"internal-control-{internal_control_id}-operational_areas",
                None,
            )
            if internal_control_selected is not None:
                session_filters |= Q(id__in=internal_control_selected)

        text_search_filters, annotations, ordering = handle_text_search_ordering(self.q)


        # bulk assessment exclusions so users cannot add and remove the same records at once
        operational_areas = self.forwarded.get("operational_areas", None)
        remove_operational_areas = self.forwarded.get("remove_operational_areas", None)

        if operational_areas is not None:
            qs = qs.exclude(id__in=operational_areas)
        if remove_operational_areas:
            qs = qs.exclude(id__in=remove_operational_areas)

        # calendar event/cap use_assessment_data filters
        assessment_data_filters = Q()
        use_assessment_data = self.forwarded.get("use_assessment_data", None)
        if use_assessment_data:
            applicable_legislation_ids = self.forwarded.get("associated_legislation", None)
            if applicable_legislation_ids:
                applicable_legislation_ids = list(map(int, applicable_legislation_ids.split(",")))
                assessment_ids = ApplicableLegislation.objects.filter(id__in=applicable_legislation_ids).values_list(
                    "assessment_id", flat=True)
                assessment_operational_areas_link_table = ApplicableLegislationAssessment.operational_areas.through
                operational_area_ids = assessment_operational_areas_link_table.objects.filter(
                    applicablelegislationassessment_id__in=assessment_ids).values_list(
                    "operationalarea_id", flat=True,
                )
                assessment_data_filters &= Q(id__in=operational_area_ids)

        return qs.filter(
            session_filters & text_search_filters & assessment_data_filters,
        ).annotate(**annotations).order_by(*ordering)


class ProgramAutocomplete(autocomplete.Select2QuerySetView):
    paginate_by = None

    def get_queryset(self):
        if not self.request.user.is_authenticated:
            return Program.objects.none()

        qs = self.request.user.tenant.program_set.all()
        active = self.forwarded.get("active", None)
        inactive = self.forwarded.get("inactive")

        if active:
            qs = qs.filter(archived=False)
        elif inactive:
            qs = qs.filter(archived=True)

        # Filter based on selected values for assessment and internal controls retrieved from session
        session_filters = Q()
        assessment_id = self.request.GET.get("assessment_id")
        if assessment_id:
            assessment_selected = self.request.session.get(
                f"assessment-{assessment_id}-programs",
                None,
            )
            if assessment_selected is not None:
                session_filters |= Q(id__in=assessment_selected)

        internal_control_id = self.request.GET.get("internal_control_id")
        if internal_control_id:
            internal_control_selected = self.request.session.get(
                f"internal-control-{internal_control_id}-programs",
                None,
            )
            if internal_control_selected is not None:
                session_filters |= Q(id__in=internal_control_selected)

        text_search_filters, annotations, ordering = handle_text_search_ordering(self.q)

        # bulk assessment exclusions so users cannot add and remove the same records at once
        programs = self.forwarded.get("programs", None)
        remove_programs = self.forwarded.get("remove_programs", None)
        if programs is not None:
            qs = qs.exclude(id__in=programs)
        if remove_programs:
            qs = qs.exclude(id__in=remove_programs)

        # calendar event/cap use_assessment_data filters
        assessment_data_filters = Q()
        use_assessment_data = self.forwarded.get("use_assessment_data", None)
        if use_assessment_data:
            applicable_legislation_ids = self.forwarded.get("associated_legislation", None)
            if applicable_legislation_ids:
                applicable_legislation_ids = list(map(int, applicable_legislation_ids.split(",")))
                assessment_ids = ApplicableLegislation.objects.filter(id__in=applicable_legislation_ids).values_list(
                    "assessment_id", flat=True)
                assessment_programs_link_table = ApplicableLegislationAssessment.programs.through
                program_ids = assessment_programs_link_table.objects.filter(
                    applicablelegislationassessment_id__in=assessment_ids).values_list(
                    "program_id", flat=True,
                )
                assessment_data_filters &= Q(id__in=program_ids)

        return qs.filter(
            session_filters & text_search_filters & assessment_data_filters,
        ).annotate(**annotations).order_by(*ordering)


class ActivityAutocomplete(autocomplete.Select2QuerySetView):
    paginate_by = None

    def get_queryset(self):
        if not self.request.user.is_authenticated:
            return Activity.objects.none()

        qs = self.request.user.tenant.activity_set.all()
        active = self.forwarded.get("active", None)
        inactive = self.forwarded.get("inactive")

        if active:
            qs = qs.filter(archived=False)
        elif inactive:
            qs = qs.filter(archived=True)

        # Filter based on selected values for assessment and internal controls retrieved from session
        session_filters = Q()
        assessment_id = self.request.GET.get("assessment_id")
        if assessment_id:
            assessment_selected = self.request.session.get(
                f"assessment-{assessment_id}-activities",
                None,
            )
            if assessment_selected is not None:
                session_filters |= Q(id__in=assessment_selected)

        internal_control_id = self.request.GET.get("internal_control_id")
        if internal_control_id:
            internal_control_selected = self.request.session.get(
                f"internal-control-{internal_control_id}-activities",
                None,
            )
            if internal_control_selected is not None:
                session_filters |= Q(id__in=internal_control_selected)

        text_search_filters, annotations, ordering = handle_text_search_ordering(self.q)

        # bulk assessment exclusions so users cannot add and remove the same records at once
        activities = self.forwarded.get("activities", None)
        remove_activities = self.forwarded.get("remove_activities", None)
        if activities is not None:
            qs = qs.exclude(id__in=activities)
        if remove_activities:
            qs = qs.exclude(id__in=remove_activities)

        # calendar event/cap use_assessment_data filters
        assessment_data_filters = Q()
        use_assessment_data = self.forwarded.get("use_assessment_data", None)
        if use_assessment_data:
            applicable_legislation_ids = self.forwarded.get("associated_legislation", None)
            if applicable_legislation_ids:
                applicable_legislation_ids = list(map(int, applicable_legislation_ids.split(",")))
                assessment_ids = ApplicableLegislation.objects.filter(id__in=applicable_legislation_ids).values_list(
                    "assessment_id", flat=True)
                assessment_activities_link_table = ApplicableLegislationAssessment.activities.through
                activity_ids = assessment_activities_link_table.objects.filter(
                    applicablelegislationassessment_id__in=assessment_ids).values_list(
                    "activity_id", flat=True,
                )
                assessment_data_filters &= Q(id__in=activity_ids)

        return qs.filter(
            session_filters & text_search_filters & assessment_data_filters,
        ).annotate(**annotations).order_by(*ordering)


class UserAutocomplete(autocomplete.Select2QuerySetView):
    paginate_by = None

    def get_queryset(self):
        if not self.request.user.is_authenticated:
            return User.objects.none()

        qs = User.objects.active_set().filter(tenant=self.request.user.tenant)

        # Filter based on selected values for assessment and internal controls retrieved from session
        session_filters = Q()
        assessment_id = self.request.GET.get("assessment_id")
        if assessment_id:
            assessment_selected = self.request.session.get(
                f"assessment-{assessment_id}-assigned_to",
                None,
            )
            if assessment_selected is not None:
                session_filters |= Q(id__in=assessment_selected)

        internal_control_id = self.request.GET.get("internal_control_id")
        if internal_control_id:
            internal_control_selected = self.request.session.get(
                f"internal-control-{internal_control_id}-assigned_to",
                None,
            )
            if internal_control_selected is not None:
                session_filters |= Q(id__in=internal_control_selected)

        text_search_filters, annotations, ordering = handle_text_search_ordering(self.q)

        # bulk assessment exclusions so users cannot add and remove the same records at once
        assigned_to = self.forwarded.get("assigned_to", None)
        remove_assigned_to = self.forwarded.get("remove_assigned_to", None)
        if assigned_to is not None:
            qs = qs.exclude(id__in=assigned_to)
        if remove_assigned_to:
            qs = qs.exclude(id__in=remove_assigned_to)

        # calendar event/cap use_assessment_data filters
        assessment_data_filters = Q()
        use_assessment_data = self.forwarded.get("use_assessment_data", None)
        if use_assessment_data:
            applicable_legislation_ids = self.forwarded.get("associated_legislation", None)
            if applicable_legislation_ids:
                applicable_legislation_ids = list(map(int, applicable_legislation_ids.split(",")))
                assessment_ids = ApplicableLegislation.objects.filter(id__in=applicable_legislation_ids).values_list(
                    "assessment_id", flat=True)
                assessment_user_ids = ApplicableLegislationAssessmentUser.objects.filter(
                    assessment_id__in=assessment_ids).values_list(
                    "user_id", flat=True,
                )
                assessment_data_filters &= Q(id__in=assessment_user_ids)

        return qs.filter(
            session_filters & text_search_filters & assessment_data_filters,
        ).annotate(**annotations).order_by(*ordering)


class ControlDocumentAutocomplete(autocomplete.Select2QuerySetView):
    paginate_by = None

    def get_queryset(self):
        if not self.request.user.is_authenticated:
            return ControlDocument.objects.none()


        active = self.forwarded.get("active", None) or self.request.GET.get("active")
        inactive = self.forwarded.get("inactive")
        use_assessment_data = self.forwarded.get("use_assessment_data", None)

        if active:
            qs = ControlDocument.objects.active().filter(tenant=self.request.user.tenant)
        elif inactive:
            qs = ControlDocument.objects.inactive().filter(tenant=self.request.user.tenant)
        else:
            qs = ControlDocument.objects.filter(tenant=self.request.user.tenant)

        text_search_filters, annotations, ordering = handle_text_search_ordering(self.q)

        # calendar event/cap use_assessment_data filters
        assessment_data_filters = Q()
        if use_assessment_data:
            applicable_legislation_ids = self.forwarded.get("associated_legislation", None)
            if applicable_legislation_ids:
                applicable_legislation_ids = list(map(int, applicable_legislation_ids.split(",")))
                assessment_ids = ApplicableLegislation.objects.filter(id__in=applicable_legislation_ids).values_list(
                    "assessment_id", flat=True)
                control_document_ids = InternalControl.objects.filter(assessment_id__in=assessment_ids).values_list(
                    "control_document_id", flat=True)
                assessment_data_filters &= Q(id__in=control_document_ids)

        return qs.filter(
            text_search_filters & assessment_data_filters,
        ).annotate(**annotations).order_by(*ordering)


class ApplicableLegislationAutocomplete(autocomplete.Select2QuerySetView):
    def get_queryset(self):
        if not self.request.user.is_authenticated:
            return ApplicableLegislation.objects.none()

        qs = self.request.user.tenant.applicablelegislation_set.all()

        if self.q:
            search_filters = Q(
                layout__fragment__search_data__search_vector_content=EnhancedSearchQuery(
                    self.q,
                    search_type="conformii",
                    config="english",
                ),
            ) | Q(
                layout__document__search_data__search_vector_title=EnhancedSearchQuery(
                    self.q,
                    search_type="conformii",
                    config="english",
                ),
            )

            qs = qs.filter(search_filters)
        return qs
