from functools import partial

from django.contrib.auth.mixins import LoginRequiredMixin
from django.db.models import F
from django.db.models import Value
from django.http import HttpResponse
from django.shortcuts import redirect
from django.shortcuts import render
from django.urls import reverse_lazy
from django.views.generic import FormView
from django.views.generic import RedirectView
from django_htmx.http import trigger_client_event
from waffle import flag_is_active

from apps.briefcase.forms import ApplicableLegislationReasonForm
from apps.briefcase.models.applicable_legislation import ApplicableLegislation
from apps.common.views import CustomPermissionRequiredMixin
from apps.common.views.mixins import CustomWaffleFlagMixin
from apps.document.models import Document
from apps.document.models import DocumentLayout
from apps.document.models import LayoutType
from apps.tenants.models import TenantFeatureFlag


class ToggleBriefcaseView(LoginRequiredMixin, CustomPermissionRequiredMixin, FormView):
    permission_required = "briefcase.change_applicablelegislation"
    form_class = ApplicableLegislationReasonForm
    template_name = "briefcase/toggle_briefcase_reason.html"

    def get_form_kwargs(self):
        form_kwargs = super().get_form_kwargs()
        form_data = self.get_form_data()
        if self.request.method == "GET":
            form_kwargs["initial"] = form_data
        else:
            form_kwargs["data"] = form_data
        return form_kwargs

    def get_form_data(self):
        if self.request.method == "GET":
            return self.request.GET
        return self.request.POST

    def get_action_verbose(self, action):
        if action == "add":
            return "Add to Briefcase"
        elif action == "remove":
            return "Remove from Briefcase"
        return "No Action"

    def get_context_data(self, **kwargs):
        form_data = self.get_form_data()
        action = form_data.get("action")
        layout_id = form_data.get("layout_id")
        context_data = super().get_context_data(**kwargs)
        if layout_id:
            layout = DocumentLayout.objects.filter(id=layout_id).annotate(
                highlighted_fragment_name=F("fragment__name"),
                highlighted_fragment_content=F("fragment__content")
            ).first()
            context_data["layout"] = layout
        context_data["form_title"] = self.get_action_verbose(action)
        context_data["content_layout_type"] = LayoutType.CONTENT.value
        context_data["heading_layout_type"] = LayoutType.HEADING.value
        context_data["root_layout_type"] = LayoutType.ROOT.value
        context_data["RenderOption"] = DocumentLayout.RenderOption
        context_data["render_layouts_on_submit"] = True
        return context_data

    def form_valid(self, form):
        cleaned_data = form.cleaned_data
        include_all_child = cleaned_data.get("include_all_child")
        layout_id = cleaned_data.get("layout_id")
        action = cleaned_data.get("action")
        session_key = cleaned_data.get("session_key")
        reason = cleaned_data.get("reason")
        render_layouts_on_submit = cleaned_data.get("render_layouts_on_submit", 1)
        request = self.request
        tenant = request.user.tenant

        verbose_action = self.get_action_verbose(action)
        response_context = {
            "session_key": session_key,
            "render_layouts_on_submit": render_layouts_on_submit,
            "clear_selection_footer": True,
        }

        if layout_id:
            layout = DocumentLayout.objects.get(id=layout_id)
            root_layout = layout.document.root_layout
            applicable_legislation = ApplicableLegislation.objects.filter(layout_id=layout_id, tenant=tenant).first()
            response_context["clear_selection_footer"] = False

            if include_all_child:
                descendants = layout.get_descendants(include_self=True).filter(current=True).annotate(
                    applicable_to_tenant=Value(int(applicable_legislation is None)),
                )
                if applicable_legislation:
                    applicable_legislations = tenant.applicablelegislation_set.filter(
                        layout__in=descendants,
                    )
                    ApplicableLegislation.bulk_remove_from_briefcase(
                        user=request.user,
                        object_ids=list(applicable_legislations.values_list("id", flat=True)),
                        documents=[layout.document],
                        comment=reason,
                    )
                else:
                    ApplicableLegislation.bulk_add_applicable_legislation_to_briefcase(
                        user=request.user,
                        layout_ids=list(descendants.values_list("id", flat=True)),
                        documents=[layout.document],
                        comment=reason,
                    )

                selected_layouts = list(descendants)
                if root_layout not in selected_layouts:
                    root_layout.applicable_to_tenant = root_layout.applicablelegislation_set.filter(tenant=tenant).exists()
                    selected_layouts.append(root_layout)

                response_context["selected_layouts"] = selected_layouts
                response_context["clear_selection_footer"] = False
                response = render(
                    request,
                    "document/content_layout_library_actions_response.partial.html",
                    context=response_context,
                )

            else:
                if applicable_legislation:
                    applicable_legislation.remove_from_briefcase(user=request.user, comment=reason)
                    layout.applicable_to_tenant = False
                else:
                    ApplicableLegislation.add_applicable_legislation_to_briefcase(tenant, layout_id, user=request.user, comment=reason)
                    layout.applicable_to_tenant = True

                selected_layouts = [layout]
                if root_layout not in selected_layouts:
                    root_layout.applicable_to_tenant = root_layout.applicablelegislation_set.filter(tenant=tenant).exists()
                    selected_layouts.append(root_layout)
                response_context["selected_layouts"] = selected_layouts
                response = render(
                    request,
                    "document/content_layout_library_actions_response.partial.html",
                    context=response_context,
                )

            return trigger_client_event(
                response,
                "showToastMessageAndCloseModal",
                params={"message": f"Successfully {verbose_action}."},
            )

        elif action and session_key:
            session_data = request.session[session_key]
            selected = session_data.get("selected", [])
            response_context["selected"] = selected

            if action == "add":
                applicable_to_tenant = Value(1)
                action_str = f"Added {len(selected)} clauses to"
                selected_layouts = DocumentLayout.objects.library_current_set().filter(id__in=selected).annotate(
                    applicable_to_tenant=applicable_to_tenant,
                )
                document_ids = list(selected_layouts.values_list("document_id", flat=True).distinct())
                documents = Document.objects.filter(id__in=document_ids)
                ApplicableLegislation.bulk_add_applicable_legislation_to_briefcase(
                    user=request.user,
                    layout_ids=list(selected_layouts.values_list("id", flat=True)),
                    documents=documents,
                    comment=reason,
                )

                # Include the root layout node since it can be added in the background
                selected_layouts = list(selected_layouts)
                for document in documents:
                    document_root_layout = document.root_layout
                    if document_root_layout not in selected_layouts:
                        document_root_layout.applicable_to_tenant = applicable_to_tenant
                        selected_layouts.append(document_root_layout)
                response_context["selected_layouts"] = selected_layouts

            elif action == "remove":
                applicable_to_tenant = Value(0)
                action_str = f"Removed {len(selected)} clauses from"
                selected_layouts = DocumentLayout.objects.library_current_set().filter(id__in=selected).annotate(
                    applicable_to_tenant=applicable_to_tenant,
                )
                applicable_legislations_to_delete = ApplicableLegislation.objects.filter(tenant=tenant, layout_id__in=selected)
                document_ids = list(applicable_legislations_to_delete.values_list("layout__document_id", flat=True).distinct())
                documents = Document.objects.filter(id__in=document_ids)
                ApplicableLegislation.bulk_remove_from_briefcase(
                    object_ids=list(applicable_legislations_to_delete.values_list("id", flat=True)),
                    user=request.user,
                    comment=reason,
                    documents=documents,
                )
                response_context["selected_layouts"] = selected_layouts

            else:
                raise Exception("Invalid action specified.")

            response = render(
                request,
                "document/content_layout_library_actions_response.partial.html",
                context=response_context,
            )

            # clear all selected layouts
            request.session[session_key] = {"selected": []}

            return trigger_client_event(
                response,
                "showToastMessageAndCloseModalAndHandleCheckboxes",
                params={
                    "message": f"Successfully {action_str} Briefcase.",
                    "toggle": False,
                },
            )

        else:
            raise Exception("Layout Id, Action, or Sesssion Key is not specified.")


class BriefcaseRedirectView(LoginRequiredMixin, CustomWaffleFlagMixin, RedirectView):
    """
    Checks the user's feature flags and permissions to redirect to the modules available
    """
    briefcase_waffle_permission_redirects = [
        {
            "permission": "briefcase.view_applicablelegislation",
            "waffle_flag": TenantFeatureFlag.Features.APPLICABLE_LEGISLATION.value,
            "redirect_url": reverse_lazy("briefcase:applicable-legislations"),
        },
        {
            "permission": "document.view_tenant_document",
            "waffle_flag": TenantFeatureFlag.Features.CONDITIONS_AND_COMMITMENTS.value,
            "redirect_url": reverse_lazy("briefcase:conditions-and-commitments:current:list"),
        },
        {
            "permission": "calendar.view_calendareventoccurrence",
            "waffle_flag": TenantFeatureFlag.Features.CALENDAR.value,
            "redirect_url": reverse_lazy("briefcase:calendar-event:list"),
        },
        {
            "permission": "briefcase.view_correctiveactionplan",
            "waffle_flag": TenantFeatureFlag.Features.CORRECTIVE_ACTION_PLAN.value,
            "redirect_url": reverse_lazy("briefcase:corrective-action-plan:list"),
        },
        {
            "permission": "briefcase.view_applicabilityreview",
            "waffle_flag": TenantFeatureFlag.Features.APPLICABILITY_REVIEW.value,
            "redirect_url": reverse_lazy("briefcase:applicability-review:list"),
        },
        {
            "permission": "history.view_history",
            "waffle_flag": TenantFeatureFlag.Features.HISTORY.value,
            "redirect_url": reverse_lazy("briefcase:history:index"),
        },
    ]

    def dispatch(self, request, *args, **kwargs):
        func = partial(flag_is_active, request)
        valid_waffles = []
        has_permissions = []
        print ("DISPATCHING BRIEFCASE", self.briefcase_waffle_permission_redirects)
        print (dir(request), func)
        for info in self.briefcase_waffle_permission_redirects:
            active = self.validate_waffle(
                info["waffle_flag"],
                func,
            )
            print ("INFO: ", info)
            print ("ACTIVE: ", active)
            has_permission = request.user.has_perm(info["permission"])
            print ("HAS_PERMISSION: ", has_permission)
            redirect_url = info["redirect_url"]
            print ("ACTIVATE AND PERM", active and has_permission)
            if active and has_permission:
                return redirect(redirect_url)
            elif active:
                valid_waffles.append(redirect_url)
            elif has_permission:
                has_permissions.append(redirect_url)

        if valid_waffles:
            return redirect(valid_waffles[0])
        elif has_permissions:
            return redirect(has_permissions[0])
        else:
            print ("returning unauth")
            return HttpResponse("Unauthorized", status=401)
