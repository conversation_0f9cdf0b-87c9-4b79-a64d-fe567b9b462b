from collections import defaultdict
from datetime import timedelta

from bootstrap_datepicker_plus.widgets import DatePickerInput
from dal import forward
from dateutil.rrule import DAILY
from dateutil.rrule import MONTHLY
from dateutil.rrule import WEEKLY
from dateutil.rrule import YEARLY
from dateutil.rrule import rrule
from django import forms
from django.contrib import messages
from django.contrib.auth.decorators import login_required
from django.contrib.auth.decorators import permission_required
from django.contrib.auth.mixins import LoginRequiredMixin
from django.contrib.contenttypes.models import ContentType
from django.contrib.postgres.search import SearchHeadline
from django.db.models import F
from django.db.models import Q
from django.http import HttpResponse
from django.http import HttpResponseRedirect
from django.shortcuts import get_object_or_404
from django.shortcuts import render
from django.template.loader import render_to_string
from django.urls import reverse
from django.urls import reverse_lazy
from django.utils.translation import gettext_lazy as _
from django.views.decorators.http import require_GET
from django.views.generic import CreateView
from django.views.generic import View
from view_breadcrumbs.generic import CreateBreadcrumbMixin
from view_breadcrumbs.generic import DetailBreadcrumbMixin

from apps.briefcase.models import Activity
from apps.briefcase.models import ApplicableLegislation
from apps.briefcase.models import ControlDocument
from apps.briefcase.models import OperationalArea
from apps.briefcase.models import Program
from apps.briefcase.views.widgets import BulkAddSelect2MultipleWidget
from apps.calendar.models import CalendarEventOccurrence
from apps.calendar.models import CalendarEventSeries
from apps.calendar.models import OccurrenceUser
from apps.calendar.tasks import schedule_emails_for_calendar_event
from apps.calendar.tasks import send_ce_assigned_email
from apps.comments.models import UserComment
from apps.comments.utils import send_emails_to_mentions
from apps.common.forms import QuillMentionWidget
from apps.common.htmx import HTTPResponseHXRedirect
from apps.common.views import UpdatePermissionRequiredMixin
from apps.document.models import Document
from apps.document.models import DocumentLayout
from apps.document.models import LayoutType
from apps.history.schemas.data import HistoryData
from apps.history.schemas.data import StateAction
from apps.search.postgres.query import EnhancedSearchQuery
from apps.users.models import User
from apps.utils import StateHandler

from .base import CalendarEventViewSetMixin


class CalendarEventSeriesUpdateView(
    CalendarEventViewSetMixin,
    DetailBreadcrumbMixin,
    UpdatePermissionRequiredMixin,
):
    model = CalendarEventSeries
    permission_required_get = "calendar.view_calendareventoccurrence"
    permission_required_post = "calendar.change_calendareventoccurrence"
    fields = [
        "repeats",
        "repeats_every",
        "repeat_ends",
        "recurrence_rule",
    ]
    template_name = "briefcase/calendar_event/create.html"
    crumbs = [
        ("Calendar Events", reverse_lazy("briefcase:calendar-event:list")),
        ("Edit Calendar Event Series", ""),
    ]
    success_message = _("Calendar Event Series Successfully Updated")
    form_cancel_url = reverse_lazy("briefcase:calendar-event:list")

    def get_initial(self, form_class=None):
        initial = super().get_initial()
        occurrence_id = self.request.GET.get("occ_id")
        if occurrence_id:
            occurrence = CalendarEventOccurrence.objects.get(id=occurrence_id)
            initial.update(occurrence.__dict__)
        return initial

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        series = self.get_object()
        # retrieve associated legislations from all occurrences
        occurrences = series.calendareventoccurrence_set.all()
        associated_legislation = ApplicableLegislation.objects.filter(
            tenant=self.request.user.tenant,
            calendareventoccurrence__in=occurrences,
        ).distinct()
        context["object_type"] = "calendar_event_series"
        context["user_comments"] = UserComment.objects.filter(
            content_type=ContentType.objects.get_for_model(
                CalendarEventSeries,
            ),
            object_id=series.id,
        )

        context["associated_legislation"] = associated_legislation
        context["associated_legislation_ids"] = ",".join(
            map(str, list(associated_legislation.values_list("id", flat=True))),
        )
        context["form_cancel_url"] = self.form_cancel_url
        context["readonly"] = not self.has_permission_post
        return context

    def get_form(self, form_class=None):
        form = super().get_form(form_class)
        series = self.get_object()
        tenant = self.request.user.tenant
        tenant_event_types = tenant.calendareventtype_set.values_list("name", flat=True)
        event_type_choices = CalendarEventOccurrence.CalendarEventType.choices + [
            (t, t) for t in tenant_event_types
        ]

        readonly = self.check_form_is_readonly()

        occurrence_operational_areas = list(
            filter(
                lambda oparea_id: oparea_id,
                series.calendareventoccurrence_set.all()
                .values_list("operational_areas", flat=True)
                .distinct(),
            ),
        )

        occurrence_programs = list(
            filter(
                lambda program_id: program_id,
                series.calendareventoccurrence_set.all()
                .values_list("programs", flat=True)
                .distinct(),
            ),
        )

        occurrence_activities = list(
            filter(
                lambda activity_id: activity_id,
                series.calendareventoccurrence_set.all()
                .values_list("activities", flat=True)
                .distinct(),
            ),
        )

        occurrence_control_documents = list(
            filter(
                lambda control_id: control_id,
                series.calendareventoccurrence_set.all()
                .values_list("control_documents", flat=True)
                .distinct(),
            ),
        )

        occurrence_assigned_to = list(
            filter(
                lambda assignee_id: assignee_id,
                series.calendareventoccurrence_set.all()
                .values_list("assigned_to", flat=True)
                .distinct(),
            ),
        )

        form.fields["name"] = forms.CharField(label="Name", required=True)
        form.fields["event_type"] = forms.ChoiceField(
            choices=event_type_choices,
            initial=CalendarEventOccurrence.CalendarEventType.REPORTING_REQUIREMENT.value,
        )
        form.fields["start_date"] = forms.DateField(
            required=True,
            widget=DatePickerInput(),
        )
        form.fields["end_date"] = forms.DateField(
            required=True,
            widget=DatePickerInput(),
        )
        form.fields["recurrence_rule"] = forms.ChoiceField(
            label="",
            required=False,
            choices=CalendarEventSeries.RecurrenceChoices.choices,
        )

        form.fields["comment"] = forms.CharField(
            label="Description",
            required=False,
            widget=QuillMentionWidget(
                attrs={"rows": 3, "label": "Description", "id": f"comment-{self.object.id}", "readonly": readonly}),
        )

        form.fields["use_assessment_data"] = forms.BooleanField(
            required=False,
            label="Use Assessment Data Only",
        )

        form.fields["repeats_every"] = forms.IntegerField(
            required=False,
            label="",
            widget=forms.NumberInput(),
        )
        form.fields["repeat_ends"] = forms.DateField(
            required=False,
            widget=DatePickerInput(),
        )
        form.fields["repeats"] = forms.BooleanField(
            required=False,
            label="Repeats",
            widget=forms.CheckboxInput(
                attrs={"class": "form-check-input", "role": "switch"},
            ),
        )
        form.fields["email_reminders"] = forms.BooleanField(
            required=False,
            widget=forms.CheckboxInput(
                attrs={"class": "form-check-input", "role": "switch"},
            ),
        )
        form.fields["operational_areas"] = forms.ModelMultipleChoiceField(
            queryset=OperationalArea.objects.filter(tenant=self.request.user.tenant),
            required=False,
            widget=BulkAddSelect2MultipleWidget(
                url=reverse_lazy("briefcase:operational_areas-autocomplete"),
                forward=(
                    forward.Const(True, "active"),
                    "use_assessment_data",
                    "associated_legislation",
                ),
                attrs={"class": "text-bg-secondary"},
            ),
        )
        form.fields["programs"] = forms.ModelMultipleChoiceField(
            queryset=Program.objects.filter(tenant=self.request.user.tenant),
            required=False,
            widget=BulkAddSelect2MultipleWidget(
                url=reverse_lazy("briefcase:programs-autocomplete"),
                forward=(
                    forward.Const(True, "active"),
                    "use_assessment_data",
                    "associated_legislation",
                ),
                attrs={"class": "text-bg-secondary"},
            ),
        )

        form.fields["activities"] = forms.ModelMultipleChoiceField(
            queryset=Activity.objects.filter(tenant=self.request.user.tenant),
            required=False,
            widget=BulkAddSelect2MultipleWidget(
                url=reverse_lazy("briefcase:activities-autocomplete"),
                forward=(
                    forward.Const(True, "active"),
                    "use_assessment_data",
                    "associated_legislation",
                ),
                attrs={"class": "text-bg-secondary", "id": "assessment-activity-field"},
            ),
        )

        form.fields["control_documents"] = forms.ModelMultipleChoiceField(
            queryset=ControlDocument.objects.filter(tenant=self.request.user.tenant),
            required=False,
            widget=BulkAddSelect2MultipleWidget(
                url=reverse_lazy("briefcase:control-documents-autocomplete"),
                forward=(
                    forward.Const(True, "active"),
                    "use_assessment_data",
                    "associated_legislation",
                ),
                attrs={
                    "class": "text-bg-secondary",
                },
            ),
        )

        form.fields["assigned_to"] = forms.ModelMultipleChoiceField(
            queryset=User.objects.filter(tenant=self.request.user.tenant),
            required=False,
            widget=BulkAddSelect2MultipleWidget(
                url=reverse_lazy("briefcase:users-autocomplete"),
                forward=("use_assessment_data", "associated_legislation"),
                attrs={
                    "class": "text-bg-secondary",
                },
            ),
        )

        form.fields["operational_areas"].initial = occurrence_operational_areas
        form.fields["programs"].initial = occurrence_programs
        form.fields["activities"].initial = occurrence_activities
        form.fields["control_documents"].initial = occurrence_control_documents
        form.fields["assigned_to"].initial = occurrence_assigned_to

        action = "Update"
        if readonly:
            action = "View"
            self.set_form_to_readonly(form)

        form.form_title = _(f"{action} Calendar Event Series")

        can_change_assignees = self.request.user.has_perm("calendar.change_occurrenceuser")
        if not can_change_assignees:
            form.fields["assigned_to"].disabled = True
            form.fields["assigned_to"].widget.attrs["readonly"] = True
            form.fields["assigned_to"].required = False
            form.fields["assigned_to"].help_text = "You do not have permissions to change assignees"

        return form

    def get_success_url(self):
        return reverse("briefcase:calendar-event:list")

    def create_occurrence(
        self,
        cleaned_data,
        series,
        start_date,
        end_date,
        associated_legislation_ids,
    ):
        occurrence = CalendarEventOccurrence.objects.create(
            name=cleaned_data["name"],
            event_type=cleaned_data["event_type"],
            start_date=start_date,
            end_date=end_date,
            comment=cleaned_data["comment"],
            use_assessment_data=cleaned_data["use_assessment_data"],
            email_reminders=cleaned_data["email_reminders"],
            series=series,
        )

        occurrence.operational_areas.set(cleaned_data["operational_areas"])
        occurrence.programs.set(cleaned_data["programs"])
        occurrence.activities.set(cleaned_data["activities"])
        occurrence.control_documents.set(cleaned_data["control_documents"])
        occurrence.assigned_to.set(cleaned_data["assigned_to"])
        occurrence.associated_legislation.set(associated_legislation_ids)
        occurrence.event_status = occurrence.get_event_status()
        occurrence.save(update_fields=["event_status"])

        notified_reviewers_for_series = list(OccurrenceUser.objects.filter(
            notified=True, calendareventoccurrence__series=series,
        ).values_list("user_id", flat=True).distinct())

        for user in occurrence.assigned_to.all():
            notified = user.id in notified_reviewers_for_series
            review, created = OccurrenceUser.objects.update_or_create(
                user_id=user.id, calendareventoccurrence=occurrence,
                defaults={
                    "notified": notified,
                },
            )

        send_ce_assigned_email(occurrence.id)

        if occurrence.email_reminders:
            schedule_emails_for_calendar_event(occurrence)

    def form_valid(self, form):
        cleaned_data = form.cleaned_data

        # validate start date and end date
        if cleaned_data["start_date"] > cleaned_data["end_date"]:
            form.add_error("start_date", "Start date must be before the end date.")
            return self.form_invalid(form)

        previous_state = StateHandler.serialize_state(
            self.get_object(),
            exclude=["code"],
        )
        documents = []
        # associated legislations
        add_associated_legislations = []
        associated_legislation_ids = list(
            filter(
                lambda al_id: al_id,
                self.request.POST.get("associated_legislation", "").split(","),
            ),
        )
        associated_legislations_link_table = (
            CalendarEventOccurrence.associated_legislation.through
        )
        if associated_legislation_ids and associated_legislation_ids != [""]:
            applicable_legislations = ApplicableLegislation.objects.filter(
                id__in=map(int, associated_legislation_ids),
            )
            associated_legislation_ids = applicable_legislations.values_list(
                "id",
                flat=True,
            )

            document_ids = applicable_legislations.values_list(
                "layout__document_id",
                flat=True,
            ).distinct()
            documents = Document.objects.filter(id__in=document_ids)

        self.object.delete_all_occurrences()
        if form.cleaned_data["repeats"]:
            self.object = form.save(commit=False)
            self.object.tenant = self.request.user.tenant
            self.object.created_by = self.request.user
            self.object.modified_by = self.request.user
            self.object.save()
            start_date = form.cleaned_data["start_date"]
            end_date = form.cleaned_data["end_date"]
            duration = end_date - start_date

            if (
                    cleaned_data["recurrence_rule"]
                    == CalendarEventSeries.RecurrenceChoices.DAILY
            ):
                freq = DAILY
            elif (
                    cleaned_data["recurrence_rule"]
                    == CalendarEventSeries.RecurrenceChoices.WEEKLY
            ):
                freq = WEEKLY
            elif (
                    cleaned_data["recurrence_rule"]
                    == CalendarEventSeries.RecurrenceChoices.MONTHLY
            ):
                freq = MONTHLY
            elif (
                    cleaned_data["recurrence_rule"]
                    == CalendarEventSeries.RecurrenceChoices.YEARLY
            ):
                freq = YEARLY
            else:
                freq = DAILY

            until = cleaned_data["repeat_ends"] or (start_date + timedelta(days=365))

            recurrence_dates = list(
                rrule(
                    freq=freq,
                    interval=cleaned_data["repeats_every"] or 1,
                    dtstart=start_date,
                    until=until,
                ),
            )

            for date in recurrence_dates:
                self.create_occurrence(
                    cleaned_data,
                    self.object,
                    date,
                    date + duration,
                    associated_legislation_ids,
                )
        else:
            self.object = form.save(commit=False)
            self.object.tenant = self.request.user.tenant
            self.object.created_by = self.request.user
            self.object.modified_by = self.request.user
            self.object.save()
            self.create_occurrence(
                cleaned_data,
                self.object,
                cleaned_data["start_date"],
                cleaned_data["end_date"],
                associated_legislation_ids,
            )

        tenant = self.request.user.tenant
        user = self.request.user
        current_state = StateHandler.serialize_state(
            self.object,
            exclude=["code"],
        )
        state_update = StateHandler.normalizer.delta(previous_state, current_state)
        history_data = HistoryData(
            previous_state=previous_state,
            state=current_state,
            state_update=state_update,
            action=StateAction.UPDATE.value,
        )

        model_name = "Calendar Event"
        action = "Updated %s Series"
        first_occurrence = self.object.calendareventoccurrence_set.first()
        if first_occurrence:
            model_name = first_occurrence._meta.verbose_name

        action = action % model_name
        comment = cleaned_data.get("comment")

        self.object.save_history(
            tenant=tenant,
            user=user,
            action=action,
            comment=comment,
            documents=documents,
            data=history_data,
        )

        # Schedule the update for assessment export fields
        occurrence_ids = self.object.calendareventoccurrence_set.values_list("id", flat=True)
        CalendarEventOccurrence.update_export_relations(
            CalendarEventOccurrence.objects.filter(id__in=occurrence_ids),
        )

        # send emails to anyone mentioned in the comment
        send_emails_to_mentions(self.object, user, comment)

        messages.success(self.request, self.success_message)
        return HttpResponseRedirect(self.get_success_url())


class CalendarEventOccurrenceUpdateView(
    CalendarEventViewSetMixin,
    DetailBreadcrumbMixin,
    UpdatePermissionRequiredMixin,
):
    model = CalendarEventOccurrence
    permission_required_get = "calendar.view_calendareventoccurrence"
    permission_required_post = "calendar.change_calendareventoccurrence"
    success_message = _("Calendar Event Occurrence Successfully Updated")
    template_name = "briefcase/calendar_event/update.html"
    crumbs = [
        ("Calendar Events", reverse_lazy("briefcase:calendar-event:list")),
        ("Edit Calendar Event Occurrence", ""),
    ]
    list_view_url = form_cancel_url = reverse_lazy("briefcase:calendar-event:list")
    fields = [
        "name",
        "event_type",
        "comment",
        "use_assessment_data",
        "operational_areas",
        "programs",
        "activities",
        "control_documents",
        "email_reminders",
        "start_date",
        "end_date",
        "assigned_to",
    ]

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        instance = self.get_object()
        reviewers = instance.assigned_to.all()
        reviews = []
        for user in reviewers:
            review, created = OccurrenceUser.objects.get_or_create(
                user=user, calendareventoccurrence=instance,
            )
            reviews.append(
                {
                    "id"     : review.id,
                    "user"   : {"id": review.user.id, "name": review.user.name},
                    "checked": review.checked,
                },
            )
        context["reviews"] = reviews
        context["user"] = self.request.user
        context["associated_legislation"] = instance.associated_legislation.all()
        context["associated_legislation_ids"] = ",".join(
            map(
                str, list(instance.associated_legislation.values_list("id", flat=True)),
            ),
        )
        context["form_cancel_url"] = self.form_cancel_url
        context["object_type"] = "calendar_event_occurrence"
        context["user_comments"] = UserComment.objects.filter(
            content_type=ContentType.objects.get_for_model(
                CalendarEventOccurrence,
            ),
            object_id=instance.id,
        )
        context["readonly"] = not self.has_permission_post
        return context

    def get_form(self, form_class=None):
        form = super().get_form(form_class)
        instance = self.get_object()
        tenant = self.request.user.tenant
        tenant_event_types = tenant.calendareventtype_set.values_list("name", flat=True)
        event_type_choices = CalendarEventOccurrence.CalendarEventType.choices + [
            (t, t) for t in tenant_event_types
        ]

        readonly = self.check_form_is_readonly()

        form.fields["use_assessment_data"] = forms.BooleanField(
            required=False,
            label="Use Assessment Data Only",
        )

        form.fields["comment"] = forms.CharField(
            label="Description",
            required=False,
            widget=QuillMentionWidget(
                attrs={"rows": 3, "label": "Description", "id": f"comment-{self.object.id}", "readonly": readonly}),
        )

        form.fields["event_type"] = forms.ChoiceField(
            choices=event_type_choices,
            initial=CalendarEventOccurrence.CalendarEventType.REPORTING_REQUIREMENT.value,
        )
        form.fields["start_date"] = forms.DateField(
            required=True,
            widget=DatePickerInput(),
        )
        form.fields["end_date"] = forms.DateField(
            required=True,
            widget=DatePickerInput(),
        )

        form.fields["email_reminders"] = forms.BooleanField(
            required=False,
            widget=forms.CheckboxInput(
                attrs={"class": "form-check-input", "role": "switch"},
            ),
        )
        form.fields["operational_areas"] = forms.ModelMultipleChoiceField(
            queryset=OperationalArea.objects.all(),
            required=False,
            widget=BulkAddSelect2MultipleWidget(
                url=reverse_lazy("briefcase:operational_areas-autocomplete"),
                forward=(
                    forward.Const(True, "active"),
                    "use_assessment_data",
                    "associated_legislation",
                ),
                attrs={
                    "class": "text-bg-secondary",
                    "id"   : "assessment-oparea-field",
                },
            ),
        )
        form.fields["programs"] = forms.ModelMultipleChoiceField(
            queryset=Program.objects.all(),
            required=False,
            widget=BulkAddSelect2MultipleWidget(
                url=reverse_lazy("briefcase:programs-autocomplete"),
                forward=(
                    forward.Const(True, "active"),
                    "use_assessment_data",
                    "associated_legislation",
                ),
                attrs={"class": "text-bg-secondary",
                       "id"   : "assessment-program-field",
                       },
            ),
        )

        form.fields["activities"] = forms.ModelMultipleChoiceField(
            queryset=Activity.objects.filter(tenant=self.request.user.tenant),
            required=False,
            widget=BulkAddSelect2MultipleWidget(
                url=reverse_lazy("briefcase:activities-autocomplete"),
                forward=(
                    forward.Const(True, "active"),
                    "use_assessment_data",
                    "associated_legislation",
                ),
                attrs={"class": "text-bg-secondary"},
            ),
        )

        form.fields["control_documents"] = forms.ModelMultipleChoiceField(
            queryset=ControlDocument.objects.all(),
            required=False,
            widget=BulkAddSelect2MultipleWidget(
                url=reverse_lazy("briefcase:control-documents-autocomplete"),
                forward=(
                    forward.Const(True, "active"),
                    "use_assessment_data",
                    "associated_legislation",
                ),
                attrs={
                    "class": "text-bg-secondary",
                },
            ),
        )

        form.fields["assigned_to"] = forms.ModelMultipleChoiceField(
            queryset=User.objects.all(),
            required=False,
            widget=BulkAddSelect2MultipleWidget(
                url=reverse_lazy("briefcase:users-autocomplete"),

                forward=("use_assessment_data", "associated_legislation"),
                attrs={
                    "class"     : "form-control text-bg-secondary",
                    "hx-post"   : reverse(
                        "briefcase:calendar-event:update-reviews",
                        kwargs={"pk": self.object.id},
                    ),
                    "hx-trigger": "changed",
                    "hx-target" : "#reviews-section",
                    "type"      : "search",
                    "hx-swap"   : "innerHTML",
                },
            ),
        )

        action = "Update"
        if readonly:
            action = "View"
            self.set_form_to_readonly(form)

        can_change_assignees = self.request.user.has_perm("calendar.change_occurrenceuser")
        if not can_change_assignees:
            form.fields["assigned_to"].disabled = True
            form.fields["assigned_to"].widget.attrs["readonly"] = True
            form.fields["assigned_to"].required = False
            form.fields["assigned_to"].help_text = "You do not have permissions to change assignees"

        form.form_title = _(f"{action} Calendar Event Occurrence")
        return form

    def get_success_url(self):
        return reverse("briefcase:calendar-event:list")

    def form_invalid(self, form):
        # Print all form errors
        for field, errors in form.errors.items():
            for error in errors:
                print(f"Field: {field}, Error: {error}")

        # Call the parent class's form_invalid method
        return super().form_invalid(form)

    def form_valid(self, form):
        cleaned_data = form.cleaned_data
        # validate start date and end date
        if cleaned_data["start_date"] > cleaned_data["end_date"]:
            form.add_error("start_date", "Start date must be before the end date.")
            return self.form_invalid(form)

        previous_state = StateHandler.serialize_state(
            self.get_object(),
            exclude=["code"],
        )
        associated_legislation_ids = self.request.POST.get(
            "associated_legislation",
            "",
        ).split(",")
        self.object = form.save(commit=False)

        can_change_assignees = self.request.user.has_perm("calendar.change_occurrenceuser")
        if not can_change_assignees:
            form.cleaned_data["assigned_to"] = self.object.assigned_to.all()

        self.object.created_by = self.request.user
        self.object.modified_by = self.request.user
        self.object.save()
        form.save_m2m()

        selected_users = list(map(int, self.request.POST.getlist("reviews")))
        notified_reviewers_for_series = list(OccurrenceUser.objects.filter(
            notified=True, calendareventoccurrence__series=self.object.series,
        ).values_list("user_id", flat=True).distinct())

        for user in self.object.assigned_to.all():
            checked = user.id in selected_users
            notified = user.id in notified_reviewers_for_series
            review, created = OccurrenceUser.objects.update_or_create(
                user_id=user.id,
                calendareventoccurrence=self.object,
                defaults={
                    "checked": checked,
                    "notified": notified,
                },
            )

        send_ce_assigned_email(self.object.id)

        documents = []

        if associated_legislation_ids and associated_legislation_ids != [""]:
            associated_legislation = ApplicableLegislation.objects.filter(
                id__in=map(int, associated_legislation_ids),
            )

            self.object.associated_legislation.clear()
            self.object.associated_legislation.add(*associated_legislation)
            self.object.save()

            document_ids = associated_legislation.values_list(
                "layout__document_id",
                flat=True,
            ).distinct()
            documents = Document.objects.filter(id__in=document_ids)

        self.object.event_status = self.object.get_event_status()
        self.object.save()

        tenant = self.request.user.tenant
        user = self.request.user
        current_state = StateHandler.serialize_state(
            self.object,
            exclude=["code"],
        )
        state_update = StateHandler.normalizer.delta(previous_state, current_state)
        history_data = HistoryData(
            previous_state=previous_state,
            state=current_state,
            state_update=state_update,
            action=StateAction.UPDATE.value,
        )

        model_name = self.object._meta.verbose_name
        action = "Updated %s" % model_name

        comment = cleaned_data.get("comment")

        self.object.save_history(
            tenant=tenant,
            user=user,
            action=action,
            comment=comment,
            documents=documents,
            data=history_data,
        )

        # send emails to anyone mentioned in the comment
        send_emails_to_mentions(self.object, user, comment)

        messages.success(self.request, self.success_message)
        if self.object.email_reminders:
            schedule_emails_for_calendar_event(self.object)

        # Ensure export relations gets updated
        CalendarEventOccurrence.update_export_relations(
            queryset=CalendarEventOccurrence.objects.filter(id=self.object.id),
        )

        return HttpResponseRedirect(self.get_success_url())

    def get(self, request, *args, **kwargs):
        if request.htmx and request.htmx.trigger == "filtered-fields":
            return self.get_filtered_fields()
        return super().get(request, *args, **kwargs)

    def get_filtered_fields(self):
        form = self.get_form()
        assessment = self.get_object().assessment  # Assuming there's an assessment field in your model

        if assessment:
            form.fields["operational_areas"].queryset = assessment.operational_areas.all()
            form.fields["programs"].queryset = assessment.programs.all()
            form.fields["activities"].queryset = assessment.activities.all()

        context = {
            "form": form,
        }

        html = render_to_string("path/to/filtered_fields_template.html", context, request=self.request)
        return HttpResponse(html)


class CalendarEventCreateView(
    CalendarEventViewSetMixin,
    CreateBreadcrumbMixin,
    CreateView,
):
    model = CalendarEventSeries
    permission_required = "calendar.add_calendareventoccurrence"
    fields = [
        "repeats",
        "repeats_every",
        "repeat_ends",
        "recurrence_rule",
    ]
    template_name = "briefcase/calendar_event/create.html"
    crumbs = [
        ("Calendar Events", reverse_lazy("briefcase:calendar-event:list")),
        ("Create", reverse_lazy("briefcase:calendar-event:create")),
    ]
    success_message = _("Calendar Event Successfully Created")
    form_cancel_url = reverse_lazy("briefcase:calendar-event:list")

    def get_initial(self, form_class=None):
        initial = super().get_initial()
        occurrence_id = self.request.GET.get("occ_id", None)
        if occurrence_id:
            occurrence = CalendarEventOccurrence.objects.get(id=occurrence_id)
            initial.update(occurrence.__dict__)
            initial["programs"] = occurrence.programs.all()
            initial["control_documents"] = occurrence.control_documents.all()
            initial["operational_areas"] = occurrence.operational_areas.all()
            initial["assigned_to"] = occurrence.assigned_to.all()
            initial["activities"] = occurrence.activities.all()
        return initial

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        applicable_legislation_id = self.request.GET.get("applicable_legislation_id")
        occurrence_id = self.request.GET.get("occ_id", None)
        if applicable_legislation_id:
            applicable_legislation = ApplicableLegislation.objects.filter(
                id=applicable_legislation_id,
            )
            context["associated_legislation"] = applicable_legislation
            context["associated_legislation_ids"] = ",".join(
                map(str, list(applicable_legislation.values_list("id", flat=True))),
            )
        elif occurrence_id:
            occurrence = CalendarEventOccurrence.objects.get(id=occurrence_id)
            context["associated_legislation"] = occurrence.associated_legislation.all()
            context["associated_legislation_ids"] = ",".join(
                map(
                    str,
                    list(occurrence.associated_legislation.values_list("id", flat=True)),
                ),
            )
        context["form_cancel_url"] = self.form_cancel_url
        return context

    def get_form(self, form_class=None):
        tenant = self.request.user.tenant
        tenant_event_types = tenant.calendareventtype_set.values_list("name", flat=True)
        event_type_choices = CalendarEventOccurrence.CalendarEventType.choices + [
            (t, t) for t in tenant_event_types
        ]

        form = super().get_form(form_class)
        form.fields["name"] = forms.CharField(label="Name", required=True)
        form.fields["event_type"] = forms.ChoiceField(
            choices=event_type_choices,
            initial=CalendarEventOccurrence.CalendarEventType.REPORTING_REQUIREMENT.value,
        )
        form.fields["start_date"] = forms.DateField(
            required=True,
            widget=DatePickerInput(),
        )
        form.fields["end_date"] = forms.DateField(
            required=True,
            widget=DatePickerInput(),
        )
        form.fields["recurrence_rule"] = forms.ChoiceField(
            label="",
            required=True,
            choices=CalendarEventSeries.RecurrenceChoices.choices,
        )

        form.fields["comment"] = forms.CharField(
            label="Description",
            required=False,
            widget=QuillMentionWidget(attrs={"rows": 3, "label": "Description", "id": "create-comment"}),
        )

        form.fields["use_assessment_data"] = forms.BooleanField(
            required=False,
            label="Use Assessment Data Only",
        )

        form.fields["repeats_every"] = forms.IntegerField(
            required=False,
            label="",
            widget=forms.NumberInput(),
        )
        form.fields["repeat_ends"] = forms.DateField(
            required=False,
            widget=DatePickerInput(),
        )
        form.fields["repeats"] = forms.BooleanField(
            required=False,
            label="Repeats",
            widget=forms.CheckboxInput(
                attrs={"class": "form-check-input", "role": "switch"},
            ),
        )
        form.fields["email_reminders"] = forms.BooleanField(
            required=False,
            widget=forms.CheckboxInput(
                attrs={"class": "form-check-input", "role": "switch"},
            ),
        )
        form.fields["operational_areas"] = forms.ModelMultipleChoiceField(
            queryset=OperationalArea.objects.all(),
            required=False,
            widget=BulkAddSelect2MultipleWidget(
                url=reverse_lazy("briefcase:operational_areas-autocomplete"),
                forward=(
                    forward.Const(True, "active"),
                    "use_assessment_data",
                    "associated_legislation",
                ),
                attrs={"class": "text-bg-secondary"},
            ),
        )
        form.fields["programs"] = forms.ModelMultipleChoiceField(
            queryset=Program.objects.all(),
            required=False,
            widget=BulkAddSelect2MultipleWidget(
                url=reverse_lazy("briefcase:programs-autocomplete"),
                forward=(
                    forward.Const(True, "active"),
                    "use_assessment_data",
                    "associated_legislation",
                ),
                attrs={"class": "text-bg-secondary"},
            ),
        )

        form.fields["activities"] = forms.ModelMultipleChoiceField(
            queryset=Activity.objects.filter(tenant=self.request.user.tenant),
            required=False,
            widget=BulkAddSelect2MultipleWidget(
                url=reverse_lazy("briefcase:activities-autocomplete"),
                forward=(
                    forward.Const(True, "active"),
                    "use_assessment_data",
                    "associated_legislation",
                ),
                attrs={"class": "text-bg-secondary", "id": "assessment-activity-field"},
            ),
        )

        form.fields["control_documents"] = forms.ModelMultipleChoiceField(
            queryset=ControlDocument.objects.all(),
            required=False,
            widget=BulkAddSelect2MultipleWidget(
                url=reverse_lazy("briefcase:control-documents-autocomplete"),
                forward=(
                    forward.Const(True, "active"),
                    "use_assessment_data",
                    "associated_legislation",
                ),
                attrs={
                    "class": "text-bg-secondary",
                },
            ),
        )

        form.fields["assigned_to"] = forms.ModelMultipleChoiceField(
            queryset=User.objects.all(),
            required=False,
            widget=BulkAddSelect2MultipleWidget(
                url=reverse_lazy("briefcase:users-autocomplete"),
                forward=("use_assessment_data", "associated_legislation"),
                attrs={
                    "class": "text-bg-secondary",
                },
            ),
        )
        form.form_title = _("Create Calendar Event")

        can_change_assignees = self.request.user.has_perm("calendar.change_occurrenceuser")
        if not can_change_assignees:
            form.fields["assigned_to"].disabled = True
            form.fields["assigned_to"].widget.attrs["readonly"] = True
            form.fields["assigned_to"].required = False
            form.fields["assigned_to"].help_text = "You do not have permissions to change assignees"

        return form

    def get_success_url(self):
        return reverse("briefcase:calendar-event:list")

    def create_occurrence(
        self,
        cleaned_data,
        series,
        start_date,
        end_date,
        associated_legislation_ids,
    ):
        occurrence = CalendarEventOccurrence.objects.create(
            name=cleaned_data["name"],
            event_type=cleaned_data["event_type"],
            start_date=start_date,
            end_date=end_date,
            comment=cleaned_data["comment"],
            use_assessment_data=cleaned_data["use_assessment_data"],
            email_reminders=cleaned_data["email_reminders"],
            series=series,
        )

        occurrence.operational_areas.set(cleaned_data["operational_areas"])
        occurrence.programs.set(cleaned_data["programs"])
        occurrence.activities.set(cleaned_data["activities"])
        occurrence.control_documents.set(cleaned_data["control_documents"])
        occurrence.assigned_to.set(cleaned_data["assigned_to"])
        occurrence.associated_legislation.set(associated_legislation_ids)
        occurrence.event_status = occurrence.get_event_status()
        occurrence.save(update_fields=["event_status"])

        notified_reviewers_for_series = list(OccurrenceUser.objects.filter(
            notified=True, calendareventoccurrence__series=series,
        ).values_list("user_id", flat=True).distinct())

        for user in occurrence.assigned_to.all():
            notified = user.id in notified_reviewers_for_series
            review, created = OccurrenceUser.objects.update_or_create(
                user_id=user.id, calendareventoccurrence=occurrence,
                defaults={
                    "notified": notified,
                },
            )

        send_ce_assigned_email(occurrence.id)

        if occurrence.email_reminders:
            schedule_emails_for_calendar_event(occurrence)

    def form_valid(self, form):
        cleaned_data = form.cleaned_data

        # validate start date and end date
        if cleaned_data["start_date"] > cleaned_data["end_date"]:
            form.add_error("start_date", "Start date must be before the end date.")
            return self.form_invalid(form)

        # associated legislations
        add_associated_legislations = []
        associated_legislation_ids = list(
            filter(
                lambda al_id: al_id,
                self.request.POST.get("associated_legislation", "").split(","),
            ),
        )
        associated_legislations_link_table = (
            CalendarEventOccurrence.associated_legislation.through
        )
        if associated_legislation_ids and associated_legislation_ids != [""]:
            associated_legislation_ids = ApplicableLegislation.objects.filter(
                id__in=map(int, associated_legislation_ids),
            ).values_list("id", flat=True)

        documents = []
        if associated_legislation_ids:
            document_ids = (
                ApplicableLegislation.objects.filter(id__in=associated_legislation_ids)
                .values_list("layout__document_id", flat=True)
                .distinct()
            )
            documents = Document.objects.filter(id__in=document_ids)

        if form.cleaned_data["repeats"]:
            self.object = form.save(commit=False)
            self.object.tenant = self.request.user.tenant
            self.object.created_by = self.request.user
            self.object.modified_by = self.request.user
            self.object.save()
            start_date = form.cleaned_data["start_date"]
            end_date = form.cleaned_data["end_date"]
            duration = end_date - start_date

            if (
                    cleaned_data["recurrence_rule"]
                    == CalendarEventSeries.RecurrenceChoices.DAILY
            ):
                freq = DAILY
            elif (
                    cleaned_data["recurrence_rule"]
                    == CalendarEventSeries.RecurrenceChoices.WEEKLY
            ):
                freq = WEEKLY
            elif (
                    cleaned_data["recurrence_rule"]
                    == CalendarEventSeries.RecurrenceChoices.MONTHLY
            ):
                freq = MONTHLY
            elif (
                    cleaned_data["recurrence_rule"]
                    == CalendarEventSeries.RecurrenceChoices.YEARLY
            ):
                freq = YEARLY
            else:
                freq = DAILY

            until = cleaned_data["repeat_ends"] or (start_date + timedelta(days=365))

            recurrence_dates = list(
                rrule(
                    freq=freq,
                    interval=cleaned_data["repeats_every"] or 1,
                    dtstart=start_date,
                    until=until,
                ),
            )

            for date in recurrence_dates:
                self.create_occurrence(
                    cleaned_data,
                    self.object,
                    date,
                    date + duration,
                    associated_legislation_ids,
                )
        else:
            self.object = form.save(commit=False)
            self.object.tenant = self.request.user.tenant
            self.object.created_by = self.request.user
            self.object.modified_by = self.request.user
            self.object.save()
            self.create_occurrence(
                cleaned_data,
                self.object,
                cleaned_data["start_date"],
                cleaned_data["end_date"],
                associated_legislation_ids,
            )

        comment = cleaned_data.get("comment", "")

        user = self.request.user
        tenant = user.tenant

        model_name = self.object._meta.verbose_name
        history_object = self.object
        first_occurrence = self.object.calendareventoccurrence_set.first()
        if first_occurrence:
            model_name = first_occurrence._meta.verbose_name
            history_object = first_occurrence

        action = "Created %s" % model_name

        history_object.save_history(
            tenant=tenant,
            user=user,
            action=action,
            comment="",
            documents=documents,
            data=HistoryData(
                action=StateAction.CREATE.value,
                state=StateHandler.serialize_state(
                    self.object,
                    exclude=["code"],
                ),
            ),
        )

        # send email to anyone mentioned in comment
        send_emails_to_mentions(self.object, user, comment)

        messages.success(self.request, self.success_message)
        return HttpResponseRedirect(self.get_success_url())


class CopyCalendarEventView(CalendarEventCreateView):
    pass


class UpdateReviewsView(LoginRequiredMixin, View):
    def post(self, request, pk, *args, **kwargs):
        occurrence = get_object_or_404(CalendarEventOccurrence, pk=pk)
        assigned_to_ids = request.POST.getlist("assigned_to")

        # Fetch the selected users
        users = User.objects.filter(id__in=assigned_to_ids)

        # Update or create OccurrenceUser instances
        reviews = []
        for user in users:
            review, created = OccurrenceUser.objects.get_or_create(
                user=user, calendareventoccurrence=occurrence,
            )
            reviews.append({
                "id"     : review.id,
                "user"   : {"id": review.user.id, "name": review.user.name},
                "checked": review.checked,
            })

        context = {
            "reviews"             : reviews,
            "object"              : occurrence,
            "user"                : request.user,  # Ensure you have access to the current user
            "no_controls_required": False,  # Adjust based on your logic
        }

        # Render the partial template
        html = render_to_string("briefcase/calendar_event/reviews.partial.html", context, request=request)
        return HttpResponse(html)

def save_selections(request):
    if request.method == "POST":
        selected_ids = request.POST.getlist("selectedlegislation")
        tenant_id = request.user.tenant.id
        selected_legislations = ApplicableLegislation.objects.filter(
            layout_id__in=selected_ids,
            tenant_id=tenant_id,
        )
        selected_legislation_ids = ",".join(
            map(str, list(selected_legislations.values_list("id", flat=True))),
        )
        print(selected_legislation_ids)
        return render(
            request,
            "briefcase/calendar_event/selected_applicable_legislation.html",
            {
                "selected_applicable_legislations": selected_legislations,
                "associated_legislation"          : selected_legislation_ids,
            },
        )


@require_GET
def search_applicable_legislations(request):
    query = request.GET.get("query", "")
    document_id = request.GET.get("document_id")
    fetch_count = request.GET.get("fetch_count")
    tenant = request.user.tenant

    applicable_layout_filter = Q(
        tenant=tenant,
        layout__document__archived=False,
        layout__document__published=True,
        layout__current=True,
        layout__fragment__isnull=False,
    )

    if document_id:
        applicable_layout_filter &= Q(layout__document_id=document_id)
        highlight_document_name_annotation = F("layout__document__name")
        highlight_fragment_content_annotation = F("layout__fragment__content")
        highlight_fragment_name_annotation = F("layout__fragment__name")

        if query:
            search_query = EnhancedSearchQuery(query, search_type="conformii", config="english")
            applicable_layout_filter &= Q(layout__fragment__search_data__search_vector_content=search_query)
            highlight_document_name_annotation = SearchHeadline(
                highlight_document_name_annotation,
                search_query,
                start_sel='<mark class="search-match">',
                stop_sel="</mark>",
                config="english",
                highlight_all=True,
            )

            highlight_fragment_name_annotation = SearchHeadline(
                highlight_fragment_name_annotation,
                search_query,
                start_sel='<mark class="search-match">',
                stop_sel="</mark>",
                config="english",
                highlight_all=True,
            )

            highlight_fragment_content_annotation = SearchHeadline(
                highlight_fragment_content_annotation,
                search_query,
                start_sel='<mark class="search-match">',
                stop_sel="</mark>",
                config="english",
                highlight_all=True,
            )

        applicable_layouts = ApplicableLegislation.objects.filter(
            applicable_layout_filter,
        ).select_related(
            "layout",
            "layout__document",
            "layout__document__jurisdiction",
            "layout__fragment",
        ).prefetch_related(
            "layout__fragment__search_data",
            "layout__document__search_data",
        ).annotate(
            highlight_document_name_annotation=highlight_document_name_annotation,
            highlight_fragment_name_annotation=highlight_fragment_name_annotation,
            highlight_fragment_content_annotation=highlight_fragment_content_annotation,
        )

        print("getting values from applicable legislations...")

        results = applicable_layouts.values(
            "layout__id",
            "layout__document__jurisdiction__name",
            "layout__document__jurisdiction_id",
            "layout__document__id",
            "layout__document__requires_license",
            "layout__layout_type",
            "layout__render_option",
            "highlight_document_name_annotation",
            "highlight_fragment_name_annotation",
            "highlight_fragment_content_annotation",
            "layout__dfs_order",
        ).order_by("layout__dfs_order")

        template_name = "briefcase/calendar_event/search_results.partial.html"
        if fetch_count:
            template_name = "briefcase/calendar_event/search_results_count.partial.html"

        return render(
            request,
            template_name,
            {
                "document_layouts"   : results,
                "document"           : {"id": document_id},
                "query"              : query,
                "content_layout_type": LayoutType.CONTENT.value,
                "heading_layout_type": LayoutType.HEADING.value,
                "root_layout_type"   : LayoutType.ROOT.value,
                "RenderOption"       : DocumentLayout.RenderOption,
            },
        )

    else:
        jurisdictions = defaultdict(
            lambda: {"documents": defaultdict(lambda: {"layouts": []})},
        )

        if query:
            search_query = EnhancedSearchQuery(query, search_type="conformii", config="english")
            applicable_layout_filter &= Q(layout__fragment__search_data__search_vector_content=search_query)
            highlight_document_name_annotation = SearchHeadline(
                F("layout__document__name"),
                search_query,
                start_sel='<mark class="search-match">',
                stop_sel="</mark>",
                config="english",
                highlight_all=True,
            )

            applicable_layouts = ApplicableLegislation.objects.filter(
                applicable_layout_filter,
            ).select_related(
                "layout",
                "layout__document",
                "layout__document__jurisdiction",
                "layout__fragment",
            ).prefetch_related(
                "layout__fragment__search_data",
                "layout__document__search_data",
            ).annotate(
                highlight_document_name_annotation=highlight_document_name_annotation,
            )

            print("getting values...")

            document_results = applicable_layouts.values(
                "layout__document__jurisdiction__name",
                "layout__document__jurisdiction_id",
                "layout__document__id",
                "layout__document__name",
                "highlight_document_name_annotation",
            ).order_by(
                "layout__document__jurisdiction__name",
                "layout__document__jurisdiction_id",
            )

            print("iterating results from applicable legislations", document_results.count())

            for document_result in document_results:
                jurisdiction_id = document_result["layout__document__jurisdiction_id"]
                jurisdiction = document_result["layout__document__jurisdiction__name"]
                document_id = document_result["layout__document__id"]
                document_name = document_result["highlight_document_name_annotation"]

                jurisdictions[jurisdiction]["jurisdiction_name"] = jurisdiction
                jurisdictions[jurisdiction]["jurisdiction_id"] = jurisdiction_id

                jurisdictions[jurisdiction]["documents"][document_id]["document_name"] = (
                    document_name
                )
                jurisdictions[jurisdiction]["documents"][document_id]["document_id"] = (
                    document_id
                )

        else:
            highlight_document_name_annotation = F("document__name")

            tenant_document_filters = Q(
                document__archived=False,
                document__published=True,
            )

            tenant_documents = tenant.tenantdocument_set.filter(
                tenant_document_filters,
            ).select_related(
                "document",
                "document__jurisdiction",
            ).prefetch_related(
                "document__search_data",
            ).annotate(
                highlight_document_name_annotation=highlight_document_name_annotation,
            ).order_by(
                "document__jurisdiction__name",
                "document__jurisdiction_id",
            ).values(
                "document__jurisdiction__name",
                "document__jurisdiction_id",
                "highlight_document_name_annotation",
                "document_id",
            )

            print("iterating results from tenant doc", tenant_documents.count())

            for tenant_doc in tenant_documents:
                jurisdiction_id = tenant_doc["document__jurisdiction_id"]
                jurisdiction = tenant_doc["document__jurisdiction__name"]
                document_id = tenant_doc["document_id"]
                document_name = tenant_doc["highlight_document_name_annotation"]

                jurisdictions[jurisdiction]["jurisdiction_name"] = jurisdiction
                jurisdictions[jurisdiction]["jurisdiction_id"] = jurisdiction_id

                jurisdictions[jurisdiction]["documents"][document_id]["document_name"] = (
                    document_name
                )
                jurisdictions[jurisdiction]["documents"][document_id]["document_id"] = (
                    document_id
                )

        print("Grouping results")

        grouped_results = []
        for jurisdiction, j_data in jurisdictions.items():
            j_data["documents_count"] = len(j_data["documents"])
            j_data["documents"] = [
                {
                    "document_name": d["document_name"],
                    "document_id": d["document_id"],
                }
                for d in j_data["documents"].values()
            ]
            grouped_results.append(j_data)

        print("Rendering...")

        return render(
            request,
            "briefcase/calendar_event/search_results.html",
            {
                "object_list": grouped_results,
                "query": query,
                "content_layout_type": LayoutType.CONTENT.value,
                "heading_layout_type": LayoutType.HEADING.value,
                "root_layout_type": LayoutType.ROOT.value,
                "RenderOption": DocumentLayout.RenderOption,
            },
        )


@permission_required("calendar.delete_calendareventoccurrence")
@login_required
def delete_calendar_event_occurrence(request, occurrence_id):
    occurrence = CalendarEventOccurrence.objects.get(id=occurrence_id)
    document_ids = occurrence.associated_legislation.values_list(
        "layout__document_id",
        flat=True,
    ).distinct()
    documents = Document.objects.filter(id__in=document_ids)

    model_name = occurrence._meta.verbose_name
    action = "Deleted %s" % model_name

    occurrence.save_history(
        tenant=request.user.tenant,
        user=request.user,
        action=action,
        comment="",
        documents=documents,
        data=HistoryData(
            action=StateAction.DELETE.value,
            state=StateHandler.serialize_state(
                occurrence,
                exclude=["code"],
            ),
        ),
    )

    occurrence.delete()
    messages.success(request, "Calendar Event Occurrence Successfully Deleted")
    return HTTPResponseHXRedirect(redirect_to=reverse("briefcase:calendar-event:list"))

@permission_required("calendar.delete_calendareventoccurrence")
@login_required
def delete_calendar_event_series(request, series_id):
    series = CalendarEventSeries.objects.get(id=series_id)

    occurrences = series.calendareventoccurrence_set.all()
    document_ids = occurrences.values_list(
        "associated_legislation__layout__document_id",
        flat=True,
    ).distinct()
    documents = Document.objects.filter(id__in=document_ids)

    model_name = "Calendar Event"
    action = "Deleted %s Series"
    first_occurrence = occurrences.first()
    if first_occurrence:
        model_name = first_occurrence._meta.verbose_name

    action = action % model_name

    series.save_history(
        tenant=request.user.tenant,
        user=request.user,
        action=action,
        comment="",
        documents=documents,
        data=HistoryData(
            action=StateAction.DELETE.value,
            state=StateHandler.serialize_state(
                series,
                exclude=["code"],
            ),
        ),
    )

    series.delete_all_occurrences()
    series.delete()
    messages.success(request, f"{model_name} Successfully Deleted")
    return HTTPResponseHXRedirect(redirect_to=reverse("briefcase:calendar-event:list"))

@permission_required("calendar.delete_calendareventoccurrence")
@login_required
def confirm_delete_occurrence(request, record_id):
    record = CalendarEventOccurrence.objects.get(id=record_id)
    return render(
        request,
        "briefcase/calendar_event/confirm_delete_occurrence_modal.html",
        {"record": record},
    )

@permission_required("calendar.delete_calendareventoccurrence")
@login_required
def confirm_delete_series(request, record_id):
    record = CalendarEventOccurrence.objects.get(id=record_id)
    return render(
        request,
        "briefcase/calendar_event/confirm_delete_series_modal.html",
        {"record": record},
    )
