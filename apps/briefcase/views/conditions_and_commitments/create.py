from dal import autocomplete
from dal import forward
from django import forms
from django.urls import reverse
from django.urls import reverse_lazy
from django.utils.translation import gettext_lazy as _

from apps.document.models import DocumentRevision
from apps.document.models import DocumentType
from apps.document.views import DocumentRevisionCreateView
from apps.users.models import User

from .base import ConditionsAndCommitmentsRevisionMixin


class ConditionsAndCommitmentsRevisionCreateView(DocumentRevisionCreateView, ConditionsAndCommitmentsRevisionMixin):
    model = DocumentRevision
    success_message = _("Conditions and Commitments Draft successfully created")
    permission_required = "document.add_tenant_document"
    form_cancel_url = reverse_lazy("briefcase:conditions-and-commitments:revision:list")
    crumbs = [
        ("Conditions and Commitment Revisions", reverse_lazy("briefcase:conditions-and-commitments:revision:list")),
        ("Create", reverse_lazy("briefcase:conditions-and-commitments:revision:create"),
    )]

    def get_success_url(self):
        return reverse("briefcase:conditions-and-commitments:revision:update", kwargs={ "id": self.object.id })

    def get_form_kwargs(self):
        form_kwargs = super().get_form_kwargs()
        # make query dict mutable
        if "data" in form_kwargs:
            form_kwargs["data"] = form_kwargs["data"].copy()
            form_kwargs["data"]["tenant"] = self.request.user.tenant.id
        return form_kwargs

    def get_form(self, form_class=None):
        form = super().get_form(form_class)
        form.fields["document_type"].queryset = DocumentType.objects.filter(
            tenant_specific=True,
        )
        form.fields["requires_license"].widget = forms.HiddenInput()
        form.fields["assigned_to"] = forms.ModelChoiceField(
            queryset=User.objects.all(),
            required=False,
            widget=autocomplete.ModelSelect2(
                url=reverse_lazy("users:autocomplete"),
                attrs={
                    "data-placeholder": "Select a user",
                    "class": "fs-6 text-align-start",
                    "allowClear": True,
                },
                forward=(
                    forward.Const(["document.change_tenant_document"], "has_permission"),
                ),
            ),
        )

        form.fields["version_name"].required = False
        form.fields["source_url"].required = False
        return form

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context["form_cancel_url"] = self.form_cancel_url
        return context
