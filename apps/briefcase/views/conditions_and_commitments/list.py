import re

import arrow
import django_filters
import django_tables2 as tables
import waffle
from django.contrib.postgres.search import SearchHeadline
from django.db.models import F
from django.db.models import Q
from django.http import HttpResponseRedirect
from django.urls import reverse
from django.urls import reverse_lazy
from django.utils.translation import gettext_lazy as _

from apps.briefcase.forms import ConditionsAndCommitmentsRevisionSearchForm
from apps.briefcase.forms import ConditionsAndCommitmentsSearchForm
from apps.briefcase.models import ApplicableLegislation
from apps.briefcase.views import ApplicableLegislationAssessmentExportView
from apps.briefcase.views import ApplicableLegislationDocumentExportView
from apps.briefcase.views import ApplicableLegislationDocumentLayoutCountView
from apps.briefcase.views import ApplicableLegislationExportView
from apps.briefcase.views import ApplicableLegislationFilter
from apps.briefcase.views import ApplicableLegislationLayoutJurisdictionListView
from apps.briefcase.views import ApplicableLegislationListView
from apps.briefcase.views import ApplicableLegislationSelectionView
from apps.common.forms.filters import OPERATOR_OR
from apps.document.models import Document
from apps.document.models import DocumentRevision
from apps.document.models import DocumentType
from apps.document.models import Tag
from apps.exports.views import ExportMixinViewSet
from apps.search.forms import DocumentSearchForm
from apps.search.postgres.query import EnhancedSearchQuery
from apps.search.views import DocumentSearchView
from apps.search.views import SearchListView
from apps.users.models import User

from .base import ConditionsAndCommitmentsMixin
from .base import ConditionsAndCommitmentsRevisionMixin


class ConditionsAndCommitmentsFilter(ApplicableLegislationFilter):
    title = _("Conditions and Commitments")
    instructions = _("Manage conditions and commitments of your organization")

class ConditionsAndCommitmentsRevisionFilter(django_filters.FilterSet):
    title = _("Conditions and Commitments Drafts")
    instructions = _("Manage conditions and commitments drafts of your organization")

    search_input = django_filters.CharFilter(method="full_text_search_filter", label="Search input")
    search_criteria = django_filters.ChoiceFilter(method="search_criteria_filter", label="Search Criteria",
                                                  choices=DocumentSearchForm.CATEGORY_CHOICES)


    revision_type = django_filters.ChoiceFilter(field_name="revision_type",label="Revision Type", choices=DocumentRevision.RevisionType.choices)
    status = django_filters.ChoiceFilter(field_name="status", label="Status", choices=DocumentRevision.Status.choices)
    document_type = django_filters.ModelMultipleChoiceFilter(
        field_name="document_type",
        to_field_name="id",
        label="Document Type",
        queryset=DocumentType.objects.filter(tenant_specific=True),
    )
    no_assignees = django_filters.BooleanFilter(field_name="assigned_to", lookup_expr="isnull", label="No Assignees", required=False)


    class Meta:
        model = DocumentRevision
        exclude = ["data"]

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        tenant = self.request.user.tenant
        self.filters["documents"] = django_filters.ModelMultipleChoiceFilter(
            field_name="document",
            queryset=Document.objects.tenant_current_set(tenant=tenant),
            to_field_name="id",
            label="Documents",
        )

        self.filters["assignees"] = django_filters.ModelMultipleChoiceFilter(
            field_name="assigned_to",
            to_field_name="id",
            label="Assigned To",
            queryset=User.objects.filter(tenant=tenant),
        )

        tag_types = Tag.get_root_nodes()
        for tag_type in tag_types:
            tag_type_code = f"tag-{tag_type.code}"
            tags = tag_type.get_descendants(include_self=True)

            self.filters[tag_type_code] = django_filters.ModelMultipleChoiceFilter(
                queryset=tags,
                field_name="layout__tags",
                to_field_name="id",
                label=tag_type.name,
            )

    def full_text_search_filter(self, queryset, name, value):
        search_criteria = self.form.cleaned_data.get("search_criteria")
        document_search_filter = Q(
            document__search_data__search_vector_title=EnhancedSearchQuery(
                value,
                search_type="conformii",
                config="english",
            ),
        ) | Q(
            search_data__search_vector_title=EnhancedSearchQuery(
                value,
                search_type="conformii",
                config="english",
            ),
        )
        content_search_filter = Q(
            search_data__search_vector_content=EnhancedSearchQuery(
                value,
                search_type="conformii",
                config="english",
            ),
        )
        text_search_filter = document_search_filter | content_search_filter
        if search_criteria == DocumentSearchForm.CLAUSES_ONLY:
            text_search_filter = content_search_filter
        elif search_criteria == DocumentSearchForm.TITLES_ONLY:
            text_search_filter = document_search_filter

        return queryset.filter(text_search_filter)

    def search_criteria_filter(self, queryset, name, value):
        return queryset

class ConditionsAndCommitmentsSearchFormView(
    ConditionsAndCommitmentsMixin,
    DocumentSearchView,
):
    form_class = ConditionsAndCommitmentsSearchForm
    success_url = reverse_lazy("briefcase:conditions-and-commitments:current:list")

    def get_form_kwargs(self):
        form_kwargs = super().get_form_kwargs()

        form_kwargs["data"]._mutable = True
        form_kwargs["data"].setdefault("operational_areas_operator", OPERATOR_OR)
        form_kwargs["data"].setdefault("programs_operator", OPERATOR_OR)
        form_kwargs["data"].setdefault("activities_operator", OPERATOR_OR)
        form_kwargs["data"].setdefault("assignees_operator", OPERATOR_OR)
        form_kwargs["data"].setdefault("control_documents_operator", OPERATOR_OR)
        return form_kwargs


class ConditionsAndCommitmentsRevisionTable(tables.Table):
    class Meta:
        model = DocumentRevision
        template_name = "django-tables2/bootstrap5.html"
        fields = [
            "draft",
            "name",
            "revision_type",
            "effective_date",
            "version_name",
            "status",
            "assigned_to",
            "source_url",
            "modified",
            "modified_by",
        ]
        attrs = {"class": "table table-striped table-hover text-sm"}
        empty_text = _("No results found.")

    draft = tables.TemplateColumn(
        """<a href='{% url "briefcase:conditions-and-commitments:revision:update" record.id %}'><i class="fa-solid fa-pen-to-square"></i></a>""",
        verbose_name="Draft",
    )
    name = tables.TemplateColumn(
        """{% if record.document and record.document.published %}<a href='{% url "document:current:detail" record.document.id %}'>{{ record.name }}</a> {% else %} {{ record.name }} {% endif %}""",
        verbose_name="Document",
    )

    source_url = tables.TemplateColumn(
        """
        {% if record.source_url %}
        <a class="text-sm" href='{{ record.source_url }}' target="_blank"><i class="fa-solid fa-share-square"></i>
        {{ record.source_url|truncatechars:30 }}
        </a>
        {% else %}
        -
        {% endif %}
        """,
        verbose_name="Source URL",
    )

    def order_draft(self, queryset, is_descending):
        return queryset, True

    def render_status(self, record):
        choices = dict(record.Status.choices)
        if record.status:
            return choices.get(record.status)
        return "-"


class ConditionsAndCommitmentsCurrentListView(ConditionsAndCommitmentsMixin, ApplicableLegislationListView):
    filterset_class = ConditionsAndCommitmentsFilter
    template_name = "briefcase/conditions_and_commitments/list.html"

    def get_queryset(self):
        tenant = self.request.user.tenant
        jurisdiction_qs = tenant.jurisdictions.all()
        jurisdiction_ids = list(jurisdiction_qs.values_list("id", flat=True))
        queryset = (
            ApplicableLegislation.objects.tenant_document_set(tenant=tenant).filter(
                layout__document__jurisdiction_id__in=jurisdiction_ids,
                layout__current=True,
            )
            .select_related(
                "layout",
                "layout__document",
                "layout__document__tenant",
                "layout__document__jurisdiction",
                "layout__fragment",
                "assessment",
            )
            .prefetch_related(
                "assessment__operational_areas",
                "assessment__programs",
                "assessment__activities",
                "assessment__internalcontrol_set",
                "assessment__internalcontrol_set__control_document",
                "layout__tags",
            ).order_by("layout__document__name", "layout__dfs_order")
        )
        return queryset

    def get_export_params(self):
        return "?%s&%s" % ("is_async=1",  self.request.GET.urlencode())

    def get_export_urls(self):
        export_urls = []
        for export_format in ExportMixinViewSet.export_formats:
            export_url = reverse(
                "briefcase:conditions-and-commitments:current:export",
                kwargs={"export_format": export_format},
            ) + self.get_export_params()
            export_urls.append(
                f"""<a hx-get='{export_url}' type='button' class='dropdown-item'>.{export_format}</a>""",
            )
        return export_urls

    def get_export_documents_urls(self):
        export_urls = []
        for export_format in ExportMixinViewSet.export_formats:
            export_url = reverse(
                "briefcase:conditions-and-commitments:current:export-documents",
                kwargs={"export_format": export_format},
            ) + self.get_export_params()
            export_urls.append(
                f"""<a hx-get='{export_url}' type='button' class='dropdown-item'>.{export_format}</a>""",
            )
        return export_urls

    def get_export_assessments_urls(self):
        export_urls = []
        for export_format in ExportMixinViewSet.export_formats:
            export_url = reverse(
                "briefcase:conditions-and-commitments:current:export-assessments",
                kwargs={"export_format": export_format},
            ) + self.get_export_params()
            export_urls.append(
                f"""<a hx-get='{export_url}' type='button' class='dropdown-item'>.{export_format}</a>""",
            )
        return export_urls

    def get_submit_url(self):
        return reverse_lazy("briefcase:conditions-and-commitments:current:list")

    def get_form_modal_url(self):
        return reverse_lazy("briefcase:conditions-and-commitments:current:search")

    def get_session_key(self):
        return "conditions-and-commitments-search"

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context["has_unassessed_applicability_reviews"] = False
        return context

class ConditionsAndCommitmentsJurisdictionListView(ConditionsAndCommitmentsCurrentListView):

    def has_filters(self):
        data = self.request.GET
        active_filters = []
        active_tags = []
        search_input = data.get("search_input")
        for key, value in data.items():
            if key not in ["search_criteria", "archived"]:
                if value:  # Check if the value is non-empty
                    active_filters.append(key)
                if key.startswith("tag-"):
                    active_tags.extend(data.getlist(key))
        print("active filters: ", active_filters, "active_tags: ", active_tags, "search input: ", search_input)
        return search_input or len(active_filters) > 1 or len(active_tags) > 1

    def dispatch(self, request, *args, **kwargs):
        ret = super().dispatch(request, *args, **kwargs)

        # redirect to a search queries based on layouts if there are more than one filter or tags applied for more accurate result
        if self.has_filters() and not waffle.switch_is_active("skip_search_queries_on_layout"):
            return HttpResponseRedirect(
                reverse("briefcase:conditions-and-commitments:current:layout-list") + "?%s" % self.request.GET.urlencode(),
            )
        return ret

class ConditionsAndCommitmentsCurrentLayoutListView(
    ConditionsAndCommitmentsCurrentListView,
    ApplicableLegislationLayoutJurisdictionListView,
):

    def get_template_names(self):
        return "briefcase/conditions_and_commitments/layout_list.html"


class ConditionsAndCommitmentsDocumentListView(ConditionsAndCommitmentsCurrentListView):
    template_name = "briefcase/conditions_and_commitments/document_list.html"

    def clear_session(self):
        # skip clearing sessions
        return

    def get_search_results(self, queryset):
        search_criteria = self.request.GET.get("search_criteria")
        search_input = self.request.GET.get("search_input")
        highlight_document_name_annotation = F("layout__document__name")

        if search_input and search_criteria in [
            ConditionsAndCommitmentsSearchForm.TITLES_AND_CLAUSES,
            ConditionsAndCommitmentsSearchForm.TITLES_ONLY,
        ]:
            search_query = EnhancedSearchQuery(
                search_input,
                search_type="conformii",
                config="english",
            )
            highlight_document_name_annotation = SearchHeadline(
                highlight_document_name_annotation,
                search_query,
                start_sel='<mark class="search-match">',
                stop_sel="</mark>",
                config="english",
                highlight_all=True,
            )

        search_results = queryset.annotate(
            highlighted_document_name=highlight_document_name_annotation,
        ).order_by("layout__document__name").distinct().values(
            "layout__document_id",
            "layout__document__name",
            "highlighted_document_name",
        )

        return search_results

    def get_context_data(self, **kwargs):
        context_data = super().get_context_data(**kwargs)
        return context_data

class ConditionsAndCommitmentsDocumentListCountView(
    ConditionsAndCommitmentsCurrentListView,
    ApplicableLegislationDocumentLayoutCountView,
):

    def clear_session(self):
        # skip clearing sessions
        return

    def get_queryset(self):
        queryset = super().get_queryset()
        return queryset.filter(layout__document_id=self.kwargs["document_id"])


class ConditionsAndCommitmentsSelectionView(ConditionsAndCommitmentsCurrentListView, ApplicableLegislationSelectionView):
    template_name = "briefcase/conditions_and_commitments/bulk_selection.html"

    def clear_session(self):
        # skip clearing sessions
        return

    def get_queryset(self):
        return super().get_queryset().filter(
            layout__document_id=self.request.GET.get("document_id"),
        )


class ConditionsAndCommitmentsRevisionSearchFormView(
    ConditionsAndCommitmentsRevisionMixin,
    DocumentSearchView,
):
    form_class = ConditionsAndCommitmentsRevisionSearchForm
    success_url = reverse_lazy("briefcase:conditions-and-commitments:revision:list")


class ConditionsAndCommitmentsRevisionListView(
    ConditionsAndCommitmentsRevisionMixin,
    tables.SingleTableView,
    SearchListView,
):
    table_class = ConditionsAndCommitmentsRevisionTable
    filterset_class = ConditionsAndCommitmentsRevisionFilter
    template_name = "briefcase/conditions_and_commitments/revision_list.html"
    paginate_by = 25

    def get_session_key(self):
        return "conditions-and-commitments-revision-search"

    def get_submit_url(self):
        return reverse_lazy("briefcase:conditions-and-commitments:revision:list")

    def get_form_modal_url(self):
        return reverse_lazy("briefcase:conditions-and-commitments:revision:search")

    def get_search_results(self, queryset):
        return queryset


class ConditionsAndCommitmentsExportMixin:
    permission_required = "document.export_tenant_document"

    def clear_session(self):
        # skip clearing sessions
        return

    def get_export_filename(self):
        now = arrow.now().datetime
        model = "Conditions-And-Commitments"
        export_filename =  f"{model}-Export-%s" % (
            now.strftime("%Y:%m:%dT%H:%M:%S")
        )

        if "documents" in self.request.GET:
            document_ids = self.request.GET.getlist("documents", [])
            documents = Document.objects.filter(id__in=document_ids)
            if documents.count() == 1:
                export_filename = export_filename.replace(
                    model,
                    documents.first().name,
                )
        # Allow only alphanumeric characters, underscores, and hyphens
        return re.sub(r"[^\w\-_]", " ", export_filename)


class ConditionsAndCommitmentsExportView(
    ConditionsAndCommitmentsCurrentListView,
    ConditionsAndCommitmentsExportMixin,
    ApplicableLegislationExportView,
):
    pass



class ConditionsAndCommitmentsDocumentExportView(
    ConditionsAndCommitmentsCurrentListView,
    ConditionsAndCommitmentsExportMixin,
    ApplicableLegislationDocumentExportView,
):
    def get_export_filename(self):
        filename = super().get_export_filename()
        model = "Conditions-And-Commitments"
        if model in filename:
            return filename.replace(model, model + "-Documents")
        return filename

class ConditionsAndCommimentsAssessmentExportView(
    ConditionsAndCommitmentsCurrentListView,
    ConditionsAndCommitmentsExportMixin,
    ApplicableLegislationAssessmentExportView,
):
    def get_export_filename(self):
        filename = super().get_export_filename()
        model = "Conditions-And-Commitments"
        if model in filename:
            return filename.replace(model, model + "-Assessment")
        return filename
