
import arrow
from django.contrib.contenttypes.models import ContentType
from django.urls import reverse
from django.urls import reverse_lazy

from apps.briefcase.forms import HistorySearchForm
from apps.briefcase.forms.history import BriefcaseHistoryListForm
from apps.briefcase.forms.history import UserHistoryDetailForm
from apps.history.views import HistoryDetailView
from apps.history.views import HistoryExportView
from apps.history.views import HistoryFilter
from apps.history.views import HistoryListView
from apps.history.views import HistorySearchFormView
from apps.history.views import HistoryTable
from apps.history.views import HistoryViewSetMixin
from apps.users.models import User


class UserHistoryViewSetMixin(HistoryViewSetMixin):
    def get_permission_required(self):
        return ("history.view_history", "users.view_user")  # Adjust permissions as needed

    def get_queryset(self):
        queryset = super().get_queryset()
        content_types = ContentType.objects.get_for_models(User)
        content_type_id = list(content_types.values()).pop().id  # Single model
        return queryset.filter(content_type_id=content_type_id)


class UserHistoryFilter(UserHistoryViewSetMixin, HistoryFilter):
    pass


class UserHistoryTable(HistoryTable):
    class Meta(HistoryTable.Meta):
        exclude = ["assessment", "documents", "section", "comment"]  # Adjust fields as necessary

    def get_detailed_url(self, value, record):
        return reverse(
            "briefcase:history:users:detail",
            kwargs={"id": record.id},
        )


class UserHistorySearchFormView(
    UserHistoryViewSetMixin,
    HistorySearchFormView,
):
    form_class = BriefcaseHistoryListForm

    def get_success_url(self) -> str:
        return (
            reverse("briefcase:history:users:list")
            + "?%s" % self.request.GET.urlencode()
        )

    def get_form_kwargs(self):
        form_kwargs = super().get_form_kwargs()
        form_kwargs["form_submit_url"] = reverse(
            "briefcase:history:users:list",
        )
        return form_kwargs


class UserHistoryDetailView(HistoryDetailView):
    def get_template_names(self):
        return "briefcase/history/users/detail.partial.html"

    def get_form(self):
        return UserHistoryDetailForm(
            instance=self.object,
            request=self.request,
        )

class UserHistoryListView(UserHistoryViewSetMixin, HistoryListView):
    table_class = UserHistoryTable
    filterset_class = UserHistoryFilter
    list_view_url = form_cancel_url = htmx_pagination_submit_url = reverse_lazy(
        "briefcase:history:users:list",
    )

    def get_base_export_url(self, export_format):
        return reverse("briefcase:history:users:export", kwargs={"export_format": export_format})

    def get_form(self):
        return HistorySearchForm(
            request=self.request,
            form_submit_url=self.get_form_modal_url(),
            action_choices=self.get_action_choices(),
        )

    def get_form_modal_url(self):
        return reverse("briefcase:history:users:search")

    def get_template_names(self):
        if self.request.htmx:
            template_name = "briefcase/history/list.partial.html"
        else:
            template_name = "briefcase/history/users/list.html"
        return template_name

    def get_session_key(self):
        return "history-user"



class UserHistoryExportView(HistoryExportView, UserHistoryListView):
    def get_export_filename(self):
        now = arrow.now().datetime
        model_name = f"User-{self.get_dataframe_model().__name__}"
        return f"{model_name}-Export-{now.strftime('%Y:%m:%dT%H:%M:%S')}"
