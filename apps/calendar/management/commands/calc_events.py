import logging

import arrow
from django.core.management.base import BaseCommand
from progressbar import progressbar

from apps.calendar.models import CalendarEventOccurrence

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = "Calculate event statuses from Pending -> In Progress -> Overdue regularly"

    def handle(self, *args, **options):
        logger.info("Calculting status of incomplete events...")
        events = CalendarEventOccurrence.objects.exclude(event_status=CalendarEventOccurrence.EventStatuses.COMPLETE)
        update_events = []
        now = arrow.now().date()
        for event in progressbar(events):

            current_status = new_status = event.event_status

            if not event.start_date:
                new_status = event.event_status or event.EventStatuses.PENDING

            if now < event.to_date(event.start_date):
                new_status = event.EventStatuses.PENDING

            if event.end_date:
                if event.to_date(event.start_date) <= now <= event.to_date(event.end_date):
                    new_status = event.EventStatuses.IN_PROGRESS
                elif now > event.to_date(event.end_date):
                    new_status = event.EventStatuses.OVERDUE
            else:
                new_status = event.EventStatuses.IN_PROGRESS

            if current_status != new_status:
                event.event_status = new_status
                update_events.append(event)

        if update_events:
            logger.info(f"Updating status of {len(update_events)} events...")
            CalendarEventOccurrence.objects.bulk_update(update_events, ["event_status"], batch_size=2000)
