# Generated by Django 5.0.3 on 2025-01-15 17:22

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('calendar', '0006_alter_calendareventoccurrence_options_and_more'),
        ('tenants', '0005_alter_tenant_options'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.RemoveField(
            model_name='calendareventoccurrence',
            name='deleted',
        ),
        migrations.RemoveField(
            model_name='calendareventoccurrence',
            name='deleted_by_cascade',
        ),
        migrations.RemoveField(
            model_name='calendareventseries',
            name='deleted',
        ),
        migrations.RemoveField(
            model_name='calendareventseries',
            name='deleted_by_cascade',
        ),
        migrations.RemoveField(
            model_name='occurrenceuser',
            name='deleted',
        ),
        migrations.RemoveField(
            model_name='occurrenceuser',
            name='deleted_by_cascade',
        ),
        migrations.AlterField(
            model_name='calendareventseries',
            name='tenant',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='%(class)s_set', to='tenants.tenant'),
        ),
        migrations.AlterField(
            model_name='occurrenceuser',
            name='calendareventoccurrence',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='calendar.calendareventoccurrence'),
        ),
        migrations.AlterField(
            model_name='occurrenceuser',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL),
        ),
    ]
