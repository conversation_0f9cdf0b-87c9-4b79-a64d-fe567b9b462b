# Generated by Django 5.0.3 on 2025-01-15 17:22

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('comments', '0003_usercomment_comments_us_object__b59f7d_idx'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.RemoveField(
            model_name='usercomment',
            name='deleted',
        ),
        migrations.RemoveField(
            model_name='usercomment',
            name='deleted_by_cascade',
        ),
        migrations.AlterField(
            model_name='usercomment',
            name='user',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL),
        ),
    ]
