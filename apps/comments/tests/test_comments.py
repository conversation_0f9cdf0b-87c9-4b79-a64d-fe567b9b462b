from django.test import TestCase

from apps.exports.views import extract_plain_text_from_comments


class TestExtractPlainTextFromComments(TestCase):

    def test_empty_list(self):
        """Tests with an empty list input."""
        result = extract_plain_text_from_comments([])
        self.assertEqual(result, "")

    def test_single_string_input(self):
        """Tests with a single string input."""
        result = extract_plain_text_from_comments("This is a simple string")
        self.assertEqual(result, "This is a simple string")

    def test_single_json_input(self):
        """Tests with a single JSON input."""
        result = extract_plain_text_from_comments('{"ops":[{"insert":"Test comment\\n"}]}')
        self.assertEqual(result, "Test comment")

    def test_list_of_strings_input(self):
        """Tests with a list of string inputs."""
        result = extract_plain_text_from_comments(["String 1", "String 2"])
        self.assertEqual(result, "String 1, String 2")

    def test_mixed_list_input(self):
        """Tests with a mixed list of JSON inputs, None, and Empty comment, and valid comment."""
        result = extract_plain_text_from_comments(
            [
                '{"ops":[{"insert":{"mention":{"index":"3","denotationChar":"@","id":"12","value":"Testing Terry"}}},{"insert":" \\n"}]}',
                '{"ops":[{"insert":"Test comment\\n"}]}',
                None,
                "",
                "This is a valid comment",
            ],
        )
        self.assertEqual(result, "Testing Terry, Test comment, This is a valid comment")
