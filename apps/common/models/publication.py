from computedfields.models import ComputedField, ComputedFieldsModel
from django.conf import settings
from django.db import models
from django.utils.translation import gettext_lazy as _

def get_title_string(inst):
    return inst.status.title()

class   PublicationMixin(models.Model):

    class Meta:
        abstract = True
        indexes = [models.Index(fields=['published'], name='published')]

    published = models.BooleanField(default=False)
    published_by = models.ForeignKey(
        settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True, related_name="published_%(class)ss")
    published_date = models.DateTimeField(null=True, default=None, verbose_name="Published Date")

class PublicationStatusMixin(PublicationMixin, ComputedFieldsModel):

    class Meta:
        abstract = True
        indexes = [models.Index(fields=['status'], name='status')]
    class Status(models.TextChoices):
        ASSESS = "assess", _("Assess")
        POPULATE = "populate", _("Populate")
        QA = "qa", _("QA")
        REVIEW = "review", _("Review")
        FOR_PUBLISH = "for_publish", _("For_publish")

    class DraftStatus(models.TextChoices):
        ASSESS = "assess", _("Assess")
        POPULATE = "populate", _("Populate")
        QA = "qa", _("QA")
        REVIEW = "review", _("Review")

    class Meta:
        abstract = True

    status = models.CharField(max_length=50, editable=True)

    status_display = ComputedField(
        models.CharField(max_length=32),
        depends=[('self', ['status'])],
        compute=get_title_string
    )

    assigned_to = models.ForeignKey(
        settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True, related_name="assigned_%(class)ss", verbose_name="Assigned To")
