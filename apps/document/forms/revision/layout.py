
import uuid
from collections import OrderedDict

from crispy_forms.bootstrap import StrictButton
from crispy_forms.layout import HTML
from crispy_forms.layout import Column
from crispy_forms.layout import Div
from crispy_forms.layout import Field
from crispy_forms.layout import Layout
from crispy_forms.layout import Row
from django import forms
from django.urls import reverse
from django.urls import reverse_lazy
from django_q.tasks import async_task
from tinymce.widgets import TinyMCE

from apps.briefcase.views.widgets import BulkAddSelect2MultipleWidget
from apps.common.forms import CrispyFormTemplate
from apps.document.models import DocumentLayout
from apps.document.models import LayoutType
from apps.document.models import Tag


class DocumentRevisionLayoutForm(CrispyFormTemplate, forms.ModelForm):
    parent_id = forms.IntegerField(widget=forms.HiddenInput(), required=False, initial=0)
    document_id = forms.IntegerField(widget=forms.HiddenInput(), required=False, initial=0)
    form_id = forms.CharField(widget=forms.HiddenInput())
    dfs_order = forms.IntegerField(widget=forms.HiddenInput(), required=False, initial=1)
    depth = forms.IntegerField(widget=forms.HiddenInput(), required=False, initial=1)
    min_revision_number = forms.IntegerField(widget=forms.HiddenInput(), required=False, initial=1)
    render_option = forms.ChoiceField(choices=DocumentLayout.RenderOption.choices, label="Render Option", required=False)
    order = forms.IntegerField(widget=forms.HiddenInput(), required=False)


    class Meta:
        model = DocumentLayout
        fields = [
            "order",
            "dfs_order",
            "depth",
            "min_revision_number",
            "render_option",
        ]

    def __init__(self, *args, **kwargs):
        self.revision = kwargs.pop("revision")
        self.document = self.revision.document
        super().__init__(*args, **kwargs)
        self.fields["name"] = forms.CharField(widget=TinyMCE(
            mce_attrs={
                "body_id": f"layout-{self.initial['form_id']}-name-body",
            },
            attrs={"id": f"layout-{self.initial['form_id']}-name"}),
            label="Content",
            required=False,
        )

    def clean(self):
        cleaned_data = super().clean()
        if not cleaned_data.get("name"):
            raise forms.ValidationError({"name": "Content cannot be empty."})

    def instance_is_valid(self):
        return hasattr(self, "instance") and self.instance and bool(self.instance.id)

class DocumentRevisionHeadingForm(DocumentRevisionLayoutForm):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields["name"].widget.attrs["placeholder"] = "Heading"
        self.fields["render_option"].choices = DocumentLayout.RenderOptionHeading.choices


    def build_crispy_form(self):
        super().build_crispy_form()

        self.helper.form_class = "row"

        self.helper.layout = Layout(
            Column(
                Field("render_option", wrapper_class="col-3"),
                Field("name", wrapper_class="col-9"),
                css_class="row",
            ),
            Column(
                Field("parent_id"),
                Field("document_id"),
                Field("form_id"),
                Field("order"),
                Field("dfs_order"),
                Field("depth"),
                Field("min_revision_number"),
            ),
        )

        # setup htmx
        form_id = self.initial.get("form_id")
        self.helper.form_id = form_id

    def save(self, *args, **kwargs):
        """
        If the layout does not exist, just insert the layout to the document
        Else, create_or_update the draft_fragment field on the layout
        """
        layout = self.instance
        fragment_name = self.cleaned_data.get("name")
        fragment_kwargs = {
            "name": fragment_name,
            "min_revision_number": self.revision.revision_number,
        }
        layout.layout_type = LayoutType.HEADING.value

        if not layout.id:
            # ensure code is unique for new records
            layout.code = uuid.uuid4().hex

        layout = super().save(*args, **kwargs)

        if layout.id not in layout.path:
            layout.arrange_siblings_ordering()

        draft_fragment = self.document.create_or_update_draft_fragment(layout, fragment_kwargs)
        async_task(self.revision.update_search_data, cluster="system")

        return layout

class DocumentRevisionContentForm(DocumentRevisionLayoutForm):
    suggest_calendar_event = forms.BooleanField(required=False, label="Suggest for a Calendar Event")
    tags = forms.ModelMultipleChoiceField(
        label="Tags",
        required=False,
        queryset=Tag.objects.filter(parent__isnull=False),
        widget=forms.MultipleHiddenInput(),
    )
    class Meta:
        model = DocumentLayout
        fields = [
            "order",
            "depth",
            "dfs_order",
            "min_revision_number",
            "render_option",
            "suggest_calendar_event",
            "tags",
        ]

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        form_id = self.initial.get("form_id")
        self.fields["name"] = forms.CharField(
            required=True,
            label="Section",
            widget=forms.TextInput(attrs={
                "placeholder": "Section Name",
                "id": f"layout-{form_id}-name",
                "autofocus": "autofocus",
            }),
        )
        self.fields["content"] = forms.CharField(
            label="Content",
            widget=TinyMCE(
                attrs={"placeholder": "Content", "id": f"layout-{self.initial['form_id']}-content"},
                mce_attrs={
                    "body_id": f"layout-{self.initial['form_id']}-content-body",
                },
            ),
            required=True,
        )
        self.fields["render_option"].choices = DocumentLayout.RenderOptionContent.choices
        self.fields["tags"] =  forms.ModelMultipleChoiceField(
            label="Tags",
            required=False,
            queryset=Tag.objects.filter(parent__isnull=False),
            widget=forms.MultipleHiddenInput(),
        )

    @property
    def initial_grouped_tags(self):
        grouped_tags_dict = dict()
        tags = Tag.objects.filter(id__in=self.initial.get("tags", []))
        for tag in tags:
            for root_tag_code, root_tag_info in Tag.grouped_tags_dict().items():
                descendants = root_tag_info.get("descendants", [])
                root_tag = root_tag_info.get("root")
                if tag.id in descendants:
                    grouped_tags_dict.setdefault(root_tag_code, {"root": root_tag, "descendants": []})
                    grouped_tags_dict[root_tag.code]["descendants"].append(tag)

        for root_tag_code, root_tag_info in grouped_tags_dict.items():
            root_tag_info["descendants"] = sorted(root_tag_info["descendants"], key=lambda tag: tag.name)
        return OrderedDict(sorted(grouped_tags_dict.items()))

    def build_crispy_form(self):
        super().build_crispy_form()
        self.helper.form_class = "row"

        selected_tags_html = """<div id="selected-tags" class="mt-3">
            {% if object and object.grouped_tags.items and perms.document.view_tag %}
                {% include 'document/revision/selected_tags.html' with grouped_tags=object.grouped_tags %}
            {% elif form.initial_grouped_tags.items and perms.document.view_tag %}
                {% include 'document/revision/selected_tags.html' with grouped_tags=form.initial_grouped_tags %}
            {% else %}
                <p>No tags selected.</p>
            {% endif %}
        </div>
        """
        form_id = self.initial.get("form_id")
        self.helper.form_id = form_id
        tag_anchor = "<p>Tags:</p>"
        can_tag_permission = self.request.user.has_perm("document.tag_document")
        if self.document.is_specific_to_tenant:
            can_tag_permission = self.request.user.has_perm("document.tag_tenant_document")
        if can_tag_permission:
            tag_anchor = """
            <a type="button"
                 class="fw-bold text-decoration-underline"
                 data-bs-dismiss="modal"
                 hx-get="{% url 'document:revision:tags-modal' %}{% if layout.id %}?parent_id={{ layout.id }}{% endif %}"
                 hx-target="#tags-modal-content"
                 hx-trigger="click"
                 hx-swap="innerHTML"
            >
                <i class="fa fa-pen-to-square me-1"></i>Tags:
            </a>
            """

        self.helper.layout = Layout(
            Column(
                Field("name", wrapper_class="col-4"),
                Field("content", wrapper_class="col-8"),
                css_class="row",
            ),
            Column(
                Column(
                    Field("render_option"),
                    Field("suggest_calendar_event"),
                    css_class="col-4",
                ),
                Column(
                    HTML(tag_anchor),
                    HTML(f"""
                    <div id="selected-tags">
                        {selected_tags_html}
                    </div>
                    """),
                    css_class="col-8",
                ),
                css_class="row",
            ),
            Column(
                Field("parent_id"),
                Field("document_id"),
                Field("form_id"),
                Field("order"),
                Field("dfs_order"),
                Field("min_revision_number"),
                Field("depth"),
            ),
        )

    def clean_tags(self):
        cleaned_tags = self.cleaned_data["tags"]
        cleaned_tag_ids = list(map(lambda tag: tag.id, cleaned_tags))
        instance = self.instance
        ancestors = instance.get_ancestors().exclude(max_revision_number__lt=self.revision.revision_number)
        ancestor_tag_ids = list(filter(lambda tag_id: tag_id is not None, ancestors.values_list("tags", flat=True).distinct()))
        removed_ancestor_tag_ids = []
        for tag_id in ancestor_tag_ids:
            if tag_id not in cleaned_tag_ids:
                removed_ancestor_tag_ids.append(tag_id)
        if removed_ancestor_tag_ids:
            # raise forms.ValidationError(f"The following tags are inherited from ancestor nodes and cannot be removed: {', '.join(list(map(lambda tag: f'{tag.name} ({tag.parent.name})', Tag.objects.filter(id__in=removed_ancestor_tag_ids))))}")

            # just add the tags that are removed instead of throwing validation error
            ancestor_tags = Tag.objects.filter(id__in=removed_ancestor_tag_ids)
            cleaned_tags = cleaned_tags.union(ancestor_tags)

        return cleaned_tags

    def save(self, *args, **kwargs):
        # add parent and document

        layout = self.instance
        layout.parent_id = self.cleaned_data["parent_id"]
        layout.document_id = self.cleaned_data["document_id"]
        layout.order = self.cleaned_data["order"]
        layout.layout_type = LayoutType.CONTENT.value
        previous_tag_ids = []
        if layout.id:
            previous_tag_ids = list(layout.tags.values_list("id", flat=True))
        else:
            # ensure code is unique for new records
            layout.code = uuid.uuid4().hex

        layout = super().save(*args, **kwargs)

        if layout.id not in layout.path:
            layout.arrange_siblings_ordering()

        fragment_kwargs = {
            "name": self.cleaned_data.get("name"),
            "content": self.cleaned_data.get("content"),
            "suggest_calendar_event": self.cleaned_data.get("suggest_calendar_event"),
            "min_revision_number": self.revision.revision_number,
        }

        draft_fragment = self.document.create_or_update_draft_fragment(layout, fragment_kwargs)
        async_task(self.revision.update_search_data, cluster="system")
        async_task(self.revision.update_search_tags, cluster="system")

        # ensure the tags for the layout are reflected to descendants
        current_tag_ids = list(layout.tags.values_list("id", flat=True))
        layout.reflect_tags_to_active_descendants(
            previous_tag_ids=previous_tag_ids,
            tag_ids=current_tag_ids,
            revision_number=self.revision.revision_number,
        )
        return layout

    @property
    def selected_tags(self):
        if self.data:
            tag_ids = self.data.getlist("tags")
            return Tag.objects.filter(id__in=tag_ids)
        elif self.instance and self.instance.pk:
            return self.instance.tags.all()
        else:
            return []

class DocumentRevisionRootLayoutForm(CrispyFormTemplate, forms.ModelForm):
    tags = forms.ModelMultipleChoiceField(
        label="Tags",
        required=False,
        queryset=Tag.objects.filter(parent__isnull=False),
        widget=BulkAddSelect2MultipleWidget(
            url=reverse_lazy("general:tag-autocomplete"),
            attrs={
                "data-placeholder": "Select a tag for content",
                "placeholder": "Tags",
                "class": "badge text-bg-secondary",
            },
        ),
    )

    class Meta:
        model = DocumentLayout
        fields = ["tags"]

    def get_form_id(self):
        return "revision-root-node-tags-form"

    def get_results_id(self):
        return f"root-{self.instance.id}"

    def get_form_attributes(self):
        results_id = self.get_results_id()
        return {
            "hx-post": reverse("document:revision:update-root-tags", kwargs={ "id": self.revision.id }),
            "hx-target": f"#{results_id}",
            "hx-swap": "innerHTML",
            "hx-indicator": "previous .spinner-border",
            "data-loading": "block",
        }

    def __init__(self, *args, **kwargs):
        self.revision = kwargs.pop("revision")
        self.request = kwargs.get("request")

        super().__init__(*args, **kwargs)
        if self.data:
            self.fields["tags"].initial = self.data.getlist("tags")
        elif self.instance and self.instance.pk:
            self.fields["tags"].initial = self.instance.document.root_layout.tags.all().values_list("id", flat=True)


class DocumentRevisionBulkTagUpdateForm(CrispyFormTemplate):

    def __init__(self, *args, **kwargs):
        self.revision = kwargs.pop("revision")
        self.session_key = kwargs.pop("session_key")
        self.mode = kwargs.pop("mode")
        super().__init__(*args, **kwargs)

    def get_form_id(self):
        return "layout-tags-bulk-update-form"

    def get_form_layout(self):
        tag_types = Tag.get_root_nodes()
        tag_divs = []

        for tag_type in tag_types:
            tag_type_code = f"tag-{tag_type.code}"
            self.fields[tag_type_code] = forms.ModelMultipleChoiceField(
                label="",
                required=False,
                queryset=tag_type.get_descendants(),
                widget=forms.CheckboxSelectMultiple(
                    attrs={ "id": self.get_form_id() },
                ),
            )
            tag_divs.append(
                Div(
                    HTML(f"""<div class='card-header'><h6><i class="align-middle mx-2 {tag_type.icon}"></i><span class="align-middle">{tag_type.name}</span></h6></div>"""),
                    Field(tag_type_code),
                    css_class="card",
                    style="border: none; padding: 5px;",
                ),
            )

        mode_title = "Add Tags"
        submit_mode_btn_color = "btn-conformii-dark"
        if self.mode == "remove":
            mode_title = "Remove Tags"
            submit_mode_btn_color = "btn-danger bg-danger"

        return Layout(
            Row(
                Column(
                    Row(
                        Column(
                            StrictButton(
                                """<i class="align-middle mx-2 fa-solid fa-arrow-left"></i>Back""",
                                css_class="col-12 my-2 btn btn-md btn-shadow btn-secondary",
                                **{
                                    "hx-trigger": "click",
                                    "hx-get": reverse("document:revision:layout-bulk-tag-select", kwargs={"id": self.revision.id}),
                                    "hx-target": "#side-column",
                                    "hx-vals": "js:{elementId: 'side-column', addClass: 'bulk-tag-column', toggleSideColumn: 1, session_key: " + f"'{self.session_key}'" + "}",
                                    "hx-swap": "innerHTML",
                                },
                            ),
                            StrictButton(
                                f"""<i class="align-middle mx-2 fa-solid fa-tags"></i>{mode_title}""",
                                type="submit",
                                css_class=f"col-12 my-2 mb-4 input-group-text btn-md p-2 shadow-none {submit_mode_btn_color}",
                            ),
                            HTML("<hr>"),
                            css_class="row",
                        ),
                    ),
                    Row(
                        Column(
                            Div(
                                *tag_divs,
                                style="m-auto",
                            ),
                        ),
                    ),
                    css_class="col-12 my-2 border-end",
                ),
            ),
        )

    def get_form_class(self):
        return ""

    def get_form_attributes(self):
        # setup htmx
        return {
            "hx-post": reverse("document:revision:layout-bulk-tag-update", kwargs={ "id": self.revision.id }),
            "hx-target": "#bulk-tag-update-form-parent",
            "hx-swap": "outerHTML",
            "hx-indicator": "previous .spinner-border",
            "data-loading": "block",
            "hx-vals": "js:{mode: '" + self.mode + "', session_key: '" + self.session_key + "'}",
        }

    def clean(self):
        cleaned_data = super().clean()

        assessment_tags = cleaned_data.get("tag-assessment", [])
        asset_type_tags = cleaned_data.get("tag-asset_type", [])
        asset_lifecycle_tags = cleaned_data.get("tag-asset_lifecycle", [])
        other_categories = cleaned_data.get("tag-other_categories", [])
        program_business_area_tags = cleaned_data.get("tag-program_(business_area)", [])

        tags_selected_qs = (
            assessment_tags | asset_type_tags | asset_lifecycle_tags | other_categories | program_business_area_tags
        )

        is_submit = self.request.method == "POST"

        if is_submit and not tags_selected_qs.exists():
            raise forms.ValidationError("At least one tag must be selected.")

        return cleaned_data

