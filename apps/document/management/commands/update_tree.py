from django.apps import apps
from django.core.management.base import BaseCommand, CommandParser
from apps.common.models import TreeNodeMixin
from apps.utils import get_subclasses

class Command(BaseCommand):
    help = "Updates the tree fields by calling with_tree_fields() and saving results to database"

    def add_arguments(self, parser: CommandParser) -> None:
        parser.add_argument("--model", type=str, nargs="?", help="App label and name of the model. (e.g. document.Tag) If not provided, looks at all models inherting from TreeNodeMixin")

    def handle(self, *args, **options):

        subclasses = []
        if options.get("model"):
            try:
                app_label, model_name = options.get("model").split(".")
            except ValueError:
                raise Exception(f"Invalid model name: {options.get('model')} Class name of the model should follow this format (e.g. document.Tag)")
            model = apps.get_model(app_label, model_name)
            subclasses.append(model)
        else:
            subclasses = list(filter(lambda model: not model._meta.abstract, get_subclasses(TreeNodeMixin, subclasses)))

        for subclass in subclasses:

            if hasattr(subclass, "update_tree"):
                print(f"Updating {subclass.__name__} tree fields")
                subclass.update_tree()
                print(f"Updated {subclass.__name__} tree fields")
            else:
                print(f"{subclass.__name__} does not have an update_tree method")
                continue
