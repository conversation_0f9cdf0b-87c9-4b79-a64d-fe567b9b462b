# Generated by Django 5.0.3 on 2024-05-22 19:26

import django.contrib.postgres.search
from django.db import migrations


# Taken from https://testdriven.io/blog/django-search/
def document_compute_search_vector(apps, schema_editor):
    Document = apps.get_model('document', 'Document')
    Document.objects.update(search_vector=django.contrib.postgres.search.SearchVector('name', config='english'))


def fragment_compute_search_vector(apps, schema_editor):
    DocumentFragment = apps.get_model('document', 'DocumentFragment')
    DocumentFragment.objects.update(
        search_vector=django.contrib.postgres.search.SearchVector('name_plain_text', 'content_plain_text',
                                                                  config='english'))


class Migration(migrations.Migration):
    dependencies = [
        ('document', '0002_initial'),
    ]

    operations = [
        migrations.RunSQL(
            sql="""
            DROP TRIGGER IF EXISTS search_vector_trigger
            ON document_document;
            CREATE TRIGGER search_vector_trigger
            BEFORE INSERT OR UPDATE OF name, search_vector
            ON document_document
            FOR EACH ROW EXECUTE PROCEDURE
            tsvector_update_trigger(
                search_vector, 'pg_catalog.english', name
            );
            UPDATE document_document SET search_vector = NULL;
            """,
            reverse_sql="""
            DROP TRIGGER IF EXISTS search_vector_trigger
            ON document_document;
            """,
        ),
        migrations.RunSQL(
            sql="""
            DROP TRIGGER IF EXISTS search_vector_trigger
            ON document_document;
            CREATE TRIGGER search_vector_trigger
            BEFORE INSERT OR UPDATE OF name, search_vector
            ON document_document
            FOR EACH ROW EXECUTE PROCEDURE
            tsvector_update_trigger(
                search_vector, 'pg_catalog.english', name
            );
            UPDATE document_document SET search_vector = NULL;
            """,
            reverse_sql="""
            DROP TRIGGER IF EXISTS search_vector_trigger
            ON document_document;
            """,
        ),
        migrations.RunSQL(
            sql="""
            DROP TRIGGER IF EXISTS search_vector_trigger
            ON document_documentfragment;
            CREATE TRIGGER search_vector_trigger
            BEFORE INSERT OR UPDATE OF name_plain_text, content_plain_text, search_vector
            ON document_documentfragment
            FOR EACH ROW EXECUTE PROCEDURE
            tsvector_update_trigger(
                search_vector, 'pg_catalog.english', name_plain_text, content_plain_text
            );
            UPDATE document_documentfragment SET search_vector = NULL;
            """,
            reverse_sql="""
            DROP TRIGGER IF EXISTS search_vector_trigger
            ON document_documentfragment;
            """,
        ),
        migrations.RunSQL(
            sql="""
            DROP TRIGGER IF EXISTS search_vector_trigger
            ON document_documentfragment;
            CREATE TRIGGER search_vector_trigger
            BEFORE INSERT OR UPDATE OF name_plain_text, content_plain_text, search_vector
            ON document_documentfragment
            FOR EACH ROW EXECUTE PROCEDURE
            tsvector_update_trigger(
                search_vector, 'pg_catalog.english', name_plain_text, content_plain_text
            );
            UPDATE document_documentfragment SET search_vector = NULL;
            """,
            reverse_sql="""
            DROP TRIGGER IF EXISTS search_vector_trigger
            ON document_documentfragment;
            """,
        ),
        migrations.RunPython(
            document_compute_search_vector, reverse_code=migrations.RunPython.noop
        ),
        migrations.RunPython(
            document_compute_search_vector, reverse_code=migrations.RunPython.noop
        ),
        migrations.RunPython(
            fragment_compute_search_vector, reverse_code=migrations.RunPython.noop
        ),
        migrations.RunPython(
            fragment_compute_search_vector, reverse_code=migrations.RunPython.noop
        ),
    ]
