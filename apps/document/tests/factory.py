from factory.django import DjangoModelFactory

from apps.document.models import Document
from apps.document.models import DocumentFragment
from apps.document.models import DocumentLayout
from apps.document.models import DocumentRevision
from apps.document.models import DocumentType
from apps.document.models import JurisdictionTag
from apps.document.models import Tag


class DocumentFactory(DjangoModelFactory):
    class Meta:
        model = Document

class DocumentRevisionFactory(DjangoModelFactory):
    class Meta:
        model = DocumentRevision

class DocumentLayoutFactory(DjangoModelFactory):
    class Meta:
        model = DocumentLayout

class DocumentFragmentFactory(DjangoModelFactory):
    class Meta:
        model = DocumentFragment

class DocumentTypeFactory(DjangoModelFactory):
    class Meta:
        model = DocumentType

class TagFactory(DjangoModelFactory):
    class Meta:
        model = Tag

class JurisdictionTagFactory(DjangoModelFactory):
    class Meta:
        model = JurisdictionTag
