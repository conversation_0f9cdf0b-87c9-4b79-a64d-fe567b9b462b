import arrow
from django.db.models import Exists
from django.db.models import OuterRef

from apps.document.models import Document
from apps.document.models import DocumentLayout
from apps.document.models import DocumentRevision
from apps.document.models import LayoutType

from ...briefcase.models import ApplicabilityReview
from ...briefcase.models import ApplicabilityReviewUser
from ...briefcase.models import ApplicableLegislation
from ...users.tests.factories import UserFactory
from ...utils import raw_text
from . import DocumentFactory
from . import DocumentFragmentFactory
from . import DocumentLayoutFactory
from . import DocumentRevisionFactory
from .test_document_base import TestDocumentBase


class TestDocument(TestDocumentBase):
    def test_document_root_layout(self):
        self.assertEqual(
            self.document.root_layout,
            self.document.layouts.get(parent=None),
            "There should be only one root layout per document.",
        )

        new_document = DocumentFactory(
            tenant=None,
            document_type=self.document_type,
            name="New referenced document name",
            version_name="New referenced document version name",
            code="new_referenced_document",
            effective_date=arrow.now().datetime,
            published=True,
            jurisdiction=self.jurisdiction,
            source_url="https://google.com",
        )

        self.assertIsNotNone(new_document.root_layout, "Accessing the property should create the root layout if it doesnt exist.")

    def test_document_current_and_latest_revision(self):
        new_document_metadata = {
            "name": "Test document New Name",
            "version_name": "Test document version 2",
            "effective_date": arrow.now().datetime,
            "published": False,
            "jurisdiction": self.jurisdiction,
            "source_url": "https://google.com/new_amendment",
            "amendment_name": "Test document amendment name",
            "amendment_url": "https://google.com/new_amendment",
            "amendment_long_name": "Test document amendment long name",
            "requires_license": True,
        }

        new_revision = DocumentRevisionFactory(
            document=self.document,
            revision_number=2,
            status=DocumentRevision.Status.POPULATE.value,
            document_type=self.document_type,
            revision_type=DocumentRevision.RevisionType.AMENDMENT,
            **new_document_metadata,
        )

        self.assertEqual(
            self.document.current_revision,
            self.document.revisions.filter(published=True).latest("revision_number"),
            "The current revision should equal to the latest published revision.",
        )

        self.assertEqual(
            self.document.latest_revision,
            new_revision,
            "The latest revision should equal to the revision with the largest revision number.",
        )

    def test_document_insert_layout(self):
        root_layout = self.document.root_layout

        largest_children_order = 1
        largest_children_dfs_order = root_layout.dfs_order
        if root_layout.children.exists():
            last_children = root_layout.children.latest("order")
            largest_children_order = last_children.order + 1
            last_children_descendant = last_children.get_descendants()
            if last_children_descendant.exists():
                largest_children_dfs_order = last_children_descendant.latest("dfs_order").dfs_order + 1

        fragment_kwargs = {
            "name": "<p>Test Fragment Name</p>",
            "name_plain_text": raw_text("<p>Test Fragment Name</p>"),
            "content": "<p>Test Fragment Content</p>",
            "content_plain_text": raw_text("<p>Test Fragment Content</p>"),
            "min_revision_number": self.document_revision.revision_number,
        }
        layout_kwargs = {
            "parent": root_layout,
            "order": largest_children_order,
            "dfs_order": largest_children_dfs_order,
            "document": self.document,
            "render_option": DocumentLayout.RenderOption.DISPLAY_KEY_INLINE,
            "min_revision_number": self.document_revision.revision_number,
        }

        layout, fragment = self.document.insert_layout(
            fragment_kwargs=fragment_kwargs,
            layout_kwargs=layout_kwargs,
        )

        self.assertEqual(fragment.layout, layout, "The fragment's layout must be set to the created layout.")
        self.assertEqual(fragment.name, fragment_kwargs["name"], "The name field must match with the provided kwargs.")
        self.assertEqual(fragment.name_plain_text, fragment_kwargs["name_plain_text"], "The name_plain_text field must match with the provided kwargs.")
        self.assertEqual(fragment.content, fragment_kwargs["content"], "The content field must match with the provided kwargs.")
        self.assertEqual(fragment.content_plain_text, fragment_kwargs["content_plain_text"], "The content_plain_text field must match with the provided kwargs.")
        self.assertEqual(fragment.min_revision_number, fragment_kwargs["min_revision_number"], "The min_revision_number field must match with the provided kwargs.")

        self.assertEqual(layout.parent, layout_kwargs["parent"], "The parent field must match with the provided kwargs.")
        self.assertEqual(layout.order, layout_kwargs["order"], "The order field must match with the provided kwargs.")
        self.assertEqual(layout.dfs_order, layout_kwargs["dfs_order"], "The dfs_order field must match with the provided kwargs.")
        self.assertEqual(layout.document, layout_kwargs["document"], "The document field must match with the provided kwargs.")
        self.assertEqual(layout.render_option, layout_kwargs["render_option"], "The render_option field must match with the provided kwargs.")
        self.assertEqual(layout.min_revision_number, layout_kwargs["min_revision_number"], "The min_revision_number field must match with the provided kwargs.")

        layout_at_root, layout_at_root_fragment = self.document.insert_layout_at_root(
            fragment_kwargs=fragment_kwargs,
            layout_kwargs=layout_kwargs,
        )
        self.assertEqual(layout_at_root.parent, root_layout, "The parent of the layout inserted at root must be the root layout.")

    def test_publish_document(self):
        self.document_revision.published = False
        self.document_revision.save()
        self.document.published = False
        self.document.save()

        self.document.publish()

        self.document.refresh_from_db()
        self.document_revision.refresh_from_db()

        self.assertTrue(self.document.published, "The document should be published.")
        self.assertTrue(self.document_revision.published, "The document revision should be published.")

    def test_repeal_document(self):

        reason = "Repeal document reason"
        comment = "Repeal document comment"

        applicability_reviews = self.tenant.applicabilityreview_set.filter(layout__document=self.document)

        add_applicable_legislations = []
        add_applicability_reviews = []
        for layout in self.document.layouts.filter(layout_type=LayoutType.CONTENT.value):
            add_applicable_legislations.append(ApplicableLegislation(
                layout=layout,
                tenant=self.tenant,
            ))
            if not applicability_reviews.exists():
                add_applicability_reviews.append(
                    ApplicabilityReview(
                        reason=ApplicabilityReview.Reasons.NEW_DOCUMENT,
                        suggestion=ApplicabilityReview.Suggestions.ADD,
                        review_action=ApplicabilityReview.ReviewActions.NO_ACTION.value,
                        tenant=self.tenant,
                        layout=layout,
                        fragment=layout.fragment,
                        revision=self.document_revision,
                        created=self.document.created,
                        modified=self.document.modified,
                    ),
                )



        if add_applicable_legislations:
            ApplicableLegislation.objects.bulk_create(add_applicable_legislations, batch_size=2000, ignore_conflicts=True)
            ApplicableLegislation.create_assessments(
                self.tenant.applicablelegislation_set.filter(layout__document=self.document),
            )

        # set assignees to assessment
        assignee_user = UserFactory(
            email="<EMAIL>",
            name="Assignee User",
            tenant=self.tenant,
            password="<PASSWORD>",
        )
        for applicable_legislation in self.tenant.applicablelegislation_set.filter(layout__document=self.document):
            applicable_legislation.assessment.applicablelegislationassessmentuser_set.get_or_create(
                user=assignee_user,
            )

        if add_applicability_reviews:
            ApplicabilityReview.objects.bulk_create(add_applicability_reviews, batch_size=2000, ignore_conflicts=True)
            applicability_reviews = applicability_reviews.all()

        applicability_reviews_ids = list(applicability_reviews.values_list("id", flat=True))

        add_reviewers = []
        for applicability_review in applicability_reviews:
                add_reviewers.append(
                    ApplicabilityReviewUser(
                        applicability_review=applicability_review,
                        user=self.user,
                    ),
                )

        if add_reviewers:
            ApplicabilityReviewUser.objects.bulk_create(add_reviewers, batch_size=2000, ignore_conflicts=True)

        self.document.repeal(
            reason=reason,
            comment=comment,
            user=self.user,
        )

        applicability_reviews = self.tenant.applicabilityreview_set.filter(
            id__in=applicability_reviews_ids,
        )

        new_applicability_reviews = self.tenant.applicabilityreview_set.filter(
            layout__document=self.document,
            review_action=ApplicabilityReview.ReviewActions.NO_ACTION.value,
        ).exclude(id__in=applicability_reviews_ids).annotate(
            user_is_default_reviewer=Exists(
                ApplicabilityReviewUser.objects.filter(
                    applicability_review=OuterRef("id"),
                    user=self.user,
                ),
            ),
            assignee_user_is_default_reviewer=Exists(
                ApplicabilityReviewUser.objects.filter(
                    applicability_review=OuterRef("id"),
                    user=assignee_user,
                ),
            ),
        )

        self.assertTrue(self.document.archived, "The document should be set as archived.")
        self.assertEqual(self.document.archived_by, self.user, "The document should be archived by the user provided.")
        self.assertIsNotNone(self.document.archived_date, "The document archived date should be populated.")
        self.assertEqual(self.document.archive_reason, reason, "The document archive reason should be set as the reason provided.")
        self.assertEqual(self.document.archive_comment, comment, "The document archive comment should be set as the comment provided.")

        self.assertEqual(
            applicability_reviews.filter(review_action=ApplicabilityReview.ReviewActions.DISMISSED_BY_SYSTEM.value).count(),
            applicability_reviews.count(),
            "All pending applicability reviews should be dismissed by system after repeal",
        )
        self.assertEqual(
            new_applicability_reviews.filter(
                user_is_default_reviewer=True,
            ).count(),
            new_applicability_reviews.count(),
            "All new applicability reviews to notify the repealed applicable layouts should have previous reviewers as default reviewers.",
        )

        self.assertEqual(
            new_applicability_reviews.filter(
                assignee_user_is_default_reviewer=True,
            ).count(),
            new_applicability_reviews.count(),
            "All new applicability reviews to notify the repealed applicable layouts should have assessment assignees as default reviewers.",
        )

    def test_document_update_metadata_from_revision(self):
        # already covered in test_revision.test_revision_publish
        pass

    def test_document_create_or_update_draft_fragment(self):
        layout = self.document.layouts.filter(layout_type=LayoutType.CONTENT.value).first()
        fragment_kwargs = {
            "name": "<p>Test Fragment Name</p>",
            "name_plain_text": raw_text("<p>Test Fragment Name</p>"),
            "content": "<p>Test Fragment Content</p>",
            "content_plain_text": raw_text("<p>Test Fragment Content</p>"),
            "min_revision_number": self.document_revision.revision_number,
        }
        draft_fragment = self.document.create_or_update_draft_fragment(layout, fragment_kwargs)

        self.assertEqual(
            layout.draft_fragment,
            draft_fragment,
            "The layout draft fragment should be the same as the return value.",
        )
        self.assertEqual(draft_fragment.name, fragment_kwargs["name"], "The draft fragment name should be the same as the name provided in the function arguments.")
        self.assertEqual(draft_fragment.name_plain_text, fragment_kwargs["name_plain_text"], "The draft fragment name_plain_text should be the same as the name_plain_text provided in the function arguments.")
        self.assertEqual(draft_fragment.content, fragment_kwargs["content"],
                         "The draft fragment content should be the same as the content provided in the function arguments.")
        self.assertEqual(draft_fragment.content_plain_text, fragment_kwargs["content_plain_text"],
                         "The draft fragment content_plain_text should be the same as the content_plain_text provided in the function arguments.")
        self.assertEqual(draft_fragment.min_revision_number, self.document_revision.revision_number, "The min revision number should be the same as the revision number provided in the function arguments.")

        fragment_kwargs["content"] = "<p>Updated Test Fragment Content</p>"
        draft_fragment = self.document.create_or_update_draft_fragment(layout, fragment_kwargs)
        self.assertEqual(draft_fragment.content, fragment_kwargs["content"],
                         "The draft fragment content should be the same as the content provided in the function arguments.")

    def test_document_calculate_and_clear_draft_fragments(self):
        new_document_metadata = {
            "name": "Test document New Name",
            "version_name": "Test document version 2",
            "effective_date": arrow.now().datetime,
            "published": False,
            "jurisdiction": self.jurisdiction,
            "source_url": "https://google.com/new_amendment",
            "amendment_name": "Test document amendment name",
            "amendment_url": "https://google.com/new_amendment",
            "amendment_long_name": "Test document amendment long name",
            "requires_license": True,
        }

        new_revision = DocumentRevisionFactory(
            document=self.document,
            revision_number=2,
            status=DocumentRevision.Status.POPULATE.value,
            document_type=self.document_type,
            revision_type=DocumentRevision.RevisionType.AMENDMENT.value,
            **new_document_metadata,
        )

        draft_fragment_kwargs = {
            "name": "<p>Draft Test Fragment Name</p>",
            "content": "<p>Draft Test Fragment Content</p>",
        }

        layouts_should_have_draft_fragment_ids = []
        for idx, layout in enumerate(self.document.layouts.filter(layout_type=LayoutType.CONTENT.value).order_by("id")):

            create_draft = idx % 2 == 0
            if create_draft:
                draft_fragment = DocumentFragmentFactory(
                    layout=layout,
                    min_revision_number=new_revision.revision_number,
                    name=draft_fragment_kwargs["name"],
                    content=draft_fragment_kwargs["content"],
                    name_plain_text=raw_text(draft_fragment_kwargs["name"]),
                    content_plain_text=raw_text(draft_fragment_kwargs["content"]),
                    current=True,
                )
                layouts_should_have_draft_fragment_ids.append(layout.id)

        self.document.calculate_draft_fragments()

        layouts_with_draft_fragments = self.document.layouts.filter(draft_fragment__isnull=False)
        self.assertEqual(
            list(layouts_with_draft_fragments.values_list("id", flat=True).order_by("id")),
            layouts_should_have_draft_fragment_ids,
            "Layouts that created draft fragments should have draft fragment fk field populated.",
        )

        self.document.clear_draft_fragments()
        self.assertFalse(
            self.document.layouts.filter(draft_fragment__isnull=False).exists(),
            "All draft fragment relations should be removed from layouts.",
        )

    def test_document_calculate_current_nodes(self):
        # already covered by test_revision.test_revision_publish
        pass

    def test_document_create_new_document_and_revision(self):

        effective_start_date = arrow.now().datetime
        effective_end_date = arrow.now().shift(years=10).datetime

        version_name = f"Version {arrow.now().date()}"
        amendment_name = f"Amendment {arrow.now().date().year}"

        new_document_kwargs = {
            "name": "Test New Document",
            "created_by": self.user,
            "modified_by": self.user,
            "description": "Test New Document Description",
            "version_name": version_name,
            "amendment_name": amendment_name,
            "effective_date": effective_start_date,
            "jurisdiction": self.jurisdiction,
            "document_type": self.document_type,
        }
        first_revision_kwargs = new_document_kwargs.copy()

        first_revision_kwargs["status"] = DocumentRevision.Status.FOR_PUBLISH
        first_revision_kwargs["revision_type"] = DocumentRevision.RevisionType.NEW_DOCUMENT

        new_document, revision = Document.create_new_document_and_revision(
            new_document_kwargs,
            first_revision_kwargs,
        )

        for field in [
            "name",
            "created_by",
            "modified_by",
            "description",
            "version_name",
            "amendment_name",
            "effective_date",
        ]:
            self.assertEqual(getattr(new_document, field), new_document_kwargs[field], f"The {field} field for the created document must match the provided value in the function arguments.")
            self.assertEqual(getattr(new_document, field), first_revision_kwargs[field], f"The {field} field for the created revision must match the provided value in the function arguments.")

        self.assertFalse(new_document.published, "The new document must not be published.")
        self.assertFalse(revision.published, "The new revision must not be published.")
        self.assertEqual(revision.status, first_revision_kwargs["status"], "The status field for the created revision must match the provided value in function arguments.")
        self.assertEqual(revision.revision_type, first_revision_kwargs["revision_type"], "The revision_type field for the created revision must match the provided value in function arguments.")
        self.assertEqual(revision.document, new_document, "The document field for the created revision must be set to the document created.")

    def test_document_get_revision(self):
        self.assertEqual(
            self.document.get_revision(self.document_revision.revision_number),
            self.document_revision,
            "The return value should return the correction revision based on the provided revision number.",
        )

    def test_document_get_previous_revision(self):
        new_document_metadata = {
            "name": "Test document New Name",
            "version_name": "Test document version 2",
            "effective_date": arrow.now().datetime,
            "published": False,
            "jurisdiction": self.jurisdiction,
            "source_url": "https://google.com/new_amendment",
            "amendment_name": "Test document amendment name",
            "amendment_url": "https://google.com/new_amendment",
            "amendment_long_name": "Test document amendment long name",
            "requires_license": True,
        }

        new_revision = DocumentRevisionFactory(
            document=self.document,
            revision_number=2,
            status=DocumentRevision.Status.POPULATE.value,
            document_type=self.document_type,
            revision_type=DocumentRevision.RevisionType.AMENDMENT,
            **new_document_metadata,
        )

        another_revision = DocumentRevisionFactory(
            document=self.document,
            revision_number=3,
            status=DocumentRevision.Status.POPULATE.value,
            document_type=self.document_type,
            revision_type=DocumentRevision.RevisionType.CORRECTION,
            **new_document_metadata,
        )

        self.assertIsNone(
            self.document.get_previous_revision(1),
            "There should be no previous revision before the first revision",
        )

        self.assertEqual(
            self.document.get_previous_revision(new_revision.revision_number),
            self.document_revision,
            "It should return the correct revision based on the provided revision number.",
        )

        self.assertEqual(
            self.document.get_previous_revision(another_revision.revision_number),
            new_revision,
            "It should return the correct revision based on the provided revision number.",
        )

    def test_document_get_layout_and_fragment_qs_for_revision(self):
        repeal_layout_on_first_revision = self.document.layouts.filter(
            min_revision_number=1,
            layout_type=LayoutType.CONTENT.value,
        ).first()
        repeal_layout_on_first_revision.repeal_for_revision(1)
        repealed_fragments = repeal_layout_on_first_revision.fragments.all()

        modified_layout = self.document.layouts.filter(layout_type=LayoutType.HEADING.value).first()
        previous_fragment = modified_layout.fragment
        modified_fragment = DocumentFragmentFactory(
            layout=modified_layout,
            min_revision_number=2,
            name="<p>Modified Fragment Name</p>",
            content="<p>Modified Fragment Content</p>",
            name_plain_text=raw_text("<p>Modified Fragment Name</p>"),
            content_plain_text=raw_text("<p>Modified Fragment Content</p>"),
        )
        previous_fragment.repeal_for_revision(1)

        add_layout_for_second_revision = DocumentLayoutFactory(
            parent=self.document.root_layout,
            document=self.document,
            min_revision_number=2,
        )
        add_fragment_for_second_revision = DocumentFragmentFactory(
            layout=add_layout_for_second_revision,
            min_revision_number=2,
            name="<p>Added Fragment for Second revision Name</p>",
            content="<p>Added Fragment for Second revision Content</p>",
            name_plain_text=raw_text("<p>Added Fragment for Second revision Name</p>"),
            content_plain_text=raw_text("<p>Added Fragment for Second revision Content</p>"),
        )

        add_layout_for_third_revision = DocumentLayoutFactory(
            parent=self.document.root_layout,
            document=self.document,
            min_revision_number=3,
        )
        add_fragment_for_third_revision = DocumentFragmentFactory(
            layout=add_layout_for_third_revision,
            min_revision_number=3,
            name="<p>Added Fragment for Third revision Name</p>",
            content="<p>Added Fragment for Third revision Content</p>",
            name_plain_text=raw_text("<p>Added Fragment for Third revision Name</p>"),
            content_plain_text=raw_text("<p>Added Fragment for Third revision Content</p>"),
        )

        self.assertFalse(
            self.document.get_layout_qs_for_revision(2).filter(
                id=repeal_layout_on_first_revision.id,
            ).exists(),
            "Repealed layout for the first revision should not exist in the second revision queryset.",
        )
        self.assertTrue(
            self.document.get_layout_qs_for_revision(2).filter(
                id=add_layout_for_second_revision.id,
            ).exists(),
            "Added layout for the second revision should exist in the second revision queryset.",
        )
        self.assertFalse(
            self.document.get_layout_qs_for_revision(2).filter(
                id=add_layout_for_third_revision.id,
            ),
            "Added layout for the third revision should not exist in the second revision queryset.",
        )
        self.assertEqual(
            list(self.document.layouts.filter(min_revision_number__lte=2).exclude(max_revision_number__lt=2).values_list("id", flat=True).order_by("id")),
            list(self.document.get_layout_qs_for_revision(2).values_list("id", flat=True).order_by("id")),
            "The ids between the two expressions should match.",
        )

        self.assertEqual(
            list(
                self.document.layouts.filter(min_revision_number=2).values_list(
                    "id", flat=True).order_by("id")),
            list(self.document.get_added_layout_qs_for_revision(2).values_list("id", flat=True).order_by("id")),
            "The ids between the two expressions should match.",
        )

        self.assertEqual(
            [modified_layout.id],
            list(self.document.get_modified_layout_qs_for_revision(2).values_list("id", flat=True).order_by("id")),
            "The ids between the two expressions should match.",
        )

        self.assertEqual(
            [repeal_layout_on_first_revision.id],
            list(self.document.get_repealed_layout_qs_for_revision(1).values_list("id", flat=True).order_by("id")),
            "The ids between the two expressions should match.",
        )


        self.assertFalse(
            self.document.get_fragment_qs_for_revision(2).filter(
                layout_id=repeal_layout_on_first_revision.id,
            ).exists(),
            "Repealed fragments for first revision should not be in the second revision fragment queryset.",
        )
        self.assertTrue(
            self.document.get_fragment_qs_for_revision(2).filter(
                id=add_fragment_for_second_revision.id,
            ).exists(),
            "Fragments added on the second revision should be in the second revision fragment queryset.",
        )
        self.assertFalse(
            self.document.get_fragment_qs_for_revision(2).filter(
                id=add_fragment_for_third_revision.id,
            ).exists(),
            "Fragments added on the third revision should not be in the second revision fragment queryset.",
        )
        self.assertFalse(
            self.document.get_fragment_qs_for_revision(2).filter(
                id=previous_fragment.id,
            ).exists(),
            "Fragments that were replaced in the second revision should not be in the second revision fragment queryset.",
        )
        self.assertEqual(
            list(
                self.document.fragments.filter(
                    min_revision_number__lte=2,
                    layout__in=self.document.get_layout_qs_for_revision(2),
                ).exclude(
                    max_revision_number__lt=2,
                ).values_list(
                    "id", flat=True,
                ).order_by("id"),
            ),
            list(self.document.get_fragment_qs_for_revision(2).values_list("id", flat=True).order_by("id")),
            "The ids between the two expressions should match.",
        )

        self.assertEqual(
            [add_fragment_for_second_revision.id],
            list(self.document.get_added_fragment_qs_for_revision(2).values_list("id", flat=True).order_by("id")),
            "The ids between the two expressions should match.",
        )

        self.assertEqual(
            [modified_fragment.id],
            list(self.document.get_modified_fragment_qs_for_revision(2).values_list("id", flat=True).order_by("id")),
            "The ids between the two expressions should match.",
        )

        self.assertEqual(
            list(repealed_fragments.values_list("id", flat=True).order_by("id")),
            list(self.document.get_repealed_fragment_qs_for_revision(1).values_list("id", flat=True).order_by("id")),
            "The ids between the two expressions should match.",
        )
