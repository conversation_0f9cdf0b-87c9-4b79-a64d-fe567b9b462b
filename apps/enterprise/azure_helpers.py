import xmltodict


def extract_azure_roles(xml_string: str) -> list[str]:
    """
    Extract all assigned roles from Azure AD's XML role assignment string.

    Args:
        xml_string: The XML string from Azure AD SCIM role assignment

    Returns:
        list[str]: List of role names (e.g., ["Administrator", "Assessment Support"])

    Example:
        roles = extract_azure_roles(scim_payload['roles'][0]['value'])
        print(f"User was assigned these roles: {roles}")
    """
    # First unescape the XML string since it comes escaped in the SCIM payload
    xml_string = xml_string.encode("utf-8").decode("unicode_escape")

    # Parse XML to dict
    data = xmltodict.parse(xml_string)
    roles = []

    # Navigate through the structure to the EntryModification array
    modifications = data["ArrayOfEntryModification"]["EntryModification"]

    # Handle both single and multiple EntryModification cases
    if not isinstance(modifications, list):
        modifications = [modifications]

    for modification in modifications:
        properties = modification.get("Properties", {}).get("d3p1:anyType", [])

        # Ensure properties is a list
        if not isinstance(properties, list):
            properties = [properties]

        # Look for the displayName property with a Value
        for prop in properties:
            # Check if this property has a Name/TargetNameV1 structure
            definition = prop.get("Definition", {})
            name_info = definition.get("Name", {}).get("TargetNameV1", {})

            if name_info.get("Name") == "displayName":
                # Get the Values section
                values = prop.get("Values", {}).get("d3p1:anyType", {})

                # Handle both single value and array cases
                if isinstance(values, list):
                    value = values[0].get("Value")
                else:
                    value = values.get("Value")

                if value:
                    roles.append(value)

    return roles
