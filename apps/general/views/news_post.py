import re

import django_filters as filters
import django_tables2 as tables
from bs4 import BeautifulSoup
from django.contrib import messages
from django.contrib.auth.mixins import LoginRequiredMixin
from django.contrib.contenttypes.models import ContentType
from django.contrib.messages.views import SuccessMessageMixin
from django.contrib.postgres.search import SearchHeadline
from django.core.exceptions import ValidationError
from django.db.models import Exists
from django.db.models import F
from django.db.models import OuterRef
from django.db.models.fields import CharField
from django.db.models.functions import Cast
from django.http import HttpResponseRedirect
from django.urls import reverse
from django.urls import reverse_lazy
from django.utils.translation import gettext_lazy as _
from django.views.generic import CreateView
from django.views.generic import DetailView
from django.views.generic import ListView
from django.views.generic import UpdateView
from django_filters.views import FilterView
from view_breadcrumbs.generic import BaseBreadcrumbMixin
from view_breadcrumbs.generic import CreateBreadcrumbMixin
from view_breadcrumbs.generic import DetailBreadcrumbMixin
from view_breadcrumbs.generic import ListBreadcrumbMixin
from view_breadcrumbs.generic import UpdateBreadcrumbMixin

from apps.common.messages import ErrorMessageMixin
from apps.common.views import CustomPermissionRequiredMixin
from apps.common.views.mixins import CustomWaffleFlagMixin
from apps.exports.views import ExportMixinViewSet
from apps.general.forms import NewsPostCreateForm
from apps.general.forms import NewsPostFeedForm
from apps.general.forms import NewsPostListForm
from apps.general.forms import NewsPostUpdateForm
from apps.general.models import NewsPost
from apps.notifications.models import Notification
from apps.search.postgres.query import EnhancedSearchQuery
from apps.tenants.models import TenantFeatureFlag
from apps.utils.functions import raw_text
from apps.utils.pagination import HTMXPaginationMixin


class NewsPostViewSetMixin(BaseBreadcrumbMixin, LoginRequiredMixin, CustomWaffleFlagMixin, CustomPermissionRequiredMixin):
    model = NewsPost
    permission_required = "general.change_newspost"
    slug_field = "id"
    slug_url_kwarg = "id"
    waffle_flag = TenantFeatureFlag.Features.DASHBOARD.value


class NewsPostCreateView(CreateBreadcrumbMixin, SuccessMessageMixin, ErrorMessageMixin, NewsPostViewSetMixin,
                         CreateView):
    form_class = NewsPostCreateForm
    template_name = "general/news_post/create.html"
    add_home = False
    form_cancel_url = reverse_lazy("general:news_post:list")
    permission_required = "general.add_newspost"
    crumbs = [
        ("News Post Updates", reverse_lazy("general:news_post:list")),
        ("Create", reverse_lazy("general:news_post:create"),
         )]
    success_message = _("News Post successfully created")

    def get_success_url(self):
        return reverse("general:news_post:detail", kwargs={"id": self.object.id})

    def form_valid(self, form):
        """If the form is valid, save the associated model."""
        self.object = form.save(commit=False)
        self.object.created_by = self.request.user
        self.object.modified_by = self.request.user
        self.object.save()
        return HttpResponseRedirect(self.get_success_url())


class NewsPostDetailView(DetailBreadcrumbMixin, NewsPostViewSetMixin, DetailView):
    template_name = "general/news_post/detail.html"
    add_home = False
    list_view_url = form_cancel_url = reverse_lazy("general:news_post:list")

    def detail_view_url(self, instance):
        return reverse(
            "general:news_post:detail",
            kwargs={"id": self.object.id},
        )


class NewsPostUpdateView(UpdateBreadcrumbMixin, SuccessMessageMixin, ErrorMessageMixin, NewsPostViewSetMixin,
                         UpdateView):
    form_class = NewsPostUpdateForm
    template_name = "general/news_post/update.html"
    list_view_url = success_url = form_cancel_url = reverse_lazy("general:news_post:list")
    success_message = _("News Post successfully updated")

    def detail_view_url(self, instance):
        return reverse(
            "general:news_post:detail",
            kwargs={"id": self.object.id},
        )

    def update_view_url(self, instance):
        return reverse(
            "general:news_post:detail",
            kwargs={"id": self.object.id},
        )

    def form_valid(self, form):
        """If the form is valid, save the associated model."""
        self.object = form.save(commit=False)
        self.object.modified_by = self.request.user
        self.object.save()
        success_message = self.get_success_message(form.cleaned_data)
        if success_message:
            messages.success(self.request, success_message)
        return HttpResponseRedirect(self.get_success_url())


class NewsPostTable(tables.Table):
    class Meta:
        model = NewsPost
        template_name = "django_tables2/bootstrap5.html"
        fields = ["title_plain_text", "created_by", "modified_by", "published_date", "published", "jurisdiction"]
        attrs = {"class": "table table-striped table-hover text-sm"}
        empty_text = _("No results found.")

    title_plain_text = tables.TemplateColumn(
        """<a href='{% url "general:news_post:detail" record.id %}'><i class="fa-solid fa-pen-to-square"></i>{{ record.title_plain_text }}</a>""",
        verbose_name="Title")
    created_by = tables.Column(verbose_name="Created By")
    modified_by = tables.Column(verbose_name="Modified By")
    published_date = tables.TemplateColumn("{{ record.published_date | date }}", verbose_name="Published Date")


class NewsPostFilter(filters.FilterSet):
    class Meta:
        model = NewsPost
        fields = ["query"]

    query = filters.CharFilter(method="news_post_filter", label="Search")
    jurisdictions = filters.CharFilter(method="jurisdictions_filter", label="Jurisdictions")
    published_date = filters.CharFilter(method="published_date_filter", label="Publication Date")

    def news_post_filter(self, queryset, name, value):
        return queryset.filter(
            search_vector=EnhancedSearchQuery(
                value,
                search_type="conformii",
                config="english",
            ),
        )

    def jurisdictions_filter(self, queryset, name, value):
        jurisdiction_ids = self.request.GET.getlist("jurisdictions", [])
        if jurisdiction_ids:
            return queryset.filter(jurisdiction_id__in=jurisdiction_ids)

        return queryset

    def published_date_filter(self, queryset, name, value):
        try:
            return queryset.filter(published_date__date__range=value.split(" - "))
        except ValidationError:
            return queryset


class NewsPostListView(ListBreadcrumbMixin, HTMXPaginationMixin, NewsPostViewSetMixin, FilterView,
                       tables.SingleTableView):
    table_class = NewsPostTable
    filterset_class = NewsPostFilter
    queryset = NewsPost.objects.order_by("-created")
    paginate_by = 25
    add_home = False
    crumbs = [("News Post Updates", reverse_lazy("general:news_post:list"))]

    # htmx pagination
    htmx_pagination_submit_url = reverse_lazy("general:news_post:list")
    htmx_pagination_indicator = ".progress"
    htmx_pagination_swap = "innerHTML"
    htmx_pagination_target_element = "#news-post-results"

    def get_template_names(self):
        if self.request.htmx:
            template_name = "common/list.partial.html"
        else:
            template_name = "general/news_post/list.html"

        return template_name

    def get_form(self):
        return NewsPostListForm(request=self.request)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context["title"] = "News Post Updates"
        context["filter"] = self.get_filterset(self.get_filterset_class())
        context["form"] = self.get_form()
        return context


class NewsPostFeedView(ListBreadcrumbMixin, HTMXPaginationMixin, NewsPostViewSetMixin, FilterView, ListView):
    filterset_class = NewsPostFilter
    queryset = NewsPost.objects.filter(published=True)
    permission_required = "general.view_newspost"
    paginate_by = 25
    add_home = False
    crumbs = []

    # htmx pagination
    htmx_pagination_submit_url = reverse_lazy("general:news_post:feed")
    htmx_pagination_indicator = ".progress"
    htmx_pagination_swap = "innerHTML"
    htmx_pagination_target_element = "#news-post-results"

    def get_queryset(self):
        queryset = super().get_queryset()
        query = self.request.GET.get("query", "")
        post_filter = self.request.GET.get("post_filter")
        user = self.request.user
        content_type = ContentType.objects.get_for_model(NewsPost)
        annotations = {
            "pk_as_str": Cast("pk", output_field=CharField()),
            "unread": Exists(
                Notification.objects.filter(
                    unread=True,
                    recipient=user,
                    actor_content_type=content_type,
                    actor_object_id=OuterRef("pk_as_str"),
                ),
            ),
            "pinned": Exists(
                Notification.objects.filter(
                    pinned=True,
                    recipient=user,
                    actor_content_type=content_type,
                    actor_object_id=OuterRef("pk_as_str"),
                ),
            ),
        }
        if query:
            search_query = EnhancedSearchQuery(
                query,
                search_type="conformii",
                config="english",
            )
            annotations.update(
                highlighted_title_annotation=SearchHeadline(
                    F("title"),
                    search_query,
                    start_sel='<mark class="search-match">',
                    stop_sel="</mark>",
                    config="english",
                    highlight_all=True,
                ),
                highlighted_content_annotation=SearchHeadline(
                    F("content"),
                    search_query,
                    start_sel='<mark class="search-match">',
                    stop_sel="</mark>",
                    config="english",
                    highlight_all=True,
                ),
            )

        queryset = queryset.annotate(**annotations)

        tenant = self.request.user.tenant
        tenant_jurisdictions = tenant.jurisdictions.all()
        queryset = queryset.filter(jurisdiction__in=tenant_jurisdictions)

        if post_filter == NewsPostFeedForm.UNREAD:
            queryset = queryset.filter(unread=True)
        elif post_filter == NewsPostFeedForm.PINNED:
            queryset = queryset.filter(pinned=True)

        ordering = self.request.GET.get("sort", NewsPostFeedForm.NEWEST)
        if ordering == NewsPostFeedForm.NEWEST:
            ordering = "-published_date"
        else:
            ordering = "published_date"
        ordering = ("-pinned", ordering)

        return queryset.order_by(*ordering)

    def get(self, request, *args, **kwargs):
        request.GET = request.GET.copy()
        request.GET.setdefault("post_filter", NewsPostFeedForm.UNREAD)
        response = super().get(request, *args, **kwargs)
        for_export = list(filter(lambda key: "export_" in key, self.request.GET.keys()))
        # Redirect to export view
        if for_export:
            export_format = for_export[0].replace("export_", "")
            response.headers["HX-Redirect"] = reverse("general:news_post:feed-export", kwargs={
                "export_format": export_format}) + "?%s" % self.request.GET.urlencode()
        return response

    def get_template_names(self):
        if self.request.htmx:
            template_name = "general/news_post/feed.partial.html"
        else:
            template_name = "general/news_post/feed.html"
        return template_name

    def get_export_urls(self):
        export_urls = []
        for export_format in ExportMixinViewSet.export_formats:
            export_urls.append(
                f"""<input class="btn btn-sm btn-link w-100 text-align-start" id="export_{export_format}" type="submit" form="news-post-form" name="export_{export_format}" value=".{export_format}">""",
            )
        return export_urls

    def get_form(self):
        data = self.get_url_query_params()
        data.setdefault("sort", NewsPostFeedForm.NEWEST)
        return NewsPostFeedForm(request=self.request, data=data)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        form = self.get_form()
        context["title"] = "News Feed"
        context["form"] = form
        context["export_urls"] = self.get_export_urls()
        context["post_filter"] = form.data.get("post_filter", form.UNREAD)
        context["query"] = self.request.GET.urlencode()
        return context

class NewsPostFeedCountView(NewsPostFeedView):
    def get_template_names(self):
        return "general/news_post/feed-count.html"

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context["show_count"] = True
        return context

class NewsPostFeedFilterCountView(NewsPostFeedView):
    def get(self, request, *args, **kwargs):
        request.GET = request.GET.copy()
        request.GET["post_filter"] = NewsPostFeedForm.SHOW_ALL
        response = super().get(request, *args, **kwargs)
        return response

    def get_template_names(self):
        return "general/news_post/feed-filter-count.html"

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context["pinned_count"] = self.filterset.qs.filter(
            pinned=True,
        ).count()
        context["unread_count"] = self.filterset.qs.filter(
            unread=True,
        ).count()
        context["all_count"] = self.filterset.qs.count()
        return context


class NewsPostFeedExportView(ExportMixinViewSet, NewsPostFeedView):
    permission_required = "general.export_newspost"

    def get_dataframe_queryset(self):
        # return the object_list if filters are applied via FilterView + ListView
        # since self.get_queryset() is not filtered yet
        return self.object_list

    def get_export_columns(self):
        return [
            {
                "column_name": "df_title",
                "annotation": F("title"),
                "label": "Title",
                "apply_func": lambda row: prettify_news_post_str_for_export(row),
            },
            {
                "column_name": "df_content_plain_text",
                "annotation": F("content"),
                "label": "Content",
                "apply_func": lambda row: prettify_news_post_str_for_export(row),
            },
            {
                "column_name": "df_jurisdiction",
                "annotation": F("jurisdiction__name"),
                "label": "Jurisdiction",
            },
            {
                "column_name": "df_published_date",
                "annotation": F("published_date"),
                "label": "Publication Date",
                "datetime_format": "%Y-%m-%d",
            },
            {
                "column_name": "df_news_url",
                "annotation": F("news_url"),
                "label": "Link to Source",
            },
        ]


def prettify_news_post_str_for_export(news_post_str: str):
    """ Replaces html tags with spaces and fixes periods for news post export. """
    prettified = re.sub(r"</.*?>", " ", news_post_str)
    prettified = re.sub(r"<.*?>", " ", news_post_str)
    prettified = BeautifulSoup(prettified).text.strip()
    prettified = re.sub(r"@\s+", " ", prettified)
    prettified = prettified.replace(" . ", ". ")
    prettified = prettified.replace(" .", ". ")
    return raw_text(prettified)
