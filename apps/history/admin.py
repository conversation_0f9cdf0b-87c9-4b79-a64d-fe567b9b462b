from dalf.admin import DALFModelAdmin
from dalf.admin import DALFRelatedFieldAjax
from django.contrib import admin
from import_export.admin import ImportExportModelAdmin
from rangefilter.filters import DateTimeRangeFilterBuilder

from apps.common.admin import ConformiiAdmin
from apps.history.models import History


@admin.register(History)
class HistoryAdmin(ConformiiAdmin, ImportExportModelAdmin, DALFModelAdmin):
    list_display = ["timestamp", "user", "tenant", "action", "object_str_repr", "document_names", "comment",
                    "timestamp", "user",
                    "content_type"]
    list_filter = [
        ("tenant", DALFRelatedFieldAjax),
        ("user", DALFRelatedFieldAjax),
        ("timestamp",DateTimeRangeFilterBuilder()),
        "action",
    ]
    raw_id_fields = ["applicable_legislations", "documents"]
    filter_horizontal = ["documents" ]

    def document_names(self, instance):
        return ", ".join([doc.name for doc in instance.documents.all()])
