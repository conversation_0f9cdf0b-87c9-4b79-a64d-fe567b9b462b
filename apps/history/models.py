import arrow
from django.conf import settings
from django.contrib.contenttypes.models import ContentType
from django.contrib.postgres.fields import ArrayField
from django.db import models
from django.shortcuts import reverse
from django.utils.safestring import mark_safe
from django.utils.translation import gettext_lazy as _
from model_utils.models import TimeStampedModel
from polymorphic.models import PolymorphicModel
from progressbar import progressbar
from safedelete.models import SafeDeleteModel
from schema_field.fields import J<PERSON>NSchemedField

from apps.common.models import AuditMixin
from apps.common.models import CodeMixin
from apps.history.schemas.data import HistoryData
from apps.tenants.models import TenantMixin


class History(CodeMixin, TenantMixin, TimeStampedModel, PolymorphicModel):
    class Meta:
        ordering = ["-created"]
        verbose_name_plural = _("History")
        permissions = [
            ("export_history", "Can Export History"),
        ]

    content_type = models.ForeignKey(ContentType, on_delete=models.CASCADE)
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL, on_delete=models.CASCADE, null=True,
    )
    action = models.CharField(max_length=100)
    comment = models.TextField(blank=True, null=True)
    documents = models.ManyToManyField("document.Document")
    object_id = models.CharField(max_length=100, blank=True, null=True)
    object_ids = ArrayField(
        models.CharField(max_length=100),
        default=list,
        blank=True,
    )
    object_str_repr = models.TextField(blank=True, null=True)
    timestamp = models.DateTimeField(null=True)
    applicable_legislations = models.ManyToManyField("briefcase.ApplicableLegislation", blank=True)
    data = JSONSchemedField(schema=HistoryData, default=HistoryData)


    def render_section(self):
        if self.applicable_legislations.exists():
            link_html = ""
            link_limit = 3
            applicable_legislation_count = self.applicable_legislations.count()
            for al in self.applicable_legislations.all()[:link_limit]:
                layout = al.layout
                anchor_link = (
                    reverse("document:current:detail", kwargs={"id": layout.document.id})
                    + f"#layout-{layout.id}"
                )
                section_name = layout.fragment.name_plain_text.strip() or "View"
                link_html += f"<a href='{anchor_link}' target='_blank'>{section_name}</a>, "

            if applicable_legislation_count > link_limit:
                link_html += "..."
            return mark_safe(link_html)
        return "-"

    def render_assessment_link(self):
        if self.applicable_legislations.exists():
            link_html = ""
            link_limit = 3
            applicable_legislation_count = self.applicable_legislations.count()
            for al in self.applicable_legislations.all()[:link_limit]:
                layout = al.layout
                anchor_link = reverse(
                    "briefcase:assessment_form_with_layout_id",
                    kwargs={
                        "layout_id": layout.id,
                    },
                )
                section_name = layout.fragment.name_plain_text.strip() or "View"
                link_html += (
                    f"<a href='{anchor_link}' target='_blank'>{section_name}</a>, "
                )

            if applicable_legislation_count > link_limit:
                link_html += "..."
            return mark_safe(link_html)
        return "-"

    @property
    def entity(self):
        if self.object_id:
            model_cls = self.content_type.model_class()
            if issubclass(model_cls, SafeDeleteModel) and hasattr(model_cls, "all_objects"):
                return model_cls.all_objects.filter(id=self.object_id).first()
            return model_cls.objects.filter(id=self.object_id).first()
        return None

    @property
    def entities(self):
        if self.object_ids:
            model_cls = self.content_type.model_class()
            if issubclass(model_cls, SafeDeleteModel) and hasattr(model_cls, "all_objects"):
                return model_cls.all_objects.filter(id__in=self.object_ids)
            return model_cls.objects.filter(id__in=self.object_ids)
        return []

    @property
    def history_object(self):
        return self.entity or self.entities

    @classmethod
    def save_history(
        cls,
        tenant,
        instance,
        user,
        action,
        comment,
        documents,
        applicable_legislations,
        data,
        object_str_repr=None,
        timestamp=None,
    ):
        """
        Creates a history object based on the instance
        """

        if not object_str_repr:
            object_str_repr = str(instance)

        if not timestamp:
            timestamp = arrow.now().datetime

        history = cls.objects.create(
            tenant=tenant,
            content_type=ContentType.objects.get_for_model(instance),
            user=user,
            object_id=str(instance.pk),
            object_str_repr=object_str_repr,
            action=action,
            comment=comment,
            data=data,
            timestamp=timestamp,
        )
        history.documents.set(documents)
        history.applicable_legislations.set(applicable_legislations)
        return history

    @classmethod
    def save_history_bulk(
        cls,
        tenant,
        content_type,
        object_ids,
        user,
        action,
        comment,
        documents,
        applicable_legislations,
        data,
        object_str_repr="",
        timestamp=None,
    ):
        """
        Creates a history object based on the instances
        """

        if not object_str_repr:
            object_str_repr = f"{len(object_ids)} records"

        if not timestamp:
            timestamp = arrow.now().datetime

        history = cls.objects.create(
            tenant=tenant,
            content_type=content_type,
            user=user,
            object_ids=object_ids,
            object_str_repr=object_str_repr,
            action=action,
            comment=comment,
            data=data,
            timestamp=timestamp,
        )
        history.documents.set(documents)
        history.applicable_legislations.set(applicable_legislations)
        return history

    @classmethod
    def assign_applicable_legislations(cls, queryset=None):
        """
        Assigns applicable legislations to the history queryset
        """
        applicable_legislation_link_table = cls.applicable_legislations.through
        applicable_legislation_list = []
        if queryset is None:
            queryset = cls.objects.all()

        for history in progressbar(queryset):
            applicable_legislation_ids = []
            if history.object_ids:
                model_cls = history.content_type.model_class()
                applicable_legislation_ids = model_cls.get_applicable_legislation_ids(history.object_ids)
            elif history.entity:

                # models such as calendar events can have multiple applicable legislations per object
                applicable_legislation_id = history.entity.get_applicable_legislation_id()

                if isinstance(applicable_legislation_id, list):
                    applicable_legislation_ids += applicable_legislation_id
                elif applicable_legislation_id:
                    applicable_legislation_ids.append(applicable_legislation_id)

            for al_id in list(filter(lambda id: id is not None, applicable_legislation_ids)):
                applicable_legislation_list.append(
                    applicable_legislation_link_table(applicablelegislation_id=al_id, history_id=history.id),
                )

        if applicable_legislation_list:
            print(f"Found {len(applicable_legislation_list)} applicable legislation links...")
            applicable_legislation_link_table.objects.bulk_create(applicable_legislation_list, batch_size=2000, ignore_conflicts=True)


class HistoryMixin(AuditMixin):
    """
    Mixin class to track the audit history of a table

    created, modified, and deleted timestamps are inherited from TimeStampedModel
    """

    class Meta:
        ordering = ["-created"]
        abstract = True

    history_class = History

    def save_history(
        self,
        tenant,
        user,
        action,
        comment,
        documents,
        data=None,
        object_str_repr=None,
        timestamp=None,
    ):
        if not data:
            data = HistoryData()

        if not timestamp:
            timestamp = arrow.now().datetime

        # Models such as calendar events can have multiple applicable legislations per object
        applicable_legislation_ids = []
        applicable_legislation_id = self.get_applicable_legislation_id()
        if isinstance(applicable_legislation_id, list):
            applicable_legislation_ids += applicable_legislation_id
        elif applicable_legislation_id:
            applicable_legislation_ids.append(applicable_legislation_id)

        HistoryCls = self.history_class
        return HistoryCls.save_history(
            tenant=tenant,
            instance=self,
            user=user,
            action=action,
            comment=comment,
            documents=documents,
            applicable_legislations=list(
                filter(lambda id: id is not None, applicable_legislation_ids),
            ),
            data=data,
            object_str_repr=object_str_repr,
            timestamp=timestamp,
        )

    @property
    def history_events(self):
        HistoryCls = self.history_class
        return HistoryCls.objects.filter(
            models.Q(
                content_type=ContentType.objects.get_for_model(self.__class__),
            ) & models.Q(
                models.Q(object_id=self.id) | models.Q(object_ids__contains=[self.id]),
            ),
        )

    @classmethod
    def save_history_bulk(
        cls,
        tenant,
        object_ids,
        user,
        action,
        comment,
        documents,
        object_str_repr="",
        data=None,
        timestamp=None,
    ):
        if not data:
            data = HistoryData()

        if not timestamp:
            timestamp = arrow.now().datetime

        applicable_legislation_ids = cls.get_applicable_legislation_ids(object_ids)
        content_type = ContentType.objects.get_for_model(cls)

        HistoryCls = cls.history_class
        return HistoryCls.save_history_bulk(
            tenant=tenant,
            content_type=content_type,
            object_ids=object_ids,
            user=user,
            action=action,
            comment=comment,
            documents=documents,
            applicable_legislations=list(
                filter(lambda id: id is not None, applicable_legislation_ids),
            ),
            object_str_repr=object_str_repr,
            data=data,
            timestamp=timestamp,
        )

    def revert_delete(self):
        self.deleted = None
        self.deleted_by_cascade = False
        self.save(update_fields=["deleted", "deleted_by_cascade"])

    def get_applicable_legislation_id(self):
        raise NotImplementedError(
            f"SubClass - {self} must implement get_applicable_legislation_id()",
        )

    @classmethod
    def get_applicable_legislation_ids(cls, object_ids):
        raise NotImplementedError(
            f"SubClass - {cls} must implement get_applicable_legislation_ids()",
        )
