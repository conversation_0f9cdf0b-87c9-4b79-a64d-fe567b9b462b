class SharePointListIterator:
    def __init__(self, list_connection, batch_size=5000):
        self.target_list = list_connection
        self.batch_size = batch_size
        self.current_index = 0

    def __iter__(self):
        return self

    def __next__(self):
        items = self.target_list.items.get().skip(self.current_index).top(self.batch_size).execute_query()
        print(len(items))

        if not len(items):
            raise StopIteration

        self.current_index += len(items)
        return [item.properties for item in items]
