from django.contrib.auth.decorators import login_required
from django.contrib.contenttypes.models import ContentType
from django.contrib.postgres.search import SearchHeadline
from django.db.models import Exists
from django.db.models import F
from django.db.models import OuterRef
from django.db.models.fields import <PERSON><PERSON><PERSON><PERSON>
from django.db.models.functions import Cast
from django.shortcuts import get_object_or_404
from django.shortcuts import render
from django.urls import reverse
from django.views.decorators.http import require_GET
from django.views.decorators.http import require_POST
from django_htmx.http import trigger_client_event

from apps.general.forms import NewsPostFeedForm
from apps.general.models import NewsPost
from apps.general.views import NewsPostFeedView
from apps.notifications.models import Notification
from apps.search.postgres.query import EnhancedSearchQuery


class ToggleNotificationView(NewsPostFeedView):

    def get(self, request, *args, **kwargs):
        ret = super().get(request, *args, **kwargs)
        action = "Unread" if self.unread else "Read"
        return trigger_client_event(
            ret,
            name="showToastMessage",
            params={"message": f"Marked {len(self.posts_ids)} Post(s) as {action}"},
        )

    def get_template_names(self):
        return "general/news_post/feed.partial.html"

    def get_context_data(self, **kwargs):
        user = self.request.user
        self.unread = bool(int(self.request.GET.get("unread", False)))
        self.all = bool(int(self.request.GET.get("all", False)))
        content_type = ContentType.objects.get_for_model(NewsPost)
        context = super().get_context_data(**kwargs)

        # update unread status of selected post results
        posts = context["page_obj"].object_list
        if self.all:
            posts = self.object_list
        self.posts_ids = list(map(lambda p: p.id, posts))
        user.notifications.filter(
            actor_content_type=content_type,
            actor_object_id__in=self.posts_ids,
        ).update(unread=self.unread)

        # prepare results_url
        query_dict = self.request.GET.copy()
        query_dict._mutable = True

        # revert back to original post filter after unread status has been handled
        post_filter = self.request.GET.get("post_filter", NewsPostFeedForm.UNREAD)

        query_dict.pop("unread", None)
        query_dict.pop("all", None)

        if post_filter == NewsPostFeedForm.UNREAD and not self.unread:
            if not context["page_obj"].has_next() and context["page_obj"].has_previous():
                query_dict["page"] = context["page_obj"].previous_page_number()
            else:
                query_dict.pop("page", None)

        context["results_url"] = reverse("general:news_post:feed") + "?%s" % query_dict.urlencode()
        return context


@login_required
@require_POST
def toggle_mark_as_read(request, post_id):
    user = request.user
    query = request.GET.get("query", "")
    post_filter = request.GET.get("post_filter")
    search_query = EnhancedSearchQuery(query, search_type="conformii", config="english")
    content_type = ContentType.objects.get_for_model(NewsPost)
    post = get_object_or_404(NewsPost, id=post_id)

    notification, created = Notification.objects.get_or_create(
        recipient=user,
        actor_content_type=content_type,
        actor_object_id=post.id,
        defaults={
            "verb": NewsPost.ActionVerbs.NEW,
        },
    )
    action = "Read"
    if notification.unread:
        notification.mark_as_read()
    else:
        notification.mark_as_unread()
        action = "Unread"

    post = NewsPost.objects.filter(id=post_id).annotate(
        highlighted_title_annotation=SearchHeadline(
            F("title"),
            search_query,
            start_sel='<mark class="search-match">',
            stop_sel="</mark>",
            config="english",
            highlight_all=True,
        ),
        highlighted_content_annotation=SearchHeadline(
            F("content"),
            search_query,
            start_sel='<mark class="search-match">',
            stop_sel="</mark>",
            config="english",
            highlight_all=True,
        ),
        pk_as_str=Cast("pk", output_field=CharField()),
        unread=Exists(
            Notification.objects.filter(
                unread=True,
                recipient=user,
                actor_content_type=content_type,
                actor_object_id=OuterRef("pk_as_str"),
            ),
        ),
    ).first()

    if post_filter != NewsPostFeedForm.UNREAD:
        response = render(
            request,
            "general/news_post/post.html",
            context={"post": post, "query": request.GET.urlencode(), "notification": notification, "update_feed_filter_count": True},
        )
    else:
        response = render(
            request,
            "general/news_post/feed-count.html",
            context={"fetch_count_url": reverse("general:news_post:feed-count") + "?%s" % request.GET.urlencode()},
        )
    return trigger_client_event(
        response,
        name="showToastMessage",
        params={"message": f"Marked Post {action}"},
    )

@login_required
@require_POST
def toggle_pinned(request, post_id):
    user = request.user
    query = request.GET.get("query", "")
    post_filter = request.GET.get("post_filter")
    search_query = EnhancedSearchQuery(query, search_type="conformii", config="english")
    content_type = ContentType.objects.get_for_model(NewsPost)
    post = get_object_or_404(NewsPost, id=post_id)

    notification, created = Notification.objects.get_or_create(
        recipient=user,
        actor_content_type=content_type,
        actor_object_id=post.id,
        defaults={
            "verb": NewsPost.ActionVerbs.NEW,
        },
    )
    action = "Unpinned"
    if notification.pinned:
        notification.unpin()
    else:
        notification.pin()
        action = "Pinned"

    post = NewsPost.objects.filter(id=post_id).annotate(
        highlighted_title_annotation=SearchHeadline(
            F("title"),
            search_query,
            start_sel='<mark class="search-match">',
            stop_sel="</mark>",
            config="english",
            highlight_all=True,
        ),
        highlighted_content_annotation=SearchHeadline(
            F("content"),
            search_query,
            start_sel='<mark class="search-match">',
            stop_sel="</mark>",
            config="english",
            highlight_all=True,
        ),
        pk_as_str=Cast("pk", output_field=CharField()),
        unread=Exists(
            Notification.objects.filter(
                unread=True,
                recipient=user,
                actor_content_type=content_type,
                actor_object_id=OuterRef("pk_as_str"),
            ),
        ),
        pinned=Exists(
            Notification.objects.filter(
                pinned=True,
                recipient=user,
                actor_content_type=content_type,
                actor_object_id=OuterRef("pk_as_str"),
            ),
        ),
    ).first()

    if post_filter != NewsPostFeedForm.PINNED:
        response = render(
            request,
            "general/news_post/post.html",
            context={"post": post, "query": request.GET.urlencode(), "notification": notification, "update_feed_filter_count": True},
        )
    else:
        response = render(
            request,
            "general/news_post/feed-count.html",
            context={"fetch_count_url": reverse("general:news_post:feed-count") + "?%s" % request.GET.urlencode()},
        )

    return trigger_client_event(
        response,
        name="showToastMessage",
        params={"message": f"{action} Post"},
    )


@login_required
@require_GET
def post_notification_detail(request, post_id):
    user = request.user
    post = get_object_or_404(NewsPost, id=post_id)
    content_type = ContentType.objects.get_for_model(NewsPost)

    notification, created = Notification.objects.get_or_create(
        recipient=user,
        actor_content_type=content_type,
        actor_object_id=post.id,
        defaults={
            "verb": NewsPost.ActionVerbs.NEW,
        },
    )
    return render(
        request,
        "general/news_post/notification.html",
        context={"post": post, "query": request.GET.urlencode(), "notification": notification},
    )

@login_required
@require_GET
def post_comments(request, post_id):
    post = get_object_or_404(NewsPost, id=post_id)

    comments = post.user_comments.all()
    return render(
        request,
        "general/news_post/comment_list.html",
        context={"comments": comments},
    )
