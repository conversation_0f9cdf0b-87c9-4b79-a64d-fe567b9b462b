from datetime import datetime
from io import StringIO

import dash_ag_grid as dag
import dash_bootstrap_components as dbc
from dash import Input
from dash import Output
from dash import State
from dash import dcc
from dash import html
from dash.exceptions import PreventUpdate
from django.db.models import Case
from django.db.models import F
from django.db.models import IntegerField
from django.db.models import Sum
from django.db.models import Value
from django.db.models import When
from django.db.models.functions import Coalesce
from django_plotly_dash import DjangoDash
from pandas import DataFrame

from apps.briefcase.models import ApplicableLegislationAssessment
from apps.reports.dash import base
from apps.reports.dash.base import build_clauses_link

STATUS_ORDERING = ["CPL", "EO", "NA", "PG", "GAP"]

dash_compliance_app = DjangoDash("compliance_report")
app = dash_compliance_app

"""Set up the layout of the dashboard, including KPIs and filters."""
COLUMN_DEFS = [
    {
        "field"     : "document_name",
        "tooltipField": "document_name",
        "headerName": "Document",
        "headerTooltip": "Document",
        "flex"      : 5,
    },
    {
        "field"     : "jurisdiction_name",
        "tooltipField": "jurisdiction_name",
        "headerName": "Jurisdiction",
        "headerTooltip": "Jurisdiction",
        "flex"      : 3,
        # "filter"    : "agSetColumnFilter",
    },
    {
        "field"     : "operational_area_name",
        "tooltipField"     : "operational_area_name",
        "headerName": "Operational Area",
        "headerTooltip": "Operational Area",
        "flex"      : 3,
    },
    {"field": "program_name", "tooltipField": "program_name", "headerName": "Program", "headerTooltip": "Program",
     "flex": 3},
    {"field": "user_name", "tooltipField": "user_name", "headerName": "User", "headerTooltip": "User", "flex": 3},
]
COLUMN_DEFS += [
    {
        "field"     : status,
        "tooltipField": status,
        "headerName": ApplicableLegislationAssessment.ComplianceStatuses(
            status,
        ).label,
        "headerTooltip": ApplicableLegislationAssessment.ComplianceStatuses(
            status,
        ).label,
        "flex"      : 2,
    }
    for status in STATUS_ORDERING
]

app.layout = dbc.Container(
    fluid=True,
    children=[
        # Dropdowns for filters
        dbc.Row([
            dbc.Col(html.Figure([html.Blockquote(html.H4("Compliance Status Report"), className="blockquote"),
                                 html.Figcaption("Report on compliance status.", className="blockquote-footer")]),
                    width=True),
            dbc.Col([
                html.A(
                    "View Clauses",
                    id="view-clause-link",
                    href="#",
                    className="btn btn-sm btn-secondary me-2 shadow",
                    target="_blank",
                ),
                dbc.Button(
                    "Clear Filters",
                    id="clear-filters-btn",
                    size="sm",
                    className="me-2 shadow btn-secondary",
                ),
                dbc.DropdownMenu(
                    [
                        dbc.DropdownMenuItem(
                            [
                                ".csv",
                                dcc.Download(id="download-dataframe-csv"),
                            ],
                            id="download-btn-csv",
                        ),
                        dbc.DropdownMenuItem(
                            [
                                ".xlsx",
                                dcc.Download(id="download-dataframe-xlsx"),
                            ],
                            id="download-btn-xlsx",
                        ),
                    ],
                    label="Export",
                    id="download-btn-dropdown",
                    size="sm",
                    className="me-2 btn btn-link p-0",
                    toggleClassName="me-2 shadow btn-warning",
                ),
            ], width="auto"),
            html.Hr(),
        ], justify="between", align="center"),
        dbc.Row([
            dbc.Col(
                dcc.Dropdown(
                    id="legislation-dropdown",
                    options=[
                        {"label": "Legislation", "value": "public"},
                        {"label": "Conditions/Commitments", "value": "private"},
                    ],
                    placeholder="Legislation/Conditions",
                    clearable=True,
                ),
            ),
            dbc.Col(
                dcc.Dropdown(id="operational-area-dropdown", options=[], placeholder="Operational Area", multi=True)),
            dbc.Col(dcc.Dropdown(id="program-dropdown", options=[], placeholder="Program", multi=True)),
        ], className="mb-2"),
        dbc.Row([
            dbc.Col(dcc.Dropdown(id="jurisdiction-dropdown", options=[], placeholder="Jurisdiction", multi=True)),
            dbc.Col(dcc.Dropdown(id="assigned-to-dropdown", options=[], placeholder="Assigned User", multi=True)),
            dbc.Col(dcc.Dropdown(id="document-dropdown", options=[], placeholder="Document", multi=True)),
        ], className="mb-2"),
        dcc.Loading(
            id="loading-spinner",
            type="default",
            children=[
                # KPI Row
                dbc.Row(
                    [
                        dbc.Col(
                            dbc.Card(
                                [
                                    dbc.CardBody(
                                        [
                                            dbc.Checkbox(
                                                value=False,
                                                id=f"compliance-kpi-{status}-checkbox",
                                                style={
                                                    "position": "absolute",
                                                    "top"     : "5px",
                                                    "right"   : "0px",
                                                },
                                            ),
                                            html.A(
                                                html.I(className="fa fa-external-link-alt"),
                                                href="#",
                                                target="_blank",
                                                id=f"compliance-kpi-{status}-link",
                                                style={
                                                    "position": "absolute",
                                                    "top"     : "30px",
                                                    "right"   : "8px",
                                                    "color"   : "white",
                                                },
                                            ),
                                            dbc.Row([
                                                dbc.Col(
                                                    html.H5(
                                                        ApplicableLegislationAssessment.ComplianceStatuses(
                                                            status).label,
                                                        className="card-title",
                                                    ),
                                                    width=12,
                                                ),
                                            ]),
                                            dbc.Row([
                                                dbc.Col(
                                                    children=[
                                                        html.H2(
                                                            "",
                                                            id=f"compliance-kpi-{status}-document-count",
                                                            className="card-text mb-0 fw-bold text-center",
                                                        ),
                                                        html.P("Documents", className="text-center"),
                                                    ],
                                                ),
                                                dbc.Col(
                                                    children=[
                                                        html.H2(
                                                            "",
                                                            id=f"compliance-kpi-{status}-clause-count",
                                                            className="card-text mb-0 fw-bold text-center",
                                                        ),
                                                        html.P("Clauses", className="text-center"),
                                                    ],
                                                ),
                                            ]),
                                        ],
                                        className="p-1 px-2",
                                    ),
                                ],
                                color=color,
                                inverse=True,
                                style={"position": "relative"},
                            ),
                        )
                        for status, color in
                        zip(STATUS_ORDERING, ["success", "info", "gray", "warning", "#FF6347"], strict=False)
                    ],
                    className="mb-1",
                ),
                # DataTable
                dbc.Row(
                    [
                        dbc.Col(
                            dbc.Alert(
                                children="",
                                id="query-warning",
                                color="warning",
                                is_open=False,
                            ),
                        ),
                    ],
                    className="mb-1 p-1",
                ),
                dbc.Row(
                    [
                        dbc.Col(
                            dag.AgGrid(
                                enableEnterpriseModules=True,
                                licenseKey="non_commercial_license",
                                id="compliance-table",
                                rowData=[],
                                defaultColDef={
                                    "menuTabs": ["filterMenuTab"],  # Only show the filter tab in the column menu
                                },
                                columnDefs=COLUMN_DEFS,
                                dashGridOptions={
                                    "rowSelection": "multiple",
                                },
                            ),
                            width=12,
                        ),
                    ],
                ),
            ],
        ),
    ],
)


def kpi_fetch_data(tenant,
                   status_codes=(),
                   document_type=None,
                   operational_area_ids=(),
                   program_ids=(),
                   jurisdiction_ids=(),
                   assigned_to_ids=(),
                   document_ids=()):
    qs = fetch_data(tenant, status_codes, document_type, operational_area_ids, program_ids,
                    jurisdiction_ids, assigned_to_ids, document_ids)
    qs = qs.annotate(
        document_id=Coalesce(
            F("layout__document_id"),
            Value(None),
        ),
    ).values(
        "document_id",
    )
    # Add status summation using Case and Sum, but keep the original status names (no _sum suffix)
    for status in STATUS_ORDERING:
        qs = qs.annotate(
            **{
                f"{status}": Sum(
                    Case(
                        When(assessment__compliance_status=status, then=Value(1)),
                        default=Value(0),
                        output_field=IntegerField(),
                    ),
                ),
            },
        )

    df = DataFrame.from_records(qs)
    if df.empty:
        df = DataFrame(
            columns=["document_id"] + STATUS_ORDERING,
        )

    return df


def table_fetch_data(tenant,
                     status_codes=[],
                     document_type=None,
                     operational_area_ids=None,
                     program_ids=None,
                     jurisdiction_ids=None,
                     assigned_to_ids=None,
                     document_ids=None,
                     page_number=1,
                     page_size=20,
                     max_records=1000):
    # Fetch data and apply filters
    qs = fetch_data(tenant, status_codes, document_type, operational_area_ids, program_ids,
                    jurisdiction_ids, assigned_to_ids, document_ids)

    # Annotate the necessary fields (the fields we want to group by)
    qs = qs.annotate(
        document_name=Coalesce(
            F("layout__document__name"),
            Value("No Document"),
        ),
        document_id=Coalesce(
            F("layout__document_id"),
            Value(None),
        ),
        jurisdiction_name=Coalesce(
            F("layout__document__jurisdiction__name"),
            Value("No Jurisdiction"),
        ),
        program_name=Coalesce(
            F("assessment__programs__name"),
            Value("No Program"),
        ),
        user_name=Coalesce(
            F("assessment__assigned_to__name"),
            Value("No User"),
        ),
        operational_area_name=Coalesce(
            F("assessment__operational_areas__name"),
            Value("No Operational Area"),
        ),
    )

    # Group by the document and other relevant fields
    qs = qs.values(
        "document_id",
        "jurisdiction_name",
        "operational_area_name",
        "program_name",
        "user_name",
        "document_name",
    )

    # Add status summation using Case and Sum, but keep the original status names (no _sum suffix)
    for status in STATUS_ORDERING:
        qs = qs.annotate(
            **{
                f"{status}": Sum(
                    Case(
                        When(assessment__compliance_status=status, then=Value(1)),
                        default=Value(0),
                        output_field=IntegerField(),
                    ),
                ),
            },
        )

    # Limit to the first max_records (e.g., 1000) if needed
    if max_records:
        qs = qs[:max_records + 1]

    # Convert the QuerySet to a DataFrame
    df = DataFrame.from_records(qs)

    # Return an empty DataFrame if the QuerySet is empty
    if df.empty:
        df = DataFrame(
            columns=[
                        "document_id",
                        "jurisdiction_name",
                        "operational_area_name",
                        "program_name",
                        "document_name",
                        "user_name",
                    ]
                    + [status for status in STATUS_ORDERING],
        )

    return df


def fetch_data(
    tenant,
    status_codes=[],
    document_type=None,
    operational_area_ids=None,
    program_ids=None,
    jurisdiction_ids=None,
    assigned_to_ids=None,
    document_ids=None,
    kpi_only=False,
):
    """Fetch and process data for the report."""

    # Ensure only jurisdictions that tenants are subscribed to are reflected
    if jurisdiction_ids is None:
        jurisdiction_qs = tenant.jurisdictions.all()
        jurisdiction_ids = list(jurisdiction_qs.values_list("id", flat=True))

    # Exclude assessments not linked to applicable legislations and archived docs
    # Include applicable legislations with current layouts
    qs = tenant.applicablelegislation_set.select_related(
        "layout",
        "layout__document",
        "layout__document__tenant",
        "layout__document__jurisdiction",
        "assessment",
    ).filter(
        layout__current=True,
        layout__document__archived=False,
    ).prefetch_related(
        "search_control_documents",
        "assessment__operational_areas",
        "assessment__programs",
        "assessment__activities",
        "assessment__assigned_to",
    )

    if document_type:
        if document_type == "public":
            qs = qs.filter(
                layout__document__tenant__isnull=True,
            )
        elif document_type == "private":
            qs = qs.filter(
                layout__document__tenant__isnull=False,
            )

    if operational_area_ids:
        qs = qs.filter(
            assessment__operational_areas__id__in=operational_area_ids,
        )
    if program_ids:
        qs = qs.filter(
            assessment__programs__id__in=program_ids,
        )
    if jurisdiction_ids:
        qs = qs.filter(
            layout__document__jurisdiction_id__in=jurisdiction_ids,
        )
    if assigned_to_ids:
        qs = qs.filter(
            assessment__assigned_to__id__in=assigned_to_ids,
        )

    if document_ids:
        qs = qs.filter(layout__document_id__in=document_ids)

    if status_codes:
        qs = qs.filter(assessment__compliance_status__in=status_codes)

    status_pivot_annotations = {
        f"{code}": Sum(
            Case(
                When(assessment__compliance_status=code, then=Value(1)),
                default=Value(0),
                output_field=IntegerField(),
            ),
        )
        for code in STATUS_ORDERING
    }

    qs = qs.annotate(**status_pivot_annotations)
    return qs


app.callback(
    Output("operational-area-dropdown", "options"),
    [Input("operational-area-dropdown", "id")])(base.get_operational_area_selections)

app.callback(
    Output("program-dropdown", "options"),
    [Input("program-dropdown", "id")])(base.get_program_selections)

app.callback(
    Output("jurisdiction-dropdown", "options"),
    [Input("jurisdiction-dropdown", "id")])(base.get_jurisdiction_selections)
app.callback(
    Output("assigned-to-dropdown", "options"),
    [Input("assigned-to-dropdown", "id")])(base.get_assigned_to_selections)
app.callback(
    Output("document-dropdown", "options"),
    [Input("document-dropdown", "id")])(base.get_applicable_document_selections)


@app.callback(
    [Output("compliance-table", "rowData"),
     Output("query-warning", "children"),
     Output("query-warning", "is_open")],
    [
        Input("compliance-kpi-CPL-checkbox", "value"),
        Input("compliance-kpi-EO-checkbox", "value"),
        Input("compliance-kpi-NA-checkbox", "value"),
        Input("compliance-kpi-PG-checkbox", "value"),
        Input("compliance-kpi-GAP-checkbox", "value"),
        Input("legislation-dropdown", "value"),
        Input("operational-area-dropdown", "value"),
        Input("program-dropdown", "value"),
        Input("jurisdiction-dropdown", "value"),
        Input("assigned-to-dropdown", "value"),
        Input("document-dropdown", "value"),
    ],
)
def update_table(*args, request):
    document_type = args[-6]
    operational_areas_ids = args[-5]
    program_ids = args[-4]
    jurisdiction_ids = args[-3]
    assigned_to_ids = args[-2]
    document_ids = args[-1]
    status_selected = [
        status
        for selected, status in zip(
            args[:-1],
            STATUS_ORDERING,
            strict=False,
        )
        if selected
    ]
    """Update table based on selected KPIs and jurisdictions."""
    df = table_fetch_data(
        request.user.tenant,
        status_selected,
        document_type,
        operational_areas_ids,
        program_ids,
        jurisdiction_ids,
        assigned_to_ids,
        document_ids=document_ids,
    )

    warning = ""
    is_open = False
    if df.shape[0] > 1000:
        df = df.head(1000)
        warning = "Only partial data is shown in the table.  You may view complete data using the Export button, or add filters that return under 1,000 records."
        is_open = True
    return df.to_dict("records"), warning, is_open


@app.callback(
    [
        Output(f"compliance-kpi-{status}-document-count", "children")
        for status in STATUS_ORDERING
    ]
    + [
        Output(f"compliance-kpi-{status}-clause-count", "children")
        for status in STATUS_ORDERING
    ]
    + [
        Output(f"compliance-kpi-{status}-link", "href")
        for status in STATUS_ORDERING
    ],
    [
        Input("legislation-dropdown", "value"),
        Input("operational-area-dropdown", "value"),
        Input("program-dropdown", "value"),
        Input("jurisdiction-dropdown", "value"),
        Input("assigned-to-dropdown", "value"),
        Input("document-dropdown", "value"),
        Input("compliance-table", "selectedRows"),
    ],
)
def update_kpis(
    document_type,
    operational_area_selected,
    programs_selected,
    jurisdiction_selected,
    assigned_to_selected,
    document_selected,
    rows_selected,
    request,
):
    df = kpi_fetch_data(
        request.user.tenant,
        document_type=document_type,
        operational_area_ids=tuple(operational_area_selected or ()),
        jurisdiction_ids=tuple(jurisdiction_selected or ()),
        program_ids=tuple(programs_selected or ()),
        assigned_to_ids=tuple(assigned_to_selected or ()),
        document_ids=tuple(document_selected or ()),
    )

    # Calculate clause counts (unchanged)
    clause_kpi_counts = []
    kpi_links = []
    for status in STATUS_ORDERING:
        kpi = df[status].sum()
        clause_kpi_counts.append(kpi)

        kpi_link = build_clauses_link(compliance_statuses=[status], jurisdictions=jurisdiction_selected,
                                      programs=programs_selected, operational_areas=operational_area_selected,
                                      assignees=assigned_to_selected, documents=document_selected,
                                      document_type=document_type)
        kpi_links.append(kpi_link)

    document_count_df = df.groupby("document_id").sum()

    document_kpi_counts = []
    for status in STATUS_ORDERING:
        kpi = document_count_df[document_count_df[status] > 0].shape[0]
        document_kpi_counts.append(kpi)

    result = (
            document_kpi_counts + clause_kpi_counts + kpi_links
    )
    return result


@app.callback(
    Output("download-dataframe-csv", "data"),
    Input("download-btn-csv", "n_clicks"),
    [
        State("compliance-kpi-CPL-checkbox", "value"),
        State("compliance-kpi-EO-checkbox", "value"),
        State("compliance-kpi-NA-checkbox", "value"),
        State("compliance-kpi-PG-checkbox", "value"),
        State("compliance-kpi-GAP-checkbox", "value"),
        State("legislation-dropdown", "value"),
        State("operational-area-dropdown", "value"),
        State("program-dropdown", "value"),
        State("jurisdiction-dropdown", "value"),
        State("assigned-to-dropdown", "value"),
        State("document-dropdown", "value"),
    ],
    prevent_initial_call=True,
)
def generate_csv(n_clicks, *args, request):
    if n_clicks is None:
        return None

    # Get the filters/conditions from the dropdowns and checkboxes
    document_type = args[-6]
    operational_areas_ids = args[-5]
    program_ids = args[-4]
    jurisdiction_ids = args[-3]
    assigned_to_ids = args[-2]
    document_ids = args[-1]
    status_selected = [
        status
        for selected, status in zip(
            args[:-1],
            STATUS_ORDERING,
            strict=False,
        )
        if selected
    ]

    # Fetch the data based on these conditions
    df = table_fetch_data(
        tenant=request.user.tenant,
        status_codes=status_selected,
        document_type=document_type,
        operational_area_ids=operational_areas_ids,
        program_ids=program_ids,
        jurisdiction_ids=jurisdiction_ids,
        assigned_to_ids=assigned_to_ids,
        document_ids=document_ids,
        max_records=None,
    )

    # Remove the document_id column from the DataFrame
    if "document_id" in df.columns:
        df.drop(columns=["document_id"], inplace=True)

    # Map internal DataFrame column names to the corresponding `COLUMN_DEFS` names
    column_mapping = {col["field"]: col["headerName"] for col in COLUMN_DEFS}

    # Rename columns to match the names from COLUMN_DEFS
    df.rename(columns=column_mapping, inplace=True)

    # Convert the DataFrame to CSV
    csv_string = StringIO()
    df.to_csv(csv_string, index=False)
    csv_string.seek(0)
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"compliance_report_{timestamp}.csv"
    # Return the CSV file to the dcc.Download component
    return dict(content=csv_string.getvalue(), filename=filename)


@app.callback(
    Output("download-dataframe-xlsx", "data"),
    Input("download-btn-xlsx", "n_clicks"),
    [
        State("compliance-kpi-CPL-checkbox", "value"),
        State("compliance-kpi-EO-checkbox", "value"),
        State("compliance-kpi-NA-checkbox", "value"),
        State("compliance-kpi-PG-checkbox", "value"),
        State("compliance-kpi-GAP-checkbox", "value"),
        State("legislation-dropdown", "value"),
        State("operational-area-dropdown", "value"),
        State("program-dropdown", "value"),
        State("jurisdiction-dropdown", "value"),
        State("assigned-to-dropdown", "value"),
        State("document-dropdown", "value"),
    ],
    prevent_initial_call=True,
)
def generate_xlsx(n_clicks, *args, request):
    if n_clicks is None:
        return None

    # Get the filters/conditions from the dropdowns and checkboxes
    document_type = args[-6]
    operational_areas_ids = args[-5]
    program_ids = args[-4]
    jurisdiction_ids = args[-3]
    assigned_to_ids = args[-2]
    document_ids = args[-1]
    status_selected = [
        status
        for selected, status in zip(
            args[:-1],
            STATUS_ORDERING,
            strict=False,
        )
        if selected
    ]

    # Fetch the data based on these conditions
    df = table_fetch_data(
        tenant=request.user.tenant,
        status_codes=status_selected,
        document_type=document_type,
        operational_area_ids=operational_areas_ids,
        program_ids=program_ids,
        jurisdiction_ids=jurisdiction_ids,
        assigned_to_ids=assigned_to_ids,
        document_ids=document_ids,
        max_records=None,
    )

    # Remove the document_id column from the DataFrame
    if "document_id" in df.columns:
        df.drop(columns=["document_id"], inplace=True)

    # Map internal DataFrame column names to the corresponding `COLUMN_DEFS` names
    column_mapping = {col["field"]: col["headerName"] for col in COLUMN_DEFS}

    # Rename columns to match the names from COLUMN_DEFS
    df.rename(columns=column_mapping, inplace=True)

    # Convert the DataFrame to XLSX
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"compliance_report_{timestamp}.xlsx"
    # Return the XLSX file to the dcc.Download component
    return dcc.send_data_frame(
        df.to_excel,
        filename,
    )

@app.callback(
    Output("view-clause-link", "href"),
    [
        Input("compliance-kpi-CPL-checkbox", "value"),  # cpl_selected
        Input("compliance-kpi-EO-checkbox", "value"),  # eo_selected
        Input("compliance-kpi-NA-checkbox", "value"),  # na_selected
        Input("compliance-kpi-PG-checkbox", "value"),  # pg_selected
        Input("compliance-kpi-GAP-checkbox", "value"),  # gap_selected
        Input("legislation-dropdown", "value"),  # document_type
        Input("operational-area-dropdown", "value"),  # operational_area_selected
        Input("program-dropdown", "value"),  # programs_selected
        Input("jurisdiction-dropdown", "value"),  # jurisdiction_selected
        Input("assigned-to-dropdown", "value"),  # assigned_to_selected
        Input("document-dropdown", "value"),  # document_selected
    ],
)
def view_clauses_link(
    cpl_selected,
    eo_selected,
    na_selected,
    pg_selected,
    gap_selected,
    document_type,
    operational_area_selected,
    programs_selected,
    jurisdiction_selected,
    assigned_to_selected,
    document_selected,
    request,
):
    # Extract selected statuses from checkboxes
    status_selected = [
        status
        for selected, status in zip(
            [
                cpl_selected,
                eo_selected,
                na_selected,
                pg_selected,
                gap_selected,
            ],
            STATUS_ORDERING, strict=False,
        )
        if selected
    ]

    # Construct the URL
    clauses_link = build_clauses_link(compliance_statuses=status_selected, jurisdictions=jurisdiction_selected,
                                      programs=programs_selected, operational_areas=operational_area_selected,
                                      assignees=assigned_to_selected, documents=document_selected,
                                      document_type=document_type)

    return clauses_link


@app.callback(
    [
        Output("legislation-dropdown", "value"),
        Output("operational-area-dropdown", "value"),
        Output("program-dropdown", "value"),
        Output("jurisdiction-dropdown", "value"),
        Output("assigned-to-dropdown", "value"),
        Output("document-dropdown", "value"),
        Output("compliance-kpi-CPL-checkbox", "value"),
        Output("compliance-kpi-EO-checkbox", "value"),
        Output("compliance-kpi-NA-checkbox", "value"),
        Output("compliance-kpi-PG-checkbox", "value"),
        Output("compliance-kpi-GAP-checkbox", "value"),
    ],
    [Input("clear-filters-btn", "n_clicks")],
    prevent_initial_call=True,
)
def clear_filters(n_clicks):
    if n_clicks is None:
        raise PreventUpdate

    # Return default values for each input
    return [None, None, None, None, None, None, False, False, False, False, False]
