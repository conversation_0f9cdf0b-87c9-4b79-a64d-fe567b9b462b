from concurrent.futures import ThreadPoolExecutor
from concurrent.futures import as_completed

from django.apps import apps
from django.core.management.base import BaseCommand
from django.db import models
from progressbar import ProgressBar

from apps.briefcase.models import Activity
from apps.briefcase.models import ApplicableLegislation
from apps.briefcase.models import ControlDocument
from apps.briefcase.models import OperationalArea
from apps.briefcase.models import Program
from apps.briefcase.models import TenantDocument
from apps.document.models import Document
from apps.document.models import DocumentFragment
from apps.document.models import DocumentRevision
from apps.search.models import SearchableMixin
from apps.users.models import User


def process_update_search_data(instance):
    return instance.update_search_data(commit=False)

def process_update_search_m2m(instance):
    return instance.update_search_m2m(commit=False)

def process_update_search_tags(instance):
    return instance.update_search_tags(commit=False)

def get_chunk_size(model):
    if model in [Document, DocumentRevision]:
        return 500
    return 5000

class Command(BaseCommand):
    help = "Recalculates search data for all models inheriting from SearchableMixin"

    def add_arguments(self, parser):
        parser.add_argument(
            "--clear",
            action="store_true",
            help="Clear all existing search data before recalculating",
        )
        parser.add_argument(
            "--fragment",
            action="store_true",
            help="Only recalculate search data for fragments",
        )
        parser.add_argument(
            "--reference",
            action="store_true",
            help="Only recalculate search data for references",
        )
        parser.add_argument(
            "--document",
            action="store_true",
            help="Only recalculate search data for document",
        )
        parser.add_argument(
            "--tenantdoc",
            action="store_true",
            help="Only recalculate search data for tenant document",
        )
        parser.add_argument(
            "--vectoronly",
            action="store_true",
            help="Only recalculate search vectors",
        )

    def handle(self, *args, **options):
        searchable_models = []
        max_workers = None  # Adjust based on your system capabilities

        # Find all models that inherit from SearchableMixin
        for model in apps.get_models():
            if issubclass(model, SearchableMixin) and model != SearchableMixin:

                if options["fragment"] and model not in [DocumentFragment]:
                    continue

                if options["reference"] and model not in [
                    Activity, Program, OperationalArea, ControlDocument, User,
                ]:
                    continue

                if options["document"] and model not in [
                    Document, DocumentRevision,
                ]:
                    continue

                if options["tenantdoc"] and model not in [
                    TenantDocument, ApplicableLegislation,
                ]:
                    continue

                if options["clear"]:
                    self.stdout.write("Clearing search data for %s" % model.__name__)
                    search_data_cls = model.search_data.related.related_model if hasattr(model, "search_data") else None
                    search_tags_cls = model.search_tags.rel.related_model if hasattr(model, "search_tags") else None
                    if search_data_cls:
                        search_data_cls.objects.all().delete()
                    if search_tags_cls:
                        search_tags_cls.objects.all().delete()

                    model.clear_queryset_search_m2m()

                    self.stdout.write("Search data cleared for %s" % model.__name__)

                searchable_models.append(model)


        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            for model in searchable_models:
                chunk_size = get_chunk_size(model)

                search_data_cls = model.search_data.related.related_model if hasattr(model, "search_data") else None
                search_tags_cls = model.search_tags.rel.related_model if hasattr(model, "search_tags") else None

                instances = model.objects.filter(search_data__isnull=True)
                total_count = instances.count()
                if total_count == 0:
                    self.stdout.write(f"Completed {model.__name__}")
                    continue

                counter = 0
                self.stdout.write(f"Processing {model.__name__} ({total_count} items)...")
                pbar = ProgressBar(max_value=total_count)
                while instances.exists():
                    object_list = instances[:chunk_size]
                    search_data_futures = []
                    search_tag_futures = []
                    search_m2m_futures = []
                    instance_ids = []
                    for instance in object_list:
                        instance_ids.append(instance.id)
                        if search_data_cls:
                            search_data_futures.append(executor.submit(process_update_search_data, instance))

                        if search_tags_cls and not options.get("vectoronly"):
                            search_tag_futures.append(executor.submit(process_update_search_tags, instance))

                        if not options.get("vectoronly"):
                            search_m2m_futures.append(executor.submit(process_update_search_m2m, instance))

                    if search_tags_cls:
                        search_tag_instances = []
                        for future in as_completed(search_tag_futures):
                            try:
                                search_tag_instances.extend(future.result())
                            except Exception as e:
                                self.stdout.write(self.style.ERROR(f"Error processing search tag instance: {e!s}"))

                        search_tags_cls.objects.bulk_create(search_tag_instances, 2000, ignore_conflicts=True)

                    search_m2m_instances = []
                    for future in as_completed(search_m2m_futures):
                        try:
                            search_m2m_instances.extend(future.result())
                        except Exception as e:
                            self.stdout.write(self.style.ERROR(f"Error processing search m2m instance: {e!s}"))

                    if search_m2m_instances:
                        model.create_search_m2m(search_m2m_instances)

                    if search_data_cls:
                        search_data_instances = []
                        for future in as_completed(search_data_futures):
                            try:
                                search_data_instances.extend(future.result())
                            except Exception as e:
                                self.stdout.write(self.style.ERROR(f"Error processing search data instance: {e!s}"))

                        search_data_cls.objects.bulk_create(search_data_instances, 2000, ignore_conflicts=True)

                    counter += 1

                    instances = model.objects.filter(search_data__isnull=True)

                    # Update progress bar
                    pbar.update(min(counter * chunk_size, total_count))

                pbar.finish()
                self.stdout.write(f"Completed {model.__name__}")

        self.stdout.write(self.style.SUCCESS("Search data recalculation complete"))

def get_bulk_update_fields(model):
    """
    Returns a list of field names suitable for bulk_update.
    """
    fields = []
    for field in model._meta.fields:
        if isinstance(field, (
            models.AutoField,  # Auto-generated primary keys
            models.OneToOneField,  # One-to-one relationships (handled differently)
            models.ManyToManyField, # Many-to-many relationships (handled differently)
            models.ForeignKey, # Foreign key relationships (can be updated but require care)
        )):
            continue  # Skip these field types

        # Check if the field is editable (important for bulk_update)
        if field.editable:
            fields.append(field.name)
    return fields
