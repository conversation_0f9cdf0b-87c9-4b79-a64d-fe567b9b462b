# Generated by Django 5.0.3 on 2025-01-03 23:33

import django.contrib.postgres.indexes
import django.contrib.postgres.search
from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = [('search', '0001_initial')]

    operations = [
        migrations.RunSQL(
            sql="""
                DROP TRIGGER IF EXISTS search_vector_trigger
                ON document_document;""",
            reverse_sql="""
                DROP TRIGGER IF EXISTS search_vector_trigger
                ON document_document;
                """,
        ),
        migrations.RunSQL(
            sql="""
        DROP TRIGGER IF EXISTS search_vector_trigger
        ON document_documentfragment;
        """,
            reverse_sql="""
        DROP TRIGGER IF EXISTS search_vector_trigger
        ON document_documentfragment;
        """,
        ),
    ]
