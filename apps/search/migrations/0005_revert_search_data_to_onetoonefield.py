# Generated by Django 5.0.3 on 2025-02-16 04:53

import logging

import django.contrib.postgres.indexes
import django.contrib.postgres.search
import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models

logger = logging.getLogger(__name__)

def clear_search_data(apps, schema_editor):
    ActivitySearchData = apps.get_model('search', 'ActivitySearchData')
    ApplicableSearchData = apps.get_model('search', 'ApplicableSearchData')
    ProgramSearchData = apps.get_model('search', 'ProgramSearchData')
    DocumentSearchData = apps.get_model('search', 'DocumentSearchData')
    FragmentSearchData = apps.get_model('search', 'FragmentSearchData')
    RevisionSearchData = apps.get_model('search', 'RevisionSearchData')
    ControlSearchData = apps.get_model('search', 'ControlSearchData')
    OpAreaSearchData = apps.get_model('search', 'OpAreaSearchData')
    UserSearchData = apps.get_model('search', 'UserSearchData')
    TenantDocSearchData = apps.get_model('search', 'TenantDocSearchData')

    for model in [
        ActivitySearchData,
        ApplicableSearchData,
        ProgramSearchData,
        DocumentSearchData,
        FragmentSearchData,
        RevisionSearchData,
        ControlSearchData,
        OpAreaSearchData,
        UserSearchData,
        TenantDocSearchData,
    ]:
        if model.objects.exists():
            logger.info(f"Clearing search data for {model}...")
            model.objects.all().delete()

class Migration(migrations.Migration):

    dependencies = [
        ('briefcase', '0023_alter_applicablelegislationassessment_assessment_status_and_more'),
        ('document', '0015_remove_document_search_data_and_more'),
        ('search', '0004_applicablesearchcontrol_applicablesearchdata_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.RunPython(
            clear_search_data,
          reverse_code=migrations.RunPython.noop,
        ),
        migrations.RemoveIndex(
            model_name='activitysearchdata',
            name='search_acti_search__7ac92e_gin',
        ),
        migrations.RemoveIndex(
            model_name='activitysearchdata',
            name='activitysearchdata_searchtype',
        ),
        migrations.RemoveIndex(
            model_name='applicablesearchdata',
            name='applicablesearchdataindex',
        ),
        migrations.RemoveIndex(
            model_name='applicablesearchdata',
            name='applicablesearchvectorindex',
        ),
        migrations.RemoveIndex(
            model_name='controlsearchdata',
            name='search_cont_search__dc8c27_gin',
        ),
        migrations.RemoveIndex(
            model_name='controlsearchdata',
            name='controlsearchdata_searchtype',
        ),
        migrations.RemoveIndex(
            model_name='documentsearchdata',
            name='search_docu_search__647091_gin',
        ),
        migrations.RemoveIndex(
            model_name='documentsearchdata',
            name='documentsearchdata_searchtype',
        ),
        migrations.RemoveIndex(
            model_name='fragmentsearchdata',
            name='search_frag_search__a28dd1_gin',
        ),
        migrations.RemoveIndex(
            model_name='fragmentsearchdata',
            name='fragmentsearchdata_searchtype',
        ),
        migrations.RemoveIndex(
            model_name='opareasearchdata',
            name='search_opar_search__181855_gin',
        ),
        migrations.RemoveIndex(
            model_name='opareasearchdata',
            name='opareasearchdata_searchtype',
        ),
        migrations.RemoveIndex(
            model_name='programsearchdata',
            name='search_prog_search__0a31f1_gin',
        ),
        migrations.RemoveIndex(
            model_name='programsearchdata',
            name='programsearchdata_searchtype',
        ),
        migrations.RemoveIndex(
            model_name='revisionsearchdata',
            name='search_revi_search__7ee810_gin',
        ),
        migrations.RemoveIndex(
            model_name='revisionsearchdata',
            name='revisionsearchdata_searchtype',
        ),
        migrations.RemoveIndex(
            model_name='tenantdocsearchdata',
            name='search_tena_search__4325d6_gin',
        ),
        migrations.RemoveIndex(
            model_name='tenantdocsearchdata',
            name='tenantdocsearchdata_searchtype',
        ),
        migrations.RemoveIndex(
            model_name='usersearchdata',
            name='search_user_search__805542_gin',
        ),
        migrations.RemoveIndex(
            model_name='usersearchdata',
            name='usersearchdata_searchtype',
        ),
        migrations.RemoveField(
            model_name='activitysearchdata',
            name='search_type',
        ),
        migrations.RemoveField(
            model_name='activitysearchdata',
            name='search_vector',
        ),
        migrations.RemoveField(
            model_name='applicablesearchdata',
            name='search_type',
        ),
        migrations.RemoveField(
            model_name='applicablesearchdata',
            name='search_vector',
        ),
        migrations.RemoveField(
            model_name='controlsearchdata',
            name='search_type',
        ),
        migrations.RemoveField(
            model_name='controlsearchdata',
            name='search_vector',
        ),
        migrations.RemoveField(
            model_name='documentsearchdata',
            name='search_type',
        ),
        migrations.RemoveField(
            model_name='documentsearchdata',
            name='search_vector',
        ),
        migrations.RemoveField(
            model_name='fragmentsearchdata',
            name='search_type',
        ),
        migrations.RemoveField(
            model_name='fragmentsearchdata',
            name='search_vector',
        ),
        migrations.RemoveField(
            model_name='opareasearchdata',
            name='search_type',
        ),
        migrations.RemoveField(
            model_name='opareasearchdata',
            name='search_vector',
        ),
        migrations.RemoveField(
            model_name='programsearchdata',
            name='search_type',
        ),
        migrations.RemoveField(
            model_name='programsearchdata',
            name='search_vector',
        ),
        migrations.RemoveField(
            model_name='revisionsearchdata',
            name='search_type',
        ),
        migrations.RemoveField(
            model_name='revisionsearchdata',
            name='search_vector',
        ),
        migrations.RemoveField(
            model_name='tenantdocsearchdata',
            name='search_type',
        ),
        migrations.RemoveField(
            model_name='tenantdocsearchdata',
            name='search_vector',
        ),
        migrations.RemoveField(
            model_name='usersearchdata',
            name='search_type',
        ),
        migrations.RemoveField(
            model_name='usersearchdata',
            name='search_vector',
        ),
        migrations.AddField(
            model_name='activitysearchdata',
            name='search_vector_content',
            field=django.contrib.postgres.search.SearchVectorField(null=True),
        ),
        migrations.AddField(
            model_name='activitysearchdata',
            name='search_vector_title',
            field=django.contrib.postgres.search.SearchVectorField(null=True),
        ),
        migrations.AddField(
            model_name='applicablesearchdata',
            name='search_vector_content',
            field=django.contrib.postgres.search.SearchVectorField(null=True),
        ),
        migrations.AddField(
            model_name='applicablesearchdata',
            name='search_vector_title',
            field=django.contrib.postgres.search.SearchVectorField(null=True),
        ),
        migrations.AddField(
            model_name='controlsearchdata',
            name='search_vector_content',
            field=django.contrib.postgres.search.SearchVectorField(null=True),
        ),
        migrations.AddField(
            model_name='controlsearchdata',
            name='search_vector_title',
            field=django.contrib.postgres.search.SearchVectorField(null=True),
        ),
        migrations.AddField(
            model_name='documentsearchdata',
            name='search_vector_content',
            field=django.contrib.postgres.search.SearchVectorField(null=True),
        ),
        migrations.AddField(
            model_name='documentsearchdata',
            name='search_vector_title',
            field=django.contrib.postgres.search.SearchVectorField(null=True),
        ),
        migrations.AddField(
            model_name='fragmentsearchdata',
            name='search_vector_content',
            field=django.contrib.postgres.search.SearchVectorField(null=True),
        ),
        migrations.AddField(
            model_name='fragmentsearchdata',
            name='search_vector_title',
            field=django.contrib.postgres.search.SearchVectorField(null=True),
        ),
        migrations.AddField(
            model_name='opareasearchdata',
            name='search_vector_content',
            field=django.contrib.postgres.search.SearchVectorField(null=True),
        ),
        migrations.AddField(
            model_name='opareasearchdata',
            name='search_vector_title',
            field=django.contrib.postgres.search.SearchVectorField(null=True),
        ),
        migrations.AddField(
            model_name='programsearchdata',
            name='search_vector_content',
            field=django.contrib.postgres.search.SearchVectorField(null=True),
        ),
        migrations.AddField(
            model_name='programsearchdata',
            name='search_vector_title',
            field=django.contrib.postgres.search.SearchVectorField(null=True),
        ),
        migrations.AddField(
            model_name='revisionsearchdata',
            name='search_vector_content',
            field=django.contrib.postgres.search.SearchVectorField(null=True),
        ),
        migrations.AddField(
            model_name='revisionsearchdata',
            name='search_vector_title',
            field=django.contrib.postgres.search.SearchVectorField(null=True),
        ),
        migrations.AddField(
            model_name='tenantdocsearchdata',
            name='search_vector_content',
            field=django.contrib.postgres.search.SearchVectorField(null=True),
        ),
        migrations.AddField(
            model_name='tenantdocsearchdata',
            name='search_vector_title',
            field=django.contrib.postgres.search.SearchVectorField(null=True),
        ),
        migrations.AddField(
            model_name='usersearchdata',
            name='search_vector_content',
            field=django.contrib.postgres.search.SearchVectorField(null=True),
        ),
        migrations.AddField(
            model_name='usersearchdata',
            name='search_vector_title',
            field=django.contrib.postgres.search.SearchVectorField(null=True),
        ),
        migrations.AlterField(
            model_name='activitysearchdata',
            name='search_instance',
            field=models.OneToOneField(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='search_data', to='briefcase.activity'),
        ),
        migrations.AlterField(
            model_name='applicablesearchdata',
            name='search_instance',
            field=models.OneToOneField(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='search_data', to='briefcase.applicablelegislation'),
        ),
        migrations.AlterField(
            model_name='controlsearchdata',
            name='search_instance',
            field=models.OneToOneField(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='search_data', to='briefcase.controldocument'),
        ),
        migrations.AlterField(
            model_name='documentsearchdata',
            name='search_instance',
            field=models.OneToOneField(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='search_data', to='document.document'),
        ),
        migrations.AlterField(
            model_name='fragmentsearchdata',
            name='search_instance',
            field=models.OneToOneField(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='search_data', to='document.documentfragment'),
        ),
        migrations.AlterField(
            model_name='opareasearchdata',
            name='search_instance',
            field=models.OneToOneField(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='search_data', to='briefcase.operationalarea'),
        ),
        migrations.AlterField(
            model_name='programsearchdata',
            name='search_instance',
            field=models.OneToOneField(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='search_data', to='briefcase.program'),
        ),
        migrations.AlterField(
            model_name='revisionsearchdata',
            name='search_instance',
            field=models.OneToOneField(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='search_data', to='document.documentrevision'),
        ),
        migrations.AlterField(
            model_name='tenantdocsearchdata',
            name='search_instance',
            field=models.OneToOneField(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='search_data', to='briefcase.tenantdocument'),
        ),
        migrations.AlterField(
            model_name='usersearchdata',
            name='search_instance',
            field=models.OneToOneField(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='search_data', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddIndex(
            model_name='activitysearchdata',
            index=django.contrib.postgres.indexes.GinIndex(fields=['search_vector_title', 'search_vector_content'], name='search_acti_search__9525ae_gin'),
        ),
        migrations.AddIndex(
            model_name='applicablesearchdata',
            index=django.contrib.postgres.indexes.GinIndex(fields=['search_vector_title', 'search_vector_content'], name='applicablesearchvectorindex'),
        ),
        migrations.AddIndex(
            model_name='controlsearchdata',
            index=django.contrib.postgres.indexes.GinIndex(fields=['search_vector_title', 'search_vector_content'], name='search_cont_search__10f25c_gin'),
        ),
        migrations.AddIndex(
            model_name='documentsearchdata',
            index=django.contrib.postgres.indexes.GinIndex(fields=['search_vector_title', 'search_vector_content'], name='search_docu_search__efff11_gin'),
        ),
        migrations.AddIndex(
            model_name='fragmentsearchdata',
            index=django.contrib.postgres.indexes.GinIndex(fields=['search_vector_title', 'search_vector_content'], name='search_frag_search__f48f9a_gin'),
        ),
        migrations.AddIndex(
            model_name='opareasearchdata',
            index=django.contrib.postgres.indexes.GinIndex(fields=['search_vector_title', 'search_vector_content'], name='search_opar_search__49305a_gin'),
        ),
        migrations.AddIndex(
            model_name='programsearchdata',
            index=django.contrib.postgres.indexes.GinIndex(fields=['search_vector_title', 'search_vector_content'], name='search_prog_search__e5a966_gin'),
        ),
        migrations.AddIndex(
            model_name='revisionsearchdata',
            index=django.contrib.postgres.indexes.GinIndex(fields=['search_vector_title', 'search_vector_content'], name='search_revi_search__9ed598_gin'),
        ),
        migrations.AddIndex(
            model_name='tenantdocsearchdata',
            index=django.contrib.postgres.indexes.GinIndex(fields=['search_vector_title', 'search_vector_content'], name='search_tena_search__f98016_gin'),
        ),
        migrations.AddIndex(
            model_name='usersearchdata',
            index=django.contrib.postgres.indexes.GinIndex(fields=['search_vector_title', 'search_vector_content'], name='search_user_search__301d32_gin'),
        ),
        migrations.RemoveField(
            model_name='applicablesearchfields',
            name='search_instance',
        ),
        migrations.RemoveField(
            model_name='applicablesearchfields',
            name='tenant_document',
        ),
        migrations.AddField(
            model_name='applicablesearchdata',
            name='assessment_outstanding',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='applicablesearchdata',
            name='assessment_status',
            field=models.CharField(choices=[('NA', 'Not Assessed'), ('UA', 'Under Assessment'), ('C', 'Complete')],
                                   default='NA', max_length=3),
        ),
        migrations.AddField(
            model_name='applicablesearchdata',
            name='completed',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='applicablesearchdata',
            name='compliance_status',
            field=models.CharField(
                choices=[('CPL', 'Compliant'), ('PG', 'Partial Gap'), ('GAP', 'Gap'), ('NA', 'Not Required'),
                         ('EO', 'Evaluation Outstanding')], default='EO', max_length=3),
        ),
        migrations.AddField(
            model_name='applicablesearchdata',
            name='date_added',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='applicablesearchdata',
            name='no_controls_required',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='applicablesearchdata',
            name='priority_level',
            field=models.CharField(choices=[('NP', 'No Priority'), ('LP', 'Low Priority'), ('MP', 'Medium Priority'),
                                            ('HP', 'High Priority')], default='NP', max_length=2),
        ),
        migrations.AddField(
            model_name='applicablesearchdata',
            name='tenant_document',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE,
                                    related_name='applicable_search_data', to='briefcase.tenantdocument'),
        ),
        migrations.AddIndex(
            model_name='applicablesearchdata',
            index=models.Index(
                fields=['assessment_status', 'compliance_status', 'priority_level', 'date_added', 'completed',
                        'no_controls_required', 'assessment_outstanding'], name='applicablesearchdata_index'),
        ),
        migrations.DeleteModel(
            name='ApplicableSearchFields',
        ),
    ]
