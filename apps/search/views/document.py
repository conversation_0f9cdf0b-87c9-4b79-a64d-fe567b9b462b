import logging
from urllib.parse import urlencode

import django_filters
import pydash
from bootstrap_daterangepicker import fields
from django.contrib.auth.mixins import LoginRequiredMixin
from django.contrib.postgres.aggregates import ArrayAgg
from django.contrib.postgres.search import SearchHeadline
from django.db.models import Q
from django.db.models import F
from django.db.models import Value
from django.db.models import Case
from django.db.models import When
from django.db.models import CharField 
from django.db.models.functions import Concat
from django.db.models.aggregates import Count
from django.http.request import QueryDict
from django.shortcuts import redirect, render
from django.utils.datastructures import MultiValueDict
from django.utils.translation import gettext_lazy as _
from django.views.generic import FormView
from django.urls import reverse
from apps.common.views import CustomPermissionRequiredMixin
from apps.common.views.mixins import CustomWaffleFlagMixin
from apps.document.models import Document
from apps.document.models import Tag
from apps.search.forms import DocumentSearchForm
from apps.search.postgres.query import EnhancedSearchQuery
from apps.tenants.models import Tenant
from apps.tenants.models import TenantFeatureFlag
from apps.utils.lookups import lookup_key
from apps.exports.views import ExportMixinViewSet
from .base import SearchListView
from apps.document.models import DocumentLayout
from django_htmx.http import retarget

logger = logging.getLogger(__name__)

class DocumentSearchViewSetMixin(LoginRequiredMixin, CustomWaffleFlagMixin, CustomPermissionRequiredMixin):
    permission_required = "document.view_document"
    model = Document
    waffle_flag = TenantFeatureFlag.Features.LIBRARY.value


class DocumentSearchView(DocumentSearchViewSetMixin, FormView):
    form_class = DocumentSearchForm
    success_url = None

    def get_template_names(self):
        if self.request.htmx:
            return "search/form.partial.html"
        return "search/form.html"

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context.update(
            {
                "search_options": [
                    ("Title And Clauses", _("Titles and Clauses")),
                    ("Titles Only", _("Titles Only")),
                    ("Clauses Only", _("Clauses Only")),
                ],
            },
        )
        return context

    def get_form_kwargs(self):
        form_kwargs = super().get_form_kwargs()
        if self.request.method == "POST" and self.request.POST:
            form_kwargs["data"] = self.request.POST
        elif self.request.method == "GET" and self.request.GET:
            form_kwargs["data"] = self.request.GET
        else:
            form_kwargs["data"] = QueryDict(mutable=True)
        form_kwargs["request"] = self.request

        # if it is an htmx call to target a modal, display the footer
        if self.request.htmx and "modal" in self.request.htmx.target:
            form_kwargs["display_modal_footer"] = True

        return form_kwargs

    def form_invalid(self, form):
        response = super().form_invalid(form)
        if form.errors:
            logger.error("Error during form validation for document search view: %s", form.errors)
        return response

    def form_valid(self, form):
        """ Gather the query filters and redirect to success url """
        cleaned_data = form.cleaned_data
        multi_values = []
        multi_value_dict = {}
        for key, value in cleaned_data.items():
            if isinstance(value, (list, tuple)):
                multi_values.append(key)

        for key in multi_values:
            value = cleaned_data.pop(key)
            if isinstance(form.fields[key], fields.DateRangeField) and value:
                start, end = value
                if start and end:
                    multi_value_dict[key] = [f"{start} - {end}"]
                    multi_value_dict[f"{key}_min"] = [str(start)]
                    multi_value_dict[f"{key}_max"] = [str(end)]
            else:
                multi_value_dict[key] = value

        query_dict = QueryDict(urlencode(cleaned_data), mutable=True)
        query_dict.update(MultiValueDict(multi_value_dict))
        return redirect(f"{self.get_success_url()}?{query_dict.urlencode()}")

class DocumentSearchFilter(django_filters.FilterSet):
    title = _("Search Library")
    instructions = _("Start by expanding a jurisdiction or using the filtering options below")

    class Meta:
        model = Document
        exclude = ["data"]


    search_input = django_filters.CharFilter(method="full_text_search_filter", label="Search input")
    search_criteria = django_filters.ChoiceFilter(method="search_criteria_filter", label="Search Criteria",
                                                  choices=DocumentSearchForm.CATEGORY_CHOICES)
    archived = django_filters.BooleanFilter(field_name="archived", label="Archived")

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        user = self.request.user
        tenant = user.tenant

        self.filters["jurisdiction"] = django_filters.ModelMultipleChoiceFilter(
            queryset=tenant.jurisdictions.all(),
            field_name="jurisdiction",
            to_field_name="id",
            label="Jurisdiction",
        )

        self.filters["tenant"] = django_filters.ModelMultipleChoiceFilter(
            queryset=Tenant.objects.filter(id=tenant.id),
            field_name="tenant",
            to_field_name="id",
            label="Tenant",
        )

        tag_types = Tag.get_root_nodes()
        for tag_type in tag_types:
            tag_type_code = f"tag-{tag_type.code}"
            tags = tag_type.get_descendants(include_self=True)

            self.filters[tag_type_code] = django_filters.ModelMultipleChoiceFilter(
                queryset=tags,
                field_name="search_tags__tag",
                to_field_name="id",
                label=tag_type.name,
            )

    def full_text_search_filter(self, queryset, name, value):
        search_criteria = self.form.cleaned_data.get("search_criteria")
        search_vector_title_filter = Q(
            search_data__search_vector_title=EnhancedSearchQuery(
                value,
                search_type="conformii",
                config="english",
            ),
        )
        search_vector_content_filter = Q(
            search_data__search_vector_content=EnhancedSearchQuery(
                value,
                search_type="conformii",
                config="english",
            ),
        )
        search_vector_filter = search_vector_title_filter | search_vector_content_filter
        if search_criteria == DocumentSearchForm.CLAUSES_ONLY:
            search_vector_filter = search_vector_content_filter
        elif search_criteria == DocumentSearchForm.TITLES_ONLY:
            search_vector_filter = search_vector_title_filter

        return queryset.filter(search_vector_filter)


    def search_criteria_filter(self, queryset, name, value):
        return queryset


class DocumentSearchJurisdictionListView(DocumentSearchViewSetMixin, SearchListView):
    template_name = "search/jurisdiction_list.html"
    filterset_class = DocumentSearchFilter

    def get_queryset(self):
        tenant = self.request.user.tenant
        jurisdiction_qs = tenant.jurisdictions.all()
        jurisdiction_ids = list(jurisdiction_qs.values_list("id", flat=True))

        return Document.objects.library_current_set(include_archived=True).filter(
            jurisdiction__in=jurisdiction_ids,
        ).select_related("search_data")

    def get_search_results(self, queryset):
        tenant = self.request.user.tenant
        jurisdiction_qs = tenant.jurisdictions.all()

        jurisdiction_lookup = lookup_key(
            jurisdiction_qs.values("id", "name"), "id", "name",
        )

        search_results = queryset.values(
            "jurisdiction__name",
            "jurisdiction_id",
        ).order_by(
            "jurisdiction__name",
            "jurisdiction_id",
        ).annotate(
            document_ids=ArrayAgg("id", distinct=True),
            search_count=Count("id", distinct=True),
        ).values(
            "document_ids",
            "search_count",
            "jurisdiction__name",
            "jurisdiction_id",
        )

        # group each document per jurisdiction
        docs_by_jurisdiction = pydash.group_by(search_results, "jurisdiction_id")
        return  [
            {
                "jurisdiction_id": jurisdiction_id,
                "jurisdiction_name": jurisdiction_lookup[jurisdiction_id],
                "annotation": document_ids,
            }
            for jurisdiction_id, document_ids in sorted(docs_by_jurisdiction.items())
        ]

    def get_export_url(self, export_format):
        """
        Generates an export URL for a given format.
        The current GET parameters (filters) are passed along.
        """
        return reverse("search:library:search-export", kwargs={"export_format": export_format}) + "?is_async=1&" + self.request.GET.urlencode()

    def get_export_urls(self):
        """
        Build the export URL links for each available export format.
        """
        export_urls = []
        for export_format in ExportMixinViewSet.export_formats:
            export_url = self.get_export_url(export_format)
            export_urls.append(
                f"""<a hx-get="{export_url}" target="_blank" type="button" class="dropdown-item">.{export_format}</a>"""
            )
        return export_urls

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context["export_urls"] = self.get_export_urls()
        return context

class DocumentSearchListView(DocumentSearchViewSetMixin, SearchListView):
    template_name = "search/document_list.html"
    filterset_class = DocumentSearchFilter

    def get_queryset(self):
        tenant = self.request.user.tenant
        jurisdiction_qs = tenant.jurisdictions.all()
        jurisdiction_ids = list(jurisdiction_qs.values_list("id", flat=True))

        return Document.objects.library_current_set(include_archived=True).filter(
            jurisdiction_id__in=jurisdiction_ids,
        )

    def get_search_results(self, queryset):
        search_criteria = self.request.GET.get("search_criteria")
        search_input = self.request.GET.get("search_input")
        highlight_document_name_annotation = F("name")

        if search_input and search_criteria in [
            DocumentSearchForm.TITLES_AND_CLAUSES,
            DocumentSearchForm.TITLES_ONLY,
        ]:
            search_query = EnhancedSearchQuery(
                search_input,
                search_type="conformii",
                config="english",
            )
            highlight_document_name_annotation = SearchHeadline(
                highlight_document_name_annotation,
                search_query,
                start_sel='<mark class="search-match">',
                stop_sel="</mark>",
                config="english",
                highlight_all=True,
            )

        return queryset.annotate(
            highlighted_document_name=highlight_document_name_annotation,
        ).order_by("name").values(
            "id",
            "name",
            "highlighted_document_name",
        )


class DocumentSearchExportView(ExportMixinViewSet, DocumentSearchListView):
    def get_session_key(self):
        return "library-search-session"
    
    def get_submit_url(self):
        return ""

    def get_form_modal_url(self):
        return ""

    def get_export_columns(self):
        return [
            {
                "column_name": "df_name",
                "annotation" : F("name"),
                "label"      : "Document Name",
            },
            {
                "column_name": "df_jurisdiction_name",
                "annotation" : F("jurisdiction__name"),
                "label"      : "Jurisdiction",
            },
            {
                "column_name": "df_document_type",
                "annotation" : F("document_type__name"),
                "label"      : "Document Type",
            },
            {
                "column_name": "df_version_name",
                "annotation" : F("version_name"),
                "label"      : "Version Name",
            },
            {
                "column_name": "df_amendment_name",
                "annotation" : F("amendment_name"),
                "label"      : "Amendment Name",
            },
            {
                "column_name": "df_source_url",
                "annotation" : F("source_url"),
                "label"      : "Source URL",
            },
            {
                "column_name": "df_amendment_url",
                "annotation" : F("amendment_url"),
                "label"      : "Amendment URL",
            },
            {
                "column_name": "df_effective_date",
                "annotation" : F("effective_date"),
                "label"      : "Effective Date",
                "date_format": "%Y-%m-%d",
            },
            {
                "column_name": "df_requires_license",
                "annotation" : F("requires_license"),
                "label"      : "Requires License",
            },
            {
                "column_name": "df_description",
                "annotation" : F("description"),
                "label"      : "Description",
            },
            {
                "column_name": "df_document_url",
                "annotation" : F("id"),
                "label"      : "Document URL",
                "apply_func" : lambda row: self.request.build_absolute_uri(
                    reverse("document:current:detail", kwargs={"id": row}),
                ),
            },
        ]