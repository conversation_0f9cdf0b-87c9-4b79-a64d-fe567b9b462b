let scrollToTopButton = document.getElementById("btn-scroll-to-top");

window.onscroll = function () {
    scrollFunction();
};

function scrollFunction() {
    if (scrollToTopButton &&
        (document.body.scrollTop > 20 || document.documentElement.scrollTop > 20)
    ) {
        scrollToTopButton.classList.add("show");
    } else {
        scrollToTopButton.classList.remove("show");
    }
}

if (scrollToTopButton) {
    scrollToTopButton.addEventListener("click", backToTop);
}

function backToTop() {
    window.scrollTo({
        top: 0,
        behavior: "smooth"
    });
}

function toggleCheckboxes(toggle, checkboxIdPattern) {
    document.querySelectorAll("input[type='checkbox']").forEach((input) => {
        if (checkboxIdPattern) {
            if (input.id.indexOf(checkboxIdPattern) >= 0) {
                input.checked = toggle;
            }
        } else {
            input.checked = toggle;
        }
    });
}

function addScrollToLayoutListener() {
    document.body.addEventListener("scrollToLayout", function (event) {
        let elementId = event.detail.elementId;
        let focusElementId = event.detail.focusElementId;
        let mode = event.detail.mode ? event.detail.mode : "end"
        let behaviour = event.detail.behaviour ? event.detail.behaviour : "smooth"
        let layout = document.getElementById(elementId);
        let focusElement = document.getElementById(focusElementId);
        if (layout) {
            setTimeout(function () {
                layout.scrollIntoView({behavior: behaviour, block: mode, inline: mode});
            }, 100);
        }
        if (focusElement) {
            focusElement.focus();
        }
    });
}

$(document).ready(function () {
    scrollFunction();
    addScrollToLayoutListener();
});

htmx.on("htmx:afterRequest", (e) => {
    console.log('afterRequest was triggered!', e);
    let loginUrl = "/accounts/login/"; // "{{ login_url }}";
    // If the htmx response starts with the login_url, force redirect to login page
    if (e.detail?.pathInfo?.responsePath?.startsWith(loginUrl)) {
        console.log("redirect user to login page: ", loginUrl);
        window.location.replace(loginUrl);
    }
    $('[data-bs-toggle="tooltip"]').tooltip();
})

$(function () {
    $("[rel='tooltip']").tooltip();
});
