{"version": 3, "sourceRoot": "", "sources": ["../../../src/plugins/multi-carousel/scss/_multi-carousel.scss"], "names": [], "mappings": "AAAA,gBACE,kBACA,sCACE,kBACA,WACA,gBACA,mBACA,YACA,2DACE,qBACA,YACA,eACA,4BACA,cACA,+DACE,iBACA,iBAKN,yBACE,aACA,yBAFF,yBAGI,cAEF,+CACE,YACA,oEACE,WACA,cAEF,mDACE,gBAGJ,gDACE,WACA,WAEF,gDACE,SACA,WACA,WAEF,0GAEE", "file": "multi-carousel.min.css", "sourcesContent": [".multi-carousel {\n  position: relative;\n  .multi-carousel-inner {\n    position: relative;\n    width: 100%;\n    overflow: hidden;\n    white-space: nowrap;\n    font-size: 0;\n    .multi-carousel-item {\n      display: inline-block;\n      width: 33.3%;\n      font-size: 16px;\n      transition: all 0.3s ease-out;\n      padding: 0 3px;\n      img {\n        max-height: 200px;\n        object-fit: cover;\n      }\n    }\n  }\n\n  &.vertical {\n    height: 350px;\n    @media (max-width: 768px) {\n      height: 150px;\n    }\n    .multi-carousel-inner {\n      height: 100%;\n      .multi-carousel-item {\n        width: 100%;\n        padding: 2px 0;\n      }\n      img {\n        max-height: none;\n      }\n    }\n    .carousel-control-prev {\n      width: 100%;\n      height: 15%;\n    }\n    .carousel-control-next {\n      top: auto;\n      width: 100%;\n      height: 15%;\n    }\n    .carousel-control-prev span,\n    .carousel-control-next span {\n      transform: rotate(90deg);\n    }\n  }\n}\n"]}