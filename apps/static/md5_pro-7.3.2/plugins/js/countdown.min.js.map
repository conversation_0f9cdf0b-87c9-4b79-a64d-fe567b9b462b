{"version": 3, "file": "countdown.min.js", "sources": ["../../../src/plugins/countdown/js/mdb/util/index.js", "../../../src/plugins/countdown/js/mdb/dom/data.js", "../../../src/plugins/countdown/js/mdb/dom/manipulator.js", "../../../src/plugins/countdown/js/mdb/dom/polyfill.js", "../../../src/plugins/countdown/js/mdb/dom/event-handler.js", "../../../src/plugins/countdown/js/mdb/dom/selector-engine.js", "../../../src/plugins/countdown/js/countdown.js"], "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta1): util/index.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst MAX_UID = 1000000;\nconst MILLISECONDS_MULTIPLIER = 1000;\nconst TRANSITION_END = 'transitionend';\n\n// Shoutout AngusCroll (https://goo.gl/pxwQGp)\nconst toType = (obj) => {\n  if (obj === null || obj === undefined) {\n    return `${obj}`;\n  }\n\n  return {}.toString\n    .call(obj)\n    .match(/\\s([a-z]+)/i)[1]\n    .toLowerCase();\n};\n\n/**\n * --------------------------------------------------------------------------\n * Public Util Api\n * --------------------------------------------------------------------------\n */\n\nconst getUID = (prefix) => {\n  do {\n    prefix += Math.floor(Math.random() * MAX_UID);\n  } while (document.getElementById(prefix));\n\n  return prefix;\n};\n\nconst getSelector = (element) => {\n  let selector = element.getAttribute('data-mdb-target');\n\n  if (!selector || selector === '#') {\n    const hrefAttr = element.getAttribute('href');\n\n    selector = hrefAttr && hrefAttr !== '#' ? hrefAttr.trim() : null;\n  }\n\n  return selector;\n};\n\nconst getSelectorFromElement = (element) => {\n  const selector = getSelector(element);\n\n  if (selector) {\n    return document.querySelector(selector) ? selector : null;\n  }\n\n  return null;\n};\n\nconst getElementFromSelector = (element) => {\n  const selector = getSelector(element);\n\n  return selector ? document.querySelector(selector) : null;\n};\n\nconst getTransitionDurationFromElement = (element) => {\n  if (!element) {\n    return 0;\n  }\n\n  // Get transition-duration of the element\n  let { transitionDuration, transitionDelay } = window.getComputedStyle(element);\n\n  const floatTransitionDuration = Number.parseFloat(transitionDuration);\n  const floatTransitionDelay = Number.parseFloat(transitionDelay);\n\n  // Return 0 if element or transition duration is not found\n  if (!floatTransitionDuration && !floatTransitionDelay) {\n    return 0;\n  }\n\n  // If multiple durations are defined, take the first\n  transitionDuration = transitionDuration.split(',')[0];\n  transitionDelay = transitionDelay.split(',')[0];\n\n  return (\n    (Number.parseFloat(transitionDuration) + Number.parseFloat(transitionDelay)) *\n    MILLISECONDS_MULTIPLIER\n  );\n};\n\nconst triggerTransitionEnd = (element) => {\n  element.dispatchEvent(new Event(TRANSITION_END));\n};\n\nconst isElement = (obj) => (obj[0] || obj).nodeType;\n\nconst emulateTransitionEnd = (element, duration) => {\n  let called = false;\n  const durationPadding = 5;\n  const emulatedDuration = duration + durationPadding;\n\n  function listener() {\n    called = true;\n    element.removeEventListener(TRANSITION_END, listener);\n  }\n\n  element.addEventListener(TRANSITION_END, listener);\n  setTimeout(() => {\n    if (!called) {\n      triggerTransitionEnd(element);\n    }\n  }, emulatedDuration);\n};\n\nconst typeCheckConfig = (componentName, config, configTypes) => {\n  Object.keys(configTypes).forEach((property) => {\n    const expectedTypes = configTypes[property];\n    const value = config[property];\n    const valueType = value && isElement(value) ? 'element' : toType(value);\n\n    if (!new RegExp(expectedTypes).test(valueType)) {\n      throw new Error(\n        `${componentName.toUpperCase()}: ` +\n          `Option \"${property}\" provided type \"${valueType}\" ` +\n          `but expected type \"${expectedTypes}\".`\n      );\n    }\n  });\n};\n\nconst isVisible = (element) => {\n  if (!element) {\n    return false;\n  }\n\n  if (element.style && element.parentNode && element.parentNode.style) {\n    const elementStyle = getComputedStyle(element);\n    const parentNodeStyle = getComputedStyle(element.parentNode);\n\n    return (\n      elementStyle.display !== 'none' &&\n      parentNodeStyle.display !== 'none' &&\n      elementStyle.visibility !== 'hidden'\n    );\n  }\n\n  return false;\n};\n\nconst findShadowRoot = (element) => {\n  if (!document.documentElement.attachShadow) {\n    return null;\n  }\n\n  // Can find the shadow root otherwise it'll return the document\n  if (typeof element.getRootNode === 'function') {\n    const root = element.getRootNode();\n    return root instanceof ShadowRoot ? root : null;\n  }\n\n  if (element instanceof ShadowRoot) {\n    return element;\n  }\n\n  // when we don't find a shadow root\n  if (!element.parentNode) {\n    return null;\n  }\n\n  return findShadowRoot(element.parentNode);\n};\n\nconst noop = () => function () {};\n\nconst reflow = (element) => element.offsetHeight;\n\nconst getjQuery = () => {\n  const { jQuery } = window;\n\n  if (jQuery && !document.body.hasAttribute('data-mdb-no-jquery')) {\n    return jQuery;\n  }\n\n  return null;\n};\n\nconst onDOMContentLoaded = (callback) => {\n  if (document.readyState === 'loading') {\n    document.addEventListener('DOMContentLoaded', callback);\n  } else {\n    callback();\n  }\n};\n\nconst isRTL = document.documentElement.dir === 'rtl';\n\nconst array = (collection) => {\n  return Array.from(collection);\n};\n\nconst element = (tag) => {\n  return document.createElement(tag);\n};\n\nexport {\n  getjQuery,\n  TRANSITION_END,\n  getUID,\n  getSelectorFromElement,\n  getElementFromSelector,\n  getTransitionDurationFromElement,\n  triggerTransitionEnd,\n  isElement,\n  emulateTransitionEnd,\n  typeCheckConfig,\n  isVisible,\n  findShadowRoot,\n  noop,\n  reflow,\n  array,\n  element,\n  onDOMContentLoaded,\n  isRTL,\n};\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha1): dom/data.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)\n * --------------------------------------------------------------------------\n */\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst mapData = (() => {\n  const storeData = {};\n  let id = 1;\n  return {\n    set(element, key, data) {\n      if (typeof element[key] === 'undefined') {\n        element[key] = {\n          key,\n          id,\n        };\n        id++;\n      }\n\n      storeData[element[key].id] = data;\n    },\n    get(element, key) {\n      if (!element || typeof element[key] === 'undefined') {\n        return null;\n      }\n\n      const keyProperties = element[key];\n      if (keyProperties.key === key) {\n        return storeData[keyProperties.id];\n      }\n\n      return null;\n    },\n    delete(element, key) {\n      if (typeof element[key] === 'undefined') {\n        return;\n      }\n\n      const keyProperties = element[key];\n      if (keyProperties.key === key) {\n        delete storeData[keyProperties.id];\n        delete element[key];\n      }\n    },\n  };\n})();\n\nconst Data = {\n  setData(instance, key, data) {\n    mapData.set(instance, key, data);\n  },\n  getData(instance, key) {\n    return mapData.get(instance, key);\n  },\n  removeData(instance, key) {\n    mapData.delete(instance, key);\n  },\n};\n\nexport default Data;\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta1): dom/manipulator.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nfunction normalizeData(val) {\n  if (val === 'true') {\n    return true;\n  }\n\n  if (val === 'false') {\n    return false;\n  }\n\n  if (val === Number(val).toString()) {\n    return Number(val);\n  }\n\n  if (val === '' || val === 'null') {\n    return null;\n  }\n\n  return val;\n}\n\nfunction normalizeDataKey(key) {\n  return key.replace(/[A-Z]/g, (chr) => `-${chr.toLowerCase()}`);\n}\n\nconst Manipulator = {\n  setDataAttribute(element, key, value) {\n    element.setAttribute(`data-mdb-${normalizeDataKey(key)}`, value);\n  },\n\n  removeDataAttribute(element, key) {\n    element.removeAttribute(`data-mdb-${normalizeDataKey(key)}`);\n  },\n\n  getDataAttributes(element) {\n    if (!element) {\n      return {};\n    }\n\n    const attributes = {\n      ...element.dataset,\n    };\n\n    Object.keys(attributes)\n      .filter((key) => key.startsWith('mdb'))\n      .forEach((key) => {\n        let pureKey = key.replace(/^mdb/, '');\n        pureKey = pureKey.charAt(0).toLowerCase() + pureKey.slice(1, pureKey.length);\n        attributes[pureKey] = normalizeData(attributes[key]);\n      });\n\n    return attributes;\n  },\n\n  getDataAttribute(element, key) {\n    return normalizeData(element.getAttribute(`data-mdb-${normalizeDataKey(key)}`));\n  },\n\n  offset(element) {\n    const rect = element.getBoundingClientRect();\n\n    return {\n      top: rect.top + document.body.scrollTop,\n      left: rect.left + document.body.scrollLeft,\n    };\n  },\n\n  position(element) {\n    return {\n      top: element.offsetTop,\n      left: element.offsetLeft,\n    };\n  },\n\n  style(element, style) {\n    Object.assign(element.style, style);\n  },\n\n  toggleClass(element, className) {\n    if (!element) {\n      return;\n    }\n\n    if (element.classList.contains(className)) {\n      element.classList.remove(className);\n    } else {\n      element.classList.add(className);\n    }\n  },\n\n  addClass(element, className) {\n    if (element.classList.contains(className)) return;\n    element.classList.add(className);\n  },\n\n  addStyle(element, style) {\n    Object.keys(style).forEach((property) => {\n      element.style[property] = style[property];\n    });\n  },\n\n  removeClass(element, className) {\n    if (!element.classList.contains(className)) return;\n    element.classList.remove(className);\n  },\n\n  hasClass(element, className) {\n    return element.classList.contains(className);\n  },\n};\n\nexport default Manipulator;\n", "/* istanbul ignore file */\n\n/**\n * --------------------------------------------------------------------------\n * <PERSON><PERSON><PERSON> (v5.0.0-alpha1): dom/polyfill.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { getUID } from '../util/index';\n\nlet findElements = Element.prototype.querySelectorAll;\nlet findElement = Element.prototype.querySelector;\n\n// MSEdge resets defaultPrevented flag upon dispatchEvent call if at least one listener is attached\nconst defaultPreventedPreservedOnDispatch = (() => {\n  const e = new CustomEvent('Bootstrap', {\n    cancelable: true,\n  });\n\n  const element = document.createElement('div');\n  element.addEventListener('Bootstrap', () => null);\n\n  e.preventDefault();\n  element.dispatchEvent(e);\n  return e.defaultPrevented;\n})();\n\nconst scopeSelectorRegex = /:scope\\b/;\nconst supportScopeQuery = (() => {\n  const element = document.createElement('div');\n\n  try {\n    element.querySelectorAll(':scope *');\n  } catch (_) {\n    return false;\n  }\n\n  return true;\n})();\n\nif (!supportScopeQuery) {\n  findElements = function (selector) {\n    if (!scopeSelectorRegex.test(selector)) {\n      return this.querySelectorAll(selector);\n    }\n\n    const hasId = Boolean(this.id);\n\n    if (!hasId) {\n      this.id = getUID('scope');\n    }\n\n    let nodeList = null;\n    try {\n      selector = selector.replace(scopeSelectorRegex, `#${this.id}`);\n      nodeList = this.querySelectorAll(selector);\n    } finally {\n      if (!hasId) {\n        this.removeAttribute('id');\n      }\n    }\n\n    return nodeList;\n  };\n\n  findElement = function (selector) {\n    if (!scopeSelectorRegex.test(selector)) {\n      return this.querySelector(selector);\n    }\n\n    const matches = find.call(this, selector);\n\n    if (typeof matches[0] !== 'undefined') {\n      return matches[0];\n    }\n\n    return null;\n  };\n}\n\nconst find = findElements;\nconst findOne = findElement;\n\nexport { find, findOne, defaultPreventedPreservedOnDispatch };\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha1): dom/event-handler.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { defaultPreventedPreservedOnDispatch } from './polyfill';\nimport { getjQuery } from '../util/index';\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst $ = getjQuery();\nconst namespaceRegex = /[^.]*(?=\\..*)\\.|.*/;\nconst stripNameRegex = /\\..*/;\nconst stripUidRegex = /::\\d+$/;\nconst eventRegistry = {}; // Events storage\nlet uidEvent = 1;\nconst customEvents = {\n  mouseenter: 'mouseover',\n  mouseleave: 'mouseout',\n};\nconst nativeEvents = [\n  'click',\n  'dblclick',\n  'mouseup',\n  'mousedown',\n  'contextmenu',\n  'mousewheel',\n  'DOMMouseScroll',\n  'mouseover',\n  'mouseout',\n  'mousemove',\n  'selectstart',\n  'selectend',\n  'keydown',\n  'keypress',\n  'keyup',\n  'orientationchange',\n  'touchstart',\n  'touchmove',\n  'touchend',\n  'touchcancel',\n  'pointerdown',\n  'pointermove',\n  'pointerup',\n  'pointerleave',\n  'pointercancel',\n  'gesturestart',\n  'gesturechange',\n  'gestureend',\n  'focus',\n  'blur',\n  'change',\n  'reset',\n  'select',\n  'submit',\n  'focusin',\n  'focusout',\n  'load',\n  'unload',\n  'beforeunload',\n  'resize',\n  'move',\n  'DOMContentLoaded',\n  'readystatechange',\n  'error',\n  'abort',\n  'scroll',\n];\n\n/**\n * ------------------------------------------------------------------------\n * Private methods\n * ------------------------------------------------------------------------\n */\n\nfunction getUidEvent(element, uid) {\n  return (uid && `${uid}::${uidEvent++}`) || element.uidEvent || uidEvent++;\n}\n\nfunction getEvent(element) {\n  const uid = getUidEvent(element);\n\n  element.uidEvent = uid;\n  eventRegistry[uid] = eventRegistry[uid] || {};\n\n  return eventRegistry[uid];\n}\n\nfunction bootstrapHandler(element, fn) {\n  return function handler(event) {\n    if (handler.oneOff) {\n      EventHandler.off(element, event.type, fn);\n    }\n\n    return fn.apply(element, [event]);\n  };\n}\n\nfunction bootstrapDelegationHandler(element, selector, fn) {\n  return function handler(event) {\n    const domElements = element.querySelectorAll(selector);\n\n    for (let { target } = event; target && target !== this; target = target.parentNode) {\n      for (let i = domElements.length; i--; '') {\n        if (domElements[i] === target) {\n          if (handler.oneOff) {\n            EventHandler.off(element, event.type, fn);\n          }\n\n          return fn.apply(target, [event]);\n        }\n      }\n    }\n\n    // To please ESLint\n    return null;\n  };\n}\n\nfunction findHandler(events, handler, delegationSelector = null) {\n  const uidEventList = Object.keys(events);\n\n  for (let i = 0, len = uidEventList.length; i < len; i++) {\n    const event = events[uidEventList[i]];\n\n    if (event.originalHandler === handler && event.delegationSelector === delegationSelector) {\n      return event;\n    }\n  }\n\n  return null;\n}\n\nfunction normalizeParams(originalTypeEvent, handler, delegationFn) {\n  const delegation = typeof handler === 'string';\n  const originalHandler = delegation ? delegationFn : handler;\n\n  // allow to get the native events from namespaced events ('click.bs.button' --> 'click')\n  let typeEvent = originalTypeEvent.replace(stripNameRegex, '');\n  const custom = customEvents[typeEvent];\n\n  if (custom) {\n    typeEvent = custom;\n  }\n\n  const isNative = nativeEvents.indexOf(typeEvent) > -1;\n\n  if (!isNative) {\n    typeEvent = originalTypeEvent;\n  }\n\n  return [delegation, originalHandler, typeEvent];\n}\n\nfunction addHandler(element, originalTypeEvent, handler, delegationFn, oneOff) {\n  if (typeof originalTypeEvent !== 'string' || !element) {\n    return;\n  }\n\n  if (!handler) {\n    handler = delegationFn;\n    delegationFn = null;\n  }\n\n  const [delegation, originalHandler, typeEvent] = normalizeParams(\n    originalTypeEvent,\n    handler,\n    delegationFn\n  );\n  const events = getEvent(element);\n  const handlers = events[typeEvent] || (events[typeEvent] = {});\n  const previousFn = findHandler(handlers, originalHandler, delegation ? handler : null);\n\n  if (previousFn) {\n    previousFn.oneOff = previousFn.oneOff && oneOff;\n\n    return;\n  }\n\n  const uid = getUidEvent(originalHandler, originalTypeEvent.replace(namespaceRegex, ''));\n  const fn = delegation\n    ? bootstrapDelegationHandler(element, handler, delegationFn)\n    : bootstrapHandler(element, handler);\n\n  fn.delegationSelector = delegation ? handler : null;\n  fn.originalHandler = originalHandler;\n  fn.oneOff = oneOff;\n  fn.uidEvent = uid;\n  handlers[uid] = fn;\n\n  element.addEventListener(typeEvent, fn, delegation);\n}\n\nfunction removeHandler(element, events, typeEvent, handler, delegationSelector) {\n  const fn = findHandler(events[typeEvent], handler, delegationSelector);\n\n  if (!fn) {\n    return;\n  }\n\n  element.removeEventListener(typeEvent, fn, Boolean(delegationSelector));\n  delete events[typeEvent][fn.uidEvent];\n}\n\nfunction removeNamespacedHandlers(element, events, typeEvent, namespace) {\n  const storeElementEvent = events[typeEvent] || {};\n\n  Object.keys(storeElementEvent).forEach((handlerKey) => {\n    if (handlerKey.indexOf(namespace) > -1) {\n      const event = storeElementEvent[handlerKey];\n\n      removeHandler(element, events, typeEvent, event.originalHandler, event.delegationSelector);\n    }\n  });\n}\n\nconst EventHandler = {\n  on(element, event, handler, delegationFn) {\n    addHandler(element, event, handler, delegationFn, false);\n  },\n\n  one(element, event, handler, delegationFn) {\n    addHandler(element, event, handler, delegationFn, true);\n  },\n\n  off(element, originalTypeEvent, handler, delegationFn) {\n    if (typeof originalTypeEvent !== 'string' || !element) {\n      return;\n    }\n\n    const [delegation, originalHandler, typeEvent] = normalizeParams(\n      originalTypeEvent,\n      handler,\n      delegationFn\n    );\n    const inNamespace = typeEvent !== originalTypeEvent;\n    const events = getEvent(element);\n    const isNamespace = originalTypeEvent.charAt(0) === '.';\n\n    if (typeof originalHandler !== 'undefined') {\n      // Simplest case: handler is passed, remove that listener ONLY.\n      if (!events || !events[typeEvent]) {\n        return;\n      }\n\n      removeHandler(element, events, typeEvent, originalHandler, delegation ? handler : null);\n      return;\n    }\n\n    if (isNamespace) {\n      Object.keys(events).forEach((elementEvent) => {\n        removeNamespacedHandlers(element, events, elementEvent, originalTypeEvent.slice(1));\n      });\n    }\n\n    const storeElementEvent = events[typeEvent] || {};\n    Object.keys(storeElementEvent).forEach((keyHandlers) => {\n      const handlerKey = keyHandlers.replace(stripUidRegex, '');\n\n      if (!inNamespace || originalTypeEvent.indexOf(handlerKey) > -1) {\n        const event = storeElementEvent[keyHandlers];\n\n        removeHandler(element, events, typeEvent, event.originalHandler, event.delegationSelector);\n      }\n    });\n  },\n\n  trigger(element, event, args) {\n    if (typeof event !== 'string' || !element) {\n      return null;\n    }\n\n    const typeEvent = event.replace(stripNameRegex, '');\n    const inNamespace = event !== typeEvent;\n    const isNative = nativeEvents.indexOf(typeEvent) > -1;\n\n    let jQueryEvent;\n    let bubbles = true;\n    let nativeDispatch = true;\n    let defaultPrevented = false;\n    let evt = null;\n\n    if (inNamespace && $) {\n      jQueryEvent = $.Event(event, args);\n\n      $(element).trigger(jQueryEvent);\n      bubbles = !jQueryEvent.isPropagationStopped();\n      nativeDispatch = !jQueryEvent.isImmediatePropagationStopped();\n      defaultPrevented = jQueryEvent.isDefaultPrevented();\n    }\n\n    if (isNative) {\n      evt = document.createEvent('HTMLEvents');\n      evt.initEvent(typeEvent, bubbles, true);\n    } else {\n      evt = new CustomEvent(event, {\n        bubbles,\n        cancelable: true,\n      });\n    }\n\n    // merge custom informations in our event\n    if (typeof args !== 'undefined') {\n      Object.keys(args).forEach((key) => {\n        Object.defineProperty(evt, key, {\n          get() {\n            return args[key];\n          },\n        });\n      });\n    }\n\n    if (defaultPrevented) {\n      evt.preventDefault();\n\n      if (!defaultPreventedPreservedOnDispatch) {\n        Object.defineProperty(evt, 'defaultPrevented', {\n          get: () => true,\n        });\n      }\n    }\n\n    if (nativeDispatch) {\n      element.dispatchEvent(evt);\n    }\n\n    if (evt.defaultPrevented && typeof jQueryEvent !== 'undefined') {\n      jQueryEvent.preventDefault();\n    }\n\n    return evt;\n  },\n};\n\nexport const EventHandlerMulti = {\n  on(element, eventsName, handler, delegationFn) {\n    const events = eventsName.split(' ');\n\n    for (let i = 0; i < events.length; i++) {\n      EventHandler.on(element, events[i], handler, delegationFn);\n    }\n  },\n  off(element, originalTypeEvent, handler, delegationFn) {\n    const events = originalTypeEvent.split(' ');\n\n    for (let i = 0; i < events.length; i++) {\n      EventHandler.off(element, events[i], handler, delegationFn);\n    }\n  },\n};\n\nexport default EventHandler;\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha1): dom/selector-engine.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { find as findFn, findOne } from './polyfill';\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NODE_TEXT = 3;\n\nconst SelectorEngine = {\n  closest(element, selector) {\n    return element.closest(selector);\n  },\n\n  matches(element, selector) {\n    return element.matches(selector);\n  },\n\n  find(selector, element = document.documentElement) {\n    return [].concat(...findFn.call(element, selector));\n  },\n\n  findOne(selector, element = document.documentElement) {\n    return findOne.call(element, selector);\n  },\n\n  children(element, selector) {\n    const children = [].concat(...element.children);\n\n    return children.filter((child) => child.matches(selector));\n  },\n\n  parents(element, selector) {\n    const parents = [];\n\n    let ancestor = element.parentNode;\n\n    while (ancestor && ancestor.nodeType === Node.ELEMENT_NODE && ancestor.nodeType !== NODE_TEXT) {\n      if (this.matches(ancestor, selector)) {\n        parents.push(ancestor);\n      }\n\n      ancestor = ancestor.parentNode;\n    }\n\n    return parents;\n  },\n\n  prev(element, selector) {\n    let previous = element.previousElementSibling;\n\n    while (previous) {\n      if (previous.matches(selector)) {\n        return [previous];\n      }\n\n      previous = previous.previousElementSibling;\n    }\n\n    return [];\n  },\n\n  next(element, selector) {\n    let next = element.nextElementSibling;\n\n    while (next) {\n      if (this.matches(next, selector)) {\n        return [next];\n      }\n\n      next = next.nextElementSibling;\n    }\n\n    return [];\n  },\n};\n\nexport default SelectorEngine;\n", "import { typeCheckConfig, getjQ<PERSON>y, onDOMContentLoaded } from './mdb/util/index';\nimport Data from './mdb/dom/data';\nimport Manipulator from './mdb/dom/manipulator';\nimport EventHandler from './mdb/dom/event-handler';\nimport SelectorEngine from './mdb/dom/selector-engine';\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'countdown';\nconst DATA_KEY = `mdb.${NAME}`;\n\nconst CLASS_COUNTDOWN_SEPARATOR = `${NAME}-unit-separator`;\n\nconst EVENT_START = `start.${DATA_KEY}`;\nconst EVENT_END = `end.${DATA_KEY}`;\n\nconst SELECTOR_DATA_INIT = '[data-mdb-countdown-init]';\nconst SELECTOR_COUNTDOWN_UNIT = `.${NAME}-unit`;\n\nconst DEFAULT_OPTIONS = {\n  countdown: '',\n  countdownSeparator: '',\n  countdownPosition: 'horizontal',\n  countdownLabelPosition: 'vertical',\n  countdownTextStyle: '',\n  countdownLabelStyle: '',\n  countdownTextSize: '',\n  countdownInterval: 0,\n};\nconst OPTIONS_TYPE = {\n  countdown: 'string',\n  countdownSeparator: '',\n  countdownPosition: 'string',\n  countdownLabelPosition: 'string',\n  countdownTextStyle: 'string',\n  countdownLabelStyle: 'string',\n  countdownTextSize: 'string',\n  countdownInterval: 'number',\n};\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Countdown {\n  constructor(element, options = {}) {\n    this._element = element;\n    this._options = this._getConfig(options);\n\n    this._countdownDate = null;\n\n    this._countingInterval = null;\n\n    this._dateDistance = null;\n    this._previousDistance = 0;\n\n    this._isCounting = false;\n\n    if (this._element) {\n      Data.setData(element, DATA_KEY, this);\n      this._init();\n    }\n  }\n\n  // Public\n  dispose() {\n    Data.removeData(this._element, DATA_KEY);\n\n    this._stopCounting();\n\n    this._dateDistance = null;\n\n    this._element.querySelectorAll('span').forEach((span) => {\n      span.innerHTML = '';\n    });\n\n    this._element = null;\n  }\n\n  stop() {\n    clearInterval(this._countingInterval);\n\n    this._countingInterval = null;\n    this._isCounting = false;\n    this._previousDistance = 0;\n\n    EventHandler.trigger(this._element, EVENT_END);\n  }\n\n  start() {\n    EventHandler.trigger(this._element, EVENT_START);\n\n    this._dateDistance = this._checkDateDistance();\n\n    this._isCounting = true;\n\n    this._startInterval();\n  }\n\n  setCountdownDate(date) {\n    if (!this._isValidDate(this._options.countdown)) {\n      this._element.innerHTML = `\n        <p class=\"note note-danger\">\n          <strong>Invalid Date Format: ${this._options.countdown}</strong>. Please provide\n          <a href='https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Date/parse#ECMAScript_5_ISO-8601_format_support' target=\"_blank\">valid date format</a>\n        </p>\n      `;\n\n      return;\n    }\n\n    this._isCounting = false;\n    this._previousDistance = 0;\n\n    this._countdownDate = new Date(date).getTime();\n    if (!this._isCounting) {\n      this.start();\n    }\n  }\n\n  // Private\n  _getConfig(options) {\n    const attributes = Manipulator.getDataAttributes(this._element);\n\n    const config = {\n      ...DEFAULT_OPTIONS,\n      ...attributes,\n      ...options,\n    };\n\n    typeCheckConfig(NAME, config, OPTIONS_TYPE);\n\n    return config;\n  }\n\n  _setStyles() {\n    const units = SelectorEngine.find(SELECTOR_COUNTDOWN_UNIT, this._element);\n\n    units.forEach((unit) => {\n      Manipulator.addClass(unit, `countdown-unit-${this._options.countdownLabelPosition}`);\n    });\n    Manipulator.addClass(this._element, 'countdown');\n    Manipulator.addClass(this._element, `countdown-${this._options.countdownPosition}`);\n\n    if (\n      this._options.countdownSeparator !== '' &&\n      !this._options.countdownPosition.includes('vertical')\n    ) {\n      for (let i = 0; i < units.length - 1; i++) {\n        units[i].insertAdjacentHTML(\n          'afterend',\n          `<span class=${CLASS_COUNTDOWN_SEPARATOR}>${this._options.countdownSeparator}</span>`\n        );\n      }\n    }\n  }\n\n  _handleInterval() {\n    const firstDate = this._options.countdown;\n    const step = this._options.countdownInterval * 1000;\n\n    EventHandler.on(this._element, EVENT_END, () => {\n      const dateNow = new Date(Date.now()).getTime();\n      const loopRange = new Date(firstDate).getTime() + step;\n\n      let timeFromNow = 0;\n\n      if (dateNow > loopRange) {\n        const timeDifference = dateNow - loopRange;\n        let jump;\n\n        if (timeDifference > step) {\n          const passedStep = timeDifference % step;\n          jump = step - passedStep;\n        } else {\n          jump = step - timeDifference;\n        }\n\n        timeFromNow = dateNow + jump;\n      } else {\n        timeFromNow = loopRange;\n      }\n\n      this.setCountdownDate(timeFromNow);\n    });\n  }\n\n  _init() {\n    this._setCountDownDate(this._options.countdown);\n\n    this._startCounting();\n    if (this._options.countdownInterval > 0) {\n      this._handleInterval();\n    }\n  }\n\n  _setCountDownDate(date) {\n    if (!this._isValidDate(this._options.countdown)) {\n      this._element.innerHTML = `\n      <p class=\"note note-danger\">\n      <strong>Invalid Date Format: ${this._options.countdown}</strong>. Please provide\n      <a href='https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Date/parse#ECMAScript_5_ISO-8601_format_support' target=\"_blank\">valid date format</a>\n      </p>\n      `;\n\n      return;\n    }\n\n    this._countdownDate = new Date(date).getTime();\n  }\n\n  _startCounting() {\n    EventHandler.trigger(this._element, EVENT_START);\n\n    this._isCounting = true;\n\n    // first draw of timer counter outside interval - allows to update only those values that's changed\n    this._createCounter();\n\n    this._setStyles();\n\n    this._startInterval();\n  }\n\n  _startInterval() {\n    this._updateCounter(this._dateDistance);\n\n    this._countingInterval = setInterval(() => {\n      this._dateDistance = this._checkDateDistance();\n\n      if (!this._isCounting || !this._dateDistance) {\n        this._stopCounting();\n\n        return;\n      }\n\n      this._updateCounter(this._dateDistance);\n    }, 1000);\n  }\n\n  _isValidDate(date) {\n    date = new Date(date);\n    // eslint-disable-next-line no-restricted-globals\n    return date instanceof Date && !isNaN(date);\n  }\n\n  _checkDateDistance() {\n    const actualDate = new Date().getTime();\n\n    let dateDistance = this._countdownDate - actualDate;\n\n    if (dateDistance < 0) {\n      return false;\n    }\n\n    if (this._previousDistance !== 0 && this._previousDistance - dateDistance > 1000) {\n      dateDistance = this._previousDistance - 1000;\n    }\n\n    this._previousDistance = dateDistance;\n    return dateDistance;\n  }\n\n  _createCounter() {\n    const dateDistance = this._checkDateDistance();\n\n    const timeUnits = this._calculateTime(dateDistance);\n\n    Object.entries(timeUnits).forEach(([unit, value]) => {\n      const unitElement = SelectorEngine.findOne(`.countdown-${unit}`, this._element);\n\n      if (unitElement) {\n        const template = this._generateUnitTemplate(unitElement, unit, value);\n        unitElement.innerHTML = template;\n        Manipulator.setDataAttribute(unitElement, unit, value);\n        Manipulator.removeDataAttribute(unitElement, 'countdownLabel');\n      }\n    });\n  }\n\n  _updateCounter(dateDistance) {\n    const timeUnits = this._calculateTime(dateDistance);\n\n    Object.entries(timeUnits).forEach(([unit, value]) => {\n      const unitElement = SelectorEngine.findOne(`.countdown-${unit}`, this._element);\n\n      if (unitElement) {\n        const dataValue = SelectorEngine.findOne('[data-mdb-countdown-value]', unitElement);\n\n        if (dataValue.textContent === value) {\n          return;\n        }\n\n        dataValue.textContent = value;\n        Manipulator.setDataAttribute(unitElement, unit, value);\n      }\n    });\n  }\n\n  _calculateTime(distance) {\n    const days = this._formatTime(Math.floor(distance / (1000 * 60 * 60 * 24)));\n    const hours = this._formatTime(\n      Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))\n    );\n    const minutes = this._formatTime(Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60)));\n    const seconds = this._formatTime(Math.floor((distance % (1000 * 60)) / 1000));\n\n    return { days, hours, minutes, seconds };\n  }\n\n  _formatTime(time) {\n    return time > 9 ? `${time}` : `0${time}`;\n  }\n\n  _generateUnitTemplate(element, name, value) {\n    const labelName = Manipulator.getDataAttribute(element, 'countdownLabel');\n    const ariaName = `label-${name}`;\n    const labbeledBy = `aria-labelledby=\"${ariaName}\"`;\n\n    const valueClass =\n      this._options.countdownTextStyle !== '' ? `class=\"${this._options.countdownTextStyle}\"` : '';\n\n    let labelTextSize = '';\n    let valueTextSize = '';\n\n    if (this._options.countdownTextSize !== '') {\n      const textSize = this._options.countdownTextSize.match(/\\d+/)[0];\n      const textSizeUnit = this._options.countdownTextSize.split(textSize)[1];\n\n      labelTextSize = `style=\"font-size: ${textSize / 4}${textSizeUnit}\"`;\n      valueTextSize = `style=\"font-size: ${this._options.countdownTextSize}\"`;\n    }\n\n    const template = `\n      <span ${\n        labelName ? labbeledBy : ''\n      } data-mdb-countdown-value ${valueClass} ${valueTextSize}>${value}</span>\n      ${\n        labelName\n          ? `<span name=\"${ariaName}\" data-mdb-countdown-label class=\"${this._options.countdownLabelStyle}\" ${labelTextSize}>${labelName}</span>`\n          : ''\n      }\n    `;\n\n    return template;\n  }\n\n  _stopCounting() {\n    clearInterval(this._countingInterval);\n\n    this._countingInterval = null;\n    this._isCounting = false;\n    this._previousDistance = 0;\n\n    EventHandler.trigger(this._element, EVENT_END);\n  }\n\n  // Static\n  static get NAME() {\n    return NAME;\n  }\n\n  static jQueryInterface(config, options) {\n    return this.each(function () {\n      let data = Data.getData(this, DATA_KEY);\n      const _config = typeof config === 'object' && config;\n\n      if (!data && /dispose/.test(config)) {\n        return;\n      }\n\n      if (!data) {\n        data = new Countdown(this, _config);\n      }\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`);\n        }\n\n        data[config](options);\n      }\n    });\n  }\n\n  static getInstance(element) {\n    return Data.getData(element, DATA_KEY);\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation - auto initialization\n * ------------------------------------------------------------------------\n */\n\nSelectorEngine.find(SELECTOR_DATA_INIT).forEach((el) => {\n  let instance = Countdown.getInstance(el);\n  if (!instance) {\n    instance = new Countdown(el);\n  }\n\n  return instance;\n});\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Countdown to jQuery only if jQuery is present\n */\n\nonDOMContentLoaded(() => {\n  const $ = getjQuery();\n\n  if ($) {\n    const JQUERY_NO_CONFLICT = $.fn[NAME];\n    $.fn[NAME] = Countdown.jQueryInterface;\n    $.fn[NAME].Constructor = Countdown;\n    $.fn[NAME].noConflict = () => {\n      $.fn[NAME] = JQUERY_NO_CONFLICT;\n      return Countdown.jQueryInterface;\n    };\n  }\n});\nexport default Countdown;\n"], "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "j<PERSON><PERSON><PERSON>", "window", "document", "body", "hasAttribute", "documentElement", "dir", "mapData", "storeData", "id", "set", "element", "key", "data", "get", "keyProperties", "Data", "setData", "instance", "getData", "removeData", "delete", "normalizeData", "val", "Number", "toString", "normalizeDataKey", "replace", "chr", "toLowerCase", "Manipulator", "setDataAttribute", "value", "setAttribute", "removeDataAttribute", "removeAttribute", "getDataAttributes", "attributes", "dataset", "Object", "keys", "filter", "startsWith", "for<PERSON>ach", "pureKey", "char<PERSON>t", "slice", "length", "getDataAttribute", "getAttribute", "offset", "rect", "getBoundingClientRect", "top", "scrollTop", "left", "scrollLeft", "position", "offsetTop", "offsetLeft", "style", "assign", "toggleClass", "className", "classList", "contains", "remove", "add", "addClass", "addStyle", "property", "removeClass", "hasClass", "findElements", "Element", "prototype", "querySelectorAll", "findElement", "querySelector", "defaultPreventedPreservedOnDispatch", "e", "CustomEvent", "cancelable", "createElement", "addEventListener", "preventDefault", "dispatchEvent", "defaultPrevented", "scopeSelectorRegex", "_", "selector", "test", "this", "hasId", "Boolean", "prefix", "Math", "floor", "random", "getElementById", "getUID", "nodeList", "matches", "find", "call", "findOne", "$", "namespaceRegex", "stripNameRegex", "stripUidRegex", "eventRegistry", "uidEvent", "customEvents", "mouseenter", "mouseleave", "nativeEvents", "getUidEvent", "uid", "getEvent", "<PERSON><PERSON><PERSON><PERSON>", "events", "handler", "delegationSelector", "uidEventList", "i", "len", "event", "<PERSON><PERSON><PERSON><PERSON>", "normalizeParams", "originalTypeEvent", "delegationFn", "delegation", "typeEvent", "custom", "indexOf", "add<PERSON><PERSON><PERSON>", "oneOff", "handlers", "previousFn", "fn", "dom<PERSON><PERSON>s", "target", "parentNode", "EventHandler", "off", "type", "apply", "bootstrapDelegationHandler", "bootstrapHandler", "<PERSON><PERSON><PERSON><PERSON>", "removeEventListener", "on", "one", "inNamespace", "isNamespace", "elementEvent", "namespace", "storeElementEvent", "handler<PERSON><PERSON>", "removeNamespacedHandlers", "keyHandlers", "trigger", "args", "isNative", "jQueryEvent", "bubbles", "nativeDispatch", "evt", "Event", "isPropagationStopped", "isImmediatePropagationStopped", "isDefaultPrevented", "createEvent", "initEvent", "defineProperty", "SelectorEngine", "closest", "concat", "findFn", "children", "child", "parents", "ancestor", "nodeType", "Node", "ELEMENT_NODE", "push", "prev", "previous", "previousElementSibling", "next", "nextElement<PERSON><PERSON>ling", "NAME", "DATA_KEY", "CLASS_COUNTDOWN_SEPARATOR", "EVENT_START", "EVENT_END", "SELECTOR_COUNTDOWN_UNIT", "DEFAULT_OPTIONS", "countdown", "countdownSeparator", "countdownPosition", "countdownLabelPosition", "countdownTextStyle", "countdownLabelStyle", "countdownTextSize", "countdownInterval", "OPTIONS_TYPE", "Countdown", "constructor", "options", "_element", "_options", "_getConfig", "_countdownDate", "_countingInterval", "_dateDistance", "_previousDistance", "_isCounting", "_init", "dispose", "_stopCounting", "span", "innerHTML", "stop", "clearInterval", "start", "_checkDateDistance", "_startInterval", "setCountdownDate", "date", "_isValidDate", "Date", "getTime", "config", "componentName", "configTypes", "expectedTypes", "valueType", "obj", "match", "toType", "RegExp", "Error", "toUpperCase", "typeCheckConfig", "_setStyles", "units", "unit", "includes", "insertAdjacentHTML", "_handleInterval", "firstDate", "step", "dateNow", "now", "loopRange", "timeFromNow", "timeDifference", "jump", "_setCountDownDate", "_startCounting", "_createCounter", "_updateCounter", "setInterval", "isNaN", "actualDate", "dateDistance", "timeUnits", "_calculateTime", "entries", "unitElement", "template", "_generateUnitTemplate", "dataValue", "textContent", "distance", "days", "_formatTime", "hours", "minutes", "seconds", "time", "name", "labelName", "ariaName", "labbeledBy", "valueClass", "labelTextSize", "valueTextSize", "textSize", "split", "jQueryInterface", "each", "_config", "TypeError", "getInstance", "callback", "el", "JQUERY_NO_CONFLICT", "<PERSON><PERSON><PERSON><PERSON>", "noConflict", "readyState"], "mappings": "0OAOA,MA0KMA,EAAY,KACV,MAAAC,OAAEA,GAAWC,OAEnB,OAAID,IAAWE,SAASC,KAAKC,aAAa,sBACjCJ,EAGF,IAAA,EAWKE,SAASG,gBAAgBC,ICtLvC,MAAMC,QACJ,MAAMC,EAAY,CAAA,EAClB,IAAIC,EAAK,EACF,MAAA,CACL,GAAAC,CAAIC,EAASC,EAAKC,QACY,IAAjBF,EAAQC,KACjBD,EAAQC,GAAO,CACbA,MACAH,MAEFA,KAGFD,EAAUG,EAAQC,GAAKH,IAAMI,CAC9B,EACD,GAAAC,CAAIH,EAASC,GACX,IAAKD,QAAmC,IAAjBA,EAAQC,GACtB,OAAA,KAGH,MAAAG,EAAgBJ,EAAQC,GAC1B,OAAAG,EAAcH,MAAQA,EACjBJ,EAAUO,EAAcN,IAG1B,IACR,EACD,OAAOE,EAASC,GACd,QAA4B,IAAjBD,EAAQC,GACjB,OAGI,MAAAG,EAAgBJ,EAAQC,GAC1BG,EAAcH,MAAQA,WACjBJ,EAAUO,EAAcN,WACxBE,EAAQC,GAElB,OAICI,EAAO,CACX,OAAAC,CAAQC,EAAUN,EAAKC,GACbN,EAAAG,IAAIQ,EAAUN,EAAKC,EAC5B,EACDM,QAAA,CAAQD,EAAUN,IACTL,EAAQO,IAAII,EAAUN,GAE/B,UAAAQ,CAAWF,EAAUN,GACXL,EAAAc,OAAOH,EAAUN,EAC1B,GCxDH,SAASU,EAAcC,GACrB,MAAY,SAARA,GAIQ,UAARA,IAIAA,IAAQC,OAAOD,GAAKE,WACfD,OAAOD,GAGJ,KAARA,GAAsB,SAARA,EACT,KAGFA,EACT,CAEA,SAASG,EAAiBd,GACjB,OAAAA,EAAIe,QAAQ,UAAWC,GAAQ,IAAIA,EAAIC,iBAChD,CAEA,MAAMC,EAAc,CAClB,gBAAAC,CAAiBpB,EAASC,EAAKoB,GAC7BrB,EAAQsB,aAAa,YAAYP,EAAiBd,KAAQoB,EAC3D,EAED,mBAAAE,CAAoBvB,EAASC,GAC3BD,EAAQwB,gBAAgB,YAAYT,EAAiBd,KACtD,EAED,iBAAAwB,CAAkBzB,GAChB,IAAKA,EACH,MAAO,GAGT,MAAM0B,EAAa,IACd1B,EAAQ2B,SAWN,OARPC,OAAOC,KAAKH,GACTI,QAAQ7B,GAAQA,EAAI8B,WAAW,SAC/BC,SAAS/B,IACR,IAAIgC,EAAUhC,EAAIe,QAAQ,OAAQ,IACxBiB,EAAAA,EAAQC,OAAO,GAAGhB,cAAgBe,EAAQE,MAAM,EAAGF,EAAQG,QACrEV,EAAWO,GAAWtB,EAAce,EAAWzB,GAAI,IAGhDyB,CACR,EAEDW,iBAAA,CAAiBrC,EAASC,IACjBU,EAAcX,EAAQsC,aAAa,YAAYvB,EAAiBd,OAGzE,MAAAsC,CAAOvC,GACC,MAAAwC,EAAOxC,EAAQyC,wBAEd,MAAA,CACLC,IAAKF,EAAKE,IAAMnD,SAASC,KAAKmD,UAC9BC,KAAMJ,EAAKI,KAAOrD,SAASC,KAAKqD,WAEnC,EAEDC,SAAS9C,IACA,CACL0C,IAAK1C,EAAQ+C,UACbH,KAAM5C,EAAQgD,aAIlB,KAAAC,CAAMjD,EAASiD,GACNrB,OAAAsB,OAAOlD,EAAQiD,MAAOA,EAC9B,EAED,WAAAE,CAAYnD,EAASoD,GACdpD,IAIDA,EAAQqD,UAAUC,SAASF,GACrBpD,EAAAqD,UAAUE,OAAOH,GAEjBpD,EAAAqD,UAAUG,IAAIJ,GAEzB,EAED,QAAAK,CAASzD,EAASoD,GACZpD,EAAQqD,UAAUC,SAASF,IACvBpD,EAAAqD,UAAUG,IAAIJ,EACvB,EAED,QAAAM,CAAS1D,EAASiD,GAChBrB,OAAOC,KAAKoB,GAAOjB,SAAS2B,IAC1B3D,EAAQiD,MAAMU,GAAYV,EAAMU,EAAQ,GAE3C,EAED,WAAAC,CAAY5D,EAASoD,GACdpD,EAAQqD,UAAUC,SAASF,IACxBpD,EAAAqD,UAAUE,OAAOH,EAC1B,EAEDS,SAAA,CAAS7D,EAASoD,IACTpD,EAAQqD,UAAUC,SAASF,ICtGlC,IAAAU,EAAeC,QAAQC,UAAUC,iBACjCC,EAAcH,QAAQC,UAAUG,cAGpC,MAAMC,QACE,MAAAC,EAAI,IAAIC,YAAY,YAAa,CACrCC,YAAY,IAGRvE,EAAUT,SAASiF,cAAc,OAKvC,OAJQxE,EAAAyE,iBAAiB,aAAa,IAAM,OAE5CJ,EAAEK,iBACF1E,EAAQ2E,cAAcN,GACfA,EAAEO,qBAGLC,EAAqB,iBAEnB,MAAA7E,EAAUT,SAASiF,cAAc,OAEnC,IACFxE,EAAQiE,iBAAiB,WAC1B,OAAQa,GACA,OAAA,CACR,CAEM,OAAA,QAIPhB,EAAe,SAAUiB,GACvB,IAAKF,EAAmBG,KAAKD,GACpB,OAAAE,KAAKhB,iBAAiBc,GAGzB,MAAAG,EAAQC,QAAQF,KAAKnF,IAEtBoF,IACED,KAAAnF,GHrBI,CAACsF,IACX,GACDA,GAAUC,KAAKC,MAxBH,IAwBSD,KAAKE,gBACnBhG,SAASiG,eAAeJ,IAE1B,OAAAA,CAAA,EGgBOK,CAAO,UAGnB,IAAIC,EAAW,KACX,IACFX,EAAWA,EAAS/D,QAAQ6D,EAAoB,IAAII,KAAKnF,MAC9C4F,EAAAT,KAAKhB,iBAAiBc,EACvC,CAAc,QACHG,GACHD,KAAKzD,gBAAgB,KAExB,CAEM,OAAAkE,CACX,EAEExB,EAAc,SAAUa,GACtB,IAAKF,EAAmBG,KAAKD,GACpB,OAAAE,KAAKd,cAAcY,GAG5B,MAAMY,EAAUC,EAAKC,KAAKZ,KAAMF,GAEhC,YAA0B,IAAfY,EAAQ,GACVA,EAAQ,GAGV,IACX,GAGA,MAAMC,EAAO9B,EACPgC,EAAU5B,EClEV6B,EAAI3G,IACJ4G,EAAiB,qBACjBC,EAAiB,OACjBC,EAAgB,SAChBC,EAAgB,CAAA,EACtB,IAAIC,EAAW,EACf,MAAMC,EAAe,CACnBC,WAAY,YACZC,WAAY,YAERC,EAAe,CACnB,QACA,WACA,UACA,YACA,cACA,aACA,iBACA,YACA,WACA,YACA,cACA,YACA,UACA,WACA,QACA,oBACA,aACA,YACA,WACA,cACA,cACA,cACA,YACA,eACA,gBACA,eACA,gBACA,aACA,QACA,OACA,SACA,QACA,SACA,SACA,UACA,WACA,OACA,SACA,eACA,SACA,OACA,mBACA,mBACA,QACA,QACA,UASO,SAAAC,EAAYzG,EAAS0G,GAC5B,OAAQA,GAAO,GAAGA,MAAQN,OAAiBpG,EAAQoG,UAAYA,GACjE,CAEA,SAASO,EAAS3G,GACV,MAAA0G,EAAMD,EAAYzG,GAKxB,OAHAA,EAAQoG,SAAWM,EACnBP,EAAcO,GAAOP,EAAcO,IAAQ,CAAA,EAEpCP,EAAcO,EACvB,CAiCA,SAASE,EAAYC,EAAQC,EAASC,EAAqB,MACnD,MAAAC,EAAepF,OAAOC,KAAKgF,GAEjC,IAAA,IAASI,EAAI,EAAGC,EAAMF,EAAa5E,OAAQ6E,EAAIC,EAAKD,IAAK,CACvD,MAAME,EAAQN,EAAOG,EAAaC,IAElC,GAAIE,EAAMC,kBAAoBN,GAAWK,EAAMJ,qBAAuBA,EAC7D,OAAAI,CAEV,CAEM,OAAA,IACT,CAES,SAAAE,EAAgBC,EAAmBR,EAASS,GAC7C,MAAAC,EAAgC,iBAAZV,EACpBM,EAAkBI,EAAaD,EAAeT,EAGpD,IAAIW,EAAYH,EAAkBtG,QAAQiF,EAAgB,IACpD,MAAAyB,EAASrB,EAAaoB,GAExBC,IACUD,EAAAC,GASP,OANUlB,EAAamB,QAAQF,IAAa,IAGrCA,EAAAH,GAGP,CAACE,EAAYJ,EAAiBK,EACvC,CAEA,SAASG,EAAW5H,EAASsH,EAAmBR,EAASS,EAAcM,GACrE,GAAiC,iBAAtBP,IAAmCtH,EAC5C,OAGG8G,IACOA,EAAAS,EACKA,EAAA,MAGjB,MAAOC,EAAYJ,EAAiBK,GAAaJ,EAC/CC,EACAR,EACAS,GAEIV,EAASF,EAAS3G,GAClB8H,EAAWjB,EAAOY,KAAeZ,EAAOY,GAAa,CAAA,GACrDM,EAAanB,EAAYkB,EAAUV,EAAiBI,EAAaV,EAAU,MAEjF,GAAIiB,EAGF,YAFWA,EAAAF,OAASE,EAAWF,QAAUA,GAK3C,MAAMnB,EAAMD,EAAYW,EAAiBE,EAAkBtG,QAAQgF,EAAgB,KAC7EgC,EAAKR,EAlFJ,SAA2BxH,EAAS+E,EAAUiD,GAC9C,OAAA,SAASlB,EAAQK,GAChB,MAAAc,EAAcjI,EAAQiE,iBAAiBc,GAEpC,IAAA,IAAAmD,OAAEA,GAAWf,EAAOe,GAAUA,IAAWjD,KAAMiD,EAASA,EAAOC,WACtE,IAAA,IAASlB,EAAIgB,EAAY7F,OAAQ6E,IAAK,GAChC,GAAAgB,EAAYhB,KAAOiB,EAKrB,OAJIpB,EAAQe,QACVO,EAAaC,IAAIrI,EAASmH,EAAMmB,KAAMN,GAGjCA,EAAGO,MAAML,EAAQ,CAACf,IAMxB,OAAA,IACX,CACA,CAgEMqB,CAA2BxI,EAAS8G,EAASS,GA7F1C,SAAiBvH,EAASgI,GAC1B,OAAA,SAASlB,EAAQK,GAKtB,OAJIL,EAAQe,QACVO,EAAaC,IAAIrI,EAASmH,EAAMmB,KAAMN,GAGjCA,EAAGO,MAAMvI,EAAS,CAACmH,GAC9B,CACA,CAsFMsB,CAAiBzI,EAAS8G,GAE3BkB,EAAAjB,mBAAqBS,EAAaV,EAAU,KAC/CkB,EAAGZ,gBAAkBA,EACrBY,EAAGH,OAASA,EACZG,EAAG5B,SAAWM,EACdoB,EAASpB,GAAOsB,EAERhI,EAAAyE,iBAAiBgD,EAAWO,EAAIR,EAC1C,CAEA,SAASkB,EAAc1I,EAAS6G,EAAQY,EAAWX,EAASC,GAC1D,MAAMiB,EAAKpB,EAAYC,EAAOY,GAAYX,EAASC,GAE9CiB,IAILhI,EAAQ2I,oBAAoBlB,EAAWO,EAAI7C,QAAQ4B,WAC5CF,EAAOY,GAAWO,EAAG5B,UAC9B,CAcA,MAAMgC,EAAe,CACnB,EAAAQ,CAAG5I,EAASmH,EAAOL,EAASS,GAC1BK,EAAW5H,EAASmH,EAAOL,EAASS,GAAc,EACnD,EAED,GAAAsB,CAAI7I,EAASmH,EAAOL,EAASS,GAC3BK,EAAW5H,EAASmH,EAAOL,EAASS,GAAc,EACnD,EAED,GAAAc,CAAIrI,EAASsH,EAAmBR,EAASS,GACvC,GAAiC,iBAAtBD,IAAmCtH,EAC5C,OAGF,MAAOwH,EAAYJ,EAAiBK,GAAaJ,EAC/CC,EACAR,EACAS,GAEIuB,EAAcrB,IAAcH,EAC5BT,EAASF,EAAS3G,GAClB+I,EAA8C,MAAhCzB,EAAkBpF,OAAO,GAEzC,QAA2B,IAApBkF,EAAiC,CAE1C,IAAKP,IAAWA,EAAOY,GACrB,OAIF,YADAiB,EAAc1I,EAAS6G,EAAQY,EAAWL,EAAiBI,EAAaV,EAAU,KAEnF,CAEGiC,GACFnH,OAAOC,KAAKgF,GAAQ7E,SAASgH,KA9CnC,SAAkChJ,EAAS6G,EAAQY,EAAWwB,GAC5D,MAAMC,EAAoBrC,EAAOY,IAAc,CAAA,EAE/C7F,OAAOC,KAAKqH,GAAmBlH,SAASmH,IACtC,GAAIA,EAAWxB,QAAQsB,IAAiB,EAAA,CAChC,MAAA9B,EAAQ+B,EAAkBC,GAEhCT,EAAc1I,EAAS6G,EAAQY,EAAWN,EAAMC,gBAAiBD,EAAMJ,mBACxE,IAEL,CAqCQqC,CAAyBpJ,EAAS6G,EAAQmC,EAAc1B,EAAkBnF,MAAM,GAAE,IAItF,MAAM+G,EAAoBrC,EAAOY,IAAc,CAAA,EAC/C7F,OAAOC,KAAKqH,GAAmBlH,SAASqH,IACtC,MAAMF,EAAaE,EAAYrI,QAAQkF,EAAe,IAEtD,IAAK4C,GAAexB,EAAkBK,QAAQwB,IAAkB,EAAA,CACxD,MAAAhC,EAAQ+B,EAAkBG,GAEhCX,EAAc1I,EAAS6G,EAAQY,EAAWN,EAAMC,gBAAiBD,EAAMJ,mBACxE,IAEJ,EAED,OAAAuC,CAAQtJ,EAASmH,EAAOoC,GACtB,GAAqB,iBAAVpC,IAAuBnH,EACzB,OAAA,KAGT,MAAMyH,EAAYN,EAAMnG,QAAQiF,EAAgB,IAC1C6C,EAAc3B,IAAUM,EACxB+B,EAAWhD,EAAamB,QAAQF,IAAa,EAE/C,IAAAgC,EACAC,GAAU,EACVC,GAAiB,EACjB/E,GAAmB,EACnBgF,EAAM,KAkDH,OAhDHd,GAAe/C,IACH0D,EAAA1D,EAAE8D,MAAM1C,EAAOoC,GAE3BxD,EAAA/F,GAASsJ,QAAQG,GACTC,GAACD,EAAYK,uBACNH,GAACF,EAAYM,gCAC9BnF,EAAmB6E,EAAYO,sBAG7BR,GACII,EAAArK,SAAS0K,YAAY,cACvBL,EAAAM,UAAUzC,EAAWiC,GAAS,IAE5BE,EAAA,IAAItF,YAAY6C,EAAO,CAC3BuC,UACAnF,YAAY,SAKI,IAATgF,GACT3H,OAAOC,KAAK0H,GAAMvH,SAAS/B,IAClB2B,OAAAuI,eAAeP,EAAK3J,EAAK,CAC9BE,IAAM,IACGoJ,EAAKtJ,IAEf,IAID2E,IACFgF,EAAIlF,iBAECN,GACIxC,OAAAuI,eAAeP,EAAK,mBAAoB,CAC7CzJ,IAAK,KAAM,KAKbwJ,GACF3J,EAAQ2E,cAAciF,GAGpBA,EAAIhF,uBAA2C,IAAhB6E,GACjCA,EAAY/E,iBAGPkF,CACR,GChUGQ,EAAiB,CACrBC,QAAA,CAAQrK,EAAS+E,IACR/E,EAAQqK,QAAQtF,GAGzBY,QAAA,CAAQ3F,EAAS+E,IACR/E,EAAQ2F,QAAQZ,GAGzBa,KAAK,CAAAb,EAAU/E,EAAUT,SAASG,kBACzB,GAAG4K,UAAUC,EAAO1E,KAAK7F,EAAS+E,IAG3Ce,QAAQ,CAAAf,EAAU/E,EAAUT,SAASG,kBAC5BoG,EAAQD,KAAK7F,EAAS+E,GAG/ByF,SAAA,CAASxK,EAAS+E,IACC,GAAGuF,UAAUtK,EAAQwK,UAEtB1I,QAAQ2I,GAAUA,EAAM9E,QAAQZ,KAGlD,OAAA2F,CAAQ1K,EAAS+E,GACf,MAAM2F,EAAU,GAEhB,IAAIC,EAAW3K,EAAQmI,WAEvB,KAAOwC,GAAYA,EAASC,WAAaC,KAAKC,cA9BhC,IA8BgDH,EAASC,UACjE3F,KAAKU,QAAQgF,EAAU5F,IACzB2F,EAAQK,KAAKJ,GAGfA,EAAWA,EAASxC,WAGf,OAAAuC,CACR,EAED,IAAAM,CAAKhL,EAAS+E,GACZ,IAAIkG,EAAWjL,EAAQkL,uBAEvB,KAAOD,GAAU,CACX,GAAAA,EAAStF,QAAQZ,GACnB,MAAO,CAACkG,GAGVA,EAAWA,EAASC,sBACrB,CAED,MAAO,EACR,EAED,IAAAC,CAAKnL,EAAS+E,GACZ,IAAIoG,EAAOnL,EAAQoL,mBAEnB,KAAOD,GAAM,CACX,GAAIlG,KAAKU,QAAQwF,EAAMpG,GACrB,MAAO,CAACoG,GAGVA,EAAOA,EAAKC,kBACb,CAED,MAAO,EACR,GCtEGC,EAAO,YACPC,EAAW,OAAOD,IAElBE,EAA4B,GAAGF,mBAE/BG,EAAc,SAASF,IACvBG,EAAY,OAAOH,IAGnBI,EAA0B,IAAIL,SAE9BM,EAAkB,CACtBC,UAAW,GACXC,mBAAoB,GACpBC,kBAAmB,aACnBC,uBAAwB,WACxBC,mBAAoB,GACpBC,oBAAqB,GACrBC,kBAAmB,GACnBC,kBAAmB,GAEfC,EAAe,CACnBR,UAAW,SACXC,mBAAoB,GACpBC,kBAAmB,SACnBC,uBAAwB,SACxBC,mBAAoB,SACpBC,oBAAqB,SACrBC,kBAAmB,SACnBC,kBAAmB,UASrB,MAAME,EACJ,WAAAC,CAAYtM,EAASuM,EAAU,IAC7BtH,KAAKuH,SAAWxM,EACXiF,KAAAwH,SAAWxH,KAAKyH,WAAWH,GAEhCtH,KAAK0H,eAAiB,KAEtB1H,KAAK2H,kBAAoB,KAEzB3H,KAAK4H,cAAgB,KACrB5H,KAAK6H,kBAAoB,EAEzB7H,KAAK8H,aAAc,EAEf9H,KAAKuH,WACFnM,EAAAC,QAAQN,EAASsL,EAAUrG,MAChCA,KAAK+H,QAER,CAGD,OAAAC,GACO5M,EAAAI,WAAWwE,KAAKuH,SAAUlB,GAE/BrG,KAAKiI,gBAELjI,KAAK4H,cAAgB,KAErB5H,KAAKuH,SAASvI,iBAAiB,QAAQjC,SAASmL,IAC9CA,EAAKC,UAAY,EAAA,IAGnBnI,KAAKuH,SAAW,IACjB,CAED,IAAAa,GACEC,cAAcrI,KAAK2H,mBAEnB3H,KAAK2H,kBAAoB,KACzB3H,KAAK8H,aAAc,EACnB9H,KAAK6H,kBAAoB,EAEZ1E,EAAAkB,QAAQrE,KAAKuH,SAAUf,EACrC,CAED,KAAA8B,GACenF,EAAAkB,QAAQrE,KAAKuH,SAAUhB,GAE/BvG,KAAA4H,cAAgB5H,KAAKuI,qBAE1BvI,KAAK8H,aAAc,EAEnB9H,KAAKwI,gBACN,CAED,gBAAAC,CAAiBC,GACV1I,KAAK2I,aAAa3I,KAAKwH,SAASb,YAWrC3G,KAAK8H,aAAc,EACnB9H,KAAK6H,kBAAoB,EAEzB7H,KAAK0H,eAAiB,IAAIkB,KAAKF,GAAMG,UAChC7I,KAAK8H,aACR9H,KAAKsI,SAfLtI,KAAKuH,SAASY,UAAY,kFAESnI,KAAKwH,SAASb,sPAepD,CAGD,UAAAc,CAAWH,GACT,MAAM7K,EAAaP,EAAYM,kBAAkBwD,KAAKuH,UAEhDuB,EAAS,IACVpC,KACAjK,KACA6K,GAKE,MNvBa,EAACyB,EAAeD,EAAQE,KAC9CrM,OAAOC,KAAKoM,GAAajM,SAAS2B,IAC1B,MAAAuK,EAAgBD,EAAYtK,GAC5BtC,EAAQ0M,EAAOpK,GACfwK,EAAY9M,KAxBH+M,EAwBsB/M,GAxBT,IAAM+M,GAAKxD,SAwBO,UA3GnC,CAACwD,GACVA,QACK,GAAGA,IAGL,CAAE,EAACtN,SACP+E,KAAKuI,GACLC,MAAM,eAAe,GACrBnN,cAmGyDoN,CAAOjN,GAxBnD,IAAC+M,EA0Bf,IAAK,IAAIG,OAAOL,GAAelJ,KAAKmJ,GAClC,MAAM,IAAIK,MACR,GAAGR,EAAcS,0BACJ9K,qBAA4BwK,yBACjBD,MAE3B,GACF,EMQiBQ,CAAArD,EAAM0C,EAAQ3B,GAEvB2B,CACR,CAED,UAAAY,GACE,MAAMC,EAAQxE,EAAexE,KAAK8F,EAAyBzG,KAAKuH,UAS9D,GAPIoC,EAAA5M,SAAS6M,IACb1N,EAAYsC,SAASoL,EAAM,kBAAkB5J,KAAKwH,SAASV,yBAAwB,IAEzE5K,EAAAsC,SAASwB,KAAKuH,SAAU,aACpCrL,EAAYsC,SAASwB,KAAKuH,SAAU,aAAavH,KAAKwH,SAASX,qBAGxB,KAArC7G,KAAKwH,SAASZ,qBACb5G,KAAKwH,SAASX,kBAAkBgD,SAAS,YAE1C,IAAA,IAAS7H,EAAI,EAAGA,EAAI2H,EAAMxM,OAAS,EAAG6E,IACpC2H,EAAM3H,GAAG8H,mBACP,WACA,eAAexD,KAA6BtG,KAAKwH,SAASZ,4BAIjE,CAED,eAAAmD,GACQ,MAAAC,EAAYhK,KAAKwH,SAASb,UAC1BsD,EAAyC,IAAlCjK,KAAKwH,SAASN,kBAE3B/D,EAAaQ,GAAG3D,KAAKuH,SAAUf,GAAW,KACxC,MAAM0D,EAAU,IAAItB,KAAKA,KAAKuB,OAAOtB,UAC/BuB,EAAY,IAAIxB,KAAKoB,GAAWnB,UAAYoB,EAElD,IAAII,EAAc,EAElB,GAAIH,EAAUE,EAAW,CACvB,MAAME,EAAiBJ,EAAUE,EAC7B,IAAAG,EAEJ,GAAID,EAAiBL,EAAM,CAEzBM,EAAON,EADYK,EAAiBL,CAE9C,MACUM,EAAON,EAAOK,EAGhBD,EAAcH,EAAUK,CAChC,MACsBF,EAAAD,EAGhBpK,KAAKyI,iBAAiB4B,EAAW,GAEpC,CAED,KAAAtC,GACO/H,KAAAwK,kBAAkBxK,KAAKwH,SAASb,WAErC3G,KAAKyK,iBACDzK,KAAKwH,SAASN,kBAAoB,GACpClH,KAAK+J,iBAER,CAED,iBAAAS,CAAkB9B,GACX1I,KAAK2I,aAAa3I,KAAKwH,SAASb,WAWrC3G,KAAK0H,eAAiB,IAAIkB,KAAKF,GAAMG,UAVnC7I,KAAKuH,SAASY,UAAY,4EAEKnI,KAAKwH,SAASb,gPAShD,CAED,cAAA8D,GACetH,EAAAkB,QAAQrE,KAAKuH,SAAUhB,GAEpCvG,KAAK8H,aAAc,EAGnB9H,KAAK0K,iBAEL1K,KAAK0J,aAEL1J,KAAKwI,gBACN,CAED,cAAAA,GACOxI,KAAA2K,eAAe3K,KAAK4H,eAEpB5H,KAAA2H,kBAAoBiD,aAAY,KAC9B5K,KAAA4H,cAAgB5H,KAAKuI,qBAErBvI,KAAK8H,aAAgB9H,KAAK4H,cAM1B5H,KAAA2K,eAAe3K,KAAK4H,eALvB5H,KAAKiI,eAK+B,GACrC,IACJ,CAED,YAAAU,CAAaD,GAGX,OAFOA,EAAA,IAAIE,KAAKF,cAEOE,OAASiC,MAAMnC,EACvC,CAED,kBAAAH,GACE,MAAMuC,GAAa,IAAIlC,MAAOC,UAE1B,IAAAkC,EAAe/K,KAAK0H,eAAiBoD,EAEzC,QAAIC,EAAe,KAIY,IAA3B/K,KAAK6H,mBAA2B7H,KAAK6H,kBAAoBkD,EAAe,MAC1EA,EAAe/K,KAAK6H,kBAAoB,KAG1C7H,KAAK6H,kBAAoBkD,EAClBA,EACR,CAED,cAAAL,GACQ,MAAAK,EAAe/K,KAAKuI,qBAEpByC,EAAYhL,KAAKiL,eAAeF,GAE/BpO,OAAAuO,QAAQF,GAAWjO,SAAQ,EAAE6M,EAAMxN,MACxC,MAAM+O,EAAchG,EAAetE,QAAQ,cAAc+I,IAAQ5J,KAAKuH,UAEtE,GAAI4D,EAAa,CACf,MAAMC,EAAWpL,KAAKqL,sBAAsBF,EAAavB,EAAMxN,GAC/D+O,EAAYhD,UAAYiD,EACZlP,EAAAC,iBAAiBgP,EAAavB,EAAMxN,GACpCF,EAAAI,oBAAoB6O,EAAa,iBAC9C,IAEJ,CAED,cAAAR,CAAeI,GACP,MAAAC,EAAYhL,KAAKiL,eAAeF,GAE/BpO,OAAAuO,QAAQF,GAAWjO,SAAQ,EAAE6M,EAAMxN,MACxC,MAAM+O,EAAchG,EAAetE,QAAQ,cAAc+I,IAAQ5J,KAAKuH,UAEtE,GAAI4D,EAAa,CACf,MAAMG,EAAYnG,EAAetE,QAAQ,6BAA8BsK,GAEnE,GAAAG,EAAUC,cAAgBnP,EAC5B,OAGFkP,EAAUC,YAAcnP,EACZF,EAAAC,iBAAiBgP,EAAavB,EAAMxN,EACjD,IAEJ,CAED,cAAA6O,CAAeO,GAQb,MAAO,CAAEC,KAPIzL,KAAK0L,YAAYtL,KAAKC,MAAMmL,UAO1BG,MAND3L,KAAK0L,YACjBtL,KAAKC,MAAOmL,EAAY,MAAiB,OAKrBI,QAHN5L,KAAK0L,YAAYtL,KAAKC,MAAOmL,EAAY,KAAY,MAGtCK,QAFf7L,KAAK0L,YAAYtL,KAAKC,MAAOmL,EAAY,IAAc,MAGxE,CAED,WAAAE,CAAYI,GACV,OAAOA,EAAO,EAAI,GAAGA,IAAS,IAAIA,GACnC,CAED,qBAAAT,CAAsBtQ,EAASgR,EAAM3P,GACnC,MAAM4P,EAAY9P,EAAYkB,iBAAiBrC,EAAS,kBAClDkR,EAAW,SAASF,IACpBG,EAAa,oBAAoBD,KAEjCE,EACiC,KAArCnM,KAAKwH,SAAST,mBAA4B,UAAU/G,KAAKwH,SAAST,sBAAwB,GAE5F,IAAIqF,EAAgB,GAChBC,EAAgB,GAEhB,GAAoC,KAApCrM,KAAKwH,SAASP,kBAA0B,CAC1C,MAAMqF,EAAWtM,KAAKwH,SAASP,kBAAkBmC,MAAM,OAAO,GAG9DgD,EAAgB,qBAAqBE,EAAW,IAF3BtM,KAAKwH,SAASP,kBAAkBsF,MAAMD,GAAU,MAGrDD,EAAA,qBAAqBrM,KAAKwH,SAASP,oBACpD,CAaM,MAXU,iBAEb+E,EAAYE,EAAa,+BACEC,KAAcE,KAAiBjQ,mBAE1D4P,EACI,eAAeC,sCAA6CjM,KAAKwH,SAASR,wBAAwBoF,KAAiBJ,WACnH,UAKT,CAED,aAAA/D,GACEI,cAAcrI,KAAK2H,mBAEnB3H,KAAK2H,kBAAoB,KACzB3H,KAAK8H,aAAc,EACnB9H,KAAK6H,kBAAoB,EAEZ1E,EAAAkB,QAAQrE,KAAKuH,SAAUf,EACrC,CAGD,eAAWJ,GACF,OAAAA,CACR,CAED,sBAAOoG,CAAgB1D,EAAQxB,GACtB,OAAAtH,KAAKyM,MAAK,WACf,IAAIxR,EAAOG,EAAKG,QAAQyE,KAAMqG,GACxB,MAAAqG,EAA4B,iBAAX5D,GAAuBA,EAE9C,IAAK7N,IAAQ,UAAU8E,KAAK+I,MAIvB7N,IACIA,EAAA,IAAImM,EAAUpH,KAAM0M,IAGP,iBAAX5D,GAAqB,CAC9B,QAA4B,IAAjB7N,EAAK6N,GACd,MAAM,IAAI6D,UAAU,oBAAoB7D,MAGrC7N,EAAA6N,GAAQxB,EACd,CACP,GACG,CAED,kBAAOsF,CAAY7R,GACV,OAAAK,EAAKG,QAAQR,EAASsL,EAC9B,EN9MwB,IAACwG,SMuN5B1H,EAAexE,KA9XY,6BA8Xa5D,SAAS+P,IAC3C,IAAAxR,EAAW8L,EAAUwF,YAAYE,GAK9B,OAJFxR,IACQA,EAAA,IAAI8L,EAAU0F,IAGpBxR,CAAA,IN7NmBuR,EMuOT,KACjB,MAAM/L,EAAI3G,IAEV,GAAI2G,EAAG,CACC,MAAAiM,EAAqBjM,EAAEiC,GAAGqD,GAChCtF,EAAEiC,GAAGqD,GAAQgB,EAAUoF,gBACvB1L,EAAEiC,GAAGqD,GAAM4G,YAAc5F,EACzBtG,EAAEiC,GAAGqD,GAAM6G,WAAa,KACtBnM,EAAEiC,GAAGqD,GAAQ2G,EACN3F,EAAUoF,gBAEpB,GNjP2B,YAAxBlS,SAAS4S,WACF5S,SAAAkF,iBAAiB,mBAAoBqN"}