{"version": 3, "file": "data-parser.min.js", "sources": ["../../../src/plugins/data-parser/js/mdb/util/index.js", "../../../src/plugins/data-parser/js/data/colorGenerator.js", "../../../src/plugins/data-parser/js/data/colorMap.js", "../../../src/plugins/data-parser/js/strategy/util.js", "../../../src/plugins/data-parser/js/strategy/datatable.js", "../../../src/plugins/data-parser/js/strategy/chart.js", "../../../src/plugins/data-parser/js/data/countryCodes.js", "../../../src/plugins/data-parser/js/data/markers.js", "../../../src/plugins/data-parser/js/strategy/vector-map.js", "../../../src/plugins/data-parser/js/strategy/treeview.js", "../../../src/plugins/data-parser/js/utils/arrays.js", "../../../src/plugins/data-parser/js/data-parser.js", "../../../src/plugins/data-parser/js/utils/collections.js", "../../../src/plugins/data-parser/js/utils/objects.js"], "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta1): util/index.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst MAX_UID = 1000000;\nconst MILLISECONDS_MULTIPLIER = 1000;\nconst TRANSITION_END = 'transitionend';\n\n// Shoutout AngusCroll (https://goo.gl/pxwQGp)\nconst toType = (obj) => {\n  if (obj === null || obj === undefined) {\n    return `${obj}`;\n  }\n\n  return {}.toString\n    .call(obj)\n    .match(/\\s([a-z]+)/i)[1]\n    .toLowerCase();\n};\n\n/**\n * --------------------------------------------------------------------------\n * Public Util Api\n * --------------------------------------------------------------------------\n */\n\nconst getUID = (prefix) => {\n  do {\n    prefix += Math.floor(Math.random() * MAX_UID);\n  } while (document.getElementById(prefix));\n\n  return prefix;\n};\n\nconst getSelector = (element) => {\n  let selector = element.getAttribute('data-mdb-target');\n\n  if (!selector || selector === '#') {\n    const hrefAttr = element.getAttribute('href');\n\n    selector = hrefAttr && hrefAttr !== '#' ? hrefAttr.trim() : null;\n  }\n\n  return selector;\n};\n\nconst getSelectorFromElement = (element) => {\n  const selector = getSelector(element);\n\n  if (selector) {\n    return document.querySelector(selector) ? selector : null;\n  }\n\n  return null;\n};\n\nconst getElementFromSelector = (element) => {\n  const selector = getSelector(element);\n\n  return selector ? document.querySelector(selector) : null;\n};\n\nconst getTransitionDurationFromElement = (element) => {\n  if (!element) {\n    return 0;\n  }\n\n  // Get transition-duration of the element\n  let { transitionDuration, transitionDelay } = window.getComputedStyle(element);\n\n  const floatTransitionDuration = Number.parseFloat(transitionDuration);\n  const floatTransitionDelay = Number.parseFloat(transitionDelay);\n\n  // Return 0 if element or transition duration is not found\n  if (!floatTransitionDuration && !floatTransitionDelay) {\n    return 0;\n  }\n\n  // If multiple durations are defined, take the first\n  transitionDuration = transitionDuration.split(',')[0];\n  transitionDelay = transitionDelay.split(',')[0];\n\n  return (\n    (Number.parseFloat(transitionDuration) + Number.parseFloat(transitionDelay)) *\n    MILLISECONDS_MULTIPLIER\n  );\n};\n\nconst triggerTransitionEnd = (element) => {\n  element.dispatchEvent(new Event(TRANSITION_END));\n};\n\nconst isElement = (obj) => (obj[0] || obj).nodeType;\n\nconst emulateTransitionEnd = (element, duration) => {\n  let called = false;\n  const durationPadding = 5;\n  const emulatedDuration = duration + durationPadding;\n\n  function listener() {\n    called = true;\n    element.removeEventListener(TRANSITION_END, listener);\n  }\n\n  element.addEventListener(TRANSITION_END, listener);\n  setTimeout(() => {\n    if (!called) {\n      triggerTransitionEnd(element);\n    }\n  }, emulatedDuration);\n};\n\nconst typeCheckConfig = (componentName, config, configTypes) => {\n  Object.keys(configTypes).forEach((property) => {\n    const expectedTypes = configTypes[property];\n    const value = config[property];\n    const valueType = value && isElement(value) ? 'element' : toType(value);\n\n    if (!new RegExp(expectedTypes).test(valueType)) {\n      throw new Error(\n        `${componentName.toUpperCase()}: ` +\n          `Option \"${property}\" provided type \"${valueType}\" ` +\n          `but expected type \"${expectedTypes}\".`\n      );\n    }\n  });\n};\n\nconst isVisible = (element) => {\n  if (!element) {\n    return false;\n  }\n\n  if (element.style && element.parentNode && element.parentNode.style) {\n    const elementStyle = getComputedStyle(element);\n    const parentNodeStyle = getComputedStyle(element.parentNode);\n\n    return (\n      elementStyle.display !== 'none' &&\n      parentNodeStyle.display !== 'none' &&\n      elementStyle.visibility !== 'hidden'\n    );\n  }\n\n  return false;\n};\n\nconst findShadowRoot = (element) => {\n  if (!document.documentElement.attachShadow) {\n    return null;\n  }\n\n  // Can find the shadow root otherwise it'll return the document\n  if (typeof element.getRootNode === 'function') {\n    const root = element.getRootNode();\n    return root instanceof ShadowRoot ? root : null;\n  }\n\n  if (element instanceof ShadowRoot) {\n    return element;\n  }\n\n  // when we don't find a shadow root\n  if (!element.parentNode) {\n    return null;\n  }\n\n  return findShadowRoot(element.parentNode);\n};\n\nconst noop = () => function () {};\n\nconst reflow = (element) => element.offsetHeight;\n\nconst getjQuery = () => {\n  const { jQuery } = window;\n\n  if (jQuery && !document.body.hasAttribute('data-mdb-no-jquery')) {\n    return jQuery;\n  }\n\n  return null;\n};\n\nconst onDOMContentLoaded = (callback) => {\n  if (document.readyState === 'loading') {\n    document.addEventListener('DOMContentLoaded', callback);\n  } else {\n    callback();\n  }\n};\n\nconst isRTL = document.documentElement.dir === 'rtl';\n\nconst array = (collection) => {\n  return Array.from(collection);\n};\n\nconst element = (tag) => {\n  return document.createElement(tag);\n};\n\nexport {\n  getjQuery,\n  TRANSITION_END,\n  getUID,\n  getSelectorFromElement,\n  getElementFromSelector,\n  getTransitionDurationFromElement,\n  triggerTransitionEnd,\n  isElement,\n  emulateTransitionEnd,\n  typeCheckConfig,\n  isVisible,\n  findShadowRoot,\n  noop,\n  reflow,\n  array,\n  element,\n  onDOMContentLoaded,\n  isRTL,\n};\n", "export default {\n  50: [\n    '#FFEBEE',\n    '#FCE4EC',\n    '#F3E5F5',\n    '#EDE7F6',\n    '#E8EAF6',\n    '#E3F2FD',\n    '#E1F5FE',\n    '#E0F7FA',\n    '#E0F2F1',\n    '#E8F5E9',\n    '#F1F8E9',\n    '#F9FBE7',\n    '#FFFDE7',\n    '#FFF8E1',\n    '#FFF3E0',\n    '#FBE9E7',\n    '#EFEBE9',\n    '#FAFAFA',\n    '#ECEFF1',\n  ],\n  100: [\n    '#FFCDD2',\n    '#F8BBD0',\n    '#E1BEE7',\n    '#D1C4E9',\n    '#C5CAE9',\n    '#BBDEFB',\n    '#B3E5FC',\n    '#B2EBF2',\n    '#B2DFDB',\n    '#C8E6C9',\n    '#DCEDC8',\n    '#F0F4C3',\n    '#FFF9C4',\n    '#FFECB3',\n    '#FFE0B2',\n    '#FFCCBC',\n    '#D7CCC8',\n    '#F5F5F5',\n    '#CFD8DC',\n  ],\n  200: [\n    '#EF9A9A',\n    '#F48FB1',\n    '#CE93D8',\n    '#B39DDB',\n    '#9FA8DA',\n    '#90CAF9',\n    '#81D4FA',\n    '#80DEEA',\n    '#80CBC4',\n    '#A5D6A7',\n    '#C5E1A5',\n    '#E6EE9C',\n    '#FFF59D',\n    '#FFE082',\n    '#FFCC80',\n    '#FFAB91',\n    '#BCAAA4',\n    '#EEEEEE',\n    '#B0BEC5',\n  ],\n  300: [\n    '#E57373',\n    '#F06292',\n    '#BA68C8',\n    '#9575CD',\n    '#7986CB',\n    '#64B5F6',\n    '#4FC3F7',\n    '#4DD0E1',\n    '#4DB6AC',\n    '#81C784',\n    '#AED581',\n    '#DCE775',\n    '#FFF176',\n    '#FFD54F',\n    '#FFB74D',\n    '#FF8A65',\n    '#A1887F',\n    '#E0E0E0',\n    '#90A4AE',\n  ],\n  400: [\n    '#EF5350',\n    '#EC407A',\n    '#AB47BC',\n    '#7E57C2',\n    '#5C6BC0',\n    '#42A5F5',\n    '#29B6F6',\n    '#26C6DA',\n    '#26A69A',\n    '#66BB6A',\n    '#9CCC65',\n    '#D4E157',\n    '#FFEE58',\n    '#FFCA28',\n    '#FFA726',\n    '#FF7043',\n    '#8D6E63',\n    '#BDBDBD',\n    '#78909C',\n  ],\n  500: [\n    '#F44336',\n    '#E91E63',\n    '#9C27B0',\n    '#673AB7',\n    '#3F51B5',\n    '#2196F3',\n    '#03A9F4',\n    '#00BCD4',\n    '#26A69A',\n    '#4CAF50',\n    '#8BC34A',\n    '#CDDC39',\n    '#FFEB3B',\n    '#FFC107',\n    '#FF9800',\n    '#FF5722',\n    '#795548',\n    '#9E9E9E',\n    '#607D8B',\n  ],\n  600: [\n    '#E53935',\n    '#D81B60',\n    '#8E24AA',\n    '#5E35B1',\n    '#3949AB',\n    '#1E88E5',\n    '#039BE5',\n    '#00ACC1',\n    '#00897B',\n    '#43A047',\n    '#7CB342',\n    '#C0CA33',\n    '#FDD835',\n    '#FFB300',\n    '#FB8C00',\n    '#F4511E',\n    '#6D4C41',\n    '#757575',\n    '#546E7A',\n  ],\n  700: [\n    '#D32F2F',\n    '#C2185B',\n    '#7B1FA2',\n    '#512DA8',\n    '#303F9F',\n    '#1976D2',\n    '#0288D1',\n    '#0097A7',\n    '#00796B',\n    '#388E3C',\n    '#689F38',\n    '#AFB42B',\n    '#FBC02D',\n    '#FFA000',\n    '#F57C00',\n    '#E64A19',\n    '#5D4037',\n    '#616161',\n    '#455A64',\n  ],\n  800: [\n    '#C62828',\n    '#AD1457',\n    '#6A1B9A',\n    '#4527A0',\n    '#283593',\n    '#1565C0',\n    '#0277BD',\n    '#0097A7',\n    '#00796B',\n    '#2E7D32',\n    '#558B2F',\n    '#9E9D24',\n    '#F9A825',\n    '#FF8F00',\n    '#EF6C00',\n    '#D84315',\n    '#4E342E',\n    '#424242',\n    '#37474F',\n  ],\n  900: [\n    '#C62828',\n    '#880E4F',\n    '#4A148C',\n    '#4527A0',\n    '#1A237E',\n    '#0D47A1',\n    '#01579B',\n    '#006064',\n    '#004D40',\n    '#1B5E20',\n    '#33691E',\n    '#827717',\n    '#F57F17',\n    '#FF6F00',\n    '#E65100',\n    '#BF360C',\n    '#3E2723',\n    '#212121',\n    '#263238',\n  ],\n  mdb: ['#1266F1', '#B23CFD', '#00B74A', '#F93154', '#FFA900', '#39C0ED', '#262626'],\n};\n", "export default {\n  red: [\n    '#FFEBEE',\n    '#FFCDD2',\n    '#EF9A9A',\n    '#E57373',\n    '#EF5350',\n    '#F44336',\n    '#E53935',\n    '#D32F2F',\n    '#C62828',\n    '#B71C1C',\n  ],\n  pink: [\n    '#FCE4EC',\n    '#F8BBD0',\n    '#F48FB1',\n    '#F06292',\n    '#EC407A',\n    '#E91E63',\n    '#D81B60',\n    '#C2185B',\n    '#AD1457',\n    '#880E4F',\n  ],\n  purple: [\n    '#F3E5F5',\n    '#E1BEE7',\n    '#CE93D8',\n    '#BA68C8',\n    '#AB47BC',\n    '#9C27B0',\n    '#8E24AA',\n    '#7B1FA2',\n    '#6A1B9A',\n    '#4A148C',\n  ],\n  deepPurple: [\n    '#EDE7F6',\n    '#D1C4E9',\n    '#B39DDB',\n    '#9575CD',\n    '#7E57C2',\n    '#673AB7',\n    '#5E35B1',\n    '#512DA8',\n    '#4527A0',\n    '#4527A0',\n  ],\n  indigo: [\n    '#E8EAF6',\n    '#C5CAE9',\n    '#9FA8DA',\n    '#7986CB',\n    '#5C6BC0',\n    '#3F51B5',\n    '#3949AB',\n    '#303F9F',\n    '#283593',\n    '#1A237E',\n  ],\n  blue: [\n    '#E3F2FD',\n    '#BBDEFB',\n    '#90CAF9',\n    '#64B5F6',\n    '#42A5F5',\n    '#2196F3',\n    '#1E88E5',\n    '#1976D2',\n    '#1565C0',\n    '#0D47A1',\n  ],\n  lightBlue: [\n    '#E1F5FE',\n    '#B3E5FC',\n    '#81D4FA',\n    '#4FC3F7',\n    '#29B6F6',\n    '#03A9F4',\n    '#039BE5',\n    '#0288D1',\n    '#0277BD',\n    '#01579B',\n  ],\n  cyan: [\n    '#E0F7FA',\n    '#B2EBF2',\n    '#80DEEA',\n    '#4DD0E1',\n    '#26C6DA',\n    '#00BCD4',\n    '#00ACC1',\n    '#0097A7',\n    '#0097A7',\n    '#006064',\n  ],\n  teal: [\n    '#E0F2F1',\n    '#B2DFDB',\n    '#80CBC4',\n    '#4DB6AC',\n    '#26A69A',\n    '#26A69A',\n    '#00897B',\n    '#00796B',\n    '#00796B',\n    '#004D40',\n  ],\n  green: [\n    '#E8F5E9',\n    '#C8E6C9',\n    '#A5D6A7',\n    '#81C784',\n    '#66BB6A',\n    '#4CAF50',\n    '#43A047',\n    '#388E3C',\n    '#2E7D32',\n    '#1B5E20',\n  ],\n  lightGreen: [\n    '#F1F8E9',\n    '#DCEDC8',\n    '#C5E1A5',\n    '#AED581',\n    '#9CCC65',\n    '#8BC34A',\n    '#7CB342',\n    '#689F38',\n    '#558B2F',\n    '#33691E',\n  ],\n  lime: [\n    '#F9FBE7',\n    '#F0F4C3',\n    '#E6EE9C',\n    '#DCE775',\n    '#D4E157',\n    '#CDDC39',\n    '#C0CA33',\n    '#AFB42B',\n    '#9E9D24',\n    '#827717',\n  ],\n  yellow: [\n    '#FFFDE7',\n    '#FFF9C4',\n    '#FFF59D',\n    '#FFF176',\n    '#FFEE58',\n    '#FFEB3B',\n    '#FDD835',\n    '#FBC02D',\n    '#F9A825',\n    '#F57F17',\n  ],\n  amber: [\n    '#FFF8E1',\n    '#FFECB3',\n    '#FFE082',\n    '#FFD54F',\n    '#FFCA28',\n    '#FFC107',\n    '#FFB300',\n    '#FFA000',\n    '#FF8F00',\n    '#FF6F00',\n  ],\n  orange: [\n    '#FFF3E0',\n    '#FFE0B2',\n    '#FFCC80',\n    '#FFB74D',\n    '#FFA726',\n    '#FF9800',\n    '#FB8C00',\n    '#F57C00',\n    '#EF6C00',\n    '#E65100',\n  ],\n  deepOrange: [\n    '#FBE9E7',\n    '#FFCCBC',\n    '#FFAB91',\n    '#FF8A65',\n    '#FF7043',\n    '#FF5722',\n    '#F4511E',\n    '#E64A19',\n    '#D84315',\n    '#BF360C',\n  ],\n  brown: [\n    '#EFEBE9',\n    '#D7CCC8',\n    '#BCAAA4',\n    '#A1887F',\n    '#8D6E63',\n    '#795548',\n    '#6D4C41',\n    '#5D4037',\n    '#4E342E',\n    '#3E2723',\n  ],\n  gray: [\n    '#FAFAFA',\n    '#F5F5F5',\n    '#EEEEEE',\n    '#E0E0E0',\n    '#BDBDBD',\n    '#9E9E9E',\n    '#757575',\n    '#616161',\n    '#424242',\n    '#212121',\n  ],\n  blueGray: [\n    '#ECEFF1',\n    '#CFD8DC',\n    '#B0BEC5',\n    '#90A4AE',\n    '#78909C',\n    '#607D8B',\n    '#546E7A',\n    '#455A64',\n    '#37474F',\n    '#263238',\n  ],\n};\n", "import chartColors from '../data/colorGenerator';\nimport colorMap from '../data/colorMap';\n\nconst getCSVDataArray = (data, delimiter = ',') => {\n  return data.split('\\n').map((row) => row.split(delimiter).map((value) => normalize(value)));\n};\n\nconst getSelectedEntries = (array, a, b) => {\n  if (typeof a === 'number') {\n    return array.slice(a, b);\n  }\n\n  return array.filter((_, i) => a.indexOf(i) !== -1);\n};\n\nconst normalize = (value) => {\n  // Booleans\n  if (value === 'true' || value === true) {\n    return true;\n  }\n  if (value === 'false' || value === false) {\n    return false;\n  }\n\n  // eslint-disable-next-line\n  if (!isNaN(Number(value))) {\n    return parseFloat(value);\n  }\n\n  return value;\n};\n\nconst getColumnsFromRows = (rows) => {\n  const [row] = rows;\n\n  if (!row) {\n    return [];\n  }\n\n  return Object.keys(row);\n};\n\nfunction* colorGenerator(colors, i) {\n  const colorLibrary = {\n    ...colorMap,\n    ...chartColors,\n  };\n\n  const colorPalette = Array.isArray(colors) ? colors : colorLibrary[colors];\n\n  while (true) {\n    yield colorPalette[i];\n\n    i++;\n\n    if (i > colorPalette.length - 1) {\n      i = 0;\n    }\n  }\n}\n\nexport { colorGenerator, getCSVDataArray, getSelectedEntries, getColumnsFromRows, normalize };\n", "import { typeCheckConfig } from '../mdb/util/index';\nimport { getCSVDataArray, getSelectedEntries, normalize, getColumnsFromRows } from './util';\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'datatableStrategy';\n\nconst DEFAULT_OPTIONS = {\n  rows: {\n    start: 0,\n    end: undefined,\n    indexes: undefined,\n  },\n  columns: {\n    start: 0,\n    end: undefined,\n    indexes: undefined,\n  },\n  headerIndex: -1,\n  keys: null,\n  delimiter: ',',\n};\n\nconst OPTIONS_TYPE = {\n  columns: 'object',\n  rows: 'object',\n  headerIndex: 'number',\n  keys: '(null|array)',\n  delimiter: 'string',\n};\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass DatatableStrategy {\n  constructor(format, options = {}) {\n    this._format = format;\n    this._options = this._getConfig(options);\n  }\n\n  // Getters\n\n  // Public\n\n  parse(data) {\n    if (this._format === 'csv') {\n      return this._parseCSV(data);\n    }\n\n    return this._parseJSON(data);\n  }\n\n  getValueExtrema(data, field) {\n    const values = this._getFieldValues(data, field);\n\n    const min = Math.min(...values);\n\n    const max = Math.max(...values);\n\n    return {\n      min,\n      max,\n    };\n  }\n\n  // Private\n  _getConfig(options) {\n    const config = {\n      ...DEFAULT_OPTIONS,\n      ...options,\n    };\n\n    typeCheckConfig(NAME, config, OPTIONS_TYPE);\n\n    return config;\n  }\n\n  _parseCSV(data) {\n    const { delimiter, columns, rows, headerIndex } = this._options;\n\n    const dataArr = getCSVDataArray(data, delimiter);\n\n    const header = dataArr[headerIndex];\n\n    let computedRows = getSelectedEntries(dataArr, rows.indexes || rows.start, rows.end);\n\n    if (!header) return { rows: computedRows };\n\n    const computedColumns = getSelectedEntries(\n      header,\n      columns.indexes || columns.start,\n      columns.end\n    );\n\n    computedRows = computedRows.map((row) => {\n      return getSelectedEntries(row, columns.indexes || columns.start, columns.end);\n    });\n\n    return {\n      rows: computedRows,\n      columns: computedColumns,\n    };\n  }\n\n  _parseJSON(data) {\n    const { rows, keys } = this._options;\n\n    let computedRows = getSelectedEntries(data, rows.indexes || rows.start, rows.end).map(\n      (entry) => {\n        const output = {};\n\n        Object.keys(entry).forEach((key) => {\n          output[key] = normalize(entry[key]);\n        });\n\n        return output;\n      }\n    );\n\n    const columns = getColumnsFromRows(computedRows);\n\n    if (!keys) {\n      return {\n        columns,\n        rows: computedRows,\n      };\n    }\n\n    computedRows = computedRows.map((row) => {\n      columns.forEach((column) => {\n        if (keys.indexOf(column) === -1) {\n          delete row[column];\n        }\n      });\n\n      return row;\n    });\n\n    return {\n      columns: keys,\n      rows: computedRows,\n    };\n  }\n\n  _getFieldValues(data, field) {\n    return data.map((entry) => entry[field]);\n  }\n}\n\nexport default DatatableStrategy;\n", "import { typeCheckConfig } from '../mdb/util/index';\nimport {\n  getCSVDataArray,\n  getSelectedEntries,\n  normalize,\n  getColumnsFromRows,\n  colorGenerator,\n} from './util';\nimport colors from '../data/colorGenerator';\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'chartStrategy';\n\nconst DEFAULT_OPTIONS = {\n  rows: {\n    start: 0,\n    end: undefined,\n    indexes: undefined,\n  },\n  columns: {\n    start: 0,\n    end: undefined,\n    indexes: undefined,\n  },\n  datasetLabel: null,\n  labelsIndex: -1,\n  delimiter: ',',\n  keys: null,\n  ignoreKeys: [],\n  formatLabel: (label) => {\n    return label;\n  },\n  getCoordinates: null,\n  color: 'mdb',\n};\n\nconst OPTIONS_TYPE = {\n  datasetLabel: '(number|string|null)',\n  rows: 'object',\n  columns: 'object',\n  labelsIndex: 'number',\n  delimiter: 'string',\n  keys: '(null|array)',\n  ignoreKeys: 'array',\n  formatLabel: 'function',\n  getCoordinates: '(null|function)',\n  color: '(string|number)',\n};\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass ChartStrategy {\n  constructor(format, options = {}) {\n    this._options = this._getConfig(options);\n\n    this._format = format;\n\n    this._structure = {\n      labels: [],\n      datasets: [\n        {\n          label: '',\n          data: [],\n        },\n      ],\n    };\n  }\n\n  // Public\n\n  parse(data) {\n    if (this._format === 'csv') {\n      return this._parseCSV(data);\n    }\n\n    return this._parseJSON(data);\n  }\n\n  getValueExtrema(data, field) {\n    const values = this._getFieldValues(data, field);\n\n    const min = Math.min(...values);\n\n    const max = Math.max(...values);\n\n    return {\n      min,\n      max,\n    };\n  }\n\n  // Private\n\n  _parseCSV(data) {\n    const {\n      delimiter,\n      columns,\n      rows,\n      labelsIndex,\n      datasetLabel,\n      formatLabel,\n      color,\n      getCoordinates,\n    } = this._options;\n\n    const dataArr = getCSVDataArray(data, delimiter);\n\n    const header = dataArr[labelsIndex];\n\n    const computedRows = getSelectedEntries(dataArr, rows.indexes || rows.start, rows.end);\n\n    if (!header) return { rows: computedRows };\n\n    const labels = getSelectedEntries(header, columns.indexes || columns.start, columns.end);\n\n    const colorIterator = colorGenerator(colors[color], 0);\n\n    const datasets = computedRows.map((row) => {\n      const getData = () => {\n        const computedEntry = getSelectedEntries(\n          row,\n          columns.indexes || columns.start,\n          columns.end\n        );\n\n        if (getCoordinates) {\n          return [getCoordinates(computedEntry)];\n        }\n\n        return computedEntry;\n      };\n\n      const label = row[datasetLabel] || '';\n      const color = colorIterator.next().value;\n      const data = getData();\n\n      return {\n        label,\n        data,\n        color,\n      };\n    });\n\n    return {\n      datasets,\n      labels: labels.map((label) => formatLabel(label)),\n    };\n  }\n\n  _parseJSON(data) {\n    const { rows, keys, ignoreKeys, datasetLabel, formatLabel, color, getCoordinates } =\n      this._options;\n\n    const computedEntries = getSelectedEntries(data, rows.indexes || rows.start, rows.end).map(\n      (entry) => {\n        const output = {};\n\n        Object.keys(entry).forEach((key) => {\n          output[key] = normalize(entry[key]);\n        });\n\n        return output;\n      }\n    );\n\n    const labels =\n      keys ||\n      getColumnsFromRows(computedEntries).filter((label) => ignoreKeys.indexOf(label) === -1);\n\n    const colorIterator = colorGenerator(colors[color], 0);\n\n    const datasets = computedEntries.map((entry) => {\n      const getData = () => {\n        if (getCoordinates) {\n          return [getCoordinates(entry)];\n        }\n\n        return labels.map((label) => {\n          return entry[label] || 0;\n        });\n      };\n\n      const data = getData();\n      const label = entry[datasetLabel] || '';\n      const color = colorIterator.next().value;\n\n      return {\n        data,\n        label,\n        color,\n      };\n    });\n\n    return {\n      labels: labels.map((label) => formatLabel(label)),\n      datasets,\n    };\n  }\n\n  _getFieldValues(data, field) {\n    return data.map((entry) => entry[field]);\n  }\n\n  _getConfig(options) {\n    const config = {\n      ...DEFAULT_OPTIONS,\n      ...options,\n    };\n\n    typeCheckConfig(NAME, config, OPTIONS_TYPE);\n\n    return config;\n  }\n}\n\nexport default ChartStrategy;\n", "export default [\n  { country: 'Afghanistan', alpha2: 'AF', alpha3: 'AFG', numeric: '004' },\n  { country: 'Albania', alpha2: 'AL', alpha3: 'ALB', numeric: '008' },\n  { country: 'Algeria', alpha2: 'DZ', alpha3: 'DZA', numeric: '012' },\n  { country: 'American Samoa', alpha2: 'AS', alpha3: 'ASM', numeric: '016' },\n  { country: 'Andorra', alpha2: 'AD', alpha3: 'AND', numeric: '020' },\n  { country: 'Angola', alpha2: 'AO', alpha3: 'AGO', numeric: '024' },\n  { country: 'Anguilla', alpha2: 'AI', alpha3: 'AIA', numeric: '660' },\n  { country: 'Antarctica', alpha2: 'AQ', alpha3: 'ATA', numeric: '010' },\n  { country: 'Antigua and Barbuda', alpha2: 'AG', alpha3: 'ATG', numeric: '028' },\n  { country: 'Argentina', alpha2: 'AR', alpha3: 'ARG', numeric: '032' },\n  { country: 'Armenia', alpha2: 'AM', alpha3: 'ARM', numeric: '051' },\n  { country: 'Aruba', alpha2: 'AW', alpha3: 'ABW', numeric: '533' },\n  { country: 'Australia', alpha2: 'AU', alpha3: 'AUS', numeric: '036' },\n  { country: 'Austria', alpha2: 'AT', alpha3: 'AUT', numeric: '040' },\n  { country: 'Azerbaijan', alpha2: 'AZ', alpha3: 'AZE', numeric: '031' },\n  { country: 'Bahamas', alpha2: 'BS', alpha3: 'BHS', numeric: ' 044' },\n  { country: 'Bahrain', alpha2: 'BH', alpha3: 'BHR', numeric: '048' },\n  { country: 'Bangladesh', alpha2: 'BD', alpha3: 'BGD', numeric: '050' },\n  { country: 'Barbados', alpha2: 'BB', alpha3: 'BRB', numeric: '052' },\n  { country: 'Belarus', alpha2: 'BY', alpha3: 'BLR', numeric: '112' },\n  { country: 'Belgium', alpha2: 'BE', alpha3: 'BEL', numeric: '056' },\n  { country: 'Belize', alpha2: 'BZ', alpha3: 'BLZ', numeric: '084' },\n  { country: 'Benin', alpha2: 'BJ', alpha3: 'BEN', numeric: '204' },\n  { country: 'Bermuda', alpha2: 'BM', alpha3: 'BMU', numeric: '060' },\n  { country: 'Bhutan', alpha2: 'BT', alpha3: 'BTN', numeric: '064' },\n  { country: 'Bolivia', alpha2: 'BO', alpha3: 'BOL', numeric: '068' },\n  { country: 'Bonaire, Sint Eustatius and Saba', alpha2: 'BQ', alpha3: 'BES', numeric: '535' },\n  { country: 'Bosnia and Herzegovina', alpha2: 'BA', alpha3: 'BIH', numeric: '070' },\n  { country: 'Botswana', alpha2: 'BW', alpha3: 'BWA', numeric: '072' },\n  { country: 'Bouvet Island', alpha2: 'BV', alpha3: 'BVT', numeric: '074' },\n  { country: 'Brazil', alpha2: 'BR', alpha3: 'BRA', numeric: '076' },\n  { country: 'British Indian Ocean Territory', alpha2: 'IO', alpha3: 'IOT', numeric: '086' },\n  { country: 'Brunei Darussalam', alpha2: 'BN', alpha3: 'BRN', numeric: '096' },\n  { country: 'Bulgaria', alpha2: 'BG', alpha3: 'BGR', numeric: '100' },\n  { country: 'Burkina Faso', alpha2: 'BF', alpha3: 'BFA', numeric: '854' },\n  { country: 'Burundi', alpha2: 'BI', alpha3: 'BDI', numeric: '108' },\n  { country: 'Cabo Verde', alpha2: 'CV', alpha3: 'CPV', numeric: '132' },\n  { country: 'Cambodia', alpha2: 'KH', alpha3: 'KHM', numeric: '116' },\n  { country: 'Cameroon', alpha2: 'CM', alpha3: 'CMR', numeric: '120' },\n  { country: 'Canada', alpha2: 'CA', alpha3: 'CAN', numeric: '124' },\n  { country: 'Cayman Islands', alpha2: 'KY', alpha3: 'CYM', numeric: '136' },\n  { country: 'Central African Republic', alpha2: 'CF', alpha3: 'CAF', numeric: '140' },\n  { country: 'Chad', alpha2: 'TD', alpha3: 'TCD', numeric: '148' },\n  { country: 'Chile', alpha2: 'CL', alpha3: 'CHL', numeric: '152' },\n  { country: 'China', alpha2: 'CN', alpha3: 'CHN', numeric: '156' },\n  { country: 'Christmas Island', alpha2: 'CX', alpha3: 'CXR', numeric: '162' },\n  { country: 'Cocos Islands', alpha2: 'CC', alpha3: 'CCK', numeric: '166' },\n  { country: 'Colombia', alpha2: 'CO', alpha3: 'COL', numeric: '170' },\n  { country: 'Comoros', alpha2: 'KM', alpha3: 'COM', numeric: '174' },\n  {\n    country: 'Congo',\n    alpha2: 'CD',\n    alpha3: 'COD',\n    numeric: '180',\n  },\n  { country: 'Congo', alpha2: 'CG', alpha3: 'COG', numeric: '178' },\n  { country: 'Cook Islands', alpha2: 'CK', alpha3: 'COK', numeric: '184' },\n  { country: 'Costa Rica', alpha2: 'CR', alpha3: 'CRI', numeric: '188' },\n  { country: 'Croatia', alpha2: 'HR', alpha3: 'HRV', numeric: '191' },\n  { country: 'Cuba', alpha2: 'CU', alpha3: 'CUB', numeric: '192' },\n  { country: 'Curaçao', alpha2: 'CW', alpha3: 'CUW', numeric: '531' },\n  { country: 'Cyprus', alpha2: 'CY', alpha3: 'CYP', numeric: '196' },\n  { country: 'Czechia', alpha2: 'CZ', alpha3: 'CZE', numeric: '203' },\n  { country: \"Côte d'Ivoire\", alpha2: 'CI', alpha3: 'CIV', numeric: '384' }, // eslint-disable-line\n  { country: 'Denmark', alpha2: 'DK', alpha3: 'DNK', numeric: '208' },\n  { country: 'Djibouti', alpha2: 'DJ', alpha3: 'DJI', numeric: '262' },\n  { country: 'Dominica', alpha2: 'DM', alpha3: 'DMA', numeric: '212' },\n  { country: 'Dominican Republic', alpha2: 'DO', alpha3: 'DOM', numeric: '214' },\n  { country: 'Ecuador', alpha2: 'EC', alpha3: 'ECU', numeric: '218' },\n  { country: 'Egypt', alpha2: 'EG', alpha3: 'EGY', numeric: '818' },\n  { country: 'El Salvador', alpha2: 'SV', alpha3: 'SLV', numeric: '222' },\n  { country: 'Equatorial Guinea', alpha2: 'GQ', alpha3: 'GNQ', numeric: '226' },\n  { country: 'Eritrea', alpha2: 'ER', alpha3: 'ERI', numeric: '232' },\n  { country: 'Estonia', alpha2: 'EE', alpha3: 'EST', numeric: '233' },\n  { country: 'Eswatini', alpha2: 'SZ', alpha3: 'SWZ', numeric: '748' },\n  { country: 'Ethiopia', alpha2: 'ET', alpha3: 'ETH', numeric: '231' },\n  { country: 'Falkland Islands [Malvinas]', alpha2: 'FK', alpha3: 'FLK', numeric: '238' },\n  { country: 'Faroe Islands', alpha2: 'FO', alpha3: 'FRO', numeric: '234' },\n  { country: 'Fiji', alpha2: 'FJ', alpha3: 'FJI', numeric: '242' },\n  { country: 'Finland', alpha2: 'FI', alpha3: 'FIN', numeric: '246' },\n  { country: 'France', alpha2: 'FR', alpha3: 'FRA', numeric: '250' },\n  { country: 'French Guiana', alpha2: 'GF', alpha3: 'GUF', numeric: '254' },\n  { country: 'French Polynesia', alpha2: 'PF', alpha3: 'PYF', numeric: '258' },\n  { country: 'French Southern Territories', alpha2: 'TF', alpha3: 'ATF', numeric: '260' },\n  { country: 'Gabon', alpha2: 'GA', alpha3: 'GAB', numeric: '266' },\n  { country: 'Gambia', alpha2: 'GM', alpha3: 'GMB', numeric: '270' },\n  { country: 'Georgia', alpha2: 'GE', alpha3: 'GEO', numeric: '268' },\n  { country: 'Germany', alpha2: 'DE', alpha3: 'DEU', numeric: '276' },\n  { country: 'Ghana', alpha2: 'GH', alpha3: 'GHA', numeric: '288' },\n  { country: 'Gibraltar', alpha2: 'GI', alpha3: 'GIB', numeric: '292' },\n  { country: 'Greece', alpha2: 'GR', alpha3: 'GRC', numeric: '300' },\n  { country: 'Greenland', alpha2: 'GL', alpha3: 'GRL', numeric: '304' },\n  { country: 'Grenada', alpha2: 'GD', alpha3: 'GRD', numeric: '308' },\n  { country: 'Guadeloupe', alpha2: 'GP', alpha3: 'GLP', numeric: '312' },\n  { country: 'Guam', alpha2: 'GU', alpha3: 'GUM', numeric: '316' },\n  { country: 'Guatemala', alpha2: 'GT', alpha3: 'GTM', numeric: '320' },\n  { country: 'Guernsey', alpha2: 'GG', alpha3: 'GGY', numeric: '831' },\n  { country: 'Guinea', alpha2: 'GN', alpha3: 'GIN', numeric: '324' },\n  { country: 'Guinea-Bissau', alpha2: 'GW', alpha3: 'GNB', numeric: '624' },\n  { country: 'Guyana', alpha2: 'GY', alpha3: 'GUY', numeric: '328' },\n  { country: 'Haiti', alpha2: 'HT', alpha3: 'HTI', numeric: '332' },\n  { country: 'Heard Island and McDonald Islands', alpha2: 'HM', alpha3: 'HMD', numeric: '334' },\n  { country: 'Holy See', alpha2: 'VA', alpha3: 'VAT', numeric: '336' },\n  { country: 'Honduras', alpha2: 'HN', alpha3: 'HND', numeric: '340' },\n  { country: 'Hong Kong', alpha2: 'HK', alpha3: 'HKG', numeric: '344' },\n  { country: 'Hungary', alpha2: 'HU', alpha3: 'HUN', numeric: '348' },\n  { country: 'Iceland', alpha2: 'IS', alpha3: 'ISL', numeric: '352' },\n  { country: 'India', alpha2: 'IN', alpha3: 'IND', numeric: '356' },\n  { country: 'Indonesia', alpha2: 'ID', alpha3: 'IDN', numeric: '360' },\n  { country: 'Iran', alpha2: 'IR', alpha3: 'IRN', numeric: '364' },\n  { country: 'Iraq', alpha2: 'IQ', alpha3: 'IRQ', numeric: '368' },\n  { country: 'Ireland', alpha2: 'IE', alpha3: 'IRL', numeric: '372' },\n  { country: 'Isle of Man', alpha2: 'IM', alpha3: 'IMN', numeric: '833' },\n  { country: 'Israel', alpha2: 'IL', alpha3: 'ISR', numeric: '376' },\n  { country: 'Italy', alpha2: 'IT', alpha3: 'ITA', numeric: '380' },\n  { country: 'Jamaica', alpha2: 'JM', alpha3: 'JAM', numeric: '388' },\n  { country: 'Japan', alpha2: 'JP', alpha3: 'JPN', numeric: '392' },\n  { country: 'Jersey', alpha2: 'JE', alpha3: 'JEY', numeric: '832' },\n  { country: 'Jordan', alpha2: 'JO', alpha3: 'JOR', numeric: '400' },\n  { country: 'Kazakhstan', alpha2: 'KZ', alpha3: 'KAZ', numeric: '398' },\n  { country: 'Kenya', alpha2: 'KE', alpha3: 'KEN', numeric: '404' },\n  { country: 'Kiribati', alpha2: 'KI', alpha3: 'KIR', numeric: '296' },\n  { country: 'Korea', alpha2: 'KP', alpha3: 'PRK', numeric: '408' },\n  { country: 'Korea', alpha2: 'KR', alpha3: 'KOR', numeric: '410' },\n  { country: 'Kuwait', alpha2: 'KW', alpha3: 'KWT', numeric: '414' },\n  { country: 'Kyrgyzstan', alpha2: 'KG', alpha3: 'KGZ', numeric: '417' },\n  { country: \"Lao People's Democratic Republic\", alpha2: 'LA', alpha3: 'LAO', numeric: '418' }, // eslint-disable-line\n  { country: 'Latvia', alpha2: 'LV', alpha3: 'LVA', numeric: '428' },\n  { country: 'Lebanon', alpha2: 'LB', alpha3: 'LBN', numeric: '422' },\n  { country: 'Lesotho', alpha2: 'LS', alpha3: 'LSO', numeric: '426' },\n  { country: 'Liberia', alpha2: 'LR', alpha3: 'LBR', numeric: '430' },\n  { country: 'Libya', alpha2: 'LY', alpha3: 'LBY', numeric: '434' },\n  { country: 'Liechtenstein', alpha2: 'LI', alpha3: 'LIE', numeric: '438' },\n  { country: 'Lithuania', alpha2: 'LT', alpha3: 'LTU', numeric: '440' },\n  { country: 'Luxembourg', alpha2: 'LU', alpha3: 'LUX', numeric: '442' },\n  { country: 'Macao', alpha2: 'MO', alpha3: 'MAC', numeric: '446' },\n  { country: 'Madagascar', alpha2: 'MG', alpha3: 'MDG', numeric: '450' },\n  { country: 'Malawi', alpha2: 'MW', alpha3: 'MWI', numeric: '454' },\n  { country: 'Malaysia', alpha2: 'MY', alpha3: 'MYS', numeric: '458' },\n  { country: 'Maldives', alpha2: 'MV', alpha3: 'MDV', numeric: '462' },\n  { country: 'Mali', alpha2: 'ML', alpha3: 'MLI', numeric: '466' },\n  { country: 'Malta', alpha2: 'MT', alpha3: 'MLT', numeric: '470' },\n  { country: 'Marshall Islands', alpha2: 'MH', alpha3: 'MHL', numeric: '584' },\n  { country: 'Martinique', alpha2: 'MQ', alpha3: 'MTQ', numeric: '474' },\n  { country: 'Mauritania', alpha2: 'MR', alpha3: 'MRT', numeric: '478' },\n  { country: 'Mauritius', alpha2: 'MU', alpha3: 'MUS', numeric: '480' },\n  { country: 'Mayotte', alpha2: 'YT', alpha3: 'MYT', numeric: '175' },\n  { country: 'Mexico', alpha2: 'MX', alpha3: 'MEX', numeric: '484' },\n  { country: 'Micronesia', alpha2: 'FM', alpha3: 'FSM', numeric: '583' },\n  { country: 'Moldova', alpha2: 'MD', alpha3: 'MDA', numeric: '498' },\n  { country: 'Monaco', alpha2: 'MC', alpha3: 'MCO', numeric: '492' },\n  { country: 'Mongolia', alpha2: 'MN', alpha3: 'MNG', numeric: '496' },\n  { country: 'Montenegro', alpha2: 'ME', alpha3: 'MNE', numeric: '499' },\n  { country: 'Montserrat', alpha2: 'MS', alpha3: 'MSR', numeric: '500' },\n  { country: 'Morocco', alpha2: 'MA', alpha3: 'MAR', numeric: '504' },\n  { country: 'Mozambique', alpha2: 'MZ', alpha3: 'MOZ', numeric: '508' },\n  { country: 'Myanmar', alpha2: 'MM', alpha3: 'MMR', numeric: '104' },\n  { country: 'Namibia', alpha2: 'NA', alpha3: 'NAM', numeric: '516' },\n  { country: 'Nauru', alpha2: 'NR', alpha3: 'NRU', numeric: '520' },\n  { country: 'Nepal', alpha2: 'NP', alpha3: 'NPL', numeric: '524' },\n  { country: 'Netherlands', alpha2: 'NL', alpha3: 'NLD', numeric: '528' },\n  { country: 'New Caledonia', alpha2: 'NC', alpha3: 'NCL', numeric: '540' },\n  { country: 'New Zealand', alpha2: 'NZ', alpha3: 'NZL', numeric: '554' },\n  { country: 'Nicaragua', alpha2: 'NI', alpha3: 'NIC', numeric: '558' },\n  { country: 'Niger', alpha2: 'NE', alpha3: 'NER', numeric: '562' },\n  { country: 'Nigeria', alpha2: 'NG', alpha3: 'NGA', numeric: '566' },\n  { country: 'Niue', alpha2: 'NU', alpha3: 'NIU', numeric: '570' },\n  { country: 'Norfolk Island', alpha2: 'NF', alpha3: 'NFK', numeric: '574' },\n  { country: 'Northern Mariana Islands', alpha2: 'MP', alpha3: 'MNP', numeric: '580' },\n  { country: 'Norway', alpha2: 'NO', alpha3: 'NOR', numeric: '578' },\n  { country: 'Oman', alpha2: 'OM', alpha3: 'OMN', numeric: '512' },\n  { country: 'Pakistan', alpha2: 'PK', alpha3: 'PAK', numeric: '586' },\n  { country: 'Palau', alpha2: 'PW', alpha3: 'PLW', numeric: '585' },\n  { country: 'Palestine, State of', alpha2: 'PS', alpha3: 'PSE', numeric: '275' },\n  { country: 'Panama', alpha2: 'PA', alpha3: 'PAN', numeric: '591' },\n  { country: 'Papua New Guinea', alpha2: 'PG', alpha3: 'PNG', numeric: '598' },\n  { country: 'Paraguay', alpha2: 'PY', alpha3: 'PRY', numeric: '600' },\n  { country: 'Peru', alpha2: 'PE', alpha3: 'PER', numeric: '604' },\n  { country: 'Philippines', alpha2: 'PH', alpha3: 'PHL', numeric: '608' },\n  { country: 'Pitcairn', alpha2: 'PN', alpha3: 'PCN', numeric: '612' },\n  { country: 'Poland', alpha2: 'PL', alpha3: 'POL', numeric: '616' },\n  { country: 'Portugal', alpha2: 'PT', alpha3: 'PRT', numeric: '620' },\n  { country: 'Puerto Rico', alpha2: 'PR', alpha3: 'PRI', numeric: '630' },\n  { country: 'Qatar', alpha2: 'QA', alpha3: 'QAT', numeric: '634' },\n  { country: 'Republic of North Macedonia', alpha2: 'MK', alpha3: 'MKD', numeric: '807' },\n  { country: 'Romania', alpha2: 'RO', alpha3: 'ROU', numeric: '642' },\n  { country: 'Russian Federation', alpha2: 'RU', alpha3: 'RUS', numeric: '643' },\n  { country: 'Rwanda', alpha2: 'RW', alpha3: 'RWA', numeric: '646' },\n  { country: 'Réunion', alpha2: 'RE', alpha3: 'REU', numeric: '638' },\n  { country: 'Saint Barthélemy', alpha2: 'BL', alpha3: 'BLM', numeric: '652' },\n  {\n    country: 'Saint Helena, Ascension and Tristan da Cunha',\n    alpha2: 'SH',\n    alpha3: 'SHN',\n    numeric: '654',\n  },\n  { country: 'Saint Kitts and Nevis', alpha2: 'KN', alpha3: 'KNA', numeric: '659' },\n  { country: 'Saint Lucia', alpha2: 'LC', alpha3: 'LCA', numeric: '662' },\n  { country: 'Saint Martin', alpha2: 'MF', alpha3: 'MAF', numeric: '663' },\n  { country: 'Saint Pierre and Miquelon', alpha2: 'PM', alpha3: 'SPM', numeric: '666' },\n  { country: 'Saint Vincent and the Grenadines', alpha2: 'VC', alpha3: 'VCT', numeric: '670' },\n  { country: 'Samoa', alpha2: 'WS', alpha3: 'WSM', numeric: '882' },\n  { country: 'San Marino', alpha2: 'SM', alpha3: 'SMR', numeric: '674' },\n  { country: 'Sao Tome and Principe', alpha2: 'ST', alpha3: 'STP', numeric: '678' },\n  { country: 'Saudi Arabia', alpha2: 'SA', alpha3: 'SAU', numeric: '682' },\n  { country: 'Senegal', alpha2: 'SN', alpha3: 'SEN', numeric: '686' },\n  { country: 'Serbia', alpha2: 'RS', alpha3: 'SRB', numeric: '688' },\n  { country: 'Seychelles', alpha2: 'SC', alpha3: 'SYC', numeric: '690' },\n  { country: 'Sierra Leone', alpha2: 'SL', alpha3: 'SLE', numeric: '694' },\n  { country: 'Singapore', alpha2: 'SG', alpha3: 'SGP', numeric: '702' },\n  { country: 'Sint Maarten', alpha2: 'SX', alpha3: 'SXM', numeric: '534' },\n  { country: 'Slovakia', alpha2: 'SK', alpha3: 'SVK', numeric: '703' },\n  { country: 'Slovenia', alpha2: 'SI', alpha3: 'SVN', numeric: '705' },\n  { country: 'Solomon Islands', alpha2: 'SB', alpha3: 'SLB', numeric: '090' },\n  { country: 'Somalia', alpha2: 'SO', alpha3: 'SOM', numeric: '706' },\n  { country: 'South Africa', alpha2: 'ZA', alpha3: 'ZAF', numeric: '710' },\n  {\n    country: 'South Georgia and the South Sandwich Islands',\n    alpha2: 'GS',\n    alpha3: 'SGS',\n    numeric: '239',\n  },\n  { country: 'South Sudan', alpha2: 'SS', alpha3: 'SSD', numeric: '728' },\n  { country: 'Spain', alpha2: 'ES', alpha3: 'ESP', numeric: '724' },\n  { country: 'Sri Lanka', alpha2: 'LK', alpha3: 'LKA', numeric: '144' },\n  { country: 'Sudan', alpha2: 'SD', alpha3: 'SDN', numeric: '729' },\n  { country: 'Suriname', alpha2: 'SR', alpha3: 'SUR', numeric: '740' },\n  { country: 'Svalbard and Jan Mayen', alpha2: 'SJ', alpha3: 'SJM', numeric: '744' },\n  { country: 'Sweden', alpha2: 'SE', alpha3: 'SWE', numeric: '752' },\n  { country: 'Switzerland', alpha2: 'CH', alpha3: 'CHE', numeric: '756' },\n  { country: 'Syrian Arab Republic', alpha2: 'SY', alpha3: 'SYR', numeric: '760' },\n  { country: 'Taiwan', alpha2: 'TW', alpha3: 'TWN', numeric: '158' },\n  { country: 'Tajikistan', alpha2: 'TJ', alpha3: 'TJK', numeric: '762' },\n  { country: 'Tanzania, United Republic of', alpha2: 'TZ', alpha3: 'TZA', numeric: '834' },\n  { country: 'Thailand', alpha2: 'TH', alpha3: 'THA', numeric: '764' },\n  { country: 'Timor-Leste', alpha2: 'TL', alpha3: 'TLS', numeric: '626' },\n  { country: 'Togo', alpha2: 'TG', alpha3: 'TGO', numeric: '768' },\n  { country: 'Tokelau', alpha2: 'TK', alpha3: 'TKL', numeric: '772' },\n  { country: 'Tonga', alpha2: 'TO', alpha3: 'TON', numeric: '776' },\n  { country: 'Trinidad and Tobago', alpha2: 'TT', alpha3: 'TTO', numeric: '780' },\n  { country: 'Tunisia', alpha2: 'TN', alpha3: 'TUN', numeric: '788' },\n  { country: 'Turkey', alpha2: 'TR', alpha3: 'TUR', numeric: '792' },\n  { country: 'Turkmenistan', alpha2: 'TM', alpha3: 'TKM', numeric: '795' },\n  { country: 'Turks and Caicos Islands', alpha2: 'TC', alpha3: 'TCA', numeric: '796' },\n  { country: 'Tuvalu', alpha2: 'TV', alpha3: 'TUV', numeric: '798' },\n  { country: 'Uganda', alpha2: 'UG', alpha3: 'UGA', numeric: '800' },\n  { country: 'Ukraine', alpha2: 'UA', alpha3: 'UKR', numeric: '804' },\n  { country: 'United Arab Emirates', alpha2: 'AE', alpha3: 'ARE', numeric: '784' },\n  {\n    country: 'United Kingdom',\n    alpha2: 'GB',\n    alpha3: 'GBR',\n    numeric: '826',\n  },\n  { country: 'United States Minor Outlying Islands', alpha2: 'UM', alpha3: 'UMI', numeric: '581' },\n  { country: 'United States of America', alpha2: 'US', alpha3: 'USA', numeric: '840' },\n  { country: 'Uruguay', alpha2: 'UY', alpha3: 'URY', numeric: '858' },\n  { country: 'Uzbekistan', alpha2: 'UZ', alpha3: 'UZB', numeric: '860' },\n  { country: 'Vanuatu', alpha2: 'VU', alpha3: 'VUT', numeric: '548' },\n  { country: 'Venezuela', alpha2: 'VE', alpha3: 'VEN', numeric: '862' },\n  { country: 'Viet Nam', alpha2: 'VN', alpha3: 'VNM', numeric: '704' },\n  { country: 'Virgin Islands', alpha2: 'VG', alpha3: 'VGB', numeric: '092' },\n  { country: 'Virgin Islands', alpha2: 'VI', alpha3: 'VIR', numeric: '850' },\n  { country: 'Wallis and Futuna', alpha2: 'WF', alpha3: 'WLF', numeric: '876' },\n  { country: 'Western Sahara', alpha2: 'EH', alpha3: 'ESH', numeric: '732' },\n  { country: 'Yemen', alpha2: 'YE', alpha3: 'YEM', numeric: '887' },\n  { country: 'Zambia', alpha2: 'ZM', alpha3: 'ZMB', numeric: '894' },\n  { country: 'Zimbabwe', alpha2: 'ZW', alpha3: 'ZWE', numeric: '716' },\n  { country: 'Åland Islands', alpha2: 'AX', alpha3: 'ALA', numeric: '248' },\n];\n", "export default [\n  {\n    x: 475,\n    y: 294,\n    type: 'bullet',\n    fill: 'rgb(185, 211, 220)',\n    label: 'London',\n    latitude: 51.5002,\n    longitude: -0.1262,\n  },\n  {\n    x: 510,\n    y: 275,\n    type: 'bullet',\n    fill: 'rgb(185, 211, 220)',\n    label: 'Copenhagen',\n    latitude: 55.6763,\n    longitude: 12.5681,\n  },\n  {\n    x: 487,\n    y: 297,\n    fill: 'rgb(185, 211, 220)',\n    type: 'bullet',\n    label: 'Brussels',\n    latitude: 50.8371,\n    longitude: 4.3676,\n  },\n  {\n    x: 414,\n    y: 227,\n    fill: 'rgb(185, 211, 220)',\n    type: 'bullet',\n    label: 'Reykjavik',\n    latitude: 64.1353,\n    longitude: -21.8952,\n  },\n  {\n    x: 481,\n    y: 306,\n    fill: 'rgb(185, 211, 220)',\n    type: 'bullet',\n    label: 'Paris',\n    latitude: 48.8567,\n    longitude: 2.351,\n  },\n  {\n    x: 581,\n    y: 275,\n    fill: 'rgb(185, 211, 220)',\n    type: 'bullet',\n    label: 'Moscow',\n    latitude: 55.7558,\n    longitude: 37.6176,\n  },\n  {\n    x: 465,\n    y: 340,\n    fill: 'rgb(185, 211, 220)',\n    type: 'bullet',\n    label: 'Madrid',\n    latitude: 40.4167,\n    longitude: -3.7033,\n  },\n  {\n    x: 690,\n    y: 380,\n    fill: 'rgb(185, 211, 220)',\n    type: 'bullet',\n    label: 'New Delhi',\n    latitude: 28.6353,\n    longitude: 77.225,\n  },\n  {\n    x: 867,\n    y: 356,\n    r: 0.2,\n    fill: 'rgb(185, 211, 220)',\n    type: 'bullet',\n    label: 'Tokyo',\n    latitude: 35.6785,\n    longitude: 139.6823,\n  },\n  {\n    x: 259,\n    y: 345,\n    fill: 'rgb(185, 211, 220)',\n    type: 'bullet',\n    label: 'Washington',\n    latitude: 38.8921,\n    longitude: -77.0241,\n  },\n  {\n    x: 881.5,\n    y: 577,\n    fill: 'rgb(185, 211, 220)',\n    type: 'bullet',\n    label: 'Melbourne',\n    latitude: -37.813629,\n    longitude: 144.963058,\n  },\n  {\n    x: 566,\n    y: 342,\n    fill: 'rgb(185, 211, 220)',\n    type: 'bullet',\n    label: 'Ankara',\n    latitude: 39.9439,\n    longitude: 32.856,\n  },\n  {\n    x: 311,\n    y: 566,\n    fill: 'rgb(185, 211, 220)',\n    type: 'bullet',\n    label: 'Buenos Aires',\n    latitude: -34.6118,\n    longitude: -58.4173,\n  },\n  {\n    x: 343,\n    y: 506,\n    fill: 'rgb(185, 211, 220)',\n    type: 'bullet',\n    label: 'Brasilia',\n    latitude: -15.7801,\n    longitude: -47.9292,\n  },\n  {\n    x: 262,\n    y: 321,\n    fill: 'rgb(185, 211, 220)',\n    type: 'bullet',\n    label: 'Ottawa',\n    latitude: 45.4235,\n    longitude: -75.6979,\n  },\n  {\n    x: 518,\n    y: 475.5,\n    fill: 'rgb(185, 211, 220)',\n    type: 'bullet',\n    label: 'Kinshasa',\n    latitude: -4.3369,\n    longitude: 15.3271,\n  },\n  {\n    x: 563,\n    y: 375,\n    fill: 'rgb(185, 211, 220)',\n    type: 'bullet',\n    label: 'Cairo',\n    latitude: 30.0571,\n    longitude: 31.2272,\n  },\n  {\n    x: 553,\n    y: 537,\n    fill: 'rgb(185, 211, 220)',\n    type: 'bullet',\n    label: 'Pretoria',\n    latitude: -25.7463,\n    longitude: 28.1876,\n  },\n  {\n    x: 425.9,\n    y: 421.4,\n    fill: 'rgb(185, 211, 220)',\n    type: 'bullet',\n    label: 'Dakar',\n    latitude: 14.693425,\n    longitude: -17.447938,\n  },\n  {\n    x: 586.5,\n    y: 473,\n    fill: 'rgb(185, 211, 220)',\n    type: 'bullet',\n    label: 'Mombasa',\n    latitude: -4.05052,\n    longitude: 39.667169,\n  },\n  {\n    x: 561,\n    y: 470,\n    fill: 'rgb(185, 211, 220)',\n    type: 'bullet',\n    label: 'Ngara',\n    latitude: -1.2832533,\n    longitude: 36.8172449,\n  },\n  {\n    x: 563,\n    y: 450,\n    fill: 'rgb(185, 211, 220)',\n    type: 'bullet',\n    label: 'Juba',\n    latitude: 4.8472017,\n    longitude: 31.5951655,\n  },\n  {\n    x: 484,\n    y: 444.5,\n    fill: 'rgb(185, 211, 220)',\n    type: 'bullet',\n    label: 'Lagos',\n    latitude: 6.4550575,\n    longitude: 3.3941795,\n  },\n  {\n    x: 533.2664953167957,\n    y: 290.67072055266874,\n    fill: 'rgb(185, 211, 220)',\n    type: 'bullet',\n    label: 'Warsaw',\n    latitude: 52.2319581,\n    longitude: 21.0067249,\n  },\n  {\n    x: 512.2629523880797,\n    y: 289.3736995043222,\n    fill: 'rgb(185, 211, 220)',\n    type: 'bullet',\n    label: 'Berlin',\n    latitude: 52.5170365,\n    longitude: 13.3888599,\n  },\n  {\n    x: 510,\n    y: 333.8,\n    fill: 'rgb(185, 211, 220)',\n    type: 'bullet',\n    label: 'Rome',\n    latitude: 41.8933203,\n    longitude: 12.4829321,\n  },\n  {\n    x: 528.5,\n    y: 188,\n    fill: 'rgb(185, 211, 220)',\n    type: 'bullet',\n    label: 'Tromso',\n    latitude: 69.649208,\n    longitude: 18.9543434,\n  },\n  {\n    x: 544,\n    y: 251,\n    fill: 'rgb(185, 211, 220)',\n    type: 'bullet',\n    label: 'Helsinki',\n    latitude: 60.1674881,\n    longitude: 24.9427473,\n  },\n  {\n    x: 504.9568273555848,\n    y: 253,\n    fill: 'rgb(185, 211, 220)',\n    type: 'bullet',\n    label: 'Oslo',\n    latitude: 59.9133301,\n    longitude: 10.7389701,\n  },\n  {\n    x: 525.1725398407159,\n    y: 256,\n    fill: 'rgb(185, 211, 220)',\n    type: 'bullet',\n    label: 'Stockholm',\n    latitude: 59.3251172,\n    longitude: 18.0710935,\n  },\n  {\n    x: 547,\n    y: 175,\n    fill: 'rgb(185, 211, 220)',\n    type: 'bullet',\n    label: 'Nordkapp',\n    latitude: 71.1699506,\n    longitude: 25.7858893,\n  },\n  {\n    x: 561,\n    y: 298.7779415243888,\n    fill: 'rgb(185, 211, 220)',\n    type: 'bullet',\n    label: 'Kyiv',\n    latitude: 50.4500336,\n    longitude: 30.5241361,\n  },\n  {\n    x: 668,\n    y: 346.5,\n    fill: 'rgb(185, 211, 220)',\n    type: 'bullet',\n    label: 'Dushanbe',\n    latitude: 38.5762709,\n    longitude: 68.7863573,\n  },\n  {\n    x: 680,\n    y: 408,\n    fill: 'rgb(185, 211, 220)',\n    type: 'bullet',\n    label: 'Mumbai',\n    latitude: 19.0759899,\n    longitude: 72.8773928,\n  },\n  {\n    x: 699,\n    y: 442.5,\n    fill: 'rgb(185, 211, 220)',\n    type: 'bullet',\n    label: 'Colombo',\n    latitude: 6.9349969,\n    longitude: 79.8538463,\n  },\n  {\n    x: 714,\n    y: 382.3,\n    fill: 'rgb(185, 211, 220)',\n    type: 'bullet',\n    label: 'Kathmandu',\n    latitude: 27.708317,\n    longitude: 85.3205817,\n  },\n  {\n    x: 815.5,\n    y: 371,\n    fill: 'rgb(185, 211, 220)',\n    type: 'bullet',\n    label: 'Shanghai',\n    latitude: 31.2322758,\n    longitude: 121.4692071,\n  },\n  {\n    x: 800,\n    y: 338,\n    fill: 'rgb(185, 211, 220)',\n    type: 'bullet',\n    label: 'Beijing',\n    latitude: 39.906217,\n    longitude: 116.3912757,\n  },\n  {\n    x: 898.2,\n    y: 565,\n    fill: 'rgb(185, 211, 220)',\n    type: 'bullet',\n    label: 'Sydney',\n    latitude: -33.8548157,\n    longitude: 151.2164539,\n  },\n  {\n    x: 801,\n    y: 556,\n    fill: 'rgb(185, 211, 220)',\n    type: 'bullet',\n    label: 'Perth',\n    latitude: -31.9527121,\n    longitude: 115.8604796,\n  },\n  {\n    x: 966,\n    y: 574,\n    fill: 'rgb(185, 211, 220)',\n    type: 'bullet',\n    label: 'Auckland',\n    latitude: -36.852095,\n    longitude: 174.7631803,\n  },\n  {\n    x: 54.5,\n    y: 245,\n    fill: 'rgb(185, 211, 220)',\n    type: 'bullet',\n    label: 'Anchorage',\n    latitude: 61.2163129,\n    longitude: -149.8948523,\n  },\n  {\n    x: 129.5,\n    y: 304.5,\n    fill: 'rgb(185, 211, 220)',\n    type: 'bullet',\n    label: 'Vancouver',\n    latitude: 49.2608724,\n    longitude: -123.1139529,\n  },\n  {\n    x: 143,\n    y: 362,\n    fill: 'rgb(185, 211, 220)',\n    type: 'bullet',\n    label: 'Los Angeles',\n    latitude: 34.0536909,\n    longitude: -118.242766,\n  },\n  {\n    x: 256.0608456551366,\n    y: 487.4930349608487,\n    fill: 'rgb(185, 211, 220)',\n    type: 'bullet',\n    label: 'Panama City',\n    latitude: 8.9714493,\n    longitude: -79.5341802,\n  },\n  {\n    x: 276,\n    y: 560,\n    fill: 'rgb(185, 211, 220)',\n    type: 'bullet',\n    label: 'Santiago',\n    latitude: 9.8694792,\n    longitude: -83.7980749,\n  },\n  {\n    x: 526.7,\n    y: 563.5,\n    fill: 'rgb(185, 211, 220)',\n    type: 'bullet',\n    label: 'Cape Town',\n    latitude: -33.928992,\n    longitude: 18.417396,\n  },\n  {\n    x: 330,\n    y: 226.7,\n    fill: 'rgb(185, 211, 220)',\n    type: 'bullet',\n    label: 'Nuuk',\n    latitude: 64.175029,\n    longitude: -51.7355386,\n  },\n];\n", "import { typeCheckConfig } from '../mdb/util/index';\nimport COUNTRY_DATA from '../data/countryCodes';\nimport COLOR_MAP from '../data/colorMap';\n\nimport { getCSVDataArray, getSelectedEntries } from './util';\n\nimport MARKERS from '../data/markers';\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'vectorMapStrategy';\nconst DEFAULT_OPTIONS = {\n  field: null,\n  color: 'blue',\n  countries: undefined,\n  countryIdentifier: null,\n  rows: {\n    start: 0,\n    end: undefined,\n    indexes: undefined,\n  },\n  headerIndex: -1,\n  delimiter: ',',\n  tooltips: () => null,\n};\nconst OPTIONS_TYPE = {\n  field: '(number|string|null)',\n  color: '(string|array)',\n  countryIdentifier: '(number|string|null)',\n  rows: 'object',\n  headerIndex: 'number',\n  delimiter: 'string',\n  tooltips: 'function',\n};\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass VectorMapStrategy {\n  constructor(format, options = {}) {\n    this._options = this._getConfig(options);\n    this._format = format;\n\n    this._colorMap = this._setColorMap();\n  }\n\n  // Getters\n\n  // Public\n\n  parse(data) {\n    return this._parseArrayData(this._getDataArray(data));\n  }\n\n  getIdentifiers(data) {\n    const entries = this._getEntries(this._getDataArray(data));\n\n    return entries.map((entry) => this._getAlpha2Code(entry));\n  }\n\n  getValueExtrema(data, field) {\n    const values = this._getFieldValues(this._getEntries(this._getDataArray(data)), field);\n\n    return this._getBoundryValues(values);\n  }\n\n  getMapCoordinates(latitude, longitude) {\n    const closestYPoints = this._getClosestPoints(latitude, 'latitude');\n    const closestXPoints = this._getClosestPoints(longitude, 'longitude');\n\n    return {\n      x: this._getCoordinate(closestXPoints, latitude, 'x'),\n      y: this._getCoordinate(closestYPoints, longitude, 'y'),\n    };\n  }\n\n  // Private\n  _getClosestPoints(value, coordinate) {\n    const points = MARKERS.sort((a, b) => {\n      const value = a[coordinate] - b[coordinate];\n      if (value < 0) return -1;\n      if (value > 0) return 1;\n      return 0;\n    });\n\n    const result1 = points.reduce((a, b, i, markers) => {\n      return i && Math.abs(markers[a][coordinate] - value) < Math.abs(b[coordinate] - value)\n        ? a\n        : i;\n    }, -1);\n\n    const points2 = points.filter((marker) => marker !== points[result1]);\n\n    const result2 = points2.reduce((a, b, i, markers) => {\n      return i && Math.abs(markers[a][coordinate] - value) < Math.abs(b[coordinate] - value)\n        ? a\n        : i;\n    }, -1);\n\n    const point1 = points[result1];\n    const point2 = points2[result2];\n\n    return {\n      point1,\n      point2,\n    };\n  }\n\n  _getCoordinate({ point1, point2 }, value, axis) {\n    const coordinate = axis === 'x' ? 'latitude' : 'longitude';\n\n    const coordinateDiff1 = point1[coordinate] - value;\n\n    const coordinateDiff2 = point2[coordinate] - value;\n\n    const searchedValue =\n      (coordinateDiff2 * point1[axis] - coordinateDiff1 * point2[axis]) /\n      (coordinateDiff2 - coordinateDiff1);\n\n    return searchedValue;\n  }\n\n  _parseArrayData(data) {\n    return this._generateColorCodes(this._getEntries(data));\n  }\n\n  _getDataArray(data) {\n    if (this._format === 'csv') {\n      const { delimiter } = this._options;\n\n      return getCSVDataArray(data, delimiter);\n    }\n\n    return data;\n  }\n\n  _getEntries(data) {\n    const { rows, countries } = this._options;\n\n    return countries\n      ? this._getSelectedCountries(data)\n      : getSelectedEntries(data, rows.indexes || rows.start, rows.end);\n  }\n\n  _getConfig(options) {\n    const config = {\n      ...DEFAULT_OPTIONS,\n      ...options,\n    };\n\n    typeCheckConfig(NAME, config, OPTIONS_TYPE);\n\n    return config;\n  }\n\n  _getBoundryValues(array) {\n    return {\n      max: Math.max(...array),\n      min: Math.min(...array),\n    };\n  }\n\n  _getSelectedCountries(array) {\n    const { countryIdentifier: identifier, countries } = this._options;\n\n    return array.filter((entry) => {\n      return countries.indexOf(entry[identifier]) !== -1;\n    });\n  }\n\n  _generateColorCodes(data) {\n    const { field, step: fixedStep } = this._options;\n    const intervals = this._colorMap.length;\n\n    const values = data.map((entry) => entry[field]);\n\n    const { min, max } = this._getBoundryValues(values);\n\n    const step = fixedStep || Math.floor((max - min) / intervals);\n\n    const legend = this._colorMap.map((color, i) => {\n      const minValue = min + i * step;\n      let maxValue = minValue + step;\n\n      if (i === intervals - 1) {\n        maxValue = max;\n      }\n\n      return {\n        color,\n        min: minValue,\n        max: maxValue,\n      };\n    });\n\n    const colorMap = this._colorMap.map((color) => ({\n      fill: color,\n      regions: [],\n    }));\n\n    values.forEach((value, i) => {\n      const interval = Math.floor((value - min) / step);\n\n      const index = interval < intervals ? interval : intervals - 1;\n\n      const alpha2Code = this._getAlpha2Code(data[i]);\n\n      if (!alpha2Code) return;\n\n      colorMap[index].regions.push({\n        id: alpha2Code,\n        tooltip: this._options.tooltips(value),\n      });\n    });\n\n    return { colorMap, legend };\n  }\n\n  _getAlpha2Code(entry) {\n    const { countryIdentifier: identifier } = this._options;\n\n    const findCountry = (value, key) => {\n      return COUNTRY_DATA.find((country) => country[key].toLowerCase().match(value.toLowerCase()));\n    };\n\n    let key;\n\n    switch (entry[identifier].length) {\n      case 2:\n        key = 'alpha2';\n        break;\n      case 3:\n        key = 'alpha3';\n        break;\n      default:\n        key = 'country';\n    }\n\n    const country = findCountry(entry[identifier], key);\n\n    if (!country) {\n      return null;\n    }\n\n    return country.alpha2;\n  }\n\n  _getFieldValues(data, field) {\n    return data.map((entry) => entry[field]);\n  }\n\n  _setColorMap() {\n    const { color } = this._options;\n\n    if (Array.isArray(color)) return color;\n\n    const colorMap = COLOR_MAP[color];\n\n    if (!colorMap) {\n      throw new Error(`Color ${color} not found.`);\n    }\n\n    return colorMap;\n  }\n}\n\nexport default VectorMapStrategy;\n", "import { typeCheckConfig } from '../mdb/util/index';\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'treeviewStrategy';\n\nconst DEFAULT_OPTIONS = {\n  name: 'name',\n  children: 'children',\n  icon: null,\n  show: false,\n  disabled: false,\n  id: null,\n};\nconst OPTIONS_TYPE = {\n  name: '(string|function)',\n  children: 'string',\n  icon: '(null|function|string)',\n  show: '(function|boolean)',\n  disabled: '(function|boolean)',\n  id: '(null|number|string)',\n};\n\nconst TREEVIEW_KEYS = ['name', 'children', 'show', 'disabled', 'id', 'icon'];\n\nconst FUNCTION_KEYS = ['icon', 'disabled', 'show', 'name'];\n\nconst REFERENCE_KEYS = ['children', 'name'];\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass TreeviewStrategy {\n  constructor(format, options = {}) {\n    this._options = this._getConfig(options);\n\n    this._format = format;\n\n    this._data = [];\n\n    this._structure = [];\n\n    this._functionKeys = FUNCTION_KEYS.filter((key) => typeof this._options[key] === 'function');\n\n    this._referenceKeys = REFERENCE_KEYS.filter((key) => typeof key === 'string');\n  }\n\n  // Public\n\n  parse(data) {\n    this._data = data;\n\n    return this._parseStructure(data);\n  }\n\n  // Private\n\n  _parseStructure(structure) {\n    return structure.map((el) => {\n      return this._parseNode(el);\n    });\n  }\n\n  _parseNode(el) {\n    const output = {};\n\n    TREEVIEW_KEYS.forEach((key) => {\n      if (this._functionKeys.includes(key)) {\n        output[key] = this._options[key](el);\n      } else if (this._referenceKeys.includes(key)) {\n        if (!el[this._options[key]]) {\n          return;\n        }\n\n        if (key === 'children') {\n          output.children = this._parseStructure(el[this._options[key]]);\n        } else {\n          output[key] = el[this._options[key]];\n        }\n      } else {\n        output[key] = this._options[key];\n      }\n    });\n\n    return output;\n  }\n\n  _getConfig(options) {\n    const config = {\n      ...DEFAULT_OPTIONS,\n      ...options,\n    };\n\n    typeCheckConfig(NAME, config, OPTIONS_TYPE);\n\n    return config;\n  }\n}\n\nexport default TreeviewStrategy;\n", "const flattenDeep = (array = []) => {\n  return array.reduce(\n    (acc, val) => (Array.isArray(val) ? acc.concat(flattenDeep(val)) : acc.concat(val)),\n    []\n  );\n};\n\nconst pullAll = (array = [], items = []) => {\n  items.forEach((item) => {\n    for (let i = 0; i < array.length; i++) {\n      if (array[i] === item) {\n        array.splice(i, 1);\n      } else {\n        continue;\n      }\n    }\n  });\n\n  return array;\n};\n\nconst take = (array = [], items = 1) => {\n  if (array.length < items) {\n    return array;\n  }\n\n  const output = array.slice(0, items);\n\n  return output;\n};\n\nconst takeRight = (array = [], items = 1) => {\n  if (array.length < items) {\n    return array;\n  }\n\n  const output = array.slice(array.length - items, array.length);\n\n  return output;\n};\n\nconst union = (...args) => {\n  const output = [];\n\n  args.forEach((arg) => {\n    const value = Array.isArray(arg) ? arg : new Array(arg);\n\n    output.push(value);\n  });\n\n  return uniq(flattenDeep(output));\n};\n\nconst unionBy = (value, ...args) => {\n  const array = flattenDeep(new Array(...args));\n\n  return uniqBy(value, array);\n};\n\nconst uniq = (array = []) => {\n  return [...new Set(array)];\n};\n\nconst uniqBy = (value, array = []) => {\n  const flattenArray = flattenDeep(array);\n  const values = [];\n  const output = [];\n\n  const type = typeof value;\n\n  switch (type) {\n    case 'function':\n      for (let i = 0; i < flattenArray.length; i++) {\n        if (values.includes(value(flattenArray[i]))) {\n          continue;\n        }\n\n        values.push(value(flattenArray[i]));\n        output.push(flattenArray[i]);\n      }\n      break;\n    case 'string':\n      for (let i = 0; i < flattenArray.length; i++) {\n        if (values.includes(flattenArray[i][value])) {\n          continue;\n        }\n\n        values.push(flattenArray[i][value]);\n        output.push(flattenArray[i]);\n      }\n      break;\n    default:\n      throw new Error('Invalid iteratee parameter type');\n  }\n\n  return output;\n};\n\nconst zip = (...args) => {\n  const output = [];\n\n  const lengths = args.map((item) => item.length);\n\n  const maxLength = lengths.reduce((a, b) => Math.max(a, b));\n\n  for (let i = 0; i < maxLength; i++) {\n    output[i] = [];\n    args.forEach((array) => {\n      output[i].push(array[i]);\n    });\n  }\n\n  return output;\n};\n\nconst zipObject = (keys = [], values = []) => {\n  const output = {};\n\n  keys.forEach((key, index) => {\n    output[key] = values[index];\n  });\n\n  return output;\n};\n\nexport { flattenDeep, pullAll, take, takeRight, union, unionBy, uniq, uniqBy, zip, zipObject };\n", "import DatatableStrategy from './strategy/datatable';\nimport ChartStrategy from './strategy/chart';\nimport VectorMapStrategy from './strategy/vector-map';\nimport TreeviewStrategy from './strategy/treeview';\nimport {\n  flattenDeep,\n  pullAll,\n  take,\n  takeRight,\n  union,\n  unionBy,\n  uniq,\n  uniqBy,\n  zip,\n  zipObject,\n} from './utils/arrays';\n\nimport { countBy, groupBy, sortBy, orderBy } from './utils/collections';\nimport { invert, invertBy, omit, omitBy, pick, pickBy, transform } from './utils/objects';\nimport { colorGenerator, getCSVDataArray } from './strategy/util';\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'dataParser';\n\nconst STRATEGY_VECTOR_MAP = 'vectorMap';\nconst STRATEGY_DATATABLE = 'datatable';\nconst STRATEGY_CHART = 'chart';\nconst STRATEGY_TREEVIEW = 'treeview';\n\nconst PARSER_STRATEGY = {\n  [STRATEGY_DATATABLE]: DatatableStrategy,\n  [STRATEGY_CHART]: ChartStrategy,\n  [STRATEGY_VECTOR_MAP]: VectorMapStrategy,\n  [STRATEGY_TREEVIEW]: TreeviewStrategy,\n};\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass DataParser {\n  constructor(strategy = STRATEGY_DATATABLE, format = 'json', options) {\n    this._strategy = strategy;\n    this._format = format;\n    this._options = options;\n\n    this._parser = this._setupStrategy();\n  }\n\n  // Public\n  parse(data) {\n    return this._parser.parse(data);\n  }\n\n  getValueExtrema(data, field) {\n    return this._parser.getValueExtrema(data, field);\n  }\n\n  // Vector Map\n\n  getRegionIdentifiers(data) {\n    if (this._strategy !== STRATEGY_VECTOR_MAP) {\n      throw new Error(`This method is not available for ${this._strategy} strategy`);\n    }\n\n    return this._parser.getIdentifiers(data);\n  }\n\n  getMapCoordinates(latitude, longitude) {\n    return this._parser.getMapCoordinates(latitude, longitude);\n  }\n\n  // Private\n\n  _setupStrategy() {\n    if (!PARSER_STRATEGY[this._strategy]) {\n      throw new Error(`Parser strategy ${this._strategy} not found`);\n    }\n\n    return new PARSER_STRATEGY[this._strategy](this._format, this._options);\n  }\n\n  // Static\n  static get NAME() {\n    return NAME;\n  }\n\n  // Array utils\n\n  static flattenDeep(...args) {\n    return flattenDeep(args);\n  }\n\n  static pullAll(...args) {\n    return pullAll(...args);\n  }\n\n  static take(...args) {\n    return take(...args);\n  }\n\n  static takeRight(...args) {\n    return takeRight(...args);\n  }\n\n  static union(...args) {\n    return union(...args);\n  }\n\n  static unionBy(...args) {\n    return unionBy(...args);\n  }\n\n  static uniq(...args) {\n    return uniq(...args);\n  }\n\n  static uniqBy(...args) {\n    return uniqBy(...args);\n  }\n\n  static zip(...args) {\n    return zip(...args);\n  }\n\n  static zipObject(...args) {\n    return zipObject(...args);\n  }\n\n  // Collection utils\n\n  static countBy(...args) {\n    return countBy(...args);\n  }\n\n  static groupBy(...args) {\n    return groupBy(...args);\n  }\n\n  static sortBy(...args) {\n    return sortBy(...args);\n  }\n\n  static orderBy(...args) {\n    return orderBy(...args);\n  }\n\n  // Object utils\n\n  static invert(...args) {\n    return invert(...args);\n  }\n\n  static invertBy(...args) {\n    return invertBy(...args);\n  }\n\n  static omit(...args) {\n    return omit(...args);\n  }\n\n  static omitBy(...args) {\n    return omitBy(...args);\n  }\n\n  static pick(...args) {\n    return pick(...args);\n  }\n\n  static pickBy(...args) {\n    return pickBy(...args);\n  }\n\n  static transform(...args) {\n    return transform(...args);\n  }\n\n  // More\n\n  static colorGenerator(...args) {\n    return colorGenerator(...args);\n  }\n\n  static getCSVDataArray(...args) {\n    return getCSVDataArray(...args);\n  }\n}\n\nexport default DataParser;\n", "/* eslint-disable consistent-return */\nconst countBy = (collection = [], value) => {\n  const valueType = typeof value;\n  const collectionType = Array.isArray(collection);\n  const output = {};\n\n  if (collectionType) {\n    const keys =\n      valueType === 'function'\n        ? collection.map((item) => value(item))\n        : collection.map((item) => item[value]);\n\n    [...new Set(keys)].forEach((key) => {\n      output[key] = 0;\n\n      keys.forEach((item) => {\n        if (key === item) {\n          output[key]++;\n        }\n\n        return;\n      });\n    });\n  } else {\n    const objKeys = Object.keys(collection);\n    const keys =\n      valueType === 'function'\n        ? objKeys.map((item) => value(collection[item]))\n        : objKeys.map((item) => collection[item][value]);\n\n    [...new Set(keys)].forEach((key) => {\n      output[key] = 0;\n\n      keys.forEach((item) => {\n        if (key === item) {\n          output[key]++;\n        }\n\n        return;\n      });\n    });\n  }\n\n  return output;\n};\n\nconst groupBy = (collection = [], value) => {\n  const valueType = typeof value;\n  const collectionType = Array.isArray(collection);\n  const output = {};\n\n  if (collectionType) {\n    const keys =\n      valueType === 'function'\n        ? collection.map((item) => value(item))\n        : collection.map((item) => item[value]);\n\n    [...new Set(keys)].forEach((key) => {\n      output[key] = [];\n\n      keys.forEach((item, index) => {\n        if (key === item) {\n          output[key].push(collection[index]);\n        }\n\n        return;\n      });\n    });\n  } else {\n    const objKeys = Object.keys(collection);\n    const keys =\n      valueType === 'function'\n        ? objKeys.map((item) => value(collection[item]))\n        : objKeys.map((item) => collection[item][value]);\n\n    [...new Set(keys)].forEach((key) => {\n      output[key] = [];\n\n      keys.forEach((item, index) => {\n        if (key === item) {\n          output[key].push(collection[objKeys[index]]);\n        }\n\n        return;\n      });\n    });\n  }\n\n  return output;\n};\n\nconst orderBy = (collection = [], values = [], order = []) => {\n  let output = [];\n\n  if (order.length < values.length) {\n    for (let i = order.length; i < values.length; i++) {\n      order[i] = 'asc';\n    }\n  }\n\n  const valuesType = Array.isArray(values);\n\n  const length = valuesType ? values.length : 1;\n\n  const collectionType = Array.isArray(collection);\n\n  if (collectionType) {\n    if (typeof collection[0] === 'object') {\n      // eslint-disable-next-line array-callback-return\n      output = collection.sort((a, b) => {\n        for (let i = 0; i < length; i++) {\n          const aValue = valuesType ? a[values[i]] : values(a);\n          const bValue = valuesType ? b[values[i]] : values(b);\n\n          if (order[i] === 'desc') {\n            if (aValue < bValue) {\n              return 1;\n            }\n\n            if (aValue > bValue) {\n              return -1;\n            }\n\n            continue;\n          }\n\n          if (aValue > bValue) {\n            return 1;\n          }\n\n          if (aValue < bValue) {\n            return -1;\n          }\n\n          continue;\n        }\n      });\n    } else {\n      output = collection.sort();\n\n      if (values === 'desc' || values[0] === 'desc') {\n        output.reverse();\n      }\n    }\n  } else {\n    const keys = Object.keys(collection);\n\n    const valuesToSort = keys.map((key) => collection[key]);\n\n    output = valuesToSort.sort();\n\n    if (values === 'desc' || values[0] === 'desc') {\n      output.reverse();\n    }\n  }\n\n  return output;\n};\n\nconst sortBy = (collection = [], values = []) => {\n  let output = [];\n\n  const valuesType = Array.isArray(values);\n\n  const length = valuesType ? values.length : 1;\n\n  const collectionType = Array.isArray(collection);\n\n  if (collectionType) {\n    if (typeof collection[0] === 'object') {\n      // eslint-disable-next-line array-callback-return\n      output = collection.sort((a, b) => {\n        for (let i = 0; i < length; i++) {\n          const aValue = valuesType ? a[values[i]] : values(a);\n          const bValue = valuesType ? b[values[i]] : values(b);\n\n          if (aValue > bValue) {\n            return 1;\n          }\n\n          if (aValue < bValue) {\n            return -1;\n          }\n\n          continue;\n        }\n      });\n    } else {\n      output = collection.sort();\n    }\n  } else {\n    const keys = Object.keys(collection);\n\n    const valuesToSort = keys.map((key) => collection[key]);\n\n    output = valuesToSort.sort();\n  }\n\n  return output;\n};\n\nexport { countBy, groupBy, orderBy, sortBy };\n", "const invert = (object = {}) => {\n  const keys = Object.keys(object);\n\n  const output = {};\n\n  keys.forEach((key) => {\n    output[object[key]] = key;\n  });\n\n  return output;\n};\n\nconst invertBy = (object = {}, func = (key) => key) => {\n  const keys = Object.keys(object);\n\n  const output = {};\n\n  keys.map((key) => {\n    const newKey = func(object[key]);\n\n    if (!Array.isArray(output[newKey])) {\n      output[newKey] = [];\n    }\n\n    return output[newKey].push(key);\n  });\n\n  return output;\n};\n\nconst omit = (object = {}, keys = []) => {\n  const objectKeys = Object.keys(object);\n  const output = {};\n\n  objectKeys\n    .filter((key) => {\n      return !keys.includes(key);\n    })\n    .forEach((key) => {\n      output[key] = object[key];\n    });\n\n  return output;\n};\n\nconst omitBy = (object = {}, func = () => false) => {\n  const objectKeys = Object.keys(object);\n  const output = {};\n\n  objectKeys\n    .filter((key) => {\n      return !func(object[key]);\n    })\n    .forEach((key) => {\n      output[key] = object[key];\n    });\n\n  return output;\n};\n\nconst pick = (object = {}, keys = []) => {\n  const objectKeys = Object.keys(object);\n  const output = {};\n\n  objectKeys\n    .filter((key) => {\n      return keys.includes(key);\n    })\n    .forEach((key) => {\n      output[key] = object[key];\n    });\n\n  return output;\n};\n\nconst pickBy = (object = {}, func = () => false) => {\n  const objectKeys = Object.keys(object);\n  const output = {};\n\n  objectKeys\n    .filter((key) => {\n      return func(object[key]);\n    })\n    .forEach((key) => {\n      output[key] = object[key];\n    });\n\n  return output;\n};\n\nconst transform = (object = {}, func, accumulator = {}) => {\n  return Object.keys(object).reduce((a, b) => {\n    const result = func(a, object[b], b);\n\n    if (result !== undefined) {\n      return result;\n    }\n\n    return accumulator;\n  }, accumulator);\n};\n\nexport { invert, invertBy, omit, omitBy, pick, pickBy, transform };\n"], "names": ["typeCheckConfig", "componentName", "config", "configTypes", "Object", "keys", "for<PERSON>ach", "property", "expectedTypes", "value", "valueType", "obj", "nodeType", "toString", "call", "match", "toLowerCase", "toType", "RegExp", "test", "Error", "toUpperCase", "document", "documentElement", "dir", "colors", "mdb", "COLOR_MAP", "red", "pink", "purple", "deepPurple", "indigo", "blue", "lightBlue", "cyan", "teal", "green", "lightGreen", "lime", "yellow", "amber", "orange", "deepOrange", "brown", "gray", "blueGray", "getCSVDataArray", "data", "delimiter", "split", "map", "row", "normalize", "getSelectedEntries", "array", "a", "b", "slice", "filter", "_", "i", "indexOf", "isNaN", "Number", "parseFloat", "getColumnsFromRows", "rows", "colorGenerator", "colorLibrary", "colorMap", "chartColors", "colorPalette", "Array", "isArray", "length", "DEFAULT_OPTIONS", "start", "end", "indexes", "columns", "headerIndex", "OPTIONS_TYPE", "datasetLabel", "labelsIndex", "<PERSON><PERSON><PERSON><PERSON>", "formatLabel", "label", "getCoordinates", "color", "COUNTRY_DATA", "country", "alpha2", "alpha3", "numeric", "MARKERS", "x", "y", "type", "fill", "latitude", "longitude", "r", "field", "countries", "countryIdentifier", "tooltips", "name", "children", "icon", "show", "disabled", "id", "TREEVIEW_KEYS", "FUNCTION_KEYS", "REFERENCE_KEYS", "flattenDeep", "reduce", "acc", "val", "concat", "uniq", "Set", "uniqBy", "flattenArray", "values", "output", "includes", "push", "STRATEGY_VECTOR_MAP", "STRATEGY_DATATABLE", "PARSER_STRATEGY", "constructor", "format", "options", "this", "_format", "_options", "_getConfig", "parse", "_parseCSV", "_parseJSON", "getValueExtrema", "_getField<PERSON><PERSON><PERSON>", "min", "Math", "max", "NAME", "dataArr", "header", "computedRows", "computedColumns", "entry", "key", "column", "chart", "_structure", "labels", "datasets", "colorIterator", "next", "computedEntry", "getData", "computedEntries", "_colorMap", "_setColorMap", "_parseArrayData", "_getDataArray", "getIdentifiers", "_getEntries", "_getAlpha2Code", "_getBoundry<PERSON><PERSON><PERSON>", "getMapCoordinates", "closestYPoints", "_getClosestPoints", "closestXPoints", "_getCoordinate", "coordinate", "points", "sort", "result1", "markers", "abs", "points2", "marker", "result2", "point1", "point2", "axis", "coordinateDiff1", "coordinateDiff2", "_generateColorCodes", "_getSelectedCountries", "identifier", "step", "fixedStep", "intervals", "floor", "legend", "minValue", "maxValue", "regions", "interval", "index", "alpha2Code", "tooltip", "find", "treeview", "_data", "_functionKeys", "_reference<PERSON>eys", "_parseStructure", "structure", "el", "_parseNode", "strategy", "_strategy", "_parser", "_setupStrategy", "getRegionIdentifiers", "args", "pullAll", "items", "item", "splice", "take", "takeRight", "union", "arg", "unionBy", "zip", "max<PERSON><PERSON><PERSON>", "zipObject", "countBy", "collection", "collectionType", "ob<PERSON><PERSON><PERSON><PERSON>", "groupBy", "sortBy", "valuesType", "aValue", "bValue", "orderBy", "order", "reverse", "invert", "object", "invertBy", "func", "new<PERSON>ey", "omit", "objectKeys", "omitBy", "pick", "pickBy", "transform", "accumulator", "result"], "mappings": "2OAYM,MAuGAA,EAAkB,CAACC,EAAeC,EAAQC,KAC9CC,OAAOC,KAAKF,GAAaG,SAASC,IAC1B,MAAAC,EAAgBL,EAAYI,GAC5BE,EAAQP,EAAOK,GACfG,EAAYD,KAxBHE,EAwBsBF,GAxBT,IAAME,GAAKC,SAwBO,UA3GnC,CAACD,GACVA,QACK,GAAGA,IAGL,CAAE,EAACE,SACPC,KAAKH,GACLI,MAAM,eAAe,GACrBC,cAmGyDC,CAAOR,GAxBnD,IAACE,EA0Bf,IAAK,IAAIO,OAAOV,GAAeW,KAAKT,GAClC,MAAM,IAAIU,MACR,GAAGnB,EAAcoB,0BACJd,qBAA4BG,yBACjBF,MAE3B,GACF,EAmEWc,SAASC,gBAAgBC,ICnMxB,MAAAC,EAAA,CACb,GAAI,CACF,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,WAEF,IAAK,CACH,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,WAEF,IAAK,CACH,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,WAEF,IAAK,CACH,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,WAEF,IAAK,CACH,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,WAEF,IAAK,CACH,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,WAEF,IAAK,CACH,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,WAEF,IAAK,CACH,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,WAEF,IAAK,CACH,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,WAEF,IAAK,CACH,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,WAEFC,IAAK,CAAC,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,YCnN3DC,EAAA,CACbC,IAAK,CACH,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,WAEFC,KAAM,CACJ,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,WAEFC,OAAQ,CACN,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,WAEFC,WAAY,CACV,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,WAEFC,OAAQ,CACN,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,WAEFC,KAAM,CACJ,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,WAEFC,UAAW,CACT,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,WAEFC,KAAM,CACJ,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,WAEFC,KAAM,CACJ,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,WAEFC,MAAO,CACL,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,WAEFC,WAAY,CACV,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,WAEFC,KAAM,CACJ,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,WAEFC,OAAQ,CACN,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,WAEFC,MAAO,CACL,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,WAEFC,OAAQ,CACN,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,WAEFC,WAAY,CACV,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,WAEFC,MAAO,CACL,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,WAEFC,KAAM,CACJ,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,WAEFC,SAAU,CACR,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,YChOEC,EAAkB,CAACC,EAAMC,EAAY,MAClCD,EAAKE,MAAM,MAAMC,KAAKC,GAAQA,EAAIF,MAAMD,GAAWE,KAAK1C,GAAU4C,EAAU5C,OAG/E6C,EAAqB,CAACC,EAAOC,EAAGC,IACnB,iBAAND,EACFD,EAAMG,MAAMF,EAAGC,GAGjBF,EAAMI,QAAO,CAACC,EAAGC,KAAyB,IAAnBL,EAAEM,QAAQD,KAGpCR,EAAa5C,GAEH,SAAVA,IAA8B,IAAVA,GAGV,UAAVA,IAA+B,IAAVA,IAKpBsD,MAAMC,OAAOvD,IAIXA,EAHEwD,WAAWxD,IAMhByD,EAAsBC,IACpB,MAACf,GAAOe,EAEd,OAAKf,EAIEhD,OAAOC,KAAK+C,GAHV,EAGa,EAGd,SAAAgB,EAAe3C,EAAQoC,GAC/B,MAAMQ,EAAe,IAChBC,KACAC,GAGCC,EAAeC,MAAMC,QAAQjD,GAAUA,EAAS4C,EAAa5C,GAEnE,aACQ+C,EAAaX,KAEnBA,EAEQW,EAAaG,OAAS,IACxBd,EAAA,EAGV,CClDA,MAEMe,EAAkB,CACtBT,KAAM,CACJU,MAAO,EACPC,SAAK,EACLC,aAAS,GAEXC,QAAS,CACPH,MAAO,EACPC,SAAK,EACLC,aAAS,GAEXE,aAAa,EACb5E,KAAM,KACN4C,UAAW,KAGPiC,EAAe,CACnBF,QAAS,SACTb,KAAM,SACNc,YAAa,SACb5E,KAAM,eACN4C,UAAW,UChBb,MAEM2B,EAAkB,CACtBT,KAAM,CACJU,MAAO,EACPC,SAAK,EACLC,aAAS,GAEXC,QAAS,CACPH,MAAO,EACPC,SAAK,EACLC,aAAS,GAEXI,aAAc,KACdC,aAAa,EACbnC,UAAW,IACX5C,KAAM,KACNgF,WAAY,GACZC,YAAcC,GACLA,EAETC,eAAgB,KAChBC,MAAO,OAGHP,EAAe,CACnBC,aAAc,uBACdhB,KAAM,SACNa,QAAS,SACTI,YAAa,SACbnC,UAAW,SACX5C,KAAM,eACNgF,WAAY,QACZC,YAAa,WACbE,eAAgB,kBAChBC,MAAO,mBCnDM,MAAAC,EAAA,CACb,CAAEC,QAAS,cAAeC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAChE,CAAEH,QAAS,UAAWC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC5D,CAAEH,QAAS,UAAWC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC5D,CAAEH,QAAS,iBAAkBC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OACnE,CAAEH,QAAS,UAAWC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC5D,CAAEH,QAAS,SAAUC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC3D,CAAEH,QAAS,WAAYC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC7D,CAAEH,QAAS,aAAcC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC/D,CAAEH,QAAS,sBAAuBC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OACxE,CAAEH,QAAS,YAAaC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC9D,CAAEH,QAAS,UAAWC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC5D,CAAEH,QAAS,QAASC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC1D,CAAEH,QAAS,YAAaC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC9D,CAAEH,QAAS,UAAWC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC5D,CAAEH,QAAS,aAAcC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC/D,CAAEH,QAAS,UAAWC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,QAC5D,CAAEH,QAAS,UAAWC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC5D,CAAEH,QAAS,aAAcC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC/D,CAAEH,QAAS,WAAYC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC7D,CAAEH,QAAS,UAAWC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC5D,CAAEH,QAAS,UAAWC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC5D,CAAEH,QAAS,SAAUC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC3D,CAAEH,QAAS,QAASC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC1D,CAAEH,QAAS,UAAWC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC5D,CAAEH,QAAS,SAAUC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC3D,CAAEH,QAAS,UAAWC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC5D,CAAEH,QAAS,mCAAoCC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OACrF,CAAEH,QAAS,yBAA0BC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC3E,CAAEH,QAAS,WAAYC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC7D,CAAEH,QAAS,gBAAiBC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAClE,CAAEH,QAAS,SAAUC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC3D,CAAEH,QAAS,iCAAkCC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OACnF,CAAEH,QAAS,oBAAqBC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OACtE,CAAEH,QAAS,WAAYC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC7D,CAAEH,QAAS,eAAgBC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OACjE,CAAEH,QAAS,UAAWC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC5D,CAAEH,QAAS,aAAcC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC/D,CAAEH,QAAS,WAAYC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC7D,CAAEH,QAAS,WAAYC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC7D,CAAEH,QAAS,SAAUC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC3D,CAAEH,QAAS,iBAAkBC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OACnE,CAAEH,QAAS,2BAA4BC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC7E,CAAEH,QAAS,OAAQC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OACzD,CAAEH,QAAS,QAASC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC1D,CAAEH,QAAS,QAASC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC1D,CAAEH,QAAS,mBAAoBC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OACrE,CAAEH,QAAS,gBAAiBC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAClE,CAAEH,QAAS,WAAYC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC7D,CAAEH,QAAS,UAAWC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC5D,CACEH,QAAS,QACTC,OAAQ,KACRC,OAAQ,MACRC,QAAS,OAEX,CAAEH,QAAS,QAASC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC1D,CAAEH,QAAS,eAAgBC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OACjE,CAAEH,QAAS,aAAcC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC/D,CAAEH,QAAS,UAAWC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC5D,CAAEH,QAAS,OAAQC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OACzD,CAAEH,QAAS,UAAWC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC5D,CAAEH,QAAS,SAAUC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC3D,CAAEH,QAAS,UAAWC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC5D,CAAEH,QAAS,gBAAiBC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAClE,CAAEH,QAAS,UAAWC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC5D,CAAEH,QAAS,WAAYC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC7D,CAAEH,QAAS,WAAYC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC7D,CAAEH,QAAS,qBAAsBC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OACvE,CAAEH,QAAS,UAAWC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC5D,CAAEH,QAAS,QAASC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC1D,CAAEH,QAAS,cAAeC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAChE,CAAEH,QAAS,oBAAqBC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OACtE,CAAEH,QAAS,UAAWC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC5D,CAAEH,QAAS,UAAWC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC5D,CAAEH,QAAS,WAAYC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC7D,CAAEH,QAAS,WAAYC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC7D,CAAEH,QAAS,8BAA+BC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAChF,CAAEH,QAAS,gBAAiBC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAClE,CAAEH,QAAS,OAAQC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OACzD,CAAEH,QAAS,UAAWC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC5D,CAAEH,QAAS,SAAUC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC3D,CAAEH,QAAS,gBAAiBC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAClE,CAAEH,QAAS,mBAAoBC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OACrE,CAAEH,QAAS,8BAA+BC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAChF,CAAEH,QAAS,QAASC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC1D,CAAEH,QAAS,SAAUC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC3D,CAAEH,QAAS,UAAWC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC5D,CAAEH,QAAS,UAAWC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC5D,CAAEH,QAAS,QAASC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC1D,CAAEH,QAAS,YAAaC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC9D,CAAEH,QAAS,SAAUC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC3D,CAAEH,QAAS,YAAaC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC9D,CAAEH,QAAS,UAAWC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC5D,CAAEH,QAAS,aAAcC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC/D,CAAEH,QAAS,OAAQC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OACzD,CAAEH,QAAS,YAAaC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC9D,CAAEH,QAAS,WAAYC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC7D,CAAEH,QAAS,SAAUC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC3D,CAAEH,QAAS,gBAAiBC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAClE,CAAEH,QAAS,SAAUC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC3D,CAAEH,QAAS,QAASC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC1D,CAAEH,QAAS,oCAAqCC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OACtF,CAAEH,QAAS,WAAYC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC7D,CAAEH,QAAS,WAAYC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC7D,CAAEH,QAAS,YAAaC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC9D,CAAEH,QAAS,UAAWC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC5D,CAAEH,QAAS,UAAWC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC5D,CAAEH,QAAS,QAASC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC1D,CAAEH,QAAS,YAAaC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC9D,CAAEH,QAAS,OAAQC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OACzD,CAAEH,QAAS,OAAQC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OACzD,CAAEH,QAAS,UAAWC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC5D,CAAEH,QAAS,cAAeC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAChE,CAAEH,QAAS,SAAUC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC3D,CAAEH,QAAS,QAASC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC1D,CAAEH,QAAS,UAAWC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC5D,CAAEH,QAAS,QAASC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC1D,CAAEH,QAAS,SAAUC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC3D,CAAEH,QAAS,SAAUC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC3D,CAAEH,QAAS,aAAcC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC/D,CAAEH,QAAS,QAASC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC1D,CAAEH,QAAS,WAAYC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC7D,CAAEH,QAAS,QAASC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC1D,CAAEH,QAAS,QAASC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC1D,CAAEH,QAAS,SAAUC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC3D,CAAEH,QAAS,aAAcC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC/D,CAAEH,QAAS,mCAAoCC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OACrF,CAAEH,QAAS,SAAUC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC3D,CAAEH,QAAS,UAAWC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC5D,CAAEH,QAAS,UAAWC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC5D,CAAEH,QAAS,UAAWC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC5D,CAAEH,QAAS,QAASC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC1D,CAAEH,QAAS,gBAAiBC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAClE,CAAEH,QAAS,YAAaC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC9D,CAAEH,QAAS,aAAcC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC/D,CAAEH,QAAS,QAASC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC1D,CAAEH,QAAS,aAAcC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC/D,CAAEH,QAAS,SAAUC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC3D,CAAEH,QAAS,WAAYC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC7D,CAAEH,QAAS,WAAYC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC7D,CAAEH,QAAS,OAAQC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OACzD,CAAEH,QAAS,QAASC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC1D,CAAEH,QAAS,mBAAoBC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OACrE,CAAEH,QAAS,aAAcC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC/D,CAAEH,QAAS,aAAcC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC/D,CAAEH,QAAS,YAAaC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC9D,CAAEH,QAAS,UAAWC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC5D,CAAEH,QAAS,SAAUC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC3D,CAAEH,QAAS,aAAcC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC/D,CAAEH,QAAS,UAAWC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC5D,CAAEH,QAAS,SAAUC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC3D,CAAEH,QAAS,WAAYC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC7D,CAAEH,QAAS,aAAcC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC/D,CAAEH,QAAS,aAAcC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC/D,CAAEH,QAAS,UAAWC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC5D,CAAEH,QAAS,aAAcC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC/D,CAAEH,QAAS,UAAWC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC5D,CAAEH,QAAS,UAAWC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC5D,CAAEH,QAAS,QAASC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC1D,CAAEH,QAAS,QAASC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC1D,CAAEH,QAAS,cAAeC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAChE,CAAEH,QAAS,gBAAiBC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAClE,CAAEH,QAAS,cAAeC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAChE,CAAEH,QAAS,YAAaC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC9D,CAAEH,QAAS,QAASC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC1D,CAAEH,QAAS,UAAWC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC5D,CAAEH,QAAS,OAAQC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OACzD,CAAEH,QAAS,iBAAkBC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OACnE,CAAEH,QAAS,2BAA4BC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC7E,CAAEH,QAAS,SAAUC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC3D,CAAEH,QAAS,OAAQC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OACzD,CAAEH,QAAS,WAAYC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC7D,CAAEH,QAAS,QAASC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC1D,CAAEH,QAAS,sBAAuBC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OACxE,CAAEH,QAAS,SAAUC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC3D,CAAEH,QAAS,mBAAoBC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OACrE,CAAEH,QAAS,WAAYC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC7D,CAAEH,QAAS,OAAQC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OACzD,CAAEH,QAAS,cAAeC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAChE,CAAEH,QAAS,WAAYC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC7D,CAAEH,QAAS,SAAUC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC3D,CAAEH,QAAS,WAAYC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC7D,CAAEH,QAAS,cAAeC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAChE,CAAEH,QAAS,QAASC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC1D,CAAEH,QAAS,8BAA+BC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAChF,CAAEH,QAAS,UAAWC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC5D,CAAEH,QAAS,qBAAsBC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OACvE,CAAEH,QAAS,SAAUC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC3D,CAAEH,QAAS,UAAWC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC5D,CAAEH,QAAS,mBAAoBC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OACrE,CACEH,QAAS,+CACTC,OAAQ,KACRC,OAAQ,MACRC,QAAS,OAEX,CAAEH,QAAS,wBAAyBC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC1E,CAAEH,QAAS,cAAeC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAChE,CAAEH,QAAS,eAAgBC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OACjE,CAAEH,QAAS,4BAA6BC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC9E,CAAEH,QAAS,mCAAoCC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OACrF,CAAEH,QAAS,QAASC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC1D,CAAEH,QAAS,aAAcC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC/D,CAAEH,QAAS,wBAAyBC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC1E,CAAEH,QAAS,eAAgBC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OACjE,CAAEH,QAAS,UAAWC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC5D,CAAEH,QAAS,SAAUC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC3D,CAAEH,QAAS,aAAcC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC/D,CAAEH,QAAS,eAAgBC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OACjE,CAAEH,QAAS,YAAaC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC9D,CAAEH,QAAS,eAAgBC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OACjE,CAAEH,QAAS,WAAYC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC7D,CAAEH,QAAS,WAAYC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC7D,CAAEH,QAAS,kBAAmBC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OACpE,CAAEH,QAAS,UAAWC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC5D,CAAEH,QAAS,eAAgBC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OACjE,CACEH,QAAS,+CACTC,OAAQ,KACRC,OAAQ,MACRC,QAAS,OAEX,CAAEH,QAAS,cAAeC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAChE,CAAEH,QAAS,QAASC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC1D,CAAEH,QAAS,YAAaC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC9D,CAAEH,QAAS,QAASC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC1D,CAAEH,QAAS,WAAYC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC7D,CAAEH,QAAS,yBAA0BC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC3E,CAAEH,QAAS,SAAUC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC3D,CAAEH,QAAS,cAAeC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAChE,CAAEH,QAAS,uBAAwBC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OACzE,CAAEH,QAAS,SAAUC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC3D,CAAEH,QAAS,aAAcC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC/D,CAAEH,QAAS,+BAAgCC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OACjF,CAAEH,QAAS,WAAYC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC7D,CAAEH,QAAS,cAAeC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAChE,CAAEH,QAAS,OAAQC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OACzD,CAAEH,QAAS,UAAWC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC5D,CAAEH,QAAS,QAASC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC1D,CAAEH,QAAS,sBAAuBC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OACxE,CAAEH,QAAS,UAAWC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC5D,CAAEH,QAAS,SAAUC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC3D,CAAEH,QAAS,eAAgBC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OACjE,CAAEH,QAAS,2BAA4BC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC7E,CAAEH,QAAS,SAAUC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC3D,CAAEH,QAAS,SAAUC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC3D,CAAEH,QAAS,UAAWC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC5D,CAAEH,QAAS,uBAAwBC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OACzE,CACEH,QAAS,iBACTC,OAAQ,KACRC,OAAQ,MACRC,QAAS,OAEX,CAAEH,QAAS,uCAAwCC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OACzF,CAAEH,QAAS,2BAA4BC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC7E,CAAEH,QAAS,UAAWC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC5D,CAAEH,QAAS,aAAcC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC/D,CAAEH,QAAS,UAAWC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC5D,CAAEH,QAAS,YAAaC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC9D,CAAEH,QAAS,WAAYC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC7D,CAAEH,QAAS,iBAAkBC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OACnE,CAAEH,QAAS,iBAAkBC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OACnE,CAAEH,QAAS,oBAAqBC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OACtE,CAAEH,QAAS,iBAAkBC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OACnE,CAAEH,QAAS,QAASC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC1D,CAAEH,QAAS,SAAUC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC3D,CAAEH,QAAS,WAAYC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,OAC7D,CAAEH,QAAS,gBAAiBC,OAAQ,KAAMC,OAAQ,MAAOC,QAAS,QC7QrDC,EAAA,CACb,CACEC,EAAG,IACHC,EAAG,IACHC,KAAM,SACNC,KAAM,qBACNZ,MAAO,SACPa,SAAU,QACVC,WAAW,OAEb,CACEL,EAAG,IACHC,EAAG,IACHC,KAAM,SACNC,KAAM,qBACNZ,MAAO,aACPa,SAAU,QACVC,UAAW,SAEb,CACEL,EAAG,IACHC,EAAG,IACHE,KAAM,qBACND,KAAM,SACNX,MAAO,WACPa,SAAU,QACVC,UAAW,QAEb,CACEL,EAAG,IACHC,EAAG,IACHE,KAAM,qBACND,KAAM,SACNX,MAAO,YACPa,SAAU,QACVC,WAAW,SAEb,CACEL,EAAG,IACHC,EAAG,IACHE,KAAM,qBACND,KAAM,SACNX,MAAO,QACPa,SAAU,QACVC,UAAW,OAEb,CACEL,EAAG,IACHC,EAAG,IACHE,KAAM,qBACND,KAAM,SACNX,MAAO,SACPa,SAAU,QACVC,UAAW,SAEb,CACEL,EAAG,IACHC,EAAG,IACHE,KAAM,qBACND,KAAM,SACNX,MAAO,SACPa,SAAU,QACVC,WAAW,QAEb,CACEL,EAAG,IACHC,EAAG,IACHE,KAAM,qBACND,KAAM,SACNX,MAAO,YACPa,SAAU,QACVC,UAAW,QAEb,CACEL,EAAG,IACHC,EAAG,IACHK,EAAG,GACHH,KAAM,qBACND,KAAM,SACNX,MAAO,QACPa,SAAU,QACVC,UAAW,UAEb,CACEL,EAAG,IACHC,EAAG,IACHE,KAAM,qBACND,KAAM,SACNX,MAAO,aACPa,SAAU,QACVC,WAAW,SAEb,CACEL,EAAG,MACHC,EAAG,IACHE,KAAM,qBACND,KAAM,SACNX,MAAO,YACPa,UAAU,UACVC,UAAW,YAEb,CACEL,EAAG,IACHC,EAAG,IACHE,KAAM,qBACND,KAAM,SACNX,MAAO,SACPa,SAAU,QACVC,UAAW,QAEb,CACEL,EAAG,IACHC,EAAG,IACHE,KAAM,qBACND,KAAM,SACNX,MAAO,eACPa,UAAU,QACVC,WAAW,SAEb,CACEL,EAAG,IACHC,EAAG,IACHE,KAAM,qBACND,KAAM,SACNX,MAAO,WACPa,UAAU,QACVC,WAAW,SAEb,CACEL,EAAG,IACHC,EAAG,IACHE,KAAM,qBACND,KAAM,SACNX,MAAO,SACPa,SAAU,QACVC,WAAW,SAEb,CACEL,EAAG,IACHC,EAAG,MACHE,KAAM,qBACND,KAAM,SACNX,MAAO,WACPa,UAAU,OACVC,UAAW,SAEb,CACEL,EAAG,IACHC,EAAG,IACHE,KAAM,qBACND,KAAM,SACNX,MAAO,QACPa,SAAU,QACVC,UAAW,SAEb,CACEL,EAAG,IACHC,EAAG,IACHE,KAAM,qBACND,KAAM,SACNX,MAAO,WACPa,UAAU,QACVC,UAAW,SAEb,CACEL,EAAG,MACHC,EAAG,MACHE,KAAM,qBACND,KAAM,SACNX,MAAO,QACPa,SAAU,UACVC,WAAW,WAEb,CACEL,EAAG,MACHC,EAAG,IACHE,KAAM,qBACND,KAAM,SACNX,MAAO,UACPa,UAAU,QACVC,UAAW,WAEb,CACEL,EAAG,IACHC,EAAG,IACHE,KAAM,qBACND,KAAM,SACNX,MAAO,QACPa,UAAU,UACVC,UAAW,YAEb,CACEL,EAAG,IACHC,EAAG,IACHE,KAAM,qBACND,KAAM,SACNX,MAAO,OACPa,SAAU,UACVC,UAAW,YAEb,CACEL,EAAG,IACHC,EAAG,MACHE,KAAM,qBACND,KAAM,SACNX,MAAO,QACPa,SAAU,UACVC,UAAW,WAEb,CACEL,EAAG,kBACHC,EAAG,mBACHE,KAAM,qBACND,KAAM,SACNX,MAAO,SACPa,SAAU,WACVC,UAAW,YAEb,CACEL,EAAG,kBACHC,EAAG,kBACHE,KAAM,qBACND,KAAM,SACNX,MAAO,SACPa,SAAU,WACVC,UAAW,YAEb,CACEL,EAAG,IACHC,EAAG,MACHE,KAAM,qBACND,KAAM,SACNX,MAAO,OACPa,SAAU,WACVC,UAAW,YAEb,CACEL,EAAG,MACHC,EAAG,IACHE,KAAM,qBACND,KAAM,SACNX,MAAO,SACPa,SAAU,UACVC,UAAW,YAEb,CACEL,EAAG,IACHC,EAAG,IACHE,KAAM,qBACND,KAAM,SACNX,MAAO,WACPa,SAAU,WACVC,UAAW,YAEb,CACEL,EAAG,kBACHC,EAAG,IACHE,KAAM,qBACND,KAAM,SACNX,MAAO,OACPa,SAAU,WACVC,UAAW,YAEb,CACEL,EAAG,kBACHC,EAAG,IACHE,KAAM,qBACND,KAAM,SACNX,MAAO,YACPa,SAAU,WACVC,UAAW,YAEb,CACEL,EAAG,IACHC,EAAG,IACHE,KAAM,qBACND,KAAM,SACNX,MAAO,WACPa,SAAU,WACVC,UAAW,YAEb,CACEL,EAAG,IACHC,EAAG,kBACHE,KAAM,qBACND,KAAM,SACNX,MAAO,OACPa,SAAU,WACVC,UAAW,YAEb,CACEL,EAAG,IACHC,EAAG,MACHE,KAAM,qBACND,KAAM,SACNX,MAAO,WACPa,SAAU,WACVC,UAAW,YAEb,CACEL,EAAG,IACHC,EAAG,IACHE,KAAM,qBACND,KAAM,SACNX,MAAO,SACPa,SAAU,WACVC,UAAW,YAEb,CACEL,EAAG,IACHC,EAAG,MACHE,KAAM,qBACND,KAAM,SACNX,MAAO,UACPa,SAAU,UACVC,UAAW,YAEb,CACEL,EAAG,IACHC,EAAG,MACHE,KAAM,qBACND,KAAM,SACNX,MAAO,YACPa,SAAU,UACVC,UAAW,YAEb,CACEL,EAAG,MACHC,EAAG,IACHE,KAAM,qBACND,KAAM,SACNX,MAAO,WACPa,SAAU,WACVC,UAAW,aAEb,CACEL,EAAG,IACHC,EAAG,IACHE,KAAM,qBACND,KAAM,SACNX,MAAO,UACPa,SAAU,UACVC,UAAW,aAEb,CACEL,EAAG,MACHC,EAAG,IACHE,KAAM,qBACND,KAAM,SACNX,MAAO,SACPa,UAAU,WACVC,UAAW,aAEb,CACEL,EAAG,IACHC,EAAG,IACHE,KAAM,qBACND,KAAM,SACNX,MAAO,QACPa,UAAU,WACVC,UAAW,aAEb,CACEL,EAAG,IACHC,EAAG,IACHE,KAAM,qBACND,KAAM,SACNX,MAAO,WACPa,UAAU,UACVC,UAAW,aAEb,CACEL,EAAG,KACHC,EAAG,IACHE,KAAM,qBACND,KAAM,SACNX,MAAO,YACPa,SAAU,WACVC,WAAW,aAEb,CACEL,EAAG,MACHC,EAAG,MACHE,KAAM,qBACND,KAAM,SACNX,MAAO,YACPa,SAAU,WACVC,WAAW,aAEb,CACEL,EAAG,IACHC,EAAG,IACHE,KAAM,qBACND,KAAM,SACNX,MAAO,cACPa,SAAU,WACVC,WAAW,YAEb,CACEL,EAAG,kBACHC,EAAG,kBACHE,KAAM,qBACND,KAAM,SACNX,MAAO,cACPa,SAAU,UACVC,WAAW,YAEb,CACEL,EAAG,IACHC,EAAG,IACHE,KAAM,qBACND,KAAM,SACNX,MAAO,WACPa,SAAU,UACVC,WAAW,YAEb,CACEL,EAAG,MACHC,EAAG,MACHE,KAAM,qBACND,KAAM,SACNX,MAAO,YACPa,UAAU,UACVC,UAAW,WAEb,CACEL,EAAG,IACHC,EAAG,MACHE,KAAM,qBACND,KAAM,SACNX,MAAO,OACPa,SAAU,UACVC,WAAW,aCjaTzB,EAAkB,CACtB2B,MAAO,KACPd,MAAO,OACPe,eAAW,EACXC,kBAAmB,KACnBtC,KAAM,CACJU,MAAO,EACPC,SAAK,EACLC,aAAS,GAEXE,aAAa,EACbhC,UAAW,IACXyD,SAAU,IAAM,MAEZxB,EAAe,CACnBqB,MAAO,uBACPd,MAAO,iBACPgB,kBAAmB,uBACnBtC,KAAM,SACNc,YAAa,SACbhC,UAAW,SACXyD,SAAU,YC5BZ,MAEM9B,EAAkB,CACtB+B,KAAM,OACNC,SAAU,WACVC,KAAM,KACNC,MAAM,EACNC,UAAU,EACVC,GAAI,MAEA9B,EAAe,CACnByB,KAAM,oBACNC,SAAU,SACVC,KAAM,yBACNC,KAAM,qBACNC,SAAU,qBACVC,GAAI,wBAGAC,EAAgB,CAAC,OAAQ,WAAY,OAAQ,WAAY,KAAM,QAE/DC,EAAgB,CAAC,OAAQ,WAAY,OAAQ,QAE7CC,EAAiB,CAAC,WAAY,QC/BpC,MAAMC,EAAc,CAAC7D,EAAQ,KACpBA,EAAM8D,QACX,CAACC,EAAKC,IAAS9C,MAAMC,QAAQ6C,GAAOD,EAAIE,OAAOJ,EAAYG,IAAQD,EAAIE,OAAOD,IAC9E,IAwDEE,EAAO,CAAClE,EAAQ,KACb,IAAI,IAAImE,IAAInE,IAGfoE,EAAS,CAAClH,EAAO8C,EAAQ,MACvB,MAAAqE,EAAeR,EAAY7D,GAC3BsE,EAAS,GACTC,EAAS,GAIf,cAFoBrH,GAGlB,IAAK,WACH,IAAA,IAASoD,EAAI,EAAGA,EAAI+D,EAAajD,OAAQd,IACnCgE,EAAOE,SAAStH,EAAMmH,EAAa/D,OAIvCgE,EAAOG,KAAKvH,EAAMmH,EAAa/D,KACxBiE,EAAAE,KAAKJ,EAAa/D,KAE3B,MACF,IAAK,SACH,IAAA,IAASA,EAAI,EAAGA,EAAI+D,EAAajD,OAAQd,IACnCgE,EAAOE,SAASH,EAAa/D,GAAGpD,MAIpCoH,EAAOG,KAAKJ,EAAa/D,GAAGpD,IACrBqH,EAAAE,KAAKJ,EAAa/D,KAE3B,MACF,QACQ,MAAA,IAAIzC,MAAM,mCAGb,OAAA0G,CAAA,EClEHG,EAAsB,YACtBC,EAAqB,YAIrBC,EAAkB,CACtBD,CAACA,GPMH,MACE,WAAAE,CAAYC,EAAQC,EAAU,IAC5BC,KAAKC,QAAUH,EACVE,KAAAE,SAAWF,KAAKG,WAAWJ,EACjC,CAMD,KAAAK,CAAM3F,GACA,MAAiB,QAAjBuF,KAAKC,QACAD,KAAKK,UAAU5F,GAGjBuF,KAAKM,WAAW7F,EACxB,CAED,eAAA8F,CAAgB9F,EAAMuD,GACpB,MAAMsB,EAASU,KAAKQ,gBAAgB/F,EAAMuD,GAMnC,MAAA,CACLyC,IALUC,KAAKD,OAAOnB,GAMtBqB,IAJUD,KAAKC,OAAOrB,GAMzB,CAGD,UAAAa,CAAWJ,GACT,MAAMpI,EAAS,IACV0E,KACA0D,GAKE,OAFSa,EAtEP,oBAsEajJ,EAAQgF,GAEvBhF,CACR,CAED,SAAA0I,CAAU5F,GACR,MAAMC,UAAEA,EAAW+B,QAAAA,EAAAb,KAASA,EAAMc,YAAAA,GAAgBsD,KAAKE,SAEjDW,EAAUrG,EAAgBC,EAAMC,GAEhCoG,EAASD,EAAQnE,GAEnB,IAAAqE,EAAehG,EAAmB8F,EAASjF,EAAKY,SAAWZ,EAAKU,MAAOV,EAAKW,KAEhF,IAAKuE,EAAe,MAAA,CAAElF,KAAMmF,GAE5B,MAAMC,EAAkBjG,EACtB+F,EACArE,EAAQD,SAAWC,EAAQH,MAC3BG,EAAQF,KAOH,OAJQwE,EAAAA,EAAanG,KAAKC,GACxBE,EAAmBF,EAAK4B,EAAQD,SAAWC,EAAQH,MAAOG,EAAQF,OAGpE,CACLX,KAAMmF,EACNtE,QAASuE,EAEZ,CAED,UAAAV,CAAW7F,GACT,MAAMmB,KAAEA,EAAA9D,KAAMA,GAASkI,KAAKE,SAExB,IAAAa,EAAehG,EAAmBN,EAAMmB,EAAKY,SAAWZ,EAAKU,MAAOV,EAAKW,KAAK3B,KAC/EqG,IACC,MAAM1B,EAAS,CAAA,EAMR,OAJP1H,OAAOC,KAAKmJ,GAAOlJ,SAASmJ,IAC1B3B,EAAO2B,GAAOpG,EAAUmG,EAAMC,GAAI,IAG7B3B,CAAA,IAIL,MAAA9C,EAAUd,EAAmBoF,GAEnC,OAAKjJ,GAOUiJ,EAAAA,EAAanG,KAAKC,IACvB4B,EAAA1E,SAASoJ,KACkB,IAA7BrJ,EAAKyD,QAAQ4F,WACRtG,EAAIsG,EACZ,IAGItG,KAGF,CACL4B,QAAS3E,EACT8D,KAAMmF,IAlBC,CACLtE,UACAb,KAAMmF,EAkBX,CAED,eAAAP,CAAgB/F,EAAMuD,GACpB,OAAOvD,EAAKG,KAAKqG,GAAUA,EAAMjD,IAClC,GOrHDoD,MNwBF,MACE,WAAAvB,CAAYC,EAAQC,EAAU,IACvBC,KAAAE,SAAWF,KAAKG,WAAWJ,GAEhCC,KAAKC,QAAUH,EAEfE,KAAKqB,WAAa,CAChBC,OAAQ,GACRC,SAAU,CACR,CACEvE,MAAO,GACPvC,KAAM,KAIb,CAID,KAAA2F,CAAM3F,GACA,MAAiB,QAAjBuF,KAAKC,QACAD,KAAKK,UAAU5F,GAGjBuF,KAAKM,WAAW7F,EACxB,CAED,eAAA8F,CAAgB9F,EAAMuD,GACpB,MAAMsB,EAASU,KAAKQ,gBAAgB/F,EAAMuD,GAMnC,MAAA,CACLyC,IALUC,KAAKD,OAAOnB,GAMtBqB,IAJUD,KAAKC,OAAOrB,GAMzB,CAID,SAAAe,CAAU5F,GACF,MAAAC,UACJA,EAAA+B,QACAA,EAAAb,KACAA,EAAAiB,YACAA,EAAAD,aACAA,EAAAG,YACAA,EAAAG,MACAA,EAAAD,eACAA,GACE+C,KAAKE,SAEHW,EAAUrG,EAAgBC,EAAMC,GAEhCoG,EAASD,EAAQhE,GAEjBkE,EAAehG,EAAmB8F,EAASjF,EAAKY,SAAWZ,EAAKU,MAAOV,EAAKW,KAElF,IAAKuE,EAAe,MAAA,CAAElF,KAAMmF,GAEtB,MAAAO,EAASvG,EAAmB+F,EAAQrE,EAAQD,SAAWC,EAAQH,MAAOG,EAAQF,KAE9EiF,EAAgB3F,EAAe3C,EAAOgE,GAAQ,GA4B7C,MAAA,CACLqE,SA3BeR,EAAanG,KAAKC,IACjC,MAcMmC,EAAQnC,EAAI+B,IAAiB,GAC7BM,EAAQsE,EAAcC,OAAOvJ,MAG5B,MAAA,CACL8E,QACAvC,KApBc,MACd,MAAMiH,EAAgB3G,EACpBF,EACA4B,EAAQD,SAAWC,EAAQH,MAC3BG,EAAQF,KAGV,OAAIU,EACK,CAACA,EAAeyE,IAGlBA,CAAA,EAKIC,GAKXzE,MAAAA,EACR,IAKMoE,OAAQA,EAAO1G,KAAKoC,GAAUD,EAAYC,KAE7C,CAED,UAAAsD,CAAW7F,GACH,MAAAmB,KAAEA,OAAM9D,EAAMgF,WAAAA,EAAAF,aAAYA,cAAcG,EAAaG,MAAAA,EAAAD,eAAOA,GAChE+C,KAAKE,SAED0B,EAAkB7G,EAAmBN,EAAMmB,EAAKY,SAAWZ,EAAKU,MAAOV,EAAKW,KAAK3B,KACpFqG,IACC,MAAM1B,EAAS,CAAA,EAMR,OAJP1H,OAAOC,KAAKmJ,GAAOlJ,SAASmJ,IAC1B3B,EAAO2B,GAAOpG,EAAUmG,EAAMC,GAAI,IAG7B3B,CAAA,IAIL+B,EACJxJ,GACA6D,EAAmBiG,GAAiBxG,QAAQ4B,IAA0C,IAAhCF,EAAWvB,QAAQyB,KAErEwE,EAAgB3F,EAAe3C,EAAOgE,GAAQ,GAE9CqE,EAAWK,EAAgBhH,KAAKqG,IAe7B,CACLxG,KAdIwC,EACK,CAACA,EAAegE,IAGlBK,EAAO1G,KAAKoC,GACViE,EAAMjE,IAAU,IAUzBA,MALYiE,EAAMrE,IAAiB,GAMnCM,MALYsE,EAAcC,OAAOvJ,UAS9B,MAAA,CACLoJ,OAAQA,EAAO1G,KAAKoC,GAAUD,EAAYC,KAC1CuE,WAEH,CAED,eAAAf,CAAgB/F,EAAMuD,GACpB,OAAOvD,EAAKG,KAAKqG,GAAUA,EAAMjD,IAClC,CAED,UAAAmC,CAAWJ,GACT,MAAMpI,EAAS,IACV0E,KACA0D,GAKE,OAFSa,EA1MP,gBA0MajJ,EAAQgF,GAEvBhF,CACR,GMxLD+H,CAACA,GHQH,MACE,WAAAG,CAAYC,EAAQC,EAAU,IACvBC,KAAAE,SAAWF,KAAKG,WAAWJ,GAChCC,KAAKC,QAAUH,EAEVE,KAAA6B,UAAY7B,KAAK8B,cACvB,CAMD,KAAA1B,CAAM3F,GACJ,OAAOuF,KAAK+B,gBAAgB/B,KAAKgC,cAAcvH,GAChD,CAED,cAAAwH,CAAexH,GAGb,OAFgBuF,KAAKkC,YAAYlC,KAAKgC,cAAcvH,IAErCG,KAAKqG,GAAUjB,KAAKmC,eAAelB,IACnD,CAED,eAAAV,CAAgB9F,EAAMuD,GACd,MAAAsB,EAASU,KAAKQ,gBAAgBR,KAAKkC,YAAYlC,KAAKgC,cAAcvH,IAAQuD,GAEzE,OAAAgC,KAAKoC,kBAAkB9C,EAC/B,CAED,iBAAA+C,CAAkBxE,EAAUC,GAC1B,MAAMwE,EAAiBtC,KAAKuC,kBAAkB1E,EAAU,YAClD2E,EAAiBxC,KAAKuC,kBAAkBzE,EAAW,aAElD,MAAA,CACLL,EAAGuC,KAAKyC,eAAeD,EAAgB3E,EAAU,KACjDH,EAAGsC,KAAKyC,eAAeH,EAAgBxE,EAAW,KAErD,CAGD,iBAAAyE,CAAkBrK,EAAOwK,GACvB,MAAMC,EAASnF,EAAQoF,MAAK,CAAC3H,EAAGC,KAC9B,MAAMhD,EAAQ+C,EAAEyH,GAAcxH,EAAEwH,GAChC,OAAIxK,EAAQ,GAAU,EAClBA,EAAQ,EAAU,EACf,CAAA,IAGH2K,EAAUF,EAAO7D,QAAO,CAAC7D,EAAGC,EAAGI,EAAGwH,IAC/BxH,GAAKoF,KAAKqC,IAAID,EAAQ7H,GAAGyH,GAAcxK,GAASwI,KAAKqC,IAAI7H,EAAEwH,GAAcxK,GAC5E+C,EACAK,IACD,GAEC0H,EAAUL,EAAOvH,QAAQ6H,GAAWA,IAAWN,EAAOE,KAEtDK,EAAUF,EAAQlE,QAAO,CAAC7D,EAAGC,EAAGI,EAAGwH,IAChCxH,GAAKoF,KAAKqC,IAAID,EAAQ7H,GAAGyH,GAAcxK,GAASwI,KAAKqC,IAAI7H,EAAEwH,GAAcxK,GAC5E+C,EACAK,IACD,GAKE,MAAA,CACL6H,OAJaR,EAAOE,GAKpBO,OAJaJ,EAAQE,GAMxB,CAED,cAAAT,EAAeU,OAAEA,EAAAC,OAAQA,GAAUlL,EAAOmL,GAClC,MAAAX,EAAsB,MAATW,EAAe,WAAa,YAEzCC,EAAkBH,EAAOT,GAAcxK,EAEvCqL,EAAkBH,EAAOV,GAAcxK,EAMtC,OAHJqL,EAAkBJ,EAAOE,GAAQC,EAAkBF,EAAOC,KAC1DE,EAAkBD,EAGtB,CAED,eAAAvB,CAAgBtH,GACd,OAAOuF,KAAKwD,oBAAoBxD,KAAKkC,YAAYzH,GAClD,CAED,aAAAuH,CAAcvH,GACR,GAAiB,QAAjBuF,KAAKC,QAAmB,CACpB,MAAAvF,UAAEA,GAAcsF,KAAKE,SAEpB,OAAA1F,EAAgBC,EAAMC,EAC9B,CAEM,OAAAD,CACR,CAED,WAAAyH,CAAYzH,GACV,MAAMmB,KAAEA,EAAAqC,UAAMA,GAAc+B,KAAKE,SAEjC,OAAOjC,EACH+B,KAAKyD,sBAAsBhJ,GAC3BM,EAAmBN,EAAMmB,EAAKY,SAAWZ,EAAKU,MAAOV,EAAKW,IAC/D,CAED,UAAA4D,CAAWJ,GACT,MAAMpI,EAAS,IACV0E,KACA0D,GAKE,OAFSa,EA/IP,oBA+IajJ,EAAQgF,GAEvBhF,CACR,CAED,iBAAAyK,CAAkBpH,GACT,MAAA,CACL2F,IAAKD,KAAKC,OAAO3F,GACjByF,IAAKC,KAAKD,OAAOzF,GAEpB,CAED,qBAAAyI,CAAsBzI,GACpB,MAAQkD,kBAAmBwF,EAAYzF,UAAAA,GAAc+B,KAAKE,SAEnD,OAAAlF,EAAMI,QAAQ6F,IAC6B,IAAzChD,EAAU1C,QAAQ0F,EAAMyC,KAElC,CAED,mBAAAF,CAAoB/I,GAClB,MAAMuD,MAAEA,EAAO2F,KAAMC,GAAc5D,KAAKE,SAClC2D,EAAY7D,KAAK6B,UAAUzF,OAE3BkD,EAAS7E,EAAKG,KAAKqG,GAAUA,EAAMjD,MAEnCyC,IAAEA,EAAKE,IAAAA,GAAQX,KAAKoC,kBAAkB9C,GAEtCqE,EAAOC,GAAalD,KAAKoD,OAAOnD,EAAMF,GAAOoD,GAE7CE,EAAS/D,KAAK6B,UAAUjH,KAAI,CAACsC,EAAO5B,KAClC,MAAA0I,EAAWvD,EAAMnF,EAAIqI,EAC3B,IAAIM,EAAWD,EAAWL,EAMnB,OAJHrI,IAAMuI,EAAY,IACTI,EAAAtD,GAGN,CACLzD,QACAuD,IAAKuD,EACLrD,IAAKsD,EACb,IAGUlI,EAAWiE,KAAK6B,UAAUjH,KAAKsC,IAAW,CAC9CU,KAAMV,EACNgH,QAAS,OAkBJ,OAfA5E,EAAAvH,SAAQ,CAACG,EAAOoD,KACrB,MAAM6I,EAAWzD,KAAKoD,OAAO5L,EAAQuI,GAAOkD,GAEtCS,EAAQD,EAAWN,EAAYM,EAAWN,EAAY,EAEtDQ,EAAarE,KAAKmC,eAAe1H,EAAKa,IAEvC+I,GAEItI,EAAAqI,GAAOF,QAAQzE,KAAK,CAC3BhB,GAAI4F,EACJC,QAAStE,KAAKE,SAAS/B,SAASjG,IACjC,IAGI,CAAE6D,WAAUgI,SACpB,CAED,cAAA5B,CAAelB,GACb,MAAQ/C,kBAAmBwF,GAAe1D,KAAKE,SAM3C,IAAAgB,EAEI,OAAAD,EAAMyC,GAAYtH,QACxB,KAAK,EACG8E,EAAA,SACN,MACF,KAAK,EACGA,EAAA,SACN,MACF,QACQA,EAAA,UAGV,MAAM9D,GAjBelF,EAiBO+I,EAAMyC,GAjBNxC,EAiBmBA,EAhBtC/D,EAAaoH,MAAMnH,GAAYA,EAAQ8D,GAAKzI,cAAcD,MAAMN,EAAMO,kBAD3D,IAACP,EAAOgJ,EAmB5B,OAAK9D,EAIEA,EAAQC,OAHN,IAIV,CAED,eAAAmD,CAAgB/F,EAAMuD,GACpB,OAAOvD,EAAKG,KAAKqG,GAAUA,EAAMjD,IAClC,CAED,YAAA8D,GACQ,MAAA5E,MAAEA,GAAU8C,KAAKE,SAEnB,GAAAhE,MAAMC,QAAQe,GAAe,OAAAA,EAE3B,MAAAnB,EAAW3C,EAAU8D,GAE3B,IAAKnB,EACH,MAAM,IAAIlD,MAAM,SAASqE,gBAGpB,OAAAnB,CACR,GGxODyI,SFCF,MACE,WAAA3E,CAAYC,EAAQC,EAAU,IACvBC,KAAAE,SAAWF,KAAKG,WAAWJ,GAEhCC,KAAKC,QAAUH,EAEfE,KAAKyE,MAAQ,GAEbzE,KAAKqB,WAAa,GAEbrB,KAAA0E,cAAgB/F,EAAcvD,QAAQ8F,GAAsC,mBAAvBlB,KAAKE,SAASgB,KAExElB,KAAK2E,eAAiB/F,EAAexD,QAAQ8F,GAAuB,iBAARA,GAC7D,CAID,KAAAd,CAAM3F,GAGG,OAFPuF,KAAKyE,MAAQhK,EAENuF,KAAK4E,gBAAgBnK,EAC7B,CAID,eAAAmK,CAAgBC,GACP,OAAAA,EAAUjK,KAAKkK,GACb9E,KAAK+E,WAAWD,IAE1B,CAED,UAAAC,CAAWD,GACT,MAAMvF,EAAS,CAAA,EAoBR,OAlBOb,EAAA3G,SAASmJ,IACrB,GAAIlB,KAAK0E,cAAclF,SAAS0B,GAC9B3B,EAAO2B,GAAOlB,KAAKE,SAASgB,GAAK4D,QACxB,GAAA9E,KAAK2E,eAAenF,SAAS0B,GAAM,CAC5C,IAAK4D,EAAG9E,KAAKE,SAASgB,IACpB,OAGU,aAARA,EACK3B,EAAAlB,SAAW2B,KAAK4E,gBAAgBE,EAAG9E,KAAKE,SAASgB,KAExD3B,EAAO2B,GAAO4D,EAAG9E,KAAKE,SAASgB,GAEzC,MACQ3B,EAAO2B,GAAOlB,KAAKE,SAASgB,EAC7B,IAGI3B,CACR,CAED,UAAAY,CAAWJ,GACT,MAAMpI,EAAS,IACV0E,KACA0D,GAKE,OAFSa,EA5FP,mBA4FajJ,EAAQgF,GAEvBhF,CACR,WExDH,MACE,WAAAkI,CAAYmF,EAAWrF,EAAoBG,EAAS,OAAQC,GAC1DC,KAAKiF,UAAYD,EACjBhF,KAAKC,QAAUH,EACfE,KAAKE,SAAWH,EAEXC,KAAAkF,QAAUlF,KAAKmF,gBACrB,CAGD,KAAA/E,CAAM3F,GACG,OAAAuF,KAAKkF,QAAQ9E,MAAM3F,EAC3B,CAED,eAAA8F,CAAgB9F,EAAMuD,GACpB,OAAOgC,KAAKkF,QAAQ3E,gBAAgB9F,EAAMuD,EAC3C,CAID,oBAAAoH,CAAqB3K,GACf,GAAAuF,KAAKiF,YAAcvF,EACrB,MAAM,IAAI7G,MAAM,oCAAoCmH,KAAKiF,sBAGpD,OAAAjF,KAAKkF,QAAQjD,eAAexH,EACpC,CAED,iBAAA4H,CAAkBxE,EAAUC,GAC1B,OAAOkC,KAAKkF,QAAQ7C,kBAAkBxE,EAAUC,EACjD,CAID,cAAAqH,GACE,IAAKvF,EAAgBI,KAAKiF,WACxB,MAAM,IAAIpM,MAAM,mBAAmBmH,KAAKiF,uBAGnC,OAAA,IAAIrF,EAAgBI,KAAKiF,WAAWjF,KAAKC,QAASD,KAAKE,SAC/D,CAGD,eAAWU,GACF,MAhEE,YAiEV,CAID,kBAAO/B,IAAewG,GACpB,OAAOxG,EAAYwG,EACpB,CAED,cAAOC,IAAWD,GACT,MD9FK,EAACrK,EAAQ,GAAIuK,EAAQ,MAC7BA,EAAAxN,SAASyN,IACb,IAAA,IAASlK,EAAI,EAAGA,EAAIN,EAAMoB,OAAQd,IAC5BN,EAAMM,KAAOkK,GACTxK,EAAAyK,OAAOnK,EAAG,EAInB,IAGIN,GCmFEsK,IAAWD,EACnB,CAED,WAAOK,IAAQL,GACN,MDpFE,EAACrK,EAAQ,GAAIuK,EAAQ,IAC5BvK,EAAMoB,OAASmJ,EACVvK,EAGMA,EAAMG,MAAM,EAAGoK,GC+ErBG,IAAQL,EAChB,CAED,gBAAOM,IAAaN,GACX,MD9EO,EAACrK,EAAQ,GAAIuK,EAAQ,IACjCvK,EAAMoB,OAASmJ,EACVvK,EAGMA,EAAMG,MAAMH,EAAMoB,OAASmJ,EAAOvK,EAAMoB,QCyE9CuJ,IAAaN,EACrB,CAED,YAAOO,IAASP,GACP,MDxEG,KAAIA,KAChB,MAAM9F,EAAS,GAQR,OANF8F,EAAAtN,SAAS8N,IACN,MAAA3N,EAAQgE,MAAMC,QAAQ0J,GAAOA,EAAM,IAAI3J,MAAM2J,GAEnDtG,EAAOE,KAAKvH,EAAK,IAGZgH,EAAKL,EAAYU,GAAO,EC+DtBqG,IAASP,EACjB,CAED,cAAOS,IAAWT,GACT,MDhEK,EAACnN,KAAUmN,KACzB,MAAMrK,EAAQ6D,EAAY,IAAI3C,SAASmJ,IAEhC,OAAAjG,EAAOlH,EAAO8C,EAAK,EC6DjB8K,IAAWT,EACnB,CAED,WAAOnG,IAAQmG,GACN,OAAAnG,KAAQmG,EAChB,CAED,aAAOjG,IAAUiG,GACR,OAAAjG,KAAUiG,EAClB,CAED,UAAOU,IAAOV,GACL,MD/BC,KAAIA,KACd,MAAM9F,EAAS,GAITyG,EAFUX,EAAKzK,KAAK4K,GAASA,EAAKpJ,SAEd0C,QAAO,CAAC7D,EAAGC,IAAMwF,KAAKC,IAAI1F,EAAGC,KAEvD,IAAA,IAASI,EAAI,EAAGA,EAAI0K,EAAW1K,IACtBiE,EAAAjE,GAAK,GACP+J,EAAAtN,SAASiD,IACZuE,EAAOjE,GAAGmE,KAAKzE,EAAMM,GAAE,IAIpB,OAAAiE,CAAA,ECiBEwG,IAAOV,EACf,CAED,gBAAOY,IAAaZ,GACX,MDlBO,EAACvN,EAAO,GAAIwH,EAAS,MACrC,MAAMC,EAAS,CAAA,EAMR,OAJFzH,EAAAC,SAAQ,CAACmJ,EAAKkD,KACV7E,EAAA2B,GAAO5B,EAAO8E,EAAK,IAGrB7E,CAAA,ECWE0G,IAAaZ,EACrB,CAID,cAAOa,IAAWb,GACT,MC1IK,EAACc,EAAa,GAAIjO,KAChC,MAAMC,SAAmBD,EACnBkO,EAAiBlK,MAAMC,QAAQgK,GAC/B5G,EAAS,CAAA,EAEf,GAAI6G,EAAgB,CAClB,MAAMtO,EACU,aAAdK,EACIgO,EAAWvL,KAAK4K,GAAStN,EAAMsN,KAC/BW,EAAWvL,KAAK4K,GAASA,EAAKtN,KAEnC,IAAG,IAAIiH,IAAIrH,IAAOC,SAASmJ,IAC1B3B,EAAO2B,GAAO,EAETpJ,EAAAC,SAASyN,IACRtE,IAAQsE,GACVjG,EAAO2B,IAGT,GACD,GAEP,KAAS,CACC,MAAAmF,EAAUxO,OAAOC,KAAKqO,GACtBrO,EACU,aAAdK,EACIkO,EAAQzL,KAAK4K,GAAStN,EAAMiO,EAAWX,MACvCa,EAAQzL,KAAK4K,GAASW,EAAWX,GAAMtN,KAE5C,IAAG,IAAIiH,IAAIrH,IAAOC,SAASmJ,IAC1B3B,EAAO2B,GAAO,EAETpJ,EAAAC,SAASyN,IACRtE,IAAQsE,GACVjG,EAAO2B,IAGT,GACD,GAEJ,CAEM,OAAA3B,CAAA,EDgGE2G,IAAWb,EACnB,CAED,cAAOiB,IAAWjB,GACT,MCjGK,EAACc,EAAa,GAAIjO,KAChC,MAAMC,SAAmBD,EACnBkO,EAAiBlK,MAAMC,QAAQgK,GAC/B5G,EAAS,CAAA,EAEf,GAAI6G,EAAgB,CAClB,MAAMtO,EACU,aAAdK,EACIgO,EAAWvL,KAAK4K,GAAStN,EAAMsN,KAC/BW,EAAWvL,KAAK4K,GAASA,EAAKtN,KAEnC,IAAG,IAAIiH,IAAIrH,IAAOC,SAASmJ,IACnB3B,EAAA2B,GAAO,GAETpJ,EAAAC,SAAQ,CAACyN,EAAMpB,KACdlD,IAAQsE,GACVjG,EAAO2B,GAAKzB,KAAK0G,EAAW/B,GAG9B,GACD,GAEP,KAAS,CACC,MAAAiC,EAAUxO,OAAOC,KAAKqO,GACtBrO,EACU,aAAdK,EACIkO,EAAQzL,KAAK4K,GAAStN,EAAMiO,EAAWX,MACvCa,EAAQzL,KAAK4K,GAASW,EAAWX,GAAMtN,KAE5C,IAAG,IAAIiH,IAAIrH,IAAOC,SAASmJ,IACnB3B,EAAA2B,GAAO,GAETpJ,EAAAC,SAAQ,CAACyN,EAAMpB,KACdlD,IAAQsE,GACVjG,EAAO2B,GAAKzB,KAAK0G,EAAWE,EAAQjC,IAGtC,GACD,GAEJ,CAEM,OAAA7E,CAAA,EDuDE+G,IAAWjB,EACnB,CAED,aAAOkB,IAAUlB,GACR,MCYI,EAACc,EAAa,GAAI7G,EAAS,MACxC,IAAIC,EAAS,GAEP,MAAAiH,EAAatK,MAAMC,QAAQmD,GAE3BlD,EAASoK,EAAalH,EAAOlD,OAAS,EAOxCmD,EALmBrD,MAAMC,QAAQgK,GAGN,iBAAlBA,EAAW,GAEXA,EAAWvD,MAAK,CAAC3H,EAAGC,KAC3B,IAAA,IAASI,EAAI,EAAGA,EAAIc,EAAQd,IAAK,CACzB,MAAAmL,EAASD,EAAavL,EAAEqE,EAAOhE,IAAMgE,EAAOrE,GAC5CyL,EAASF,EAAatL,EAAEoE,EAAOhE,IAAMgE,EAAOpE,GAElD,GAAIuL,EAASC,EACJ,OAAA,EAGT,GAAID,EAASC,EACJ,OAAA,CAIV,KAGMP,EAAWvD,OAGT/K,OAAOC,KAAKqO,GAECvL,KAAKsG,GAAQiF,EAAWjF,KAE5B0B,OAGjB,OAAArD,CAAA,EDnDEgH,IAAUlB,EAClB,CAED,cAAOsB,IAAWtB,GACT,MC5DK,EAACc,EAAa,GAAI7G,EAAS,GAAIsH,EAAQ,MACrD,IAAIrH,EAAS,GAET,GAAAqH,EAAMxK,OAASkD,EAAOlD,OACxB,IAAA,IAASd,EAAIsL,EAAMxK,OAAQd,EAAIgE,EAAOlD,OAAQd,IAC5CsL,EAAMtL,GAAK,MAIT,MAAAkL,EAAatK,MAAMC,QAAQmD,GAE3BlD,EAASoK,EAAalH,EAAOlD,OAAS,EAErBF,MAAMC,QAAQgK,GAGN,iBAAlBA,EAAW,GAEpB5G,EAAS4G,EAAWvD,MAAK,CAAC3H,EAAGC,KAC3B,IAAA,IAASI,EAAI,EAAGA,EAAIc,EAAQd,IAAK,CACzB,MAAAmL,EAASD,EAAavL,EAAEqE,EAAOhE,IAAMgE,EAAOrE,GAC5CyL,EAASF,EAAatL,EAAEoE,EAAOhE,IAAMgE,EAAOpE,GAE9C,GAAa,SAAb0L,EAAMtL,GAAN,CAYJ,GAAImL,EAASC,EACJ,OAAA,EAGT,GAAID,EAASC,EACJ,OAAA,CAPR,KAVG,CACF,GAAID,EAASC,EACJ,OAAA,EAGT,GAAID,EAASC,EACJ,OAAA,CAIV,CAWF,MAGHnH,EAAS4G,EAAWvD,OAEL,SAAXtD,GAAmC,SAAdA,EAAO,IAC9BC,EAAOsH,YAQXtH,EAJa1H,OAAOC,KAAKqO,GAECvL,KAAKsG,GAAQiF,EAAWjF,KAE5B0B,OAEP,SAAXtD,GAAmC,SAAdA,EAAO,IAC9BC,EAAOsH,WAIJ,OAAAtH,CAAA,EDLEoH,IAAWtB,EACnB,CAID,aAAOyB,IAAUzB,GACR,ME7JI,EAAC0B,EAAS,MACjB,MAAAjP,EAAOD,OAAOC,KAAKiP,GAEnBxH,EAAS,CAAA,EAMR,OAJFzH,EAAAC,SAASmJ,IACL3B,EAAAwH,EAAO7F,IAAQA,CAAA,IAGjB3B,CAAA,EFoJEuH,IAAUzB,EAClB,CAED,eAAO2B,IAAY3B,GACV,MErJM,EAAC0B,EAAS,CAAA,EAAIE,EAAO,CAAC/F,GAAQA,MACvC,MAAApJ,EAAOD,OAAOC,KAAKiP,GAEnBxH,EAAS,CAAA,EAYR,OAVFzH,EAAA8C,KAAKsG,IACR,MAAMgG,EAASD,EAAKF,EAAO7F,IAM3B,OAJKhF,MAAMC,QAAQoD,EAAO2H,MACjB3H,EAAA2H,GAAU,IAGZ3H,EAAO2H,GAAQzH,KAAKyB,EAAG,IAGzB3B,CAAA,EFsIEyH,IAAY3B,EACpB,CAED,WAAO8B,IAAQ9B,GACN,MEvIE,EAAC0B,EAAS,GAAIjP,EAAO,MAC1B,MAAAsP,EAAavP,OAAOC,KAAKiP,GACzBxH,EAAS,CAAA,EAUR,OAPJ6H,EAAAhM,QAAQ8F,IACCpJ,EAAK0H,SAAS0B,KAEvBnJ,SAASmJ,IACD3B,EAAA2B,GAAO6F,EAAO7F,EAAG,IAGrB3B,CAAA,EF2HE4H,IAAQ9B,EAChB,CAED,aAAOgC,IAAUhC,GACR,ME5HI,EAAC0B,EAAS,CAAE,EAAEE,EAAO,MAAM,MAClC,MAAAG,EAAavP,OAAOC,KAAKiP,GACzBxH,EAAS,CAAA,EAUR,OAPJ6H,EAAAhM,QAAQ8F,IACC+F,EAAKF,EAAO7F,MAErBnJ,SAASmJ,IACD3B,EAAA2B,GAAO6F,EAAO7F,EAAG,IAGrB3B,CAAA,EFgHE8H,IAAUhC,EAClB,CAED,WAAOiC,IAAQjC,GACN,MEjHE,EAAC0B,EAAS,GAAIjP,EAAO,MAC1B,MAAAsP,EAAavP,OAAOC,KAAKiP,GACzBxH,EAAS,CAAA,EAUR,OAPJ6H,EAAAhM,QAAQ8F,GACApJ,EAAK0H,SAAS0B,KAEtBnJ,SAASmJ,IACD3B,EAAA2B,GAAO6F,EAAO7F,EAAG,IAGrB3B,CAAA,EFqGE+H,IAAQjC,EAChB,CAED,aAAOkC,IAAUlC,GACR,MEtGI,EAAC0B,EAAS,CAAE,EAAEE,EAAO,MAAM,MAClC,MAAAG,EAAavP,OAAOC,KAAKiP,GACzBxH,EAAS,CAAA,EAUR,OAPJ6H,EAAAhM,QAAQ8F,GACA+F,EAAKF,EAAO7F,MAEpBnJ,SAASmJ,IACD3B,EAAA2B,GAAO6F,EAAO7F,EAAG,IAGrB3B,CAAA,EF0FEgI,IAAUlC,EAClB,CAED,gBAAOmC,IAAanC,GACX,ME3FO,EAAC0B,EAAS,CAAA,EAAIE,EAAMQ,EAAc,CAAA,IAC3C5P,OAAOC,KAAKiP,GAAQjI,QAAO,CAAC7D,EAAGC,KACpC,MAAMwM,EAAST,EAAKhM,EAAG8L,EAAO7L,GAAIA,GAElC,YAAe,IAAXwM,EACKA,EAGFD,CAAA,GACNA,GFkFMD,IAAanC,EACrB,CAID,qBAAOxJ,IAAkBwJ,GAChB,OAAAxJ,KAAkBwJ,EAC1B,CAED,sBAAO7K,IAAmB6K,GACjB,OAAA7K,KAAmB6K,EAC3B"}