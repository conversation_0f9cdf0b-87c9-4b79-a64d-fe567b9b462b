!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e():"function"==typeof define&&define.amd?define(e):(t="undefined"!=typeof globalThis?globalThis:t||self).EcommerceGallery=e()}(this,(function(){"use strict";const t=t=>{do{t+=Math.floor(1e6*Math.random())}while(document.getElementById(t));return t},e=()=>{const{jQuery:t}=window;return t&&!document.body.hasAttribute("data-mdb-no-jquery")?t:null};document.documentElement.dir;const n=(()=>{const t={};let e=1;return{set(n,i,o){void 0===n[i]&&(n[i]={key:i,id:e},e++),t[n[i].id]=o},get(e,n){if(!e||void 0===e[n])return null;const i=e[n];return i.key===n?t[i.id]:null},delete(e,n){if(void 0===e[n])return;const i=e[n];i.key===n&&(delete t[i.id],delete e[n])}}})(),i={setData(t,e,i){n.set(t,e,i)},getData:(t,e)=>n.get(t,e),removeData(t,e){n.delete(t,e)}};let o=Element.prototype.querySelectorAll,s=Element.prototype.querySelector;const a=(()=>{const t=new CustomEvent("Bootstrap",{cancelable:!0}),e=document.createElement("div");return e.addEventListener("Bootstrap",(()=>null)),t.preventDefault(),e.dispatchEvent(t),t.defaultPrevented})(),r=/:scope\b/;(()=>{const t=document.createElement("div");try{t.querySelectorAll(":scope *")}catch(e){return!1}return!0})()||(o=function(e){if(!r.test(e))return this.querySelectorAll(e);const n=Boolean(this.id);n||(this.id=t("scope"));let i=null;try{e=e.replace(r,`#${this.id}`),i=this.querySelectorAll(e)}finally{n||this.removeAttribute("id")}return i},s=function(t){if(!r.test(t))return this.querySelector(t);const e=l.call(this,t);return void 0!==e[0]?e[0]:null});const l=o,c=s,d=e(),u=/[^.]*(?=\..*)\.|.*/,h=/\..*/,m=/::\d+$/,f={};let g=1;const v={mouseenter:"mouseover",mouseleave:"mouseout"},b=["click","dblclick","mouseup","mousedown","contextmenu","mousewheel","DOMMouseScroll","mouseover","mouseout","mousemove","selectstart","selectend","keydown","keypress","keyup","orientationchange","touchstart","touchmove","touchend","touchcancel","pointerdown","pointermove","pointerup","pointerleave","pointercancel","gesturestart","gesturechange","gestureend","focus","blur","change","reset","select","submit","focusin","focusout","load","unload","beforeunload","resize","move","DOMContentLoaded","readystatechange","error","abort","scroll"];function p(t,e){return e&&`${e}::${g++}`||t.uidEvent||g++}function _(t){const e=p(t);return t.uidEvent=e,f[e]=f[e]||{},f[e]}function E(t,e,n=null){const i=Object.keys(t);for(let o=0,s=i.length;o<s;o++){const s=t[i[o]];if(s.originalHandler===e&&s.delegationSelector===n)return s}return null}function y(t,e,n){const i="string"==typeof e,o=i?n:e;let s=t.replace(h,"");const a=v[s];a&&(s=a);return b.indexOf(s)>-1||(s=t),[i,o,s]}function x(t,e,n,i,o){if("string"!=typeof e||!t)return;n||(n=i,i=null);const[s,a,r]=y(e,n,i),l=_(t),c=l[r]||(l[r]={}),d=E(c,a,s?n:null);if(d)return void(d.oneOff=d.oneOff&&o);const h=p(a,e.replace(u,"")),m=s?function(t,e,n){return function i(o){const s=t.querySelectorAll(e);for(let{target:e}=o;e&&e!==this;e=e.parentNode)for(let a=s.length;a--;"")if(s[a]===e)return i.oneOff&&C.off(t,o.type,n),n.apply(e,[o]);return null}}(t,n,i):function(t,e){return function n(i){return n.oneOff&&C.off(t,i.type,e),e.apply(t,[i])}}(t,n);m.delegationSelector=s?n:null,m.originalHandler=a,m.oneOff=o,m.uidEvent=h,c[h]=m,t.addEventListener(r,m,s)}function I(t,e,n,i,o){const s=E(e[n],i,o);s&&(t.removeEventListener(n,s,Boolean(o)),delete e[n][s.uidEvent])}const C={on(t,e,n,i){x(t,e,n,i,!1)},one(t,e,n,i){x(t,e,n,i,!0)},off(t,e,n,i){if("string"!=typeof e||!t)return;const[o,s,a]=y(e,n,i),r=a!==e,l=_(t),c="."===e.charAt(0);if(void 0!==s){if(!l||!l[a])return;return void I(t,l,a,s,o?n:null)}c&&Object.keys(l).forEach((n=>{!function(t,e,n,i){const o=e[n]||{};Object.keys(o).forEach((s=>{if(s.indexOf(i)>-1){const i=o[s];I(t,e,n,i.originalHandler,i.delegationSelector)}}))}(t,l,n,e.slice(1))}));const d=l[a]||{};Object.keys(d).forEach((n=>{const i=n.replace(m,"");if(!r||e.indexOf(i)>-1){const e=d[n];I(t,l,a,e.originalHandler,e.delegationSelector)}}))},trigger(t,e,n){if("string"!=typeof e||!t)return null;const i=e.replace(h,""),o=e!==i,s=b.indexOf(i)>-1;let r,l=!0,c=!0,u=!1,m=null;return o&&d&&(r=d.Event(e,n),d(t).trigger(r),l=!r.isPropagationStopped(),c=!r.isImmediatePropagationStopped(),u=r.isDefaultPrevented()),s?(m=document.createEvent("HTMLEvents"),m.initEvent(i,l,!0)):m=new CustomEvent(e,{bubbles:l,cancelable:!0}),void 0!==n&&Object.keys(n).forEach((t=>{Object.defineProperty(m,t,{get:()=>n[t]})})),u&&(m.preventDefault(),a||Object.defineProperty(m,"defaultPrevented",{get:()=>!0})),c&&t.dispatchEvent(m),m.defaultPrevented&&void 0!==r&&r.preventDefault(),m}};function L(t){return"true"===t||"false"!==t&&(t===Number(t).toString()?Number(t):""===t||"null"===t?null:t)}function O(t){return t.replace(/[A-Z]/g,(t=>`-${t.toLowerCase()}`))}const S={setDataAttribute(t,e,n){t.setAttribute(`data-mdb-${O(e)}`,n)},removeDataAttribute(t,e){t.removeAttribute(`data-mdb-${O(e)}`)},getDataAttributes(t){if(!t)return{};const e={...t.dataset};return Object.keys(e).filter((t=>t.startsWith("mdb"))).forEach((t=>{let n=t.replace(/^mdb/,"");n=n.charAt(0).toLowerCase()+n.slice(1,n.length),e[n]=L(e[t])})),e},getDataAttribute:(t,e)=>L(t.getAttribute(`data-mdb-${O(e)}`)),offset(t){const e=t.getBoundingClientRect();return{top:e.top+document.body.scrollTop,left:e.left+document.body.scrollLeft}},position:t=>({top:t.offsetTop,left:t.offsetLeft}),style(t,e){Object.assign(t.style,e)},toggleClass(t,e){t&&(t.classList.contains(e)?t.classList.remove(e):t.classList.add(e))},addClass(t,e){t.classList.contains(e)||t.classList.add(e)},addStyle(t,e){Object.keys(e).forEach((n=>{t.style[n]=e[n]}))},removeClass(t,e){t.classList.contains(e)&&t.classList.remove(e)},hasClass:(t,e)=>t.classList.contains(e)},M={closest:(t,e)=>t.closest(e),matches:(t,e)=>t.matches(e),find:(t,e=document.documentElement)=>[].concat(...l.call(e,t)),findOne:(t,e=document.documentElement)=>c.call(e,t),children:(t,e)=>[].concat(...t.children).filter((t=>t.matches(e))),parents(t,e){const n=[];let i=t.parentNode;for(;i&&i.nodeType===Node.ELEMENT_NODE&&3!==i.nodeType;)this.matches(i,e)&&n.push(i),i=i.parentNode;return n},prev(t,e){let n=t.previousElementSibling;for(;n;){if(n.matches(e))return[n];n=n.previousElementSibling}return[]},next(t,e){let n=t.nextElementSibling;for(;n;){if(this.matches(n,e))return[n];n=n.nextElementSibling}return[]}},D="ecommerceGallery",w="mdb.ecommerceGallery",A="img:not(.ecommerce-gallery-main-img):not(.ecommerce-disabled)",j="slid.mdb.multiCarousel",k="slid.mdb.lightbox",T={activation:"string",autoHeight:"boolean",zoomEffect:"(string|boolean)"},$={activation:"click",autoHeight:!1,zoomEffect:!1};class H{constructor(t,e={}){this._element=t,this._options=e,this._activeImg=null,this._lightbox=null,this._toggleEvent=null,this._vertical=this._element.classList.contains("vertical"),this._animating=!1,this._element&&i.setData(t,w,this)}static get NAME(){return D}get options(){const t={...$,...S.getDataAttributes(this._element),...this._options};return((t,e,n)=>{Object.keys(n).forEach((i=>{const o=n[i],s=e[i],a=s&&((r=s)[0]||r).nodeType?"element":(t=>null==t?`${t}`:{}.toString.call(t).match(/\s([a-z]+)/i)[1].toLowerCase())(s);var r;if(!new RegExp(o).test(a))throw new Error(`${t.toUpperCase()}: Option "${i}" provided type "${a}" but expected type "${o}".`)}))})(D,t,T),t}get thumbnails(){const t=[];return M.find(A,this._element).forEach((e=>{t.push(e)})),t}get _multiCarousel(){return M.findOne(".multi-carousel",this._element)}init(){this._setGalleryData(),this._addMdbId(),this._lightbox&&this._appendLightboxContent(),this._addEvents()}dispose(){this._removeEvents(),i.removeData(this._element,w),this._element=null}_toggle(t){this._animating||t.target.dataset.mdbId===this._activeImg.dataset.activeImg||(this._animationStart(),this._toggleThumbsClass(t),this._toggleMainImg(t))}_setGalleryData(){this._activeImg=M.findOne(".ecommerce-gallery-main-img",this._element),S.addClass(this._activeImg,"active"),this._lightbox=M.findOne(".lightbox",this._element)}_animationStart(){this._animating=!0,setTimeout((()=>{this._animating=!1}),500)}_appendLightboxContent(){this._lightbox.innerHTML="",this.thumbnails.forEach((t=>{const e=(n="img",document.createElement(n));var n;e.src=t.dataset.mdbImg,e.dataset.mdbId=t.dataset.mdbId,e.alt=t.alt,t.dataset.caption&&(e.dataset.caption=t.dataset.caption),this.options.autoHeight&&S.style(e,{height:"auto"}),this._applyLigthboxImgClassList(e,t),this._lightbox.append(e)}))}_addMdbId(){this.thumbnails.forEach((e=>{e.dataset.mdbId=t("ecommerce-gallery-")}))}_addEvents(){this.thumbnails.forEach((t=>{this._toggleEvent=this._toggle.bind(this),C.on(t,this.options.activation,this._toggleEvent)})),this._multiCarousel&&(this._updateEventsHandler=this._updateEvents.bind(this),C.on(this._multiCarousel,j,this._updateEventsHandler)),this._lightbox&&(this._onLightboxSlideHandler=this._onLightboxSlide.bind(this),C.on(this._lightbox,k,this._onLightboxSlideHandler)),this.options.zoomEffect&&M.find("img",this._lightbox).forEach((t=>{C.on(t,"mousemove",this._onMainImgMousemove),C.on(t,"mouseleave",this._onMainImgMouseleave)}))}_updateEvents(t){M.closest(t.target,".ecommerce-gallery")===this._element&&this.thumbnails.forEach((t=>{this._toggleEvent=this._toggle.bind(this),C.on(t,this.options.activation,this._toggleEvent)}))}_removeEvents(){this.thumbnails.forEach((t=>{C.off(t,this.options.activation,this._toggleEvent)})),this._multiCarousel&&C.off(this._multiCarousel,j,this._updateEventsHandler),this._lightbox&&C.off(this._lightbox,k,this._onLightboxSlideHandler),this.options.zoomEffect&&M.find("img",this._lightbox).forEach((t=>{C.off(t,"mousemove",this._onMainImgMousemove)}))}_onLightboxSlide(){const t=mdb.Lightbox.getInstance(this._lightbox),e=t.activeImg,n=t.currentImg;M.find("img",this._lightbox).forEach(((t,n)=>{S.removeClass(t,"active"),n===e&&S.addClass(t,"active")})),M.find(A,this._element).forEach((t=>{S.removeClass(t,"active"),n.src!==t.src&&n.src!==t.dataset.mdbImg||S.addClass(t,"active")}))}_onMainImgMousemove(t){this._activeImg=t.target;const e=-(t.offsetX-this._activeImg.width/2)/2,n=-(t.offsetY-this._activeImg.height/2)/2;S.style(this._activeImg,{transform:`scale(4.5) translate(${e}px, ${n}px)`})}_onMainImgMouseleave(){S.style(this._activeImg,{transform:"scale(1)"})}_applyLigthboxImgClassList(t,e){this._activeImg.classList.forEach((e=>{"active"!==e&&S.addClass(t,e)})),e.classList.contains("active")&&S.addClass(t,"active")}_toggleThumbsClass(t){M.find(A,this._element).forEach((t=>{S.removeClass(t,"active")})),S.addClass(t.target,"active")}_toggleMainImg(t){M.find("img",this._lightbox).forEach((e=>{t.target.dataset.mdbId===e.dataset.mdbId?(this._activeImg=e,this._fadeIn(e)):this._fadeOut(e)}))}_fadeIn(t){["animation","fade-in","faster","active"].forEach((e=>S.addClass(t,e))),setTimeout((()=>{["animation","fade-in","faster"].forEach((e=>S.removeClass(t,e)))}),500)}_fadeOut(t){t.classList.contains("active")&&(["animating","animation","fade-out","faster"].forEach((e=>S.addClass(t,e))),setTimeout((()=>{["animation","animating","fade-out","faster","active"].forEach((e=>S.removeClass(t,e)))}),500))}static getInstance(t){return i.getData(t,w)}static jQueryInterface(t,e){return this.each((function(){let n=i.getData(this,w);const o="object"==typeof t&&t;if((n||!/dispose/.test(t))&&(n||(n=new H(this,o)),"string"==typeof t)){if(void 0===n[t])throw new TypeError(`No method named "${t}"`);n[t](e)}}))}}var N;return M.find("[data-mdb-ecommerce-gallery-init]").forEach((t=>{new H(t).init()})),M.find("[data-mdb-lightbox-init] img:not(.lightbox-disabled)").forEach((t=>{const e=mdb.Lightbox.getInstance(t);e&&e.dispose(),new mdb.Lightbox(t).init()})),N=()=>{const t=e();if(t){const e=t.fn[D];t.fn[D]=H.jQueryInterface,t.fn[D].Constructor=H,t.fn[D].noConflict=()=>(t.fn[D]=e,H.jQueryInterface)}},"loading"===document.readyState?document.addEventListener("DOMContentLoaded",N):N(),H}));
//# sourceMappingURL=ecommerce-gallery.min.js.map
