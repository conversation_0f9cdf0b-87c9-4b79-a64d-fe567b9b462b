!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e():"function"==typeof define&&define.amd?define(e):(t="undefined"!=typeof globalThis?globalThis:t||self).Onboarding=e()}(this,(function(){"use strict";const t=(t,e,n)=>{Object.keys(n).forEach((s=>{const i=n[s],o=e[s],r=o&&((a=o)[0]||a).nodeType?"element":(t=>null==t?`${t}`:{}.toString.call(t).match(/\s([a-z]+)/i)[1].toLowerCase())(o);var a;if(!new RegExp(i).test(r))throw new Error(`${t.toUpperCase()}: Option "${s}" provided type "${r}" but expected type "${i}".`)}))},e=()=>{const{jQuery:t}=window;return t&&!document.body.hasAttribute("data-mdb-no-jquery")?t:null};document.documentElement.dir;const n=(()=>{const t={};let e=1;return{set(n,s,i){void 0===n[s]&&(n[s]={key:s,id:e},e++),t[n[s].id]=i},get(e,n){if(!e||void 0===e[n])return null;const s=e[n];return s.key===n?t[s.id]:null},delete(e,n){if(void 0===e[n])return;const s=e[n];s.key===n&&(delete t[s.id],delete e[n])}}})(),s={setData(t,e,s){n.set(t,e,s)},getData:(t,e)=>n.get(t,e),removeData(t,e){n.delete(t,e)}};function i(t){return"true"===t||"false"!==t&&(t===Number(t).toString()?Number(t):""===t||"null"===t?null:t)}function o(t){return t.replace(/[A-Z]/g,(t=>`-${t.toLowerCase()}`))}const r={setDataAttribute(t,e,n){t.setAttribute(`data-mdb-${o(e)}`,n)},removeDataAttribute(t,e){t.removeAttribute(`data-mdb-${o(e)}`)},getDataAttributes(t){if(!t)return{};const e={...t.dataset};return Object.keys(e).filter((t=>t.startsWith("mdb"))).forEach((t=>{let n=t.replace(/^mdb/,"");n=n.charAt(0).toLowerCase()+n.slice(1,n.length),e[n]=i(e[t])})),e},getDataAttribute:(t,e)=>i(t.getAttribute(`data-mdb-${o(e)}`)),offset(t){const e=t.getBoundingClientRect();return{top:e.top+document.body.scrollTop,left:e.left+document.body.scrollLeft}},position:t=>({top:t.offsetTop,left:t.offsetLeft}),style(t,e){Object.assign(t.style,e)},toggleClass(t,e){t&&(t.classList.contains(e)?t.classList.remove(e):t.classList.add(e))},addClass(t,e){t.classList.contains(e)||t.classList.add(e)},addStyle(t,e){Object.keys(e).forEach((n=>{t.style[n]=e[n]}))},removeClass(t,e){t.classList.contains(e)&&t.classList.remove(e)},hasClass:(t,e)=>t.classList.contains(e)};let a=Element.prototype.querySelectorAll,l=Element.prototype.querySelector;const c=(()=>{const t=new CustomEvent("Bootstrap",{cancelable:!0}),e=document.createElement("div");return e.addEventListener("Bootstrap",(()=>null)),t.preventDefault(),e.dispatchEvent(t),t.defaultPrevented})(),h=/:scope\b/;(()=>{const t=document.createElement("div");try{t.querySelectorAll(":scope *")}catch(e){return!1}return!0})()||(a=function(t){if(!h.test(t))return this.querySelectorAll(t);const e=Boolean(this.id);e||(this.id=(t=>{do{t+=Math.floor(1e6*Math.random())}while(document.getElementById(t));return t})("scope"));let n=null;try{t=t.replace(h,`#${this.id}`),n=this.querySelectorAll(t)}finally{e||this.removeAttribute("id")}return n},l=function(t){if(!h.test(t))return this.querySelector(t);const e=u.call(this,t);return void 0!==e[0]?e[0]:null});const u=a,p=l,d=e(),_=/[^.]*(?=\..*)\.|.*/,b=/\..*/,g=/::\d+$/,m={};let f=1;const v={mouseenter:"mouseover",mouseleave:"mouseout"},S=["click","dblclick","mouseup","mousedown","contextmenu","mousewheel","DOMMouseScroll","mouseover","mouseout","mousemove","selectstart","selectend","keydown","keypress","keyup","orientationchange","touchstart","touchmove","touchend","touchcancel","pointerdown","pointermove","pointerup","pointerleave","pointercancel","gesturestart","gesturechange","gestureend","focus","blur","change","reset","select","submit","focusin","focusout","load","unload","beforeunload","resize","move","DOMContentLoaded","readystatechange","error","abort","scroll"];function y(t,e){return e&&`${e}::${f++}`||t.uidEvent||f++}function C(t){const e=y(t);return t.uidEvent=e,m[e]=m[e]||{},m[e]}function L(t,e,n=null){const s=Object.keys(t);for(let i=0,o=s.length;i<o;i++){const o=t[s[i]];if(o.originalHandler===e&&o.delegationSelector===n)return o}return null}function x(t,e,n){const s="string"==typeof e,i=s?n:e;let o=t.replace(b,"");const r=v[o];r&&(o=r);return S.indexOf(o)>-1||(o=t),[s,i,o]}function w(t,e,n,s,i){if("string"!=typeof e||!t)return;n||(n=s,s=null);const[o,r,a]=x(e,n,s),l=C(t),c=l[a]||(l[a]={}),h=L(c,r,o?n:null);if(h)return void(h.oneOff=h.oneOff&&i);const u=y(r,e.replace(_,"")),p=o?function(t,e,n){return function s(i){const o=t.querySelectorAll(e);for(let{target:e}=i;e&&e!==this;e=e.parentNode)for(let r=o.length;r--;"")if(o[r]===e)return s.oneOff&&I.off(t,i.type,n),n.apply(e,[i]);return null}}(t,n,s):function(t,e){return function n(s){return n.oneOff&&I.off(t,s.type,e),e.apply(t,[s])}}(t,n);p.delegationSelector=o?n:null,p.originalHandler=r,p.oneOff=i,p.uidEvent=u,c[u]=p,t.addEventListener(a,p,o)}function P(t,e,n,s,i){const o=L(e[n],s,i);o&&(t.removeEventListener(n,o,Boolean(i)),delete e[n][o.uidEvent])}const I={on(t,e,n,s){w(t,e,n,s,!1)},one(t,e,n,s){w(t,e,n,s,!0)},off(t,e,n,s){if("string"!=typeof e||!t)return;const[i,o,r]=x(e,n,s),a=r!==e,l=C(t),c="."===e.charAt(0);if(void 0!==o){if(!l||!l[r])return;return void P(t,l,r,o,i?n:null)}c&&Object.keys(l).forEach((n=>{!function(t,e,n,s){const i=e[n]||{};Object.keys(i).forEach((o=>{if(o.indexOf(s)>-1){const s=i[o];P(t,e,n,s.originalHandler,s.delegationSelector)}}))}(t,l,n,e.slice(1))}));const h=l[r]||{};Object.keys(h).forEach((n=>{const s=n.replace(g,"");if(!a||e.indexOf(s)>-1){const e=h[n];P(t,l,r,e.originalHandler,e.delegationSelector)}}))},trigger(t,e,n){if("string"!=typeof e||!t)return null;const s=e.replace(b,""),i=e!==s,o=S.indexOf(s)>-1;let r,a=!0,l=!0,h=!1,u=null;return i&&d&&(r=d.Event(e,n),d(t).trigger(r),a=!r.isPropagationStopped(),l=!r.isImmediatePropagationStopped(),h=r.isDefaultPrevented()),o?(u=document.createEvent("HTMLEvents"),u.initEvent(s,a,!0)):u=new CustomEvent(e,{bubbles:a,cancelable:!0}),void 0!==n&&Object.keys(n).forEach((t=>{Object.defineProperty(u,t,{get:()=>n[t]})})),h&&(u.preventDefault(),c||Object.defineProperty(u,"defaultPrevented",{get:()=>!0})),l&&t.dispatchEvent(u),u.defaultPrevented&&void 0!==r&&r.preventDefault(),u}},k={closest:(t,e)=>t.closest(e),matches:(t,e)=>t.matches(e),find:(t,e=document.documentElement)=>[].concat(...u.call(e,t)),findOne:(t,e=document.documentElement)=>p.call(e,t),children:(t,e)=>[].concat(...t.children).filter((t=>t.matches(e))),parents(t,e){const n=[];let s=t.parentNode;for(;s&&s.nodeType===Node.ELEMENT_NODE&&3!==s.nodeType;)this.matches(s,e)&&n.push(s),s=s.parentNode;return n},prev(t,e){let n=t.previousElementSibling;for(;n;){if(n.matches(e))return[n];n=n.previousElementSibling}return[]},next(t,e){let n=t.nextElementSibling;for(;n;){if(this.matches(n,e))return[n];n=n.nextElementSibling}return[]}},T="onboarding",E="mdb.onboarding",O=`start.${E}`,$=`end.${E}`,D=`open.${E}`,B=`close.${E}`,H=`next.${E}`,R=`prev.${E}`,M={nextLabel:"Next",prevLabel:"Back",skipLabel:"Skip",finishLabel:"Finish",pauseLabel:"Pause",resumeLabel:"Resume",steps:[],startTrigger:"",startEvent:"click",autostart:!1,autoplay:!1,startDelay:0,stepsDuration:0,autoscroll:!0,startIndex:1,debounce:300,backdrop:!1,backdropOpacity:.5,btnMain:"btn-primary",btnClose:"btn-danger",btnPause:"btn-primary",btnResume:"btn-success",autofocus:!1,customClass:""},A={nextLabel:"string",prevLabel:"string",skipLabel:"string",finishLabel:"string",pauseLabel:"string",resumeLabel:"string",steps:"array",startTrigger:"string",startEvent:"string",autostart:"boolean",autoplay:"boolean",startDelay:"number",stepsDuration:"number",autoscroll:"boolean",startIndex:"number",debounce:"number",backdrop:"boolean",backdropOpacity:"number",btnMain:"string",btnClose:"string",btnPause:"string",btnResume:"string",autofocus:"boolean",customClass:"string"},j={nextLabel:"string",prevLabel:"string",skipLabel:"string",finishLabel:"string",pauseLabel:"string",resumeLabel:"string",btnMain:"string",btnClose:"string",btnPause:"string",btnResume:"string",onboardingContent:"string",placement:"string",index:"number",target:"(string || null)",node:"element",backdrop:"(boolean || null)",backdropOpacity:"number",duration:"number",autoplay:"boolean",title:"string"},N={nextLabel:"",prevLabel:"",skipLabel:"",finishLabel:"",pauseLabel:"",resumeLabel:"",btnMain:"",btnClose:"",btnPause:"",btnResume:"",onboardingContent:"",placement:"bottom",index:null,target:null,node:null,backdrop:null,backdropOpacity:.6,duration:0,autoplay:!0,title:""};class W{constructor(t,e={}){this._element=t,this._options=this._getConfig(e),this._triggerElement=null,this._steps=null,this._currentStepIndex=null,this._currentStep=null,this._currentPopover=null,this._isPopoverOpen=!1,this._container=null,this._canvas=null,this._ctx=null,this._debounceTimeId=null,this._autoPlayInterval=null,this._remainingInterval=null,this._isPaused=!1,this._startTime=null,this._observer=null,this._openStepClickHandler=this._handleOpenStepClicks.bind(this),this._openStepKeydownHandler=this._debounceStepKeyDown.bind(this),this._canvasScrollHandler=this._handleCanvasContainerScroll.bind(this),this._canvasResizeHandler=this._handleCanvasResize.bind(this),this._element&&s.setData(t,E,this),this._init()}get prevBtn(){return k.findOne(".prev",this._currentPopover.tip)}get nextBtn(){return k.findOne(".next",this._currentPopover.tip)}get closeBtn(){return k.findOne(".end",this._currentPopover.tip)}get controlBtn(){return k.findOne(".control",this._currentPopover.tip)}dispose(){this._isPopoverOpen&&this._handlePopoverClose(),s.removeData(this._element,E),this._element=null}open(t){this._setCurrentStepIndex(t||this._options.startIndex-1),this._handleToggleStep()}close(){this._handlePopoverClose()}nextStep(){this._setCurrentStepIndex(this._currentStepIndex+1),this._handleToggleStep()}prevStep(){this._setCurrentStepIndex(this._currentStepIndex-1),this._handleToggleStep()}pause(){this._pauseInterval()}resume(){this._resumeInterval()}_getConfig(e){const n=r.getDataAttributes(this._element);return e={...M,...n,...e},t(T,e,A),e}_init(){if(this._options.steps.length)this._steps=this._getStepsFromJS(this._options.steps);else{const t=k.find("[data-mdb-step]",this._element);this._steps=this._getStepsFromHTML(t)}this._options.autoplay&&this._steps.forEach((t=>{t.duration=0!==t.duration?t.duration:this._options.stepsDuration})),this._initPopovers(),this._getStartingOptions()}_getStepsFromJS(e){const n=[];return e.forEach((e=>{const s=k.findOne(`[data-mdb-target=${e.target}]`);e={...N,...e,node:s,nextLabel:e.nextLabel?e.nextLabel:this._options.nextLabel,prevLabel:e.prevLabel?e.prevLabel:this._options.prevLabel,skipLabel:e.skipLabel?e.skipLabel:this._options.skipLabel,finishLabel:e.finishLabel?e.finishLabel:this._options.finishLabel,pauseLabel:e.pauseLabel?e.pauseLabel:this._options.pauseLabel,resumeLabel:e.resumeLabel?e.resumeLabel:this._options.resumeLabel,btnMain:e.btnMain?e.btnMain:this._options.btnMain,btnClose:e.btnClose?e.btnClose:this._options.btnClose,btnPause:e.btnPause?e.btnPause:this._options.btnPause,btnResume:e.btnResume?e.btnResume:this._options.btnResume},t(T,e,j),n.push(e)})),n.sort(((t,e)=>this._sortByStepIndex(t,e)))}_getStepsFromHTML(t){const e=[];return t.forEach((t=>{e.push(this._parseHTMLSteps(t))})),e.sort(((t,e)=>this._sortByStepIndex(t,e)))}_parseHTMLSteps(e){const n=r.getDataAttributes(e);if(!n)return;const s={...N,...n,index:parseInt(n.index,10),node:e,nextLabel:n.nextLabel?n.nextLabel:this._options.nextLabel,prevLabel:n.prevLabel?n.prevLabel:this._options.prevLabel,skipLabel:n.skipLabel?n.skipLabel:this._options.skipLabel,finishLabel:n.finishLabel?n.finishLabel:this._options.finishLabel,pauseLabel:n.pauseLabel?n.pauseLabel:this._options.pauseLabel,resumeLabel:n.resumeLabel?n.resumeLabel:this._options.resumeLabel,btnMain:n.btnMain?n.btnMain:this._options.btnMain,btnClose:n.btnClose?n.btnClose:this._options.btnClose,btnPause:n.btnPause?n.btnPause:this._options.btnPause,btnResume:n.btnResume?n.btnResume:this._options.btnResume};return t(T,s,j),s}_sortByStepIndex(t,e){return t.index<e.index?-1:t.index>e.index?1:0}_getStartingOptions(){const{startTrigger:t,startEvent:e,autostart:n,startDelay:s}=this._options;if((n||0!==s)&&window.setTimeout((()=>{this._start()}),1e3*s),t&&e){let n=k.findOne(t,this._element);"window"===t&&(n=window),this._triggerElement=n,I.on(this._triggerElement,e,(()=>{this._start()}))}}_start(){this._currentStep&&this._handlePopoverClose(),I.trigger(this._element,O),this._setCurrentStepIndex(this._options.startIndex-1),this._toggleStep()}_handleAutoToggle(){this._autoPlayInterval=setInterval((()=>{this._currentStepIndex+1>=this._steps.length?this._handlePopoverClose():(this._setCurrentStepIndex(this._currentStepIndex+1),this._toggleStep(),this._startTime=Date.now())}),1e3*this._currentStep.duration)}_setCurrentStepIndex(t){t>this._steps.length-1||t<0||(t>this._currentStepIndex?I.trigger(this._element,H,{onboarding:this._element,nextStepIndex:t,currentStepIndex:this._currentStepIndex}):t<this._currentStepIndex&&I.trigger(this._element,R,{onboarding:this._element,prevStepIndex:t,currentStepIndex:this._currentStepIndex}),this._currentStepIndex=t)}_toggleStep(){this._currentStep=this._steps[this._currentStepIndex],this._checkStepAutoPlay();const t=this._currentStep.popover;this._currentPopover&&this._currentPopover._element!==t._element&&this._currentPopover.hide(),this._currentPopover=t,this._options.autoscroll?this._handleWithScrollToggle():this._currentPopover.show(),!1!==this._currentStep.backdrop&&null!==this._currentStep.backdrop||this._options.backdrop&&!1!==this._currentStep.backdrop?(this._canvas||this._createCanvas(),this._createBackdrop()):this._clearCanvas(),setTimeout((()=>{this._setOpenStepEventHandlers()})),this._isPopoverOpen=!0,I.trigger(this._element,D,{onboarding:this._element,currentStep:this._currentStep})}_createPopoverObserver(){this._observer=new MutationObserver((()=>{const t=k.findOne(".popover");t&&t.classList.contains("show")&&(this._options.customClass&&r.addClass(t,this._options.customClass),setTimeout((()=>{t.setAttribute("tabindex","-1"),this._options.autofocus&&t.focus(),this._observer.disconnect()}),100))})),this._observer.observe(document.body,{childList:!0,subtree:!0,attributes:!1,characterData:!1})}_createBackdrop(){const{left:t}=this._getCanvasBounding(),e=this._currentStep.node,n=e.getBoundingClientRect();this._clearCanvas(),this._fillCanvas(),this._container&&this._container!==window?(this._ctx.clearRect(n.left-5-t,e.offsetTop-this._container.offsetTop-5-this._container.scrollTop,n.width+10,n.height+10),I.on(this._container,"scroll",this._canvasScrollHandler)):this._ctx.clearRect(n.left-5,n.top-5+window.scrollY,n.width+10,n.height+10),I.on(window,"resize",this._canvasResizeHandler)}_createCanvas(){const t=document.createElement("canvas");r.addClass(t,"onboarding-backdrop"),this._canvas=t;const{left:e,top:n,width:s,height:i}=this._getCanvasBounding();this._setCanvasDimensions(e,n,s,i),this._container&&this._container!==window?this._container.appendChild(t):document.body.appendChild(t),this._ctx=this._canvas.getContext("2d")}_clearCanvas(){this._canvas&&(this._ctx.setTransform(1,0,0,1,0,0),this._ctx.clearRect(0,0,this._canvas.width,this._canvas.height))}_fillCanvas(){let t;this._currentStep.backdrop?t=this._currentStep.backdropOpacity:this._options.backdrop&&(t=this._options.backdropOpacity),this._ctx.fillStyle=`rgba(0,0,0, ${t})`,this._ctx.fillRect(0,0,this._canvas.width,this._canvas.height)}_getCanvasBounding(){let t,e,n,s;if(this._container&&this._container!==window){const i=this._container.getBoundingClientRect();t=i.left,e=i.top+window.scrollY,n=this._container.clientWidth,s=i.height}else{const i=document.body,o=document.documentElement,r=Math.max(i.scrollHeight,i.offsetHeight,o.clientHeight,o.scrollHeight,o.offsetHeight);t=0,e=0,n=i.clientWidth,s=r}return{left:t,top:e,width:n,height:s}}_setCanvasDimensions(t,e,n,s){this._canvas.style.top=`${e}px`,this._canvas.style.left=`${t}px`,this._canvas.style.width=`${n}px`,this._canvas.style.height=`${s}px`,this._canvas.style.position="absolute",this._canvas.width=n,this._canvas.height=s}_removeCanvas(){this._canvas.parentNode.removeChild(this._canvas),I.off(this._container,"scroll",this._canvasScrollHandler),I.off(window,"resize",this._canvasResizeHandler),this._canvas=null,this._ctx=null}_handleCanvasContainerScroll(){if(!1===this._currentStep.backdrop&&!this._currentStep.backdrop||!this._options.backdrop&&!this._currentStep.backdrop)return void I.off(this._container,"scroll",this._canvasScrollHandler);const{left:t}=this._getCanvasBounding(),e=this._steps[this._currentStepIndex].node,n=e.getBoundingClientRect();this._clearCanvas(),this._fillCanvas(),this._ctx.clearRect(n.left-5-t,e.offsetTop-this._container.offsetTop-5-this._container.scrollTop,n.width+10,n.height+10)}_handleCanvasResize(){this._canvas&&(this._removeCanvas(),this._createCanvas(),this._createBackdrop())}_handleWithScrollToggle(){return this._handleScrollIntoStep().then((()=>{this._currentPopover&&this._currentPopover.show()}))}_setOpenStepEventHandlers(){I.on(document,"click",this._openStepClickHandler),I.on(document,"keydown",this._openStepKeydownHandler),this._createPopoverObserver()}_initPopovers(){this._steps.forEach((t=>{this._createPopover(t)}))}_createPopover(t){const e=this._popoverContentTemplate(t);t.popover=new mdb.Popover(t.node,{trigger:"manual",placement:t.placement,html:!0,content:e,sanitize:!1,title:t.title.length>0?t.title:`${t.index} / ${this._steps.length}`})}_handleScrollIntoStep(){const t=this._steps[this._currentStepIndex],e=this._getScrollContainer(t.node);return e!==document.body?(this._container=e,this._scrollContainerToStep(t)):(this._container=window,this._scrollWindowToStep(t))}_scrollWindowToStep(t){const e=window.innerHeight,n=window.scrollY,s=t.node.getBoundingClientRect(),i=s.height,o=s.top+n,r=this._getScrollTop(t.placement,o,i,e);return new Promise((t=>{this._scrollTo(r).then((()=>{t()}))}))}_scrollContainerToStep(t){const e=window.innerHeight,n=window.scrollY,s=this._container.getBoundingClientRect(),i=s.height,o=s.top+n,r=this._container.scrollHeight-this._container.clientHeight,a=t.node.clientHeight,l=t.node.offsetTop-o,c=Math.min(this._getScrollTop(t.placement,l,a,i),r);let h;return i<e?h=o-(e-i)/2:i>e&&(h=o+c),new Promise((t=>{this._scrollTo(c).then((()=>{window.scrollTo({top:h,behavior:"smooth"}),t()}))}))}_getScrollTop(t,e,n,s){switch(t){case"top":return Math.max(0,e-s/2);case"left":case"right":return Math.max(0,e+n/2-s/2);default:return Math.max(0,e+n-s/2)}}_scrollTo(t){return new Promise((e=>{this._isContainerScrollable()||e();const n=s=>{void 0===s&&e();const i=s.target.scrollTop||window.scrollY;this._compareWithinRange(i,t,5)&&(I.off(this._container,"scroll",n),e())};I.on(this._container,"scroll",n),this._container.scrollTo({top:t,behavior:"smooth"}),(this._compareWithinRange(this._container.scrollTop,t,5)||this._compareWithinRange(this._container.scrollY,t,5))&&(I.off(this._container,"scroll",n),e())}))}_isContainerScrollable(){return this._container.scrollHeight>this._container.clientHeight}_compareWithinRange(t,e,n){return t>=e-n&&t<=e+n}_getScrollContainer(t){let e=getComputedStyle(t);const n="absolute"===e.position,s=/(auto|scroll)/;if("fixed"===e.position)return document.body;for(let i=t;i=i.parentElement;)if(e=getComputedStyle(i),(!n||"static"!==e.position)&&s.test(e.overflow+e.overflowY+e.overflowX))return i;return document.body}_checkStepAutoPlay(){if(this._clearInterval(),0===this._currentStep.duration||this._autoPlayInterval||!1===this._currentStep.autoplay){if(0===this._currentStep.duration||!1===this._currentStep.autoplay)return}else this._handleAutoToggle()}_handleAutoPlayControls(){const{pauseLabel:t,btnPause:e,btnResume:n,resumeLabel:s}=this._currentStep;this._isPaused?(this._resumeInterval(),this._isPaused=!1,this.controlBtn.textContent=t,this._removeClasses(n,this.controlBtn),this._addClasses(e,this.controlBtn)):this._isPaused||(this._pauseInterval(),this._isPaused=!0,this.controlBtn.textContent=s,this._removeClasses(e,this.controlBtn),this._addClasses(n,this.controlBtn))}_addClasses(t,e){t.split(" ").forEach((t=>{r.addClass(e,t)}))}_removeClasses(t,e){t.split(" ").forEach((t=>{e.classList.contains(t)&&r.removeClass(e,t)}))}_pauseInterval(){const t=0!==this._currentStep.duration?this._currentStep.duration:this._options.stepsDuration;this._remainingInterval=1e3*t-(Date.now()-this._startTime),clearInterval(this._autoPlayInterval)}_resumeInterval(){this._setCurrentStepIndex(this._currentStepIndex+1),window.setTimeout((()=>{this._toggleStep()}),this._remainingInterval),this._remainingInterval=null,this._startTime=null}_clearInterval(){clearInterval(this._autoPlayInterval),this._autoPlayInterval=null,this._remainingInterval=null,this._startTime=null,this._isPaused=!1}_handlePopoverClose(){this._clearInterval(),this._currentStepIndex===this._steps.length-1&&I.trigger(this._element,$),I.trigger(this._element,B,{onboarding:this._element,currentStep:this._currentStep}),this._currentStep=null,this._currentPopover._popper&&this._currentPopover.hide(),this._currentPopover=null,this._isPopoverOpen=!1,this._canvas&&this._removeCanvas(),this._container=null,I.off(document,"click",this._openStepClickHandler),I.off(document,"keydown",this._openStepKeydownHandler)}_handleOpenStepClicks(t){if(!this._currentPopover)return;const{target:e}=t,n=e===this.prevBtn,s=e===this.nextBtn,i=e===this.closeBtn,o=e===this.controlBtn,r=!this._currentPopover.tip.contains(e);n?this.prevStep():s?this.nextStep():o?this._handleAutoPlayControls():(i||r)&&this.close()}_handleToggleStep(){this._autoPlayInterval&&this._clearInterval(),this._toggleStep()}_debounceStepKeyDown(t){const e=this._options.debounce;this._debounceTimeoutId&&clearTimeout(this._debounceTimeoutId),this._debounceTimeoutId=setTimeout((()=>{this._handleKeyDown(t)}),e)}_handleKeyDown(t){const{keyCode:e,shiftKey:n}=t;if(n)return t.preventDefault(),void(0!==this._currentStep.duration&&this._handleAutoPlayControls());switch(e){case 27:this.close();break;case 37:t.preventDefault(),this.prevStep();break;case 39:t.preventDefault(),this.nextStep();break;case 36:t.preventDefault(),this._setCurrentStepIndex(0),this._handleToggleStep();break;case 35:t.preventDefault(),this._setCurrentStepIndex(this._steps.length-1),this._handleToggleStep()}}_popoverContentTemplate(t){const{index:e,prevLabel:n,nextLabel:s,pauseLabel:i,resumeLabel:o,finishLabel:r,skipLabel:a,btnMain:l,btnClose:c,btnPause:h,btnResume:u,autoplay:p,duration:d,onboardingContent:_}=t,b=1===e,g=e===this._steps.length,m=g?r:a,f=this._isPaused?o:i,v=b?"disabled":"",S=g?"disabled":"",y=t=>`btn btn-sm ${t} mx-0`,C=y(l),L=y(c),x=this._isPaused?y(u):y(h);return`\n        <p class="popover-text">${_}</p>\n        <hr />\n        <button class="${C} ${v} prev" data-mdb-role="prev" data-mdb-ripple-init  aria-disabled="${!!v}">${n}\n    </button>\n      ${0!==d&&!1!==p?`<button class="${x} control" data-mdb-role="pause-resume" data-mdb-ripple-init >${f}</button>`:""}\n      <button class="${C} ${S} next" data-mdb-role="next" data-mdb-ripple-init  aria-disabled="${!!S}">${s}</button>\n        <button role="button" class="${L} float-right end" data-mdb-role="end" data-mdb-ripple-init >${m}</button>\n    `}static get NAME(){return T}static jQueryInterface(t,e){return this.each((function(){let n=s.getData(this,E);const i="object"==typeof t&&t;if((n||!/dispose/.test(t))&&(n||(n=new W(this,i)),"string"==typeof t)){if(void 0===n[t])throw new TypeError(`No method named "${t}"`);n[t](e)}}))}static getInstance(t){return s.getData(t,E)}}var z;return k.find("[data-mdb-onboarding-init]").forEach((t=>{let e=W.getInstance(t);return e||(e=new W(t)),e})),z=()=>{const t=e();if(t){const e=t.fn[T];t.fn[T]=W.jQueryInterface,t.fn[T].Constructor=W,t.fn[T].noConflict=()=>(t.fn[T]=e,W.jQueryInterface)}},"loading"===document.readyState?document.addEventListener("DOMContentLoaded",z):z(),W}));
//# sourceMappingURL=onboarding.min.js.map
