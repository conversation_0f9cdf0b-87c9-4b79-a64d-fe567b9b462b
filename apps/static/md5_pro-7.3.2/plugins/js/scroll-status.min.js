!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e():"function"==typeof define&&define.amd?define(e):(t="undefined"!=typeof globalThis?globalThis:t||self).ScrollStatus=e()}(this,(function(){"use strict";const t=t=>(t[0]||t).nodeType,e=()=>{const{jQuery:t}=window;return t&&!document.body.hasAttribute("data-mdb-no-jquery")?t:null};document.documentElement.dir;const n=(()=>{const t={};let e=1;return{set(n,o,s){void 0===n[o]&&(n[o]={key:o,id:e},e++),t[n[o].id]=s},get(e,n){if(!e||void 0===e[n])return null;const o=e[n];return o.key===n?t[o.id]:null},delete(e,n){if(void 0===e[n])return;const o=e[n];o.key===n&&(delete t[o.id],delete e[n])}}})(),o={setData(t,e,o){n.set(t,e,o)},getData:(t,e)=>n.get(t,e),removeData(t,e){n.delete(t,e)}};function s(t){return"true"===t||"false"!==t&&(t===Number(t).toString()?Number(t):""===t||"null"===t?null:t)}function i(t){return t.replace(/[A-Z]/g,(t=>`-${t.toLowerCase()}`))}const r={setDataAttribute(t,e,n){t.setAttribute(`data-mdb-${i(e)}`,n)},removeDataAttribute(t,e){t.removeAttribute(`data-mdb-${i(e)}`)},getDataAttributes(t){if(!t)return{};const e={...t.dataset};return Object.keys(e).filter((t=>t.startsWith("mdb"))).forEach((t=>{let n=t.replace(/^mdb/,"");n=n.charAt(0).toLowerCase()+n.slice(1,n.length),e[n]=s(e[t])})),e},getDataAttribute:(t,e)=>s(t.getAttribute(`data-mdb-${i(e)}`)),offset(t){const e=t.getBoundingClientRect();return{top:e.top+document.body.scrollTop,left:e.left+document.body.scrollLeft}},position:t=>({top:t.offsetTop,left:t.offsetLeft}),style(t,e){Object.assign(t.style,e)},toggleClass(t,e){t&&(t.classList.contains(e)?t.classList.remove(e):t.classList.add(e))},addClass(t,e){t.classList.contains(e)||t.classList.add(e)},addStyle(t,e){Object.keys(e).forEach((n=>{t.style[n]=e[n]}))},removeClass(t,e){t.classList.contains(e)&&t.classList.remove(e)},hasClass:(t,e)=>t.classList.contains(e)},l={closest:(t,e)=>t.closest(e),matches:(t,e)=>t.matches(e),find:(t,e=document.documentElement)=>[].concat(...Element.prototype.querySelectorAll.call(e,t)),findOne:(t,e=document.documentElement)=>Element.prototype.querySelector.call(e,t),children:(t,e)=>[].concat(...t.children).filter((t=>t.matches(e))),parents(t,e){const n=[];let o=t.parentNode;for(;o&&o.nodeType===Node.ELEMENT_NODE&&3!==o.nodeType;)this.matches(o,e)&&n.push(o),o=o.parentNode;return n},prev(t,e){let n=t.previousElementSibling;for(;n;){if(n.matches(e))return[n];n=n.previousElementSibling}return[]},next(t,e){let n=t.nextElementSibling;for(;n;){if(this.matches(n,e))return[n];n=n.nextElementSibling}return[]}},a=e(),c=/[^.]*(?=\..*)\.|.*/,d=/\..*/,u=/::\d+$/,h={};let f=1;const p={mouseenter:"mouseover",mouseleave:"mouseout"},g=["click","dblclick","mouseup","mousedown","contextmenu","mousewheel","DOMMouseScroll","mouseover","mouseout","mousemove","selectstart","selectend","keydown","keypress","keyup","orientationchange","touchstart","touchmove","touchend","touchcancel","pointerdown","pointermove","pointerup","pointerleave","pointercancel","gesturestart","gesturechange","gestureend","focus","blur","change","reset","select","submit","focusin","focusout","load","unload","beforeunload","resize","move","DOMContentLoaded","readystatechange","error","abort","scroll"];function m(t,e){return e&&`${e}::${f++}`||t.uidEvent||f++}function _(t){const e=m(t);return t.uidEvent=e,h[e]=h[e]||{},h[e]}function b(t,e,n=null){const o=Object.keys(t);for(let s=0,i=o.length;s<i;s++){const i=t[o[s]];if(i.originalHandler===e&&i.delegationSelector===n)return i}return null}function y(t,e,n){const o="string"==typeof e,s=o?n:e;let i=t.replace(d,"");const r=p[i];r&&(i=r);return g.indexOf(i)>-1||(i=t),[o,s,i]}function v(t,e,n,o,s){if("string"!=typeof e||!t)return;n||(n=o,o=null);const[i,r,l]=y(e,n,o),a=_(t),d=a[l]||(a[l]={}),u=b(d,r,i?n:null);if(u)return void(u.oneOff=u.oneOff&&s);const h=m(r,e.replace(c,"")),f=i?function(t,e,n){return function o(s){const i=t.querySelectorAll(e);for(let{target:e}=s;e&&e!==this;e=e.parentNode)for(let r=i.length;r--;"")if(i[r]===e)return s.delegateTarget=e,o.oneOff&&O.off(t,s.type,n),n.apply(e,[s]);return null}}(t,n,o):function(t,e){return function n(o){return o.delegateTarget=t,n.oneOff&&O.off(t,o.type,e),e.apply(t,[o])}}(t,n);f.delegationSelector=i?n:null,f.originalHandler=r,f.oneOff=s,f.uidEvent=h,d[h]=f,t.addEventListener(l,f,i)}function E(t,e,n,o,s){const i=b(e[n],o,s);i&&(t.removeEventListener(n,i,Boolean(s)),delete e[n][i.uidEvent])}const O={on(t,e,n,o){v(t,e,n,o,!1)},one(t,e,n,o){v(t,e,n,o,!0)},off(t,e,n,o){if("string"!=typeof e||!t)return;const[s,i,r]=y(e,n,o),l=r!==e,a=_(t),c="."===e.charAt(0);if(void 0!==i){if(!a||!a[r])return;return void E(t,a,r,i,s?n:null)}c&&Object.keys(a).forEach((n=>{!function(t,e,n,o){const s=e[n]||{};Object.keys(s).forEach((i=>{if(i.indexOf(o)>-1){const o=s[i];E(t,e,n,o.originalHandler,o.delegationSelector)}}))}(t,a,n,e.slice(1))}));const d=a[r]||{};Object.keys(d).forEach((n=>{const o=n.replace(u,"");if(!l||e.indexOf(o)>-1){const e=d[n];E(t,a,r,e.originalHandler,e.delegationSelector)}}))},trigger(t,e,n){if("string"!=typeof e||!t)return null;const o=e.replace(d,""),s=e!==o,i=g.indexOf(o)>-1;let r,l=!0,c=!0,u=!1,h=null;return s&&a&&(r=a.Event(e,n),a(t).trigger(r),l=!r.isPropagationStopped(),c=!r.isImmediatePropagationStopped(),u=r.isDefaultPrevented()),i?(h=document.createEvent("HTMLEvents"),h.initEvent(o,l,!0)):h=new CustomEvent(e,{bubbles:l,cancelable:!0}),void 0!==n&&Object.keys(n).forEach((t=>{Object.defineProperty(h,t,{get:()=>n[t]})})),u&&h.preventDefault(),c&&t.dispatchEvent(h),h.defaultPrevented&&void 0!==r&&r.preventDefault(),h}},w=".fixed-top, .fixed-bottom, .is-fixed, .sticky-top",A=".sticky-top",S="padding-right",k="margin-right";class L{constructor(){this._element=document.body}getWidth(){const t=document.documentElement.clientWidth;return Math.abs(window.innerWidth-t)}hide(){const t=this.getWidth();this._disableOverFlow(),this._setElementAttributes(this._element,S,(e=>e+t)),this._setElementAttributes(w,S,(e=>e+t)),this._setElementAttributes(A,k,(e=>e-t))}reset(){this._resetElementAttributes(this._element,"overflow"),this._resetElementAttributes(this._element,S),this._resetElementAttributes(w,S),this._resetElementAttributes(A,k)}isOverflowing(){return this.getWidth()>0}_disableOverFlow(){this._saveInitialAttribute(this._element,"overflow"),this._element.style.overflow="hidden"}_setElementAttributes(t,e,n){const o=this.getWidth();this._applyManipulationCallback(t,(t=>{if(t!==this._element&&window.innerWidth>t.clientWidth+o)return;this._saveInitialAttribute(t,e);const s=window.getComputedStyle(t).getPropertyValue(e);t.style.setProperty(e,`${n(Number.parseFloat(s))}px`)}))}_saveInitialAttribute(t,e){const n=t.style.getPropertyValue(e);n&&r.setDataAttribute(t,e,n)}_resetElementAttributes(t,e){this._applyManipulationCallback(t,(t=>{const n=r.getDataAttribute(t,e);null!==n?(r.removeDataAttribute(t,e),t.style.setProperty(e,n)):t.style.removeProperty(e)}))}_applyManipulationCallback(e,n){if(t(e))n(e);else for(const t of l.find(e,this._element))n(t)}}const P="scrollStatus",D=`mdb.${P}`,M="scroll",C="hidden.bs.modal",j={color:"#1266F1",offset:0,height:"10px",global:!1,scroll:0,target:"",openOnce:!0},x={color:"string",offset:"number",height:"string",global:"boolean",scroll:"number",target:"string",openOnce:"boolean"};class T{constructor(t,e={}){this._element=t,this._options=this._getConfig(e),this._parent=null,this._progressBar=l.findOne(".scroll-status-progress",t),this._isAlreadyOpenedOnce=!1,this._isModalLocked=!1,this._scrollPercentagePosition=0,this._scrollbar=new L,this._element&&(o.setData(t,D,this),this._init())}dispose(){o.removeData(this._element,D),O.off(this._parent,M),O.off(this._parent,C,(()=>{this._scrollbar.reset()})),this._parent=null,this._progressBar=null,this._options=null,this._isAlreadyOpenedOnce=null,this._isModalLocked=null,this._scrollPercentagePosition=0,this._element=null,this._scrollbar=null}_init(){this._setScrollTarget(),this._setStyles(),this._bindScrollProgress(),this._bindModalListener()}_getConfig(e){const n=r.getDataAttributes(this._element),o={...j,...n,...e};return((e,n,o)=>{Object.keys(o).forEach((s=>{const i=o[s],r=n[s],l=r&&t(r)?"element":null==(a=r)?`${a}`:{}.toString.call(a).match(/\s([a-z]+)/i)[1].toLowerCase();var a;if(!new RegExp(i).test(l))throw new Error(`${e.toUpperCase()}: Option "${s}" provided type "${l}" but expected type "${i}".`)}))})(P,o,x),o}_bindModalListener(){this._options.target&&(this._options.global?this._scrollbar._element=document.body:this._scrollbar._element=this._parent,O.on(this._parent,C,(()=>{this._scrollbar.reset()})))}_bindScrollProgress(){O.on(this._parent,M,(()=>{if(this._calculateScroll(),r.addStyle(this._progressBar,{width:`${this._scrollPercentagePosition}%`}),!this._options.target||this._isAlreadyOpenedOnce)return;(!this._isModalLocked&&this._scrollPercentagePosition>=this._options.scroll||this._isModalLocked&&this._scrollPercentagePosition<=this._options.scroll)&&(this._isModalLocked=!this._isModalLocked,this._showModal(),this._scrollbar.hide())}))}_calculateScroll(){let t,e;this._options.global?(t=window.pageYOffset||document.documentElement.scrollTop||document.body.scrollTop||0,e=document.documentElement.scrollHeight-window.innerHeight||document.scrollingElement.clientHeight):(t=this._parent.scrollTop,e=this._parent.scrollHeight-this._parent.clientHeight),this._scrollPercentagePosition=t/e*100}_showModal(){this._options.openOnce&&(this._isAlreadyOpenedOnce=!0);const t=l.findOne(this._options.target);if(t){new mdb.Modal(t).show()}}_setScrollTarget(){this._options.global?this._parent=window:this._parent=this._element.parentNode}_setStyles(){r.addStyle(this._progressBar,{background:this._options.color}),r.addStyle(this._element,{top:`${this._options.offset}%`}),r.addStyle(this._progressBar,{height:this._options.height})}static get NAME(){return P}static jQueryInterface(t,e){return this.each((function(){let n=o.getData(this,D);const s="object"==typeof t&&t;if((n||!/dispose/.test(t))&&(n||(n=new T(this,s)),"string"==typeof t)){if(void 0===n[t])throw new TypeError(`No method named "${t}"`);n[t](e)}}))}static getInstance(t){return o.getData(t,D)}}var $;return l.find("[data-mdb-scroll-status-init]").forEach((t=>{let e=T.getInstance(t);return e||(e=new T(t)),e})),$=()=>{const t=e();if(t){const e=t.fn[P];t.fn[P]=T.jQueryInterface,t.fn[P].Constructor=T,t.fn[P].noConflict=()=>(t.fn[P]=e,T.jQueryInterface)}},"loading"===document.readyState?document.addEventListener("DOMContentLoaded",$):$(),T}));
//# sourceMappingURL=scroll-status.min.js.map
