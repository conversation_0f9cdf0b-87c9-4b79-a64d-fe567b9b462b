!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define(t):(e="undefined"!=typeof globalThis?globalThis:e||self).Transfer=t()}(this,(function(){"use strict";const e=e=>{do{e+=Math.floor(1e6*Math.random())}while(document.getElementById(e));return e},t=()=>{const{jQuery:e}=window;return e&&!document.body.hasAttribute("data-mdb-no-jquery")?e:null};document.documentElement.dir;const r=e=>document.createElement(e),a=(()=>{const e={};let t=1;return{set(r,a,n){void 0===r[a]&&(r[a]={key:a,id:t},t++),e[r[a].id]=n},get(t,r){if(!t||void 0===t[r])return null;const a=t[r];return a.key===r?e[a.id]:null},delete(t,r){if(void 0===t[r])return;const a=t[r];a.key===r&&(delete e[a.id],delete t[r])}}})(),n={setData(e,t,r){a.set(e,t,r)},getData:(e,t)=>a.get(e,t),removeData(e,t){a.delete(e,t)}};function s(e){return"true"===e||"false"!==e&&(e===Number(e).toString()?Number(e):""===e||"null"===e?null:e)}function o(e){return e.replace(/[A-Z]/g,(e=>`-${e.toLowerCase()}`))}const i={setDataAttribute(e,t,r){e.setAttribute(`data-mdb-${o(t)}`,r)},removeDataAttribute(e,t){e.removeAttribute(`data-mdb-${o(t)}`)},getDataAttributes(e){if(!e)return{};const t={...e.dataset};return Object.keys(t).filter((e=>e.startsWith("mdb"))).forEach((e=>{let r=e.replace(/^mdb/,"");r=r.charAt(0).toLowerCase()+r.slice(1,r.length),t[r]=s(t[e])})),t},getDataAttribute:(e,t)=>s(e.getAttribute(`data-mdb-${o(t)}`)),offset(e){const t=e.getBoundingClientRect();return{top:t.top+document.body.scrollTop,left:t.left+document.body.scrollLeft}},position:e=>({top:e.offsetTop,left:e.offsetLeft}),style(e,t){Object.assign(e.style,t)},toggleClass(e,t){e&&(e.classList.contains(t)?e.classList.remove(t):e.classList.add(t))},addClass(e,t){e.classList.contains(t)||e.classList.add(t)},addStyle(e,t){Object.keys(t).forEach((r=>{e.style[r]=t[r]}))},removeClass(e,t){e.classList.contains(t)&&e.classList.remove(t)},hasClass:(e,t)=>e.classList.contains(t)};let c=Element.prototype.querySelectorAll,d=Element.prototype.querySelector;const l=(()=>{const e=new CustomEvent("Bootstrap",{cancelable:!0}),t=document.createElement("div");return t.addEventListener("Bootstrap",(()=>null)),e.preventDefault(),t.dispatchEvent(e),e.defaultPrevented})(),h=/:scope\b/;(()=>{const e=document.createElement("div");try{e.querySelectorAll(":scope *")}catch(t){return!1}return!0})()||(c=function(t){if(!h.test(t))return this.querySelectorAll(t);const r=Boolean(this.id);r||(this.id=e("scope"));let a=null;try{t=t.replace(h,`#${this.id}`),a=this.querySelectorAll(t)}finally{r||this.removeAttribute("id")}return a},d=function(e){if(!h.test(e))return this.querySelector(e);const t=u.call(this,e);return void 0!==t[0]?t[0]:null});const u=c,f=d,_=t(),p=/[^.]*(?=\..*)\.|.*/,g=/\..*/,m=/::\d+$/,b={};let y=1;const C={mouseenter:"mouseover",mouseleave:"mouseout"},S=["click","dblclick","mouseup","mousedown","contextmenu","mousewheel","DOMMouseScroll","mouseover","mouseout","mousemove","selectstart","selectend","keydown","keypress","keyup","orientationchange","touchstart","touchmove","touchend","touchcancel","pointerdown","pointermove","pointerup","pointerleave","pointercancel","gesturestart","gesturechange","gestureend","focus","blur","change","reset","select","submit","focusin","focusout","load","unload","beforeunload","resize","move","DOMContentLoaded","readystatechange","error","abort","scroll"];function v(e,t){return t&&`${t}::${y++}`||e.uidEvent||y++}function k(e){const t=v(e);return e.uidEvent=t,b[t]=b[t]||{},b[t]}function T(e,t,r=null){const a=Object.keys(e);for(let n=0,s=a.length;n<s;n++){const s=e[a[n]];if(s.originalHandler===t&&s.delegationSelector===r)return s}return null}function A(e,t,r){const a="string"==typeof t,n=a?r:t;let s=e.replace(g,"");const o=C[s];o&&(s=o);return S.indexOf(s)>-1||(s=e),[a,n,s]}function E(e,t,r,a,n){if("string"!=typeof t||!e)return;r||(r=a,a=null);const[s,o,i]=A(t,r,a),c=k(e),d=c[i]||(c[i]={}),l=T(d,o,s?r:null);if(l)return void(l.oneOff=l.oneOff&&n);const h=v(o,t.replace(p,"")),u=s?function(e,t,r){return function a(n){const s=e.querySelectorAll(t);for(let{target:t}=n;t&&t!==this;t=t.parentNode)for(let o=s.length;o--;"")if(s[o]===t)return a.oneOff&&O.off(e,n.type,r),r.apply(t,[n]);return null}}(e,r,a):function(e,t){return function r(a){return r.oneOff&&O.off(e,a.type,t),t.apply(e,[a])}}(e,r);u.delegationSelector=s?r:null,u.originalHandler=o,u.oneOff=n,u.uidEvent=h,d[h]=u,e.addEventListener(i,u,s)}function P(e,t,r,a,n){const s=T(t[r],a,n);s&&(e.removeEventListener(r,s,Boolean(n)),delete t[r][s.uidEvent])}const O={on(e,t,r,a){E(e,t,r,a,!1)},one(e,t,r,a){E(e,t,r,a,!0)},off(e,t,r,a){if("string"!=typeof t||!e)return;const[n,s,o]=A(t,r,a),i=o!==t,c=k(e),d="."===t.charAt(0);if(void 0!==s){if(!c||!c[o])return;return void P(e,c,o,s,n?r:null)}d&&Object.keys(c).forEach((r=>{!function(e,t,r,a){const n=t[r]||{};Object.keys(n).forEach((s=>{if(s.indexOf(a)>-1){const a=n[s];P(e,t,r,a.originalHandler,a.delegationSelector)}}))}(e,c,r,t.slice(1))}));const l=c[o]||{};Object.keys(l).forEach((r=>{const a=r.replace(m,"");if(!i||t.indexOf(a)>-1){const t=l[r];P(e,c,o,t.originalHandler,t.delegationSelector)}}))},trigger(e,t,r){if("string"!=typeof t||!e)return null;const a=t.replace(g,""),n=t!==a,s=S.indexOf(a)>-1;let o,i=!0,c=!0,d=!1,h=null;return n&&_&&(o=_.Event(t,r),_(e).trigger(o),i=!o.isPropagationStopped(),c=!o.isImmediatePropagationStopped(),d=o.isDefaultPrevented()),s?(h=document.createEvent("HTMLEvents"),h.initEvent(a,i,!0)):h=new CustomEvent(t,{bubbles:i,cancelable:!0}),void 0!==r&&Object.keys(r).forEach((e=>{Object.defineProperty(h,e,{get:()=>r[e]})})),d&&(h.preventDefault(),l||Object.defineProperty(h,"defaultPrevented",{get:()=>!0})),c&&e.dispatchEvent(h),h.defaultPrevented&&void 0!==o&&o.preventDefault(),h}},I={closest:(e,t)=>e.closest(t),matches:(e,t)=>e.matches(t),find:(e,t=document.documentElement)=>[].concat(...u.call(t,e)),findOne:(e,t=document.documentElement)=>f.call(t,e),children:(e,t)=>[].concat(...e.children).filter((e=>e.matches(t))),parents(e,t){const r=[];let a=e.parentNode;for(;a&&a.nodeType===Node.ELEMENT_NODE&&3!==a.nodeType;)this.matches(a,t)&&r.push(a),a=a.parentNode;return r},prev(e,t){let r=e.previousElementSibling;for(;r;){if(r.matches(t))return[r];r=r.previousElementSibling}return[]},next(e,t){let r=e.nextElementSibling;for(;r;){if(this.matches(r,t))return[r];r=r.nextElementSibling}return[]}},N="transfer",D=`mdb.${N}`,x=`.${D}`,w={titleSource:"string",titleTarget:"string",dataSource:"array",dataTarget:"array",search:"boolean",pagination:"boolean",elementsPerPage:"number",oneWay:"boolean",toSourceArrow:"string",toTargetArrow:"string",selectAll:"boolean",noDataText:"string"},L={titleSource:"Source",titleTarget:"Target",dataSource:[],dataTarget:[],search:!1,pagination:!1,elementsPerPage:5,oneWay:!1,toSourceArrow:"",toTargetArrow:"",selectAll:!0,noDataText:"No Data"},$=`listChange${x}`,B=`search${x}`,j=`itemSelected${x}`;class M{constructor(e,t){this._element=e,this._options=this._getConfig(t),this._dataSource=this._getData(this._options.dataSource),this._dataTarget=this._getData(this._options.dataTarget),this._filteredDataSource=this._dataSource,this._filteredDataTarget=this._dataTarget,this._sourceCurrentPage=1,this._targetCurrentPage=1,this._element&&(n.setData(e,D,this),this._setup())}static get NAME(){return N}dispose(){n.removeData(this._element,D),this._element=null}getTarget(){return this.dataTarget}getSource(){return this.dataSource}_getConfig(e){const t={...L,...i.getDataAttributes(this._element),...e};return((e,t,r)=>{Object.keys(r).forEach((a=>{const n=r[a],s=t[a],o=s&&((i=s)[0]||i).nodeType?"element":(e=>null==e?`${e}`:{}.toString.call(e).match(/\s([a-z]+)/i)[1].toLowerCase())(s);var i;if(!new RegExp(n).test(o))throw new Error(`${e.toUpperCase()}: Option "${a}" provided type "${o}" but expected type "${n}".`)}))})(N,t,w),t}_getData(t){const r=[];return t.forEach((t=>{const a={...t,id:e("data-mdb-"),data:t.data,checked:!!t.checked,disabled:!!t.disabled,customId:t.customId||null};r.push(a)})),r}_setup(){const{initMDB:e,Ripple:t,Input:r}=mdb;this._element.appendChild(this._createSourceContainer()),this._element.appendChild(this._createArrows()),this._element.appendChild(this._createTargetContainer()),e({Ripple:t}),this._element.querySelectorAll(".form-outline").forEach((e=>{r.getOrCreateInstance(e).update()}))}_createSourceContainer(){const{titleSource:e,pagination:t}=this._options,a=r("div");a.className="transfer-source-container transfer-container";const n="source";return a.appendChild(this._createHeader(this._dataSource,e,n)),this._options.search&&a.appendChild(this._createSearchBox(n)),a.appendChild(this._createBody(this._dataSource)),t&&a.appendChild(this._createFooter(n)),a}_createArrows(){const e=r("div");return e.className="transfer-arrows-container transfer-container",e.appendChild(this._createToSourceArrow()),e.appendChild(this._createToTargetArrow()),e}_createTargetContainer(){const{titleTarget:e,pagination:t}=this._options,a=r("div");a.className="transfer-target-container transfer-container";const n="target";return a.appendChild(this._createHeader(this._dataTarget,e,n)),this._options.search&&a.appendChild(this._createSearchBox(n)),a.appendChild(this._createBody(this._dataTarget)),t&&a.appendChild(this._createFooter(n)),a}_createHeader(t,a,n){const{selectAll:s}=this._options,o=r("div");o.className="transfer-container-header";const i=r("div");i.className="transfer-header-select-all-container";const c=e("transfer-check-");return s&&i.appendChild(this._createSelectAll(n,c)),i.appendChild(this._createTitle(a,c)),o.appendChild(i),o.appendChild(this._createQuantity(t)),o}_createSelectAll(e,t){const a=r("input");return a.setAttribute("type","checkbox"),a.className="transfer-header-select-all form-check-input",a.setAttribute("data-mdb-select-all",!1),a.id=t,O.on(a,"click",(t=>{const r="target"===e?this._dataTarget:this._dataSource;this._toggleSelection(t.target,r),this._updateInfo()})),a}_toggleSelection(e,t){"true"===e.getAttribute("data-mdb-select-all")?(e.setAttribute("data-mdb-select-all","false"),e.checked=!1,this._unselectAll(e,t)):(e.setAttribute("data-mdb-select-all","true"),e.checked=!0,this._selectAll(e,t))}_selectAll(e,t){const r=I.parents(e,".transfer-container")[0];I.find(".transfer-body-item input",r).forEach((e=>{e.classList.contains("transfer-body-item-checkbox-disabled")||(e.checked="true",e.setAttribute("data-mdb-checked","true"))})),t.forEach((e=>{e.disabled||(e.checked=!0)}))}_unselectAll(e,t){const r=I.parents(e,".transfer-container")[0];I.find(".transfer-body-item > input",r).forEach((e=>{e.checked=!1,e.setAttribute("data-mdb-checked","false")})),t.forEach((e=>{e.checked=!1}))}_createTitle(e,t){const a=r("label");return a.className="form-check-label",a.textContent=e,a.setAttribute("for",t),a}_createQuantity(e){const t=r("span");t.className="transfer-header-quantity";let a=0;return e.forEach((e=>{e.checked&&a++})),t.innerHTML=`<span class="current-checked">${a}</span>/\n    <span class="transfer-header-full-quantity">${e.length}</span>`,t}_createSearchBox(e){const t=r("div");t.className="form-outline transfer-search-outline",t.setAttribute("data-mdb-input-init",""),t.style.width="auto";const a=r("input");a.setAttribute("type","search"),a.className="transfer-search form-control",a.id=`transfer-search-${e}`,this._addEventsHandlers("input",a,e);const n=r("label");return n.className="form-label",n.textContent="Search",n.setAttribute("for",`transfer-search-${e}`),t.appendChild(a),t.appendChild(n),new mdb.Input(t).init(),t}_addEventsHandlers(e,t,r){O.on(t,e,(e=>{const t=e.target.value;O.trigger(this._element,B,{searchValue:t}),this._findInList(t,r),this._createFilteredList(r)}))}_findInList(e,t){const r="source"===t,a=(r?this._dataSource:this._dataTarget).filter((t=>{const r=t.data.toLowerCase(),a=e.toLowerCase();return!!r.includes(a)&&t}));r?this._filteredDataSource=a:this._filteredDataTarget=a}_createFilteredList(e){const t="source"===e,r=t?this._filteredDataSource:this._filteredDataTarget,a=t?I.findOne(".transfer-source-container > .transfer-body",this._element):I.findOne(".transfer-target-container > .transfer-body",this._element);I.find(".transfer-body-item",a).forEach((e=>{a.removeChild(e)}));const n=t?this._sourceCurrentPage:this._targetCurrentPage;if(this._options.pagination){const e=n*this._options.elementsPerPage,t=e-this._options.elementsPerPage;r.forEach(((r,n)=>{n>=t&&n<e&&a.appendChild(this._createBodyItem(r))}))}else r.forEach((e=>{a.appendChild(this._createBodyItem(e))}))}_createBody(e){const t=r("ul");return t.className="transfer-body",e.length?(e.forEach(((e,r)=>{this._options.pagination?r<this._options.elementsPerPage&&t.appendChild(this._createBodyItem(e)):t.appendChild(this._createBodyItem(e))})),t):(t.appendChild(this._createNoData()),t.classList.add("transfer-body-no-data"),t)}_createNoData(){const e=r("div");return e.className="transfer-body-no-data",e.innerHTML=`\n    <i class="far fa-folder-open transfer-no-data-mdb-icon"></i>\n    <span>${this._options.noDataText}</span>`,e}_createBodyItem(e){const t=r("li");return t.className="transfer-body-item",t.appendChild(this._createItemCheckbox(e)),t.appendChild(this._createItemText(e)),t}_createItemCheckbox(e){const t=r("input");return t.setAttribute("type","checkbox"),t.className="transfer-body-item-checkbox form-check-input "+(e.disabled?"transfer-body-item-checkbox-disabled":""),t.id=e.id,t.checked=e.checked,t.disabled=e.disabled,t.setAttribute("data-mdb-checked",e.checked),O.on(t,"click",(()=>{this._toggleCheckbox(t,e),this._updateInfo(),O.trigger(this._element,j,{item:e})})),t}_toggleCheckbox(e,t){"true"===e.getAttribute("data-mdb-checked")?(e.setAttribute("data-mdb-checked","false"),t.checked=!1):(e.setAttribute("data-mdb-checked","true"),t.checked=!0)}_createItemText(e){const t=r("label");return t.className="transfer-body-item-text form-check-label "+(e.disabled?"transfer-body-item-text-disabled":""),t.setAttribute("for",e.id),t.textContent=e.data,t}_createFooter(e){const t=r("div");return t.className="transfer-footer",t.appendChild(this._createArrowPrev(e)),t.appendChild(this._createCurrentPageNum(e)),t.appendChild(this._createArrowNext(e)),t}_createArrowPrev(e){const t=r("button");return i.setDataAttribute(t,"rippleInit",""),t.className="btn btn-outline-primary btn-floating btn-sm transfer-footer-arrow",t.innerHTML='<i class="fas fa-angle-left"></i>',O.on(t,"click",(()=>{this._prevPageUpdate(e),this._reloadPage(e)})),t}_prevPageUpdate(e){const t="source"===e,r=t?this._sourceCurrentPage:this._targetCurrentPage,a=1===r?1:r-1;t?this._sourceCurrentPage=a:this._targetCurrentPage=a}_reloadPage(e){const t="source"===e,r=t?I.findOne(".transfer-source-container > .transfer-body",this._element):I.findOne(".transfer-target-container > .transfer-body",this._element);I.find(".transfer-body-item",r).forEach((e=>{r.removeChild(e)})),this._createPage(t,r),this._reloadNumOfPage(t)}_createPage(e,t){const r=this._options.search,a=e?I.findOne(".transfer-source-container .transfer-search",this._element):I.findOne(".transfer-target-container .transfer-search",this._element),n=r&&a.value,s=e?this._dataSource:this._dataTarget,o=e?this._filteredDataSource:this._filteredDataTarget,i=n?o:s,c=(e?this._sourceCurrentPage:this._targetCurrentPage)*this._options.elementsPerPage,d=c-this._options.elementsPerPage;i.forEach(((e,r)=>{r>=d&&r<c&&t.appendChild(this._createBodyItem(e))}))}_reloadNumOfPage(e){const t=I.findOne(".transfer-source-container .transfer-footer-current-page",this._element),r=I.findOne(".transfer-target-container .transfer-footer-current-page",this._element);(e?t:r).textContent=e?this._sourceCurrentPage:this._targetCurrentPage}_createCurrentPageNum(e){const t=r("span");return t.className="transfer-footer-current-page",t.textContent="source"===e?this._sourceCurrentPage:this._targetCurrentPage,t}_createArrowNext(e){const t=r("button");return i.setDataAttribute(t,"rippleInit",""),t.className="btn btn-outline-primary btn-floating btn-sm transfer-footer-arrow",t.innerHTML='<i class="fas fa-angle-right"></i>',O.on(t,"click",(()=>{this._nextPageUpdate(e),this._reloadPage(e)})),t}_nextPageUpdate(e){const t="source"===e,r=t?this._sourceCurrentPage:this._targetCurrentPage,a=this._options.search,n=t?I.findOne(".transfer-source-container .transfer-search",this._element):I.findOne(".transfer-target-container .transfer-search",this._element),s=a&&n.value,o=t?this._dataSource:this._dataTarget,i=t?this._filteredDataSource:this._filteredDataTarget,c=s?i.length:o.length,d=0===c?1:Math.ceil(c/this._options.elementsPerPage),l=r===d?d:r+1;t?this._sourceCurrentPage=l:this._targetCurrentPage=l}_createToSourceArrow(){const e=r("button");return i.setDataAttribute(e,"rippleInit",""),e.className="btn btn-primary transfer-arrows-arrow",e.textContent="<",O.on(e,"click",(()=>{const e=I.findOne(".transfer-target-container > .transfer-body",this._element),t=I.findOne(".transfer-source-container > .transfer-body",this._element),r=I.find('[data-mdb-checked="true"]',e);this._handleSendToSource(),this._removeItems(r,e),this._uncheckItems(r),this._addItems(r,t),this._updateInfo(),this._options.pagination&&(this._targetCurrentPage=1,this._reloadPage("source"),this._reloadPage("target")),this._options.search&&(this._targetCurrentPage=1,this._filteredDataTarget=this._dataTarget,this._createFilteredList("target"),this._clearSearchBoxes())})),this._options.oneWay&&e.setAttribute("disabled",!0),e}_handleSendToSource(){const e=[];this._dataTarget.forEach((t=>{t.checked&&e.push(t)})),O.trigger(this._element,$,{sentItems:e}),this._dataTarget=this._dataTarget.filter((e=>{const t=!e.checked;return e.checked=!1,t})),this._dataSource=[...this._dataSource,...e]}_removeItems(e,t){e.forEach((e=>{t.removeChild(e.parentNode)}))}_uncheckItems(e=[]){e.forEach((e=>{e.setAttribute("data-mdb-checked",!1),e.checked=!1}));I.find(".transfer-header-select-all",this._element).forEach((e=>{e.setAttribute("data-mdb-select-all","false"),e.checked=!1}))}_addItems(e,t){e.forEach((e=>{t.appendChild(e.parentNode)}))}_createToTargetArrow(){const e=r("button");return i.setDataAttribute(e,"rippleInit",""),e.className="btn btn-primary transfer-arrows-arrow",e.textContent=">",O.on(e,"click",(()=>{const e=I.findOne(".transfer-target-container > .transfer-body",this._element),t=I.findOne(".transfer-source-container > .transfer-body",this._element),r=I.find('[data-mdb-checked="true"]',t);this._handleSendToTarget(),this._removeItems(r,t),this._uncheckItems(r),this._addItems(r,e),this._updateInfo(),this._options.pagination&&(this._sourceCurrentPage=1,this._reloadPage("source"),this._reloadPage("target")),this._options.search&&(this._sourceCurrentPage=1,this._filteredDataSource=this._dataSource,this._createFilteredList("source"),this._clearSearchBoxes())})),e}_handleSendToTarget(){const e=[];this._dataSource.forEach((t=>{t.checked&&e.push(t)})),O.trigger(this._element,$,{sentItems:e}),this._dataSource=this._dataSource.filter((e=>{const t=!e.checked;return e.checked=!1,t})),this._dataTarget=[...this._dataTarget,...e]}_updateInfo(){this._updateNoData(),this._updateCurrentInStockNumber(),this._updateSelectedItemsNumber()}_updateNoData(){const e=!!this._dataSource.length,t=!!this._dataTarget.length,r=I.findOne(".transfer-source-container .transfer-body",this._element),a=I.findOne(".transfer-target-container .transfer-body",this._element);if(e){r.classList.remove("transfer-body-no-data");const e=I.findOne(".transfer-body-no-data",r);e&&r.removeChild(e)}else{r.classList.add("transfer-body-no-data");I.findOne(".transfer-body-no-data",r)||r.appendChild(this._createNoData())}if(t){a.classList.remove("transfer-body-no-data");const e=I.findOne(".transfer-body-no-data",a);e&&a.removeChild(e)}else{a.classList.add("transfer-body-no-data");I.findOne(".transfer-body-no-data",a)||a.appendChild(this._createNoData())}}_updateCurrentInStockNumber(){const e=I.findOne(".transfer-target-container",this._element),t=I.findOne(".transfer-source-container",this._element),r=I.findOne(".transfer-header-full-quantity",e);I.findOne(".transfer-header-full-quantity",t).textContent=this._dataSource.length,r.textContent=this._dataTarget.length}_updateSelectedItemsNumber(){this._selectedInSource(),this._selectedInTarget()}_selectedInSource(){let e=0,t=0;this._dataSource.forEach((r=>{r.checked&&e++,r.disabled&&t++}));const r=I.findOne(".transfer-source-container .current-checked",this._element),a=I.findOne(".transfer-source-container .transfer-header-select-all",this._element);r.textContent=e;const n=e===this._dataSource.length-t,s=this._dataSource.length!==t;n&&0!==this._dataSource.length&&s?(a.checked=!0,a.setAttribute("data-mdb-select-all","true")):(a.checked=!1,a.setAttribute("data-mdb-select-all","false"))}_selectedInTarget(){let e=0,t=0;this._dataTarget.forEach((r=>{r.checked&&e++,r.disabled&&t++}));const r=I.findOne(".transfer-target-container .current-checked",this._element),a=I.findOne(".transfer-target-container .transfer-header-select-all",this._element);r.textContent=e;const n=e===this._dataTarget.length-t,s=this._dataTarget.length!==t;n&&0!==this._dataTarget.length&&s?(a.checked=!0,a.setAttribute("data-mdb-select-all","true")):(a.checked=!1,a.setAttribute("data-mdb-select-all","false"))}_clearSearchBoxes(){I.find(".transfer-search",this._element).forEach((e=>{e.value=""}))}static getInstance(e){return n.getData(e,D)}static jQueryInterface(e){return this.each((function(){let t=n.getData(this,D);if(t||(t=new M(this,"object"==typeof e&&e)),"string"==typeof e){if(void 0===t[e])throw new TypeError(`No method named "${e}"`);t[e](this)}}))}}var H;return H=()=>{const e=t();if(e){const t=e.fn[N];e.fn[N]=M.jQueryInterface,e.fn[N].Constructor=M,e.fn[N].noConflict=()=>(e.fn[N]=t,M.jQueryInterface)}},"loading"===document.readyState?document.addEventListener("DOMContentLoaded",H):H(),M}));
//# sourceMappingURL=transfer.min.js.map
