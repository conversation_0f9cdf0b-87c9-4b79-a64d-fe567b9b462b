!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e():"function"==typeof define&&define.amd?define(e):(t="undefined"!=typeof globalThis?globalThis:t||self).WYSIWYG=e()}(this,(function(){"use strict";const t=()=>{const{jQuery:t}=window;return t&&!document.body.hasAttribute("data-mdb-no-jquery")?t:null};document.documentElement.dir;const e=(()=>{const t={};let e=1;return{set(n,o,i){void 0===n[o]&&(n[o]={key:o,id:e},e++),t[n[o].id]=i},get(e,n){if(!e||void 0===e[n])return null;const o=e[n];return o.key===n?t[o.id]:null},delete(e,n){if(void 0===e[n])return;const o=e[n];o.key===n&&(delete t[o.id],delete e[n])}}})(),n={setData(t,n,o){e.set(t,n,o)},getData:(t,n)=>e.get(t,n),removeData(t,n){e.delete(t,n)}};function o(t){return"true"===t||"false"!==t&&(t===Number(t).toString()?Number(t):""===t||"null"===t?null:t)}function i(t){return t.replace(/[A-Z]/g,(t=>`-${t.toLowerCase()}`))}const a={setDataAttribute(t,e,n){t.setAttribute(`data-mdb-${i(e)}`,n)},removeDataAttribute(t,e){t.removeAttribute(`data-mdb-${i(e)}`)},getDataAttributes(t){if(!t)return{};const e={...t.dataset};return Object.keys(e).filter((t=>t.startsWith("mdb"))).forEach((t=>{let n=t.replace(/^mdb/,"");n=n.charAt(0).toLowerCase()+n.slice(1,n.length),e[n]=o(e[t])})),e},getDataAttribute:(t,e)=>o(t.getAttribute(`data-mdb-${i(e)}`)),offset(t){const e=t.getBoundingClientRect();return{top:e.top+document.body.scrollTop,left:e.left+document.body.scrollLeft}},position:t=>({top:t.offsetTop,left:t.offsetLeft}),style(t,e){Object.assign(t.style,e)},toggleClass(t,e){t&&(t.classList.contains(e)?t.classList.remove(e):t.classList.add(e))},addClass(t,e){t.classList.contains(e)||t.classList.add(e)},addStyle(t,e){Object.keys(e).forEach((n=>{t.style[n]=e[n]}))},removeClass(t,e){t.classList.contains(e)&&t.classList.remove(e)},hasClass:(t,e)=>t.classList.contains(e)};let s=Element.prototype.querySelectorAll,d=Element.prototype.querySelector;const r=(()=>{const t=new CustomEvent("Bootstrap",{cancelable:!0}),e=document.createElement("div");return e.addEventListener("Bootstrap",(()=>null)),t.preventDefault(),e.dispatchEvent(t),t.defaultPrevented})(),l=/:scope\b/;(()=>{const t=document.createElement("div");try{t.querySelectorAll(":scope *")}catch(e){return!1}return!0})()||(s=function(t){if(!l.test(t))return this.querySelectorAll(t);const e=Boolean(this.id);e||(this.id=(t=>{do{t+=Math.floor(1e6*Math.random())}while(document.getElementById(t));return t})("scope"));let n=null;try{t=t.replace(l,`#${this.id}`),n=this.querySelectorAll(t)}finally{e||this.removeAttribute("id")}return n},d=function(t){if(!l.test(t))return this.querySelector(t);const e=c.call(this,t);return void 0!==e[0]?e[0]:null});const c=s,b=d,u=t(),m=/[^.]*(?=\..*)\.|.*/,p=/\..*/,f=/::\d+$/,g={};let h=1;const y={mouseenter:"mouseover",mouseleave:"mouseout"},w=["click","dblclick","mouseup","mousedown","contextmenu","mousewheel","DOMMouseScroll","mouseover","mouseout","mousemove","selectstart","selectend","keydown","keypress","keyup","orientationchange","touchstart","touchmove","touchend","touchcancel","pointerdown","pointermove","pointerup","pointerleave","pointercancel","gesturestart","gesturechange","gestureend","focus","blur","change","reset","select","submit","focusin","focusout","load","unload","beforeunload","resize","move","DOMContentLoaded","readystatechange","error","abort","scroll"];function v(t,e){return e&&`${e}::${h++}`||t.uidEvent||h++}function _(t){const e=v(t);return t.uidEvent=e,g[e]=g[e]||{},g[e]}function $(t,e,n=null){const o=Object.keys(t);for(let i=0,a=o.length;i<a;i++){const a=t[o[i]];if(a.originalHandler===e&&a.delegationSelector===n)return a}return null}function x(t,e,n){const o="string"==typeof e,i=o?n:e;let a=t.replace(p,"");const s=y[a];s&&(a=s);return w.indexOf(a)>-1||(a=t),[o,i,a]}function S(t,e,n,o,i){if("string"!=typeof e||!t)return;n||(n=o,o=null);const[a,s,d]=x(e,n,o),r=_(t),l=r[d]||(r[d]={}),c=$(l,s,a?n:null);if(c)return void(c.oneOff=c.oneOff&&i);const b=v(s,e.replace(m,"")),u=a?function(t,e,n){return function o(i){const a=t.querySelectorAll(e);for(let{target:e}=i;e&&e!==this;e=e.parentNode)for(let s=a.length;s--;"")if(a[s]===e)return o.oneOff&&L.off(t,i.type,n),n.apply(e,[i]);return null}}(t,n,o):function(t,e){return function n(o){return n.oneOff&&L.off(t,o.type,e),e.apply(t,[o])}}(t,n);u.delegationSelector=a?n:null,u.originalHandler=s,u.oneOff=i,u.uidEvent=b,l[b]=u,t.addEventListener(d,u,a)}function T(t,e,n,o,i){const a=$(e[n],o,i);a&&(t.removeEventListener(n,a,Boolean(i)),delete e[n][a.uidEvent])}const L={on(t,e,n,o){S(t,e,n,o,!1)},one(t,e,n,o){S(t,e,n,o,!0)},off(t,e,n,o){if("string"!=typeof e||!t)return;const[i,a,s]=x(e,n,o),d=s!==e,r=_(t),l="."===e.charAt(0);if(void 0!==a){if(!r||!r[s])return;return void T(t,r,s,a,i?n:null)}l&&Object.keys(r).forEach((n=>{!function(t,e,n,o){const i=e[n]||{};Object.keys(i).forEach((a=>{if(a.indexOf(o)>-1){const o=i[a];T(t,e,n,o.originalHandler,o.delegationSelector)}}))}(t,r,n,e.slice(1))}));const c=r[s]||{};Object.keys(c).forEach((n=>{const o=n.replace(f,"");if(!d||e.indexOf(o)>-1){const e=c[n];T(t,r,s,e.originalHandler,e.delegationSelector)}}))},trigger(t,e,n){if("string"!=typeof e||!t)return null;const o=e.replace(p,""),i=e!==o,a=w.indexOf(o)>-1;let s,d=!0,l=!0,c=!1,b=null;return i&&u&&(s=u.Event(e,n),u(t).trigger(s),d=!s.isPropagationStopped(),l=!s.isImmediatePropagationStopped(),c=s.isDefaultPrevented()),a?(b=document.createEvent("HTMLEvents"),b.initEvent(o,d,!0)):b=new CustomEvent(e,{bubbles:d,cancelable:!0}),void 0!==n&&Object.keys(n).forEach((t=>{Object.defineProperty(b,t,{get:()=>n[t]})})),c&&(b.preventDefault(),r||Object.defineProperty(b,"defaultPrevented",{get:()=>!0})),l&&t.dispatchEvent(b),b.defaultPrevented&&void 0!==s&&s.preventDefault(),b}},E={closest:(t,e)=>t.closest(e),matches:(t,e)=>t.matches(e),find:(t,e=document.documentElement)=>[].concat(...c.call(e,t)),findOne:(t,e=document.documentElement)=>b.call(e,t),children:(t,e)=>[].concat(...t.children).filter((t=>t.matches(e))),parents(t,e){const n=[];let o=t.parentNode;for(;o&&o.nodeType===Node.ELEMENT_NODE&&3!==o.nodeType;)this.matches(o,e)&&n.push(o),o=o.parentNode;return n},prev(t,e){let n=t.previousElementSibling;for(;n;){if(n.matches(e))return[n];n=n.previousElementSibling}return[]},next(t,e){let n=t.nextElementSibling;for(;n;){if(this.matches(n,e))return[n];n=n.nextElementSibling}return[]}},k={textarea:(t,e)=>`<textarea class="wysiwyg-textarea" name=${t||`wysiwyg-textarea-${e}`}></textarea>`,contentDiv:()=>'<div class="wysiwyg-content" contenteditable="true"></div>',toolBar:t=>{const{wysiwygTranslations:e,wysiwygColors:n,wysiwygStylesSection:o,wysiwygFormattingSection:i,wysiwygJustifySection:a,wysiwygListsSection:s,wysiwygLinksSection:d,wysiwygShowCodeSection:r,wysiwygUndoRedoSection:l,wysiwygFixed:c,wysiwygFixedOffsetTop:b}=t,u=e;return`<div class="wysiwyg-toolbar btn-toolbar ${c?"sticky-top":""}" style="top: ${b}px" role="toolbar" aria-label="Toolbar with button groups">\n      ${o?k.stylesSection(u):""}\n      ${i?k.formattingSection(u,n):""}\n      ${a?k.justifySection(u):""}\n      ${s?k.listsSection(u):""}\n      ${d?k.linksSection(u):""}\n      ${r?k.showCodeSection(u):""}\n      ${l?k.undoRedoSection(u):""}\n    </div>`},stylesSection:t=>{const{textStyle:e,paragraph:n,heading:o,preformatted:i}=t;return`\n      <div class="wysiwyg-toolbar-group">\n        <div class="mx-1 dropdown">\n          <button\n            class="btn btn-sm wysiwyg-btn dropdown-toggle shadow-0"\n            type="button"\n            data-mdb-dropdown-init\n            data-mdb-ripple-init\n            aria-expanded="false"\n          >\n            ${e}\n          </button>\n          <ul class="wysiwyg-toolbar-options-list dropdown-menu" aria-labelledby="dropdownMenuButton">\n            <li><a class="dropdown-item" href="#!" data-mdb-cmd="formatBlock" data-mdb-arg="p">${n}</a></li>\n            <li><a class="dropdown-item" href="#!" data-mdb-cmd="formatBlock" data-mdb-arg="h1">${o} 1</a></li>\n            <li><a class="dropdown-item" href="#!" data-mdb-cmd="formatBlock" data-mdb-arg="h2">${o} 2</a></li>\n            <li><a class="dropdown-item" href="#!" data-mdb-cmd="formatBlock" data-mdb-arg="h3">${o} 3</a></li>\n            <li><a class="dropdown-item" href="#!" data-mdb-cmd="formatBlock" data-mdb-arg="h4">${o} 4</a></li>\n            <li><a class="dropdown-item" href="#!" data-mdb-cmd="formatBlock" data-mdb-arg="h5">${o} 5</a></li>\n            <li><a class="dropdown-item" href="#!" data-mdb-cmd="formatBlock" data-mdb-arg="h6">${o} 6</a></li>\n            <li><a class="dropdown-item" href="#!" data-mdb-cmd="formatBlock" data-mdb-arg="pre">${i}</a></li>\n          </ul>\n        </div>\n      </div>\n    `},formattingSection:(t,e)=>{const{bold:n,italic:o,underline:i,strikethrough:a,textcolor:s,textBackgroundColor:d}=t;return`\n      <div class="wysiwyg-toolbar-group">\n        <div class="mx-1 btn-group btn-group-sm shadow-0" role="group">\n          <button data-mdb-tooltip-init data-mdb-ripple-init data-mdb-placement="bottom" type="button" class="btn wysiwyg-btn" data-mdb-cmd="bold" title="${n}" data-mdb-ripple-init><i class="fas fa-bold"></i></button>\n          <button data-mdb-tooltip-init data-mdb-ripple-init data-mdb-placement="bottom" type="button" class="btn wysiwyg-btn" data-mdb-cmd="italic" title="${o}" data-mdb-ripple-init><i class="fas fa-italic"></i></button>\n          <button data-mdb-tooltip-init data-mdb-ripple-init data-mdb-placement="bottom" type="button" class="btn wysiwyg-btn" data-mdb-cmd="underline" title="${i}" data-mdb-ripple-init><i class="fas fa-underline"></i></button>\n          <button data-mdb-tooltip-init data-mdb-ripple-init data-mdb-placement="bottom" type="button" class="btn wysiwyg-btn" data-mdb-cmd="strikethrough" title="${a}" data-mdb-ripple-init><i class="fas fa-strikethrough"></i></button>\n          <div data-mdb-tooltip-init  data-mdb-placement="bottom" title="${s}" class="btn-group btn-group-sm" role="group">\n            <button class="dropdown-toggle btn wysiwyg-btn" data-mdb-dropdown-init data-mdb-ripple-init aria-expanded="false"><i class="fas fa-font"></i></button>\n            <div class="dropdown-menu">\n              ${k.textColorPalette(e,"foreColor")}\n            </div>\n          </div>\n          <div data-mdb-tooltip-init data-mdb-placement="bottom" title="${d}" class="btn-group btn-group-sm" role="group">\n            <button class="dropdown-toggle btn wysiwyg-btn" data-mdb-dropdown-init data-mdb-ripple-init aria-expanded="false"><i class="fas fa-paint-brush"></i></button>\n            <div class="dropdown-menu">\n              ${k.textColorPalette(e,"backColor")}\n            </div>\n          </div>\n        </div>\n      </div>\n    `},textColorPalette:(t,e)=>{let n="";return t.forEach((t=>{n+=`<button type="button" class="btn btn-link wysiwyg-color"data-mdb-ripple-init  data-mdb-ripple-color="dark" data-mdb-cmd=${e} data-mdb-arg="${t}" style="background: ${t};"></button>`})),n},justifySection:t=>{const{alignCenter:e,alignLeft:n,alignRight:o,alignJustify:i}=t;return`\n      <div class="wysiwyg-toolbar-group">\n        <div class="mx-1 btn-group btn-group-sm shadow-0" role="group">\n          <button data-mdb-tooltip-init data-mdb-ripple-init data-mdb-placement="bottom" type="button" class="btn  wysiwyg-btn" data-mdb-cmd="justifyleft" title="${n}"><i class="fas fa-align-left"></i></button>\n          <button data-mdb-tooltip-init data-mdb-ripple-init data-mdb-placement="bottom" type="button" class="btn  wysiwyg-btn" data-mdb-cmd="justifycenter" title="${e}"><i class="fas fa-align-center"></i></button>\n          <button data-mdb-tooltip-init data-mdb-ripple-init data-mdb-placement="bottom" type="button" class="btn wysiwyg-btn" data-mdb-cmd="justifyright" title="${o}"><i class="fas fa-align-right"></i></button>\n          <button data-mdb-tooltip-init data-mdb-ripple-init data-mdb-placement="bottom" type="button" class="btn wysiwyg-btn" data-mdb-cmd="justifyfull" title="${i}"><i class="fas fa-align-justify"></i></button>\n        </div>\n      </div>\n    `},listsSection:t=>{const{unorderedList:e,orderedList:n,decreaseIndent:o,increaseIndent:i}=t;return`\n      <div class="wysiwyg-toolbar-group">\n        <div class="mx-1 btn-group btn-group-sm shadow-0" role="group">\n          <button data-mdb-tooltip-init data-mdb-ripple-init data-mdb-placement="bottom" type="button" class="btn  wysiwyg-btn" data-mdb-cmd="insertUnorderedList" title="${e}"><i class="fas fa-list-ul"></i></button>\n          <button data-mdb-tooltip-init data-mdb-ripple-init data-mdb-placement="bottom" type="button" class="btn wysiwyg-btn" data-mdb-cmd="insertOrderedList" title="${n}"><i class="fas fa-list-ol"></i></button>\n          <button data-mdb-tooltip-init data-mdb-ripple-init data-mdb-placement="bottom" type="button" class="btn wysiwyg-btn" data-mdb-cmd="outdent" title="${o}"><i class="fas fa-outdent"></i></button>\n          <button data-mdb-tooltip-init data-mdb-ripple-init data-mdb-placement="bottom" type="button" class="btn wysiwyg-btn" data-mdb-cmd="indent" title="${i}"><i class="fas fa-indent"></i></button>\n        </div>\n      </div>\n    `},linksSection:t=>{const{insertLink:e,addLinkHead:n,linkUrlLabel:o,linkDescription:i,okButton:a,cancelButton:s,insertPicture:d,addImageHead:r,imageUrlLabel:l,insertHorizontalRule:c}=t;return`\n      <div class=" wysiwyg-toolbar-group">\n        <div id="links-section" class="mx-1 btn-group btn-group-sm shadow-0 dropdown" role="group">\n          <div data-mdb-tooltip-init data-mdb-placement="bottom" title="${e}" class="btn-group btn-group-sm" role="group">\n            <button class="dropdown-toggle btn wysiwyg-btn" data-mdb-display="static" data-mdb-dropdown-init data-mdb-ripple-init aria-expanded="false" data-mdb-dropdown-animation="off"><i class="fas fa-paperclip"></i></button>\n            <div class="dropdown-menu dropdown-menu-sm-end px-4 py-3" style="min-width: 25vw;">\n              <form>\n                <h5 class="mb-3">${n}</h5>\n                <div class="form-outline mb-4" data-mdb-input-init>\n                  <input type="url" class="form-control" />\n                  <label class="form-label" for="link-url">${o}</label>\n                </div>\n                <div class="form-outline mb-4" data-mdb-input-init>\n                  <input type="text" class="form-control" />\n                  <label class="form-label" for="link-description">${i}</label>\n                </div>\n                <div class="d-flex justify-content-end">\n                  <button type="button" data-mdb-cmd="insertlink" class="btn btn-primary" data-mdb-ripple-init>${a}</button>\n                  <button type="button" data-mdb-cmd="close-dropdown" class="btn btn-primary ms-2" data-mdb-ripple-init>${s}</button>\n                </div>\n              </form>\n            </div>\n          </div>\n          <div data-mdb-tooltip-init data-mdb-placement="bottom" title="${d}" class="btn-group btn-group-sm" role="group">\n            <button class="dropdown-toggle btn wysiwyg-btn" data-mdb-display="static" data-mdb-dropdown-init data-mdb-ripple-init data-mdb-dropdown-animation="off"><i class="far fa-image"></i></button>\n            <div class="dropdown-menu dropdown-menu-sm-end px-4 py-3" style="min-width: 25vw;">\n              <form>\n                <h5 class="mb-3">${r}</h5>\n                <div class="form-outline mb-4" data-mdb-input-init>\n                  <input type="url" class="form-control" />\n                  <label class="form-label" for="image-url">${l}</label>\n                </div>\n                <div class="d-flex justify-content-end">\n                  <button type="button" data-mdb-cmd="insertpicture" class="btn btn-primary" data-mdb-ripple-init>${a}</button>\n                  <button type="button" data-mdb-cmd="close-dropdown" class="btn btn-primary ms-2" data-mdb-ripple-init>${s}</button>\n                </div>\n              </form>\n            </div>\n          </div>\n          <button type="button" data-mdb-tooltip-init data-mdb-ripple-init data-placement="bottom" class="btn wysiwyg-btn" data-mdb-cmd="insertHorizontalRule" title="${c}"><i class="fas fa-grip-lines"></i></button>\n        </div>\n      </div>\n    `},showCodeSection:t=>`\n      <div class="ms-auto wysiwyg-toolbar-group">\n        <div class="btn-group mx-1 btn-group-sm shadow-0" role="group">\n          <button data-mdb-tooltip-init data-mdb-ripple-init data-mdb-placement="bottom" type="button" class="btn btn-sm wysiwyg-btn shadow-0" data-mdb-cmd="toggleHTML" title="${t.showHTML}"><i class="fas fa-code"></i></button>\n        </div>\n      </div>\n    `,undoRedoSection:t=>{const{undo:e,redo:n}=t;return`\n    <div class="wysiwyg-toolbar-group">\n      <div class="btn-group mx-1 btn-group-sm shadow-0" role="group">\n        <button data-mdb-tooltip-init data-mdb-ripple-init data-mdb-placement="bottom" type="button" class="btn wysiwyg-btn" data-mdb-cmd="undo" title="${e}"><i class="fas fa-angle-left"></i></button>\n        <button data-mdb-tooltip-init data-mdb-ripple-init data-mdb-placement="bottom" type="button" class="btn wysiwyg-btn" data-mdb-cmd="redo" title="${n}"><i class="fas fa-angle-right"></i></button>\n      </div>\n    </div>\n  `},toolbarToggler:t=>`\n    <div class="ms-auto wysiwyg-toolbar-group wysiwyg-toolbar-toggler">\n      <div data-mdb-tooltip-init data-mdb-placement="bottom" title="${t.moreOptions}" class="mx-1 dropdown">\n        <button class="dropdown-toggle btn btn-sm wysiwyg-btn shadow-0" data-mdb-display="static" data-mdb-dropdown-init data-mdb-ripple-init data-mdb-dropdown-animation="off"><i class="fas fa-ellipsis-h"></i></button>\n        <div class="dropdown-menu dropdown-menu-end">\n\n        </div>\n      </div>\n    </div>\n  `},C="wysiwyg",D=`mdb.${C}`,O=`.${D}`,A=`${C}-toolbar-toggler`,B=`${C}-hide`,I=`${C}-show-html`,H="active",M=`resize${O}`,j=`click${O}`,U=`mousedown${O}`,N="hide.bs.dropdown",R="input",F="[data-mdb-cmd]",P=".form-outline",W=".dropdown",z=".dropdown-menu",q='[type="url"]',J='[type="text"]',Q="#links-section",Y="[data-mdb-dropdown-init]",G=`.${C}`,K=`.${`${C}-content`}`,Z=`.${`${C}-toolbar`}`,V=`.${A}`,X=`.${`${C}-toolbar-group`}`,tt=`${V} ${W}`,et=`${V} .dropdown-toggle`,nt=`${V} ${z}`,ot=`${X} ${F}`,it=`${P} input`,at=`${X}${`.${B}`}`,st={wysiwygColors:["#1266F1","#B23CFD","#00B74A","#F93154","#FFA900","#39C0ED","#FBFBFB","#262626"],wysiwygTranslations:{paragraph:"Paragraph",textStyle:"Text style",heading:"Heading",preformatted:"Preformatted",bold:"Bold",italic:"Italic",strikethrough:"Strikethrough",underline:"Underline",textcolor:"Color",textBackgroundColor:"Background Color",alignLeft:"Align Left",alignCenter:"Align Center",alignRight:"Align Right",alignJustify:"Align Justify",insertLink:"Insert Link",insertPicture:"Insert Picture",unorderedList:"Unordered List",orderedList:"Numbered List",increaseIndent:"Increase Indent",decreaseIndent:"Decrease Indent",insertHorizontalRule:"Insert Horizontal Line",showHTML:"Show HTML code",undo:"Undo",redo:"Redo",addLinkHead:"Add Link",addImageHead:"Add Image",linkUrlLabel:"Enter a URL:",linkDescription:"Enter a description",imageUrlLabel:"Enter a URL:",okButton:"OK",cancelButton:"cancel",moreOptions:"Show More Options"},wysiwygStylesSection:!0,wysiwygFormattingSection:!0,wysiwygJustifySection:!0,wysiwygListsSection:!0,wysiwygLinksSection:!0,wysiwygShowCodeSection:!0,wysiwygUndoRedoSection:!0,wysiwygFixed:!1,wysiwygFixedOffsetTop:0,wysiwygTextareaName:""},dt={wysiwygColors:"array",wysiwygTranslations:"object",wysiwygStylesSection:"boolean",wysiwygFormattingSection:"boolean",wysiwygJustifySection:"boolean",wysiwygListsSection:"boolean",wysiwygLinksSection:"boolean",wysiwygShowCodeSection:"boolean",wysiwygUndoRedoSection:"boolean",wysiwygFixed:"boolean",wysiwygFixedOffsetTop:"number",wysiwygTextareaName:"string"};class rt{constructor(t,e={}){this._element=t,this._content="",this._toolbar="",this._toolbarToggler="",this._textarea="",this._options=this._getConfig(e),this._isCodeShown=!1,this._toolsWidth=[],this._selection={},this._UUID=this._randomUUID(),this._element&&(this._init(),n.setData(t,D,this))}dispose(){n.removeData(this._element,D),L.off(window,M,this._windowResizeHandler),L.off(this._content,R),E.find(F,this._element).forEach((t=>{L.off(t,j)})),this._toolbarToggler&&(L.off(E.findOne(tt),N),L.off(E.findOne(tt),U)),this._element=null,this._options=null,this._isCodeShown=null,this._toolbarToggler=null,this._toolsWidth=null,this._selection=null,this._UUID=null,this._textarea=null}getCode(){return this._content.innerHTML}_init(){const{initMDB:t,Ripple:e,Dropdown:n,Tooltip:o}=mdb;t({Ripple:e,Dropdown:n,Tooltip:o}),a.setDataAttribute(this._element,"uuid",this._UUID);const i=this._element.innerHTML,s=document.createElement("div");s.innerHTML=k.contentDiv();const d=E.children(s,K)[0];this._element.innerHTML="",this._element.insertAdjacentHTML("beforebegin",k.textarea(this._options.wysiwygTextareaName,this._UUID)),this._element.insertAdjacentHTML("beforeend",k.toolBar(this._options)),this._element.append(d),this._content=E.findOne(K,this._element),this._toolbar=E.findOne(Z,this._element),this._textarea=E.prev(this._element,".wysiwyg-textarea")[0],this._content.innerHTML=i,this._textarea.value=i,this._updateToolbar(),E.find(P,this._toolbar).forEach((t=>{new mdb.Input(t).init()}));const r=E.find(`${X}:not(${V}) ${F}`,this._element),l=E.find(Q,this._element);this._initTooltips(this._toolbar),this._initDropdowns(this._toolbar),this._onActionButtonClick(r),this._onInput(),this._onWindowResize(),this._onBlur(),this._onOpenDropdown(l)}_onInput(){L.on(this._content,R,(()=>{this._textarea.value=this._content.innerHTML}))}_onWindowResize(){this._windowResizeHandler=this._updateToolbar.bind(this),L.on(window,M,this._windowResizeHandler)}_onActionButtonClick(t){t.forEach((t=>{L.on(t,U,(e=>{e.preventDefault();const n=a.getDataAttribute(t,"cmd"),o="close-dropdown"===n;if("insertlink"===n||"insertpicture"===n||o){const e=t.closest(z).previousElementSibling;if(mdb.Dropdown.getInstance(e).hide(),this._toolbarToggler){const t=E.parents(this._toolbarToggler,W)[0];L.off(t,N);mdb.Dropdown.getInstance(this._toolbarToggler).hide(),this._onCloseDropdown(t)}}o||(this._performAction(e.target),this._content.focus())}))}))}_onBlur(){L.on(this._content,"blur",(()=>{const t=document.getSelection();this._selection.focusOffset=t.focusOffset,this._selection.focusNode=t.focusNode,this._selection.anchorOffset=t.anchorOffset,this._selection.anchorNode=t.anchorNode}))}_onOpenDropdown(t){t.forEach((t=>{L.on(t,"show.bs.dropdown",(t=>{const e=document.getSelection();e.baseNode||this._content.focus();const n=e.baseNode.parentElement.href,o=e.toString(),i=t.target.nextElementSibling,s=E.findOne(F,i),d="insertpicture"===a.getDataAttribute(s,"cmd");if(n){E.findOne(q,i).value=n}if(!d){E.findOne(J,i).value=o}i.querySelectorAll(it).forEach((t=>{t.dispatchEvent(new Event("blur"))}))}))}))}_onCloseDropdown(t){L.on(t,N,(t=>{const e=document.activeElement,n=E.parents(e,z)[0],o=t.target.parentElement.parentElement.classList.contains(A);n&&o&&t.preventDefault()}))}_isContentFocused(){const t=document.activeElement,e=t?E.parents(t,G)[0]:"";return(e?a.getDataAttribute(e,"uuid"):"")===this._UUID}_updateToolbar(){const t=this._content.offsetWidth,e=E.find(`${X}:not(${V})`,this._element);let n=0;this._toolbarToggler&&(n+=this._toolbarToggler.offsetWidth),e.forEach(((e,o)=>{const i=e.classList.contains(B);n+=e.offsetWidth||this._toolsWidth[o],t<n&&!i?(this._toolsWidth[o]=e.offsetWidth,a.addClass(e,B),this._toolbarToggler||this._createToolbarToggler(),this._updateToolbarTogglerMenu()):t>n&&i&&(a.removeClass(e,B),this._updateToolbarTogglerMenu())}))}_randomUUID(){let t=(new Date).getTime();return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,(e=>{const n=(t+16*Math.random())%16|0;return t=Math.floor(t/16),("x"==e?n:3&n|8).toString(16)}))}_createToolbarToggler(){this._toolbar.insertAdjacentHTML("beforeend",k.toolbarToggler(this._options.wysiwygTranslations)),this._toolbarToggler=E.findOne(et,this._toolbar);const t=E.parents(this._toolbarToggler,W)[0];this._initDropdowns(t),this._onCloseDropdown(t),this._updateToolbar()}_updateToolbarTogglerMenu(){const t=E.findOne(nt,this._element),e=E.find(at,this._element);t.innerHTML="",e.forEach((e=>{const n=E.children(e,"div")[0].cloneNode(!0);t.appendChild(n)}));const n=E.find(ot,t),o=E.find(`${Q}:not(${B})`,this._element);this._onOpenDropdown(o),this._onActionButtonClick(n),this._initDropdowns(t),this._initTooltips(t),0===t.childNodes.length&&this._removeToolbarToggler()}_initDropdowns(t){E.find(Y,t).map((t=>mdb.Dropdown.getOrCreateInstance(t)))}_initTooltips(t){E.find("[data-mdb-tooltip-init]",t).forEach((t=>{let e=mdb.Tooltip.getInstance(t);e||(e=new mdb.Tooltip(t,{trigger:"manual"})),t.addEventListener("mouseenter",(t=>{const n=E.findOne(Y,t.target);!!n&&n.classList.contains("show")||e.show()})),t.addEventListener("mouseleave",(()=>{e.hide()}))}))}_removeToolbarToggler(){this._toolbarToggler.closest(V).remove(),this._toolbarToggler=""}_performAction(t){this._isContentFocused()||(this._content.focus(),document.execCommand("selectAll",!1,null),document.getSelection().collapseToEnd());const e=t.closest(F);let n=a.getDataAttribute(e,"cmd"),o=a.getDataAttribute(e,"arg");if("insertlink"===n||"insertpicture"===n){const e=t.closest(z),i=E.findOne(q,e),a=E.findOne(J,e),s=document.getSelection(),{anchorNode:d,anchorOffset:r,focusNode:l,focusOffset:c}=this._selection;a?(o=`<a href="${i.value}" target="_blank">${a.value}</a>`,a.value=""):o=`<img src="${i.value}" target="_blank" class="img-fluid" />`,n="insertHTML",s.setBaseAndExtent(d,r,l,c),i.value=""}"toggleHTML"!==n?document.execCommand(n,!1,o):this._isCodeShown?(this._content.innerHTML=this._content.textContent,this._content.classList.remove(I),this._isCodeShown=!1,a.removeClass(e,H)):(this._content.textContent=this._content.innerHTML,this._content.classList.add(I),this._isCodeShown=!0,a.addClass(e,H))}_getConfig(t){const e=a.getDataAttributes(this._element);return t={...st,...e,...t},((t,e,n)=>{Object.keys(n).forEach((o=>{const i=n[o],a=e[o],s=a&&((d=a)[0]||d).nodeType?"element":(t=>null==t?`${t}`:{}.toString.call(t).match(/\s([a-z]+)/i)[1].toLowerCase())(a);var d;if(!new RegExp(i).test(s))throw new Error(`${t.toUpperCase()}: Option "${o}" provided type "${s}" but expected type "${i}".`)}))})(C,t,dt),t}static get NAME(){return C}static jQueryInterface(t,e){return this.each((function(){let o=n.getData(this,D);const i="object"==typeof t&&t;if((o||!/dispose|hide/.test(t))&&(o||(o=new rt(this,i)),"string"==typeof t)){if(void 0===o[t])throw new TypeError(`No method named "${t}"`);o[t](e)}}))}static getInstance(t){return n.getData(t,D)}}var lt;return E.find("[data-mdb-wysiwyg-init]").forEach((t=>{let e=rt.getInstance(t);return e||(e=new rt(t)),e})),lt=()=>{const e=t();if(e){const t=e.fn[C];e.fn[C]=rt.jQueryInterface,e.fn[C].Constructor=rt,e.fn[C].noConflict=()=>(e.fn[C]=t,rt.jQueryInterface)}},"loading"===document.readyState?document.addEventListener("DOMContentLoaded",lt):lt(),rt}));
//# sourceMappingURL=wysiwyg.min.js.map
