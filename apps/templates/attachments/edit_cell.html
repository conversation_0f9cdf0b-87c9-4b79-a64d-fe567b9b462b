<ul class="navbar-nav">
  <li class="nav-item dropdown">
    <a class="nav-link dropdown-toggle" role="button" data-bs-toggle="dropdown" aria-expanded="false">
      {{ record|truncatechars:50|default:'No Name' }}
    </a>
    <ul class="dropdown-menu">
      <li class="submenu submenu-md dropend">
        {% if record.attachment_type != AttachmentTypes.EXPORT.value %}
          <a class="dropdown-item" hx-get="{% url 'attachments:detail' id=record.id %}"
             hx-target="#attachment-modal-content" hx-swap="innerHTML"
             hx-trigger="click">
            <i class="fa-solid fa-pen-to-square"></i>
            Edit
          </a>
        {% endif %}
      </li>
      <li class="submenu submenu-md dropend">
        <a class="dropdown-item" href="{{ record.get_absolute_url }}">
          <i class="fa-regular fa-circle-down"></i> Download
        </a>
      </li>
      <li class="submenu submenu-md dropend">
        <a class="dropdown-item" hx-get="{% url 'attachments:delete' id=record.id %}"
           hx-target="#attachment-modal-content" hx-swap="innerHTML"
           hx-trigger="click">
          <i class="fa-solid text-danger fa-trash"></i> Delete
        </a>
      </li>
    </ul>
  </li>
</ul>
