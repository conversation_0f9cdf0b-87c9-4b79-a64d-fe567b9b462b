{% extends "common/list.html" %}
{% load crispy_forms_tags %}

{% block css %}
  {{ block.super }}
  <style>
    legend {
      font-size: unset;
    }
  </style>
{% endblock %}

{% block list-header %}
  <div class="row">
    <h4>File Exports</h4>
  </div>
  <form method="get" action="{% url 'attachments:list' %}">
    <div class="row">
      <div class="col-6">
        {{ filter.form.file_location | as_crispy_field }}
      </div>
      <div class="col-6">
        {{ filter.form.created | as_crispy_field }}
      </div>
      <div class="col-6">
        {{ filter.form.attachment_type | as_crispy_field }}
      </div>
      <div class="col-6">
        {{ filter.form.status | as_crispy_field }}
      </div>
    </div>
    <div class="d-flex justify-content-end">
      <div class="btn-group">
        <button class="btn btn-warning">Apply</button>
      </div>
    </div>
  </form>
{% endblock %}

{% block list-results %}
  {{ block.super }}
  <div id="attachment-modal" class="modal fade modal-md">
    <div class="modal-dialog" style="overflow-y: scroll;">
      <div class="modal-header">
        <span id="attachment-modal-header-title"></span>
        <button id="attachment-modal-close" type="button" class="btn-close" data-bs-dismiss="modal"
                aria-label="Close"></button>
      </div>
      <div class="modal-content" id="attachment-modal-content" hx-target="this"></div>
    </div>
  </div>

{% endblock %}
