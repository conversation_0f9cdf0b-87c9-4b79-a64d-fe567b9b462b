{% load view_breadcrumbs %}
<div class="container-fluid p-2" id="breadcrumbs">
  <nav aria-label="breadcrumb">
    <ol class="breadcrumb m-2">
      {% for url, label in breadcrumbs %}
        {% if not url or forloop.counter == breadcrumbs_total %}
          <li class="breadcrumb-item{% if forloop.counter == breadcrumbs_total %} active{% endif %}"
              {% if forloop.counter == breadcrumbs_total %}aria-current="page"{% endif %}
              title="{{ label|safe }}">{{ label|safe|truncatechars:50 }}</li>
        {% else %}
          <li class="breadcrumb-item"><a class="breadcrumb-link" href="{{ url }}"
                                         title="{{ label|safe }}">{{ label|safe|truncatechars:50 }}</a></li>
        {% endif %}
      {% endfor %}
    </ol>
  </nav>
</div>
