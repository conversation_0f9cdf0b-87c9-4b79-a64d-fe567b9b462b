{% load i18n %}
{% if perms.briefcase.change_applicabilityreview %}
    <div class="p-2 d-flex flex-column justify-content-center">
        <span>Actions:</span>
        {% if applicability_review.is_suggestion_to_add %}
            <button
                    class="btn btn-md m-2 btn-shadow btn-conformii-dark"
                    hx-target="#applicability-review-modal-content"
                    hx-get="{% url 'briefcase:applicability-review:reviewer-update' applicability_review.id user.id %}"
                    hx-vals="js:{review_action: '{{ applicability_review.ReviewActions.ACCEPTED.value }}', session_key: '{{ review_group_session_key }}'}"
                    hx-swap="innerHTML"
                    title="Add to Briefcase"
                    data-bs-dismiss="modal"
            >
                {% translate "Add to Briefcase" %}
            </button>
            <button
                    class="btn btn-md m-2 btn-secondary"
                    hx-target="#applicability-review-modal-content"
                    hx-get="{% url 'briefcase:applicability-review:reviewer-update' applicability_review.id user.id %}"
                    hx-vals="js:{review_action: '{{ applicability_review.ReviewActions.DECLINED.value }}', session_key: '{{ review_group_session_key }}'}"
                    hx-swap="innerHTML"

                    data-bs-dismiss="modal"
            >
                {% translate "Dismiss" %}
            </button>
        {% elif applicability_review.is_suggestion_to_keep %}
            <button
                    class="btn btn-md m-2 btn-shadow btn-conformii-dark"
                    hx-target="#applicability-review-modal-content"
                    hx-get="{% url 'briefcase:applicability-review:reviewer-update' applicability_review.id user.id %}"
                    hx-vals="js:{review_action: '{{ applicability_review.ReviewActions.ACCEPTED.value }}', session_key: '{{ review_group_session_key }}'}"
                    hx-swap="innerHTML"
                    title="Keep in Briefcase"
                    data-bs-dismiss="modal"
            >
                {% translate "Keep in Briefcase" %}
            </button>
            <button
                    class="btn btn-md m-2 btn-danger"
                    hx-target="#applicability-review-modal-content"
                    hx-get="{% url 'briefcase:applicability-review:reviewer-update' applicability_review.id user.id %}"
                    hx-vals="js:{review_action: '{{ applicability_review.ReviewActions.DECLINED.value }}', session_key: '{{ review_group_session_key }}'}"
                    hx-swap="innerHTML"
                    data-bs-dismiss="modal"
            >
                {% translate "Remove from Briefcase" %}
            </button>
        {% elif applicability_review.is_suggestion_to_remove %}

            {% if applicability_review.applicable_to_tenant %}

                <button
                        class="btn btn-md m-2 btn-shadow btn-danger"
                        hx-target="#applicability-review-modal-content"
                        hx-get="{% url 'briefcase:applicability-review:reviewer-update' applicability_review.id user.id %}"
                        hx-vals="js:{review_action: '{{ applicability_review.ReviewActions.ACCEPTED.value }}', session_key: '{{ review_group_session_key }}'}"
                        hx-swap="innerHTML"
                        title="Remove from Briefcase"
                        data-bs-dismiss="modal"
                >
                    {% translate "Remove from Briefcase" %}
                </button>

                <button
                        class="btn btn-md m-2 btn-secondary"
                        hx-target="#applicability-review-modal-content"
                        hx-get="{% url 'briefcase:applicability-review:reviewer-update' applicability_review.id user.id %}"
                        hx-vals="js:{review_action: '{{ applicability_review.ReviewActions.DECLINED.value }}', session_key: '{{ review_group_session_key }}'}"
                        hx-swap="innerHTML"
                        data-bs-dismiss="modal"
                >
                    {% translate "Keep In Briefcase" %}
                </button>

            {% else %}
                <button
                        class="btn btn-md m-2 btn-secondary"
                        hx-target="#applicability-review-modal-content"
                        hx-get="{% url 'briefcase:applicability-review:reviewer-update' applicability_review.id user.id %}"
                        hx-vals="js:{review_action: '{{ applicability_review.ReviewActions.ACCEPTED.value }}', session_key: '{{ review_group_session_key }}'}"
                        hx-swap="innerHTML"
                        data-bs-dismiss="modal"
                >
                    {% translate "Dismiss" %}
                </button>
            {% endif %}

        {% elif applicability_review.is_suggestion_to_must_remove %}
            <button
                    class="btn btn-md m-2 btn-shadow btn-danger"
                    hx-target="#applicability-review-modal-content"
                    hx-get="{% url 'briefcase:applicability-review:reviewer-update' applicability_review.id user.id %}"
                    hx-vals="js:{review_action: '{{ applicability_review.ReviewActions.ACCEPTED.value }}', session_key: '{{ review_group_session_key }}'}"
                    hx-swap="innerHTML"
                    title="Remove from Briefcase"
                    data-bs-dismiss="modal"
            >
                {% translate "Remove from Briefcase" %}
            </button>
        {% else %}
            <button
                    class="btn btn-md m-2 btn-secondary"
                    hx-target="#applicability-review-modal-content"
                    hx-get="{% url 'briefcase:applicability-review:reviewer-update' applicability_review.id user.id %}"
                    hx-vals="js:{review_action: '{{ applicability_review.ReviewActions.DECLINED.value }}', session_key: '{{ review_group_session_key }}'}"
                    hx-swap="innerHTML"
                    title="Dismiss"
                    data-bs-dismiss="modal"
            >
                {% translate "Dismiss" %}
            </button>
        {% endif %}

    </div>
{% endif %}
