{% extends "briefcase/applicability_review/bulk_actions/action_response.html" %}
{% load static i18n crispy_forms_tags %}

{% block form_block %}
  <div class="p-2 m-2">
    <span>
      {% translate "Would you like to assess the selected clause updates now?" %}
    </span>
  </div>
  <span id="applicability-review-modal-header-title" hx-swap-oob="true">
    <h4>
      {{ form.title }}
    </h4>
  </span>

  <div class="d-flex justify-content-end">
    <div class="btn-group shadow-none m-1 p-1">
      <a
        class="btn btn-link btn-warning btn-sm mx-1"
        aria-label="Yes"
        hx-get="{% url 'briefcase:bulk-assessment' %}"
        hx-target="#assessment-modal-content"
        hx-trigger="click"
        hx-vals="js:{session_key: '{{ review_group_session_key }}', 'is_modal_popup': 1}"
        hx-swap="innerHTML"
        target="_blank"
        data-bs-dismiss="modal"
      >
        Yes
      </a>
      <button
        class="btn btn-secondary btn-sm"
        aria-label="No"
        type="button"
        data-bs-dismiss="modal"
      >
        No
      </button>
    </div>
  </div>
{% endblock %}
