{% load crispy_forms_tags static i18n %}
<div class="progress">
  <div class="indeterminate"></div>
</div>
<form
  id="applicability-review-{{ applicability_review.id }}-user-{{ applicability_review_user.user.id }}"
  method="post"
  action="{% url "briefcase:applicability-review:reviewer-update" applicability_review.id applicability_review_user.user.id %}"
  hx-post="{% url "briefcase:applicability-review:reviewer-update" applicability_review.id applicability_review_user.user.id %}"
  hx-target="this"
  hx-swap="outerHTML"
  hx-vals="js:{review_action: '{{ form.review_action }}', session_key: '{{ review_group_session_key }}'}"
  hx-indicator=".progress"
  hx-trigger-after="submit"
>

  {% csrf_token %}
  <div class="p-2 m-2">
    {% include "briefcase/applicability_review/reviewers/header.html" %}
  </div>
  <div class="p-2 m-2">
    {{ form.non_field_errors }}
    {{ form.comment | as_crispy_field }}
    {{ form.user_review_action | as_crispy_field }}
    {% if form.keep_in_briefcase_change_type %}
      {{ form.keep_in_briefcase_change_type | as_crispy_field }}
    {% endif %}
  </div>

  <div class="d-flex justify-content-end">
    <div class="btn-group shadow-none m-1 p-1">
      <button
        class="btn btn-warning btn-sm mx-1"
        type="submit"
        aria-label="Save"
      >
        Save
      </button>
      <button
        class="btn btn-secondary btn-sm"
        aria-label="Close"
        type="button"
        data-bs-dismiss="modal"
      >
        Cancel
      </button>
    </div>
  </div>
</form>

{% block htmx_swap_oob %}
  {% include "user_task_list.html" with partial=True %}
  <span id="applicability-review-modal-header-title" hx-swap-oob="true">
    <h4>
      {{ form.title }}
    </h4>
  </span>

  <div id="applicability-review-{{ applicability_review.id }}-parent-div" hx-swap-oob="true">
    {% if applicability_review.needs_evaluation %}
      {% include "briefcase/applicability_review/detail.html" %}
    {% endif %}
  </div>

  {% if applicability_review.is_already_evaluated %}
    <div
      id="layout-{{ applicability_review.layout.id }}-applicability-review-icon"
      hx-swap-oob="true"
    >
    </div>
    {% include "briefcase/applicability_review/layout/toggle.html" %}
  {% endif %}

  {% if update_review_group %}

    {% if document_search_count and all_reviews_count %}
      <span
        id="document-{{ applicability_review.layout.document_id }}-{{ applicability_review.revision_id }}-result-name"
        hx-swap-oob="true">
        {{ applicability_review.layout.document.name }}
        ({{ document_search_count }} Clause Updates)
      </span>
      <span class="text-muted small" hx-swap-oob="true"
            id="applicability-review-document-{{ applicability_review.layout.document_id }}-revision-{{ applicability_review.revision_id }}-{{ review_group }}-count">
            {{ all_reviews_count }} {{ review_group_title }}
      </span>
      <span class="m-3 py-1 fw-lighter fs-6" hx-swap-oob="true"
            id="applicability-review-document-{{ applicability_review.layout.document_id }}-revision-{{ applicability_review.revision_id }}-{{ review_group }}-selected-count">
        {% translate 'Bulk Actions' %} ({{ selected_reviews_count }}/{{ all_reviews_count }} {% translate 'selected' %})
      </span>
    {% else %}
      <div id="document-{{ applicability_review.layout.document_id }}-accordion-item" hx-swap-oob="true"></div>
    {% endif %}

  {% endif %}

{% endblock %}

{% block toast %}
  {% include "toast.html" with toast_id="modal-toast" toast_body_id="modal-toast-body" %}
{% endblock %}

