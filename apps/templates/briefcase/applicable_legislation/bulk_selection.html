{% if selected|length > 0 %}
  <footer class="fixed-bottom mt-auto py-3 bg-conformii-secondary text-black search-footer"
          id="search-footer-selection">
    <div class="d-flex justify-content-center">
      <div class="spinner-border spinner-border-sm text-navbar-color my-2" role="status"
           id="footer-selector-loader"></div>
      <strong class="m-2">
        {{ selected|length }} Selected
      </strong>
      {% block bulk_actions %}
        <div
          {% assessment_permission_tooltip %}
        >
          <button
            id="bulk-assess-btn"
            class="btn btn-secondary btn-shadow btn-md mx-2"
            type="button"
            {% if perms.briefcase.change_applicablelegislationassessment %}
            hx-get="{% url 'briefcase:bulk-assessment' %}"
            hx-target="#assessment-modal-content"
            hx-trigger="click"
            hx-vals='js:{is_modal_popup:"True", query:"{{ query }}"}'
            hx-swap="innerHTML"
            {% else %}
            disabled
            {% endif %}
          >
            <i class="text-sm conformii-dark fa-solid fa-shield-halved ve-icon-badge-parent"></i>
            Bulk Assessment
          </button>
        </div>
        <div
          {% applicable_legislation_permission_tooltip action='delete' %}
        >
          <button
            id="remove-from-briefcase-btn"
            class="btn btn-secondary btn-shadow btn-md mx-2"
            type="button"
            {% if perms.briefcase.delete_applicablelegislation %}
            hx-headers='{"X-CSRFToken": "{{ csrf_token }}"}'
            hx-target="#briefcase-toggle-modal-content"
            hx-vals="js:{action:'remove', session_key: '{{ session_key }}', render_layouts_on_submit: 0}"
            hx-get="{% url 'briefcase:toggle_briefcase' %}"
            hx-indicator="#footer-selector-loader"
            hx-swap="innerHTML"
            data-bs-dismiss="modal"
            {% else %}
            disabled
            {% endif %}
          >
            <i class="text-sm text-bs-red fa-solid fa-circle-minus"></i>
            Remove from Briefcase
          </button>
        </div>
        <button
          id="unselect-all-btn"
          class="btn btn-secondary btn-shadow btn-md mx-2"
          type="button"
          hx-headers='{"X-CSRFToken": "{{ csrf_token }}"}'
          hx-target="#search-footer-selection"
          hx-vals="js:{toggle: 0, session_key: '{{ session_key }}'}"
          hx-get="{% url 'search:library:selection' %}"
          hx-indicator="#footer-selector-loader"
          hx-swap="outerHTML"
          data-bs-dismiss="modal"
        >
          <i class="text-sm text-grey fa-solid fa-circle-xmark"></i>
          Unselect All
        </button>
      {% endblock %}
    </div>
  </footer>
{% endif %}
