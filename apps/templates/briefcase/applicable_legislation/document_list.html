{% extends "search/document_list.html" %}
{% load applicable_legislation_tags %}

{% block accordion_btn_parent_attributes %}
  id="document-{{ document.document_id }}-accordion-item"
{% endblock %}

{% block accordion_btn_attributes %}
  {% url 'briefcase:applicable-legislation-document' document.document_id as document_detail_url %}
  id="document-{{ document.document_id }}-accordion-btn"
  hx-get="{% url_with_query document_detail_url %}"
  hx-vals="js:{session_key: '{{ session_key }}'}"
  hx-target="#document-{{ document.document_id }}"
  hx-swap="innerHTML"
  hx-trigger="click once"
  hx-indicator='#document-{{ document.document_id }}-loader'
  class="accordion-button collapsed right-align-accordion-btn"
  type="button"
  data-bs-toggle="collapse"
  data-bs-target="#document-{{ document.document_id }}"
  aria-expanded="false"
  aria-controls="document-{{ document.document_id }}"
{% endblock %}

{% block accordion_loader %}
  <div id="document-{{ document.document_id }}"
       class="accordion-collapse collapse justify-content-center">
    <div class="spinner-border spinner-border-sm text-navbar-color my-2 position-relative" role="status"
         id="document-{{ document.document_id }}-loader"
         style="left: 50%;"
    ></div>
    <span class="spinner-text text-navbar-color text-center">Fetching Matching Results...</span>
  </div>
{% endblock %}

{% block document_result_name %}
  <div class="text-break">
    {{ document.highlighted_document_name|default:document.document__name|safe }}
  </div>
{% endblock %}

{% block document_result_stats %}
  <div class="d-flex" style="min-width: fit-content">
    {% include "briefcase/applicable_legislation/document_result_stats.partial.html" with document_id=document.document_id %}
    <div id="document-{{ document.document_id }}-result-stats-indicator"
        class="spinner-border spinner-border-sm text-navbar-color" role="status"></div>
  </div>
{% endblock %}
