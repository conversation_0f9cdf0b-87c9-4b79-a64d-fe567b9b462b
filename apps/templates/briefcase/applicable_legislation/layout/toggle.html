<button
  id="layout-{{ layout.id }}-briefcase-icon"
  {% if render_layouts_on_submit %}
  hx-swap-oob="true"
  {% endif %}
  {% if not layout.applicable_to_tenant %}
  class="btn btn-sm shadow-none ms-3 border-0 text-secondary opacity-25"
    {% if perms.briefcase.add_applicablelegislation %}
  hx-get="{% url 'briefcase:toggle_briefcase' %}"
  hx-vals="js:{layout_id: {{ layout.id }}, session_key: '{{ session_key }}', render_layouts_on_submit: 1, action: 'add'}"
  hx-target="#briefcase-toggle-modal-content"
  hx-trigger="click"
  hx-indicator="#toggle-briefcase-{{ layout.id }}-loader"
  hx-swap="innerHTML"
    {% else %}
  disabled
    {% endif %}

  {% else %}
  class="btn btn-sm shadow-none bg-transparent text-bg-contrast"

    {% if perms.briefcase.delete_applicablelegislation %}
  hx-get="{% url 'briefcase:toggle_briefcase' %}"
  hx-vals="js:{layout_id: {{ layout.id }}, session_key: '{{ session_key }}', render_layouts_on_submit: 1, action: 'remove'}"
  hx-target="#briefcase-toggle-modal-content"
  hx-trigger="click"
  hx-indicator="#toggle-briefcase-{{ layout.id }}-loader"
  hx-swap="innerHTML"
    {% else %}
  disabled
    {% endif %}

  {% endif %}
>
  <i
    class="fa-solid fa-briefcase"
    {% if not layout.applicable_to_tenant %}
    title="Add to Briefcase"
    {% else %}
    title="Remove from Briefcase"
    {% endif %}
  >
  </i>
</button>
