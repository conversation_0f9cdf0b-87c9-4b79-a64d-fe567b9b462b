{% extends 'base.html' %}
{% load crispy_forms_tags static comment_tags i18n %}

{% block javascript %}
  {{ block.super }}

  {{ form.media }}
{% endblock %}

{% block content %}
  <div class="container-fluid mt-3">
    <h5><strong>Assessment</strong></h5>
    {% if not can_manage_assessment %}
      <p class="text-red">
        <b>Note</b>: You do not have permission to edit this assessment
      </p>
    {% endif %}
    {% if check_if_document_is_archived %}
      <p class="text-red">
        <b>Note</b>: The document under assessment is archived.
      </p>
    {% endif %}
    {% if layout_is_descendant %}
      <p class="text-red">
        <b>Note</b>: Saving this form will create a new assessment for this subclause. Alternatively, you can assess the
        parent clause.
      </p>
    {% endif %}
    {% if layout_has_descendants %}
      <p class="text-red">
        <b>Note</b>: Some sub levels of this clause have their own assessments and these will not be updated. You will
        have to
        assess them separately.
      </p>
    {% endif %}
    {% if layout_separate_assessment %}
      <p class="text-red">
        <b>Note</b>: This sub-clause is independent of its parent clause assessment. Any changes to the parent
        assessment will not be reflected on this assessment.
      </p>
    {% endif %}
  </div>
  {% include "briefcase/assessment.partial.html" %}
  <div style="width: 100%;"
       class="d-flex flex-row justify-content-between ps-2 {% if applicability_review %}bg-danger{% endif %}">
    <strong class="p-1 m-1 pt-2">Date Added: {{ object.created|date:"M d,Y h:i A T" }}</strong>
    <strong class="p-1 m-1 pt-2">{% if applicability_review %} Warning: An applicability review exists
      for this clause.
      You should complete it before this assessment. You can see it <a id="layout-{{ layout.id }}-briefcase-icon"
                                                                       hx-get="{% url 'briefcase:applicability-review:detail' applicability_review.id %}"
                                                                       hx-target="#applicability-review-modal-content"
                                                                       hx-trigger="click" class="link-opacity-100"
                                                                       hx-vals="js:{show_document_link: true}"
                                                                       hx-swap="innerHTML" data-bs-dismiss="modal">
        here
      </a>

      {% include "briefcase/applicability_review/modal.html" %}
    {% endif %}
    </strong>
    <div class="p-1 m-1 pe-4 me-4 d-flex justify-content-between">
      <div class="spinner-border spinner-border-sm text-navbar-color m-2 mx-4" role="status"
           id="assessment-form-loader"></div>
      <div>
        {% if can_manage_assessment %}

          {% if layout_is_descendant and same_assessment_highest_order_layout_id %}
            <a
              class="btn btn-primary m-2 cursor-pointer"
              target="_blank"
              href="{% url 'briefcase:assessment_form_with_layout_id' layout_id=same_assessment_highest_order_layout_id %}"
            >
              Edit Parent Assessment
            </a>
          {% endif %}

          <button class="btn btn-warning m-2" form="assessment-form-{{ object.id }}" type="submit">
            {% if layout_is_descendant %}
              Create Assessment
            {% else %}
              Save
            {% endif %}
          </button>
        {% endif %}
      </div>
      <a href="{{ success_url }}" class="btn btn-secondary m-2" type="button"
         style="margin-right: 60px;">Cancel</a>
    </div>
  </div>
{% endblock %}

{% block navbar %}
  {% include "navbar.html" with help_page_url="https://conformii.doc.vantedge.com/Guide/4-company-briefcase/#42-assessments" %}
{% endblock %}

{% block modal %}
  {{ block.super }}
  {% include "briefcase/applicability_review/modal.html" %}
{% endblock %}
