{% load crispy_forms_tags comment_tags %}
<div class="accordion-item" id="controldetail-{{ form_identifier }}">
  <h4 class="accordion-header" id="controldetailheading-{{ form_identifier }}">
    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
            data-bs-target="#controldetailcollapse-{{ form_identifier }}" aria-expanded="true"
            aria-controls="controldetailcollapse-{{ form_identifier }}">
      <div class="d-flex flex-column flex-md-row justify-content-between w-100">
        <div class="column">
          <div>
            <small>Control Document</small>
          </div>
          <div>
            <h5>
              <strong>
                {{ control_document.name }}
              </strong>
            </h5>
          </div>
        </div>
        <div class="d-flex align-items-center">
          {% if control_document.sharepoint_url %}
            <a href="{{ control_document.sharepoint_url }}" target="_blank"
               class="btn btn-link text-decoration-none"
               title="Open">
              <i class="fas fa-share-square"></i>
            </a>
          {% elif control_document.url %}
            <a href="{{ control_document.url }}" target="_blank" class="btn btn-link text-decoration-none"
               title="Open">
              <i class="fas fa-share-square"></i>
            </a>
          {% else %}
            <a class="btn btn-link text-decoration-none" type="button" disabled title="No SharePoint URL available">
              <i class="fas fa-share-square"></i>
            </a>
          {% endif %}
        </div>
      </div>
    </button>
  </h4>
  <div id="controldetailcollapse-{{ form_identifier }}" class="accordion-collapse collapse"
       aria-labelledby="controldetailheading-{{ form_identifier }}">
    <div id="accordionbody-{{ form_identifier }}" class="accordion-body">
      <form id="controlform_{{ form_identifier }}" method="post"
            action="{% url 'briefcase:control_bulk_create_form' %}"
            hx-post="{% url 'briefcase:control_bulk_create_form' %}"
            hx-target="#controldetail-{{ form_identifier }}" hx-swap="outerHTML"
            hx-vals="js:{form_identifier: '{{ form_identifier }}'}"
      >
        {% csrf_token %}
        <style>
          .select2 {
            width: 100% !important;
          }
        </style>
        <div class="flex-column p-4 d-flex">
          <h6 class="row"><strong>{{ form.control_document | as_crispy_field }}</strong></h6>
          <h6 class="row"><strong>{{ form.operational_areas | as_crispy_field }}</strong></h6>
          <h6 class="row"><strong>{{ form.programs | as_crispy_field }}</strong></h6>
          <h6 class="row"><strong>{{ form.activities | as_crispy_field }}</strong></h6>
          <h6 class="row"><strong>{{ form.comment | as_crispy_field }}</strong></h6>
          <h6 class="row"><strong>{{ form.reviewed_by | as_crispy_field }}</strong></h6>
          <h6 class="row"><strong>{{ form.reviewed_reason | as_crispy_field }}</strong></h6>

        </div>
        <div class="d-flex justify-content-between py-2 my-2 border-top">
          <div>
            <button type="submit" class="btn btn-warning">Save</button>
            <button type="button" class="btn btn-danger"
                    hx-trigger="click"
                    hx-post="{% url 'briefcase:control_bulk_delete' %}"
                    hx-target="#controldetail-{{ form_identifier }}"
                    hx-swap="delete">
              Delete
            </button>

          </div>
          <div class="d-flex flex-column">
            <div class="d-flex flex-row justify-content-end">
              <h6 class="p-2"><strong>Approve</strong></h6>
              <div class="py-2">{{ form.approved }}</div>
            </div>
            <div>
              {% for error in form.approved.errors %}
                <div class="text-warning">{{ error }}</div>
              {% endfor %}
            </div>
          </div>
        </div>
      </form>
    </div>
  </div>
</div>
