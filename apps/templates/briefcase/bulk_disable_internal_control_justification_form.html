{% load crispy_forms_tags comment_tags %}

{% if editing %}
  <button
    id="add-internal-control-button"
    type="button"
    class="m-2 btn btn-secondary"
    hx-swap-oob="true"
    hx-get="{% url 'briefcase:enable_internal_control' %}"
    hx-target="#control-column"
    hx-trigger="click"
    hx-swap="innerHTML"
  >
    Allow Controls
  </button>
  <button disabled id="disable-control-button" type="button" class="m-2 btn btn-secondary btn-shadow"
          hx-swap-oob="true">
    No Controls Required
  </button>
{% endif %}

<form class="py-3" id="usercomment{{ object_type }}_{{ form_identifier }}" method="post"
      action="{% url 'briefcase:disable_internal_control_bulk_form' %}"
      hx-post="{% url 'briefcase:disable_internal_control_bulk_form' %}"
      hx-target="#control-list-parent" hx-swap="innerHTML"
      hx-indicator="#nocontrols-loader"
>
  {% csrf_token %}

  <div class="form-group row">
    <h6><strong>No Internal Controls</strong></h6>
    <div class="col-md-9">
      {{ form.comment | as_crispy_field }}
      {% if user and modified_ts %}
        <small class="form-text text-muted">
          by {{ user }}, {{ modified_ts }}
        </small>
      {% endif %}
    </div>
    <div class="col-md-3 d-flex align-items-center justify-content-start row">
      <button type="submit" class="btn btn-warning mt-2 py-2">Save</button>
    </div>
    <div class="d-flex justify-content-end py-2 my-2">
      <div class="d-flex flex-column">
        <div class="d-flex flex-row justify-content-end">
          <div class="spinner-border spinner-border-sm my-2" role="status" id="nocontrols-loader"></div>
          <h6 class="p-2"><strong>Approve</strong></h6>
          <div class="py-2">{{ form.approved }}</div>
        </div>
        <div>
          {% for error in form.approved.errors %}
            <div class="text-warning">{{ error }}</div>
          {% endfor %}
        </div>
      </div>
    </div>
</form>
