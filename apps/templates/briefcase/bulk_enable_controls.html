<div class="col-md-6 column border border-top-0 p-4" id="control-column" hx-swap-oob="true">
  <div class="col d-flex align-items-center">
    <h5><strong>Internal Controls</strong></h5>
  </div>
  <div class="col d-flex align-items-center justify-content-end">
    <button id="add-internal-control-button" type="button" class="m-2 btn btn-secondary btn-shadow"
      {% if perms.briefcase.add_internalcontrol %}
            hx-trigger="click"
            hx-get="{% url 'briefcase:control_bulk_create_form' %}"
            hx-target="#control-list"
            hx-swap="beforeend"
      {% else %}
            disabled
      {% endif %}
    >+ Internal Control
    </button>
    <button type="button" class="m-2 btn btn-secondary btn-shadow"
      {% if perms.briefcase.add_internalcontrol and perms.briefcase.set_no_controls_required %}
            hx-trigger="click" id="disable-controls-button"
            hx-get="{% url 'briefcase:disable_internal_control_bulk_form' %}"
            hx-target="#control-list-parent"
            hx-swap="innerHTML"
      {% else %}
            disabled
      {% endif %}
    >No Controls Required
    </button>
  </div>
  {% if not perms.briefcase.add_internalcontrol %}
    <p class="form-text">You do not have permission to add Internal Controls</p>
  {% endif %}
  {% if not perms.briefcase.set_no_controls_required %}
    <p class="form-text">You do not have permission to set No Controls Required</p>
  {% endif %}
  <div id="control-list-parent">
    <div class="accordion" id="control-list">
      {% include "briefcase/bulk_empty_internal_control_form.html" %}
    </div>
    <div id="no-control-list"></div>
  </div>
</div>
