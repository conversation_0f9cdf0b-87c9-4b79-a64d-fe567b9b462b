{% load i18n %}

<style>
  img {
    max-width: 100%;
    height: auto;
  }
</style>

{% for applicable_legislation in selected_applicable_legislations %}
  <div class="d-flex flex-row">
    <span hx-get="{% url 'briefcase:calendar-event:get_layout_path_crumbs' applicable_legislation.layout.id %}"
          hx-target="this" hx-swap="outerHTML" hx-trigger="load" id="layout-path-{{ applicable_legislation.id }}">Getting
        path...
</span>
  </div>
  <div class="border-bottom row">
    <span class="fw-bold px-2 col-2" style="text-align: end;">
        <strong>{{ applicable_legislation.layout.fragment.name|default:''|safe }}</strong>
    </span>
    <span class="col-10 mb-2">
        <span style="font-family: 'Times New Roman', Times, serif;">
          {% if applicable_legislation.layout.document.requires_license %}
            {% translate "Please refer to your licensed version of this document" %}
          {% else %}
            {{ applicable_legislation.layout.fragment.content|default:''|safe|truncatewords:30 }}
          {% endif %}
        </span>
        <br>
      {% if applicable_legislation.assessment %}
        <a class="btn btn-primary btn-sm my-2"
           href="{% url 'briefcase:assessment_form' pk=applicable_legislation.assessment.id %}"
           target="_blank"
        >
          See Associated Assessment
        </a>
      {% endif %}
    </span>

  </div>
{% endfor %}
<input type="hidden" name="associated_legislation" id="associated_legislation" value="{{ associated_legislation }}">

{% include "briefcase/assessment_modal.html" %}

{% include "briefcase/applicability_review/modal.html" %}

