{% extends 'briefcase/index.html' %}
{% load i18n %}

{% block tab-content %}
  <div class="tab-pane fade show active" id="briefcase-pills-calendar-event" role="tabpanel"
       aria-labelledby="calendar-event" tabindex="2">
    <div class="d-flex flex-column">
      <div class="text-align-start">
        <figure>
          <blockquote class="blockquote">
            <h4>Suggested Events</h4>
            <div class="spinner-border spinner-border-sm" style="display:none;"></div>
          </blockquote>
          <figcaption class="blockquote-footer">
            Recommendations for calendar events identified as applicable to the company's operations
          </figcaption>
        </figure>
      </div>
      <div class="col m-2 text-align-start" id="applicable-legislation-results">

        {% include "briefcase/calendar_event/suggested_events_pagination.html" %}

        <p>
          Showing {{ page_obj.start_index }}-{{ page_obj.end_index }} of
          {{ page_obj.paginator.count }} items.
        </p>


        <form id="bulk-dismiss-form">

          <div class="m-2 p-2 d-flex justify-content-between align-items-center">
            <div class="col-8">
              {% include 'search/filters.html' %}
            </div>
            <div class="col-4 d-flex justify-content-end">
              {% include 'common/export_formats.html' with export_button_class="btn-sm btn-shadow btn-primary me-2" %}
              <button id="select-all-btn" class="btn btn-sm btn-shadow btn-secondary me-2" type="button">
                <i class="fa-solid fa-check-square fa-md mx-2"></i>Select All
              </button>
              <button class="btn btn-sm btn-shadow btn-danger" type="button" data-bs-toggle="modal"
                      data-bs-target="#exampleModal">
                <i class="fa-solid fa-circle-xmark fa-md mx-2"></i>Bulk Dismiss
              </button>
            </div>
          </div>
          <div class="spinner-border spinner-border-sm text-navbar-color position-relative my-2" role="status"
               id="applicable-legislation-results-loader" style="left: 50%;"></div>
          {% for applicable_legislation in page_obj %}
            <div id="suggested-{{ applicable_legislation.id }}">
              <div class="d-flex flex-row py-2 border-top">
                <div class="col-1 d-flex justify-content-between me-2">
                  <input type="checkbox" name="dismiss_ids" value="{{ applicable_legislation.id }}"
                         class="form-check-input">
                  <span class="fw-bold document-content">
                      {{ applicable_legislation.layout.fragment.name|default:''|safe }}
                  </span>
                </div>
                <span class="col-9 flex-fill document-content ms-3">
                            {% if applicable_legislation.layout.document.requires_license %}
                              {% translate "Please refer to your licensed version of this document" %}
                            {% else %}
                              {{ applicable_legislation.layout.fragment.content|default:''|safe }}
                            {% endif %}
                        </span>
                <div class="col-2 d-flex align-items-center">
                  <div class="w-100">
                    {% if perms.calendar.add_calendareventoccurrence %}
                      <a href="{% url 'briefcase:calendar-event:create' %}" class="btn btn-sm btn-secondary btn-shadow"><i
                        class="fa-solid fa-circle-plus fa-md mx-2"></i>Add Event</a>
                    {% endif %}
                  </div>
                  <div class="w-100">
                    {% if perms.briefcase.change_applicablelegislation %}
                      <a
                        class="btn btn-sm btn-danger text-white"
                        hx-post="{% url 'briefcase:calendar-event:dismiss_suggested_legislation' applicable_legislation.id %}"
                        hx-trigger="click" hx-target="#suggested-{{ applicable_legislation.id }}"
                        hx-swap="outerHTML" hx-confirm="Are you sure you want to dismiss this suggested event?"
                      >
                        <i class="fa-solid fa-circle-xmark fa-md mx-2 "></i>Dismiss
                      </a>
                    {% endif %}
                  </div>
                </div>
              </div>
              <div hx-get="{% url 'briefcase:calendar-event:get_layout_path_crumbs' applicable_legislation.layout.id %}"
                   hx-target="this" hx-swap="outerHTML" hx-trigger="load"
                   id="layout-path-{{ applicable_legislation.id }}">Getting path...
              </div>
            </div>
          {% endfor %}
          {% if page_obj|length == 0 %}
            <div class="row text-align-center">
              <div class="col">
                <div class="alert alert-warning" role="alert">
                  No suggested events found matching the search criteria
                </div>
              </div>
            </div>
          {% endif %}
        </form>

        {% include "briefcase/calendar_event/suggested_events_pagination.html" %}

      </div>

    </div>
  </div>


  <!-- Modal -->
  <div class="modal fade" id="exampleModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog">
      <div class="modal-content">
        <div class="modal-header">
          <h1 class="modal-title fs-5" id="exampleModalLabel">Confirm Bulk Dismiss</h1>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <p id="dismiss-count">Are you sure you want to dismiss the selected items?</p>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
          {% if perms.briefcase.change_applicablelegislation %}
            <button type="button" class="btn btn-danger" hx-post="{% url 'briefcase:calendar-event:bulk_dismiss' %}"
                    hx-target="#calendar-events" hx-swap="outerHTML" hx-trigger="click"
                    hx-include="[name='dismiss_ids']">
              Dismiss
            </button>
          {% endif %}
        </div>
      </div>
    </div>
  </div>
{% endblock %}

{% block navbar %}
  {% include "navbar.html" with help_page_url="https://conformii.doc.vantedge.com/Guide/4-company-briefcase/#441-adding-events-through-pending-events" %}
{% endblock %}

{% block javascript %}
  {{ block.super }}
  <script type="text/javascript">
    function updateSelectAllButtonText(button, isAllChecked) {
      button.innerHTML = '<i class="fa-solid fa-' + (isAllChecked ? 'square-check' : 'check-square') + ' fa-md mx-2"></i>' +
        (isAllChecked ? 'Deselect All' : 'Select All');
    }

    function attachSelectAllListener() {
      var selectAllBtn = document.getElementById('select-all-btn');
      if (selectAllBtn) {
        var allChecked = false;
        selectAllBtn.addEventListener('click', function () {
          var checkboxes = document.querySelectorAll('input[name="dismiss_ids"]');
          allChecked = !allChecked;
          checkboxes.forEach(function (checkbox) {
            checkbox.checked = allChecked;
          });
          updateSelectAllButtonText(selectAllBtn, allChecked);
        });
      }
    }


    document.addEventListener('DOMContentLoaded', function () {
      var myModal = document.getElementById('exampleModal');
      myModal.addEventListener('show.bs.modal', function (event) {
        var checkboxes = document.querySelectorAll('input[name="dismiss_ids"]:checked');
        var count = checkboxes.length;
        var dismissCountElement = document.getElementById('dismiss-count');
        dismissCountElement.textContent = 'Are you sure you want to dismiss ' + count + ' selected item(s)?';
      });

      attachSelectAllListener()
      document.body.addEventListener('htmx:afterSwap', function (e) {
        if (e.detail.target.querySelector('#select-all-btn')) {
          attachSelectAllListener();
        }
      });
    });

  </script>
{% endblock %}
