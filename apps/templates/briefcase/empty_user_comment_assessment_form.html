{% load crispy_forms_tags %}
<form id="usercommentassessment_{{object.id}}"
    method="post" 
    action="{% url 'briefcase:user_comment_assessment_update_form' pk=object.id %}" 
    hx-post="{% url 'briefcase:user_comment_assessment_update_form' pk=object.id %}"
    hx-target="#usercommentassessment_{{object.id}}" 
    hx-swap="outerHTML">
    {% csrf_token %}
    <div class="form-group row">
        <div class="col-md-9">
        <textarea 
            name="{{ form.comment.name }}" 
            placeholder="{{ form.comment.label }}" 
            class="form-control" 
            rows="3"
        >{{ form.comment.value }}</textarea>
        {% for error in form.comment.errors %}
            <div class="error">{{ error }}</div>
        {% endfor %}

        </div>
        <div class="col-md-3 d-flex align-items-start justify-content-start row">
            <button type="submit" class="btn btn-primary">Save</button>
            <button hx-trigger="click"
                    action="{% url 'briefcase:user_comment_assessment_detail' pk=object.id %}" 
                    hx-get="{% url 'briefcase:user_comment_assessment_detail' pk=object.id %}"
                    hx-target="#usercommentassessment_{{object.id}}"
                    class="btn btn-secondary">Cancel</button>
        </div>
    </div>
</form>