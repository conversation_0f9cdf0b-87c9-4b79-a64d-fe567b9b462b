{% extends "tenants/configuration.html" %}
{% load static crispy_forms_tags %}

{% block javascript %}
  {{ block.super }}
  {{ form.media.js }}
{% endblock %}

{% block css %}
  {{ block.super }}
  {{ form.media.css }}
{% endblock %}

{% block tab-content %}
  <div class="row">
    <div class="col-12">
      <h3>
        Editing Program: {{ object.name }}
      </h3>
    </div>
  </div>
  <!-- Action buttons -->
  <div class="row">
    <div class="col-6">
      <a
        class="btn btn-conformii-dark mx-2"
        href="{% url 'briefcase:programs:list' %}"
        role="button"
      >
        Back
      </a>
      {% if object.linked_to_assessment %}
        <a
          class="btn btn-warning mx-2"
          href="{{ object.get_assessments_link }}"
          role="button"
          target="_blank"
        >
          View Assessments
        </a>
      {% endif %}
      {% if object.linked_to_calendar_events %}
        <a
          class="btn btn-warning mx-2"
          href="{{ object.get_calendar_link }}"
          role="button"
          target="_blank"
        >
          View Calendar Events
        </a>
      {% endif %}
      {% if object.linked_to_cap %}
        <a
          class="btn btn-warning mx-2"
          href="{{ object.get_cap_link }}"
          role="button"
          target="_blank"
        >
          View Corrective Action Plans
        </a>
      {% endif %}
    </div>
    <div class="col-6 justify-content-end d-flex">
      {% if perms.briefcase.change_program %}
        <button type="submit" form="program-change-form" class="btn btn-conformii-dark mx-2">Save</button>
      {% endif %}
      {% if perms.briefcase.delete_program %}
        <div
          {% if not object.can_delete %}
            data-bs-toggle="tooltip"
            data-bs-placement="top"
            title="{% get_delete_warning_message %}"
          {% endif %}>
          <button
            type="submit"
            class="btn btn-danger"
            {% if object.can_delete %}
            data-bs-toggle="modal"
            data-bs-target="#deleteConfirmationModal"
            {% else %}
            disabled
            {% endif %}
          >
            Delete
          </button>
        </div>
      {% endif %}
    </div>
  </div>
  <!-- End Action buttons -->
  <hr>
  <form method="POST" id="program-change-form" action="{% url 'briefcase:programs:update' object.id %}">
    {% csrf_token %}
    <small>
      <p>
        Created: {{ object.created }}
      </p>
      <p>
        Last Modified: {{ object.modified }}
      </p>
    </small>
    {{ form.name | as_crispy_field }}
    {{ form.users | as_crispy_field }}
    {{ form.archived | as_crispy_field }}
  </form>

  {% url 'briefcase:programs:delete' object.id as deletion_url %}
  {% url 'briefcase:programs:list' as success_url %}
  {% if perms.briefcase.delete_program %}
    {% with label_prefix="Program: " %}
      {% with full_label=label_prefix|add:object.name %}
        {% include "delete_modal.html" with delete_url=deletion_url object_name=full_label success_url=success_url modal_id="deleteConfirmationModal" %}
      {% endwith %}
    {% endwith %}
  {% endif %}


  <div id="notificationArea"></div>

{% endblock %}

{% block navbar %}
  {% include "navbar.html" with help_page_url="https://conformii.doc.vantedge.com/Guide/06-company-setup/01-company-setup-and-admin/#figure-84-edit-programs" %}
{% endblock %}
