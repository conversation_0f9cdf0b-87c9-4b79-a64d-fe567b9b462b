{% include "briefcase/applicable_legislation/layout/toggle.html" %}

<div
  id="document-{{ layout.document_id }}-layout-{{ layout.id }}-assessment-actions"
  {% if render_layouts_on_submit %}
  hx-swap-oob="true"
  {% endif %}
  {% if layout.applicable_to_tenant %}
  hx-get="{% url 'briefcase:applicable_legislation_actions' layout.id %}"
  hx-trigger="intersect once"
  hx-swap="innerHTML"
  hx-vals="js:{tag_style: {{ tag_style|default:1 }}, assessing_from_briefcase: false}"
  hx-indicator="#toggle-briefcase-{{ layout.id }}-loader"
  {% endif %}
  class="applicable-legislation-actions">
</div>

{% if layout.applicability_review %}
  <button
    id="layout-{{ layout.id }}-applicability-review-icon"
    hx-swap-oob="true"
    class="btn btn-sm shadow-none text-bg-contrast d-flex align-items-center"
    hx-get="{% url 'briefcase:applicability-review:detail' layout.applicability_review %}"
    hx-target="#applicability-review-modal-content"
    hx-trigger="click"
    hx-vals="js:{show_document_link: true}"
    hx-swap="innerHTML"
  >
    <i class="fa-solid fa-circle-exclamation text-bs-red text-large"
       title="Applicability Review Exists"></i>
  </button>
{% endif %}
