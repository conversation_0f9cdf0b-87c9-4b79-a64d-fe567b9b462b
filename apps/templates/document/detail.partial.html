{% load static compress crispy_forms_tags humanize %}

{% compress css %}
  <link rel="stylesheet" type="text/x-scss" href="{% static "scss/styles.scss" %}"/>
{% endcompress %}

<script type="text/javascript">
  $(document).ready(function () {
    var fragment = window.location.hash.substring(1);
    if (fragment) {
      var element = document.getElementById(fragment);
      if (element) {
        element.style['background-color'] = "#fff3cd"
      }
    }
  });
</script>

<!-- Loader Spinners -->
<div class="spinner-border spinner-border-sm text-navbar-color position-relative my-2" role="status" style="left: 50%;"
     id="document-{{ document.id }}-loader"></div>
<span class="spinner-text text-navbar-color text-center">Fetching Matching Results...</span>

<!-- Main Container -->
<div class="container-fluid document-detail" id="document-{{ document.id }}-detail">
  <div class="card border">
    <div class="card-header">
      <!-- Top Row: Document Info and History Actions -->
      <div class="row align-items-center">
        <!-- Document Info Column -->
        <div class="col-9 mb-4 mb-md-0">
          <h5 class="fw-bold card-title">{{ document.name }}</h5>
          <h6>{{ document.version_name }}</h6>
          <p class="text-sm fw-lighter">{{ document.amendment_long_name|default:""|safe }}</p>
        </div>
        <!-- History Actions Column -->
        <div class="col-3">
          <div class="d-flex flex-wrap justify-content-md-end justify-content-start mb-3 mb-md-0">
            {% if request.user.is_staff %}
              <a href="{% url 'document:current:history' document.id %}"
                 class="btn btn-sm btn-shadow btn-secondary m-2">
                Revision History
              </a>
            {% elif document.tenant %}
              <a href="{% url 'document:current:history' document.id %}"
                 class="btn btn-sm btn-shadow btn-secondary m-2">
                Revision History
              </a>
            {% endif %}
            <a href="{% url 'document-diff:index' pk=document.id %}"
               class="btn btn-sm btn-shadow btn-secondary m-2">
              Compare History
            </a>
          </div>
        </div>
      </div>
    </div>
    <div class="card-body">
      <div class="row">
        <!-- Metadata Column -->
        <div class="col-md-6 col-12">
          <dl class="row">
            <dt class="col-4 fw-bold text-sm">Effective Date:</dt>
            <dd class="col-8 text-sm">{{ document.effective_date|date:"F d, Y" }}</dd>

            <dt class="col-4 fw-bold text-sm">Jurisdiction:</dt>
            <dd class="col-8 text-sm">{{ document.jurisdiction }}</dd>

            <dd class="col-12 fw-bold text-sm">
              <a class="text-bg-contrast text-sm link-underline-primary"
                 href="{% url 'document:current:history' document.id %}">View
                History</a>
            </dd>

            <dt class="col-4 fw-bold text-sm">Copyright</dt>
            <dd class="col-8 text-sm text-wrap text-sm">
              {{ document.jurisdiction.copyright|safe|default:"No Copyright" }}
            </dd>
          </dl>
          {% if document.requires_license %}
            <span class="badge badge-sm bg-conformii-primary">Requires License</span>
          {% endif %}
        </div>

        <div class="col-md-6 col-12">
          <!-- Links and Actions Column -->
          <div class="d-flex flex-wrap justify-content-md-end justify-content-start">
            <div class="spinner-border spinner-border-sm text-navbar-color my-2" role="status"
                 id="document-{{ document.id }}-actions-loader"></div>
            {% if not document.archived %}
              {% if document|user_can_edit_document:request.user %}
                <button
                  type="button"
                  class="btn btn-sm btn-shadow btn-warning me-2"
                  hx-get="{% url 'document:revision:reason' document.id %}"
                  hx-target="#document-search-modal-content"
                  hx-trigger="click"
                  hx-indicator="#document-{{ document.id }}-actions-loader"
                  data-bs-dismiss="modal"
                >
                  <i class="text-sm fa-solid fa-pencil"></i>
                  Edit
                </button>
              {% endif %}
              {% if document|user_can_archive_document:request.user %}
                <button
                  type="button"
                  class="btn btn-sm btn-shadow btn-warning"
                  hx-get="{% url 'document:current:archive' document.id %}"
                  hx-target="#document-search-modal-content"
                  hx-trigger="click"
                  hx-indicator="#document-{{ document.id }}-actions-loader"
                  data-bs-dismiss="modal"
                >
                  <i class="text-sm fa-solid fa-box-archive"></i>
                  Archive
                </button>
              {% endif %}
            {% endif %}
          </div>
          <!-- Open Document Link -->
          <div class="d-flex my-1 justify-content-md-end justify-content-start">
            <a href="{{ document.get_absolute_url }}" target="_blank" type="button"
               class="btn btn-link bg-transparent text-bg-contrast">
              <i class="text-sm fa-solid fa-up-right-from-square"></i>
              Open Document
            </a>
          </div>

          <!-- Source Document Link -->
          <div class="d-flex my-1 justify-content-md-end justify-content-start">
            <a href="{{ document.source_url }}" target="_blank" type="button"
               class="btn btn-link bg-transparent text-bg-contrast">
              <i class="text-sm fa-solid fa-share-square"></i>
              Source Document
            </a>
          </div>

          <!-- Referenced Documents Button -->
          <div class="d-flex my-1 justify-content-md-end justify-content-start flex-wrap">
            <div id="reference-document-div">
              <div class="d-flex justify-content-end">
                <div>
                  <button
                    class="btn text-bg-contrast dropdown-toggle btn-link"
                    type="button" data-bs-toggle="collapse" data-bs-target="#referencedDocumentsCollapse"
                    aria-expanded="false" aria-controls="referencedDocumentsCollapse"
                    id="referenceDocumentDropdown"
                  >
                    <i class="text-sm fa-solid fa-book"></i>
                    Referenced Documents
                  </button>
                </div>
              </div>
              <div class="row">
                <div class="col">
                  <div class="collapse multi-collapse" id="referencedDocumentsCollapse">
                    <div class="card">
                      <div class="card-body">
                        <div class="progress referenced-form-progress">
                          <div class="indeterminate"></div>
                        </div>
                        <div class="dropdown-divider"></div>
                        {% include "document/referenced_documents.partial.html" with document=document %}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Actions Block -->
          {% block actions %}
            {% if perms.briefcase.add_applicablelegislation or perms.briefcase.delete_applicablelegislation %}
              <div class="d-flex my-1 justify-content-md-end justify-content-start align-items-center flex-wrap">
                <div class="spinner-border spinner-border-sm text-navbar-color m-2" role="status"
                     id="toggle-briefcase-{{ document.id }}-loader"></div>
                <span class="text-sm pe-2">Actions:</span>

                <button
                  type="button"
                  class="btn btn-sm btn-shadow btn-secondary mx-1 me-2"
                  hx-get="{{ select_all_url }}"
                  hx-trigger="click"
                  hx-target="#selection-footer-parent"
                  hx-indicator="#toggle-briefcase-{{ document.id }}-loader"
                  hx-swap="innerHTML"
                >
                  <i class="text-sm fa-solid fa-check"></i>
                  Select All
                </button>
                <button
                  type="button"
                  class="btn btn-sm btn-shadow btn-secondary mx-1"
                  hx-get="{{ clear_all_url }}"
                  hx-trigger="click"
                  hx-target="#selection-footer-parent"
                  hx-indicator="#toggle-briefcase-{{ document.id }}-loader"
                  hx-swap="innerHTML"
                >
                  <i class="text-sm fa-solid fa-xmark"></i>
                  Clear All
                </button>
              </div>
            {% endif %}
          {% endblock %}
        </div>
        <!-- Clause List Results -->
        <div class="col-12 p-4 px-1" id="document-{{ document.id }}-clause-list-results">
          {% block render_node %}
            {% if paginator.count > paginator.per_page %}
              <div id="partial-warning" class="alert alert-warning my-2" role="alert">
                The results are paginated as it returned more than {{ paginator.per_page|intcomma }} records. To view
                the complete
                data, you can <a href="{{ document.get_absolute_url }}" target="_blank" type="button">Open the
                Document</a> or
                scroll down to the end of the page to load the remaining results.
              </div>
            {% endif %}
            {% include "search/layout_list.html" %}
          {% endblock %}
        </div>
      </div>
    </div>
  </div>
</div>
