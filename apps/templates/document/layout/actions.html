<div class="d-flex mx-1">
  {% url 'search:library:selection' as selection_url %}
  <div
    id="document-{{ layout.document_id }}-layout-{{ layout.id }}-checkbox"
  ></div>
  <div class="mx-1">
    <div class="spinner-border spinner-border-sm text-navbar-color my-2" role="status"
         id="toggle-briefcase-{{ layout.id }}-loader"></div>
  </div>
  {% if perms.briefcase.view_applicablelegislation %}
    <div
      hx-get="{% url 'document:current:layout-actions' layout_id=layout.id %}"
      hx-trigger="intersect once"
      hx-indicator="#toggle-briefcase-{{ layout.id }}-loader"
      hx-swap="outerHTML"
      hx-vals="js:{session_key: '{{ session_key }}', tag_style: {{ tag_style|default:1 }}, no_hierarchy: {{ no_hierarchy|default:0 }}, selection_url: '{{ selection_url }}', query: '{{ query }}'}"
    >
    </div>
  {% endif %}
</div>
