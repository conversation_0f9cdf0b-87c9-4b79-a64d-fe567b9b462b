{% extends "document/layout/render.html" %}

{% block layout_actions %}
  {% if layout.layout_type == root_layout_type %}
    {% include "document/layout/root_draft_actions.html" %}
  {% else %}
    {% include "document/layout/draft_actions.html" %}
  {% endif %}
{% endblock layout_actions %}

{% block layout_content %}
  {% include "document/fragment/draft_render.html" %}
{% endblock layout_content %}

{% block layout_links %}
  <div class="d-flex">
    <div class="mx-1">
      <div class="spinner-border spinner-border-sm text-navbar-color my-2" role="status"
           id="layout-{{ layout.id }}-loader"></div>
    </div>
    {% if layout.layout_type != root_layout_type %}
      <div
        style="min-width: max-content;"
        hx-target="this"
        hx-swap="innerHTML"
        hx-get="{% url "document:revision:layout-actions" id=revision.id layout_id=layout.id %}"
        hx-trigger="intersect once"
        hx-indicator="#layout-{{ layout.id }}-loader"
      >
      </div>
    {% endif %}
  </div>
{% endblock layout_links %}

{% block layout_tags %}
  {% include "document/layout/draft_tags.html" %}

  <div
    class="children-node"
    id="layout-{{ layout.id }}-children"
    style="{{ layout.depth|layout_left_margin }}"
    {% if partial %}hx-swap-oob="true"{% endif %}
  ></div>

  {% if render_descendants_tags %}
    {% include "document/render_tags.partial.html" %}
  {% endif %}

{% endblock layout_tags %}
