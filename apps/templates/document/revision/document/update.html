{% extends "base.html" %}
{% load static i18n django_bootstrap5 crispy_forms_tags %}

{% block css %}
  {{ block.super }}

  <style>

    #div_id_status {
      margin-bottom: 0 !important;
    }

    #div_id_referenced_documents .select2 {
      width: 100% !important;
    }

    #div_id_referenced_documents .select2-search__field {
      width: 100% !important;
    }

    #update-root-tag-form .select2 {
      width: 100% !important;
    }

    #update-root-tag-form select {
      border: 0 !important;
      clip: rect(0 0 0 0) !important;
      height: 1px !important;
      margin: -1px !important;
      overflow: hidden !important;
      padding: 0 !important;
      position: absolute !important;
      width: 1px !important;
    }

  </style>
{% endblock %}

{% block javascript %}
  {{ block.super }}
  <script type="text/javascript" src="{% static 'htmx/checkbox.js' %}"></script>
{% endblock %}

{% block content %}
  {{ block.super }}
  <div class="container-fluid" id="revision-container">
    <script>

      function handleBulkTagCheckboxes(event) {
        let toggle = event.detail.toggleAll;
        let checkboxIds = event.detail.checkboxIds;
        let checkboxToggle = event.detail.checkboxToggle;
        let revisionId = event.detail.revisionId;
        console.log("toggle=", toggle, "revisionId=", revisionId, event);
        if (toggle !== undefined) {
          handleCheckboxes(toggle, revisionId);
        } else if (checkboxIds !== undefined && checkboxToggle !== undefined) {
          handleCheckboxes(checkboxToggle, checkboxIds);
        }
      }

      function toggleSideColumn(event) {
        console.log("toggleSideColumn - event: " + event);
        let elementId = event.detail.elementId;
        let addClass = event.detail.addClass;
        let toggleSideColumn = event.detail.toggleSideColumn;
        console.log(toggleSideColumn);
        let column = document.getElementById(elementId);
        let documentColumn = document.getElementById("document-column");
        if (column && documentColumn) {
          if (toggleSideColumn) {
            column.classList.remove("hidden-element");
            column.classList.add(addClass);
            documentColumn.classList.add("scrollable");
          } else {
            // hide column if it already is being shown
            column.classList.add("hidden-element");
            column.classList.remove(addClass);
            documentColumn.classList.remove("scrollable");
          }
        }
      }

      document.body.addEventListener("handleBulkTagging", function (event) {
        toggleSideColumn(event);
        handleBulkTagCheckboxes(event);
      });

      $(document).ready(function () {
        // Attach to 'select2:select' jQuery event from Select2
        $('#id_referenced_documents').on('change.select2', (e) => {
          // Dispatch 'changed' event to trigger HTMX
          let event = new Event('changed');
          document.querySelector('#id_referenced_documents').dispatchEvent(event);
        });
      });

    </script>
    {% csrf_token %}
    <div class="jumbotron m-2 text-center bg-conformii-secondary text-black sticky-top">
      <div><strong>Draft: </strong>({{ revision.get_revision_type_display }})</div>
      <div
        class="d-flex justify-content-center"
      >
        <p>
        <div id="justification">
          <strong>Justification: </strong>
          <button
            hx-target="#document-update-modal-content"
            hx-get="{{ update_justification_url }}"
            class="btn shadow-none p-1">
            <i class="text-sm text-black fa-solid fa-pencil"></i>
          </button>
          {{ revision.justification|default:""|truncatechars:100 }}
        </div>
        </p>
      </div>
      <div id="non-tagged-content-paginator-parent"></div>
    </div>

    {% block document_header %}
      <div id="document-header">
        {% include 'document/revision/document/header.html' with revision=revision %}
      </div>
      <div class="row">
        <div class="col-3">
          <div class="p-4" hx-target="#document-update-modal-content">
            <button hx-get="{{ update_heading_url }}" class="btn btn-warning btn-shadow">
              {% translate "Edit Document Header" %}
            </button>
          </div>
        </div>
        <div class="col-9 text-align-end">
          {% if revision.document.published %}
            <div>
              <a href="{{ revision.document.get_absolute_url }}" target="_blank" type="button"
                 class="btn btn-link bg-transparent text-bg-contrast">
                <i class="text-sm fa-solid fa-up-right-from-square"></i>
                Open Document
              </a>
            </div>
          {% endif %}
          <div class="d-flex my-2 justify-content-end">
            <a href="{{ revision.source_url }}" target="_blank" type="button"
               class="btn btn-link bg-transparent text-bg-contrast">
              <i class="text-sm fa-solid fa-share-square"></i>
              Source Document
            </a>
          </div>
          <div id="reference-document-div">
            <button
              class="btn dropdown-toggle text-md text-dark btn-link"
              type="button" data-bs-toggle="collapse" data-bs-target="#referencedDocumentsCollapse"
              aria-expanded="false" aria-controls="referencedDocumentsCollapse"
              id="referenceDocumentDropdown"
            >
              <i class="text-sm fa-solid fa-book"></i>
              Referenced Documents
            </button>
            <div class="row">
              <div class="col">
                <div class="collapse multi-collapse" id="referencedDocumentsCollapse">
                  <div class="card">
                    <div class="card-body">
                      <div class="w-100">
                        {% crispy referenced_documents_form %}
                      </div>
                      <div class="progress referenced-form-progress">
                        <div class="indeterminate"></div>
                      </div>
                      <div class="dropdown-divider"></div>
                      {% include "document/referenced_documents.partial.html" with document=revision %}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    {% endblock document_header %}

    <hr>

    {% block document_body %}
      <div class="row">
        <div class="col" id="document-column">
          <div class="p-4 card" id="document-column-wrapper">
            {% include 'document/layout/draft.html' with layout=root_layout %}
            {% for layout in revision_layouts %}
              {% if layout.draft_fragment_id %}
                {% include 'document/layout/draft.html' %}
              {% else %}
                {% include 'document/layout/draft.html' %}
              {% endif %}
            {% endfor %}
          </div>
          {% comment %} Add space for the footer and target for new layouts {% endcomment %}
          <div id="footer-padding" style="height: 650px; scroll-margin: 200px;"></div>
        </div>
        {% comment %} Side Menu Bulk Tagging {% endcomment %}
        <div class="col-auto mx-4 hidden-element" id="side-column" style="max-width: 350px;">
        </div>
      </div>
    {% endblock %}


    {% block modal %}
      <div id="document-update-modal" class="modal fade">
        <div class="modal-dialog">
          <div class="modal-header">
          <span id="document-update-modal-header-title">
          </span>
            <button id="document-update-modal-close" type="button" class="btn-close" data-bs-dismiss="modal"
                    aria-label="Close"></button>
          </div>
          <div class="modal-content" id="document-update-modal-content" style="height: 500px; overflow-y: scroll;"
               hx-target="this">
          </div>
        </div>
      </div>
      <div class="modal fade" id="tags-modal">
        <div class="modal-dialog modal-fullscreen bg-white">
          <div class="modal-header sticky-top me-2">
            <div class="modal-title" id="tags-modal-title"></div>
            <button id="tags-modal-close" type="button" class="btn-close" data-bs-dismiss="modal"
                    aria-label="Close"></button>
          </div>
          <div class="modal-content" id="tags-modal-content">
          </div>
        </div>
      </div>
    {% endblock %}

    {% block footer %}
      <footer class="fixed-bottom mt-auto py-3 bg-conformii-secondary text-black draft-footer">
        <div class="d-flex justify-content-center">
          <strong class="m-2">
            Status:
          </strong>
          <div>
            {% crispy status_form %}
          </div>
          <strong class="m-2 pe-4">Actions: </strong>
          <div class="btn-group" role="group" aria-label="Document Actions">
            {% if revision.document|user_can_publish_document:request.user %}
              <a
                class="btn btn-conformii-dark btn-md btn-shadow mx-1 rounded"
                hx-post="{% url 'document:revision:publish' revision.id %}"
                hx-target="#revision-container"
                hx-confirm="Are you sure you're ready to publish?{% if revision.document.is_specific_to_tenant %} Your edit will be available to conformii users in search and possibly in their Company Briefcase{% endif %}"
              >
                <i class="text-sm fa-solid fa-check-to-slot"></i>
                Set for Publish
              </a>
            {% endif %}
            {% if revision.document|user_can_tag_document:request.user %}
              <button
                id="bulk-tag-btn"
                class="btn btn-secondary btn-shadow btn-md mx-1"
                type="button"
                hx-target="#side-column"
                hx-vals="js:{elementId: 'side-column', addClass: 'bulk-tag-column', toggleSideColumn: 1, session_key: '{{ session_key }}'}"
                hx-get="{% url 'document:revision:layout-bulk-tag-select' id=revision.id %}"
              >
                <i class="text-sm fa-solid fa-tag"></i>
                Bulk Tag
              </button>
            {% endif %}
          </div>
          <div class="btn-group shadow-none w-auto ms-4" role="group" aria-label="Document Actions">
            <button
              class="btn btn-warning btn-md btn-shadow mx-1 rounded"
              hx-post="{% url 'document:revision:delete' revision.id %}"
              hx-confirm="Are you sure you want to discard this draft?"
              hx-swap="none"
            >
              <i class="text-sm fa-solid fa-trash"></i>
              Discard Draft
            </button>
          </div>
        </div>
      </footer>
    {% endblock %}
  </div>
{% endblock %}
