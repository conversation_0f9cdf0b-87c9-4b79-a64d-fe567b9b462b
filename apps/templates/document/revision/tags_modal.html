<h5 id="tags-modal-title" hx-swap-oob="true">Select Tags</h5>
<form id="tags-selection-form" hx-post="{% url 'document:revision:tags-modal' %}"
      hx-target="#selected-tags"
      hx-swap="outerHTML">
  <div class="modal-body">
    {% csrf_token %}
    <div class="card" style="border: none; padding: 5px;">
      <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 1rem;">
        {% for tag_type in tag_types %}
          <div class="card" style="border: none; padding: 5px;">
            <div class="card-header">
              <h6>
                <i class="align-middle mx-2 {{ tag_type.icon }}" aria-hidden="true"></i>
                <span class="align-middle">{{ tag_type.name }}</span>
              </h6>
            </div>
            <div class="card-body">
              {% for subtag in tag_type.get_children %}
                {% if subtag.get_children.exists %}
                  <div class="form-check">
                    <input type="checkbox" name="tags" value="{{ subtag.id }}"
                           id="id_tags_{{ subtag.id }}" class="form-check-input"
                      {% if subtag.id in selected_tag_ids %} checked {% endif %}>
                    <label for="id_tags_{{ subtag.id }}" class="form-check-label">{{ subtag.name }}</label>
                    <button class="btn btn-link btn-sm m-0 ms-2 p-0" type="button"
                            data-bs-toggle="collapse" data-bs-target="#collapse_{{ tag_type.code }}_{{ subtag.id }}"
                            aria-expanded="false" aria-controls="collapse_{{ tag_type.code }}_{{ subtag.id }}">
                      <i class="fas fa-chevron-down text-xs"></i>
                    </button>
                    <div class="collapse m-1 p-1" id="collapse_{{ tag_type.code }}_{{ subtag.id }}">
                      {% for subsubtag in subtag.get_children %}
                        <div class="form-check">
                          <input type="checkbox" name="tags" value="{{ subsubtag.id }}"
                                 id="id_tags_{{ subsubtag.id }}" class="form-check-input"
                            {% if subsubtag.id in selected_tag_ids %} checked {% endif %}>
                          <label for="id_tags_{{ subsubtag.id }}" class="form-check-label">{{ subsubtag.name }}</label>
                        </div>
                      {% endfor %}
                    </div>
                  </div>
                {% else %}
                  <div class="form-check">
                    <input type="checkbox" name="tags" value="{{ subtag.id }}"
                           id="id_tags_{{ subtag.id }}" class="form-check-input"
                      {% if subtag.id in selected_tag_ids %} checked {% endif %}>
                    <label for="id_tags_{{ subtag.id }}" class="form-check-label">{{ subtag.name }}</label>
                  </div>
                {% endif %}
              {% endfor %}
            </div>
          </div>
        {% endfor %}
      </div>
    </div>
  </div>
  <div class="modal-footer">
    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
    <button class="btn btn-primary" type="submit">Select</button>
  </div>
</form>
