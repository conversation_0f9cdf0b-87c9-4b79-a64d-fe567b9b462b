<div class="d-flex" style="max-height: 30px;" id="diff-paginator">
  <div class="p-1 m-1">
    <h6><strong>Jump to changes</strong></h6>
  </div>
  <nav aria-label="Jump to change" class="p-0 m-0">
    <ul class="pagination">
      <li class="page-item">
        <a class="page-link cursor-pointer" hx-get="{% url "document-diff:scroll" %}" hx-swap="innerHTML" hx-target="#diff-paginator" hx-vals="js:{ layout_id: {{ first_node_id }}, session_key: '{{ session_key }}' }" aria-label="First Change">
          <span aria-hidden="true">&laquo;</span>
        </a>
      </li>
      {% if previous_node_id %}
      <li class="page-item">
        <a class="page-link cursor-pointer" hx-get="{% url "document-diff:scroll" %}" hx-swap="innerHTML" hx-target="#diff-paginator" hx-vals="js:{ layout_id: {{ previous_node_id }}, session_key: '{{ session_key }}' }" aria-label="Previous Change">
          <span aria-hidden="true" class="text-sm">&lt;</span>
        </a>
      </li>
      {% endif %}
      <li class="page-item">
        <span class="page-link" id="change-pagination">{{ layout_index|default:0|add:1 }}/{{ changed_node_ids_length }}</span>
      </li>
      {% if next_node_id %}
      <li class="page-item">
        <a class="page-link cursor-pointer" hx-get="{% url "document-diff:scroll" %}" hx-swap="innerHTML" hx-target="#diff-paginator" hx-vals="js:{ layout_id: {{ next_node_id }}, session_key: '{{ session_key }}' }" aria-label="Next Change">
          <span aria-hidden="true" class="text-sm">&gt;</span>
        </a>
      </li>
      {% endif %}
      <li class="page-item">
        <a class="page-link cursor-pointer" hx-get="{% url "document-diff:scroll" %}" hx-swap="innerHTML" hx-target="#diff-paginator" hx-vals="js:{ layout_id: {{ last_node_id }}, session_key: '{{ session_key }}' }" aria-label="Last Change">
          <span aria-hidden="true">&raquo;</span>
        </a>
      </li>
    </ul>
  </nav>
</div>
{% comment %} <div class="m-2 ps-4 ms-4">
  <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
</div> {% endcomment %}
