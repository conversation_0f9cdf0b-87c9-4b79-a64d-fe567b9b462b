{% extends "email/base.mjml" %}

{% block header %}
<mj-head>
    <mj-preview>A {{ object_type }} you are assigned to was due yesterday.</mj-preview>
    <mj-font name="Montserrat" href="https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;700&display=swap" />
</mj-head>
{% endblock %}

{% block content %}
<mj-wrapper padding="30px" background-color="#f9f9f9" border-radius="5px" css-class="container">
  <mj-section>
    <mj-column>
      <mj-text align="left" color="#2c3e50" font-size="28px" font-weight="700" font-family="Arial, sans-serif">{{ object_type }} Overdue</mj-text>
    </mj-column>
  </mj-section>
  <mj-section>
    <mj-column>
      <mj-text font-family="Arial, sans-serif" line-height="1.6" color="#333333">
        <p>Hello,</p>
        <p>A {{ object_type }} (<a href="{{settings.APP_DOMAIN}}{{ object_url }}" style="font-weight: bold; color: #e74c3c;">{{ object_name }}</a>) that you are assigned to was due yesterday.</p>
      </mj-text>
      <mj-text align="center" padding-top="30px">
        <mj-button background-color="#ecbf45" color="#000000" font-family="Montserrat, sans-serif" font-size="14px" padding="12px 24px" border-radius="3px" border="1px solid #ecbf45" href="{{settings.APP_DOMAIN}}{{ object_url }}" target="_blank" rel="noopener noreferrer">View {{ object_type }}</mj-button>
      </mj-text>
    </mj-column>
  </mj-section>
  <mj-section>
    <mj-column>
      <mj-text align="left" font-size="14px" color="#666666" padding-top="20px" border-top="1px solid #ddd" font-family="Arial, sans-serif">
        <p>Thanks,<br>Conformii Team</p>
      </mj-text>
    </mj-column>
  </mj-section>
</mj-wrapper>
{% endblock %}
