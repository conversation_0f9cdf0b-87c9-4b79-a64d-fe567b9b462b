{% extends "sidenav.html" %}

{% block list-group-parent-attrs %}class="position-sticky"{% endblock %}

{% block list-group %}
  <a
    class="list-group-item list-group-item-action text-bs-primary {% if collapsed %}text-align-start{% else %}text-align-center{% endif %} ripple active"
    href="#"
    id="post-filter-{{ form.UNREAD }}"
    title="{{ form.UNREAD }}"
    data-bs-placement="right"
    onclick="togglePostFilter('{{ form.UNREAD }}')"
  >
    <i class="fa fa-solid fa-eye">
      <span id="post-filter-unread-count"></span>
    </i>
  </a>
  <a
    class="list-group-item list-group-item-action text-bs-primary {% if collapsed %}text-align-start{% else %}text-align-center{% endif %} ripple"
    href="#"
    id="post-filter-{{ form.PINNED }}"
    title="{{ form.PINNED }}"
    data-bs-placement="right"
    onclick="togglePostFilter('{{ form.PINNED }}')"
  >
    <i class="fa fa-solid fa-thumbtack">
      <span id="post-filter-pinned-count"></span>
    </i>
  </a>
  <a
    class="list-group-item list-group-item-action text-bs-primary {% if collapsed %}text-align-start{% else %}text-align-center{% endif %} ripple"
    href="#"
    id="post-filter-{{ form.SHOW_ALL }}"
    title="{{ form.SHOW_ALL }}"
    data-bs-placement="right"
    onclick="togglePostFilter('{{ form.SHOW_ALL }}')"
  >
    <i class="fa fa-solid fa-inbox">
      <span id="post-filter-all-count"></span>
    </i>
  </a>
{% endblock %}
