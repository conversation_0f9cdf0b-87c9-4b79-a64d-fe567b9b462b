{% load static i18n crispy_forms_tags %}

<script>
  document.body.addEventListener("switchTheme", function (event) {
    let rootHtml = document.getElementById("root-html")
    if (rootHtml) {
      let detail = event.detail
      if (detail.newTheme) {
        rootHtml.setAttribute("data-bs-theme", detail.newTheme)
      }
    }
  });
</script>

<nav class="navbar navbar-expand-xl py-2 position-sticky" style="top: 0px; z-index: 1050;">
  <div class="container-fluid">
    <a class="navbar-brand d-flex align-items-center m-0 p-0" href="{% url 'home' %}">
      {% include 'navbar-icon.html' %}
    </a>

    <button class="navbar-toggler border-0" type="button" data-bs-toggle="collapse"
            data-bs-target="#navbarSupportedContent"
            aria-controls="navbarSupportedContent" aria-expanded="false" aria-label="{% trans 'Toggle navigation' %}">
      <i class="fa-solid fa-bars" style="color: white !important;"></i>
    </button>

    <div class="collapse navbar-collapse" id="navbarSupportedContent">
      <ul class="navbar-nav ms-4 me-auto mb-2 mb-lg-0">
        <li class="nav-item">
          <a class="nav-link text-nowrap {{ request|nav_link_is_active:'general/news' }}"
             aria-current="page"
             href="{% url 'general:news_post:feed' %}"
             {% if not perms.general.view_newspost %}disabled{% endif %}>
            News Feed
          </a>
        </li>
        <li class="nav-item">
          <a class="nav-link text-nowrap {{ request|nav_link_is_active:'search/library' }}"
             href="{% url 'search:library:search' %}"
             {% if not perms.document.view_document %}disabled{% endif %}>Library</a>
        </li>
        <li class="nav-item">
          <a class="nav-link text-nowrap {{ request|nav_link_is_active:'briefcase/' }}"
             href="{% url 'briefcase:index' %}"
            {% if not perms.briefcase.view_applicablelegislation and not perms.calendar.view_calendareventoccurrence and not perms.briefcase.view_correctiveactionplan and not perms.briefcase.view_applicabilityreview and not perms.history.view_history %}
             disabled
            {% endif %}
          >Company
            Briefcase</a>
        </li>
        <li class="nav-item">
          <a class="nav-link text-nowrap {{ request|nav_link_is_active:'reports/' }}"
             href="{% url 'reports:index' %}"
             {% if not perms.briefcase.view_assessment_reports and not perms.briefcase.view_applicability_review_reports %}disabled{% endif %}>Reports</a>
        </li>
      </ul>

      <div class="d-flex align-items-center">
        {% if request.user and request.user.is_authenticated %}
          <a
            class="nav-link d-flex p-1 align-items-center {{ request|nav_link_is_active:request.user.get_absolute_url }}"
            href="{% url 'users:update' request.user.pk %}"
            title="View User Profile"
          >
            <i class="fa-solid fa-user me-2"></i>
            <span class="text-nowrap">{{ request.user.name|truncatechars:50 }}</span>
          </a>

          <div class="vr text-light my-2 mx-1"></div>

          {% include "user_task_list.html" %}

          <a href="{% url 'contact' %}" class="btn btn-sm nav-link px-2 mx-1" title="Contact Support">
            <i class="fa-solid fa-phone"></i>
          </a>

          {% if help_page_url %}
            {% include "page_help_button.html" %}
          {% endif %}

          <div class="dropdown" title="Settings">
            <button type="button" class="btn btn-sm nav-link px-2 mx-1" id="navbarDropdown" data-bs-toggle="dropdown"
                    aria-expanded="false">
              <i class="fa-solid fa-gear"></i>
            </button>
            <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="navbarDropdown"
                style="margin-top: 10px; z-index: 2000;">
              <li class="text-center"><small>{{ request.user.name }}</small></li>
              <li class="text-center"><small>{{ request.user.email }}</small></li>
              <li class="p-3">
                {% if request.user.tenants.count > 1 %}
                  <form name="change_tenant_form" class="form" action="{% url 'users:set_tenant' user.id %}"
                        method="POST">
                    {% csrf_token %}
                    {% crispy change_tenant_form %}
                  </form>
                {% else %}
                  <span class="fw-normal">{{ request.user.tenant.name }}</span>
                {% endif %}
              </li>
              <li><a class="dropdown-item"
                     href="{% url 'tenants:configuration' %}">{% trans 'Company Configuration' %}</a></li>
              <li>
                <hr class="dropdown-divider">
              </li>

              {% if request.user.is_staff %}
                <li><a class="dropdown-item"
                       href="{% url 'search:library:browse-archived' %}">{% trans 'Archive' %}</a></li>
                <li><a class="dropdown-item" href="{% url 'admin:index' %}">{% trans 'Administration Page' %}</a></li>
              {% endif %}

              {% if request.user.is_host_tenant_user %}
                <li><a
                  class="dropdown-item {% if not perms.document.add_document and not perms.document.change_document %}disabled{% endif %}"
                  href="{% url 'document:revision:list' %}">{% trans 'Legislation Drafts' %}</a></li>
                <li><a
                  class="dropdown-item {% if not perms.general.add_newspost and not perms.general.change_newspost %}disabled{% endif %}"
                  href="{% url 'general:news_post:list' %}">{% trans 'News Post Updates' %}</a></li>
              {% endif %}

              <li><a class="dropdown-item" href="{% url 'users:email-settings' %}">{% trans 'Email Settings' %}</a>
              </li>
              <li><a class="dropdown-item" href="{% url 'attachments:list' %}">{% trans 'File Exports' %}</a>
              </li>
              <li>
                <hr class="dropdown-divider">
              </li>
              <li class="text-center">
                <a class="btn btn-link" href="{% url 'releases' %}" target="_blank">
                  <strong>Version: {{ APP_VERSION }}</strong>
                  <i class="fa-solid fa-circle-question"></i>
                </a>
              </li>
              <li>
                <hr class="dropdown-divider">
              </li>
              <li><a class="dropdown-item" href="{% url 'about' %}" target="_blank">{% trans 'About' %}</a></li>
              <li><a class="dropdown-item" href="{% url 'copyright' %}">Document Copyrights</a></li>
              <li><a class="dropdown-item" href="{% url 'account_logout' %}">{% trans 'Sign Out' %}</a></li>
            </ul>
          </div>
        {% endif %}
      </div>
    </div>
  </div>
</nav>
