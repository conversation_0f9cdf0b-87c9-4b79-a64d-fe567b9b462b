{% extends 'common/list.html' %}
{% load django_tables2 crispy_forms_tags i18n %}

{% block css %}
  {{ block.super }}
  {{ form.media.css }}
{% endblock %}

{% block javascript %}
  {{ block.super }}
  {{ form.media.js }}
{% endblock %}

{% block list-header %}
  <div class="row p-1">
    <div class="col-12">
      <h3>{% block title_header %}{{ form.title }}{% endblock %}</h3>
    </div>
    <div class="col-12">
      <div {% block list-search-attributes %}class="list-search" {% endblock %}>
        <form
          id="library-browse-form"
          href="{{ submit_url }}"
          method="get"
        >
          <div class="row pt-2">
            <div class="col-2">
              {{ form.query | as_crispy_field }}
            </div>
            <div class="col-3">
              {{ form.jurisdictions | as_crispy_field }}
            </div>
            <div class="col-2">
              {{ form.document_type | as_crispy_field }}
            </div>
            <div class="col-3">
              {{ form.published_date | as_crispy_field }}
            </div>
            <div class="col-1">
              <button class="btn btn-sm input-group-text p-2 btn-shadow btn-secondary">
                <i class="align-middle mx-2 fas fa-search"></i>
              </button>
            </div>
            <div class="col-1">
              {% include "common/export_formats.html" with export_permission=perms.document.export_document export_urls=export_urls export_button_class="btn-sm btn-shadow btn-warning mx-2" %}
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>
  {% block list-loading %}
    <div class="progress">
      <div class="indeterminate"></div>
    </div>
  {% endblock %}
{% endblock %}



{% block modal %}
  {{ block.super }}
  {% include "notifications/modal.html" %}
{% endblock %}


{% block list-results-attributes %}
  class="list-results"
  id="browse-documents-results"
{% endblock %}

{% block navbar %}
  {% include "navbar.html" with help_page_url="https://conformii.doc.vantedge.com/Guide/3-library/" %}
{% endblock %}
