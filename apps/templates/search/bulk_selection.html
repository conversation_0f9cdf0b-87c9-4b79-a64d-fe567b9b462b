{% block content %}
  {% if selected|length > 0 %}
    <footer class="fixed-bottom mt-auto py-3 bg-conformii-secondary text-black search-footer"
            id="search-footer-selection">
      <div class="d-flex justify-content-center">
        <div class="spinner-border spinner-border-sm text-navbar-color my-2" role="status"
             id="footer-selector-loader"></div>
        <strong class="m-2">
          {{ selected|length }} Selected
        </strong>
        <button
          id="add-to-briefcase-btn"
          class="btn btn-secondary btn-shadow btn-md mx-2"
          type="button"
          {% if perms.briefcase.add_applicablelegislation %}
          hx-headers='{"X-CSRFToken": "{{ csrf_token }}"}'
          hx-target="#briefcase-toggle-modal-content"
          hx-vals="js:{action:'add', session_key: '{{ session_key }}', render_layouts_on_submit: 1}"
          hx-get="{% url 'briefcase:toggle_briefcase' %}"
          hx-indicator="#footer-selector-loader"
          hx-swap="innerHTML"
          {% else %}
          disabled
          {% endif %}
        >
          <i class="text-sm conformii-dark fa-solid fa-circle-plus"></i>
          Add To Briefcase
        </button>
        <button
          id="remove-from-briefcase-btn"
          class="btn btn-secondary btn-shadow btn-md mx-2"
          type="button"
          {% if perms.briefcase.delete_applicablelegislation %}
          hx-headers='{"X-CSRFToken": "{{ csrf_token }}"}'
          hx-target="#briefcase-toggle-modal-content"
          hx-vals="js:{action:'remove', session_key: '{{ session_key }}', render_layouts_on_submit: 1}"
          hx-get="{% url 'briefcase:toggle_briefcase' %}"
          hx-indicator="#footer-selector-loader"
          hx-swap="innerHTML"
          {% else %}
          disabled
          {% endif %}
        >
          <i class="text-sm text-bs-red fa-solid fa-circle-minus"></i>
          Remove from Briefcase
        </button>
        <div
          {% assessment_permission_tooltip %}
        >
          <button
            id="bulk-assess-btn"
            class="btn btn-secondary btn-shadow btn-md mx-2"
            type="button"
            {% if perms.briefcase.change_applicablelegislationassessment %}
            hx-get="{% url 'briefcase:bulk-assessment' %}"
            hx-target="#assessment-modal-content"
            hx-trigger="click"
            hx-vals='js:{is_modal_popup:1, query:"{{ query }}", session_key: "{{ session_key }}", from_library:1}'
            hx-swap="innerHTML"
            hx-indicator="#footer-selector-loader"
            {% else %}
            disabled
            {% endif %}
          >
            <i class="text-sm conformii-dark fa-solid fa-shield-halved ve-icon-badge-parent"></i>
            Bulk Assessment
          </button>
        </div>
        <button
          id="unselect-all-btn"
          class="btn btn-secondary btn-shadow btn-md mx-2"
          type="button"
          hx-headers='{"X-CSRFToken": "{{ csrf_token }}"}'
          hx-target="#search-footer-selection"
          hx-vals="js:{toggle: 0, session_key: '{{ session_key }}'}"
          hx-get="{% url 'search:library:selection' %}"
          hx-indicator="#footer-selector-loader"
          hx-swap="outerHTML"
        >
          <i class="text-sm text-grey fa-solid fa-circle-xmark"></i>
          Unselect All
        </button>

      </div>
    </footer>
  {% endif %}
{% endblock %}
