{% extends "search/jurisdiction_list.html" %}
{% load i18n %}

{% block list-navigation %}
  <div class="btn-group w-100" role="group" aria-label="Change results view">
    <a class="btn btn-shadow btn-link btn-outline-light" type="button" href="{{ hierarchy_view_url }}">
      <input
        type="radio"
        class="btn-check btn-view"
        id="hierarchyviewbtn"
      >
      <label for="hierarchyviewbtn">
        <i class="align-middle fa fa-indent"></i>
        Document Hierarchy
      </label>
    </a>

    <input
      id="clauseviewbtn"
      type="radio"
      class="btn-check btn-view"
      checked
    >
    <label
      class="btn shadow btn-outline-light"
      for="clauseviewbtn"
    >
      <i class="align-middle fa fa-list"></i>
      Clause List
    </label>
  </div>
{% endblock %}

{% block list-results %}
  <div class="container">
    <br>
    {% include 'common/paginator.html' with show_count=True %}
    {% for layout in page_obj %}
      <div
        id="layout-{{ layout.id }}"
        class="row p-4 text-sm border-top">
        <div class="col-auto px-0" id="layout-{{ layout.id }}-actions">
          {% include "document/layout/actions.html" %}
        </div>
        <div class="col document-content ps-2 pe-5" id="layout-{{ layout.id }}-content">
          {% include "document/fragment/render.html" %}
        </div>
        <div class="col-1" id="layout-{{ layout.id }}-links">
          {% include "document/layout/links.html" %}
        </div>
      </div>
      <div class="row p-2 ps-0" id="layout-{{ layout.id }}-tags"></div>
      <div id="layout-{{ layout.id }}-path"
           hx-trigger="intersect once"
           hx-get="{% url 'document:current:layout-path' layout.id %}"
           hx-swap="outerHTML"></div>
    {% endfor %}
    {% if page_obj|length == 0 %}
      <div class="row text-align-center">
        <div class="col">
          <div class="alert alert-warning" role="alert">
            No clauses were found matching the search criteria
          </div>
        </div>
      </div>
    {% endif %}
    {% include 'common/paginator.html' with show_count=True %}
  </div>
{% endblock %}
