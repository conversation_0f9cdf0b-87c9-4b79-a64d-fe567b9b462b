{% load static %}

<div class="accordion accordion-flush" id="jurisdiction-result-accordion">
  {% for result in object_list %}
    <div class="accordion-item my-1">
      <h2 class="accordion-header">
        <button
          class="accordion-button collapsed right-align-accordion-btn"
          type="button"
          data-bs-toggle="collapse"
          data-bs-target="#jurisdiction-result-{{ result.jurisdiction_id }}"
          aria-expanded="false"
          aria-controls="jurisdiction-result-{{ result.jurisdiction_id }}"
          {% block accordion_btn_attributes %}
          id="jurisdiction-{{ result.jurisdiction_id }}-accordion-btn"
          hx-get="{% jurisdiction_document_list_with_query 'search:library:document-list' result.jurisdiction_id %}"
          hx-vals="js:{session_key: '{{ session_key }}'}"
          hx-target="#jurisdiction-result-{{ result.jurisdiction_id }}"
          hx-swap="innerHTML"
          hx-indicator='#jurisdiction-result-{{ result.jurisdiction_id }}-loader'
          hx-trigger="click once"
          class="accordion-button collapsed right-align-accordion-btn"
          {% endblock %}
        >
          {% block accordion_header_html %}
            <div class="d-flex flex-column flex-md-row justify-content-between w-100">
            <span class="fw-lighter">
              {{ result.jurisdiction_name }}
              <span class="fst-italic">
                ({{ result.annotation.0.document_ids|length }} documents)
              </span>
            </span>
            </div>
          {% endblock %}
        </button>
        {% block document_actions %}
        {% endblock %}
      </h2>
      {% block accordion_loader %}
        <div id="jurisdiction-result-{{ result.jurisdiction_id }}"
             class="accordion-collapse collapse justify-content-center {% block jurisdiction_accordion_attributes %} {% if jurisdiction_id|slugify == result.jurisdiction_id|slugify %}show{% endif %}{% endblock %}">
          <div class="spinner-border spinner-border-sm text-navbar-color position-relative my-2" role="status"
               style="left: 50%;" id="jurisdiction-result-{{ result.jurisdiction_id }}-loader"></div>
          <span class="spinner-text text-navbar-color text-center">Fetching Matching Results...</span>
        </div>
      {% endblock %}
    </div>
  {% endfor %}
</div>
