{% extends "base.html" %}
{% load static crispy_forms_tags %}

{% block javascript %}
  {{ block.super }}
  <script type="text/javascript" src="{% static 'htmx/checkbox.js' %}"></script>
{% endblock %}

{% block content %}
  {{ block.super }}
  <div class="container-fluid">
    <div class="row justify-content-center">
      <div class="col-8 border-bottom">
        <figure>
          <blockquote class="blockquote">
            <h4>{{ filter.title }}</h4>
          </blockquote>
          {% if filter.instructions %}
            <figcaption class="blockquote-footer">
              {{ filter.instructions }}
            </figcaption>
          {% endif %}
        </figure>
      </div>
      <div class="col-4 border-bottom">
        {% block list-actions %}
          <div class="d-flex my-2 justify-content-end">
            <div role="group" aria-label="Search Actions">
              {% include "common/export_formats.html" with export_permission=perms.document.export_document export_urls=export_urls export_button_class="btn-sm btn-shadow btn-warning" %}
              {% if perms.document.add_document %}
                <a href="{% url 'document:revision:create' %}" class="btn btn-sm btn-shadow btn-warning">
                  New Document
                </a>
              {% endif %}
              <a href="{% url 'search:library:browse' %}" class="btn btn-sm btn-shadow btn-secondary">
                Browse Legislation
              </a>

            </div>
          </div>
        {% endblock %}
      </div>
    </div>

    {% include 'search/filters.html' %}

    {% block list-navigation %}
      <div class="btn-group w-100" role="group" aria-label="Change results view">
        <input
          type="radio"
          class="btn-check btn-view"
          id="hierarchyviewbtn"
          autocomplete="off"
          checked
        >
        <label class="btn shadow btn-outline-light" for="hierarchyviewbtn">
          <i class="align-middle fa fa-indent"></i>
          Document Hierarchy
        </label>

        <a class="btn btn-shadow btn-link btn-outline-light" type="button" href="{{ clause_view_url }}">
          <input
            id="clauseviewbtn"
            type="radio"
            class="btn-check btn-view"
            autocomplete="off"
          >
          <label for="clauseviewbtn">
            <i class="align-middle fa fa-list"></i>
            Clause List
          </label>
        </a>
      </div>
    {% endblock %}

    {% block list-results %}
      <div class="row pt-2 justify-content-center">
        <div class="col-12 tab-content">
          <div id="view-container" class="list-results">
            {% if object_list %}
              {% block hierarchy-list-results %}
                {% include 'search/hierarchy_view.html' %}
              {% endblock %}
            {% else %}
              <div class="row text-align-center">
                <div class="col">
                  <div class="alert alert-warning" role="alert">
                    No documents were found matching the search criteria.
                  </div>
                </div>
              </div>
            {% endif %}
          </div>
        </div>
      </div>
    {% endblock %}

    {% block footer %}
      <div id="selection-footer-parent">
        {% include "search/bulk_selection.html" %}
      </div>
    {% endblock %}

    {% block modal %}
      {% include "briefcase/assessment_modal.html" %}

      {% include "search/search_modal.html" %}

      {% include "briefcase/toggle_modal.html" %}

      {% include "briefcase/applicability_review/modal.html" %}

      {% include "notifications/modal.html" %}
    {% endblock %}

  </div>
{% endblock %}

{% block navbar %}
  {% include "navbar.html" with help_page_url="https://conformii.doc.vantedge.com/Guide/3-library/" %}
{% endblock %}

