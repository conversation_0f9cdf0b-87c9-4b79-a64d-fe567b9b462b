{% extends "common/htmx_form.html" %}
{% load i18n %}

{% block parentelement_attributes %}
  id="user-password-form-parent"
{% endblock %}

{% block form_parent_attributes %}
  class="row"
{% endblock %}

{% block page_header %}
  <div id="user-password-form-errors"></div>
{% endblock %}

{% block form_action %}
  id="user-password-form"
  method="POST"
  action="{% url 'users:set_password' user.id %}"
  hx-post="{% url 'users:set_password' user.id %}"
  hx-target="#user-password-form-parent"
  hx-indicator="#user-password-form-spinner-border"
  data-loading="block"
{% endblock %}

{% block form_class %}
  row m-1 py-1
{% endblock %}

{% block form_header %}
  <br/>

  <span id="password-modal-header-title" hx-swap-oob="true">
  <h4>{% translate "Set Password" %}</h4>
</span>

{% endblock %}

{% block form_footer %}
  <div class="d-flex justify-content-end text-end">
    <style>
      .spinner-border {
        display: none !important;
      }

      form ~ .user-password-form-actions {
        display: block !important;
      }

      .htmx-request.spinner-border ~ .user-password-form-actions {
        display: none !important;
      }

      .htmx-request.spinner-border {
        display: block !important;
      }
    </style>
    <div class="spinner-border spinner-border-sm text-navbar-color my-2" role="status"
         id="user-password-form-spinner-border"></div>
    <div class="user-password-form-actions d-flex my-2 pt-2">
      <div class="col m-2">
        <button type="submit" form="user-password-form" class="btn btn-warning btn-shadow save">
          <i class="fa-solid fa-check"></i>
        </button>
        <button type="button" data-bs-dismiss="modal" class="btn btn-default btn-shadow cancel">
          <i class="fa-solid fa-remove"></i>
        </button>
      </div>
    </div>
  </div>
{% endblock %}
