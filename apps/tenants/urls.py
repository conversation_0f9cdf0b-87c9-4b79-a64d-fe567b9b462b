from django.urls import path, include

from .views import (
    tenant_detail_view,
    tenant_redirect_view,
    tenant_update_view,
    TenantConfigurationView,
)

app_name = "tenants"
urlpatterns = [
    path("~redirect/", view=tenant_redirect_view, name="redirect"),
    path("~update/", view=tenant_update_view, name="update"),
    path("<int:id>/", view=tenant_detail_view, name="detail"),
    path("configuration/", view=TenantConfigurationView.as_view(), name="configuration"),
]
