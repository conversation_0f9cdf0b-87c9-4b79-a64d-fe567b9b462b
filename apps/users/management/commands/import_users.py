import logging
from collections import OrderedDict

from django.contrib.auth import get_user_model
from django.contrib.auth.models import Group
from django.core.management.base import BaseCommand
from progressbar import progressbar

from apps.complishield import models as complishield
from apps.document.models import Jurisdiction
from apps.document.models import JurisdictionTag
from apps.document.models import Tag
from apps.tenants.models import Tenant

logger = logging.getLogger(__name__)

DEFAULT_TENANT_NAME = "VantEdge"
BATCH_SIZE = 2000

User = get_user_model()


class Command(BaseCommand):
    help = "Imports tenants and users from legacy db"

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.populate_tenants()
        self.populate_jurisdictions()
        self.populate_users()
        self.populate_tags()

    def populate_tenants(self):
        self.tenants = {
            tenant.code: tenant
            for tenant in Tenant.objects.all()
            if tenant.code.isdigit()
        }

    def populate_jurisdictions(self):
        self.jurisdictions = {
            jurisdiction.name: jurisdiction
            for jurisdiction in Jurisdiction.objects.all()
        }

    def populate_users(self):
        self.users = {user.email: user for user in User.objects.all()}

    def populate_tags(self):
        self.tags = OrderedDict()
        root_tags_qs = Tag.objects.filter(parent=None)
        for root in root_tags_qs:
            tag_info = {"tag_type": root, "descendants": {}}
            for tag in root.descendants(include_self=True):
                tag_info["descendants"][tag.name.lower()] = tag
            self.tags[root.name.lower()] = tag_info

    def handle(self, *args, **options):
        # Call the import_tenants and import_users functions here
        self.import_tenants()
        self.import_users()

    def import_tenants(self):
        logger.info("Importing complishield tenants...")

        complishield_tenants = complishield.Company.objects.all()
        complishield_tenant_ids = list(
            complishield_tenants.values_list("id", flat=True),
        )
        migrated_tenants = Tenant.objects.filter(
            code__in=complishield_tenant_ids,
        )

        create_tenants = []
        update_tenants = []

        for company in progressbar(complishield_tenants):
            tenant = self.tenants.get(str(company.id))
            if not tenant:
                tenant = Tenant(
                    name=company.name,
                    code=company.id,
                    address=company.address,
                    website=company.website,
                )
                create_tenants.append(tenant)
                self.tenants[str(company.id)] = tenant
            else:
                # keep VantEdge tenant
                if tenant.name != DEFAULT_TENANT_NAME:
                    tenant.name = company.name
                tenant.address = company.address
                tenant.website = company.website
                update_tenants.append(tenant)

        if update_tenants:
            logger.info(f"Bulk updating {len(update_tenants)} tenants...")
            Tenant.objects.bulk_update(
                update_tenants,
                ["name", "address", "website"],
                BATCH_SIZE,
            )

        if create_tenants:
            logger.info(f"Bulk creating {len(create_tenants)} tenants...")
            Tenant.objects.bulk_create(create_tenants, BATCH_SIZE)

        self.populate_tenants()

        assert (
            migrated_tenants.count() == complishield_tenants.count()
        ), f"The number of migrated tenants ({migrated_tenants.count()}) does not match the complishield tenant count ({complishield_tenants.count()})."

        logger.info(
            f"{migrated_tenants.count()}/{complishield_tenants.count()} tenants migrated.",
        )

        # TODO Import Jurisdictions, Company Attributes, and Permissions
        complishield_tenants = {
            str(tenant.id): tenant for tenant in complishield_tenants
        }

        logger.info("Importing Jurisdictions for Tenants...")
        complishield_jurisdictions = complishield.Jurisdiction.objects.all()

        for tenant_code, tenant in progressbar(self.tenants.items()):
            complishield_tenant = complishield_tenants[str(tenant_code)]
            for cj in complishield_tenant.companyjurisdiction_set.all():
                jurisdiction = self.jurisdictions[cj.jurisdictionid.name]
                tenant.jurisdictions.add(jurisdiction)

        logger.info("Importing Company Attributes...")

        asset_tags = Tag.objects.get(name="Asset Type").children.all()

        for tenant_code, tenant in progressbar(self.tenants.items()):
            attributes = []
            if tenant.name == DEFAULT_TENANT_NAME:
                # activate all tenant attributes
                for jurisdiction_name, jurisdiction in self.jurisdictions.items():
                    for tag in asset_tags:
                        jtag, created = JurisdictionTag.objects.get_or_create(
                            tag=tag,
                            jurisdiction=jurisdiction,
                        )
                        attributes.append(jtag)
            else:
                complishield_tenant = complishield_tenants[str(tenant_code)]
                for attribute in complishield_tenant.companyattribute_set.all():
                    root_tag_name = attribute.tagid.tagtypeid.name.lower()
                    tag_name = attribute.tagid.name.lower().strip()
                    root_tag = self.tags[root_tag_name]

                    tag = root_tag["descendants"][tag_name]
                    jurisdiction = self.jurisdictions[attribute.jurisdictionid.name]
                    jtag, created = JurisdictionTag.objects.get_or_create(
                        tag=tag,
                        jurisdiction=jurisdiction,
                    )
                    attributes.append(jtag)

            tenant._skip_applicability_review_trigger = True
            tenant.attributes.add(*attributes)

        logger.info("Set all the tenants for superusers...")
        for user in User.objects.filter(is_superuser=True):
            user.tenants.add(*self.tenants.values())

    def import_users(self):
        logger.info("Generating groups and permissions...")
        logger.info("Importing complishield users...")

        complishield_users = complishield.Aspnetusers.objects.prefetch_related(
            "aspnetuserroles_set",
        ).all()
        complishield_user_emails = list(
            complishield_users.values_list("email", flat=True).distinct(),
        )
        complishield_user_email_to_id = {
            complishield_user.email: complishield_user.id
            for complishield_user in complishield_users
        }
        migrated_users = User.objects.filter(email__in=complishield_user_emails)

        # used to lookup by email
        self.populate_users()
        create_users = []
        update_users = []
        user_tenants = {}
        user_groups = {}

        tenant_admins = {}

        groups = {group.name: group for group in Group.objects.all()}

        administrator, admin_created = Group.objects.get_or_create(name="Administrator")
        administrator, admin_created = Group.objects.get_or_create(name="Administrator")

        for cu in progressbar(complishield_users):
            tenant = self.tenants.get(str(cu.companyid_id))

            user = self.users.get(cu.email)
            user_group_names = cu.aspnetuserroles_set.values(
                "roleid__displayname",
                "roleid__hostonly",
            )

            user_group_instances = []
            for info in user_group_names:
                group_name = info["roleid__displayname"]
                hostonly = info["roleid__hostonly"]
                normalize_group_name = str(group_name)
                if group_name == "Admin":
                    normalize_group_name = "Administrator"
                elif group_name == "Sales":
                    if hostonly:
                        normalize_group_name = "Sales Demo"
                    else:
                        normalize_group_name = "Sales"
                elif group_name == "Analyst":
                    normalize_group_name = "Data Analyst"
                elif group_name in ["Edit Draft", "Population"]:
                    normalize_group_name = "Data Entry"
                elif group_name == "Calendar Only":
                    normalize_group_name = "Calendar Manager"

                if normalize_group_name in groups:
                    user_group_instances.append(groups[normalize_group_name])

            if not user:
                user = User(
                    email=cu.email,
                    name=cu.fullname,
                    title=cu.title,
                    phone=cu.phonenumber,
                    is_active=cu.isactive,
                    is_staff=cu.lockoutenabled,
                    lockout_end_date=cu.lockoutenddateutc,
                    tenant=tenant,
                    accepted_terms=cu.agreementacceptedutc != None,
                    last_login=cu.lastloggedinutc,
                )
                create_users.append(user)
                self.users[cu.email] = user
            else:
                user.name = cu.fullname
                user.title = cu.title
                user.phone = cu.phonenumber
                user.is_active = cu.isactive
                user.lockout_end_date = cu.lockoutenddateutc
                user.tenant = tenant
                user.accepted_terms = cu.agreementacceptedutc != None
                user.last_login = cu.lastloggedinutc
                update_users.append(user)

            user_groups.setdefault(cu.email, user_group_instances)
            user_tenants.setdefault(cu.email, [])
            if tenant.id not in user_tenants[cu.email]:
                user_tenants[cu.email].append(tenant.id)

            if (
                cu.companyid.primaryuserid_id
                and cu.companyid.primaryuserid.email == cu.email
            ):
                tenant_admins[cu.email] = tenant

        if create_users:
            logger.info(f"Bulk creating {len(create_users)} users...")
            User.objects.bulk_create(create_users, BATCH_SIZE)

        if update_users:
            logger.info(f"Bulk updating {len(update_users)} users...")
            User.objects.bulk_update(
                update_users,
                [
                    "name",
                    "title",
                    "phone",
                    "is_active",
                    "is_staff",
                    "lockout_end_date",
                    "tenant",
                    "accepted_terms",
                    "last_login",
                ],
                BATCH_SIZE,
            )

        # re-evaluate queryset
        migrated_users = User.objects.filter(email__in=complishield_user_emails)

        # used to lookup users by complishield id
        self.populate_users()

        assert (
            migrated_users.count() == complishield_users.count()
        ), "The number of migrated users does not match the complishield user count."

        logger.info(
            f"{migrated_users.count()}/{complishield_users.count()} users migrated.",
        )

        logger.info("Assigning tenants to users...")
        for user_email, tenant_ids in user_tenants.items():
            user = self.users[user_email]

            # do not set the tenants if user is superuser. Already handle in the preload
            if not user.is_superuser:
                user.tenants.set(tenant_ids)

            # assign administrator role to primary user from complishield
            admin_for_tenant = tenant_admins.get(user.email)
            if admin_for_tenant:
                user.groups.add(administrator)
                admin_for_tenant.primary_contact = user
                admin_for_tenant.save()

        logger.info("Assigning groups to users...")
        for user_email, groups in user_groups.items():
            user = self.users[user_email]
            for group in groups:
                user.groups.add(group)
