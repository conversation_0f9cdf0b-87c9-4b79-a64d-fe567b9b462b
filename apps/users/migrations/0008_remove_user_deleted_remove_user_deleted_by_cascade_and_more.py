# Generated by Django 5.0.3 on 2025-01-15 17:22

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('tenants', '0005_alter_tenant_options'),
        ('users', '0007_alter_user_programs'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='user',
            name='deleted',
        ),
        migrations.RemoveField(
            model_name='user',
            name='deleted_by_cascade',
        ),
        migrations.AlterField(
            model_name='user',
            name='tenant',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='%(class)s_set', to='tenants.tenant'),
        ),
    ]
