from urllib.parse import parse_qs
from urllib.parse import urlparse

from constance import config
from django.http.request import QueryDict


def encode_url_get_params(query_dict: QueryDict, exclude_fields=["page"]):

    for field in exclude_fields:
        if field in query_dict:
            del query_dict[field]

    return query_dict.urlencode()


class HTMXPaginationMixin:
    """ Mixin for setting up pagination with filters and HTMX"""

    htmx_pagination_target_element = ".list-results"
    htmx_pagination_submit_url = None
    htmx_pagination_swap = "innerHTML"
    htmx_pagination_indicator = ".progress"

    @property
    def pagination_per_page(self):
        return config.PAGINATION_PER_PAGE

    def get_htmx_pagination_target(self):
        return self.htmx_pagination_target_element

    def get_htmx_pagination_submit_url(self):
        return self.htmx_pagination_submit_url

    def get_htmx_pagination_swap(self):
        return self.htmx_pagination_swap

    def get_htmx_pagination_indicator(self):
        return self.htmx_pagination_indicator

    def get_htmx_pagination_operation(self):
        if self.request.method == "GET":
            return ("pagination_hx_get", self.get_htmx_pagination_submit_url())
        else:
            return ("pagination_hx_post", self.get_htmx_pagination_submit_url())

    def get_url_query_params(self):
        if self.request.method == "GET":
            params = self.request.GET.copy()
        else:
            params = self.request.POST.copy()
        return params

    def get_page(self):
        # check url params or full path
        url_params = self.get_url_query_params()
        parsed_url = urlparse(self.request.get_full_path())
        query_params = parse_qs(parsed_url.query)

        page = None
        if "page" in url_params:
            page = url_params.get("page")
        elif "page" in query_params:
            page = query_params.get("page")

        if isinstance(page, list):
            page = int(page[0])

        return page

    def get_htmx_pagination_kwargs(self):
        htmx_kwargs = {}
        htmx_operation, htmx_operation_url = self.get_htmx_pagination_operation()
        htmx_kwargs[htmx_operation] = htmx_operation_url
        htmx_kwargs["pagination_hx_target"] = self.get_htmx_pagination_target()
        htmx_kwargs["pagination_hx_swap"] = self.get_htmx_pagination_swap()
        htmx_kwargs["pagination_hx_indicator"] = self.get_htmx_pagination_indicator()
        htmx_kwargs["page_number"] = self.get_page()
        return htmx_kwargs

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        htmx_pagination = self.get_htmx_pagination_kwargs()
        context["pagination_query"] = encode_url_get_params(self.get_url_query_params())
        context["htmx_pagination"] = htmx_pagination
        return context
