pool: "Wheelhouse Build"

# Uncomment if you want this pipeline to trigger after Testing Pipeline completes
# resources:
#   pipelines:
#   - pipeline: Backend_Staging
#     source: Testing-Pipeline
#     trigger:
#       branches:
#         include:
#         - staging

steps:
  # - script: |
  #    docker stop $(docker ps -a -q) 2>&1
  #    docker rm $(docker ps -a -q) 2>&1
  #    exit 0
  #   displayName: 'Remove old docker containers'
  #   continueOnError: true
  - task: DockerCompose@0
    displayName: "Build services"
    inputs:
      containerregistrytype: "Container Registry"
      dockerRegistryEndpoint: "Wheelhouse Registry"
      dockerComposePath: '/usr/local/bin/docker-compose'
      dockerComposeFile: buildscripts/pipelines/$(build.sourceBranchName).yml
      additionalDockerComposeFiles: $(build yml)
      action: "Build services"
      additionalImageTags: $(build.sourceBranchName)
  - task: DockerCompose@0
    displayName: "Push services"
    inputs:
      containerregistrytype: "Container Registry"
      dockerRegistryEndpoint: "Wheelhouse Registry"
      azureSubscription: 'Azure subscription 1(23057013-ba68-42b8-9ff2-5153ceb0b676)'
      azureContainerRegistry: '{"loginServer":"wheelhouse.azurecr.io", "id" : "/subscriptions/23057013-ba68-42b8-9ff2-5153ceb0b676/resourceGroups/Wheelhouse/providers/Microsoft.ContainerRegistry/registries/wheelhouse"}'
      dockerComposePath: '/usr/local/bin/docker-compose'
      dockerComposeFile: "buildscripts/pipelines/$(build.sourceBranchName).yml"
      additionalDockerComposeFiles: "$(build yml)"
      action: "Push services"
      additionalImageTags: $(build.sourceBranchName)

  - task: PublishPipelineArtifact@1
    displayName: "Publish Pipeline Artifact"
