pool: "Conformii Pool"

steps:
  - task: DockerCompose@1
    displayName: "Build services"
    inputs:
        containerregistrytype: 'Azure Container Registry'
        azureSubscription: 'Vantedge Subscription (23057013-ba68-42b8-9ff2-5153ceb0b676)'
        azureContainerRegistry: '{"loginServer":"wheelhouse.azurecr.io", "id" : "/subscriptions/23057013-ba68-42b8-9ff2-5153ceb0b676/resourceGroups/Wheelhouse/providers/Microsoft.ContainerRegistry/registries/wheelhouse"}'
        dockerComposeFile: $(build.sourceBranchName).yml
        action: 'Build services'
        additionalImageTags: $(build.sourceBranchName)
        includeSourceTags: true
  - task: DockerCompose@1
    displayName: "Push services"
    inputs:
      containerregistrytype: 'Azure Container Registry'
      azureSubscription: 'Vantedge Subscription (23057013-ba68-42b8-9ff2-5153ceb0b676)'
      azureContainerRegistry: '{"loginServer":"wheelhouse.azurecr.io", "id" : "/subscriptions/23057013-ba68-42b8-9ff2-5153ceb0b676/resourceGroups/Wheelhouse/providers/Microsoft.ContainerRegistry/registries/wheelhouse"}'
      dockerComposeFile: '$(build.sourceBranchName).yml'
      action: 'Push services'
      additionalImageTags: '$(build.sourceBranchName)'


  - task: PublishPipelineArtifact@1
    displayName: "Publish Pipeline Artifact"
