version: '3'

services:
  vantedge_traefik:
    image: traefik:v2.3
    container_name: vantedge_traefik
    command:
      - "--log.level=DEBUG"
      - "--api.insecure=true"
      - "--providers.docker=true"
      - "--providers.docker.exposedbydefault=false"
      - "--entrypoints.web.address=:80"
    restart: always
    ports:
      - "80:80"
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
    labels:
      - traefik.http.routers.redirs.rule=hostregexp(`{host:.+}`)

  vantedge_redis:
    image: redis:6.0.6
    container_name: vantedge_redis

  vantedge_django:
    container_name: vantedge_django
    build:
      context: ../../..
      dockerfile: buildscripts/docker/local/django/Dockerfile
    depends_on:
      - vantedge_db
      - vantedge_traefik
    volumes:
      - ../../../:/app
      - ../../../data:/data
    env_file:
      - ../.envs/.local/.django
      - ../.envs/.local/.postgres
    command: /start
    ports:
      - 5678-5699:5678-5699
    labels:
      - traefik.enable=true
      - traefik.django.priority=10
      - traefik.http.routers.django.entrypoints=web
      - traefik.http.services.django.loadbalancer.server.port=8000
      - traefik.http.routers.django.rule=Host(`localhost`) && (PathPrefix(`/admin`) || PathPrefix(`/api`) || PathPrefix(`/print`))

  vantedge_db:
    container_name: vantedge_db
    build:
      context: ../../..
      dockerfile: buildscripts/docker/production/postgres/Dockerfile
    volumes:
      - ../../../database:/var/lib/postgresql/data
      - ../../../database_backup:/backups
    env_file:
      - ../.envs/.local/.postgres
      - ../.envs/.local/.do
    ports:
      - 5432:5432

  vantedge_mailpit:
    container_name: vantedge_mailpit
    image: axllent/mailpit
    depends_on:
      - vantedge_traefik
    logging:
      driver: 'none'  # disable saving logs
    ports:
      - 1025:1025
      - 8025:8025
    labels:
      - traefik.enable=true
      - traefik.mailpit.priority=15
      - traefik.http.routers.mailpit.entrypoints=web
      - traefik.http.services.mailpit.loadbalancer.server.port=8025
      - traefik.http.routers.mailpit.rule=Host(`mail.localhost`)

  vantedge_static:
    container_name: vantedge_static
    image: nginx:alpine
    depends_on:
      - vantedge_traefik
    volumes:
      - ../../../static:/static
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
    labels:
      - traefik.enable=true
      - traefik.static.priority=1
      - traefik.http.routers.static.rule=Host(`localhost`) && (PathPrefix(`/`) || PathPrefix(`/static`))
      - traefik.http.routers.static.entrypoints=web
