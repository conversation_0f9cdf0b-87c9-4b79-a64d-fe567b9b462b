FROM python:3.11-slim-bullseye

ENV PYTHONUNBUFFERED 1

RUN apt-get update --fix-missing \
    && apt-get install -y -q apt-utils software-properties-common \
    && apt-get install -y gcc linux-libc-dev --fix-missing \
    && apt-get install -y -q libgeos-dev libgdal-dev --fix-missing \
    && chown -R root /usr/lib/x86_64-linux-gnu/libgeos_c.a \
    && cp /usr/lib/x86_64-linux-gnu/libgeos_c.a /usr/lib/libgeos_c.a \
    && cp /usr/lib/x86_64-linux-gnu/libgeos_c.so /usr/lib/libgeos_c.so.1 \
    && apt-get install -y libcairo2 libpango-1.0-0 libpangocairo-1.0-0 libgdk-pixbuf2.0-0 libffi-dev libpangoft2-1.0-0 libjpeg-dev libopenjp2-7-dev pango* pango1.0-tools shared-mime-info --fix-missing \
    && apt-get install -y libopencv-dev python3-opencv \
    && apt-get install -y poppler-utils tesseract-ocr \
    && apt-get install -y build-essential libpoppler-cpp-dev pkg-config python-dev-is-python3 \
    && apt-get install -y curl \
    && curl -o /home/<USER>//deb.nodesource.com/setup_16.x \
    && bash /home/<USER>
    && apt-get install -y nodejs \
    && npm install -g mjml \
    && apt-get install -y wkhtmltopdf \
    && apt-get install -y git

# Requirements are installed here to ensure they will be cached.
COPY ./requirements /requirements
RUN pip install --no-cache-dir -r /requirements/production.txt \
    && rm -rf /requirements

COPY ./buildscripts/docker/production/django/entrypoint /entrypoint
RUN sed -i 's/\r//' /entrypoint \
    && chmod +x /entrypoint

COPY ./buildscripts/docker/production/django/start /start
RUN sed -i 's/\r//' /start \
    && chmod +x /start

COPY . /app

RUN useradd -ms /bin/bash django

RUN chown -R django /app

RUN chown -R django /media

USER django

WORKDIR /app

ENTRYPOINT ["/entrypoint"]
