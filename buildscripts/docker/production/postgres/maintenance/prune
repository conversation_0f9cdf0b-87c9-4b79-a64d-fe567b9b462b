#!/usr/bin/env bash

working_dir="$(dirname ${0})"
source "${working_dir}/_sourced/messages.sh"

message_info $(date)

ANCHORE_DATE_REQUEST_EVENTS=$(date -d "20 days ago" +"%Y-%m-%d")
message_info "Pruning easyaudit request events before $ANCHORE_DATE_REQUEST_EVENTS..."

export PGHOST="${POSTGRES_HOST}"
export PGPORT="${POSTGRES_PORT}"
export PGUSER="${POSTGRES_USER}"
export PGPASSWORD="${POSTGRES_PASSWORD}"
export PGDATABASE="${POSTGRES_DB}"

psql -U ${PGUSER} -d ${PGDATABASE} -c "DELETE FROM easyaudit_requestevent WHERE datetime < '${ANCHORE_DATE_REQUEST_EVENTS}';"
message_info "easyaudit_requestevent table pruned, now vacuuming easyaudit request table..."

psql -U ${PGUSER} -d ${PGDATABASE} -c "VACUUM easyaudit_requestevent;"

ANCHORE_DATE_GEO_CLM=$(date -d "60 days ago" +"%Y-%m-%d")
message_info "Pruning GEO CLM data before $ANCHORE_DATE_GEO_CLM..."

psql -U ${PGUSER} -d ${PGDATABASE} -c "DELETE FROM edi_geoclm WHERE created < '${ANCHORE_DATE_GEO_CLM}';"
message_info "edi_geoclm table pruned, now vacuuming edi geo clm table..."

psql -U ${PGUSER} -d ${PGDATABASE} -c "VACUUM edi_geoclm;"


message_info "Done! $(date)"