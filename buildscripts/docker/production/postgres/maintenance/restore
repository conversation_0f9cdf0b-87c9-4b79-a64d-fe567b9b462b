#!/usr/bin/env bash


### Restore database from a backup.
###
### Parameters:
###     <1> filename of an existing backup.
###
### Usage:
###     $ docker-compose -f <environment>.yml (exec |run --rm) postgres restore <1>


set -o errexit
set -o pipefail
set -o nounset


working_dir="$(dirname ${0})"
source "${working_dir}/_sourced/constants.sh"
source "${working_dir}/_sourced/messages.sh"


if [[ -z ${1+x} ]]; then
    message_error "Backup filename is not specified yet it is a required parameter. Make sure you provide one and try again."
    exit 1
fi
backup_filename="${BACKUP_DIR_PATH}/${1}"
if [[ ! -f "${backup_filename}" ]]; then
    message_error "No backup with the specified filename found. Check out the 'backups' maintenance script output to see if there is one and try again."
    exit 1
fi

message_welcome "Restoring the '${POSTGRES_DB}' database from the '${backup_filename}' backup..."

if [[ "${POSTGRES_USER}" == "postgres" ]]; then
    message_error "Restoring as 'postgres' user is not supported. Assign 'POSTGRES_USER' env with another one and try again."
    exit 1
fi

export PGHOST="${POSTGRES_HOST}"
export PGPORT="${POSTGRES_PORT}"
export PGUSER="${POSTGRES_USER}"
export PGPASSWORD="${POSTGRES_PASSWORD}"
export PGDATABASE="${POSTGRES_DB}"

message_info "Dropping the database..."
dropdb "${PGDATABASE}"

message_info "Creating a new database..."
createdb --owner="${POSTGRES_USER}"

message_info "Applying the backup to the new database..."

# Detect number of CPU cores for optimal parallel processing
CPU_CORES=$(nproc 2>/dev/null || echo "4")
# Use all available cores, but cap at 16 to avoid overwhelming the system
PARALLEL_JOBS=$((CPU_CORES > 16 ? 16 : CPU_CORES))

message_info "Using ${PARALLEL_JOBS} parallel jobs (detected ${CPU_CORES} CPU cores)..."

# Optimize PostgreSQL settings for faster restore
psql -d "${POSTGRES_DB}" -c "
    ALTER SYSTEM SET maintenance_work_mem = '1GB';
    ALTER SYSTEM SET max_wal_size = '4GB';
    ALTER SYSTEM SET checkpoint_completion_target = 0.9;
    ALTER SYSTEM SET wal_buffers = '64MB';
    ALTER SYSTEM SET shared_buffers = '512MB';
    SELECT pg_reload_conf();
"

# Use optimized pg_restore with parallel jobs and performance options
pg_restore \
    -j "${PARALLEL_JOBS}" \
    --no-owner \
    --no-privileges \
    --disable-triggers \
    -d "${POSTGRES_DB}" \
    "${backup_filename}"

# Reset PostgreSQL settings to defaults after restore
psql -d "${POSTGRES_DB}" -c "
    ALTER SYSTEM RESET maintenance_work_mem;
    ALTER SYSTEM RESET max_wal_size;
    ALTER SYSTEM RESET checkpoint_completion_target;
    ALTER SYSTEM RESET wal_buffers;
    ALTER SYSTEM RESET shared_buffers;
    SELECT pg_reload_conf();
"

message_success "The '${POSTGRES_DB}' database has been restored from the '${backup_filename}' backup."
