version: "3.3"

services:
  vantedge_traefik:
    image: traefik:v2.6
    container_name: vantedge_traefik
    command:
      - --entrypoints.web.address=:80
      - --entrypoints.websecure.address=:443
      - --entrypoints.web.http.redirections.entryPoint.to=websecure
      - --entrypoints.web.http.redirections.entryPoint.scheme=https
      - --providers.docker
      - --api
      - --api.insecure
      - --api.dashboard
      - --certificatesresolvers.certresolver.acme.tlschallenge=true
      - --certificatesresolvers.certresolver.acme.email=<EMAIL>
      - --certificatesresolvers.certresolver.acme.storage=/certificates/acme.json
      - --providers.file.filename=/certificates/traefik-dynamic.yml      
    ports:
      - 80:80
      - 443:443
    labels:
      - traefik.http.routers.redirs.rule=hostregexp(`{host:.+}`)
      - traefik.http.routers.redirs.entrypoints=web
      - traefik.http.routers.redirs.middlewares=https-redirect
      - traefik.http.middlewares.https-redirect.redirectscheme.scheme=https
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - ~/ssl_certs:/certificates
    restart: always

  vantedge_redis:
    image: redis:6.0.6
    container_name: vantedge_redis
    restart: always

  vantedge_django:
    container_name: vantedge_django
    depends_on:
      - vantedge_db
      - vantedge_traefik
    shm_size: 4GB
    environment:
      - DJANGO_SETTINGS_MODULE=${DJANGO_SETTINGS_MODULE}
      - DJANGO_SECRET_KEY=${DJANGO_SECRET_KEY}
      - WEB_CONCURRENCY=${WEB_CONCURRENCY}
      - POSTGRES_HOST=${POSTGRES_HOST}
      - POSTGRES_PORT=${POSTGRES_PORT}
      - POSTGRES_DB=${POSTGRES_DB}
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - DATABASE_URL=${DATABASE_URL}
      - BASE_URL=${BASE_URL}
      - BASE_ADMIN_URL=${BASE_ADMIN_URL}
      - BASE_API_URL=${BASE_API_URL}
      - REDIS_URL=${REDIS_URL}
      - AZURE_CONTAINER=${AZURE_CONTAINER}
      - AZURE_ACCOUNT_KEY=${AZURE_ACCOUNT_KEY}
      - BRANCH_NAME=${BRANCH_NAME}
      - EMAIL_HOST=${EMAIL_HOST}
      - EMAIL_PORT=${EMAIL_PORT}
      - EMAIL_USE_TLS=${EMAIL_USE_TLS}
      - EMAIL_HOST_USER=${EMAIL_HOST_USER}
      - EMAIL_HOST_PASSWORD=${EMAIL_HOST_PASSWORD}
      - BACKEND_VERSION=${BACKEND_VERSION}
    command: /start
    volumes:
      - ~/data:/data
      - ~/static:/static
    labels:
      - traefik.enable=true
      - traefik.http.routers.django.priority=10
      - traefik.http.routers.django.entrypoints=websecure
      - traefik.http.routers.django.tls=true
      - traefik.http.routers.django.tls.certresolver=certresolver
      - traefik.http.services.django.loadbalancer.server.port=8000
      - traefik.http.routers.django.rule=(Host(`plains-uat.wheelhouse.vantedgelgx.com`) || Host(`plains-uat.wheelhouse.vantedge.com`)) && (PathPrefix(`/admin`) || PathPrefix(`/api`) || PathPrefix(`/print`))
    restart: always

  vantedge_db:
    container_name: vantedge_db
    volumes:
      - ~/database:/var/lib/postgresql/data
      # - ~/database_backup:/backups
    shm_size:
      2GB
      # - ~/database_backup:/backups
    environment:
      - POSTGRES_HOST=${POSTGRES_HOST}
      - POSTGRES_PORT=${POSTGRES_PORT}
      - POSTGRES_DB=${POSTGRES_DB}
      - POSTGRES_DB_2=${POSTGRES_DB_2}
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - S3_ACCESS_KEY=${S3_ACCESS_KEY}
      - S3_SECRET=${S3_SECRET}
      - S3_ENDPOINT=${S3_ENDPOINT}
      - S3_DB_BUCKET=${S3_DB_BUCKET}
      - WORKING_ENV=${WORKING_ENV}
    restart: always

  vantedge_static:
    container_name: vantedge_static
    image: nginx:alpine
    depends_on:
      - vantedge_traefik
    volumes:
      - ~/static:/static
      - ~/dist:/dist
      - ~/pwa:/pwa
      - ../docker/production/nginx/nginx.conf:/etc/nginx/nginx.conf
    labels:
      - traefik.enable=true
      - traefik.http.routers.static.priority=1
      - traefik.http.routers.static.rule=(Host(`plains-uat.wheelhouse.vantedgelgx.com`) ||  Host(`plains-uat.wheelhouse.vantedge.com`)) && (PathPrefix(`/`) || PathPrefix(`/static`) || PathPrefix(`/demo`))
      - traefik.http.routers.static.entrypoints=websecure
      - traefik.http.routers.static.tls=true
      - traefik.http.routers.static.tls.certresolver=certresolver
    restart: always

