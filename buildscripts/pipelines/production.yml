version: '3.3'

services:
  vantedge_traefik:
    image: traefik:v2.6
    container_name: vantedge_traefik
    command:
      - --entrypoints.web.address=:80
      - --entrypoints.websecure.address=:443
      - --providers.docker
      - --certificatesresolvers.certresolver.acme.tlschallenge=true
      - --certificatesresolvers.certresolver.acme.email=<EMAIL>
      - --certificatesresolvers.certresolver.acme.storage=/certificates/acme.json
      - --providers.file.filename=/certificates/traefik-dynamic.yml
    ports:
      - 80:80
      - 443:443
    labels:
      - traefik.http.routers.redirs.rule=hostregexp(`{host:.+}`)
      - traefik.http.routers.redirs.entrypoints=web
      - traefik.http.routers.redirs.middlewares=https-redirect
      - traefik.http.middlewares.https-redirect.redirectscheme.scheme=https
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - ~/ssl_certs:/certificates
    restart: always

  vantedge_redis:
    image: redis:6.0.6
    container_name: vantedge_redis
    restart: always

  vantedge_django:
    # build: back_vantedge_django
    # image: back_vantedge_django:production
    # dummy change: None
    container_name: vantedge_django
    depends_on:
      - vantedge_db
      - vantedge_traefik
    shm_size: 3GB
    environment:
      - DJANGO_SETTINGS_MODULE=${DJANGO_SETTINGS_MODULE}
      - DJANGO_SECRET_KEY=${DJANGO_SECRET_KEY}
      - WEB_CONCURRENCY=${WEB_CONCURRENCY}
      - POSTGRES_HOST=${POSTGRES_HOST}
      - POSTGRES_PORT=${POSTGRES_PORT}
      - POSTGRES_DB=${POSTGRES_DB}
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - DATABASE_URL=${DATABASE_URL}
      - BASE_URL=${BASE_URL}
      - BASE_ADMIN_URL=${BASE_ADMIN_URL}
      - BASE_API_URL=${BASE_API_URL}
      - REDIS_URL=${REDIS_URL}
      - EMAIL_HOST=${EMAIL_HOST}
      - EMAIL_PORT=${EMAIL_PORT}
      - EMAIL_USE_TLS=${EMAIL_USE_TLS}
      - EMAIL_HOST_USER=${EMAIL_HOST_USER}
      - EMAIL_HOST_PASSWORD=${EMAIL_HOST_PASSWORD}
      - DEFAULT_FROM_EMAIL=${DEFAULT_FROM_EMAIL}
      - SENDGRID_API_KEY=${SENDGRID_API_KEY}
      - AZURE_CONTAINER=${AZURE_CONTAINER}
      - AZURE_ACCOUNT_KEY=${AZURE_ACCOUNT_KEY}
      - RAILINC_FTP_HOST=${RAILINC_FTP_HOST}
      - RAILINC_FTP_USER=${RAILINC_FTP_USER}
      - RAILINC_FTP_PASS=${RAILINC_FTP_PASS}
      - S3_ACCESS_KEY=${S3_ACCESS_KEY}
      - S3_SECRET=${S3_SECRET}
      - S3_EDI_BUCKET=${S3_EDI_BUCKET}
      - S3_ENDPOINT=${S3_ENDPOINT}
      - TWILIO_ACCOUNT_SID=${TWILIO_ACCOUNT_SID}
      - TWILIO_AUTH_TOKEN=${TWILIO_AUTH_TOKEN}
      - TWILIO_NUMBER=${TWILIO_NUMBER}
      - BACKEND_VERSION=${BACKEND_VERSION}
      - VANTEDGE_PATH_KEY=${VANTEDGE_PATH_KEY}
      - VANTEDGE_PATH_CERT=${VANTEDGE_PATH_CERT}
      - GEO_OAUTH_PARAM=${GEO_OAUTH_PARAM}
      - SENTRY_ENV=Production
      - SENTRY_DSN=${SENTRY_DSN}
      - DJANGO_EMAIL_BACKEND=${DJANGO_EMAIL_BACKEND}
      - AZURE_COMMUNICATION_CONNECTION_STRING=${AZURE_COMMUNICATION_CONNECTION_STRING}
      - AZURE_COMMUNICATION_TRACKING_DISABLED=${AZURE_COMMUNICATION_TRACKING_DISABLED}
      - EMAIL_BACKEND_SECONDARY_0=${EMAIL_BACKEND_SECONDARY_0}
    command: /start
    volumes:
      - ~/data:/data
      - ~/static:/static
    labels:
      - traefik.enable=true
      - traefik.http.routers.django.priority=10
      - traefik.http.routers.django.entrypoints=websecure
      - traefik.http.routers.django.tls=true
      - traefik.http.routers.django.tls.certresolver=certresolver
      - traefik.http.services.django.loadbalancer.server.port=8000
      - traefik.http.routers.django.rule=(Host(`wheelhouse.vantedge.com`) || Host(`wheelhouse.vantedgelgx.com`)) && (PathPrefix(`/admin`) || PathPrefix(`/api`) || PathPrefix(`/print`))
    restart: always
    logging:
      driver: gcplogs
      options:
        gcp-project: "useful-device-302517"
      # Credentials: https://stackoverflow.com/a/51357915

  vantedge_db:
    container_name: vantedge_db
    volumes:
      - ~/database:/var/lib/postgresql/data
      # - ~/database_backup:/backups
    shm_size: 13GB
      # - ~/database_backup:/backups
    environment:
      - POSTGRES_HOST=${POSTGRES_HOST}
      - POSTGRES_PORT=${POSTGRES_PORT}
      - POSTGRES_DB=${POSTGRES_DB}
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - S3_ACCESS_KEY=${S3_ACCESS_KEY}
      - S3_SECRET=${S3_SECRET}
      - S3_ENDPOINT=${S3_ENDPOINT}
      - S3_DB_BUCKET=${S3_DB_BUCKET}
      - WORKING_ENV=${WORKING_ENV}
    restart: always

  vantedge_static:
    container_name: vantedge_static
    image: nginx:alpine
    depends_on:
      - vantedge_traefik
    volumes:
      - ~/static:/static
      - ~/dist:/dist
      - ~/pwa:/pwa
      - ../docker/production/nginx/nginx.conf:/etc/nginx/nginx.conf
    labels:
      - traefik.enable=true
      - traefik.http.routers.static.priority=1
      - traefik.http.routers.static.rule=(Host(`wheelhouse.vantedge.com`) || Host(`app.wheelhouse.vantedge.com`) || Host(`wheelhouse.vantedgelgx.com`) || Host(`app.wheelhouse.vantedgelgx.com`))  && (PathPrefix(`/`) || PathPrefix(`/static`) || PathPrefix(`/demo`))
      - traefik.http.routers.static.entrypoints=websecure
      - traefik.http.routers.static.tls=true
      - traefik.http.routers.static.tls.certresolver=certresolver
    restart: always
