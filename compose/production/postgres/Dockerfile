FROM postgres:16-bookworm

#RUN echo 'deb http://deb.debian.org/debian buster-backports main' > /etc/apt/sources.list.d/backports.list && apt-get update && apt-get -y install apt-utils && apt-get -y install s3cmd
RUN apt-get update && apt-get -y install apt-utils && apt-get -y install s3cmd

COPY ./compose/production/postgres/maintenance /usr/local/bin/maintenance
COPY ./compose/production/postgres/postgresql.conf /var/lib/postgresql/data/postgresql.conf

RUN chmod +x /usr/local/bin/maintenance/*
RUN mv /usr/local/bin/maintenance/* /usr/local/bin \
    && rmdir /usr/local/bin/maintenance
