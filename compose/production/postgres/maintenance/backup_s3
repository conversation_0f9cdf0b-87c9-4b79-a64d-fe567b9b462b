#!/usr/bin/env bash


### Create a database backup.
###
### Usage:
###     $ docker-compose -f <environment>.yml (exec |run --rm) postgres backup_s3


set -o errexit
set -o pipefail
set -o nounset

working_dir="$(dirname ${0})"
source "${working_dir}/_sourced/constants.sh"
source "${working_dir}/_sourced/messages.sh"

s3_config="--host=${S3_ENDPOINT} --host-bucket=${S3_DB_BUCKET}.${S3_ENDPOINT}  --access_key=${S3_ACCESS_KEY} --secret_key=${S3_SECRET} --no-check-certificate"

backup
s3cmd put ${BACKUP_DIR_PATH}/* s3://${S3_DB_BUCKET}  $s3_config
rm ${BACKUP_DIR_PATH}/*
