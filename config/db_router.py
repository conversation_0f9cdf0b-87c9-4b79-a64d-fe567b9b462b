COMPLISHIELD_DATABASE = "complishield-prod"

class ComplishieldDBRouter:
    """
    A router to control all database operations on complishield models
    """
    complishield_app_label = {"complishield"}

    def db_for_read(self, model, **hints):
        """
        Attempts to read complishield models go to auth_db.
        """
        if model._meta.app_label in self.complishield_app_label:
            return COMPLISHIELD_DATABASE
        return "default"

    def db_for_write(self, model, **hints):
        """
        Attempts to write complishield models go to auth_db.
        """
        if model._meta.app_label in self.complishield_app_label:
            return COMPLISHIELD_DATABASE
        return "default"

    def allow_relation(self, obj1, obj2, **hints):
        """
        Relations between objects are allowed if both objects are
        from the same database
        """
        if obj1._state.db == obj2._state.db:
            return True
        return None


    def allow_migrate(self, db, app_label, model_name=None, **hints):
        """
        Make sure the complishield apps only appear in the
        COMPLISHIELD_DATABASE database.
        """
        if app_label in self.complishield_app_label:
            return db == COMPLISHIELD_DATABASE
        return True
