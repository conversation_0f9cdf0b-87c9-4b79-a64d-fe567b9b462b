"""
Base settings to build other settings files upon.
"""

import os
import environ
from pathlib import Path

import sentry_sdk
from corsheaders.defaults import default_headers
from icecream import install

from .notifications_config import *  # noqa: F403
from .constance_config import *  # noqa: F403

install()

CORS_ALLOW_HEADERS = default_headers + ("Access-Control-Allow-Origin",)

ROOT_DIR: environ.Path = (
    environ.Path(__file__) - 3
)  # (vantedge/config/settings/base.py - 3 = vantedge/)
APPS_DIR = ROOT_DIR.path("vantedge")

env = environ.Env()

READ_DOT_ENV_FILE = env.bool("DJANGO_READ_DOT_ENV_FILE", default=False)
BACKEND_VERSION = env("BACKEND_VERSION", default="Not Set")
SENTRY_ENV = env("SENTRY_ENV", default="Local")

if READ_DOT_ENV_FILE:
    # OS environment variables take precedence over variables from .env
    env.read_env(str(ROOT_DIR.path(".env")))

# GENERAL
# ------------------------------------------------------------------------------
# https://docs.djangoproject.com/en/dev/ref/settings/#debug
DEBUG = env.bool("DJANGO_DEBUG", False)
BASE_URL = env.str("BASE_URL", "")

# Local time zone. Choices are
# http://en.wikipedia.org/wiki/List_of_tz_zones_by_name
# though not all of them may be available with every OS.
# In Windows, this must be set to your system time zone.
TIME_ZONE = "America/Edmonton"
# https://docs.djangoproject.com/en/dev/ref/settings/#language-code
LANGUAGE_CODE = "en-us"
# https://docs.djangoproject.com/en/dev/ref/settings/#site-id
SITE_ID = 1
# https://docs.djangoproject.com/en/dev/ref/settings/#use-i18n
USE_I18N = True
# https://docs.djangoproject.com/en/dev/ref/settings/#use-l10n
USE_L10N = True
# https://docs.djangoproject.com/en/dev/ref/settings/#use-tz
USE_TZ = True
# https://docs.djangoproject.com/en/dev/ref/settings/#locale-paths
LOCALE_PATHS = [ROOT_DIR.path("locale")]

# https://docs.djangoproject.com/en/3.2/ref/settings/#serialization-modules
SERIALIZATION_MODULES = {"vantedge": "vantedge.serializer"}

# DATABASES
# ------------------------------------------------------------------------------
# https://docs.djangoproject.com/en/dev/ref/settings/#databases
if "DATABASE_URL" not in env:  # Get database url locally
    env.read_env(str(ROOT_DIR.path("envs/.local/.postgres")))
DATABASES = {"default": env.db("DATABASE_URL")}
DATABASES["default"]["ATOMIC_REQUESTS"] = True

# URLS
# ------------------------------------------------------------------------------
# https://docs.djangoproject.com/en/dev/ref/settings/#root-urlconf
ROOT_URLCONF = "config.urls"
# https://docs.djangoproject.com/en/dev/ref/settings/#wsgi-application
WSGI_APPLICATION = "config.wsgi.application"
CORS_ORIGIN_ALLOW_ALL = True
# APPS
# ------------------------------------------------------------------------------
DJANGO_APPS = [
    "django.contrib.auth",
    "django.contrib.contenttypes",
    "django.contrib.sessions",
    "django.contrib.sites",
    "django.contrib.messages",
    "django.contrib.staticfiles",
    "django.contrib.humanize",  # Handy template tags
    "django.contrib.gis",
    "admin_interface",
    "colorfield",
    "admin_tools",
    "admin_tools.theming",
    "admin_tools.menu",
    "admin_tools.dashboard",
    "django.contrib.admin",
    "django.contrib.postgres",
]
THIRD_PARTY_APPS = [
    "django_q",
    "crispy_forms",
    "allauth",
    "allauth.account",
    "allauth.socialaccount",
    "allauth.socialaccount.providers.microsoft",
    "allauth.socialaccount.providers.google",
    "rest_framework",
    "rest_framework_gis",
    "rest_framework.authtoken",
    "django_filters",
    "dj_rest_auth",
    "corsheaders",
    "sass_processor",
    "phonenumber_field",
    "django_object_actions",
    "computedfields",
    "admin_auto_filters",
    "import_export",
    "django_jsonform",
    "django_json_widget",
    "ninja_extra",
    "mjml",
    "vant_email",
    "constance.backends.database",
    "djrichtextfield",
    "constance",
    "rangefilter",
    "django_pandas",
    "dal",
    "dal_select2",
]

LOCAL_APPS = [
    "vantedge.feature",
    "vantedge.users.apps.UsersConfig",
    "vantedge.wheelhouse",
    "vantedge.edi",
    "vantedge.reports",
    "vantedge.attachment",
    "vantedge.printables",
    "vantedge.tasks",
    "vantedge.yard_management",
    "vantedge.trackandtrace.apps.TrackAndTraceConfig",
    "vantedge.terminalboss",
    "vantedge.dashboards",
    "vantedge.autocomplete",
    "vantedge.gaugetables.apps.GaugeTableConfig",
    "vantedge.invoice",
    "vantedge.customs",
    "vantedge.easyaudit",
    "vantedge.iot",
    # TBOSS V3
    "vantedge.tboss",
    "vant_dbreplication",
    "vant_ledger",
    "vant_utils",
    "vant_calcs",
    "vant_user",
    "vant_tboss",
    "vant_limit",
    "vant_ticket",
    "vantedge.schedule",
    "vantedge.notification",
    "vantedge.invitation",
    "vantedge.workflow",
    "vantedge.forms",
]

CUSTOM_APPS = [
    "custom_apps.altagas",
    "custom_apps.plains",
    "custom_apps.green_impact_partners",
]


CORS_ORIGIN_ALLOW_ALL = True
# https://docs.djangoproject.com/en/dev/ref/settings/#installed-apps
INSTALLED_APPS = DJANGO_APPS + THIRD_PARTY_APPS + LOCAL_APPS + CUSTOM_APPS

# MIGRATIONS
# ------------------------------------------------------------------------------
# https://docs.djangoproject.com/en/dev/ref/settings/#migration-modules
MIGRATION_MODULES = {"sites": "vantedge.contrib.sites.migrations"}
DEFAULT_AUTO_FIELD = "django.db.models.AutoField"

# AUTHENTICATION
# ------------------------------------------------------------------------------
# https://docs.djangoproject.com/en/dev/ref/settings/#authentication-backends
AUTHENTICATION_BACKENDS = [
    "django.contrib.auth.backends.ModelBackend",
    "allauth.account.auth_backends.AuthenticationBackend",
]
# https://docs.djangoproject.com/en/dev/ref/settings/#auth-user-model
AUTH_USER_MODEL = "users.User"
# https://docs.djangoproject.com/en/dev/ref/settings/#login-redirect-url
LOGIN_REDIRECT_URL = "users:redirect"
# https://docs.djangoproject.com/en/dev/ref/settings/#login-url
LOGIN_URL = "account_login"

# PASSWORDS
# ------------------------------------------------------------------------------
# https://docs.djangoproject.com/en/dev/ref/settings/#password-hashers
PASSWORD_HASHERS = [
    # https://docs.djangoproject.com/en/dev/topics/auth/passwords/#using-argon2-with-django
    "django.contrib.auth.hashers.Argon2PasswordHasher",
    "django.contrib.auth.hashers.PBKDF2PasswordHasher",
    "django.contrib.auth.hashers.PBKDF2SHA1PasswordHasher",
    "django.contrib.auth.hashers.BCryptSHA256PasswordHasher",
]
# https://docs.djangoproject.com/en/dev/ref/settings/#auth-password-validators
AUTH_PASSWORD_VALIDATORS = [
    {
        "NAME": "django.contrib.auth.password_validation.UserAttributeSimilarityValidator"
    },
    {"NAME": "django.contrib.auth.password_validation.MinimumLengthValidator"},
    {"NAME": "django.contrib.auth.password_validation.CommonPasswordValidator"},
    {"NAME": "django.contrib.auth.password_validation.NumericPasswordValidator"},
]
OLD_PASSWORD_FIELD_ENABLED = True

# MIDDLEWARE
# ------------------------------------------------------------------------------
# https://docs.djangoproject.com/en/dev/ref/settings/#middleware
MIDDLEWARE = [
    "corsheaders.middleware.CorsMiddleware",
    "django.middleware.common.CommonMiddleware",
    "django.middleware.security.SecurityMiddleware",
    "django.contrib.sessions.middleware.SessionMiddleware",
    "django.middleware.locale.LocaleMiddleware",
    "django.middleware.common.CommonMiddleware",
    "django.middleware.csrf.CsrfViewMiddleware",
    "django.contrib.auth.middleware.AuthenticationMiddleware",
    "compression_middleware.middleware.CompressionMiddleware",
    "django.contrib.messages.middleware.MessageMiddleware",
    "django.middleware.clickjacking.XFrameOptionsMiddleware",
    "vantedge.easyaudit.middleware.easyaudit.EasyAuditMiddleware",
    "allauth.account.middleware.AccountMiddleware",
]

# STATIC
# ------------------------------------------------------------------------------
# https://docs.djangoproject.com/en/dev/ref/settings/#static-root
STATIC_ROOT = str(ROOT_DIR("static"))
# https://docs.djangoproject.com/en/dev/ref/settings/#static-url
STATIC_URL = "/static/"
# https://docs.djangoproject.com/en/dev/ref/contrib/staticfiles/#std:setting-STATICFILES_DIRS
STATICFILES_DIRS = [str(APPS_DIR.path("static"))]
# https://docs.djangoproject.com/en/dev/ref/contrib/staticfiles/#staticfiles-finders
STATICFILES_FINDERS = [
    "django.contrib.staticfiles.finders.FileSystemFinder",
    "django.contrib.staticfiles.finders.AppDirectoriesFinder",
    "sass_processor.finders.CssFinder",
]

# MEDIA
# ------------------------------------------------------------------------------
BASE_MEDIA_URL = "https://wheelhouse.blob.core.windows.net"
# https://docs.djangoproject.com/en/dev/ref/settings/#media-root
MEDIA_ROOT = str(APPS_DIR("media"))
# https://docs.djangoproject.com/en/dev/ref/settings/#media-url
MEDIA_URL = "/media/"

BASE_DIR = Path(__file__).resolve().parent.parent
# TEMPLATES
# ------------------------------------------------------------------------------
# https://docs.djangoproject.com/en/dev/ref/settings/#templates
TEMPLATES = [
    {
        # https://docs.djangoproject.com/en/dev/ref/settings/#std:setting-TEMPLATES-BACKEND
        "BACKEND": "django.template.backends.django.DjangoTemplates",
        # https://docs.djangoproject.com/en/dev/ref/settings/#template-dirs
        "DIRS": [
            str(APPS_DIR.path("templates")),
            os.path.join(BASE_DIR, "custom_apps", "green_impact_partners", "templates"),
        ],
        "OPTIONS": {
            # https://docs.djangoproject.com/en/dev/ref/settings/#template-loaders
            # https://docs.djangoproject.com/en/dev/ref/templates/api/#loader-types
            "loaders": [
                "django.template.loaders.filesystem.Loader",
                "django.template.loaders.app_directories.Loader",
                "admin_tools.template_loaders.Loader",
                "vantedge.tboss.views.ticket.TemplateLoader",
            ],
            # https://docs.djangoproject.com/en/dev/ref/settings/#template-context-processors
            "context_processors": [
                "constance.context_processors.config",
                "django.template.context_processors.debug",
                "django.template.context_processors.request",
                "django.contrib.auth.context_processors.auth",
                "django.template.context_processors.i18n",
                "django.template.context_processors.media",
                "django.template.context_processors.static",
                "django.template.context_processors.tz",
                "django.contrib.messages.context_processors.messages",
            ],
            "builtins": ["vantedge.tboss.templatetags.barcode_tags"],
        },
    }
]
# http://django-crispy-forms.readthedocs.io/en/latest/install.html#template-packs
CRISPY_TEMPLATE_PACK = "bootstrap4"

# FIXTURES
# ------------------------------------------------------------------------------
# https://docs.djangoproject.com/en/dev/ref/settings/#fixture-dirs
FIXTURE_DIRS = (str(APPS_DIR.path("fixtures")),)

# SECURITY
# ------------------------------------------------------------------------------
# https://docs.djangoproject.com/en/dev/ref/settings/#session-cookie-httponly
SESSION_COOKIE_HTTPONLY = True
# https://docs.djangoproject.com/en/dev/ref/settings/#csrf-cookie-httponly
CSRF_COOKIE_HTTPONLY = True
# https://docs.djangoproject.com/en/4.2/ref/settings/#csrf-trusted-origins
CSRF_TRUSTED_ORIGINS = ["https://*.vantedgelgx.com", "https://*.vantedge.com"]

# https://docs.djangoproject.com/en/dev/ref/settings/#secure-browser-xss-filter
SECURE_BROWSER_XSS_FILTER = True
# https://docs.djangoproject.com/en/dev/ref/settings/#x-frame-options
X_FRAME_OPTIONS = "SAMEORIGIN"
# EMAIL
# ------------------------------------------------------------------------------
# https://docs.djangoproject.com/en/dev/ref/settings/#default-from-email
DEFAULT_FROM_EMAIL = env(
    "DJANGO_DEFAULT_FROM_EMAIL", default="Wheelhouse <<EMAIL>>"
)
# ------------------------------------------------------------------------------
# https://docs.djangoproject.com/en/dev/ref/settings/#email-backend
EMAIL_BACKEND = env(
    "DJANGO_EMAIL_BACKEND", default="django.core.mail.backends.smtp.EmailBackend"
)
AZURE_COMMUNICATION_CONNECTION_STRING = env(
    "AZURE_COMMUNICATION_CONNECTION_STRING", default=""
)
AZURE_COMMUNICATION_TRACKING_DISABLED = env(
    "AZURE_COMMUNICATION_TRACKING_DISABLED", default=True
)

EMAIL_BACKEND_SECONDARY_0 = env("EMAIL_BACKEND_SECONDARY_0", default=None)

# https://docs.djangoproject.com/en/2.2/ref/settings/#email-timeout
EMAIL_TIMEOUT = 20

# ADMIN
# ------------------------------------------------------------------------------
# Django Admin URL.
ADMIN_URL = "admin/"
# https://docs.djangoproject.com/en/dev/ref/settings/#admins
# ADMINS = [("""Admin""", "<EMAIL>")]
# https://docs.djangoproject.com/en/dev/ref/settings/#managers
# MANAGERS = ADMINS

# LOGGING
# ------------------------------------------------------------------------------
# https://docs.djangoproject.com/en/dev/ref/settings/#logging
# See https://docs.djangoproject.com/en/dev/topics/logging for
# more details on how to customize your logging configuration.
LOGGING = {
    "version": 1,
    "disable_existing_loggers": False,
    "formatters": {
        "verbose": {
            "format": "%(levelname)s %(asctime)s %(name)s "
            "%(process)d %(thread)d %(message)s"
        }
    },
    "handlers": {
        "console": {
            "level": "DEBUG",
            "class": "logging.StreamHandler",
            "formatter": "verbose",
        }
    },
    "root": {"level": "INFO", "handlers": ["console"]},
    "monitoring": {"level": "INFO", "handlers": ["console"]},
}

# rest-auth custom serializer
REST_AUTH = {
    "LOGIN_SERIALIZER": "vantedge.util.dj_rest_auth.serializers.CustomLoginSerializer"
}

# django-allauth
# ------------------------------------------------------------------------------
ACCOUNT_ALLOW_REGISTRATION = env.bool("DJANGO_ACCOUNT_ALLOW_REGISTRATION", True)
# https://django-allauth.readthedocs.io/en/latest/configuration.html
ACCOUNT_AUTHENTICATION_METHOD = "email"
# https://django-allauth.readthedocs.io/en/latest/configuration.html
ACCOUNT_EMAIL_REQUIRED = True
ACCOUNT_USERNAME_REQUIRED = False
# https://django-allauth.readthedocs.io/en/latest/configuration.html
ACCOUNT_EMAIL_VERIFICATION = "none"
# https://django-allauth.readthedocs.io/en/latest/configuration.html
ACCOUNT_ADAPTER = "vantedge.users.adapters.AccountAdapter"
# https://django-allauth.readthedocs.io/en/latest/configuration.html
SOCIALACCOUNT_ADAPTER = "vantedge.users.adapters.SocialAccountAdapter"
SOCIALACCOUNT_EMAIL_AUTHENTICATION_AUTO_CONNECT = True
SOCIALACCOUNT_EMAIL_AUTHENTICATION = True

# Your stuff...
# ------------------------------------------------------------------------------
ALLOWED_HOSTS = [
    "http://localhost:8080",
    "https://localhost:8080",
    "http://0.0.0.0:8080",
    "https://0.0.0.0:8080",
    "https://*.wheelhouse.vantedgelgx.com",
    "https://*.wheelhouse.vantedge.com",
]


CORS_ORIGIN_WHITELIST = [
    "http://localhost:8080",
    "https://localhost:8080",
    "http://0.0.0.0:8080",
    "https://0.0.0.0:8080",
    "https://*.wheelhouse.vantedgelgx.com",
    "https://*.wheelhouse.vantedge.com",
]

CORS_ALLOW_CREDENTIALS = True

REST_FRAMEWORK = {
    "DEFAULT_AUTHENTICATION_CLASSES": [
        "rest_framework.authentication.TokenAuthentication"
    ],
    "DEFAULT_FILTER_BACKENDS": (
        "django_filters.rest_framework.DjangoFilterBackend",
        "rest_framework.filters.SearchFilter",
        "rest_framework.filters.OrderingFilter",
    ),
    "DEFAULT_RENDERER_CLASSES": [
        "vantedge.util.json.render.VantedgeJSONRenderer",
        "rest_framework.renderers.JSONRenderer",
    ],
    "DEFAULT_PARSER_CLASSES": (
        "rest_framework.parsers.JSONParser",
        "rest_framework.parsers.FormParser",
        "rest_framework.parsers.MultiPartParser",
    ),
    # "DEFAULT_PAGINATION_CLASS": "rest_framework.pagination.LimitOffsetPagination",
    # "PAGE_SIZE": 50,
    "EXCEPTION_HANDLER": "config.api_exception_handler.custom_handler",
}

SASS_PROCESSOR_ROOT = str(APPS_DIR.path("static/"))

# Django Q
# ------------------------------------------------------------------------------
Q_CLUSTER = {
    "name": "DjangoORM",
    "workers": 4,
    "queue_limit": 8,
    "recycle": 100,
    "timeout": 1799,
    "bulk": 1,
    "retry": 1850,
    "max_attempts": 1,
    "catch_up": False,
    "orm": "default",
}

# Tabbed Admin
TABBED_ADMIN_USE_JQUERY_UI = True

# TRIMBLE
# ------------------------------------------------------------------------------
TRIMBLE_API_KEY = "694874112DEE464797C04E4CFDC22653"

# Django phonenumber field
PHONENUMBER_DEFAULT_REGION = "CA"

# django-admin-tools
# ------------------------------------------------------------------------------
ADMIN_TOOLS_MENU = "vantedge.admin_menu.AdminMenu"
ADMIN_TOOLS_INDEX_DASHBOARD = "vantedge.admin_menu.WheelhouseAdminDashboad"
ADMIN_TOOLS_APP_INDEX_DASHBOARD = "vantedge.admin_menu.WheelhouseAdminAppDashboard"

# Railinc FTP
# ------------------------------------------------------------------------------
RAILINC_FTP_HOST = env("RAILINC_FTP_HOST", default="")
RAILINC_FTP_USER = env("RAILINC_FTP_USER", default="")
RAILINC_FTP_PASS = env("RAILINC_FTP_PASS", default="")

# S3
# ------------------------------------------------------------------------------
S3_ACCESS_KEY = env("S3_ACCESS_KEY", default="")
S3_SECRET = env("S3_SECRET", default="")
S3_ENDPOINT = env("S3_ENDPOINT", default="")
S3_EDI_BUCKET = env("S3_EDI_BUCKET", default="")

DJANGO_EASY_AUDIT_UNREGISTERED_URLS_EXTRA = [r"^/api/dbreplication/"]
DJANGO_EASY_AUDIT_UNREGISTERED_CLASSES_EXTRA = [
    "trackandtrace.carevent",
    "trackandtrace.trip",
    "trackandtrace.waybill",
    "trackandtrace.railstationdistance",
    "terminalboss.objectsynctable_sites",
    "terminalboss.objectsynctable",
    "terminalboss.history",
    "terminalboss.deletedobject",
    "django_q.task",
    "django_q.success",
    "django_q.failure",
    "django_q.schedule",
    "django_q.ormq",
    "vant_dbreplication.dbreplicationworker",
    "vant_dbreplication.dbreplicationlogentry",
    "vant_dbreplication.dbreplicationserversubscription",
    "edi.edilock",
    "edi.edidocument",
    "green_impact_partners.ticketattachmentlog",
    "vant_ticket.ticketentry",
    "notification.note",
    "notification.noteuser",
    "users.userdevice",
    "yard_management.carposition",  # CarPositions are deleted and recreated constantly
    "yard_management.moveexecution",  # Already a history table
    "attachment.attachment",
    "authtoken.token",
    "authtoken.tokenproxy",
]

INVOICE_COMPANY_MODEL = "users.Company"
INVOICE_CUSTOMER_MODEL = "wheelhouse.Party"

# copied from terminalboss v3 for sync
# UNITS

# django-pint will set the DJANGO_PINT_UNIT_REGISTER automatically
# as application_registry
from vant_calcs.uom import UOM  # noqa: E402

DJANGO_PINT_UNIT_REGISTER = UOM

USE_THOUSAND_SEPARATOR = True

# GC Analysis Densities
IC5_DENSITY = 624.1378
NC5_DENSITY = 630.532
C6_DENSITY = 663.2026

TBOSS_USER_MODEL = "vant_user.User"
TBOSS_CLIENT_COMPANY_MODEL = "tboss.TerminalBossClientCompany"
TBOSS_PERMISSION_MODEL = "tboss.TBossPermission"
TBOSS_GROUP_MODEL = "tboss.TBossGroup"
TBOSS_USER_PERMISSIONS_RELATED_NAME = "tbossuser"

# EXTRA GLOBAL CONFIG
CONSTANCE_BACKEND = "constance.backends.database.DatabaseBackend"
CONSTANCE_CONFIG_FIELDSETS = {
    "AUDIT": ("LOG_ALL_REQUESTS", "LOG_ALL_REQUEST_EXCEPTIONS", "TARGET_URLS"),
    "INVITATION": ("INVITATION_EXPIRY",),
    "VANTEDGE SUPPORT": ("SUPPORT_EMAIL", "SUPPORT_CELL"),
    "EMAIL CONFIGURATION": ("PREFERRED_EMAIL_BACKEND", "DOMAINS_TO_REDIRECT"),
    "SYNC_NOTIFICATIONS": (
        "MERGE_CONFLICT_EMAIL_MINUTE_THROTTLE",
        "REPLICATION_LAST_SEEN_EMAIL_THROTTLE",
    ),
    "CLM_SYNC": ("CLM_SYNC_ENABLE", "CLM_SYNC_KEY", "CLM_SYNC_REMOTE_URL"),
}

TBOSS_BASE_TICKET_MODEL = "tboss.ticket"
DBREPLICATION_SERVER_TYPE = "wheelhouse"
# Warning: if the base_unit is changed when there are already Limits
# stored in the database you will have to convert all of the existing
# quantity data to the new unit.
TICKET_BASE_UNIT = "L"
TICKET_DISPLAY_UNIT = "L"
INITIAL_TICKET_CODE = 1000

# ------------------------------
#   vant_limit
# ------------------------------
# Warning: if the base_unit is changed when there are already Limits
# stored in the database you will have to convert all of the existing
# quantity data to the new unit.
DEFAULT_LIMIT_BASE_UNIT = "L"
DEFAULT_LIMIT_DISPLAY_UNIT = "L"
LIMIT_CHANGE_FORM_URL_NAME = "admin:truck_limit_change"


# ------------------------------
#   vant_user
# ------------------------------
# Silence warning about usernames as TBoss usernames have a custom validation:
# Ignore related_name no-effect warning for symmetrical m2m
SILENCED_SYSTEM_CHECKS = ["auth.W004", "fields.W345"]


# ------------------------------
#   pdfkit
# ------------------------------
WKHTMLTOPDF_PATH = "/usr/bin/wkhtmltopdf"  # version: wkhtmltopdf 0.12.6
# for patched-qt version:
# https://stackoverflow.com/questions/34479040/how-to-install-wkhtmltopdf-with-patched-qt


# ------------------------------
#   CERTIFICATES
# ------------------------------
VANTEDGE_PATH_KEY = env("VANTEDGE_PATH_KEY", default="")
VANTEDGE_PATH_CERT = env("VANTEDGE_PATH_CERT", default="")

# ------------------------------
#   CERTIFICATES
# ------------------------------
GEO_OAUTH_PARAM = env("GEO_OAUTH_PARAM", default="")

# ------------------------------
#   SENTRY
# ------------------------------
trace_rate = 0.0
profile_rate = 0.0

SENTRY_DSN = env("SENTRY_DSN", default=None)

if SENTRY_ENV == "Local":
    trace_rate = 1.0
    profile_rate = 1.0

elif SENTRY_ENV == "Staging":
    trace_rate = 0.1
    profile_rate = 0.1

if SENTRY_ENV != "Testing" and SENTRY_DSN:
    sentry_sdk.init(
        dsn=SENTRY_DSN,
        traces_sample_rate=trace_rate,
        profiles_sample_rate=profile_rate,
        environment=SENTRY_ENV,
    )

# ------------------------------
#   OPEN AI
# ------------------------------
OPEN_AI_KEY = env("OPEN_AI_KEY", default="")
