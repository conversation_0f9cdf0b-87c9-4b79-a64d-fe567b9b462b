CONSTANCE_CONFIG = {
    # -------------- audit
    "LOG_ALL_REQUESTS": (False, "Will log every request (use with caution)", bool),
    "LOG_ALL_REQUEST_EXCEPTIONS": (
        False,
        "Log all requests if it raise exception",
        bool,
    ),
    "TARGET_URLS": ("", "URLS to monitor (each URL in seperate line)", str),
    # -------------- invitation
    "INVITATION_EXPIRY": (14, "Number of days till an invitation expires", int),
    # -------------- vantedge-support
    "SUPPORT_EMAIL": (
        "<EMAIL>",
        "Official VantEdge support email",
        str,
    ),
    "PREFERRED_EMAIL_BACKEND": ("", "Preferred email backend works with 'Domains To Redirect'", str),
    "DOMAINS_TO_REDIRECT": ("", "Send these domains to preferred email backend", str),
    "SUPPORT_CELL": ("************", "Official VantEdge support cell", str),
    # -------------- vantedge-support
    "MERGE_CONFLICT_EMAIL_MINUTE_THROTTLE": (10, "Number of minutes till a merge conflict email gets sent", int),

    "REPLICATION_LAST_SEEN_EMAIL_THROTTLE": (15, "Number of minutes till a email reminder is sent since the last successful replication for a server"),
    "CLM_SYNC_ENABLE": (False, "Enable syncing endpoint CLM records"),
    "CLM_SYNC_KEY": (
        "NRIWIerbZZd1H2hPvVBuX4rFs38XBV81vpfRIf9tVX3UFGOJkLiA", 
        "`X-clm-sync-key` header value to authenticate for CLM syncing"
    ),
    "CLM_SYNC_REMOTE_URL": ("", "For client side of syncing, the URL to query"),
}
