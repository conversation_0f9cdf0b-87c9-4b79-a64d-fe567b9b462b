import environ

# import firebase_admin

env = environ.Env()

TWILIO_ACCOUNT_SID = env(
    "TWILIO_ACCOUNT_SID", default="**********************************"
)
TWILIO_AUTH_TOKEN = env("TWILIO_AUTH_TOKEN", default="da46c9f4fefb0c04567bd8bb25b552c4")
TWILIO_NUMBER = env("TWILIO_NUMBER", default="+***********")

# FCM_DJANGO_SETTINGS = {
#     "FCM_SERVER_KEY": env("FCM_SERVER_KEY", default=""),
#     "UPDATE_ON_DUPLICATE_REG_ID": True,
# }

# firebase_cred = firebase_admin.credentials.Certificate(
#     env("FCM_CREDENTIALS_JSON_FILE", default="credentials.json")
# )
# FIREBASE_APP = firebase_admin.initialize_app(firebase_cred)
