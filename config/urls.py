from django.conf import settings
from django.urls import include, path
from django.conf.urls.static import static
from django.contrib import admin
from django.views import defaults as default_views
from vantedge.api import (
    router as vantedge_router,
    printables_urls,
    social_urls,
    cache_urls,
)
from vantedge.external_api.api import api as external_ninja_api
from vantedge.external_api.api import router_urls as external_urls
from vantedge.util.health_check import health_check

from vantedge.users.views import empty_view
from vantedge.util.dj_rest_auth.views import (
    PasswordChangeViewFix,
    PasswordResetConfirmViewFix,
    LoginViewFix,
)
from rest_framework.schemas import get_schema_view

admin.autodiscover()
admin.site.enable_nav_sidebar = False

urlpatterns = [
    # path("", TemplateView.as_view(template_name="pages/home.html"), name="home"),
    # path(
    #     "about/", TemplateView.as_view(template_name="pages/about.html"), name="about"
    # ),
    # # Django Admin, use {% url 'admin:index' %}
    # path(settings.ADMIN_URL, admin.site.urls),
    # # User management
    path("users/", include("vantedge.users.urls", namespace="users")),
    path("accounts/", include("allauth.urls")),
    # path("api-auth/", include("rest_framework.urls")),
    path("api/auth/social/", include(social_urls)),
    path(
        "api/auth/password/reset/confirm/",
        PasswordResetConfirmViewFix.as_view(),
        name="rest_password_reset_confirm",
    ),
    path(
        "api/auth/password/change/",
        PasswordChangeViewFix.as_view(),
        name="rest_password_change",
    ),
    path("api/auth/login/", LoginViewFix.as_view(), name="rest_login"),
    path("api/auth/", include("dj_rest_auth.urls")),
    path("api/", include(vantedge_router.urls)),
    # Use a different pattern for the external API
    path("api/external/v2/", external_ninja_api.urls),
    path("api/external/v1/", include(external_urls)),
    path("api/dbreplication/", include("vant_dbreplication.router")),
    path("api/health-check/", health_check),
    path(
        "openapi/",
        get_schema_view(
            title="Your Project", description="API for all things …", version="1.0.0"
        ),
        name="openapi-schema",
    ),
    path("print/", include(printables_urls)),
    path("api/cache/", include(cache_urls)),
    path("api/custom/", include("custom_apps.urls")),
    path("api/iot/", include("vantedge.iot.api.urls")),
    path(settings.ADMIN_URL, admin.site.urls),
    path("admin_tools/", include("admin_tools.urls")),
    path(
        "auth/password-reset/<uidb64>/<token>/",
        empty_view,
        name="password_reset_confirm",
    ),
    # path("", include(router.urls)),
] + static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)


if settings.DEBUG:
    # This allows the error pages to be debugged during development, just visit
    # these url in browser to see how these error pages look like.
    urlpatterns += [
        path("api-auth/", include("rest_framework.urls")),
        path(
            "400/",
            default_views.bad_request,
            kwargs={"exception": Exception("Bad Request!")},
        ),
        path(
            "403/",
            default_views.permission_denied,
            kwargs={"exception": Exception("Permission Denied")},
        ),
        path(
            "404/",
            default_views.page_not_found,
            kwargs={"exception": Exception("Page not Found")},
        ),
        path("500/", default_views.server_error),
    ]
    if "debug_toolbar" in settings.INSTALLED_APPS:
        import debug_toolbar

        urlpatterns = [
            path("api/__debug__/", include(debug_toolbar.urls))
        ] + urlpatterns
