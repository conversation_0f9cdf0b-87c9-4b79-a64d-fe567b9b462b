import os
from concurrent.futures import ThreadPoolExecutor, as_completed
from pathlib import Path

from django.core.management.base import BaseCommand
from tqdm import tqdm
from vantedge.tboss.views.utils import barcode_utils


# ruff: noqa: T201 ARG002
class Command(BaseCommand):
    def handle(self, *args, **options):
        """ Command for testing the barcode reader. Not meant to be run in production.
            Add more files testMedia/barcodePDFs to test more barcodes.
        """
        print("\n\n START PRINT BARCODE TEST \n\n")
        directory = "testMedia/barcodePDFs"
        count = 0
        good_count = 0
        bad_count = 0

        def process_file(filename):
            file_path = Path(directory) / filename
            with Path.open(file_path, "rb") as f:
                pdf_bytes = f.read()
            barcode_values = barcode_utils.get_pages_and_barcodes_from_pdf(pdf_bytes)
            barcode = barcode_values[0][1] if barcode_values else None
            expected_reading = filename.split(".")[0]
            return expected_reading, barcode

        files = os.listdir(directory)
        count = len(files)

        """Most of the barcode heavy lifting is in the libraries and not using python.
                So threads speed it up quite a bit."""
        with ThreadPoolExecutor(max_workers=16) as executor:  # Adjust workers based on your CPU cores
            futures = {executor.submit(process_file, filename): filename for filename in files}

            for future in tqdm(as_completed(futures), total=count):
                expected_reading, barcode = future.result()
                if barcode == expected_reading:
                    good_count += 1
                else:
                    bad_count += 1
                    print("Failed: Barcode Read", barcode, "Expected", expected_reading)

        print("good count", good_count)
        print("bad count", bad_count)
        print(f"percentage of good barcodes: {good_count/count}")
