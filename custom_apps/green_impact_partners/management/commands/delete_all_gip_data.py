from django.core.management import BaseCommand
from progressbar import progressbar

from vantedge.tboss.models import TerminalBossClientCompany, Company, Product, Location, ServiceContract


class Command(BaseCommand):
    def handle(self, *args, **options):
        tenant = TerminalBossClientCompany.objects.get(name='Green Impact Partners')
        contracts = ServiceContract.objects.filter(dbreplication_client_company=tenant)
        companies = Company.objects.filter(dbreplication_client_company=tenant)
        products = Product.objects.filter(dbreplication_client_company=tenant)
        locations = Location.objects.filter(dbreplication_client_company=tenant)

        print(f'\nNumber of contracts to be deleted: {len(contracts)}')
        print(f'\nNumber of companies to be deleted: {len(companies)}')
        print(f'\nNumber of products to be deleted: {len(products)}')
        print(f'\nNumber of locations to be deleted: {len(locations)}')

        user_confirmation = input("THIS WILL DELETE ALL GIP DATA, PLEASE TYPE 'yes' TO PROCEED: ")

        if user_confirmation.upper() == 'YES':
            for contract in progressbar(contracts):
                contract.delete()

            print('\nDone deleting contracts')

            for company in progressbar(companies):
                company.delete()

            print('\nDone deleting companies')

            for product in progressbar(products):
                product.delete()

            print('\nDone deleting products')

            for location in progressbar(locations):
                location.delete()

            print('\nDone deleting locations')

        else:
            print('Cancelling request')
