import logging
import os
from io import BytesIO
from typing import Any
import numpy as np

from azure.core.exceptions import ResourceExistsError
from azure.storage.blob import BlobServiceClient
from custom_apps.green_impact_partners.models import TicketAttachmentLog
from django.conf import settings
from django.core.management import BaseCommand
from vant_dbreplication.models import DBReplicationServer
from vantedge.tboss.models import UnattachedFiles
from vantedge.tboss.models.proxies import Ticket
from vantedge.tboss.views.utils import barcode_utils
from vantedge.users.models import User
from vantedge.users.models import Company

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    """
    This script is used to attach the scanned BOLs to the tickets.
    - The script will look for the barcode in the pdf and attach the file to the ticket.
    - If the barcode is not found, the file will be attached to the unattached files.
    """
    def __init__(self):
        super().__init__()
        logging.getLogger("azure").setLevel(logging.WARNING)
        self.new_tickets_directory = 'attachment/green impact partners/new_scanned_BOLs'
        self.tickets_directory = 'attachment/green impact partners/attached_scanned_BOLs'
        self.unattached_files_path = 'attachment/green impact partners/unattached_scanned_BOLs'
        self.default_facility_name = 'Wheelhouse'
        self.company_name = 'Green Impact Partners'
        self.user_name = 'attachment_script'
        self.company_id = Company.objects.get(name=self.company_name).id
        self.server_mapping = {
                'MAY': 'Mayerthorpe',
                'GRA': 'Grande Cache',
                'GRC': 'Grande Cache',
                'CLA': 'Claresholm',
                'CYN': 'Cynthia',
                'RYC': 'Rycroft',
                'SWA': 'Swan Hills',
                'HEW': 'Heward Landfill',
            }
        # Setup the user and select the storage type
        self.user, _ = User.objects.get_or_create(name=self.user_name, username=self.user_name)
        if settings.DEFAULT_FILE_STORAGE == 'storages.backends.azure_storage.AzureStorage':
            self.use_azure = True
            if hasattr(settings, 'AZURE_CONNECTION_STRING') and hasattr(settings, 'AZURE_CONTAINER'):
                # Setup used when local azure storage is used.
                self.connection_string = settings.AZURE_CONNECTION_STRING
                self.container_name = settings.AZURE_CONTAINER
            elif (hasattr(settings, 'AZURE_ACCOUNT_NAME')
                  and hasattr(settings, 'AZURE_ACCOUNT_KEY')
                  and hasattr(settings, 'AZURE_CONTAINER')):
                self.connection_string = (
                    f'DefaultEndpointsProtocol=https;AccountName={settings.AZURE_ACCOUNT_NAME};'
                    f'AccountKey={settings.AZURE_ACCOUNT_KEY};'
                )
                self.container_name = settings.AZURE_CONTAINER
            else:
                raise ValueError("Azure connection Misconfigured. Please check the settings.py file.")
            self.blob_service_client = BlobServiceClient.from_connection_string(self.connection_string)
            self.container_client = self.blob_service_client.get_container_client(self.container_name)
        else:
            # Use local storage
            self.use_azure = False
            self.use_local = True
            if not os.path.exists(os.path.join(settings.MEDIA_ROOT, self.new_tickets_directory)):
                os.makedirs(os.path.join(settings.MEDIA_ROOT, self.new_tickets_directory))
            if not os.path.exists(os.path.join(settings.MEDIA_ROOT, self.tickets_directory)):
                os.makedirs(os.path.join(settings.MEDIA_ROOT, self.tickets_directory))
            if not os.path.exists(os.path.join(settings.MEDIA_ROOT, self.unattached_files_path)):
                os.makedirs(os.path.join(settings.MEDIA_ROOT, self.unattached_files_path))

    def handle(self, *args: Any, **options):
        self.change_content_type()

    def change_content_type(self):
        """
        Change the content type of the blob from 'application/pdf' to 'image/png'.
        """
        print("Changing content type")
        for blob in self.container_client.list_blobs(name_starts_with="attachment/green impact partners/"):
            print("loop", blob.name)
            if blob.content_settings.content_type == 'application/octet-stream' and ".pdf" in blob.name:
                print("Changing content", blob.name)
                blob.content_settings.content_type = 'application/pdf'
                self.container_client.get_blob_client(blob).set_http_headers(blob.content_settings)
