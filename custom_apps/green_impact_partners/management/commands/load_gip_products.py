import pandas as pd
import numpy as np
from progressbar import progressbar
import uuid
import logging
from django.core.management import BaseCommand, CommandError
from django.contrib.contenttypes.models import ContentType
from vant_calcs.uom import PriceChoices
from vantedge.tboss.models import Product, TerminalBossClientCompany, TBossProductServerAssociation
from vant_dbreplication.models import DBReplicationServer as Facility, DBReplicationLogEntry
from vant_dbreplication.utils import ReplicationHandler
from vantedge.tboss.models.proxies import Ticket, PeriodicLimit
from vantedge.tboss.models.schemas import ProductAndServiceTypes

class Command(BaseCommand):
    help = 'Load a product csv file into the database'

    def add_arguments(self, parser):
        parser.add_argument('--product_path', type=str, help="Path to the csv file", default='custom_apps/green_impact_partners/seed/GIPProducts.csv')
        parser.add_argument('--price_book_path', type=str, help="Path to the csv file", default='custom_apps/green_impact_partners/seed/GIPPriceBookItems.csv')
        parser.add_argument("--transfer_types_path", type=str, help="Path to the csv file", default='custom_apps/green_impact_partners/seed/GIPTransferTypes.csv')
        parser.add_argument("-verbose", type=bool, nargs="?", help="Display verbose output", default=False,)

    def handle(self, *args, **options):
        if not options.get("verbose"):
            logging.getLogger('vant_dbreplication').setLevel(logging.ERROR)
        path_to_csv = options.get('product_path')
        if not path_to_csv:
            raise CommandError("Please provide a path to the csv file")
        price_book_path = options.get('price_book_path')
        if not price_book_path:
            raise CommandError("Please provide a path to the csv file")
        trnsfer_types_path = options.get('transfer_types_path')
        if not trnsfer_types_path:
            raise CommandError("Please provide a path to the csv file")


        self.import_products(path_to_csv, price_book_path, trnsfer_types_path)
        self.test_product_import()
        self.print_product_tree()

        self.add_form_config()

    def add_form_config(self):
        print("FORM CONFIG: Custom Treating, default_solid_zero")
        product = Product.objects.get(name="Custom Treating")
        product.additional_data.update({"form_config": ["default_solid_zero"]})
        product.save()

    def test_product_import(self):
        NEW_PRICE_BOOK_SERVICES = 7
        NEW_PRICE_BOOK_PRODUCTS  = 6
        expected_service_type_count = 9
        expected_pricebook_service_count = 29 + NEW_PRICE_BOOK_SERVICES
        expected_pricebook_product_count = 18 + NEW_PRICE_BOOK_PRODUCTS
        expected_transfer_product_count = 5
        expected_transfer_type_count = 10
        expected_products_mayerthorpe = 122 + NEW_PRICE_BOOK_SERVICES + NEW_PRICE_BOOK_PRODUCTS
        expected_products_grande_cache = 106 + NEW_PRICE_BOOK_SERVICES + NEW_PRICE_BOOK_PRODUCTS
        expected_products_claresholm = 121 + NEW_PRICE_BOOK_SERVICES + NEW_PRICE_BOOK_PRODUCTS
        expected_products_cynthia = 106 + NEW_PRICE_BOOK_SERVICES + NEW_PRICE_BOOK_PRODUCTS
        expected_products_rycroft = 96 + NEW_PRICE_BOOK_SERVICES + NEW_PRICE_BOOK_PRODUCTS
        expected_products_swan_hills = 80 + NEW_PRICE_BOOK_SERVICES + NEW_PRICE_BOOK_PRODUCTS
        expected_products_heward_landfill = 81 + NEW_PRICE_BOOK_SERVICES + NEW_PRICE_BOOK_PRODUCTS
        service_type_count = Product.objects.filter(parent__name=ProductAndServiceTypes.PROCESSING_SERVICE).count()
        pricebook_service_count = Product.objects.filter(parent__name=ProductAndServiceTypes.PRICE_BOOK_SERVICE).count()
        pricebook_product_count = Product.objects.filter(parent__name=ProductAndServiceTypes.PRICE_BOOK_PRODUCT).count()
        transfer_type_count = Product.objects.filter(parent__name=ProductAndServiceTypes.TRANSFER_TYPES).count()
        transfer_product_count = Product.objects.filter(parent__name=ProductAndServiceTypes.TRANSFER_PRODUCTS).count()

        products_mayerthorpe = Facility.objects.filter(name = 'Mayerthorpe').first().product_associations.count()
        products_grande_cache = Facility.objects.filter(name = 'Grande Cache').first().product_associations.count()
        products_claresholm = Facility.objects.filter(name = 'Claresholm').first().product_associations.count()
        products_cynthia = Facility.objects.filter(name = 'Cynthia').first().product_associations.count()
        products_rycroft = Facility.objects.filter(name = 'Rycroft').first().product_associations.count()
        products_swan_hills = Facility.objects.filter(name = 'Swan Hills').first().product_associations.count()
        products_heward_landfill = Facility.objects.filter(name = 'Heward Landfill').first().product_associations.count()
        if service_type_count != expected_service_type_count:
            print("Expected {} service types, found {}".format(expected_service_type_count, service_type_count))
        else:
            print("PASS 1")
        if products_mayerthorpe != expected_products_mayerthorpe:
            print("Expected {} products for Mayerthorpe, found {}".format(expected_products_mayerthorpe, products_mayerthorpe))
        else:
            print("PASS 2")
        if products_grande_cache != expected_products_grande_cache:
            print("Expected {} products for Grande Cache, found {}".format(expected_products_grande_cache, products_grande_cache))
        else:
            print("PASS 3")
        if products_claresholm != expected_products_claresholm:
            print("Expected {} products for Claresholm, found {}".format(expected_products_claresholm, products_claresholm))
        else:
            print("PASS 4")
        if products_cynthia != expected_products_cynthia:
            print("Expected {} products for Cynthia, found {}".format(expected_products_cynthia, products_cynthia))
        else:
            print("PASS 5")
        if products_rycroft != expected_products_rycroft:
            print("Expected {} products for Rycroft, found {}".format(expected_products_rycroft, products_rycroft))
        else:
            print("PASS 6")
        if products_swan_hills != expected_products_swan_hills:
            print("Expected {} products for Swan Hills, found {}".format(expected_products_swan_hills, products_swan_hills))
        else:
            print("PASS 7")
        if products_heward_landfill != expected_products_heward_landfill:
            print("Expected {} products for Heward Landfill, found {}".format(expected_products_heward_landfill, products_heward_landfill))
        else:
            print("PASS 8")
        if pricebook_service_count != expected_pricebook_service_count:
            print("Expected {} price book services, found {}".format(expected_pricebook_service_count, pricebook_service_count))
        else:
            print("PASS 9")
        if pricebook_product_count != expected_pricebook_product_count:
            print("Expected {} price book products, found {}".format(expected_pricebook_product_count, pricebook_product_count))
        else:
            print("PASS 10")
        if transfer_type_count != expected_transfer_type_count:
            print("Expected {} transfer types, found {}".format(expected_transfer_type_count, transfer_type_count))
        else:
            print("PASS 11")
        if transfer_product_count != expected_transfer_product_count:
            print("Expected {} transfer products, found {}".format(expected_transfer_product_count, transfer_product_count))
        else:
            print("PASS 12")

        print("Done testing import")

    def import_products(self, path_to_csv, price_book_path, trnsfer_types_path):
        print("Start load GIP products")
        handler = ReplicationHandler()
        products = {}
        facilities = self.sites
        for facility in facilities:
            facility_name = facility.name
            product_config = handler.get_replication_config(Product, facility)
            if not product_config:
                raise CommandError("No product configuration for facility {}".format(facility_name))

            products[facility_name] = { "facility": facility, "config": product_config }


        defaults = {
            "dbreplication_client_company": self.tenant,
            "created_by_server": self.wheelhouse,
            "modified_by_server": self.wheelhouse,
            "dbreplication_config": product_config
        }

        print("Importing processing products...")
        # These services are common to all facilities
        processing_service_types = []
        processing_service, created = Product.objects.get_or_create(name=ProductAndServiceTypes.PROCESSING_SERVICE, code=ProductAndServiceTypes.PROCESSING_SERVICE, defaults=defaults)
        processing_service_types.append(processing_service)

        # Water Disposal
        water_disposal = Product.objects.create(name="Water Disposal", code="Water Disposal", parent=processing_service, **defaults)
        processing_service_types.append(water_disposal)
        water = Product.objects.create(name="Water-Produced (including brine solutions)", code="WATER", parent=water_disposal, **defaults)
        processing_service_types.append(water)

        # Lube Oil
        lube_oil = Product.objects.create(name="Lube Oil", code="Lube Oil", parent=processing_service, **defaults)
        processing_service_types.append(lube_oil)
        lube = Product.objects.create(name="Sludge - Hydrocarbon (SLGHYD-2)", code="SLGHYD_2", parent=lube_oil, **defaults)
        processing_service_types.append(lube)

        # Custom Treating
        custom_treating = Product.objects.create(name="Custom Treating", code="SRVCT", parent=processing_service, **defaults)
        processing_service_types.append(custom_treating)
        emulstion = Product.objects.create(name="Crude Oil and Water Emulsion", code="EMULSION", parent=custom_treating, **defaults)
        processing_service_types.append(emulstion)

        # Oil Terminaling
        oil_terminaling = Product.objects.create(name="Oil Terminaling", code="Oil Terminaling", parent=processing_service, **defaults)
        processing_service_types.append(oil_terminaling)
        dry_oil = Product.objects.create(name="Dry Oil", code="OIL", parent=oil_terminaling, **defaults)
        processing_service_types.append(dry_oil)

        # Non OilField
        non_oilfield = Product.objects.create(name="Non OilField", code="Non OilField", parent=processing_service, **defaults)
        processing_service_types.append(non_oilfield)
        nonofd = Product.objects.create(name="Non-Oilfield", code="NONOFD", parent=non_oilfield, **defaults)
        processing_service_types.append(nonofd)

        # Spend Scavenger Waste Water Disposal
        spend_scavenger = Product.objects.create(name="Spent Scavenger Waste Water Disposal", code="Spent Scavenger Waste Water Disposal", parent=processing_service, **defaults)
        processing_service_types.append(spend_scavenger)
        swtlq = Product.objects.create(name="SWEETENING AGENTS (LIQUIDS)", code="SWTLIQ", parent=spend_scavenger, **defaults)
        processing_service_types.append(swtlq)

        # Butane
        butane = Product.objects.create(name="Butane", code="Butane", parent=processing_service, **defaults)
        processing_service_types.append(butane)
        c4sp = Product.objects.create(name="Butane", code="C4-SP", parent=butane, **defaults)
        processing_service_types.append(c4sp)

        # IF FACILITY NAME IS CHANGED, THIS LIST MUST BE UPDATED
        facility_names = [ 'Mayerthorpe', 'Grande Cache', 'Claresholm', 'Cynthia', 'Rycroft', 'Claresholm', 'Swan Hills', 'Heward Landfill' ]

        for processing_service_type in processing_service_types:
            for facility_name in facility_names:
                facility = products[facility_name]["facility"]
                TBossProductServerAssociation.objects.get_or_create(entity=processing_service_type, server=facility)

        # These processing services are site specific and are loaded from the csv
        df = pd.read_csv(path_to_csv, sep=',')
        Product.objects.get_or_create(name="Waste Treatment, Disposal and Oil Recovery", code="Waste Treatment, Disposal and Oil Recovery", parent=processing_service, defaults=defaults)
        Product.objects.get_or_create(name="Hydrovac Waste Processing and Disposal", code="Hydrovac Waste Processing and Disposal", parent=processing_service, defaults=defaults)
        Product.objects.get_or_create(name="Dry Solids Disposal", code="Dry Solids Disposal", parent=processing_service, defaults=defaults)
        for index, row in progressbar(df.iterrows()):
            for colIndex, f in enumerate(df.columns):
                facility_name = f.split('.')[0]
                facility = products[facility_name]["facility"]
                product_config = products[facility_name]["config"]
                if pd.isna(row[colIndex]):
                    continue
                product_kwargs = { "defaults": defaults }

                if index > 0:
                    name = code = row[colIndex].strip().rsplit('(', 1)
                    if len(name) > 1:
                        name, code = name
                    else:
                        name = code = name[0]
                        print(name)
                    code = code.replace(')', '')
                    parent = products[facility_name]["product_group"]
                else:
                    name = code = row[colIndex].strip()
                    parent = None

                product_kwargs["defaults"]["name"] = name.strip()
                product_kwargs["code"] = code.strip()
                product_kwargs["defaults"]["parent"] = parent

                # create the product
                product, created = Product.objects.get_or_create(**product_kwargs)

                if index == 0:
                    products[facility_name]["product_group"] = product

                TBossProductServerAssociation.objects.get_or_create(entity=product, server=facility)

        print("Importing price book items...")
        defaults = {
            "dbreplication_client_company": self.tenant,
            "created_by_server": self.wheelhouse,
            "modified_by_server": self.wheelhouse,
            "dbreplication_config": product_config
        }
        price_book_service = Product.objects.create(name=ProductAndServiceTypes.PRICE_BOOK_SERVICE, code=ProductAndServiceTypes.PRICE_BOOK_SERVICE, **defaults)
        price_book_product = Product.objects.create(name=ProductAndServiceTypes.PRICE_BOOK_PRODUCT, code=ProductAndServiceTypes.PRICE_BOOK_PRODUCT, **defaults)
        # add these 2 products to all facilities
        for facility_name in facility_names:
            facility = products[facility_name]["facility"]
            TBossProductServerAssociation.objects.get_or_create(entity=price_book_service, server=facility)
            TBossProductServerAssociation.objects.get_or_create(entity=price_book_product, server=facility)


        df = pd.read_csv(price_book_path, sep=',')
        # Columns: NAME, UOM, PRICE, PRODUCT or SERVICE
        for index, row in progressbar(df.iterrows()):
            name = row['NAME']
            uom = row['UOM']
            price = 0.00
            solid_low = float(row['LOW SOLID'])
            solid_high = float(row['HIGH SOLID'])
            water_low = float(row['LOW WATER'])
            water_high = float(row['HIGH WATER'])
            sour = row['SOUR']
            association_name = row['SERVICE TYPE']
            # Ensure that water is a number
            if pd.isna(water_low):
                water_low = 0
            if pd.isna(water_high):
                water_high = 0
            if pd.isna(solid_low):
                solid_low = 0
            if pd.isna(solid_high):
                solid_high = 0
            if pd.isna(row['PRICE']):
                price = 0.00
            if pd.isna(row['SOUR']):
                sour = 0
            product_or_service = row['PRODUCT or SERVICE']
            if product_or_service == 'Product':
                product = price_book_product
            else:
                product = price_book_service
            product, created = Product.objects.get_or_create(name=name, code=name, parent=product, **defaults)
            product.data = { "price": price, "price_unit": uom }
            product.additional_data = {"water_percentage_min": float(water_low),
                                       "water_percentage_max": float(water_high),
                                       "solid_percentage_min": float(solid_low),
                                       "solid_percentage_max": float(solid_high),
                                       "sour": bool(sour) }
            product.save()
            # associate the product with all facilities
            for facility_name in facility_names:
                facility = products[facility_name]["facility"]
                TBossProductServerAssociation.objects.get_or_create(entity=product, server=facility)

            if is_valid(association_name):
                associated_prod = Product.objects.get(name=association_name, parent=processing_service)
                product.associations.add(associated_prod)
            product.save()

        print("Importing transfer types...")
        transfer_types = Product.objects.create(name=ProductAndServiceTypes.TRANSFER_TYPES, code=ProductAndServiceTypes.TRANSFER_TYPES, **defaults)
        # add these products to all facilities
        for facility_name in facility_names:
            facility = products[facility_name]["facility"]
            TBossProductServerAssociation.objects.get_or_create(entity=transfer_types, server=facility)
        df = pd.read_csv(trnsfer_types_path, sep=',')
        # Columns: NAME
        for index, row in progressbar(df.iterrows()):
            name = row['NAME']
            product = Product.objects.create(name=name, code=name, parent=transfer_types, **defaults)
            # associate the product with all facilities
            for facility_name in facility_names:
                facility = products[facility_name]["facility"]
                TBossProductServerAssociation.objects.get_or_create(entity=product, server=facility)


        print("Import transfer products...")
        transfer_products = Product.objects.create(name=ProductAndServiceTypes.TRANSFER_PRODUCTS, code=ProductAndServiceTypes.TRANSFER_PRODUCTS, **defaults)
        # add these products to all facilities
        for facility_name in facility_names:
            facility = products[facility_name]["facility"]
            TBossProductServerAssociation.objects.get_or_create(entity=transfer_products, server=facility)

        print("Importing transfer products...")
        # These products are common to all facilities
        transfer_product_types = []
        oil = Product.objects.create(name="Oil", code="Oil", parent=transfer_products, **defaults)
        transfer_product_types.append(oil)
        effluent_water = Product.objects.create(name="Effluent Water", code="Effluent Water", parent=transfer_products, **defaults)
        transfer_product_types.append(effluent_water)
        fresh_water = Product.objects.create(name="Fresh Water", code="Fresh Water", parent=transfer_products, **defaults)
        transfer_product_types.append(fresh_water)
        solid = Product.objects.create(name="Solid", code="Solid", parent=transfer_products, **defaults)
        transfer_product_types.append(solid)
        landfill = Product.objects.create(name="Landfill", code="Landfill", parent=transfer_products, **defaults)
        transfer_product_types.append(landfill)

        for transfer_product_type in transfer_product_types:
            for facility_name in facility_names:
                facility = products[facility_name]["facility"]
                TBossProductServerAssociation.objects.get_or_create(entity=transfer_product_type, server=facility)

        print("Done loading GIP products")

    def print_product_tree(self):
        with open("GIPProductTree.txt", "w") as f:
            f.write("Processing Service\n")
            for child in Product.objects.filter(name=ProductAndServiceTypes.PROCESSING_SERVICE).first().children.all():
                f.write("\t" + child.name + "\n")
                for child2 in child.children.all():
                    f.write("\t\t" + child2.name + "\n")

            f.write("\nTransfer Types\n")
            for child in Product.objects.filter(name="Transfer Types").first().children.all():
                f.write("\t" + child.name + "\n")

            f.write("\nTransfer Products\n")
            for child in Product.objects.filter(name="Transfer Products").first().children.all():
                f.write("\t" + child.name + "\n")

            f.write("\nPrice Book Service's\n")
            for child in Product.objects.filter(name="Price Book Service").first().children.all():
                f.write("\t" + child.name + "\n")

            f.write("\nPrice Book Product's\n")
            for child in Product.objects.filter(name="Price Book Product").first().children.all():
                f.write("\t" + child.name + "\n")

            f.write("\n\nMayerthorpe (All associations)\n")
            for child in Facility.objects.filter(name='Mayerthorpe').first().product_associations.all():
                f.write("\t" + child.entity.name + "\n")

            f.write("\nGrande Cache (All associations)\n")
            for child in Facility.objects.filter(name='Grande Cache').first().product_associations.all():
                f.write("\t" + child.entity.name + "\n")

            f.write("\nClaresholm (All associations)\n")
            for child in Facility.objects.filter(name='Claresholm').first().product_associations.all():
                f.write("\t" + child.entity.name + "\n")

            f.write("\nCynthia (All associations)\n")
            for child in Facility.objects.filter(name='Cynthia').first().product_associations.all():
                f.write("\t" + child.entity.name + "\n")

            f.write("\nRycroft (All associations)\n")
            for child in Facility.objects.filter(name='Rycroft').first().product_associations.all():
                f.write("\t" + child.entity.name + "\n")

            f.write("\nSwan Hills (All associations)\n")
            for child in Facility.objects.filter(name='Swan Hills').first().product_associations.all():
                f.write("\t" + child.entity.name + "\n")

        print("Please check GIPProductTree.txt for the product tree")