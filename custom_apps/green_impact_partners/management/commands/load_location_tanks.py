import pandas as pd
from django.core.management import BaseCommand, CommandError
from django.contrib.contenttypes.models import ContentType
from vantedge.tboss.models import Location, Company, CompanyRole, TerminalBossClientCompany
from vant_dbreplication.models import DBReplicationServer as Facility, DBReplicationLogEntry
from vant_dbreplication.utils import ReplicationHandler
from vant_tboss.schemas import AddressData, LandDescriptionTypes
from vant_tboss.models import LocationData
from progressbar import progressbar
import uuid

class Command(BaseCommand):
    help = 'Load GIP tanks (containers) from csv file'

    def add_arguments(self, parser):
        parser.add_argument('--path', type=str, help="Path to the csv file", default='custom_apps/green_impact_partners/seed/GIPLocationTanks.csv')

    def handle(self, *args, **options):
        path_to_csv = options.get('path')
        if not path_to_csv:
            raise CommandError("Please provide a path to the csv file")
        tenant, created = TerminalBossClientCompany.objects.get_or_create(name="Green Impact Partners")
        handler = ReplicationHandler()
        # disable replication flag for all facilities
        facility = Facility.objects.get(name="Wheelhouse", dbreplication_client_company=tenant)
        location_config = handler.get_replication_config(Location, facility)
        location_data = LocationData()
        tank_parent, created = Location.objects.get_or_create(
                name="Green Impact Partners Tank",
                code="tank",
                dbreplication_client_company=tenant,
                created_by_server = facility,
                modified_by_server = facility,
                parent=None,
                dbreplication_config=location_config,
                enabled=True,
                data=location_data,
                location_type=Location.LocationType.CONTAINER,
            )

        df = pd.read_csv(path_to_csv, sep=',')
        print("Creating (or getting) Tanks")
        for index, row in df.iterrows():
            print("\tFacility: ", row['Facility'], " Tank: ", row['Tank'])
            location_config = handler.get_replication_config(Location, facility)
            Location.objects.get_or_create(
                name=row['Tank'],
                code=row['Tank'],
                dbreplication_client_company=tenant,
                created_by_server = tenant.dbreplicationserver_set.get(name=row['Facility']),
                modified_by_server = tenant.dbreplicationserver_set.get(name=row['Facility']),
                parent=tank_parent,
                dbreplication_config=location_config,
                enabled=True,
                data=location_data,
                location_type=Location.LocationType.CONTAINER,
            )