import pandas as pd
from django.core.management import BaseCommand, CommandError
from django.contrib.contenttypes.models import ContentType
from vantedge.tboss.models import Location, Company, CompanyRole, TerminalBossClientCompany
from vant_dbreplication.models import DBReplicationServer as Facility, DBReplicationLogEntry
from vant_dbreplication.utils import ReplicationHandler
from vant_tboss.schemas import AddressData, LandDescriptionTypes
from vant_tboss.models import LocationData
from progressbar import progressbar
import uuid

class Command(BaseCommand):
    help = 'Load a locations csv file into the database'

    def add_arguments(self, parser):
        parser.add_argument('--path', type=str, help="Path to the csv file")
        parser.add_argument(
            "-verbose",
            type=bool,
            nargs="?",
            help="Display verbose output",
            default=False,
        )

    def handle(self, *args, **options):
        path_to_csv = options.get('path')
        if not path_to_csv:
            raise CommandError("Please provide a path to the csv file")
        tenant, created = TerminalBossClientCompany.objects.get_or_create(name="Green Impact Partners")
        handler = ReplicationHandler()

        # disable replication flag for all facilities
        Facility.objects.filter(dbreplication_client_company=tenant).update(replication_flag=False)

        # delete all previously imported data
        Location.objects.filter(dbreplication_client_company=tenant).delete()
        Company.objects.filter(dbreplication_client_company=tenant).delete()
        location_content_type = ContentType.objects.get_for_model(Location)
        company_content_type = ContentType.objects.get_for_model(Company)
        DBReplicationLogEntry.objects.filter(content_type__in=[location_content_type, company_content_type]).delete()
        
        # enable replication flag for all facilities
        Facility.objects.filter(dbreplication_client_company=tenant).update(replication_flag=True)
        
        customers = {}
        generators = {}
        batteries = {}
        wells = {}
        facility = Facility.objects.get(name="Wheelhouse", dbreplication_client_company=tenant)
        facility_name = facility.name
        location_config = handler.get_replication_config(Location, facility)
        if not location_config:
            print("No location configuration for facility {}".format(facility_name))
            return
        customers[facility_name] = { c.name: c for c in Company.objects.filter(roles__name__in=[CompanyRole.CompanyRoleChoices.CUSTOMER]) }
        generators[facility_name] = { c.name: c for c in Company.objects.filter(roles__name__in=[CompanyRole.CompanyRoleChoices.PRODUCER]) }
        batteries[facility_name] = {}
        wells[facility_name] = {}
        
        update_location_data = []
            
        df = pd.read_csv(path_to_csv, sep=',')
        for index, row in progressbar(df.iterrows()):
            
            # skip header
            if (index == 0):
                continue
            
            if options.get("verbose", False):
                print(row)

            well_name, battery_name, customer_name, generator_name, job_facility_name = row
            
            if not well_name or pd.isna(well_name):
                print("Invalid well name {} for row {}".format(well_name, index))
                continue
            else:
                well_name = well_name.strip()
            
            if not battery_name or pd.isna(battery_name):
                print("Invalid battery name {} for row {}".format(battery_name, index))
                continue
            else:
                battery_name = battery_name.strip()
            
            if not customer_name or pd.isna(customer_name):
                print("Invalid battery name {} for row {}".format(customer_name, index))
                continue
            else:
                customer_name = customer_name.strip()
            
            if not generator_name or pd.isna(generator_name):
                print("Invalid generator name {} for row {}".format(generator_name, index))
                continue
            else:
                generator_name = generator_name.strip()
            
            # if facility_name in facilities:
            #     facility = facilities[facility_name]
            # else:
            #     facility, facility_created = Facility.objects.get_or_create(name=facility_name, dbreplication_client_company=tenant)
            #     facilities[facility_name] = facility

            if customer_name in customers[facility_name]:
                customer = customers[facility_name][customer_name]
            else:
                company_config = handler.get_replication_config(Company, facility)
                companyrole_config = handler.get_replication_config(CompanyRole, facility)
                customer, customer_facility_created = Company.objects.get_or_create(name=customer_name, created_by_server=facility, enabled=True, modified_by_server=facility, dbreplication_client_company=tenant, dbreplication_config=company_config, defaults={"code": uuid.uuid4()})
                customer_role, customer_role_created = CompanyRole.objects.get_or_create(name=CompanyRole.CompanyRoleChoices.CUSTOMER, dbreplication_client_company=tenant, created_by_server=facility, modified_by_server=facility, dbreplication_config=companyrole_config)
                customer.roles.add(customer_role)
                customers[facility_name][customer_name] = customer

            if generator_name in generators[facility_name]:
                generator = generators[facility_name][generator_name]
            else:
                company_config = handler.get_replication_config(Company, facility)
                companyrole_config = handler.get_replication_config(CompanyRole, facility)
                generator, facility_created = Company.objects.get_or_create(name=generator_name, created_by_server=facility, modified_by_server=facility, dbreplication_client_company=tenant, enabled=True, dbreplication_config=company_config, defaults={"code": uuid.uuid4()})
                generator_role, generator_role_created = CompanyRole.objects.get_or_create(name=CompanyRole.CompanyRoleChoices.PRODUCER, dbreplication_client_company=tenant, created_by_server=facility, modified_by_server=facility, dbreplication_config=companyrole_config)
                generator.roles.add(generator_role)
                generators[facility_name][generator_name] = generator

            if battery_name in batteries[facility_name]:
                battery = batteries[facility_name][battery_name]
            else:
                location_config = handler.get_replication_config(Location, facility)
                location_data = LocationData(address=AddressData(land_description_type=LandDescriptionTypes.BATTERY))
                battery, battery_created = Location.objects.get_or_create(name=battery_name, code=battery_name, dbreplication_client_company=tenant, enabled=True, created_by_server=facility, modified_by_server=facility, dbreplication_config=location_config, location_type=Location.LocationType.FACILITY, data=location_data)
                batteries[facility_name][battery_name] = battery
            
            if well_name in wells[facility_name]:
                well = wells[facility_name][well_name]
            else:
                location_config = handler.get_replication_config(Location, facility)
                # adding the customer set should be part of the contract import but we will store it on each location additional_data for now
                well_data = { 'customer_set': [str(customer.id)] }
                location_data = LocationData(address=AddressData(land_description_type=LandDescriptionTypes.UNIQUE_WELL_IDENTIFIER))
                well, well_created = Location.objects.get_or_create(
                    name=well_name,
                    code=well_name,
                    defaults={ 
                        'additional_data': well_data,
                        'dbreplication_client_company': tenant,
                        'created_by_server': facility,
                        'modified_by_server': facility,
                        'parent': battery,
                        'dbreplication_config': location_config,
                        'enabled': True,
                        'data': location_data,
                        'location_type': Location.LocationType.FACILITY
                    }
                )
                if not well_created:
                    well.additional_data.setdefault('customer_set', [])
                    well.additional_data['customer_set'].append(str(customer.id))
                    update_location_data.append(well)
                    
                wells[facility_name][well_name] = well

            # well.company_set.set([customer, generator])
            
            # move the customer to the data to enforce only one company in the generator role
            well.company_set.set([generator])
        
        # use bulk update to update the customer_set
        Location.objects.bulk_update(update_location_data, ['additional_data'], 2000)
