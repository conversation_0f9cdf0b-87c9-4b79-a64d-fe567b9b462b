import pandas as pd
import uuid
from django.core.management import BaseCommand, CommandError
from django.contrib.contenttypes.models import ContentType
from vant_calcs.uom import PriceChoices
from vantedge.tboss.models import TerminalBossClientCompany, Product
from vant_dbreplication.models import DBReplicationServer as Facility, DBReplicationLogEntry
from vant_dbreplication.utils import ReplicationHandler
from progressbar import progressbar


class Command(BaseCommand):
    help = 'Load a pricebook csv file into the database'

    def add_arguments(self, parser):
        parser.add_argument('--path', type=str, help="Path to the csv file")
        parser.add_argument(
            "-verbose",
            type=bool,
            nargs="?",
            help="Display verbose output",
            default=False,
        )

    def handle(self, *args, **options):
        path_to_csv = options.get('path')
        if not path_to_csv:
            raise CommandError("Please provide a path to the csv file")
        tenant, created = TerminalBossClientCompany.objects.get_or_create(name="Green Impact Partners")
        handler = ReplicationHandler()
        
        tenant_facilities = Facility.objects.filter(dbreplication_client_company=tenant)

        # disable replication flag for all facilities
        tenant_facilities.update(replication_flag=False)

        # delete all previously imported data
        # Product.objects.filter(dbreplication_client_company=tenant).delete()
        product_content_type = ContentType.objects.get_for_model(Product)
        # DBReplicationLogEntry.objects.filter(content_type__in=[product_content_type]).delete()
        
        # enable replication flag for all facilities
        tenant_facilities.update(replication_flag=True)
        
        facility = Facility.objects.get(name="Wheelhouse", dbreplication_client_company=tenant)
        facility_name = facility.name
        price_book_config = handler.get_replication_config(Product, facility)
        if not price_book_config:
            print("No price book item configuration for facility {}".format(facility_name))
            return
        wheelhouse = tenant_facilities.filter(name='Wheelhouse').first()

        df = pd.read_csv(path_to_csv, sep=',')
        uoms = {
            "m³": PriceChoices.CAD_per_m3,
            "each": PriceChoices.CAD_per_item
        }
        product_config = handler.get_replication_config(Product, wheelhouse)
        
        defaults = {
            "dbreplication_client_company": tenant,
            "created_by_server": wheelhouse,
            "modified_by_server": wheelhouse,
            "dbreplication_config": product_config
        }
        
        pricebook_parent, created = Product.objects.get_or_create(
            name="Pricebook Items",
            code="Pricebook Items",
            defaults=defaults
        )
        
        for index, row in progressbar(df.iterrows()):
            
            # skip header
            if (index == 0):
                continue
            
            if options.get("verbose", False):
                print(row)
            
            name, description, uom, default_price, item_type, enabled = row

            if not name or pd.isna(name):
                print(f"Skipping row with missing name: {row}")
                continue
            
            description = description if description and not pd.isna(description) else ""
            default_price = default_price if default_price and not pd.isna(default_price) else 0
            item_type = item_type if item_type and not pd.isna(item_type) else "Product"
            parent, parent_created = Product.objects.get_or_create(**{
                "name": item_type,
                "code": item_type,
                "parent": pricebook_parent,
                "defaults": defaults
            })
            unit_of_measurement = uoms.get(uom, PriceChoices.CAD_per_item)
            product_data = Product.data_schema()
            product_data.price = default_price
            product_data.price_unit = unit_of_measurement
            product, product_created = Product.objects.get_or_create(
                name=name.strip(),
                code=name.strip(),
                parent=parent,
                enabled=enabled == "Active",
                dbreplication_client_company=tenant,
                dbreplication_config=product_config,
                created_by_server=wheelhouse,
                modified_by_server=wheelhouse,
                data=product_data
            )
