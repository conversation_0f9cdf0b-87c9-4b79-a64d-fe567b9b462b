# Generated by Django 4.2 on 2024-06-20 22:14

from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import model_utils.fields
import vantedge.autocomplete.models


class Migration(migrations.Migration):

    dependencies = [
        ("attachment", "0009_alter_attachment_object_id"),
        ("tboss", "0030_terminalbossclientcompany_tboss_company"),
        ("users", "0026_company_unique_company_name"),
        ("green_impact_partners", "0002_ticketgipinventory"),
    ]

    operations = [
        migrations.CreateModel(
            name="TicketAttachmentLog",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "created",
                    model_utils.fields.AutoCreatedField(
                        default=django.utils.timezone.now,
                        editable=False,
                        verbose_name="created",
                    ),
                ),
                (
                    "modified",
                    model_utils.fields.AutoLastModifiedField(
                        default=django.utils.timezone.now,
                        editable=False,
                        verbose_name="modified",
                    ),
                ),
                ("name", models.CharField(max_length=150)),
                ("code", models.CharField(max_length=100)),
                ("facility", models.CharField(blank=True, max_length=255, null=True)),
                ("message", models.TextField(blank=True, null=True)),
                ("success", models.BooleanField(default=False)),
                (
                    "attachment",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="attachment.attachment",
                    ),
                ),
                (
                    "company",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE, to="users.company"
                    ),
                ),
                (
                    "ticket",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="tboss.ticket",
                    ),
                ),
            ],
            options={
                "verbose_name": "TicketAttachmentLog",
                "verbose_name_plural": "TicketAttachmentLogs",
                "ordering": ["name"],
                "abstract": False,
            },
            bases=(models.Model, vantedge.autocomplete.models.AutocompleteMixin),
        ),
        migrations.AddConstraint(
            model_name="ticketattachmentlog",
            constraint=models.UniqueConstraint(
                fields=("code",),
                name="green_impact_partners_ticketattachmentlog_unique",
            ),
        ),
    ]
