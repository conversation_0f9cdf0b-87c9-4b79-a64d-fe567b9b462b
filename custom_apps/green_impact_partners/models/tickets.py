import functools
import logging
from datetime import time, datetime
from zoneinfo import ZoneInfo, ZoneInfoNotFoundError

import numpy as np
from django.core.exceptions import ValidationError
from django.core.management import call_command
from django.db.models import Q, Sum
from django.utils import timezone
from django_q.tasks import async_task
from vant_calcs.uom import UOM
from vant_dbreplication.models import DBReplicationServer
from vant_dbreplication.utils import ReplicationHandler
from vant_utils.dotdict import DotDict
from vantedge.tboss.models import (
    ProductAndServiceTypes,
    ServiceContract,
    TerminalBossClientCompany,
)
from vantedge.tboss.models.proxies import Company, Location, Ticket, TicketEntry
from vantedge.tboss.utils import no_sync_signals_decorator
from vantedge.util.classes import get_field_by_id

from ..schemas import GIPTicketData, GIPTicketStatus, GIPTicketTypes, StatusHistory

logger = logging.getLogger(__name__)


class GIPHelpers:
    @staticmethod
    def company_name():
        return "Green Impact Partners"

    @functools.cache
    @staticmethod
    def dbreplication_client_company_id():
        return TerminalBossClientCompany.objects.get(name=GIPHelpers.company_name()).id

    @functools.cache
    @staticmethod
    def wh_gip():
        from vantedge.users.models import Company as WheelhouseCompany

        wh_company = WheelhouseCompany.objects.get(name=GIPHelpers.company_name())
        return wh_company.terminalboss

    @functools.cache
    @staticmethod
    def tboss_gip():
        return GIPHelpers.wh_gip().tboss_company

    @functools.cache
    @staticmethod
    def server_gip():
        return DBReplicationServer.objects.get(
            name="Wheelhouse", dbreplication_client_company=GIPHelpers.wh_gip()
        )

    @staticmethod
    def inv_adj_product():
        return GIPHelpers.wh_gip().product_set.get(code="INV-ADJ")

    @staticmethod
    def inv_adj_oil_product():
        return GIPHelpers.wh_gip().product_set.get(code="INV-ADJ-OIL")

    @staticmethod
    def inv_adj_water_product():
        return GIPHelpers.wh_gip().product_set.get(code="INV-ADJ-WATER")

    @staticmethod
    def inv_adj_solid_product():
        return GIPHelpers.wh_gip().product_set.get(code="INV-ADJ-SOLID")

    @staticmethod
    def oil_product():
        return GIPHelpers.wh_gip().product_set.get(
            parent__code=ProductAndServiceTypes.TRANSFER_PRODUCTS, code="Oil"
        )

    @staticmethod
    def water_product():
        return GIPHelpers.wh_gip().product_set.get(
            parent__code=ProductAndServiceTypes.TRANSFER_PRODUCTS, code="Water"
        )

    @staticmethod
    def fresh_water_product():
        return GIPHelpers.wh_gip().product_set.get(
            parent__code=ProductAndServiceTypes.TRANSFER_PRODUCTS, code="Fresh Water"
        )

    @staticmethod
    def solid_product():
        return GIPHelpers.wh_gip().product_set.get(
            parent__code=ProductAndServiceTypes.TRANSFER_PRODUCTS, code="Solid"
        )


class TicketGIPBase(Ticket):
    can_handle_company_names = [GIPHelpers.company_name()]
    data_schema = GIPTicketData
    default_ticket_type = None

    class Meta(Ticket.Meta):
        proxy = True

    def get_real_instance_class(self):
        return self.__class__

    def entry_kwargs(self):
        replication_handler = ReplicationHandler()
        ticket_entry_config = replication_handler.get_replication_config(
            TicketEntry, self.created_by_server
        )
        server = self.created_by_server
        result = {
            "dbreplication_client_company": GIPHelpers.wh_gip(),
            "dbreplication_config": ticket_entry_config,
            "created_by_server": server,
        }
        return result

    def update_status_history(self):
        if self.data.additional_fields.state == GIPTicketStatus.DRAFT:
            self.status = "dr"
        if self.data.additional_fields.state in [
            GIPTicketStatus.SUBMITTED,
            GIPTicketStatus.APPROVED,
            GIPTicketStatus.INVOICED,
            GIPTicketStatus.COMPLETED,
        ]:
            self.status = "po"
        if self.data.additional_fields.state == GIPTicketStatus.VOID:
            self.status = "vo"

        if self.data.additional_fields.status_history != []:
            if isinstance(self.data.additional_fields.status_history[-1], dict):
                self.data.additional_fields.status_history[-1] = DotDict(
                    self.data.additional_fields.status_history[-1]
                )
            if (
                self.data.additional_fields.state
                == self.data.additional_fields.status_history[-1].status
            ):
                return

        timestamp = datetime.now(self.get_timezone())

        this_history = StatusHistory(
            status=self.data.additional_fields.state,
            timestamp=timestamp.strftime("%Y-%m-%d %H:%M"),
            user=self.modified_by.name,
        )
        if this_history.status == GIPTicketStatus.APPROVED:
            self.data.additional_fields.approval_date = this_history.timestamp
            self.data.additional_fields.approved_by = this_history.user
        self.data.additional_fields.status_history.append(this_history)
        if len(self.data.additional_fields.status_history) > 10:
            self.data.additional_fields.status_history.pop(0)

    def create_entry(self, **kwargs):
        self.entry_set.create(**self.entry_kwargs(), **kwargs)

    def update_ticket_envelope(self):
        super().update_ticket_envelope()
        # TODO: Would be better not have the if statement here.. Fine for now.
        if not self.data.additional_fields.user_id and self.user_id:
            self.data.additional_fields.user_id = self.user_id

        self.user_id = self.data.additional_fields.user_id
        self.ticket_type = self.can_handle_ticket_types[0] or None

        if not self.data.location_data.facility_id:
            server_name = self.created_by_server.name
            code = f"GIP_{server_name.upper()}"

            self.data.location_data.facility_id = Location.objects.get(
                dbreplication_client_company__name=GIPHelpers.company_name(), code=code
            ).id

    def update_data(self):
        party_data = self.data.party_data
        party_data.carrier_name = get_field_by_id(Company, party_data.carrier_id)
        party_data.customer_name = get_field_by_id(Company, party_data.customer_id)
        party_data.producer_name = get_field_by_id(Company, party_data.producer_id)

        location_data = self.data.location_data
        location_data.origin_name = get_field_by_id(Location, location_data.origin_id)
        location_data.destination_name = get_field_by_id(
            Location, location_data.destination_id
        )
        location_additional_data = get_field_by_id(
            Location, location_data.origin_id, "additional_data"
        )

        if location_additional_data:
            location_data.analysis_date = location_additional_data.get(
                "analysis_date", ""
            )
            location_data.analysis_density = location_additional_data.get(
                "analysis_density", ""
            )
            location_data.analysis_sulphur_percentage = location_additional_data.get(
                "analysis_sulphur_percentage", ""
            )

    def update_print_data(self):
        super().update_print_data()
        self.update_data()

        print_data = self.data.print_data
        additional_fields = self.data.additional_fields
        quantity_data = self.data.quantity_data
        party_data = self.data.party_data
        reference_data = self.data.reference_data

        print_data.carrier_name = get_field_by_id(Company, party_data.carrier_id)
        print_data.contract_name = get_field_by_id(
            ServiceContract, additional_fields.contract_id
        )
        print_data.contract_code = get_field_by_id(
            ServiceContract, additional_fields.contract_id, "code"
        )
        print_data.date = (
            self.end_time.strftime("%Y-%m-%d") if self.end_time else "YYYY-MM-DD"
        )
        print_data.facility_name = (
            self.created_by_server.name if self.created_by_server else ""
        )
        print_data.customer_name = self.company.name if self.company_id else ""
        print_data.location = self.location.name if self.location_id else ""
        print_data.service_type = (
            self.product.parent.name if self.product.parent else self.product.name
        )
        print_data.stream_name = self.product.name if self.product.parent else ""
        print_data.stream_code = self.product.code if self.product.parent else ""
        print_data.hazardous = (
            "Yes" if additional_fields.waste_characterization.hazardous else "No"
        )
        print_data.trucking_ticket_number = reference_data.carrier_bol
        print_data.producer_name = get_field_by_id(Company, party_data.producer_id)
        print_data.battery_name = (
            self.location.parent.name
            if self.location_id and self.location.parent_id
            else ""
        )
        print_data.h2s_description = (
            "Sour" if additional_fields.waste_characterization.h2s else "Sweet"
        )
        try:
            print_data.solid_volume = (
                quantity_data.total_volume * quantity_data.solid_percentage / 100.0
            )
            print_data.oil_volume = (
                quantity_data.total_volume * quantity_data.oil_percentage / 100.0
            )
            print_data.water_volume = (
                quantity_data.total_volume * quantity_data.water_percentage / 100.0
            )
        except:
            pass

        # Add the fields that should not be printed in the top table here.
        self.data.print_data.dont_print_list = [
            "oil_volume",
            "water_volume",
            "solid_volume",
        ]
        if self.ticket_type == GIPTicketTypes.TRANSFER:
            self.data.print_data.dont_print_list += [
                "gate_screen",
                "h2s_description",
                "hazardous",
            ]

        if self.ticket_type == GIPTicketTypes.LANDFILL:
            self.data.print_data.dont_print_list += ["h2s_description"]

        if self.ticket_type == GIPTicketTypes.RECEIPT:
            self.data.print_data.dont_print_list += ["gate_screen"]

        if self.ticket_type == GIPTicketTypes.INVENTORY:
            self.data.print_data.dont_print_list += [
                "gate_screen",
                "h2s_description",
                "hazardous",
                "customer_name",
                "battery_name",
            ]

        self.update_status_history()

    def save(self, *args, skip_inventory_recalc=False, **kwargs):
        # Find the earliest end time if the end time was changed.
        end_time = self.end_time
        if self.dbreplication_config and self.modified_by_server:
            self._skip_replication_signal = True
        try:
            if self.pk:
                previous_obj = self.__class__.objects.get(pk=self.pk)
                end_time = (
                    min(self.end_time, previous_obj.end_time)
                    if self.end_time
                    else previous_obj.end_time
                )
        except (self.__class__.DoesNotExist, TypeError):
            # Couldn't find the previous_obj or something was None
            pass

        super().save(*args, **kwargs)
        if not skip_inventory_recalc:
            if end_time:
                async_task(
                    call_command,
                    "gip_recalculate_inventory_tickets",
                    end_time.strftime("%Y-%m-%d"),
                    task_name="gip_recalculate_inventory_tickets",
                )
            else:
                async_task(
                    call_command,
                    "gip_recalculate_inventory_tickets",
                    task_name="gip_recalculate_inventory_tickets",
                )

    def validate_barcode_uniqueness(self):
        """Validate that the given barcode is not already associated with a different ticket.
        Raises:
            ValidationError: If the barcode is used on another ticket.
        """
        barcode_id = self.data.reference_data.bc_id
        exclude_ticket_code = self.code
        if barcode_id is None or barcode_id == "":
            return

        company_id = GIPHelpers.dbreplication_client_company_id()
        barcode_conflict_exists = (
            TicketGIPBase.objects.filter(
                dbreplication_client_company_id=company_id,
                data__reference_data__bc_id=barcode_id,
            )
            .exclude(
                Q(data__additional_fields__state=GIPTicketStatus.VOID)
                | Q(code=exclude_ticket_code)
            )
            .exists()
        )

        if barcode_conflict_exists:
            error_message = (
                f"This barcode {barcode_id} has been used in another ticket."
            )
            raise ValidationError({"BC ID": error_message})

    def compute_volumes_from_percentages(self):
        quantity_data = self.data.quantity_data
        if quantity_data.total_volume is None:
            quantity_data.total_volume = 0
        try:
            quantity_data.solid_volume = (
                quantity_data.total_volume * quantity_data.solid_percentage / 100.0
            )
            quantity_data.oil_volume = (
                quantity_data.total_volume * quantity_data.oil_percentage / 100.0
            )
            quantity_data.water_volume = (
                quantity_data.total_volume * quantity_data.water_percentage / 100.0
            )
            quantity_data.bsw_percentage = (
                quantity_data.water_percentage + quantity_data.solid_percentage
            )
            quantity_data.bsw_volume = (
                quantity_data.total_volume * quantity_data.bsw_percentage / 100.0
            )
        except Exception as e:
            logger.exception("GIP Ticket update quantity error: ", e)

    def compute_percentages_from_volumes(self):
        quantity_data = self.data.quantity_data
        if quantity_data.total_volume is None:
            quantity_data.total_volume = 0

        if quantity_data.total_volume == 0:
            return

        try:
            quantity_data.solid_percentage = (
                quantity_data.solid_volume / quantity_data.total_volume * 100.0
            )
            quantity_data.oil_percentage = (
                quantity_data.oil_volume / quantity_data.total_volume * 100.0
            )
            quantity_data.water_percentage = (
                quantity_data.water_volume / quantity_data.total_volume * 100.0
            )
            quantity_data.bsw_volume = (
                quantity_data.solid_volume + quantity_data.water_volume
            )
            quantity_data.bsw_percentage = (
                quantity_data.bsw_volume / quantity_data.total_volume * 100.0
            )
        except Exception as e:
            logger.exception("GIP Ticket update quantity error: ", e)

    def get_timezone(self):
        fallback_tz = "America/Edmonton"
        try:
            if self.created_by_server and self.created_by_server.timezone:
                return ZoneInfo(self.created_by_server.timezone)

        except (AttributeError, ZoneInfoNotFoundError) as e:
            err_msg = f"GIP Ticket: timezone error ({e}), using {fallback_tz}."
            logger.warning(err_msg)
        return ZoneInfo(fallback_tz)

class TicketGIPReceipt(TicketGIPBase):
    can_handle_ticket_types = [GIPTicketTypes.RECEIPT]

    class Meta(TicketGIPBase.Meta):
        proxy = True

    def clean(self):
        self.validate_barcode_uniqueness()
        return super().clean()

    def update_ticket_envelope(self):
        super().update_ticket_envelope()
        self.company_id = get_field_by_id(
            ServiceContract,
            self.data.additional_fields.contract_id,
            "customer_id",
            None,
        )
        self.product_id = self.data.additional_fields.stream_id
        self.location_id = self.data.location_data.origin_id
        if not self.data.location_data.destination_id:
            server_name = self.created_by_server.name
            code = f"GIP_{server_name.upper()}"

            self.data.location_data.destination_id = Location.objects.get(
                dbreplication_client_company__name=GIPHelpers.company_name(), code=code
            ).id

    def update_quantity(self):
        quantity_data = self.data.quantity_data
        self.quantity = quantity_data.total_volume if quantity_data.total_volume else 0
        self.compute_volumes_from_percentages()

    @no_sync_signals_decorator
    def update_entries(self):
        quantity_data = self.data.quantity_data
        volume_unit = self.properties.base_unit
        oil_q = UOM.convert(
            quantity_data.oil_volume,
            quantity_data.volume_unit.value,
            self.properties.base_unit,
        )
        water_q = UOM.convert(
            quantity_data.water_volume,
            quantity_data.volume_unit.value,
            self.properties.base_unit,
        )
        solid_q = UOM.convert(
            quantity_data.solid_volume,
            quantity_data.volume_unit.value,
            self.properties.base_unit,
        )

        self.entry_set.all().delete()
        # FROM SOURCE
        if oil_q:
            self.create_entry(
                company_id=self.data.party_data.customer_id,
                location_id=self.data.location_data.origin_id,
                product=GIPHelpers.oil_product(),
                quantity=-oil_q,
                display_unit=volume_unit,
            )
            self.create_entry(
                company=GIPHelpers.tboss_gip(),
                location_id=self.data.location_data.destination_id,
                product=GIPHelpers.oil_product(),
                quantity=oil_q,
                display_unit=volume_unit,
            )

        if water_q:
            self.create_entry(
                company_id=self.data.party_data.customer_id,
                location_id=self.data.location_data.origin_id,
                product=GIPHelpers.water_product(),
                quantity=-water_q,
                display_unit=volume_unit,
            )
            self.create_entry(
                company=GIPHelpers.tboss_gip(),
                location_id=self.data.location_data.destination_id,
                product=GIPHelpers.water_product(),
                quantity=water_q,
                display_unit=volume_unit,
            )

        if solid_q:
            self.create_entry(
                company_id=self.data.party_data.customer_id,
                location_id=self.data.location_data.origin_id,
                product=GIPHelpers.solid_product(),
                quantity=-solid_q,
                display_unit=volume_unit,
            )
            self.create_entry(
                company=GIPHelpers.tboss_gip(),
                location_id=self.data.location_data.destination_id,
                product=GIPHelpers.solid_product(),
                quantity=solid_q,
                display_unit=volume_unit,
            )


class TicketGIPTransfer(TicketGIPBase):
    can_handle_ticket_types = [GIPTicketTypes.TRANSFER]

    class Meta(TicketGIPBase.Meta):
        proxy = True

    def clean(self):
        self.validate_barcode_uniqueness()
        return super().clean()

    def update_quantity(self):
        # Oil truck deliver uses percentages instead of volume
        otd_service_type_name = "Oil truck delivery"
        quantity_data = self.data.quantity_data
        self.quantity = quantity_data.total_volume if quantity_data.total_volume else 0
        if self.product.name == otd_service_type_name:
            self.compute_volumes_from_percentages()
        else:
            self.compute_percentages_from_volumes()

    def update_ticket_envelope(self):
        super().update_ticket_envelope()
        self.company_id = get_field_by_id(
            ServiceContract,
            self.data.additional_fields.contract_id,
            "customer_id",
            None,
        )
        if not self.data.location_data.origin_id:
            server_name = self.created_by_server.name
            code = f"GIP_{server_name.upper()}"

            self.data.location_data.origin_id = Location.objects.get(
                dbreplication_client_company__name=GIPHelpers.company_name(), code=code
            ).id

        self.product_id = self.data.additional_fields.stream_id
        self.location_id = self.data.location_data.destination_id

    @no_sync_signals_decorator
    def update_entries(self):
        quantity_data = self.data.quantity_data
        volume_unit = self.properties.base_unit
        oil_q = UOM.convert(
            quantity_data.oil_volume,
            quantity_data.volume_unit.value,
            self.properties.base_unit,
        )
        effluent_water_q = UOM.convert(
            quantity_data.effluent_water_volume,
            quantity_data.volume_unit.value,
            self.properties.base_unit,
        )
        fresh_water_q = UOM.convert(
            quantity_data.fresh_water_volume,
            quantity_data.volume_unit.value,
            self.properties.base_unit,
        )
        solid_q = UOM.convert(
            quantity_data.solid_volume,
            quantity_data.volume_unit.value,
            self.properties.base_unit,
        )
        water_q = UOM.convert(
            quantity_data.water_volume,
            quantity_data.volume_unit.value,
            self.properties.base_unit,
        )

        self.entry_set.all().delete()
        # FROM SOURCE
        if oil_q:
            self.create_entry(
                company=GIPHelpers.tboss_gip(),
                location_id=self.data.location_data.origin_id,
                product=GIPHelpers.oil_product(),
                quantity=-oil_q,
                display_unit=volume_unit,
            )
            self.create_entry(
                company_id=self.data.party_data.customer_id,
                location_id=self.data.location_data.destination_id,
                product=GIPHelpers.oil_product(),
                quantity=oil_q,
                display_unit=volume_unit,
            )
        if effluent_water_q:
            self.create_entry(
                company=GIPHelpers.tboss_gip(),
                location_id=self.data.location_data.origin_id,
                product=GIPHelpers.water_product(),
                quantity=-effluent_water_q,
                display_unit=volume_unit,
            )
            self.create_entry(
                company_id=self.data.party_data.customer_id,
                location_id=self.data.location_data.destination_id,
                product=GIPHelpers.water_product(),
                quantity=effluent_water_q,
                display_unit=volume_unit,
            )

        if fresh_water_q:
            self.create_entry(
                company=GIPHelpers.tboss_gip(),
                location_id=self.data.location_data.origin_id,
                product=GIPHelpers.fresh_water_product(),
                quantity=-fresh_water_q,
                display_unit=volume_unit,
            )
            self.create_entry(
                company_id=self.data.party_data.customer_id,
                location_id=self.data.location_data.destination_id,
                product=GIPHelpers.fresh_water_product(),
                quantity=fresh_water_q,
                display_unit=volume_unit,
            )

        if solid_q:
            self.create_entry(
                company=GIPHelpers.tboss_gip(),
                location_id=self.data.location_data.origin_id,
                product=GIPHelpers.solid_product(),
                quantity=-solid_q,
                display_unit=volume_unit,
            )
            self.create_entry(
                company_id=self.data.party_data.customer_id,
                location_id=self.data.location_data.destination_id,
                product=GIPHelpers.solid_product(),
                quantity=solid_q,
                display_unit=volume_unit,
            )
        if water_q:
            self.create_entry(
                company=GIPHelpers.tboss_gip(),
                location_id=self.data.location_data.origin_id,
                product=GIPHelpers.water_product(),
                quantity=-water_q,
                display_unit=volume_unit,
            )
            self.create_entry(
                company_id=self.data.party_data.customer_id,
                location_id=self.data.location_data.destination_id,
                product=GIPHelpers.water_product(),
                quantity=water_q,
                display_unit=volume_unit,
            )


class TicketGIPLandfill(TicketGIPBase):
    can_handle_ticket_types = [GIPTicketTypes.LANDFILL]

    class Meta(TicketGIPBase.Meta):
        proxy = True

    def clean(self):
        self.validate_barcode_uniqueness()
        return super().clean()

    def update_ticket_envelope(self):
        super().update_ticket_envelope()
        self.company_id = get_field_by_id(
            ServiceContract,
            self.data.additional_fields.contract_id,
            "customer_id",
            None,
        )
        self.product_id = self.data.additional_fields.stream_id
        self.location_id = self.data.location_data.origin_id

    def update_quantity(self):
        quantity_data = self.data.quantity_data
        self.quantity = quantity_data.total_volume if quantity_data.total_volume else 0
        try:
            quantity_data.solid_volume = quantity_data.total_volume
        except:
            pass

    @no_sync_signals_decorator
    def update_entries(self):
        quantity_data = self.data.quantity_data
        solid_q = quantity_data.net_weight

        self.entry_set.all().delete()
        # FROM SOURCE
        self.create_entry(
            company_id=self.data.party_data.customer_id,
            location_id=self.data.location_data.origin_id,
            product_id=self.data.additional_fields.stream_id,
            quantity=-solid_q,
            display_unit=quantity_data.weight_unit,
        )

        # TO GIP AS ORIGINAL PRODUCT
        self.create_entry(
            company=GIPHelpers.tboss_gip(),
            location_id=self.data.location_data.facility_id,
            product_id=self.data.additional_fields.stream_id,
            quantity=solid_q,
            display_unit=quantity_data.weight_unit,
        )
        # TRANSFORM TO SOLID
        self.create_entry(
            company=GIPHelpers.tboss_gip(),
            location_id=self.data.location_data.facility_id,
            product_id=self.data.additional_fields.stream_id,
            quantity=-solid_q,
            display_unit=quantity_data.weight_unit,
        )
        self.create_entry(
            company=GIPHelpers.tboss_gip(),
            location_id=self.data.location_data.facility_id,
            product=GIPHelpers.solid_product(),
            quantity=solid_q,
            display_unit=quantity_data.weight_unit,
        )


class TicketGIPInventory(TicketGIPBase):
    can_handle_ticket_types = [GIPTicketTypes.INVENTORY]

    class Meta(TicketGIPBase.Meta):
        proxy = True

    def update_quantity(self):
        self.calculate_inventory_adj()
        q_data = self.data.quantity_data
        try:
            q_data.solid_pct = q_data.solid_volume / q_data.total_volume * 100.0
            q_data.oil_pct = q_data.oil_volume / q_data.total_volume * 100.0
            q_data.water_pct = q_data.water_volume / q_data.total_volume * 100.0
            q_data.bsw_volume = q_data.solid_volume + q_data.water_volume
            q_data.bsw_percentage = q_data.bsw_volume / q_data.total_volume * 100.0
        except:
            pass

    @staticmethod
    def calculate_osw_to_ts(ticketentry_qs, ts):
        qs = ticketentry_qs.filter(ticket__end_time__lt=ts)
        oil_v = (
            qs.filter(product=GIPHelpers.oil_product()).aggregate(
                quantity=Sum("quantity")
            )["quantity"]
            or 0
        )
        solid_v = (
            qs.filter(product=GIPHelpers.solid_product()).aggregate(
                quantity=Sum("quantity")
            )["quantity"]
            or 0
        )
        water_v = (
            qs.filter(
                product=GIPHelpers.water_product(),
            ).aggregate(quantity=Sum("quantity"))["quantity"]
            or 0
        )
        return np.array([oil_v, solid_v, water_v]) / 1000

    @staticmethod
    def calculate_osw_between_ts(ticketentry_qs, start_ts, end_ts):
        return TicketGIPInventory.calculate_osw_to_ts(
            ticketentry_qs, end_ts
        ) - TicketGIPInventory.calculate_osw_to_ts(ticketentry_qs, start_ts)

    def calculate_inventory_adj(self):
        q_data = self.data.quantity_data
        end_ts = self.end_time or timezone.now()
        ticketentry_qs = (
            GIPHelpers.wh_gip()
            .ticketentry_set.filter(location_id=self.data.location_data.facility_id)
            .exclude(ticket__status__in=[TicketGIPInventory.TransactionStatus.VOID])
            .exclude(ticket_id=self.id)
        )
        current_position = TicketGIPInventory.calculate_osw_to_ts(
            ticketentry_qs, end_ts
        )
        # last_inventory_ticket = self.tboss_gip.ticket_set.filter(
        #     create__lt=end_ts, ticket_type=GIPTicketTypes.INVENTORY
        # )

        # Adjustment to
        q_data.oil_volume = q_data.inventory_oil_volume - current_position[0]
        q_data.solid_volume = q_data.inventory_solid_volume - current_position[1]
        q_data.water_volume = q_data.inventory_water_volume - current_position[2]
        q_data.total_volume = (
            q_data.oil_volume + q_data.water_volume + q_data.solid_volume
        )
        return current_position

    def update_ticket_envelope(self):
        super().update_ticket_envelope()
        self.company = GIPHelpers.tboss_gip()
        self.product = GIPHelpers.inv_adj_product()
        self.location_id = self.data.location_data.facility_id

    @no_sync_signals_decorator
    def update_entries(self):
        q_data = self.data.quantity_data
        volume_unit = self.properties.base_unit
        oil_q = UOM.convert(
            q_data.oil_volume,
            q_data.volume_unit.value,
            self.properties.base_unit,
        )
        water_q = UOM.convert(
            q_data.water_volume,
            q_data.volume_unit.value,
            self.properties.base_unit,
        )
        solid_q = UOM.convert(
            q_data.solid_volume,
            q_data.volume_unit.value,
            self.properties.base_unit,
        )

        self.entry_set.all().delete()
        # OIL
        if oil_q:
            self.create_entry(
                company=GIPHelpers.tboss_gip(),
                location_id=self.data.location_data.facility_id,
                product=GIPHelpers.oil_product(),
                quantity=oil_q,
                display_unit=volume_unit,
            )
            self.create_entry(
                company=GIPHelpers.tboss_gip(),
                location_id=self.data.location_data.facility_id,
                product=GIPHelpers.inv_adj_oil_product(),
                quantity=-oil_q,
                display_unit=volume_unit,
            )
        # WATER
        if water_q:
            self.create_entry(
                company=GIPHelpers.tboss_gip(),
                location_id=self.data.location_data.facility_id,
                product=GIPHelpers.water_product(),
                quantity=water_q,
                display_unit=volume_unit,
            )
            self.create_entry(
                company=GIPHelpers.tboss_gip(),
                location_id=self.data.location_data.facility_id,
                product=GIPHelpers.inv_adj_water_product(),
                quantity=-water_q,
                display_unit=volume_unit,
            )
        # SOLID
        if solid_q:
            self.create_entry(
                company=GIPHelpers.tboss_gip(),
                location_id=self.data.location_data.facility_id,
                product=GIPHelpers.solid_product(),
                quantity=solid_q,
                display_unit=volume_unit,
            )
            self.create_entry(
                company=GIPHelpers.tboss_gip(),
                location_id=self.data.location_data.facility_id,
                product=GIPHelpers.inv_adj_solid_product(),
                quantity=-solid_q,
                display_unit=volume_unit,
            )

    def save(self, *args, **kwargs):
        # If end_time is not None, set the time to 23:59:59 while preserving the timezone
        if self.end_time:
            tz = self.get_timezone()
            if timezone.is_aware(self.end_time):
                self.end_time = self.end_time.astimezone(tz)
            end_date = self.end_time.date()

            # tz = self.end_time.tzinfo if timezone.is_aware(self.end_time) else timezone.pytz.timezone('America/Edmonton')
            self.end_time = timezone.make_aware(
                timezone.datetime.combine(end_date, time(23, 59, 59)), tz
            )
        super().save(*args, **kwargs)
