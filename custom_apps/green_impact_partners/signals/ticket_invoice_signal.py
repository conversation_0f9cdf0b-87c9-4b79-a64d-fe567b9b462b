from django.db.models.signals import post_save, pre_delete
from django.dispatch import receiver

from vantedge.tboss.models import TicketInvoice, InvoiceStatus, Ticket
from vantedge.tboss.models import ServiceContractTicket
from custom_apps.green_impact_partners.schemas import GIPTicketStatus


@receiver(post_save, sender=TicketInvoice)
def handle_ticket_invoice_post_save(sender, instance, **kwargs):
    # Reset the tickets to a default state and remove the invoiced tickets.
    service_contract_tickets = ServiceContractTicket.objects.filter(
        ticket__data__additional_fields__state = GIPTicketStatus.INVOICED,
        ticket_invoice=instance
    )
    for service_contract_ticket in service_contract_tickets.all():
        service_contract_ticket.ticket.data.additional_fields.state = GIPTicketStatus.APPROVED
        service_contract_ticket.ticket.save(update_fields=['data'])
    ServiceContractTicket.objects.filter(ticket_invoice=instance).update(ticket_invoice=None)
    # Set the ticket invoice to the ticket
    if instance.data.status == InvoiceStatus.VOID:
        return
    for id_dict in instance.data.ticket_ids:
        service_contract_ticket = ServiceContractTicket.objects.get(id=id_dict.id)
        service_contract_ticket.ticket_invoice = instance
        service_contract_ticket.ticket.data.additional_fields.state = GIPTicketStatus.INVOICED
        service_contract_ticket.ticket.save(update_fields=['data'])
        service_contract_ticket.save()
