{% load static tboss_tags %}
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Invoice Form</title>
  <style>
    body {
      background-color: #000066;
      justify-content: center;
      align-items: center;
      margin: 0;
    }

    .invoice {
      font-family: Arial, sans-serif;
    }

    .container {
      background-color: white;
      padding: 20px;
      border-radius: 8px;
      box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
      max-width: 1000px;
      margin: 1em auto;
    }

    .invoice_table {
      overflow-x: scroll;
      margin-bottom: 1em;
    }
    .consent {
      background-color: white;
      padding: 20px;
      border-radius: 8px;
      box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    }

    .consent h3 {
      margin: 0 0 10px;
      font-family: Arial, sans-serif;
    }

    .consent input[type="text"] {
      width: 100%;
      padding: 8px 0px 8px 8px;
      margin-bottom: 10px;
      border: 1px solid #ccc;
      border-radius: 4px;
    }

    .consent button {
      padding: 10px 20px;
      margin: 5px;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      box-shadow: 0 5px 10px 0 rgba(0, 0, 0, 0.3);
    }

    .consent .accept {
      background-color: green;
      color: white;
    }

    .upload {
      background-color: grey;
      border: 1px solid;
    }

    .consent .reject {
      background-color: red;
      color: white;
      float: right;
    }

    .table_header {
      width: 100%;
    }

    .table_charges {
      background-color: #1D734C;
      color: white;
      font-weight: bold;
      border: 1px solid black;
    }

    .table_description {
      background-color: lightgray;
      color: black;
      font-weight: bold;
    }

    .table_info {
      background-color: white;
      color: black;
      font-weight: normal;
    }

    .general_table {
      width: 100%;
      border: 1px solid black !important;
      font-size: small;
    }

    /* add padding to the table td */
    .general_table td {
      padding: 5px;
      border: 1px solid black;
    }

    .bol_row {
      border-top: 1px solid black;
      margin-left: 0px;
      margin-right: 0px;
    }

    .header-text {
      font-size: 15px;
    }

    .column_cell_title {
      font-weight: bold;
    }

    .container.is-size-5 {
      padding: 40px;
    }

    .company-name {
      font-weight: bold;
    }

    .logo_image {
      width: 200px;
      height: auto;
      vertical-align: middle;
    }

    .ticket-number {
      text-align: right;
      font-weight: bold;
    }

    .align-right {
      text-align: right;
    }

    .vantegde_logo_container {
      display: flex;
      justify-content: flex-end;
      align-items: center;
    }

    .logo_vantedge {
      width: 100px;
      height: auto;
      vertical-align: right;
    }

    .side-by-side {
      display: grid;
      grid-template-columns: auto auto;
      gap: 20px;
      align-items: start;
    }

    .link_box {
      margin-top: 15px;
      border-radius: 8px;
      background-color: #1D734C;
      padding: 10px;
      color: white;
      font-weight: bold;
      float: right;
      box-shadow: 0 5px 10px 0 rgba(0, 0, 0, 0.3);
    }

    .link_box a{
      color: white;
      text-decoration: none;
    }

    .invoice_not_found {
      text-align: center;
    }

    .fileinputs {
      font-family: Arial, sans-serif;
      position: relative;
      display: inline-block;
      overflow: hidden;
      border: 2px dotted #ccc;
      border-radius:4px;
      /* place in the center of the page */
      align-items: center;
      justify-content: center;
      /* place the items inside the div in the center */
      text-align: center;
      padding: 8px 8px 8px 8px;
      margin-top: 5px;
      margin-bottom: 5px;
    }

  </style>
</head>
<body>
<div class="container">
  <div class="invoice">
    <div class="columns">
      <div class="column">
        <table style="width: 100%;">
          <tr>
            <td>
              <img src="https://wheelhouse.vantedgelgx.com/static/images/green_impact_partners/logo.png"
              alt="Green Impact Operating" class="logo_image" />
            </td>
            {% if ticket_invoice.code %}
            <td class="link_box">
              <a href="{{ invoice_pdf }}" style="display: block;">
                <div>
                  Click here for PDF of <br/>
                  invoice #: {{ ticket_invoice.code }}
                </div>
              </a>
            </td>
            {% endif %}
          </tr>
        </table>
      </div>
    </div>
    <div class="columns">
      <div class="column">
        <table class="table_header">
          <tr>
            <td class="company-name header-text">Green Impact Operating Corp.</td>
            <td class="ticket-number">
              {% if ticket_invoice.code %}
              Status: {{ ticket_invoice.data.status }}
              {% endif %}
            </td>
          </tr>
          <tr>
            <td class="">322 - 11th Avenue SW, Suite 303</td>
            <td class="ticket-number"></td>
          </tr>
          <tr>
            <td class="header-text">Calgary, Alberta, Canada T2R 0C5</td>
            <td class="align-right header-text">{% if ticket_invoice.code %} Phone: ************ {% endif %}</td>
          </tr>
          {% if ticket_invoice.code == None %}
          <tr>
            <td class="header-text">Phone: ************</td>
            <td class="align-right header-text"></td>
          </tr>
          {% endif %}
          <tr>
            <td class="header-text">Email: <EMAIL></td>
            <td class="align-right header-text"></td>
          </tr>
          <tr>
            <td class="header-text">GST#: ********* RT0001</td>
          </tr>
        </table>
      </div>
    </div>

    {% if ticket_invoice.code %}
    <div class="invoice_table">
      <table class="general_table table_charges">
        <tr>
          <td colspan="7">Charges</td>
        </tr>
        <tr class="table_description">
          <td>Ticket</td>
          <td>Description</td>
          <td>Carrier Company</td>
          <td>Trucking Ticket Number</td>
          <td>QTY</td>
          <td>UoM</td>
          {% if ticket_type == 'Receipt' %}
            <td>Solid %</td>
            <td>Solid m³</td>
            <td>Water %</td>
            <td>Water m³</td>
            <td>Oil %</td>
            <td>Oil m³</td>
          {% endif %}
          <td style="text-align: right;">Rate</td>
          <td style="text-align: right;">Total</td>
        </tr>
        {% for charge in charges_table %}
          {% if charge.quantity != '0' %}
            <tr class="table_info">
              <td>{{ charge.ticket }}</td>
              <td>{{ charge.description }}</td>
              <td>{{ charge.carrier_company }}</td>
              <td>{{ charge.trucking_ticket_number }}</td>
              <td>{{ charge.quantity }}</td>
              <td>{{ charge.uom }}</td>
              {% if ticket_type == 'Receipt' %}
                <td>{{ charge.solid_percentage|floatformat:3 }}</td>
                <td>{{ charge.solid_volume|floatformat:3 }}</td>
                <td>{{ charge.water_percentage|floatformat:3 }}</td>
                <td>{{ charge.water_volume|floatformat:3 }}</td>
                <td>{{ charge.oil_percentage|floatformat:3 }}</td>
                <td>{{ charge.oil_volume|floatformat:3 }}</td>
              {% endif %}
              <td style="text-align: right;">${{ charge.rate |floatformat:2 }}</td>
              <td style="text-align: right;">${{ charge.total |floatformat:2 }}</td>
            </tr>
          {% endif %}
        {% endfor %}
        {% if ticket_type == 'Receipt' %}
          <tr class="table_info">
            <td colspan="6" style="text-align: right;">
              <b>Service Totals</b>
            </td>
            <td></td>
            <td>
              <b>{{ total_solid_volume|floatformat:3 }}</b>
            </td>
            <td></td>
            <td>
              <b>{{ total_water_volume|floatformat:3 }}</b>
            </td>
            <td></td>
            <td>
              <b>{{ total_oil_volume|floatformat:3 }}</b>
            </td>
            <td colspan="2"></td>
          </tr>
        {% endif %}
        {% if ticket_type == 'Landfill' %}
          <tr class="table_info">
            <td colspan="4" style="text-align: right;">
              <b>Service Totals</b>
            </td>
            <td>
              <b>{{ total_quantity|floatformat:3 }}</b>
            </td>
            <td colspan="3"></td>
          </tr>
        {% endif %}
      </table>
    </div>

    <div class="side-by-side">
      <p style="font-size: 10px;">
        Terms N30 days unless specified in agreement by both parties - Interest charged @ 2% per month (24% per annum)
        on accounts over 30
        days.
        It is the responsibility of the generator to characterize their waste streams.
        Oil specifications cannot exceed CAPP requirements for < 1.5 ppm phosphorous (volatile fraction) and no organic
        halides.
        Green Impact Operating Corp. is not responsible for oil analysis. Penalty may be applied.
      </p>
      <table class="general_table">
        <tr>
          <td class="column_cell_title">Subtotal</td>
          <td style="text-align: right;">$ {{ sub_total |floatformat:2 }}</td>
        </tr>
        <tr>
          <td class="column_cell_title">Tax</td>
          <td style="text-align: right;">$
          {{ tax |floatformat:2 }}</tr>
        </tr>
        <tr>
          <td class="column_cell_title">Balance Due</td>
          <td style="text-align: right;">$ {{ balance_due |floatformat:2 }}</td>
        </tr>
      </table>
    </div>
    {% else %}
    <div class="invoice_not_found">
      <h3>Invoice not found!</h3>
    </div>
    {% endif %}
  </div>

  {% if ticket_invoice.data.status == 'Approval Sent' %}
    <div class="consent">
      <h3>Invoice Approval</h3>
      <input id="note-id" type="text" placeholder="Enter your note here">
      <div class="fileinputs">
        <h3>Upload Signed Invoices Here</h3>
        <input id="attachment" type="file" name="attachment">
      </div>
      <div>
        <button onclick="approve('accepted')" class="accept">Accept Invoice</button>
        <button onclick="approve('rejected')" class="reject">Reject Invoice</button>
      </div>
    </div>
  {% endif %}

  <div class="vantegde_logo_container">
    <img src="https://wheelhouse.vantedgelgx.com/static/images/vantedge_logo_2024.png"
         alt="Vantedge" class="logo_vantedge">
  </div>

</div>

<script>
  const attachementInput = document.getElementById('attachment');
  const fileNameDisplay = document.getElementById('file-name');

  attachementInput.addEventListener('change', () => {
    const file = attachementInput.files[0];
    fileNameDisplay.textContent = file ? file.name : 'No File Selected';
  });

  function isValidAttachment() {
    var attachementType = /pdf.*/;
    const attachment = document.getElementById('attachment').files[0]
    if (attachment && !attachment.type.match(attachementType)) {
      return [false, 'Attachment must be pdf file']
    }
    if (attachment && attachment.size > 25 * 1024 * 1024) {
      return [false, 'Attachment size must be less than 25MB']
    }
    return [true, null]
  }

  function approve(condition) {
    const [isValid, msg] = isValidAttachment()
    if (!isValid) {
      alert(msg)
      return
    }
    const userConfirmed = confirm(`Proceed to mark invoice as ${condition}?`);
    if (userConfirmed) {
      const url_approve_invoice = "{{ url_approve_invoice }}"
      const attachment = document.getElementById('attachment').files[0]
      var data = new FormData();
      data.append('unique_identifier', "{{ unique_identifier }}")
      data.append('accepted', condition == 'accepted' ? true : false)
      data.append('note', document.getElementById('note-id').value)
      data.append('attachment', attachment)

      fetch(url_approve_invoice, {
        method: 'POST',
        body: data
      })
        .then(response => {
          if (!response.ok) {
            throw new Error('Network response was not ok');
          }
          return response.json();
        })
        .then(data => {
          location.reload();
        })
        .catch(error => {
          console.error('Error:', error);
          alert('Request failed.');
        });
    } else {
      alert('Operation cancelled.');
    }
  }
</script>
</body>
</html>
