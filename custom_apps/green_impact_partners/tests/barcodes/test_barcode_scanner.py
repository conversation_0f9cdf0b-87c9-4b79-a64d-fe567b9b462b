import os
from pathlib import Path

from django.test import TestCase
from vantedge.tboss.views.utils import barcode_utils


# ruff: noqa: PT009 Allow assert statements
class GetPagesAndBarcodesFromPDFTests(TestCase):

    def test_get_pages_and_barcodes_from_pdf(self):
        """ Looks into testMedia/barcodeImages directory and reads all the pdf files.
            It checks if the barcode value extracted from the pdf is the same as the filename.
        """
        directory = "testMedia/barcodePDFs"
        for filename in os.listdir(directory):
            file_path = directory + "/" + filename
            pdf_bytes = Path.open(file_path, "rb").read()
            barcode_values = barcode_utils.get_pages_and_barcodes_from_pdf(pdf_bytes)
            barcode = barcode_values[0][1] if barcode_values else None
            expected_barcode = filename.split(".")[0]
            if expected_barcode == "blank_pdf":
                expected_barcode = None
            self.assertEqual(barcode, expected_barcode)

