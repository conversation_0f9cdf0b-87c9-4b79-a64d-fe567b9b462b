from unittest import mock

from django.contrib.contenttypes.models import ContentType
from django.core.exceptions import ValidationError
from vantedge.tboss.models import TicketEntry
from vantedge.tboss.tests.factories.company import (
    TBossCompanyFactory,
    TerminalBossClientCompanyFactory,
)
from vantedge.tboss.tests.factories.location import LocationFactory
from vantedge.tboss.tests.factories.product import ProductFactory
from vantedge.tboss.tests.factories.sync.config import DBReplicationConfigFactory
from vantedge.tboss.tests.factories.sync.server import DBReplicationServerFactory
from vantedge.tboss.tests.factories.tboss_user import TBossUserFactory
from vantedge.tboss.tests.factories.ticket import (
    ReceiptTicketFactory,
    TicketPropertiesFactory,
)
from vantedge.wheelhouse.tests.base.base_api_test_case import BaseTestCase
from vantedge.wheelhouse.tests.factories.company import (
    CompanyFactory,
    WheelhouseCompanyFactory,
)

from custom_apps.green_impact_partners.schemas import (
    GIPTicketData,
    GIPTicketLocationData,
    GIPTicketPartyData,
    GIPTicketPrintData,
    GIPTicketQuantityData,
    GIPTicketStatus,
)
from custom_apps.green_impact_partners.tests.factories.gip_ticket import (
    GipTicketFactory,
    GipTransferTicketFactory,
)


# ruff: noqa: PT009 Allow assert statements
class TestGipTicket(BaseTestCase):
    def test_save__print_data__correct_service_type_and_stream_in_print_data(self):
        company = TBossCompanyFactory(name="Green Impact Partners")
        user = TBossUserFactory()
        processing_service = ProductFactory(name="Processing service")
        stream = ProductFactory(name="Stream", parent=processing_service)
        location = LocationFactory()

        with (
            mock.patch("vantedge.util.classes.ProxyCompanyMixin.can_handle_company", return_value=True),
            mock.patch("vant_ticket.models.ticket.Ticket.calculate_code"),
            mock.patch("custom_apps.green_impact_partners.models.tickets.TicketGIPBase.update_ticket_envelope"),
            mock.patch("vant_ticket.models.ticket.Ticket.calculate_ticket_type"),
            mock.patch("vant_ticket.models.ticket.Ticket.update_quantity"),
            mock.patch("vant_ticket.models.ticket.Ticket.set_ticket_properties"),
            mock.patch("custom_apps.green_impact_partners.models.tickets.TicketGIPBase.update_status_history"),
        ):
            ticket = GipTicketFactory(user=user, company=company, location=location, product=stream)

        self.assertEqual(stream.name, ticket.data.print_data.stream_name)
        self.assertEqual(processing_service.name, ticket.data.print_data.service_type)

    def test_update_entries__transfer_ticket_entries__creates_outbound_oil_entries(self):
        tboss_company = TBossCompanyFactory(name="Green Impact Partners")
        company = CompanyFactory(name="Green Impact Partners")
        wheelhouse_company = WheelhouseCompanyFactory(company=company)
        tboss_client_company = TerminalBossClientCompanyFactory(client_company=wheelhouse_company.company)
        customer = TBossCompanyFactory(name="Customer")
        carrier = TBossCompanyFactory(name="Carrier")
        user = TBossUserFactory()
        processing_service = ProductFactory(name="Processing service")
        stream = ProductFactory(name="Stream", parent=processing_service)
        transfer_product = ProductFactory(name="Transfer Product", code="Transfer Products")
        oil_product = ProductFactory(name="Oil", code="Oil", parent=transfer_product)
        tboss_client_company.product_set.add(processing_service)
        tboss_client_company.product_set.add(stream)
        tboss_client_company.product_set.add(transfer_product)
        tboss_client_company.product_set.add(oil_product)
        replication_server = DBReplicationServerFactory()
        content_type = ContentType.objects.get_for_model(TicketEntry)
        replication_config = DBReplicationConfigFactory(bidirectional=False, content_type=content_type)
        replication_config.servers.add(replication_server)
        location = LocationFactory(name="origin")
        destination = LocationFactory(name="destination")
        battery = LocationFactory(name="battery")
        ticket_properties = TicketPropertiesFactory()
        oil_volume = 1234
        data = GIPTicketData(
            quantity_data=GIPTicketQuantityData(oil_volume=oil_volume, volume_unit="L"),
            location_data=GIPTicketLocationData(
                origin_id=str(location.id),
                destination_id=str(destination.id),
                battery_id=str(battery.id),
                facility_id=str(location.id),
            ),
            party_data=GIPTicketPartyData(
                customer_id=str(customer.id), carrier_id=str(carrier.id), producer_id=str(tboss_company.id)
            ),
            print_data=GIPTicketPrintData(),
        )

        with (
            mock.patch("vantedge.util.classes.ProxyCompanyMixin.can_handle_company", return_value=True),
            mock.patch("vant_ticket.models.ticket.Ticket.calculate_code"),
            mock.patch("custom_apps.green_impact_partners.models.tickets.TicketGIPTransfer.update_ticket_envelope"),
            mock.patch("custom_apps.green_impact_partners.models.tickets.TicketGIPTransfer.update_print_data"),
            mock.patch("vant_ticket.models.ticket.Ticket.calculate_ticket_type"),
            mock.patch("vant_ticket.models.ticket.Ticket.update_quantity"),
            mock.patch("vant_ticket.models.ticket.Ticket.set_ticket_properties"),
            mock.patch("custom_apps.green_impact_partners.models.tickets.TicketGIPBase.update_status_history"),
        ):
            ticket = GipTransferTicketFactory(
                user=user,
                company=tboss_company,
                location=location,
                product=stream,
                properties=ticket_properties,
                data=data,
                created_by_server=replication_server,
            )

        ticket_entries = ticket.entry_set.all()
        expected_num_entries = 2

        self.assertEqual(expected_num_entries, len(ticket_entries))
        self.assertEqual("origin", ticket_entries[0].location.name)
        self.assertEqual(-oil_volume, ticket_entries[0].quantity)
        self.assertEqual("destination", ticket_entries[1].location.name)
        self.assertEqual(oil_volume, ticket_entries[1].quantity)

    def set_up_ticket_data(self):
        self.tboss_company = TBossCompanyFactory(name="Green Impact Partners")
        self.company = CompanyFactory(name="Green Impact Partners")
        self.wheelhouse_company = WheelhouseCompanyFactory(company=self.company)
        self.tboss_client_company = TerminalBossClientCompanyFactory(client_company=self.wheelhouse_company.company)
        self.user = TBossUserFactory()
        self.processing_service = ProductFactory(name="Processing service")
        self.stream = ProductFactory(name="Stream", parent=self.processing_service)
        self.transfer_product = ProductFactory(name="Transfer Product", code="Transfer Products")
        self.oil_product = ProductFactory(name="Oil", code="Oil", parent=self.transfer_product)
        self.tboss_client_company.product_set.add(self.processing_service)
        self.tboss_client_company.product_set.add(self.stream)
        self.tboss_client_company.product_set.add(self.transfer_product)
        self.tboss_client_company.product_set.add(self.oil_product)
        self.replication_server = DBReplicationServerFactory(name="Replication Server Name")
        self.content_type = ContentType.objects.get_for_model(TicketEntry)
        self.replication_config = DBReplicationConfigFactory(bidirectional=False, content_type=self.content_type)
        self.replication_config.servers.add(self.replication_server)
        self.location = LocationFactory(name="origin")

    def get_receipt_ticket(self, barcode="", code=None):
        data = GIPTicketData()
        data.additional_fields.state = GIPTicketStatus.SUBMITTED
        data.reference_data.bc_id = barcode
        ticket = ReceiptTicketFactory(
            code=code,
            user=self.user,
            company=self.tboss_company,
            location=self.location,
            product=self.stream,
            data=data,
            created_by_server=self.replication_server,
            dbreplication_client_company_id=self.tboss_client_company.id,
        )
        return ticket

    def get_transfer_ticket(self):
        data = GIPTicketData()
        data.additional_fields.state = GIPTicketStatus.SUBMITTED
        ticket = GipTransferTicketFactory(
                user=self.user,
                company=self.tboss_company,
                location=self.location,
                product=self.stream,
                data=data,
                created_by_server=self.replication_server,
                dbreplication_client_company_id=self.tboss_client_company.id,
        )
        return ticket


    def test_validate_barcode_uniqueness(self):
        self.set_up_ticket_data()
        with (
            mock.patch(
                "custom_apps.green_impact_partners.models.GIPHelpers.dbreplication_client_company_id",
                return_value=self.tboss_client_company.id,
            ),
            mock.patch("vantedge.util.classes.ProxyCompanyMixin.can_handle_company", return_value=True),
            mock.patch("custom_apps.green_impact_partners.models.tickets.TicketGIPReceipt.update_ticket_envelope"),
            mock.patch("custom_apps.green_impact_partners.models.tickets.TicketGIPReceipt.update_print_data"),
            mock.patch("custom_apps.green_impact_partners.models.tickets.TicketGIPReceipt.update_entries"),
            mock.patch("vant_ticket.models.ticket.Ticket.update_quantity"),
            mock.patch("vant_ticket.models.ticket.Ticket.set_ticket_properties"),
            mock.patch("custom_apps.green_impact_partners.models.tickets.TicketGIPBase.update_status_history"),
        ):
            def assert_validation_fails(ticket):
                with self.assertRaises(ValidationError):
                    ticket.validate_barcode_uniqueness()

            def assert_validation_passes(ticket):
                try:
                    ticket.validate_barcode_uniqueness()
                except ValidationError:
                    self.fail("ValidationError raised unexpectedly")

            # Make the tickets have the same barcode, should throw an error
            ticket = self.get_receipt_ticket(barcode="BCtest1", code="123")
            ticket_2 = self.get_receipt_ticket(barcode="BCtest1")
            ticket.save()
            ticket_2.save()
            assert_validation_fails(ticket)

            # Make the tickets have different barcodes, should not throw an error
            ticket.data.reference_data.bc_id = "BCtest2"
            ticket.save()
            assert_validation_passes(ticket)

            # Test code is None but barcode already exists, should throw an error
            ticket.code = None
            ticket.data.reference_data.bc_id = "BCtest2"
            assert_validation_fails(ticket)

            # Make the tickets have different barcodes and code is None or blank, should not throw an error
            ticket.data.reference_data.bc_id = "BCtest3"
            assert_validation_passes(ticket)


            # Make the tickets have the same barcode but one is void, should not throw an error
            ticket_2.data.reference_data.bc_id = ticket.data.reference_data.bc_id
            ticket_2.data.additional_fields.state = GIPTicketStatus.VOID
            ticket_2.save()
            assert_validation_passes(ticket)
            ticket_2.data.additional_fields.state = GIPTicketStatus.SUBMITTED
            # Test barcode is None, should not throw an error
            ticket.data.reference_data.bc_id = None
            ticket.save()

            # Test both ids are blank, should not throw an error
            ticket.data.reference_data.bc_id = ""
            ticket_2.data.reference_data.bc_id = ""
            ticket.save()
            ticket_2.save()
            assert_validation_passes(ticket)

    def test_ticket_update_quantity(self):
        """ Test quantity update logic for gip tickets.
            Cases:
                Transfer Ticket
                Transfer Ticket with Oil Truck Delivery
                Receipt Ticket
        """
        self.set_up_ticket_data()

        with (
            mock.patch(
                "custom_apps.green_impact_partners.models.GIPHelpers.dbreplication_client_company_id",
                return_value=self.tboss_client_company.id,
            ),
            mock.patch("vantedge.util.classes.ProxyCompanyMixin.can_handle_company", return_value=True),
            mock.patch("custom_apps.green_impact_partners.models.tickets.TicketGIPTransfer.update_ticket_envelope"),
            mock.patch("custom_apps.green_impact_partners.models.tickets.TicketGIPTransfer.update_print_data"),
            mock.patch("custom_apps.green_impact_partners.models.tickets.TicketGIPTransfer.update_entries"),
            mock.patch("custom_apps.green_impact_partners.models.tickets.TicketGIPReceipt.update_ticket_envelope"),
            mock.patch("custom_apps.green_impact_partners.models.tickets.TicketGIPReceipt.update_print_data"),
            mock.patch("custom_apps.green_impact_partners.models.tickets.TicketGIPReceipt.update_entries"),
            mock.patch("vant_ticket.models.ticket.Ticket.set_ticket_properties"),
            mock.patch("custom_apps.green_impact_partners.models.tickets.TicketGIPBase.update_status_history"),
        ):

            def update_quantity_oil_truck_delivery(total_volume, solid_percentage, oil_percentage, water_percentage):
                ticket = self.get_transfer_ticket()
                ticket.product.name = "Oil truck delivery"
                ticket.data.quantity_data.total_volume = total_volume
                ticket.data.quantity_data.solid_percentage = solid_percentage
                ticket.data.quantity_data.oil_percentage = oil_percentage
                ticket.data.quantity_data.water_percentage = water_percentage
                ticket.update_quantity()
                return ticket

            def update_quantity_transfer_ticket(total_volume, solid_volume, oil_volume, water_volume):
                ticket = self.get_transfer_ticket()
                ticket.data.quantity_data.total_volume = total_volume
                ticket.data.quantity_data.solid_volume = solid_volume
                ticket.data.quantity_data.oil_volume = oil_volume
                ticket.data.quantity_data.water_volume = water_volume
                ticket.update_quantity()
                return ticket

            def update_quantity_receipt_ticket(total_volume, solid_percentage, oil_percentage, water_percentage):
                ticket = self.get_receipt_ticket()
                ticket.data.quantity_data.total_volume = total_volume
                ticket.data.quantity_data.solid_percentage = solid_percentage
                ticket.data.quantity_data.oil_percentage = oil_percentage
                ticket.data.quantity_data.water_percentage = water_percentage
                ticket.update_quantity()
                return ticket

            def assert_volume_sums_to_total(ticket):
                total = (
                    ticket.data.quantity_data.solid_volume +
                    ticket.data.quantity_data.oil_volume +
                    ticket.data.quantity_data.water_volume
                )
                self.assertEqual(total, ticket.data.quantity_data.total_volume)

            # Build the Test Case
            total_volumes = [200, 0, None, 100, 0]
            # (Solid, Oil, Water) Percentages
            percentages = [(10, 20, 70), (10, 20, 70),(10, 20, 70),(-10, 20, 90),(0, 0, 0)]
            # (Solid, Oil, Water) Volumes
            volumes = [(20, 40, 140),(0, 0, 0),(0, 0, 0),(-10, 20, 90),(0, 0, 0)]
            bsw_percentages = [80, 80, 80, 80, 0]
            bsw_volume = [160, 0, 0, 80, 0]


            # Loop over the test cases and test the percentages correctly convert to volumes and vice versa..
            for i, (solid_percentage, oil_percentage, water_percentage) in enumerate(percentages):
                total_volume = total_volumes[i]
                volume_solid, volume_oil, volume_water = volumes[i]

                # Test the transfer ticket with the special Oil Truck Delivery case
                oil_truck_ticket = update_quantity_oil_truck_delivery(
                    total_volume, solid_percentage, oil_percentage, water_percentage
                )
                self.assertEqual(oil_truck_ticket.data.quantity_data.bsw_volume, bsw_volume[i])
                self.assertEqual(oil_truck_ticket.data.quantity_data.bsw_percentage, bsw_percentages[i])
                self.assertEqual(oil_truck_ticket.data.quantity_data.solid_volume, volume_solid)
                self.assertEqual(oil_truck_ticket.data.quantity_data.oil_volume, volume_oil)
                self.assertEqual(oil_truck_ticket.data.quantity_data.water_volume, volume_water)
                assert_volume_sums_to_total(oil_truck_ticket)

                # Test the receipt ticket update_quantity
                receipt_ticket = update_quantity_receipt_ticket(
                   total_volume, solid_percentage, oil_percentage, water_percentage
                )
                self.assertEqual(receipt_ticket.data.quantity_data.bsw_volume, bsw_volume[i])
                self.assertEqual(receipt_ticket.data.quantity_data.bsw_percentage, bsw_percentages[i])
                self.assertEqual(receipt_ticket.data.quantity_data.solid_volume, volume_solid)
                self.assertEqual(receipt_ticket.data.quantity_data.oil_volume, volume_oil)
                self.assertEqual(receipt_ticket.data.quantity_data.water_volume, volume_water)
                assert_volume_sums_to_total(receipt_ticket)

                # Test the transfer ticket update_quantity without special product
                transfer_ticket = update_quantity_transfer_ticket(
                    total_volume, volume_solid, volume_oil, volume_water
                )
                sp = solid_percentage
                op = oil_percentage
                wp = water_percentage
                bsw_p =  bsw_percentages[i]
                bsw_v = bsw_volume[i]
                if total_volume == 0 or total_volume is None:
                    sp = op  = wp = bsw_p = 0
                self.assertEqual(transfer_ticket.data.quantity_data.solid_percentage, sp)
                self.assertEqual(transfer_ticket.data.quantity_data.oil_percentage, op)
                self.assertEqual(transfer_ticket.data.quantity_data.water_percentage, wp)
                self.assertEqual(transfer_ticket.data.quantity_data.bsw_volume, bsw_v)
                self.assertEqual(transfer_ticket.data.quantity_data.bsw_percentage, bsw_p)
