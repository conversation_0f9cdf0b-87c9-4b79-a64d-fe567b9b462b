from unittest import mock
from unittest.mock import mock_open

from django.core.management import call_command

from vantedge.tboss.tests.factories.company import TerminalBossClientCompanyFactory
from vantedge.tboss.tests.factories.sync.server import DBReplicationServerFactory
from vantedge.wheelhouse.tests.base.base_api_test_case import BaseTestCase


class TestExportGipServerData(BaseTestCase):
    def test_export_gip_data(self):
        gip_company = TerminalBossClientCompanyFactory(name='Green Impact Partners')
        plains_company = TerminalBossClientCompanyFactory(name='Plains')
        DBReplicationServerFactory(name='server 1', dbreplication_client_company=gip_company)
        DBReplicationServerFactory(name='server 2', dbreplication_client_company=gip_company)
        DBReplicationServerFactory(dbreplication_client_company=plains_company)

        mock_file = mock_open()
        with mock.patch('builtins.open', mock_file):
            call_command('export_gip_server_data')

            mock_file().write.assert_called()

        mock_file().write.assert_called_once()

