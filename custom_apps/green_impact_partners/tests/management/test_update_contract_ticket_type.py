from django.core.management import call_command

from vantedge.tboss.models import ServiceContract
from vantedge.tboss.models.service_contract import ServiceContractData
from vantedge.tboss.tests.factories.company import TerminalBossClientCompanyFactory
from vantedge.tboss.tests.factories.service_contract import ServiceContractFactory
from vantedge.wheelhouse.tests.base.base_api_test_case import BaseTestCase


class TestUpdateContractTicketType(BaseTestCase):
    def test_update_contract_ticket_type__success(self):
        ticket_type_spelling_mistake = 'Reciept'
        ticket_type_spelling_correction = 'Receipt'
        contract_data = ServiceContractData(ticket_type=ticket_type_spelling_mistake)
        gip_client_company = TerminalBossClientCompanyFactory(name='Green Impact Partners')
        contract = ServiceContractFactory(
            data=contract_data,
            dbreplication_client_company=gip_client_company
        )

        call_command('update_contract_ticket_type')

        updated_contract = ServiceContract.objects.get(pk=contract.pk)

        self.assertEqual(ticket_type_spelling_correction, updated_contract.data.ticket_type)
