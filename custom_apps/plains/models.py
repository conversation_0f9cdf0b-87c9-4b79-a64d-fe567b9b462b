import functools
from vantedge.tboss.models.proxies import Ticket, TicketEntry, Company, Location


class PlainsHelpers:

    @staticmethod
    def company_name():
        return "Plains"

class TicketPlainsBase(Ticket):

    class Meta(Ticket.Meta):
        proxy = True

    can_handle_company_names = [PlainsHelpers.company_name()]
    data_schema = dict
    default_ticket_type = None

    @classmethod
    def can_handle_ticket_type(cls, instance):
        return True

    def get_real_instance_class(self):
        return self.__class__
