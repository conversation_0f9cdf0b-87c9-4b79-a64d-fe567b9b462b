from django.urls import include, path
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>

from custom_apps.green_impact_partners.views.mass_balance import MassBalanceReportViewSet
from custom_apps.green_impact_partners.views.ticket import TicketViewSet
from custom_apps.green_impact_partners.views.ticket_attachment_logs import TicketAttachmentLogViewSet

router = DefaultRouter()
router.register(r"mass_balance_report", MassBalanceReportViewSet, basename="mass_balance_report")
router.register(r"ticket_attachment_log", TicketAttachmentLogViewSet, basename="ticket_attachment_log")
router.register(r"ticket", TicketViewSet, basename="ticket")


app_name = "green_impact_partners"

urlpatterns = [
    path("gip/", include(router.urls)),
]
