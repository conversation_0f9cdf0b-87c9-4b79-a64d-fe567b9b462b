from collections import OrderedDict

from django.db import models
from django.urls import reverse
from django.utils.safestring import mark_safe
from django_extensions.db.models import ActivatorModel

from model_utils.models import TimeStampedModel
from phonenumber_field.modelfields import PhoneN<PERSON>ber<PERSON>ield

from cysf.fair.models import Topic, Fair
from cysf.projects.models import Project
from django.core.exceptions import ValidationError
from django.conf import settings

from pydash import words


class PrizeGroup(models.Model):
    category_code = models.CharField(max_length=4)
    description = models.CharField(max_length=50)
    order = models.IntegerField(default=0)
    topics = models.ManyToManyField(Topic)
    min_grade = models.IntegerField(default=None, blank=True, null=True)
    max_grade = models.IntegerField(default=None, blank=True, null=True)
    class Meta:
        ordering = ['order', 'category_code']

    def __repr__(self):
        return self.description

    def __str__(self):
        return self.description

class Sponsor(ActivatorModel ,models.Model):
    class Meta:
        ordering = ['name']

    org_code = models.IntegerField(default=0)
    name = models.CharField(max_length=255)
    invoice_to_org = models.BooleanField(default=True)
    invoice_to_other = models.CharField(max_length=64, default="", blank=True)
    invoice_prize_only = models.BooleanField(default=False)
    mail_invoice = models.BooleanField(default=True)
    uc_sponsor = models.BooleanField(default=False, verbose_name="U of C Sponsor")
    year_first_awarded = models.IntegerField(default=0)
    address1 = models.CharField(max_length=64, blank=True)
    address2 = models.CharField(max_length=64, blank=True)
    address3 = models.CharField(max_length=64, blank=True)
    address4 = models.CharField(max_length=64, blank=True)
    gen_revenue_amt = models.FloatField(default=0)
    gen_revenue_invoice = models.BooleanField(default=True)
    contact_email = models.EmailField(default="<EMAIL>", blank=True)
    contact_name = models.CharField(max_length=40, default="", blank=True)
    contact_greeting = models.CharField(max_length=30, default="", blank=True)
    contact_work_phone = PhoneNumberField(blank=True)
    contact_home_phone = PhoneNumberField(blank=True)
    contact_notes = models.CharField(max_length=80, default="", blank=True)
    presenter_name = models.CharField(max_length=100, default="", blank=True)
    presenter_email = models.EmailField(default="<EMAIL>", blank=True)
    presenter_phone = PhoneNumberField(blank=True)
    presenter_role = models.CharField(max_length=100, default="", blank=True)
    outstanding_balance = models.FloatField(default=0)
    assigned_to = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        blank=True,
        null=True,
        on_delete=models.SET_NULL,
        related_name="assigned_sponsors",
        limit_choices_to={'groups__name': 'awards'}
    )

    def __repr__(self):
        return self.name

    def __str__(self):
        return self.name

    def current_invoice(self):
        fair = Fair.get_current_fair()
        invoice = self.invoice_set.filter(fair=fair, sponsor=self).first()
        return invoice

    def generate_invoice(self):
        from constance import config

        fair = Fair.get_current_fair()
        invoice_items = []
        invoice_summary_items = OrderedDict([('General Revenue',0), ('Administrative Fee',0), ('Trophy Nameplates',0), ('Plaques',0),('Prizes',0),('Scholarships',0)])

        invoice, created = self.invoice_set.get_or_create(fair=fair, sponsor=self)
        if invoice.paid:
            raise ValidationError('There is already a paid invoice for this sponsor')

        if not self.invoice_prize_only:
            invoice_items.append({'item': 'Administrative Fee', 'item_value':float(config.INVOICE_ADMIN_FEE)})
            invoice_summary_items['Administrative Fee'] = float(config.INVOICE_ADMIN_FEE)
        if not self.invoice_prize_only and self.gen_revenue_amt:
            invoice_items.append({'item': 'General Revenue', 'item_value':self.gen_revenue_amt})
            invoice_summary_items['General Revenue'] = self.gen_revenue_amt
        for award in self.award_set.all():
            if award.trophy and not self.invoice_prize_only:
                invoice_items.append({'item': f'{award.name} Nameplate', 'item_value':float(config.INVOICE_NAMEPLATE_FEE)})
                invoice_summary_items['Trophy Nameplates'] += float(config.INVOICE_NAMEPLATE_FEE)
            if award.plaques and not self.invoice_prize_only:
                invoice_items.append({'item': f'{award.name} Plaques', 'item_value':max(2-award.plaquesinv,0) * config.INVOICE_PLAQUE_FEE})
                invoice_summary_items['Plaques'] += max(2-award.plaquesinv,0) * float(config.INVOICE_PLAQUE_FEE)
            if award.cheque_amount:
                invoice_items.append({'item': f'{award.name} Cash Prize', 'item_value':float(award.cheque_amount)})
                invoice_summary_items['Prizes'] += float(award.cheque_amount)
            if award.scholarship_amount and not self.invoice_prize_only:
                invoice_items.append({'item': f'{award.name} Scholarship', 'item_value':float(award.scholarship_amount * 2)})
                invoice_summary_items['Scholarships'] += float(award.scholarship_amount * 2)

        invoice.items = {'items': invoice_items, 'summary_items': [{'item':i[0], 'item_value':i[1]} for i in invoice_summary_items.items()]}
        invoice.amount = sum([i['item_value'] for i in invoice.items['items']])
        invoice.save()

    def admin_links(self):
        """Display links to awards and invoices."""
        links = []

        # Awards link with count
        award_count = self.award_set.count()
        awards_url = reverse('admin:awards_award_changelist') + f'?sponsor__id__exact={self.id}'
        links.append(f'<a href="{awards_url}" title="View Awards"><i class="fas fa-trophy"></i> {award_count}</a>')

        # Invoice link for current fair
        fair = Fair.get_current_fair()
        invoice = self.invoice_set.filter(fair=fair, sponsor=self).first()
        if invoice:
            invoice_url = invoice.get_absolute_url()
            links.append(f'<a href="{invoice_url}" title="View Invoice"><i class="fas fa-file-invoice-dollar"></i></a>')

        return mark_safe(' '.join(links))

    admin_links.short_description = 'Links'


# class SponsorComment(models.Model)
class Award(models.Model):
    class Meta:
        ordering = ['name']

    class AwardTypeChoices(models.TextChoices):
        STAGE = 's','Stage'
        FLOOR = 'f', 'Floor'

    name = models.CharField(max_length=255)
    sponsor = models.ForeignKey(Sponsor, db_constraint=False, on_delete=models.SET_NULL, default=None, null=True)
    description = models.TextField(blank=True, verbose_name="Notes")
    prize_code = models.IntegerField(default=1, blank=True)
    award_type = models.CharField(max_length=1, choices=AwardTypeChoices.choices, default=AwardTypeChoices.STAGE)
    criteria = models.TextField(blank=True)
    criteria_summary = models.CharField(max_length=100, blank=True)
    stage_text = models.TextField(blank=True)
    elementary = models.BooleanField(default=True)
    junior = models.BooleanField(default=True)
    intermediate = models.BooleanField(default=True)
    senior = models.BooleanField(default=True)
    stage_group = models.CharField(max_length=20, blank=True, verbose_name="Stage Group")
    stage_sort_order = models.IntegerField(default=0)
    priority_sort_order = models.IntegerField(default=0)
    trophy = models.BooleanField(default=False)
    plaques = models.BooleanField(default=False)
    certificates = models.BooleanField(default=False, verbose_name="Certificates Required?")
    plaquesinv = models.IntegerField(default=0, verbose_name="Plaques in Inventory")
    letter_required = models.BooleanField(default=False)
    cheque_amount = models.IntegerField(default=0)
    scholarship_amount = models.IntegerField(default=0)
    winner = models.ForeignKey(Project, null=True, blank=True, related_name='awards_won_set',
                               on_delete=models.SET_NULL)
    prize_group = models.ForeignKey('awards.PrizeGroup', on_delete=models.PROTECT, null=True, blank=True)
    prize_awarded = models.BooleanField(default=False)
    difficult_to_award = models.BooleanField(default=False)
    wins_only = models.BooleanField(default=False, verbose_name="Wins Only Once")
    count_in_proj_total = models.BooleanField(default=True, verbose_name="Include in counting of awards by project")

    @property
    def total_value(self):
        return self.cheque_amount + self.scholarship_amount

    def admin_links(self):
        """Display link to sponsor record."""
        links = []

        if self.sponsor:
            sponsor_url = reverse('admin:awards_sponsor_change', args=[self.sponsor.id])
            sponsor_name = self.sponsor.name
            links.append(
                f'<a href="{sponsor_url}" title="View Sponsor"><i class="fas fa-building"></i></a>')

        return mark_safe(' '.join(links))

    admin_links.short_description = 'Links'


    def generate_stage_group(self):
        """
        Generates an award group string from the sponsor and presenter names.
        The award group is created by taking the first letter of each significant word
        in the sponsor's name and the presenter's name, concatenating them,
        and converting the result to lowercase.
        """
        stop_words = {
            "the",
            "of",
            "and",
            "a",
            "an",
            "in",
            "on",
            "at",
            "for",
            "to",
            "by",
        }

        sponsor_name = self.sponsor.name if self.sponsor else ""
        presenter_name = self.sponsor.presenter_name if self.sponsor else ""

        sponsor_initials = "".join(
            [word[0] for word in words(sponsor_name) if word.lower() not in stop_words]
        )
        presenter_initials = "".join(
            [
                word[0]
                for word in words(presenter_name)
                if word.lower() not in stop_words
            ]
        )

        self.stage_group = f"{sponsor_initials}_{presenter_initials}".lower()
        self.save()

    def __repr__(self):
        return self.name

    def __str__(self):
        return self.name


class SponsorNote(TimeStampedModel):
    class Meta:
        ordering = ['-created']

    sponsor = models.ForeignKey(Sponsor, on_delete=models.CASCADE)
    note = models.TextField()

class Invoice(TimeStampedModel):
    fair = models.ForeignKey(Fair, on_delete=models.CASCADE)
    sponsor = models.ForeignKey(Sponsor, on_delete=models.CASCADE)
    amount = models.FloatField(default=0)
    paid = models.BooleanField(default=False, blank=True)
    items = models.JSONField(default=dict, blank=True)

    def get_absolute_url(self):
        return reverse('awards:invoice-detail', kwargs={'pk': self.pk})
    def __str__(self):
        return f'{self.sponsor} ({self.fair}) : ${self.amount}'
