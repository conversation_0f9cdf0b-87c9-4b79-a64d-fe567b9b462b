from cysf.awards.models import Invoice
from cysf.utils.views import PDFDetailView


class InvoicePDFDetailView(PDFDetailView):
    model = Invoice
    template_name = 'invoice/invoice_detail.html'
    context_object_name = 'invoice'

    def get_pdf_filename(self):
        return f"invoice-{self.object.fair.year}-{str(self.object.sponsor.org_code).zfill(2)}.pdf"

    def modify_pdf_context(self, context):
        # Add any additional context needed for PDF rendering
        context['is_pdf'] = True
        return context
