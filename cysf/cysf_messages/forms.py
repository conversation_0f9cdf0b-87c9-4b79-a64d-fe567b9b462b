from django import forms
from django.contrib.auth import get_user_model

from .hooks import hookset
from .models import Message
from cysf.projects.models import Project


class ProjectModelChoiceField(forms.ModelChoiceField):
    def label_from_instance(self, obj):
        return hookset.display_name(obj)


class UserModelChoiceField(forms.ModelChoiceField):
    def label_from_instance(self, obj):
        return hookset.display_name(obj)


class UserModelMultipleChoiceField(forms.ModelMultipleChoiceField):
    def label_from_instance(self, obj):
        return hookset.display_name(obj)


class NewMessageForm(forms.Form):
    to_user = UserModelChoiceField(queryset=get_user_model().objects.none())
    project = ProjectModelChoiceField(queryset=Project.objects.none())
    content = forms.CharField(widget=forms.Textarea)

    def __init__(self, *args, **kwargs):
        self.user = kwargs.pop("user")
        super().__init__(*args, **kwargs)
        self.fields["to_user"].queryset = hookset.get_user_choices(self.user)
        self.fields["project"].queryset = hookset.get_project_choices(self.user)
        if self.initial.get("to_user") is not None:
            qs = self.fields["to_user"].queryset.filter(pk=self.initial["to_user"])
            self.fields["to_user"].queryset = qs

    def save(self, commit=True):
        data = self.cleaned_data
        return Message.new_message(
            self.user,
            [data["to_user"]],
            data["project"],
            data["subject"],
            data["content"],
        )

    class Meta:
        model = Message
        fields = ["to_user", "subject", "content"]


class OldNewMessageForm(forms.Form):
    subject = forms.CharField()
    to_user = UserModelChoiceField(queryset=get_user_model().objects.none())
    project = ProjectModelChoiceField(queryset=Project.objects.none())
    content = forms.CharField(widget=forms.Textarea)

    def __init__(self, *args, **kwargs):
        self.user = kwargs.pop("user")
        super().__init__(*args, **kwargs)
        self.fields["to_user"].queryset = hookset.get_user_choices(self.user)
        self.fields["project"].queryset = hookset.get_project_choices(self.user)
        if self.initial.get("to_user") is not None:
            qs = self.fields["to_user"].queryset.filter(pk=self.initial["to_user"])
            self.fields["to_user"].queryset = qs

    def save(self, commit=True):
        data = self.cleaned_data
        return Message.new_message(
            self.user,
            [data["to_user"]],
            data["project"],
            data["subject"],
            data["content"],
        )

    class Meta:
        model = Message
        fields = ["to_user", "subject", "content"]


class MessageReplyForm(forms.ModelForm):
    def __init__(self, *args, **kwargs):
        self.thread = kwargs.pop("thread")
        self.user = kwargs.pop("user")
        super().__init__(*args, **kwargs)

    def save(self, commit=True):
        return Message.new_message(self.thread, self.user, self.cleaned_data["content"])

    class Meta:
        model = Message
        fields = ["content"]
