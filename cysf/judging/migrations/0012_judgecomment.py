# Generated by Django 3.1 on 2022-02-21 22:14

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('users', '0017_auto_20220221_1514'),
        ('projects', '0020_auto_20220221_1512'),
        ('judging', '0011_auto_20210327_1920'),
    ]

    operations = [
        migrations.CreateModel(
            name='JudgeComment',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateTimeField()),
                ('comment', models.CharField(help_text='a comment for other judges.', max_length=500)),
                ('judge', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='judge_comments', to='users.judge')),
                ('project', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='judge_comments', to='projects.project')),
            ],
            options={
                'verbose_name': 'Judge Comments',
            },
        ),
    ]
