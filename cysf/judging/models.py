from functools import cached_property
from django.db import models
from django.utils.safestring import mark_safe

from cysf.awards.models import Award
from cysf.fair.models import Fair
from cysf.projects.models import Project
from cysf.users.models import User
from model_utils.managers import InheritanceManager
from model_utils import Choices
from django.core.exceptions import ValidationError

from cysf.users.models.people import JudgeTeam
from pydash.strings import truncate

# ...

FEEDBACK_CHOICES = Choices(
    ("needs_improvement", "Needs Improvement"),
    ("good", "Good"),
    ("excellent", "Excellent"),
)


class JudgeTeamConfig(models.Model):
    project_level = models.CharField(max_length=1, choices=Project.PROJECT_LEVELS.choices, null=True, blank=True)
    project_category = models.CharField(max_length=25, choices=Project.PROJECT_CATEGORIES, null=True, blank=True)
    start = models.IntegerField(default=401)
    end = models.IntegerField(default=401)
    projects_per_team = models.IntegerField(default=0)
    language = models.CharField(
        choices=Project.LANGUAGES, default=None, max_length=10, null=True, blank=True
    )
    locked = models.BooleanField(default=False)
    award_round = models.BooleanField(default=False)

    @staticmethod
    def team_username(team_number):
        return f'{team_number}'

    @staticmethod
    def delete_teams():
        User.objects.filter(judge_team__judge_team_config_set__locked=False).delete()
        JudgeTeam.objects.filter(judge_team_config_set__locked=False).delete()

    def create_award_teams(self):
        from cysf.awards.models import PrizeGroup
        result = 0
        for prize_group, i in zip(PrizeGroup.objects.all(), range(self.start, self.end + 1)):
            user = User.objects.filter(username=JudgeTeamConfig.team_username(i)).first()
            if not user:
                user = User.objects.create_user(username=JudgeTeamConfig.team_username(i))  # , password=str(i))
            judge_team = user.make_judge_team(self, i)
            judge_team.prize_group = prize_group
            judge_team.save()
            result += 1

        # An all-award judging team
        user = User.objects.filter(username=JudgeTeamConfig.team_username(999)).first()
        if not user:
            user = User.objects.create_user(username=JudgeTeamConfig.team_username(999))  # , password=str(i))
        judge_team = user.make_judge_team(self, 999)
        judge_team.prize_group = None
        judge_team.save()
        result += 1

        return result

    def create_teams(self):
        result = 0
        if self.locked:
            return 0
        if self.award_round:
            result = self.create_award_teams()
            return result

        for i in range(self.start, self.end + 1):
            user = User.objects.filter(username=JudgeTeamConfig.team_username(i)).first()
            if not user:
                user = User.objects.create_user(username=JudgeTeamConfig.team_username(i))  # , password=str(i))
            user.make_judge_team(self, i)
            result += 1
        return result

    def get_team_project_counts(self):
        projects = Fair.get_current_fair().project_set
        project_counts = {p: projects.filter(judge_team=p).count() for p in list(range(self.start, self.end + 1))}
        return project_counts

    def assign_teams(self):
        if self.locked:
            return 0
        updated_projects = []
        project_counts = self.get_team_project_counts()
        project_set = Fair.get_current_fair().project_set.filter(project_level=self.project_level,
                                                                 judge_team__isnull=True).exclude(
            location__isnull=True).exclude(location='')
        if self.project_category:
            project_set = project_set.filter(project_category=self.project_category)
        if self.language is not None:
            project_set = project_set.filter(language=self.language)
        for project in project_set:
            if self.projects_per_team:
                try:
                    next_team = [p for p in sorted(project_counts.items(), key=lambda item: (-item[1], item[0])) if
                                 p[1] < self.projects_per_team][0][0]
                except IndexError:  # All teams are at max.
                    continue
            else:
                next_team = sorted(project_counts.items(), key=lambda item: (item[1], item[0]))[0][0]
            project.judge_team_id = next_team
            updated_projects.append(project)
            project_counts[next_team] += 1
        Project.objects.bulk_update(updated_projects, fields=['judge_team'])

    def clear_projects(self):
        if self.locked:
            return
        Fair.get_current_fair().project_set.filter(project_category=self.project_category).update(judge_team=None)

    def team_count(self):
        return self.judgeteam_set.count()

    def __str__(self):
        return f"{self.project_level} - {self.project_category}"


class AwardRoundScore(models.Model):
    class Meta:
        verbose_name = "Award Round Score"
        ordering = ["award__priority_sort_order"]

    judge_team = models.ForeignKey(JudgeTeam, on_delete=models.CASCADE, )
    award = models.ForeignKey(Award, on_delete=models.CASCADE, )
    first_location = models.CharField(max_length=3, null=True, blank=True)
    second_location = models.CharField(max_length=3, null=True, blank=True)
    first_project = models.ForeignKey(Project, on_delete=models.CASCADE, related_name="award_score_set", null=True,
                                      blank=True)
    second_project = models.ForeignKey(Project, on_delete=models.CASCADE, related_name="+", null=True, blank=True)

    def project_label(self, project):
        result = ""
        medals = {'1': 'yellow black-text', '2': 'grey black-text', '3': 'brown white-text',
                  'H': 'blue-grey white-text'}
        if project:
            result = f'<b>{truncate(project.name, length=50)}</b><br/>{project.student1_name}'
            if project.student2_name:
                result += f', {project.student2_name}'
            project.award_label = mark_safe(result)
        else:
            project = {'location': '', 'award_label': mark_safe('<div class="bg-danger">NOT A VALID PROJECT</div>')}
        return project

    def project_set(self):
        result = []
        for location, project in zip([self.first_location, self.second_location],
                                     [self.first_project, self.second_project]):
            if location:
                result.append(self.project_label(project))
        return result

    @cached_property
    def summary_label(self):
        result = [str(self.award.total_value)]

        if self.award.elementary:
            result.append("Elementary")
        if self.award.junior:
            result.append("Junior")
        if self.award.intermediate:
            result.append("Intermediate")
        if self.award.senior:
            result.append("Senior")
        if self.award.wins_only:
            result.append("Wins Only")
        return ' | '.join(result)

    @property
    def award_warnings(self):
        result = []
        project = self.first_project
        award = self.award
        level_key = dict(zip(['E', 'J', 'I', 'S'], [award.elementary, award.junior, award.intermediate, award.senior]))
        if project:
            if award.wins_only and len(project.award_score_data['won']) > 1:
                result.append('WINNER WINS ONLY ONE AWARD')
            if project.medal and  project.medal.medal_type != '1':
                result.append('NO GOLD MEDAL')
            if not level_key.get(project.project_level, False):
                result.append('WRONG LEVEL')
            project_topics = set(project.project_topics.values_list('id', flat=True))
            award_topics = set(award.prize_group.topics.values_list('id', flat=True))
            if not len(project_topics.intersection(award_topics)):
                result.append('WRONG TOPICS')

        return ' | '.join(result)

    def save(self, **args):
        self.first_project = self.second_project = None
        if self.first_location:
            self.first_location = self.first_location.upper()
            self.first_project = Fair.get_current_fair().checked_in_projects().filter(location__iexact=self.first_location).first()
        if self.second_location:
            self.second_location = self.second_location.upper()
            self.second_project = Fair.get_current_fair().checked_in_projects().filter(location__iexact=self.second_location).first()
        super().save(**args)

    @staticmethod
    def copy_award_scores_to_deliberation():
        award_forms = AwardRoundScore.objects.exclude(judge_team_id=999)
        for source_form in award_forms:
            target_form = AwardRoundScore.objects.get(judge_team_id=999, award=source_form.award)
            target_form.first_location, target_form.second_location = source_form.first_location, source_form.second_location
            target_form.save()

    @staticmethod
    def copy_deliberation_to_awards():
        award_forms = AwardRoundScore.objects.filter(judge_team_id=999).prefetch_related('award')
        for award_form in award_forms:
            award = award_form.award
            if award.winner != award_form.first_project:
                award.winner = award_form.first_project
                award.save()


class PublishedFeedback(models.Model):
    class Meta:
        verbose_name = "Published Comments"

    @property
    def verbose_name(self):
        return self._meta.verbose_name

    objects = InheritanceManager()

    project = models.ForeignKey(
        Project, on_delete=models.CASCADE, related_name="published_comment_set"
    )

    comment = models.CharField(
        blank=True,
        max_length=2000,
        help_text="A brief comment.",
    )

    def clean(self):
        raise ValidationError("Unimplemented")


class Feedback(models.Model):
    class Meta:
        verbose_name = "Comment Form"

    @property
    def verbose_name(self):
        return self._meta.verbose_name

    objects = InheritanceManager()

    comment = models.CharField(
        blank=True,
        max_length=1000,
        verbose_name="Sandwich Comment",
        help_text="Begin and end with positive comments/encouragement.  Include constructive feedback in the middle.",
    )

    constructive = models.CharField(
        blank=True,
        max_length=500,
        help_text="A brief constructive comment.",
    )
    celebratory = models.CharField(
        blank=True,
        max_length=500,
        help_text="A brief celebratory comment.",
    )

    complete = models.BooleanField(
        default=False, blank=True, help_text="Feedback Complete"
    )

    def clean(self):
        raise ValidationError("Unimplemented")


class EFeedback(Feedback):
    class Meta:
        verbose_name = "Elementary Comment Form"

    creativity_fields = [
        "originality",
        "resourcefulness",
        "application",
    ]
    communication_fields = ["visual", "oral"]

    # common sections
    # Creativity
    originality = models.CharField(
        choices=FEEDBACK_CHOICES,
        max_length=20,
        default=FEEDBACK_CHOICES.good,
        help_text="Originality.",
    )
    resourcefulness = models.CharField(
        choices=FEEDBACK_CHOICES,
        max_length=20,
        default=FEEDBACK_CHOICES.good,
        help_text="Resourcefulness: equipment and methods.",
    )
    application = models.CharField(
        choices=FEEDBACK_CHOICES,
        max_length=20,
        default=FEEDBACK_CHOICES.good,
        help_text="Potential and practical uses identified.",
    )
    # Communication
    visual = models.CharField(
        choices=FEEDBACK_CHOICES,
        max_length=20,
        default=FEEDBACK_CHOICES.good,
        help_text="Legibility, neatness, organization,headings and labels.",
    )
    oral = models.CharField(
        choices=FEEDBACK_CHOICES,
        max_length=20,
        default=FEEDBACK_CHOICES.good,
        help_text="Clear, logical, use of scientific terms.",
    )

    def clean(self):
        result = list(
            filter(
                lambda field: getattr(self, field)
                              == FEEDBACK_CHOICES.needs_improvement,
                self.method_fields + self.creativity_fields + self.communication_fields,
            )
        )
        if len(result) > 3:
            self.complete = False
            raise ValidationError("Too many NEEDS IMPROVEMENT.  Maximum 3.")


class EEFeedback(EFeedback):
    class Meta:
        verbose_name = "Elementary Experimental Comment Form"

    method_fields = [
        "background",
        "sources",
        "hypothesis",
        "scientific_method",
        "variables",
        "repetition",
        "data",
        "observations",
        "conclusions",
        "logbook",
    ]

    background = models.CharField(
        choices=FEEDBACK_CHOICES,
        max_length=20,
        default=FEEDBACK_CHOICES.good,
        help_text="Background reading evident.",
    )
    sources = models.CharField(
        choices=FEEDBACK_CHOICES,
        max_length=20,
        default=FEEDBACK_CHOICES.good,
        help_text="Sources properly credited.",
    )
    hypothesis = models.CharField(
        choices=FEEDBACK_CHOICES,
        max_length=20,
        default=FEEDBACK_CHOICES.good,
        help_text="Problem and hypothesis clearly stated.",
    )
    scientific_method = models.CharField(
        choices=FEEDBACK_CHOICES,
        max_length=20,
        default=FEEDBACK_CHOICES.good,
        help_text="Scientific method followed",
    )
    variables = models.CharField(
        choices=FEEDBACK_CHOICES,
        max_length=20,
        default=FEEDBACK_CHOICES.good,
        help_text="Variables used and understood.",
    )
    repetition = models.CharField(
        choices=FEEDBACK_CHOICES,
        max_length=20,
        default=FEEDBACK_CHOICES.good,
        help_text="Appropriate repetition (trial number, sample size) used.",
    )
    data = models.CharField(
        choices=FEEDBACK_CHOICES,
        max_length=20,
        default=FEEDBACK_CHOICES.good,
        help_text="Data presentation: tables, charts and graphs.",
    )
    observations = models.CharField(
        choices=FEEDBACK_CHOICES,
        max_length=20,
        default=FEEDBACK_CHOICES.good,
        help_text="Observations consistent with data.",
    )
    conclusions = models.CharField(
        choices=FEEDBACK_CHOICES,
        max_length=20,
        default=FEEDBACK_CHOICES.good,
        help_text="Conclusions related to hypothesis.",
    )
    logbook = models.CharField(
        choices=FEEDBACK_CHOICES,
        max_length=20,
        default=FEEDBACK_CHOICES.good,
        help_text="Logbook: procedures and results recorded.",
    )


class ENEFeedback(EFeedback):
    class Meta:
        verbose_name = "Elementary Non-Experimental Comment Form"

    method_fields = [
        "depth",
        "sources",
        "concepts",
        "issues",
        "data",
        "organisation",
        "conclusions",
        "analysis",
        "logbook",
    ]

    depth = models.CharField(
        choices=FEEDBACK_CHOICES,
        max_length=20,
        default=FEEDBACK_CHOICES.good,
        help_text="Depth of research.",
    )
    sources = models.CharField(
        choices=FEEDBACK_CHOICES,
        max_length=20,
        default=FEEDBACK_CHOICES.good,
        help_text="Sources properly credited.",
    )
    concepts = models.CharField(
        choices=FEEDBACK_CHOICES,
        max_length=20,
        default=FEEDBACK_CHOICES.good,
        help_text="Key points and concepts identified",
    )
    issues = models.CharField(
        choices=FEEDBACK_CHOICES,
        max_length=20,
        default=FEEDBACK_CHOICES.good,
        help_text="Problems and issues recognized.",
    )
    data = models.CharField(
        choices=FEEDBACK_CHOICES,
        max_length=20,
        default=FEEDBACK_CHOICES.good,
        help_text="Data scientifically accurate.",
    )
    organisation = models.CharField(
        choices=FEEDBACK_CHOICES,
        max_length=20,
        default=FEEDBACK_CHOICES.good,
        help_text="Well-organized information.",
    )
    conclusions = models.CharField(
        choices=FEEDBACK_CHOICES,
        max_length=20,
        default=FEEDBACK_CHOICES.good,
        help_text="Summary and conclusion based on research.",
    )
    analysis = models.CharField(
        choices=FEEDBACK_CHOICES,
        max_length=20,
        default=FEEDBACK_CHOICES.good,
        help_text="Critical analysis of research.",
    )
    logbook = models.CharField(
        choices=FEEDBACK_CHOICES,
        max_length=20,
        default=FEEDBACK_CHOICES.good,
        help_text="Logbook showed research notes and contacts.",
    )


class SFeedback(Feedback):
    class Meta:
        verbose_name = "Secondary Comment Form"

    problem_fields = [
        "existing_knowledge",
        "direction",
    ]
    analysis_fields = [
        "logbook",
        "consistency",
        "analysis",
        "logic",
    ]
    creativity_fields = [
        "originality",
        "resourcefulness",
        "application",
        "growth",
    ]
    communication_fields = ["visual", "oral"]

    # common sections

    # Objective
    existing_knowledge = models.CharField(
        choices=FEEDBACK_CHOICES,
        max_length=20,
        default=FEEDBACK_CHOICES.good,
        help_text="Problem / Hypothesis / Objectiv was formed using existing scientific knowledge.",
    )
    direction = models.CharField(
        choices=FEEDBACK_CHOICES,
        max_length=20,
        default=FEEDBACK_CHOICES.good,
        help_text="Problem / Hypothesis / Objectiv gave clear direction for the rest of the project.",
    )
    # Analysis
    logbook = models.CharField(
        choices=FEEDBACK_CHOICES,
        max_length=20,
        default=FEEDBACK_CHOICES.good,
        help_text="Progress was properly recorded in a logbook.",
    )
    consistency = models.CharField(
        choices=FEEDBACK_CHOICES,
        max_length=20,
        default=FEEDBACK_CHOICES.good,
        help_text="Results were related to Problem / Hypothesis / Objective.",
    )
    analysis = models.CharField(
        choices=FEEDBACK_CHOICES,
        max_length=20,
        default=FEEDBACK_CHOICES.good,
        help_text="Data/research material were critically analyzed.",
    )
    logic = models.CharField(
        choices=FEEDBACK_CHOICES,
        max_length=20,
        default=FEEDBACK_CHOICES.good,
        help_text="Conclusions were supported by the data/research material.",
    )
    # Creativity
    originality = models.CharField(
        choices=FEEDBACK_CHOICES,
        max_length=20,
        default=FEEDBACK_CHOICES.good,
        help_text="Problem approached with originality.",
    )
    resourcefulness = models.CharField(
        choices=FEEDBACK_CHOICES,
        max_length=20,
        default=FEEDBACK_CHOICES.good,
        help_text="Resourceful use of equipment and/or materials shown.",
    )
    application = models.CharField(
        choices=FEEDBACK_CHOICES,
        max_length=20,
        default=FEEDBACK_CHOICES.good,
        help_text="Practical application and extensions of the project identified.",
    )
    growth = models.CharField(
        choices=FEEDBACK_CHOICES,
        max_length=20,
        default=FEEDBACK_CHOICES.good,
        help_text="A deeper understanding of the topic was gained.",
    )
    # Communication
    oral = models.CharField(
        choices=FEEDBACK_CHOICES,
        max_length=20,
        default=FEEDBACK_CHOICES.good,
        help_text="The oral presentation was clear, concise and logical.",
    )
    depth = models.CharField(
        choices=FEEDBACK_CHOICES,
        max_length=20,
        default=FEEDBACK_CHOICES.good,
        help_text="Depth of understanding shown.",
    )
    format = models.CharField(
        choices=FEEDBACK_CHOICES,
        max_length=20,
        default=FEEDBACK_CHOICES.good,
        help_text="Written information including proper credit and applicable forms.",
    )
    visual = models.CharField(
        choices=FEEDBACK_CHOICES,
        max_length=20,
        default=FEEDBACK_CHOICES.good,
        help_text="The visual display effectively demonstrated the project.",
    )

    def clean(self):
        result = list(
            filter(
                lambda field: getattr(self, field)
                              == FEEDBACK_CHOICES.needs_improvement,
                self.problem_fields
                + self.method_fields
                + self.analysis_fields
                + self.creativity_fields
                + self.communication_fields,
            )
        )
        if len(result) > 5:
            self.complete = False
            raise ValidationError("Too many NEEDS IMPROVEMENT.  Maximum 5.")


class SEFeedback(SFeedback):
    class Meta:
        verbose_name = "Secondary Experiment Comment Form"

    method_fields = ["design", "variables", "repetition"]

    design = models.CharField(
        choices=FEEDBACK_CHOICES,
        max_length=20,
        default=FEEDBACK_CHOICES.good,
        help_text="Experimental design was appropriate and clearly described.",
    )
    variables = models.CharField(
        choices=FEEDBACK_CHOICES,
        max_length=20,
        default=FEEDBACK_CHOICES.good,
        help_text="Controlled, manipulated & responding variables recognized.",
    )
    repetition = models.CharField(
        choices=FEEDBACK_CHOICES,
        max_length=20,
        default=FEEDBACK_CHOICES.good,
        help_text="Repetition/sample size used to achieve reliable results.",
    )


class SIFeedback(SFeedback):
    class Meta:
        verbose_name = "Secondary Innovation Comment Form"

    method_fields = ["limits", "design", "testing"]

    limits = models.CharField(
        choices=FEEDBACK_CHOICES,
        max_length=20,
        default=FEEDBACK_CHOICES.good,
        help_text="Suitability and limitations of materials/methods understood.",
    )
    design = models.CharField(
        choices=FEEDBACK_CHOICES,
        max_length=20,
        default=FEEDBACK_CHOICES.good,
        help_text="Design was efficient, effective, and addressed the objective.",
    )
    testing = models.CharField(
        choices=FEEDBACK_CHOICES,
        max_length=20,
        default=FEEDBACK_CHOICES.good,
        help_text="Design appropriately tested, including adjustments/modifications.",
    )


class SRFeedback(SFeedback):
    class Meta:
        verbose_name = "Secondary Study Comment Form"

    method_fields = ["sources", "organisation", "concepts"]

    sources = models.CharField(
        choices=FEEDBACK_CHOICES,
        max_length=20,
        default=FEEDBACK_CHOICES.good,
        help_text="A wide variety of appropSriate sources were used.",
    )
    organisation = models.CharField(
        choices=FEEDBACK_CHOICES,
        max_length=20,
        default=FEEDBACK_CHOICES.good,
        help_text="Research was comprehensive and well-organized.",
    )
    concepts = models.CharField(
        choices=FEEDBACK_CHOICES,
        max_length=20,
        default=FEEDBACK_CHOICES.good,
        help_text="Key concepts, alternate explanations were explored.",
    )


class Score(models.Model):
    class Meta:
        verbose_name = "Score Form"

    @property
    def verbose_name(self):
        return self._meta.verbose_name

    objects = InheritanceManager()

    # overall scores
    problem = models.IntegerField(
        default=0,
    )
    method = models.IntegerField(
        default=0,
    )
    analysis = models.IntegerField(
        default=0,
    )
    creativity = models.IntegerField(
        default=0,
    )
    communication = models.IntegerField(
        default=0,
    )
    difficulty = models.IntegerField(
        default=0,
    )
    total = models.IntegerField(
        default=0,
    )
    total_override = models.IntegerField(
        default=0,
        verbose_name="Score Total",
    )

    complete = models.BooleanField(
        default=False, blank=True, help_text="Scoring Complete"
    )

    def calc_score(self):
        scores = [
            ("problem", self.problem_fields),
            ("method", self.method_fields),
            ("analysis", self.analysis_fields),
            ("creativity", self.creativity_fields),
            ("communication", self.communication_fields),
            ("difficulty", self.difficulty_fields),
        ]
        self.total = 0
        for category, fields in scores:
            category_total = sum([getattr(self, field) for field in fields])
            self.total += category_total
            setattr(self, category, category_total)
        if self.total_override:
            self.total = self.total_override = min(100, max(0, self.total_override))
            self.complete = True if self.total else False

    def save(self, *args, **kwargs):
        self.total = self.total_override = min(100, max(0, self.total_override))
        self.complete = True if self.total else False
        self.project.mark = self.total
        super().save(*args, **kwargs)
        self.project.save()


class SScore(Score):
    class Meta:
        verbose_name = "Secondary Score Form"

    creativity_fields = [
        "originality",
        "independence",
        "resourcefulness",
        "improvements",
        "application",
    ]
    communication_fields = ["presentation", "depth", "completeness", "effectiveness"]
    difficulty_fields = ["exceptional", "learning"]

    # common sections
    # Creativity
    originality = models.IntegerField(
        default=0,
        blank=True,
        help_text="The problem was approached with originality.",
    )
    independence = models.IntegerField(
        default=0,
        blank=True,
        help_text="Independent motivation, design and thinking were demonstrated.",
    )
    resourcefulness = models.IntegerField(
        default=0,
        blank=True,
        help_text="Resourceful use of equipment and/or materials was shown.",
    )
    improvements = models.IntegerField(
        default=0,
        blank=True,
        help_text="Improvements that can be made to the project were indicated.",
    )
    application = models.IntegerField(
        default=0,
        blank=True,
        help_text="Practical applications and future research for the project were identified.",
    )
    # Communication
    presentation = models.IntegerField(
        default=0,
        blank=True,
        help_text="The presentation was clear, logical and concise.",
    )
    depth = models.IntegerField(
        default=0,
        blank=True,
        help_text="Answers to questions were clear and signified depth of understanding.",
    )
    completeness = models.IntegerField(
        default=0,
        blank=True,
        help_text="All required written information including credits, citations and applicable ethics/consent forms were presented.",
    )
    effectiveness = models.IntegerField(
        default=0,
        blank=True,
        help_text="The visual display was effective, with a logical and self-explanatory layout.",
    )
    # Difficulty
    exceptional = models.IntegerField(
        verbose_name="Commensuration",
        default=0,
        blank=True,
        help_text="The project was exceptional (consider the student’s grade level).",
    )
    learning = models.IntegerField(
        default=0,
        blank=True,
        help_text="The student gained a deeper understanding of the topic.",
    )


class EScore(Score):
    class Meta:
        verbose_name = "Elementary Score Form"

    creativity_fields = [
        "creative",
        "resourcefulness",
        "data_interpretation",
        "improvements",
        "application",
    ]
    communication_fields = ["presentation", "depth", "completeness", "effectiveness"]
    difficulty_fields = ["exceptional"]

    # common sections
    # Creativity
    creative = models.IntegerField(
        default=0,
        blank=True,
        help_text="The project was imaginative and creative.",
    )
    resourcefulness = models.IntegerField(
        default=0,
        blank=True,
        help_text="There was resourceful use of equipment/information gathered.",
    )
    data_interpretation = models.IntegerField(
        default=0,
        blank=True,
        help_text="Creativity was shown in the interpretation of the data/information gathered (i.e. outliers noted, unexplained findings examined).",
    )
    improvements = models.IntegerField(
        default=0,
        blank=True,
        help_text="Thought was given to how the project could be improved or done differently.",
    )
    application = models.IntegerField(
        default=0,
        blank=True,
        help_text="Future spin-offs or potential applications of the project were identified.",
    )
    # Communication
    presentation = models.IntegerField(
        default=0,
        blank=True,
        help_text="The presentation was clear, logical and concise",
    )
    depth = models.IntegerField(
        default=0,
        blank=True,
        help_text="Questions were answered competently and accurately.",
    )
    completeness = models.IntegerField(
        default=0,
        blank=True,
        help_text="Outside sources were properly credited and a bibliography was properly cited.",
    )
    effectiveness = models.IntegerField(
        default=0,
        blank=True,
        help_text="The outine display effectively presented the project.",
    )
    # Difficulty
    exceptional = models.IntegerField(
        verbose_name="Commensuration",
        default=0,
        blank=True,
        help_text="The degree of difficulty of this project was exceptional.",
    )


class SStudyScore(SScore):
    class Meta:
        verbose_name = "Secondary Study Score Form"

    problem_fields = ["problem_existing_knowledge", "objective"]
    method_fields = ["data_breadth", "data_quality", "organisation", "log_book"]
    analysis_fields = ["concepts", "result_analysis", "conclusion"]

    problem_existing_knowledge = models.IntegerField(
        default=0,
        blank=True,
        verbose_name="Existing Knowledge",
        help_text="Existing knowledge and background research were integrated into the formation of the problem/objective.",
    )
    objective = models.IntegerField(
        default=0,
        blank=True,
        help_text="The objective was clearly stated and provided direction and appropriate scope for the project.",
    )
    data_breadth = models.IntegerField(
        default=0,
        blank=True,
        help_text="The information acquired showed depth and variety.",
    )
    data_quality = models.IntegerField(
        default=0,
        blank=True,
        help_text="The data gathered were reliable and appropriate (multiple independent sources were used and verified).",
    )
    organisation = models.IntegerField(
        default=0,
        blank=True,
        help_text="The research data were comprehensive and well-organised.",
    )
    log_book = models.IntegerField(
        default=0,
        blank=True,
        help_text="Logbook recorded progress of the project including detailed research notes, resources and discussions",
    )
    concepts = models.IntegerField(
        default=0,
        blank=True,
        help_text="Key scientific concepts, including alternate viewpoints, of the research topic were identified and explored.",
    )
    result_analysis = models.IntegerField(
        default=0,
        blank=True,
        help_text="Critical analysis/interpretation of research material was presented (e.g. comparison of sources, surveys and statistics).",
    )
    conclusion = models.IntegerField(
        default=0,
        blank=True,
        help_text="Logical conclusions based on the research were reached.",
    )


class SInnovationScore(SScore):
    class Meta:
        verbose_name = "Secondary Innovation Score Form"

    problem_fields = ["problem_existing_knowledge", "definition"]
    method_fields = ["suitability", "design", "testing", "log_book"]
    analysis_fields = ["connection", "iteration", "understanding"]

    problem_existing_knowledge = models.IntegerField(
        default=0,
        blank=True,
        verbose_name="Existing Knowledge",
        help_text="Existing knowledge and background research were integrated into the formation of the problem/objective",
    )
    definition = models.IntegerField(
        default=0,
        blank=True,
        help_text="A problem was clearly identified and provided direction for the project.",
    )
    suitability = models.IntegerField(
        default=0,
        blank=True,
        help_text="Suitability and limitations of the chosen materials/methods were understood.",
    )
    design = models.IntegerField(
        default=0,
        blank=True,
        help_text="The project design was efficient, effective, and addressed the problem/objective.",
    )
    testing = models.IntegerField(
        default=0,
        blank=True,
        help_text="The project design was appropriately tested.",
    )
    log_book = models.IntegerField(
        default=0,
        blank=True,
        help_text=" Logbook recorded progress of the project, including detailed procedures, results and modifications.",
    )
    connection = models.IntegerField(
        default=0,
        blank=True,
        help_text="A connection was established between the problem/objective and results.",
    )
    iteration = models.IntegerField(
        default=0,
        blank=True,
        help_text="Testing was carried out to modify the project design and correct shortcomings as the project proceeded.",
    )
    understanding = models.IntegerField(
        default=0,
        blank=True,
        help_text="The student understood how well the problem was solved.",
    )


class SExperimentScore(SScore):
    class Meta:
        verbose_name = "Secondary Experimental Score Form"

    problem_fields = ["hypothesis_existing_knowledge", "hypothesis_quality"]
    method_fields = [
        "experimental_design",
        "variables",
        "repetition_and_sample",
        "log_book",
    ]
    analysis_fields = ["data_analysis", "sources_of_error", "conclusion_quality"]

    hypothesis_existing_knowledge = models.IntegerField(
        default=0,
        blank=True,
        verbose_name="Existing Knowledge",
        help_text="Existing knowledge and background research were integrated into the formation of the problem/hypothesis",
    )
    hypothesis_quality = models.IntegerField(
        default=0,
        blank=True,
        help_text="The hypothesis related to the problem, was clearly stated, and provided direction for the project.",
    )
    experimental_design = models.IntegerField(
        default=0,
        blank=True,
        help_text="Experimental design was clearly described and appropriate for solving the problem.",
    )
    variables = models.IntegerField(
        default=0,
        blank=True,
        help_text="Controlled, manipulated and responding variables were identified and understood.",
    )
    repetition_and_sample = models.IntegerField(
        default=0,
        blank=True,
        help_text="Repetitions of tests and/or appropriate sample size were used to achieve reliable results.",
    )
    log_book = models.IntegerField(
        default=0,
        blank=True,
        help_text="Logbook recorded progress of the project including detailed procedures, results and original data.",
    )
    data_analysis = models.IntegerField(
        default=0,
        blank=True,
        help_text="Appropriate methods were used to present and analyze data (e.g. graphs, charts and statistics).",
    )
    sources_of_error = models.IntegerField(
        default=0,
        blank=True,
        help_text="Sources of error and experimental limitations (e.g. the effect of variables that could not be controlled) were understood.",
    )
    conclusion_quality = models.IntegerField(
        default=0,
        blank=True,
        help_text="Conclusions were related to the problem/hypothesis and were supported by the data presented.",
    )


class ENonExperimentScore(EScore):
    class Meta:
        verbose_name = "Elementary Non-Experimental Score Form"

    problem_fields = ["topic"]
    method_fields = ["research", "accuracy", "organisation", "log_book"]
    analysis_fields = [
        "key_points",
        "problems",
        "interpretation",
        "conclusion",
        "innovation",
    ]

    topic = models.IntegerField(
        default=0,
        blank=True,
        help_text="The topic was clearly stated and provided direction and appropriate scope for the project.",
    )
    research = models.IntegerField(
        default=0,
        blank=True,
        help_text="Evidence of extensive research including reading and contacting knowledgeable people was demonstrated.",
    )
    accuracy = models.IntegerField(
        default=0,
        blank=True,
        verbose_name="Existing Knowledge",
        help_text=" The scientific information presented was accurate.",
    )
    organisation = models.IntegerField(
        default=0,
        blank=True,
        help_text="The information was effectively gathered, combined and organized.",
    )
    log_book = models.IntegerField(
        default=0,
        blank=True,
        help_text="Logbook recorded project progress including detailed research notes, contact names and discussions.",
    )
    key_points = models.IntegerField(
        default=0,
        blank=True,
        help_text="Key points and concepts of the research topic were identified.",
    )

    problems = models.IntegerField(
        default=0,
        blank=True,
        help_text="Problems or issues related to the subject were understood.",
    )
    interpretation = models.IntegerField(
        default=0,
        blank=True,
        help_text="Critical analysis/interpretation of research material was presented.",
    )

    conclusion = models.IntegerField(
        default=0,
        blank=True,
        help_text="A logical conclusion/summary based on the research was reached.",
    )
    innovation = models.IntegerField(
        default=0,
        blank=True,
        help_text="New ideas were formulated as a result of the research project.",
    )


class EExperimentScore(EScore):
    class Meta:
        verbose_name = "Elementary Experimental Score Form"

    problem_fields = ["hypothesis_existing_knowledge", "background"]
    method_fields = [
        "experimental_design",
        "variables",
        "repetition_and_sample",
        "log_book",
    ]
    analysis_fields = [
        "data_analysis",
        "results",
        "conclusion_quality",
        "sources_of_error",
    ]

    hypothesis_existing_knowledge = models.IntegerField(
        default=0,
        blank=True,
        verbose_name="Existing Knowledge",
        help_text="The problem/hypothesis was clearly stated.",
    )
    background = models.IntegerField(
        default=0,
        blank=True,
        help_text="Adequate background reading was evident in the presentation.",
    )
    experimental_design = models.IntegerField(
        default=0,
        blank=True,
        help_text="Experimental design reflected understanding of the scientific method and underlying scientific principles.",
    )
    variables = models.IntegerField(
        default=0,
        blank=True,
        help_text="Controlled, manipulated and responding variables were identified and understood.",
    )
    repetition_and_sample = models.IntegerField(
        default=0,
        blank=True,
        help_text="Repetition of tests (minimum three trials) and/or appropriate sample size were used to achieve reliable results.",
    )
    log_book = models.IntegerField(
        default=0,
        blank=True,
        help_text="Logbook recorded the project progress including detailed procedures, results, and original data.",
    )
    data_analysis = models.IntegerField(
        default=0,
        blank=True,
        help_text="Observations were clearly summarized in tables/graphs and were consistent with data collected.",
    )
    results = models.IntegerField(
        default=0,
        blank=True,
        help_text="Results were logically explained and understood.",
    )
    conclusion_quality = models.IntegerField(
        default=0,
        blank=True,
        help_text="Conclusions and summary remarks were based on experimental data and related to the problem/hypothesis.",
    )
    sources_of_error = models.IntegerField(
        default=0,
        blank=True,
        help_text="Possible sources of error were recognized.",
    )


def create_score_form(project: Project):
    form_type = 'E' if project.grade <= 6 else 'S'
    score_forms = {
        "E": {
            "experiment": EExperimentScore,
            "innovation": ENonExperimentScore,
            "research": ENonExperimentScore,
        },
        "S": {
            "experiment": SExperimentScore,
            "innovation": SInnovationScore,
            "research": SStudyScore,
        },
    }
    result = score_forms[form_type][project.project_type].objects.create(id=project.id, project=project)
    project.score_form = result
    project.save()
    return result


def create_feedback_form(project: Project):
    form_type = 'E' if project.grade <= 6 else 'S'
    feedback_forms = {
        "E": {
            "experiment": EEFeedback,
            "innovation": ENEFeedback,
            "research": ENEFeedback,
        },
        "S": {
            "experiment": SEFeedback,
            "innovation": SIFeedback,
            "research": SRFeedback,
        },
    }

    result = feedback_forms[form_type][project.project_type].objects.create(id=project.id)
    project.feedback_form = result
    project.save()
    return result
