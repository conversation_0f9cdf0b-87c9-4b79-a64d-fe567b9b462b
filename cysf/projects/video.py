from embed_video.backends import VideoBackend
import re


class GoogleDriveBackend(VideoBackend):
    re_detect = re.compile(r"https://drive\.google\.com/file/.*/view.*")
    re_code = re.compile(r"https://drive\.google\.com/file/(?P<code>.*)/view.*")

    allow_https = True
    pattern_url = "https://drive.google.com/file/{code}/preview"


class OneDriveBackend(VideoBackend):
    re_detect = re.compile(r"https://onedrive.*/embed.*")
    re_code = re.compile(r"(?P<code>.*)")

    allow_https = True
    pattern_url = "{code}"


# <iframe src="https://onedrive.live.com/embed?cid=3D6FCB29217609A1&resid=3D6FCB29217609A1%215502&authkey=ANQ0JNdnM-aypsY" width="320" height="240" frameborder="0" scrolling="no" allowfullscreen></iframe>
# <iframe src="https://onedrive.live.com/embed?cid=3D6FCB29217609A1&resid=3D6FCB29217609A1%215504&authkey=APjRrCuB-EwXyZw" width="320" height="240" frameborder="0" scrolling="no" allowfullscreen></iframe>