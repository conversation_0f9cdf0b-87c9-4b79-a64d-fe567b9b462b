# Generated by Django 3.1 on 2021-01-02 19:06

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('safety', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='ethicsandduecare2a',
            name='submitted',
            field=models.BooleanField(blank=True, default=False, verbose_name='Submit for review to Ethics and Due Care (CYSF)?'),
        ),
        migrations.AddField(
            model_name='ethicsandduecare2b',
            name='submitted',
            field=models.BooleanField(blank=True, default=False, verbose_name='Submit for review to Ethics and Due Care (CYSF)?'),
        ),
        migrations.AlterField(
            model_name='ethicsandduecare2a',
            name='approved',
            field=models.CharField(choices=[('approved', 'Approved'), ('in_progress', 'In Progress')], default='in_progress', max_length=20),
        ),
        migrations.AlterField(
            model_name='ethicsandduecare2b',
            name='approved',
            field=models.<PERSON>r<PERSON><PERSON>(choices=[('approved', 'Approved'), ('in_progress', 'In Progress')], default='in_progress', max_length=20),
        ),
    ]
