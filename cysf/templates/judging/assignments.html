{% extends "users/user_base.html" %} {% load static %} {% block main %}

  <!--Section: Content-->
  <section>
    {#    <div class="form-inline">#}
    {#      <input type="text" id="new_project" class="form-control pull-left" placeholder="Project # or Location">#}
    {#      <button class="searchAddress btn btn-default pull-right" onclick="onAssignment()">Add Assignment</button>#}
    {#    </div>#}
    {#    <script>#}
    {#      function onAssignment() {#}
    {#        var project_num = document.getElementById("new_project").value#}
    {#        location.href = `?new_assignment=${project_num}`#}
    {#        console.log(project_num)#}
    {#      }#}
    {#    </script>#}
    <div class="row px-1">
      <!-- Section: Live preview -->
      {% for project in project_set %}
        <section class="col-md-4 mb-4">
          <!-- Card -->
          <div class="card card-cascade position-relative">
            <!-- Card image -->
            <div class="view view-cascade overlay">
              <a>
                <div class="mask rgba-white-slight waves-effect waves-light"></div>
              </a>
            </div>
            <div class="card-body card-body-cascade text-center">
              <!-- Title -->
              <h4 class="card-title m-0">
                <strong>{{ project.name }}</strong>
              </h4>

              <div class="row">
                <div class="col col-6 justify-content-start text-left">
                  <span>LOCATION: {{ project.location }}</span>
                </div>
                <div class="col-6 text-right justify-content-end">
                  {% if project.score_form.complete %}
                    <span class="badge green">SCORING COMPLETE ({{ project.score_form.total }})</span>
                  {% else %}
                    <span class="badge red">SCORING INCOMPLETE</span>
                  {% endif %} {% if project.feedback_form.complete %}
                  <span class="badge green">COMMENT COMPLETE</span>
                {% else %}
                  <span class="badge red">FEEDBACK INCOMPLETE</span>
                {% endif %}
                </div>
              </div>
              <a class="btn btn-secondary btn-sm"
                 href="{% url 'judging:score' pk=project.id form='score' %}">Score</a>
              <a class="btn btn-secondary btn-sm"
                 href="{% url 'judging:score' pk=project.id form='feedback' %}">Comment</a>
{#              <a class="btn btn-secondary btn-sm"#}
{#                 href="{% url 'projects:detail' pk=project.id %}">Project</a>#}
              <a class="btn btn-secondary btn-sm"
                 href="{{ project.get_public_url }}">Project</a>

              {#              <a class="btn btn-secondary btn-sm" href="{% url 'judging:score' pk=assignment.id form='feedback' %}">Feedback#}
              {#                Form</a>#}
            </div>
            <!-- Card content -->
          </div>
          <!-- Card -->
        </section>
      {% endfor %}
      <!-- Section: Live preview -->
    </div>
  </section>
  <!--Section: Content-->

{% endblock main %}
