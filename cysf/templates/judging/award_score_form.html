{% extends "users/user_base.html" %}

{% load i18n %}
{% load account socialaccount %}
{% load crispy_forms_tags %}

{% block head_title %}{% endblock %}

{% block main %}
  <style>
    .fixed-sn .page-footer .container-fluid,
    .fixed-sn main {
      margin-left: 0;
      margin-right: 0;
      padding-top: 1em;
    }

    .gutter {
      background-color: grey;
      cursor: col-resize;
    }

    .center-card {
      margin: 0 auto;
    }
  </style>

  <div class="row justify-content-center">
    <div class="card card-cascade narrower z-depth-0 dark-card-admin text-white col-xl-8 ">
      <div class="view view-cascade gradient-card-header secondary-color p-2">
        <div class="row mx-2">
          <i class="fas fa-star text-white mr-3" style="font-size:2em;"></i>
          <h2 class="card-header-title"><strong>Award Round Sheet</strong></h2>
        </div>
      </div>
      <!-- Content -->
      {#      {% include 'judging/score_form.htmx.html' with form=form %}#}
      <table class="table table-hover mb-0 w-100 text-white">
        <thead>
        <tr>
          <th>Award</th>
          <th>Winner</th>
        </tr>
        </thead>
        <tbody>
        {% for award in award_set %}
          <tr>
            <td><b>{{ award.award }}</b><br/>{{ award.award.criteria_summary }}</td>
            <td><input/></td>
          </tr>
        {% endfor %}

        </tbody>
      </table>
      <!-- Content -->
    </div>
  </div>
{% endblock main %}
