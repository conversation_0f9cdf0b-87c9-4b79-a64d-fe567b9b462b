{% extends "users/user_base.html" %}
{% load static crispy_forms_tags sekizai_tags %}

{% block main %}
<div class="modal fade top" id="filterModal" tabindex="-1" role="dialog" aria-labelledby="filterModalLabel"
  data-backdrop="false" aria-modal="true" style="display: hidden; padding-right: 15px;">
  <div class="modal-dialog modal-full-height modal-top modal-notify modal-info" role="document">
    <!-- Content -->
    <div class="modal-content">
      <!-- Header -->

      <!-- Body -->
      <div class="modal-body" style="background-color: #353b50 !important; color: #fff !important;">
        <div class="d-flex align-items-center py-5 px-4 dark-card-admin text-white">
          <div class="w-100">
            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
              <span aria-hidden="true" class="white-text">×</span>
            </button>
            <style>
              form {
                display: flex !important;
                flex-flow: row wrap;
                justify-content: space-evenly;
                width: 20px;
              }

              .form-group {
                width: 40%;
              }

              .control-group {
                width: 100%;
              }
            </style>
            <form class="form-horizontal w-100" method="get" action="">
              {{ filter.form | crispy}}

              <div class="control-group">
                <div class="controls">
                  <button type="submit" class="btn btn-primary">Filter</button>
                  <a href="{% url 'projects:browse' %}" class="btn btn-primary">Show All</a>
                </div>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
<!--Section: Content-->
<section>
  <div class="row pr-lg-5">
    <div class="position-fixed" style="right: 20px; top:60px; z-index: 1000;">
      <button type="button" data-toggle="modal" href="#filterModal" aria-controls="filter_collapse" id="filter-toggle"
        class="btn btn-secondary btn-sm">Filter</button>
      <a href="{% url 'projects:browse' %}" class="btn btn-primary btn-sm">Show All</a>
    </div>

    {% comment %}
    <div class="white-text my-5 wow fadeInLeft animated pl-lg-5" id="store_message"
      style="visibility: visible; animation-name: zoomInDown; animation-delay: 0.3s; display:none;"
      data-wow-delay="0.3s">
      <h1 class="h1-responsive font-weight-bold">Get Your CYSF Merchandise!</h1>
      <hr class="hr-light ">
      <h6>We have stylish hats,
        t-shirts, hoodies and drawstring bags available to purchase with free shipping directly to your door in
        Alberta. It's great stuff and it helps support the science fair.</h6>
      <br>
      <div class="row justify-content-between">
        <a class="btn btn-secondary btn-rounded waves-effect waves-light animated" target="_blank"
          href="https://cysf.square.site/">Check it out</a>
        <fieldset class="form-check">
          <input class="form-check-input" type="checkbox" id="dont_show_store">
          <label class="form-check-label" for="dont_show_store">Don't Show Again (on this device)</label>
        </fieldset>
      </div>
    </div>
    {% endcomment %}
    <!-- Section: Live preview -->
    {% for project in projects.distinct %}
    {% for i in '1' %}
    <section class="col-md-4 mb-4">
      <div class="card card-cascade" id="project_card_{{project.id}}_{{i}}">
        <!-- Card image -->
        <div class="view view-cascade overlay">
          {% if project.presentation.project_image %}
          <img src="{{ project.presentation.project_image.full.url }}" class="card-img-top" alt="">
          {% endif %}
        </div>
        <div class="card-body card-body-cascade text-center">
          <!-- Title -->
          <div class="row justify-content-end w-100 font-small font-weight-bold" style="margin-top:-20px;">
            <div class='col-auto'>Grade: {{project.grade}}</div>
          </div>
          <h4 class=" card-title"><strong>{{ project.name }}</strong></h4>

          <p class="card-text">{{ project.description }}</p>
          <a class="btn btn-secondary btn-sm" href="{% url 'projects:public-detail' slug=project.slug %}">View
            Project <i class="fas fa-chevron-right"></i></a>
        </div>
        <!-- Card content -->
      </div>
      <!-- Card -->
    </section>
    {% endfor %}
    {% endfor %}
    {% if not projects %}
    <div class='w-100 text-white text-center'>
      <h2>No Projects Found.</h2>
      <h4>Perhaps you want to adjust your filter?</h4>
    </div>
    {% endif %}

    <!-- Section: Live preview -->
  </div>
</section>
<!--Section: Content-->

{% addtoblock 'js' %}
<script>
  $(document).ready(function () {
    // onload decide baseed on localStorage
    var dont_show = localStorage.getItem('dont_show_store');
    console.log('DontShow', dont_show)
    if (!dont_show) {
      $('#store_message').show();
      console.log('SHOWING')
    }
    // on click set localStorage
    var dontShowId = document.getElementById('dont_show_store');
    dontShowId.addEventListener('click', function () {
      if (dont_show == null)
        localStorage.setItem('dont_show_store', true);
    });
    console.log('HERE!');
  });
</script>
{% endaddtoblock %}
{% endblock %}