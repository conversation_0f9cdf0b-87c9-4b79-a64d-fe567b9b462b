{% load static cysf_tags %}
<div id="slide-out" class="side-nav fixed">
  <ul class="custom-scrollbar ps ">
    <li class="logo-sn waves-effect py-3">
      <div class="text-center">
        <a href="#" class="pl-0"><img src="{% static 'images/logo_header.png' %}"></a>
      </div>
      <div class="text-center py-3">
        <a href="#" class="pl-0">
          <img src="{{ user.socialaccount_set.all.0.get_avatar_url }}" class="rounded-circle" alt="" loading="lazy"/>
        </a>
      </div>
    </li>
    <!-- Side navigation Judge links -->
    {% if user.judge_team %}
      <li>
        <ul class="collapsible">
          <li class="mdb-color darken-2">
            <div class="text-center">
              <strong>JUDGE TEAM {{ user.judge_team.id }}</strong>
            </div>
          </li>
          <li class="mdb-color darken-2">
            <div class="text-center">
              <strong>PROJECTS</strong>
            </div>
          </li>
          {% for project in user.judge_team.project_set.all %}
            <li>
              <a class="collapsible-header waves-effect arrow-r"
                 href="{% url 'judging:score' pk=project.id form='score' %}">
                <i class="fas fa-clock text-success"></i>
                {{ project.name }}
              </a>
            </li>
          {% endfor %}
        </ul>
      </li>
    {% endif %}
    <!-- Side navigation Coordiatorlinks -->
    <!-- Side navigation Coordiatorlinks -->
    {% if user.coordinator %}
      <li>
        <ul class="collapsible">
          <li class="mdb-color darken-2">
            <div class="text-center">
              <strong>COORDINATOR</strong>
            </div>
          </li>
          {% if current_fair.project_edit_open %}
            <li>
              <a class="collapsible-header waves-effect arrow-r" href="{% url 'users:coordinator:update' %}">
                <i class="fas fa-school"></i>School
              </a>
            </li>
            <li>
              <a class="collapsible-header waves-effect" href="{% url 'users:coordinator:invite-student' %}"><i
                class="fas fa-envelope-open-text"></i>Invitations</a>
            </li>
            <li>
              <a class="collapsible-header waves-effect" href="{% url 'users:coordinator:projects' %}"><i
                class="fas fa-envelope-open-text"></i>Projects</a>
            </li>
          {% else %}
            <li>
              <a class="collapsible-header waves-effect" href="{% url 'users:coordinator:projects' %}"><i
                class="fas fa-envelope-open-text"></i>Projects</a>
            </li>
            <li class="bg-danger text-center">
              EDITING CLOSED
            </li>
          {% endif %}
        </ul>
      </li>
    {% endif %}
    <!-- Side navigation Coordiatorlinks -->
    {% if user.student %}
      <li>
        <ul class="collapsible">
          <li class="mdb-color darken-2">
            <div class="row">
              <div class="col-6"><a href="{{ user.student.get_project.get_public_url }}">VIEW</a>
              </div>
              {% if current_fair.project_feedback_open %}
                <div class="col-6"><a href="{% url 'projects:feedback' pk=user.student.get_project.pk %}">COMMENTS</a>
                </div>
              {% endif %}
            </div>
          </li>
          <li>
            {% if user.student.get_project.send_to_cysf == True %}
              <div class="row">
                <div class="text-white text-center"
                     style="font-size: 0.7rem; padding-left: 2rem; margin-bottom: 0.3rem; margin-top: 0.3rem;">This
                  project has been selected for judging.
                </div>
              </div>
            {% endif %}
          </li>
          {% for requirement in user.student.get_project.nav_requirements %}
            {% if current_fair.project_edit_open or requirement.0 == "basic_project_info" %}
              <li class="mx-2 ">
                <a class=" collapsible-header waves-effect arrow-r"
                   href="{% url 'projects:update' requirement=requirement.0 %}">
                  {% if requirement.1.completed %} <i class="fas fa-check-circle text-success"></i>
                  {% else %}
                    <i class="fas fa-times-circle text-danger"></i>
                  {% endif %}
                  {{ requirement.name }}
                  {{ requirement.0|snake_to_label }}
                </a>
              </li>
            {% endif %}
          {% endfor %}
          {% if not current_fair.project_edit_open %}
            <li class="bg-danger text-center">
              EDITING CLOSED
            </li>
          {% endif %}
        </ul>
      </li>
    {% endif %}
    <!-- Side navigation links -->

    <div class="ps__scrollbar-x-rail" style="left: 0px; bottom: 0px;">
      <div class="ps__scrollbar-x" tabindex="0" style="left: 0px; width: 0px;"></div>
    </div>
    <div class="ps__scrollbar-y-rail" style="top: 0px; right: 0px;">
      <div class="ps__scrollbar-y" tabindex="0" style="top: 0px; height: 0px;"></div>
    </div>
  </ul>
  <div class="sidenav-bg mask-strong"></div>
</div>
