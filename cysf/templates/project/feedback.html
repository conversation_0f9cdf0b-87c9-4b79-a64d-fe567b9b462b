{% extends "users/user_base.html" %}

{% load i18n %}
{% load crispy_forms_tags cysf_tags static embed_video_tags %}

{% block head_title %}{% trans "Sign In" %}{% endblock %}

{% block main %}
    <div class="card card-cascade narrower z-depth-0 dark-card-admin text-white col-12 mt-0">
        <div class="view view-cascade gradient-card-header secondary-color p-2" style="z-index: 1000;">
            <div class="row mx-2 justify-content-between align-items-center">
                <h2 class="col card-header-title text-left"><i class="fas fa-comment text-white mr-3"></i>
                    <strong>Feedback</strong>
                </h2>
                <a class="col-auto btn btn-blue float-right"
                   href="{% url "projects:certificate" pk=project.id %}"><strong>
                    VIEW CERTIFICATE</strong></a>
            </div>
        </div>
        <!-- Card content -->
        <div class="card-body card-body-cascade text-left dark-card-admin text-white position-relative">
            {% if project.feedback.comment %}
                <h3>
                    COMMENTS FROM JUDGES
                </h3>
                <div class="ml-3 clearfix">
                    <div class="row align-items-center">
                        <!-- Grid column -->
                        <div class="col-auto mb-4">
                            <i class="fas fa-comment fa-3x" aria-hidden="true"></i>
                        </div>
                        <div class="col mb-4">
                            <!-- Excerpt -->
                            <p>{{ project.feedback.comment|linebreaks }}</p>
                        </div>
                        <!-- Grid column -->
                    </div>
                </div>
            {% else %}
                <h3>NO COMMENTS LOGGED</h3>
            {% endif %}
            {% if project.feedback.categories %}
                <h3>
                    FEEDBACK BY CATEGORY
                </h3>
                <p class="small">From <strong>Needs Improvement</strong> to <strong>Excellence</strong></p>
                {% for category in project.feedback.categories %}
                    <div class="ml-3">
                        <div class="row align-items-center pl-2" style="margin-top: 20px;">
                            <h4>{{ category }}</h4>
                            {% for field in project.feedback.categories|get_item:category %}
                                <div class='col-12 row align-items-center mt-1 ml-1'>
                                    <div class='col-12'>
                                        <h6>{{ field|snake_to_label }}</h6>
                                    </div>
                                    <div class='col-12 row align-items-center'>
                                        <div class="col">
                                            <div class="progress">
                                                <div class="progress-bar secondary-color" role="progressbar"
                                                     style="width: {{ project.feedback.scores|get_item:field }}%"></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    </div>
                {% endfor %}
            {% endif %}
        </div>
    </div>
{% endblock %}
