{% extends "users/user_base.html" %}
{% load static crispy_forms_tags sekizai_tags %}

{% block main %}
  <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/2.0.3/css/dataTables.bootstrap4.css"/>
  <style>
    label, .dt-info {
      color: white;
    }
  </style>
  <!--Section: Content-->
  <table id="projects" class="display" style="width:100%">
    <thead>
    <tr>
      <th>Location</th>
      <th>Names</th>
      <th>Title</th>
      <th>School</th>
      <th>#</th>
    </tr>
    </thead>
    <tbody>
    {% for project in projects %}
      <tr>
        <td>{{ project.location }}</td>
        <td>{{ project.student1_name }}{% if project.student2_name %}, {{ project.student2_name }}{% endif %}</td>
        <td>{{ project.name }}</td>
        <td>{{ project.school_name }}</td>
        <td>{{ project.id }}</td>
      </tr>
    {% endfor %}
    </tbody>
  </table>

  {% addtoblock 'js' %}
    <script type='text/javascript'
            src='//cdn.datatables.net/2.0.3/js/dataTables.min.js'></script>
    <script type='text/javascript'
            src='//cdn.datatables.net/2.0.3/js/dataTables.bootstrap4.js'></script>
    <script type="text/javascript">
      $('#projects').DataTable({"paging": false});
      $('#projects').find('label').each(function () {
        $(this).parent().append($(this).children());
      });
      $('#projects .dataTables_filter').find('input').each(function () {
        const $this = $(this);
        $this.attr("placeholder", "Search");
        $this.removeClass('form-control-sm');
      });
      $('#projects .dataTables_length').addClass('d-flex flex-row');
      $('#projects .dataTables_filter').addClass('md-form');
      $('#projects select').removeClass('custom-select custom-select-sm form-control form-control-sm');
      $('#projects select').addClass('mdb-select');
      $('#projects .mdb-select').materialSelect();
      $('#projects .dataTables_filter').find('label').remove();
      {#let table = new DataTable('#projects');#}
    </script>
  {% endaddtoblock %}
{% endblock %}
