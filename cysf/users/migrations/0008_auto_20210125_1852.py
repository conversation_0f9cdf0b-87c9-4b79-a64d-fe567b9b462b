# Generated by Django 3.1 on 2021-01-26 01:52

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('users', '0007_user_judge'),
    ]

    operations = [
        migrations.AlterField(
            model_name='judge',
            name='chemphys_comfort',
            field=models.IntegerField(default=0, help_text='How comfortable you would be judging secondary projects related to Chemical and Physical Sciences (0 = Not Comfortable, 5 = Completely Comfortable)?', verbose_name='Chemical and Physical Sciences Comfort'),
        ),
        migrations.AlterField(
            model_name='judge',
            name='comments',
            field=models.TextField(blank=True, help_text='For immediate response <NAME_EMAIL>', null=True, verbose_name='Other Comments'),
        ),
        migrations.AlterField(
            model_name='judge',
            name='conflict',
            field=models.CharField(help_text="Do you have a close relationship with any known 2021 CYSF participant that may create a conflict of interest in our judging process? If so, please list the participant's name(s).", max_length=255, null=True, verbose_name='Conflict of Interest'),
        ),
        migrations.AlterField(
            model_name='judge',
            name='consumer_comfort',
            field=models.IntegerField(default=0, help_text='How comfortable you would be judging secondary projects related to Consumer Goods (0 = Not Comfortable, 5 = Completely Comfortable)?', verbose_name='Consumer Goods Comfort'),
        ),
        migrations.AlterField(
            model_name='judge',
            name='judging_experience',
            field=models.IntegerField(default=0, help_text='How many times have you previously judged at the CYSF?', verbose_name='Juding Experience'),
        ),
        migrations.AlterField(
            model_name='judge',
            name='lifesciences_comfort',
            field=models.IntegerField(default=0, help_text='How comfortable you would be judging secondary projects related to Life and Health Sciences (0 = Not Comfortable, 5 = Completely Comfortable)?', verbose_name='Life and Health Sciences Comfort'),
        ),
        migrations.AlterField(
            model_name='judge',
            name='mathcompsci_comfort',
            field=models.IntegerField(default=0, help_text='How comfortable you would be judging secondary projects related to Mathematical and Computing Sciences (0 = Not Comfortable, 5 = Completely Comfortable)?', verbose_name='Mathematical and Computing Sciences Comfort'),
        ),
        migrations.AlterField(
            model_name='judge',
            name='prefered_level',
            field=models.CharField(choices=[('elementary', 'Elementary (Grades 5-6)'), ('secondary', 'Secondary (Grades 7-12)')], default='elementary', help_text='At what grade level would you like to judge?', max_length=15, verbose_name='Prefered Level'),
        ),
    ]
