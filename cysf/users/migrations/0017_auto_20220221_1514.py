# Generated by Django 3.1 on 2022-02-21 22:14

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('users', '0016_auto_20211120_1359'),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name='judge',
            name='awards',
            field=models.BooleanField(blank=True, default=False, help_text='Are you willing to judge in the award round in addition to the medal round?  Judges with at least four years of previous experience judging at the CYSF may apply to judge in the award round. This will be conducted virtually between Saturday 26 March and Thursday 7 April, and videoconference/audioconference interaction with the student will be required. Note that only some judges are selected for award round judging; we will contact you separately to inform you as to whether on whether you have been selected for award round judging.', verbose_name='Judge Awards Round?'),
        ),
        migrations.Alter<PERSON>ield(
            model_name='judge',
            name='conflict',
            field=models.Char<PERSON><PERSON>(blank=True, help_text="Do you have a close relationship with any known 2022 CYSF participant that may create a conflict of interest in our judging process? If so, please list the participant's name(s).", max_length=255, null=True, verbose_name='Conflict of Interest'),
        ),
    ]
