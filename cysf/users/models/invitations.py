from django.contrib.auth.models import AbstractUser
from django.db import models
from django.urls import reverse
from django.utils.translation import gettext_lazy as _
from model_utils.models import TimeStampedModel
from phonenumber_field.modelfields import Phone<PERSON><PERSON>berField

from cysf.projects.models import Project
from cysf.users.views import coordinator
from cysf.utils.email import send_template_email

from .people import Coordinator, Student, User


class StudentInvitation(TimeStampedModel):
    coordinator = models.ForeignKey(
        Coordinator, on_delete=models.CASCADE, related_name="invitations"
    )
    student = models.OneToOneField(
        Student,
        on_delete=models.CASCADE,
        related_name="invitation",
        null=True,
        blank=True,
    )
    student_email = models.EmailField(
        verbose_name="Email of student to Invite or Accept"
    )


from django.db.models.signals import post_delete, post_save, pre_save
from django.dispatch import receiver


@receiver(post_delete, sender=StudentInvitation)
def delete_invite(sender, instance, **kwargs):
    try:
        student = instance.student
        if student:
            student.coordinator = None
            student.save()

    except:
        pass


@receiver(post_save, sender=StudentInvitation)
def check_invite(sender, instance, created, **kwargs):
    if created:
        students = User.objects.filter(email__iexact=instance.student_email.lower())
        if students.count():
            student = students[0].make_student()
            instance.student = student
            instance.save()
            student.coordinator = instance.coordinator
            student.save()
        else:
            send_template_email(
                subject="Invitation to join CYSF",
                template_name="invitation",
                from_user=instance.coordinator.user,
                to=instance.student_email.lower(),
            )


@receiver(post_save, sender=Student)
def check_student(sender, instance, created, **kwargs):
    if created:
        invitations = StudentInvitation.objects.filter(
            student__isnull=True, student_email__iexact=instance.user.email.lower()
        )
        if invitations.count():
            invitation = invitations[0]
            invitation.student = instance
            invitation.save()
            instance.coordinator = invitation.coordinator
            instance.save()
