import asyncio
import signal
import urllib.parse
from django.views.generic import DetailView
from django.template.loader import render_to_string
from django.http import HttpResponse
from pyppeteer import launch

class PDFDetailView(DetailView):
    """
    A DetailView that renders the template as a PDF using pyppeteer.
    """

    pdf_options = {
        'format': 'Letter',
        'printBackground': True,
        'margin': {
            'top': '.5in',
            'right': '0in',
            'bottom': '.5in',
            'left': '0in',
        },
    }

    def get_pdf_filename(self):
        return f"{self.model.__name__.lower()}-{self.object.pk}.pdf"

    def get_pdf_options(self):
        return self.pdf_options.copy()

    def get_pdf_content_type(self):
        return 'application/pdf'

    def get_content_disposition(self):
        return f'attachment; filename="{self.get_pdf_filename()}"'

    def modify_pdf_context(self, context):
        return context

    async def generate_pdf(self, html, pdf_options):
        # Temporarily override signal.signal to bypass thread-limitation errors
        original_signal = signal.signal
        try:
            signal.signal = lambda *args, **kwargs: None
            browser = await launch(args=['--no-sandbox'], handleSIGINT=False)
            page = await browser.newPage()

            # Convert HTML into a data URL and navigate there
            data_url = "data:text/html," + urllib.parse.quote(html)
            # Use waitUntil here instead of setContent
            await page.goto(data_url, waitUntil='networkidle0')

            # Generate PDF
            pdf = await page.pdf(pdf_options)
            await browser.close()
            return pdf
        finally:
            signal.signal = original_signal

    def run_async(self, coro):
        loop = asyncio.new_event_loop()
        try:
            asyncio.set_event_loop(loop)
            return loop.run_until_complete(coro)
        finally:
            loop.close()

    def render_to_response(self, context, **response_kwargs):
        try:
            # Modify context if needed
            context = self.modify_pdf_context(context)

            # Render template to HTML
            html = render_to_string(self.template_name, context=context, request=self.request)

            pdf_options = self.get_pdf_options()

            # Generate PDF in our own event loop
            pdf = self.run_async(self.generate_pdf(html, pdf_options))

            # Create an HTTP response with the PDF
            response = HttpResponse(pdf, content_type=self.get_pdf_content_type())
            response['Content-Disposition'] = self.get_content_disposition()
            response['Cache-Control'] = 'no-cache, no-store, must-revalidate'
            response['Pragma'] = 'no-cache'
            response['Expires'] = '0'
            return response

        except Exception as e:
            raise RuntimeError(f"PDF generation failed: {str(e)}")
