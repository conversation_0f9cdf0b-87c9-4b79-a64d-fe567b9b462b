import os
from os.path import join
import re

records = {}

for root, dirs, filenames in os.walk('./EDI214'):
    for filename in list(sorted(filenames))[:1]:
        with open(join(root, filename)) as f:
            for element in map(lambda line: line.strip().split('*'),
                               filter(lambda line: line[:3] in ['MS1', 'MS2', 'GS*'], f.readlines())):
                if element[0] == 'GS':
                    event_ts = element[4] + element[5]
                if element[0] == 'MS1':
                    lon = -float(element[4][:3] + '.' + element[4][3:])
                    lat = float(element[5][:3] + '.' + element[5][3:])
                if element[0] == 'MS2':
                    car = element[1] + ' ' + element[2]
            records.append({'car': car, 'event_ts': event_ts, 'lat': lat, 'lon': lon})

print(records)
