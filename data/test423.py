import os
from os.path import join
import re
import json

records = []

for root, dirs, filenames in os.walk("./EDI423"):
    for filename in list(sorted(filenames))[:1]:
        with open(join(root, filename)) as f:
            for idx, element in enumerate(
                filter(
                    lambda line: line[0] == "N7",
                    map(lambda line: line.strip().split("*"), f.readlines()),
                )
            ):
                # print(element)
                if element[0] == "N7":
                    records.append(
                        {
                            "initial": element[1],
                            "number": f"{element[2]}",
                            "loaded": f"{element[11].strip()}",
                            "position": idx + 1,
                        }
                    )
                    if element[5].strip():
                        records[-1].update(
                            {
                                "weightTare": int(element[5].strip()),
                                "weightMax": int(element[3].strip()),
                            }
                        )


print(json.dumps({"track": "1", "cars": records}))
