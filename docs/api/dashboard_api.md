# rail shipment duration

Description: will search customer active trips and will return the past shipment duration for those (origin, destination)
TEST

URL: api/dashboard/trip_metrics/

Method: Get

**Request Params**
|Param|Type  |Description  |
|--|--|--|
|limit  |Integer  | Limit the number of response objects |
|location  | String | only return results for the given location both in origin and destination  |

**Response**

|Key|Type  |description  |
|--|--|--|
| origin | string |  |
| destination | string |  |
| avg30 | float | average shipment duration of last 30 days trips |
| count30 | int | number of trips in the last 30 days |
| avg90 | float | |
| count90 | int | |
| avg180 | float | |
| metrics | list | list of plot data  [["count", value]["mean", value]["std", value]["min", value]["max", value]["25%", value]["50%", value]["75%", value]]|




example: /api/dashboard/trip_metrics/?location=MCFARLAND&limit=2

sample response:

`[
    {
        "origin": "SOUTH BEAMER (CN)",
        "destination": "MCFARLAND (WSOR)",
        "avg30": 10.3,
        "count30": 7,
        "avg90": 10.0,
        "count90": 9,
        "avg180": 10.0,
        "count180": 9,
        "metrics": [
            [
                "count",
                65.0
            ],
            [
                "mean",
                13.4
            ],
            [
                "std",
                5.419294234492163
            ],
            [
                "min",
                8.0
            ],
            [
                "25%",
                9.0
            ],
            [
                "50%",
                11.0
            ],
            [
                "75%",
                17.0
            ],
            [
                "max",
                28.0
            ]
        ]
    },
    {
        "origin": "MCFARLAND (WSOR)",
        "destination": "SOUTH BEAMER (CN)",
        "avg30": 9.5,
        "count30": 11,
        "avg90": 10.6,
        "count90": 21,
        "avg180": 11.2,
        "count180": 37,
        "metrics": [
            [
                "count",
                150.0
            ],
            [
                "mean",
                10.973333333333333
            ],
            [
                "std",
                3.5783590155052742
            ],
            [
                "min",
                1.0
            ],
            [
                "25%",
                9.0
            ],
            [
                "50%",
                11.0
            ],
            [
                "75%",
                13.0
            ],
            [
                "max",
                22.0
            ]
        ]
    }
]`
