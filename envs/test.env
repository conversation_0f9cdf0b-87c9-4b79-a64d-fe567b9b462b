# General
# ------------------------------------------------------------------------------
# DJANGO_READ_DOT_ENV_FILE=True
DJANGO_SETTINGS_MODULE=config.settings.test
DJANGO_SECRET_KEY=qdS9VDLX6qoy9jNHtLpvkYlAIJJ7je5e4kJo1UMjfpPCo0CfZ0kg6qoKZYVuxtwQ
DJANGO_ADMIN_URL=admin/
DJANGO_ALLOWED_HOSTS=.*
DJANGO_DEBUG=True
USE_DOCKER=True

# PostgreSQL
# ------------------------------------------------------------------------------
POSTGRES_HOST=postgres
POSTGRES_DB=conformii
POSTGRES_USER=debug
POSTGRES_PASSWORD=debug

DATABASE_URL=postgres://debug:debug@localhost:5432/conformii

# # Security
# # ------------------------------------------------------------------------------
# # TIP: better off using DNS, however, redirect is OK too
DJANGO_SECURE_SSL_REDIRECT=False

# # Email
# # ------------------------------------------------------------------------------
DJANGO_SERVER_EMAIL=

MAILGUN_API_KEY=
MAILGUN_DOMAIN=


# # Azure
# # ------------------------------------------------------------------------------
AZURE_ACCOUNT_KEY=""
AZURE_ACCOUNT_NAME=""
AZURE_CONTAINER=""
AZURE_STORAGE_CONNECTION_STRING=""

# Microsoft Clarity
INCLUDE_MICROSOFT_CLARITY=False

# # django-allauth
# # ------------------------------------------------------------------------------
DJANGO_ACCOUNT_ALLOW_REGISTRATION=True

# # Gunicorn
# # ------------------------------------------------------------------------------
WEB_CONCURRENCY=4

# # Sentry
# # ------------------------------------------------------------------------------
SENTRY_DSN=


# # Redis
# # ------------------------------------------------------------------------------
REDIS_URL=redis://redis:6379/0
