version: '3'

volumes:
  vantedge_postgres_data: {}
  vantedge_postgres_data_backups: {}

services:
  django:
    container_name: vantedge_api
    build:
      context: .
      dockerfile: ./compose/local/django/Dockerfile
    image: vantedge_local_django
    depends_on:
      - postgis
      - mailpit
    volumes:
      - .:/app
      - ../data:/data
    env_file:
      - ./envs/.local/.django
      - ./envs/.local/.postgres
    ports:
      - "8000:8000"
    command: /start

  postgis:
    container_name: vantedge_db
    build:
      context: .
      dockerfile: ./compose/production/postgres/Dockerfile
    image: vantedge_production_postgres
    volumes:
      - vantedge_postgres_data:/var/lib/postgresql/data
      - vantedge_postgres_data_backups:/backups
    env_file:
      - ./envs/.local/.postgres
    ports:
      - "5432:5432"

  mailpit:
    container_name: mailpit
    image: axllent/mailpit
    ports:
      - "8025:8025"
      - "1025:1025"

  nginx:
    container_name: webserver
    image: nginx:alpine
    depends_on:
      - postgis
      - mailpit
      - django
    ports:
      - 80:80
    volumes:
      - ./vantedge/static:/static
      - ./dist:/dist
      - ./nginx/dev/sites-enabled/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/errormessages:/usr/share/nginx/html/errormessages
