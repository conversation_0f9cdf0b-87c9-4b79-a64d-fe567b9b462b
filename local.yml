version: '3'

services:
  traefik:
    image: traefik:v2.3
    container_name: traefik
    command:
      - --entrypoints.web.address=:80
      - --entrypoints.websecure.address=:443
      - --api.dashboard
      - --api
      - --providers.docker
      - --certificatesresolvers.cysfresolver.acme.tlschallenge=true
      - --certificatesresolvers.cysfresolver.acme.email=<EMAIL>
      - --certificatesresolvers.cysfresolver.acme.storage=/certificates/acme.json
    labels:
      - traefik.http.routers.redirs.rule=hostregexp(`{host:.+}`)
      - traefik.http.routers.redirs.entrypoints=web
      - traefik.http.routers.redirs.middlewares=https-redirect
      - traefik.http.middlewares.https-redirect.redirectscheme.scheme=https
    ports:
      - 80:80
      - 443:443
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - ~/data/cysf/ssl_certs:/certificates

  mjml:
    image: liminspace/mjml-tcpserver:latest
    container_name: mjml
    restart: always
    expose:
      - 28101

  django:
    build:
      context: .
      dockerfile: ./compose/production/django/Dockerfile
    image: cysf_django
    container_name: cysf_django
    depends_on:
      - postgres
    env_file:
      - ./envs/.production/.django
      - ./envs/.production/.postgres
    volumes:
      - ~/data/cysf/media:/app/cysf/media
      - ~/data/cysf/notebooks:/app/cysf/notebooks
    command: /start
    expose:
      - 5000
    labels:
      - traefik.enable=true
      - traefik.http.routers.django.rule=Host(`platform.cysf.org`)||Host(`platform.cysf.ca`)
      - traefik.http.routers.django.entrypoints=websecure,web
      - traefik.http.routers.django.tls=true
      - traefik.http.routers.django.tls.certresolver=cysfresolver

  notebook:
    build:
      context: .
      dockerfile: ./compose/production/django/Dockerfile
    image: cysf_notebook
    container_name: cysf_notebook
    depends_on:
      - postgres
    env_file:
      - ./envs/.production/.django
      - ./envs/.production/.postgres
    volumes:
      - ~/data/cysf/media:/app/cysf/media
      - ~/data/cysf/notebooks:/app/cysf/notebooks
    command: /start-notebook
    ports:
      - 8888:8888
    labels:
      - traefik.enable=true
      - traefik.http.routers.notebook.rule=Host(`analytics.platform.cysf.org`)
      - traefik.http.routers.notebook.entrypoints=websecure,web
      - traefik.http.routers.notebook.tls=true
      - traefik.http.routers.notebook.tls.certresolver=cysfresolver

  postgres:
    build:
      context: .
      dockerfile: ./compose/production/postgres/Dockerfile
    image: cysf_db
    container_name: cysf_postgres
    volumes:
      - ~/data/cysf/db:/var/lib/postgresql/data
      - ~/data/cysf/db_bak:/backups
    env_file:
      - ./envs/.production/.postgres
    ports:
      - 5432:5432

  nginx:
    container_name: cysf_nginx
    image: nginx:alpine
    expose:
      - 80
    volumes:
      - ~/data/cysf/media:/media
      - ./compose/production/nginx/nginx.conf:/etc/nginx/nginx.conf
    #      - ./nginx/errormessages:/usr/share/nginx/html/errormessages
    labels:
      - traefik.enable=true
      - traefik.http.routers.nginx.rule=(Host(`platform.cysf.org`)||Host(`platform.cysf.ca`))&&PathPrefix(`/media`)
      - traefik.http.routers.nginx.entrypoints=websecure,web
      - traefik.http.routers.nginx.tls=true
      - traefik.http.routers.nginx.tls.certresolver=cysfresolver

  whoami:
    image: containous/whoami
    container_name: simple-service
    labels:
      - traefik.enable=true
      - traefik.whoami.priority=10
      - traefik.http.routers.whoami.rule=(Host(`who.cysf.org`)||Host(`who.cysf.ca`))
      - traefik.http.routers.whoami.entrypoints=websecure
      - traefik.http.routers.whoami.tls=true
      - traefik.http.routers.whoami.tls.certresolver=cysfresolver
      - traefik.http.middlewares.auth.basicauth.users=user:$$apr1$$q8eZFHjF$$Fvmkk//V6Btlaf2i/ju5n/ # user/password
