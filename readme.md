# Windows Developer Setup Guide for CYSF Platform

This guide will help you set up the CYSF Platform development environment on Windows using WSL2 (Windows Subsystem for
Linux).

## Prerequisites

### Setting Up WSL2 with Ubuntu 23.10

1. Open PowerShell as Administrator and run:
   ```powershell
   wsl --install
   ```

2. Once WSL2 is installed, install Ubuntu 23.10 specifically:
   ```powershell
   wsl --install -d Ubuntu-23.10
   ```

3. Restart your computer if prompted.

4. Open Windows Terminal to complete Ubuntu setup with a username and password.

5. Update Ubuntu within WSL2:
   ```bash
   sudo apt update && sudo apt upgrade -y
   ```

   Note: Ubuntu 23.10 comes with Python 3.12 pre-installed, making it ideal for development.

### Installing Docker Desktop

1. Download [Docker Desktop for Windows](https://www.docker.com/products/docker-desktop)
2. During installation:
    - Ensure "WSL 2" option is selected
    - Complete the installation and restart if prompted
3. Configure Docker Desktop:
    - Open Docker Desktop
    - Go to Settings → Resources → WSL Integration
    - Enable integration with your Ubuntu WSL distro
    - Click "Apply & Restart"

### Installing Python and Development Tools

1. Ensure your WSL2 Ubuntu installation is up to date:
   ```bash
   sudo apt update && sudo apt upgrade -y
   ```

### Installing Development Tools

1. Open WSL2 terminal and install required packages:
   ```bash
   sudo apt install python3.12 python3.12-venv python3-pip git
   ```

## Project Setup

### Getting the Code

1. Clone the repository:
   ```bash
   cd ~/projects  # or your preferred directory
   # Replace [username] with your Google Cloud username

git clone ssh://[username]@<EMAIL>:2022/p/cysf-website-293623/r/project_portal

# For example:

# git clone ssh://<EMAIL>@source.developers.google.com:2022/p/cysf-website-293623/r/project_portal

cd project_portal

   ```

### Python Environment Setup

1. Create and activate virtual environment:
   ```bash
   python3.12 -m venv venv
   source venv/bin/activate
   ```

2. Update pip and install dependencies:
   ```bash
   pip install --upgrade pip
   pip install -r requirements.txt
   ```

### Environment Configuration

1. Create environment file:
   ```bash
   cp .env.example .env
   ```

2. Edit the `.env` file with your settings:
   ```bash
   nano .env
   ```

   Required settings include:
    - DATABASE_URL="postgres://debug:debug@localhost:5432/cysf"
    - Secret key
    - Debug mode
    - Allowed hosts

## Running the Application

### Starting Services

1. Ensure Docker Desktop is running

2. Start Docker services:
   ```bash
   docker-compose up -d
   ```

### Database Setup

1. Apply migrations:
   ```bash
   python manage.py migrate
   ```

2. Create admin user:
   ```bash
   python manage.py createsuperuser
   ```

### Running Development Server

1. Start Django server:
   ```bash
   python manage.py runserver 0.0.0.0:8000
   ```

2. Access the application:
    - Admin interface: http://localhost:8000/admin
    - Main site: http://localhost:8000

## Troubleshooting

### Common Issues

1. **WSL2 Not Working**
   ```powershell
   # In PowerShell (Admin):
   wsl --set-default-version 2
   wsl --update
   ```

2. **Docker Issues**
    - Verify Docker Desktop is running
    - Check WSL integration in Docker settings
    - Try restarting WSL:
      ```powershell
      wsl --shutdown
      ```

3. **Port Conflicts**
   ```bash
   # Find process using port 8000
   sudo lsof -i :8000
   
   # Kill the process if needed
   sudo kill -9 <PID>
   ```

## Development Tips

### VSCode Integration

1. Install VSCode extensions:
    - "Remote - WSL"
    - "Python"
    - "Docker"

2. Open project in VSCode:
    - Use command palette (Ctrl+Shift+P)
    - Select "WSL: Open Folder in WSL"
    - Navigate to your project directory

3. Configure Python interpreter:
    - Command palette → "Python: Select Interpreter"
    - Choose the virtual environment created earlier

### Daily Development

1. Starting development session:
   ```bash
   # Start WSL2 terminal
   cd ~/projects/cysf-platform  # your project directory
   source venv/bin/activate
   docker-compose up -d
   python manage.py runserver 0.0.0.0:8000
   ```

2. Ending development session:
   ```bash
   # Stop Django server with Ctrl+C
   docker-compose down
   deactivate  # exit virtual environment
   ```

### Database Access

- Default connection details:
    - Full URL: postgres://debug:debug@localhost:5432/cysf
    - Host: localhost
    - Port: 5432
    - Database: cysf
    - Username: debug
    - Password: debug

Note: These credentials are for local development only. Never use these credentials in production!

- Recommended tools:
    - DBeaver
    - pgAdmin

## Best Practices

1. Always work within WSL2:
    - Use WSL2 terminal or VSCode with WSL extension
    - Avoid editing WSL files from Windows applications

2. Keep dependencies updated:
   ```bash
   pip install --upgrade -r requirements.txt
   ```

3. Before committing code:
    - Run tests: `python manage.py test`
    - Check for migrations: `python manage.py makemigrations --check`

## Need Help?

- Check the project's README.md for specific project guidelines
- Consult the Django documentation: https://docs.djangoproject.com/
- Contact the development team through appropriate channels
