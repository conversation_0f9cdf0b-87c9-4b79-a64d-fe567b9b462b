GET https://pcmrail.alk.com/REST/v24.1/Service.svc/geocode/Station?format=StationState&name=FORT+STEELE+BC&AuthToken=E9B7071E26122B4C8D276ABFEA7C77B7


POST https://cnebusiness.geomapguide.ca/Handlers/search.ashx
authority: cnebusiness.geomapguide.ca
accept: application/json, text/javascript, */*; q=0.01
x-requested-with: XMLHttpRequest
user-agent: Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.61 Safari/537.36
origin: https://cnebusiness.geomapguide.ca
sec-fetch-site: same-origin
sec-fetch-mode: cors
sec-fetch-dest: empty
referer: https://cnebusiness.geomapguide.ca/
accept-language: en-GB,en-US;q=0.9,en;q=0.8
cookie: _fbp=fb.1.1587660706204.*********; ASP.NET_SessionId=fck4ct3juptlg05lqsnst5wz
Content-Type: application/x-www-form-urlencoded; charset=UTF-8

CMD=search&fsac=41975

###

GET http://localhost:8000/api/user/me/
Accept: application/json
Authorization: Token 01fdd517e2ff350ad4a93d820976dc48545bfc79

###

POST http://localhost:8000/auth/login/?password=1234
Accept: application/json

###

GET http://localhost:8000/api/company/219/contracts/

Accept: application/json

###

GET http://localhost:8000/api/company/247/facilities
Accept: application/json

###

GET http://localhost:8000/api/picker/test
Accept: application/json
Authorization: Token d1edf8422c6d0071a59fb940ba4e80480f4f93fa

###
GET http://localhost:8000/api/railshipment/
Accept: application/json
Authorization: Token d1edf8422c6d0071a59fb940ba4e80480f4f93fa

###
GET http://localhost:8000/api/facility/51
Accept: application/json
Authorization: Token d1edf8422c6d0071a59fb940ba4e80480f4f93fa

###

POST http://localhost:8000/api/auth/login/
Accept: application/json

{"email":"<EMAIL>", "password":"1"}

###

GET http://localhost:8000/api/carevent/?trip=16701901
Accept: application/json
Authorization: Token 01fdd517e2ff350ad4a93d820976dc48545bfc79
####


GET http://localhost:8000/api/carevent/?car=TILX308908
Accept: application/json
Authorization: Token 01fdd517e2ff350ad4a93d820976dc48545bfc79

###
GET http://localhost:8000/api/carevent/autocomplete/?q=123
Accept: application/json
Authorization: Token 01fdd517e2ff350ad4a93d820976dc48545bfc79

###
GET http://localhost:8000/api/carevent/409291/
Accept: application/json
Authorization: Token 01fdd517e2ff350ad4a93d820976dc48545bfc79


###
GET http://localhost:8000/api/trips/?is_active=true
Accept: application/json
Authorization: Token 01fdd517e2ff350ad4a93d820976dc48545bfc79

###
GET https://wheelhouse.vantedgelgx.com/api/yarddata/?search=SHANTZ
Accept: application/json
authorization: Token a4eab80f3209c7fa59c68a726b97d2a0356da551
