pytz==2023.3  # https://github.com/stub42/pytz
python-slugify==8.0.1  # https://github.com/un33k/python-slugify
Pillow  # https://github.com/python-pillow/Pillow
argon2-cffi  # https://github.com/hynek/argon2_cffi
whitenoise  # https://github.com/evansd/whitenoise
uvicorn[standard]  # https://github.com/encode/uvicorn
wsproto==0.15.0  # https://github.com/python-hyper/wsproto/

# Django
# ------------------------------------------------------------------------------
django==4.2.5  # pyup: < 3.1  # https://www.djangoproject.com/
django-environ==0.11.2  # https://github.com/joke2k/django-environ
django-model-utils==4.3.1  # https://github.com/jazzband/django-model-utils
django-allauth==0.57.0  # https://github.com/pennersr/django-allauth
django-crispy-forms==1.9.2  # https://github.com/django-crispy-forms/django-crispy-forms
django-object-actions
django-richtextfield
django-tabbed-admin
django-admin-interface==0.26.1
django-admin-tools
django-phonenumber-field[phonenumberslite]
django-q
django-sekizai
django-invitations
django-select2
django-htmx
selectolax
django-pandas
# django-mjml
# django-mjml[requests]

django-stdimage
django-embed-video
django-import-export
django-ckeditor==6.7.2
django_starfield
django-loginas

pandas
matplotlib
django-extra-views
django_filter
django-extensions  # https://github.com/django-extensions/django-extensions
notebook>=7.0.0
pyzmq --pre
traitlets==5.9.0

nbimporter
progressbar2
django-json-widget
icecream
django_q
mjml

django-constance
pydash

sentry-sdk[django]
openpyxl

pyppeteer

openai
langchain_community
langchain
django-markdown-deux
tablib[xls]
