pytz
python-slugify  # https://github.com/un33k/python-slugify
# Pillow==6.1.0  # https://github.com/python-pillow/Pillow
# argon2-cffi==19.1.0  # https://github.com/hynek/argon2_cffi
redis  # https://github.com/antirez/redis

#Scrapy
Scrapy
Twisted
xmltodict
billiard

# Django
# ------------------------------------------------------------------------------
django==4.2  # https://www.djangoproject.com/
django-environ  # https://github.com/joke2k/django-environ
django-model-utils # https://github.com/jazzband/django-model-utils
django-allauth==0.58.2 # https://github.com/pennersr/django-allauth
django-crispy-forms # https://github.com/django-crispy-forms/django-crispy-forms
django-redis  # https://github.com/niwinz/django-redis
uvicorn  # https://github.com/encode/uvicorn
django-storages[azure] # https://github.com/jschneier/django-storages
django-timezone-field

# Models
django-phonenumber-field[phonenumberslite]
django-treebeard
django-computedfields
django-tree-queries # https://github.com/matthiask/django-tree-queries
django-pint     # https://github.com/CarliJoy/django-pint
django-richtextfield

# # Django REST Framework
djangorestframework==3.14.0  # https://github.com/encode/django-rest-framework
django-filter
djangorestframework-camel-case
dj-rest-auth==5.0.2
djangorestframework-gis
django-auto-prefetching
django-cors-headers
django-compression-middleware
django-rest-polymorphic==0.1.10 # https://github.com/apirobot/django-rest-polymorphic

# # Django Ninja
django-ninja

# # Django Admin
django-object-actions
django-admin-row-actions
django-tabbed-admin
django-admin-interface==0.26.1
django-admin-tools
django-admin-autocomplete-filter
django-import-export
django-admin-rangefilter==0.11.2

# #Other Django
django-weasyprint==2.2.0
weasyprint==57.1
django-sass-processor
django-compressor
libsass
pysftp
argon2-cffi
argon2
django-q
django-dirtyfields
drf-nested-routers
drf_orjson_renderer
dataclasses-json
django-polymorphic
pydash
pydantic==1.10.9
django-safedelete
django-mjml
django-clone # https://pypi.org/project/django-clone/#usage

# #Data
rest-pandas
pandas==2.0.3
numpy==1.26.4
pdpipe
nltk
scikit-learn
deepdiff
django-pandas
matplotlib

# #PDF & Image
PyMuPDF
pdf2image
pytesseract
opencv-python
pdfkit
pdftotext
pdfplumber
pypdf
pydyf==0.6.0

# #Python
mimesis
more-itertools
python-dateutil
Pint
Pint-Pandas==0.4
lxml
geopy
arrow
ftpretty
openpyxl
xlsxwriter==3.1.2
faker
reverse_geocoder
boto3
timezonefinder
dictdiffer
inquirer
stringcase
zeep

# # OCR
pdf2image
pytesseract
opencv-python

# Bardcode
zxing-cpp==2.2.0

# #Widgets
django_jsonform
django-json-widget
more-itertools
progressbar2
beautifulsoup4
attrs

# # vant-packages
git+https://jcabia:<EMAIL>/VantedgeLGX/TBOSS/_git/django-schema-field@v0.2.6
git+https://jcabia:<EMAIL>/VantedgeLGX/TBOSS/_git/vant-db-replication@v2.2.4.1
git+https://jcabia:<EMAIL>/VantedgeLGX/TBOSS/_git/vant-email@v1.0.8
git+https://jcabia:<EMAIL>/VantedgeLGX/TBOSS/_git/vant-ledger@v1.3.0
git+https://jcabia:<EMAIL>/VantedgeLGX/TBOSS/_git/vant-utils@v1.6.1
git+https://jcabia:<EMAIL>/VantedgeLGX/TBOSS/_git/vant-calcs@v1.5.0
git+https://jcabia:<EMAIL>/VantedgeLGX/TBOSS/_git/vant-user@v1.6.0
git+https://jcabia:<EMAIL>/VantedgeLGX/TBOSS/_git/vant-tboss@v2.05.05
git+https://jcabia:<EMAIL>/VantedgeLGX/TBOSS/_git/vant-limit@v1.6.0
git+https://jcabia:<EMAIL>/VantedgeLGX/TBOSS/_git/vant-ticket@v1.7.11

git+https://jcabia:<EMAIL>/VantedgeLGX/Wheelhouse/_git/azure-email@v1.2.2
# #constance
django-constance[database]
django-constance

# # fcm_django
twilio

pyopenssl
cryptography
pytest

sentry-sdk # https://pypi.org/project/sentry-sdk/
jinja2

# AI Modules
openai

# Testing
freezegun