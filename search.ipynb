#%%
import os

os.environ["DJANGO_ALLOW_ASYNC_UNSAFE"] = "true"
from safedelete.models import HARD_DELETE
import json, arrow
from schema_field.dotdict import DotDict
from apps.search.postgres.query import EnhancedSearchQuery
from apps.search.utils import prepare_search_text

#%%
prepare_search_text('Canadian Standards Association (CSA) Z246.1:21 Security management for petroleum and natural gas industry systems')

#%%
d = Document.objects.filter(name__icontains="z246").first()
print(d.name)
d.search_data.id

#%%
SearchData.objects.all().delete()
l = DocumentLayout.objects.get(id=219036)
l.fragment.save()
l.fragment.search_data.search_vector
l.fragment.content_plain_text

#%%
tenant = Tenant.objects.get(name= 'VantEdge')
#%%
search_input = 'q31001'
r = SearchData.objects.filter(search_vector=EnhancedSearchQuery(
                        search_input,
                        search_type="conformii",
                        config="english",
                    )).first()
r.documentfragment_searchable

#%%
search_input = 'ao-001'
r = Document.objects.filter(search_data__search_vector=EnhancedSearchQuery(
                        search_input,
                        search_type="conformii",
                        config="english",
                    )).count()
r
#%%
