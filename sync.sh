# python manage.py migrate dbreplication zero
# python manage.py migrate vant_dbreplication zero
# python manage.py migrate tboss zero
# find . | grep -E "(__pycache__|\.pyc|\.pyo$)" | xargs rm -rf
# git ls-files --others --exclude-standard | grep -E ".*/00.*.py" | xargs rm -rf
# find ~/miniconda3/envs/wh-replication/lib/python3.9/site-packages/vant_contract/migrations | grep -E ".*/00.*.py" | xargs rm -rf
# find ~/miniconda3/envs/wh-replication/lib/python3.9/site-packages/vant_device/migrations | grep -E ".*/00.*.py" | xargs rm -rf
# find ~/miniconda3/envs/wh-replication/lib/python3.9/site-packages/vant_tboss/migrations | grep -E ".*/00.*.py" | xargs rm -rf
# find ~/miniconda3/envs/wh-replication/lib/python3.9/site-packages/vant_ticket/migrations | grep -E ".*/00.*.py" | xargs rm -rf
# find ~/miniconda3/envs/wh-replication/lib/python3.9/site-packages/vant_dbreplication/migrations | grep -E ".*/00.*.py" | xargs rm -rf
# find ~/miniconda3/envs/wh-replication/lib/python3.9/site-packages/vant_hmi/migrations | grep -E ".*/00.*.py" | xargs rm -rf
# find ~/miniconda3/envs/wh-replication/lib/python3.9/site-packages/vant_dbreplication/migrations | grep -E ".*/00.*.py" | xargs rm -rf
# pip install -r requirements/local.txt
python manage.py makemigrations
python manage.py migrate
python manage.py tboss_replication --client_company_name "BTG Energy Corp"
