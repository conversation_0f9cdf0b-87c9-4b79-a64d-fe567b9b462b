from azure.communication.email import EmailClient


def send_acs_email(to_email, subject, body):
    # Initialize the ACS SCIMEmailSchema Client
    email_client = EmailClient.from_connection_string(
        "endpoint=https://wheelhouse-cs.canada.communication.azure.com/;accesskey=iOFOV58EASGCI95qlON4WgDumtyuXO9JmRh4T2mDKzo5RyyFthUSJQQJ99AJACULyCpIeSn0AAAAAZCSKBeO")

    # Set up email message details
    email_message = {
        "senderAddress": "<EMAIL>",  # The sender email must be verified in ACS
        "content"      : {
            "subject"  : subject,
            "plainText": body,
        },
        "recipients"   : {
            "to": [{"address": to_email}],
        },
    }

    try:
        # Send the email
        response = email_client.begin_send(email_message)
        print(f"SCIMEmailSchema successfully sent. Message ID: {response['messageId']}")
        return response["messageId"]
    except Exception as ex:
        print(f"Error sending email: {ex!s}")
        return None


send_acs_email("<EMAIL>", "Test email using ACS", "Hello from Azure Communication Services!")
