from rest_framework import routers
from vantedge.reports.views import ReportViewSet
from vantedge.trackandtrace.views import (
    CarEventViewSet,
    TripViewSet,
    RailCarViewSet,
    WaybillViewSet,
    RailRateViewSet,
    RailstationViewSet,
    StccViewSet,
)
from vantedge.users.views.roles import GroupViewSet
from vantedge.yard_management.views import (
    YardDataViewSet,
    RailCarInspectionPlanViewset,
    RailCarInspectionFormViewset,
    RailCarInspectionListViewset,
    RailYardViewSet,
    RailYardCacheView,
)

from vantedge.gaugetables.views import GaugeTableViewset

from vantedge.tboss.views import (
    CodeGeneratorViewset,
    TBossV3TicketViewset,
    TBossV3TicketEntryViewset,
    TBossV3LocationViewset,
    TBossV3ProductViewset,
    TBossV3LocationProductViewset,
    TBossV3VehicleViewset,
    TBossV3VehicleCertificationViewset,
    TBossV3UserViewset,
    TBossV3UserCertificationViewset,
    TBossV3UserAccessViewset,
    TBossV3StaffViewset,
    TBossV3DriverViewset,
    TBossV3CertificationViewset,
    TBossV3PeriodicLimitViewset,
    TBossV3LimitPeriodViewset,
    TBossV3LimitPropertiesViewSet,
    TBossV3TicketPropertiesViewSet,
    TBossV3GCAnalysisViewset,
    TBossV3GroupViewSet,
    TBossV3PermissionsViewSet,
    TBossV3CompanyRoleViewSet,
    TBossV3CompanyViewset,
    TBossV3CompanyCertificationViewset,
    TBossV3ServerViewSet,
    TBossV3ServiceContractViewset,
    TBossV3ServiceContractTicketViewset,
    TBossV3TicketInvoiceViewset,
    TBossV3WorkerViewSet,
    TBossV3LogEntryViewset,
    TBossV3RouteViewset,
    PDFTemplateDetailView,
    TBossV3BarcodeViewset,
    TBossV3UnattachedFilesViewSet,
    TBossV3ServerEntityAssociationViewset,
    TBossV3CrossBorderContractViewSet,
)

from vantedge.terminalboss.views import SyncDataViewSet
from vantedge.terminalboss.wheelhouse.views import (
    SiteUserViewset,
    TicketViewViewset,
    PersonTableViewSet,
    ProductTableViewSet,
    MeasurementUnitViewSet,
    CompanyTableViewset,
    CompanyTypeViewset,
    AllocationTableViewset,
    TransferLimitViewset,
    TerminalBossCompanyViewset,
    OverviewViewset,
    PersonCompanyViewset,
    CompanyTypeTableViewset,
    CompanyHierarchyViewset,
    LocationTableViewSet,
)

from vantedge.wheelhouse.views.company import CompanyViewSet, WheelhouseCompanyViewSet
from vantedge.wheelhouse.views.location import (
    LocationViewSet,
    FacilityViewSet,
    RiserViewSet,
    RiserScheduleCompanyConfigViewSet,
)
from vantedge.wheelhouse.views.product import ProductViewSet
from vantedge.wheelhouse.views.contract import (
    ContractViewSet,
    PartyViewSet,
    RouteViewSet,
    RailShippingScheduleViewSet,
)
from vantedge.wheelhouse.views.picker import PickerViewSet
from vantedge.wheelhouse.views.rail import (
    RailShipmentViewSet,
    BOLView,
    BOLPrintView,
    RailShipmentNoteViewSet,
)
from vantedge.wheelhouse.views.railcar import LoadedRailCarViewSet
from vantedge.wheelhouse.views.integration import RailShipmentIntegrationViewSet

from vantedge.wheelhouse.views.bol import BolNumberViewSet
from vantedge.wheelhouse.views.certificate import (
    CertificateTypeViewSet,
    CertificateViewSet,
)

from vantedge.wheelhouse.views.additional_fields import AdditionalFieldsViewSet

from vantedge.users.views import (
    UserViewSet,
    MicrosoftLogin,
    MicrosoftConnect,
    GoogleLogin,
    GoogleConnect,
    PermissionsViewSet,
    ApplicationConfigurationViewSet,
    UserDeviceView,
    UserPopupViewSet,
    UserNoteViewSet,
)

from vantedge.dashboards.views import DashboardRailViewSet
from vantedge.autocomplete.api import AutocompleteViewSet
from vantedge.terminalboss.wheelhouse.views.allocation.viewset import (
    ObjectSyncTableViewset,
)

from vantedge.attachment.views import (
    AttachmentViewSet,
    BillOfLadingAttachmentViewSet,
    BillOfLadingViewSet,
)

from vantedge.invitation.views import (
    InvitationViewSet,
    PartnerRailContractViewSet,
    PartnerOrderViewSet,
    PartnerNominationViewSet,
    PartnerRailShipmentViewSet,
    PartnerTicketViewViewset,
    PartnerV3TicketViewset,
    PartnerPickerViewSet,
)
from vantedge.easyaudit.views import RequestEventViewSet, CRUDEventViewSet

from vantedge.invoice.views import (
    InvoiceViewSet,
    InvoiceItemViewSet,
    InvoicePickerViewSet,
    PricingViewSet,
    InvoiceProductViewSet,
    ProductConditionViewSet,
)

from vantedge.customs.views import CustomsInvoiceViewSet

from vantedge.schedule.views import (
    SlotViewSet,
    BookingViewSet,
    ScheduleLocationViewSet,
    ScheduleCompanyViewSet,
    SlotTemplateViewSet,
)

from vantedge.notification.views import NoteViewSet
from vantedge.forms.views import FormViewSet, FormDataViewSet

from django.urls import path
from django.conf import settings


if settings.DEBUG:
    router = routers.DefaultRouter()
else:
    router = routers.SimpleRouter()

# Attachments
router.register(
    r"attachments",
    AttachmentViewSet,
    basename="attachments",
)

# General
router.register(r"user", UserViewSet)
router.register(r"company/application_configuration", ApplicationConfigurationViewSet)
router.register(r"user_popup", UserPopupViewSet)
router.register(r"user_note", UserNoteViewSet)
router.register(r"user_device", UserDeviceView)
router.register(r"report", ReportViewSet)
router.register(r"railshipment", RailShipmentViewSet)
router.register(r"railshipmentnote", RailShipmentNoteViewSet)
router.register(r"loadedrailcar", LoadedRailCarViewSet)
router.register(r"company/wheelhouse", WheelhouseCompanyViewSet)
router.register(r"company", CompanyViewSet)
router.register(r"location", LocationViewSet)
router.register(r"facility", FacilityViewSet)
router.register(r"riser", RiserViewSet)
router.register(r"riser_schedule_company_config", RiserScheduleCompanyConfigViewSet)
router.register(r"product", ProductViewSet)
router.register(r"bolnumber", BolNumberViewSet)
router.register(r"certificatetype", CertificateTypeViewSet)
router.register(r"certificate", CertificateViewSet)
router.register(
    r"additional_fields", AdditionalFieldsViewSet, basename="additional_fields"
)
# Integration
router.register(
    r"integration/v1/rail/shipment",
    RailShipmentIntegrationViewSet,
    basename="railshipment_integration",
)

# Contracts
router.register(r"contract", ContractViewSet)
router.register(r"party", PartyViewSet)
router.register(r"route", RouteViewSet)
router.register(r"railschedule", RailShippingScheduleViewSet)
router.register(r"carevent", CarEventViewSet, basename="carevent")
router.register(r"trip", TripViewSet, basename="trip")
router.register(r"railcar", RailCarViewSet)
router.register(r"waybill", WaybillViewSet, basename="waybill")
router.register(r"railrate", RailRateViewSet, basename="railrate")
router.register(r"railstation", RailstationViewSet, basename="railstation")
router.register(r"stcc", StccViewSet, basename="stcc")
router.register(r"yarddata", YardDataViewSet)
router.register(r"picker", PickerViewSet, basename="picker")


# Documents
router.register(r"billoflading", BillOfLadingViewSet, basename="billoflading")
router.register(
    r"billofladingattachments",
    BillOfLadingAttachmentViewSet,
    basename="billofladingattachment",
)

# schedule
router.register(r"schedule/slot", SlotViewSet)
router.register(r"schedule/booking", BookingViewSet)
router.register(
    r"schedule/company", ScheduleCompanyViewSet, basename="schedule_company"
)
router.register(
    r"schedule/location", ScheduleLocationViewSet, basename="schedule_location"
)
router.register(
    r"schedule/slottemplate", SlotTemplateViewSet, basename="schedule_slottemplate"
)


# notifications
router.register(r"notification", NoteViewSet)

# forms
router.register(r"form", FormViewSet)
router.register(r"form_data", FormDataViewSet)

# yard management
router.register(
    r"yard_management/railcar_inspection_plan", RailCarInspectionPlanViewset
)
router.register(
    r"yard_management/railcar_inspection_form", RailCarInspectionFormViewset
)
router.register(
    r"yard_management/railcar_inspection_list", RailCarInspectionListViewset
)
router.register(r"yard_management/rail_yard", RailYardViewSet)


# Terminal Boss V3
router.register(
    r"terminalboss/v3/serverassociations/entity/(?P<app_label>[^/.]+)/(?P<model_name>[^/.]+)",
    TBossV3ServerEntityAssociationViewset,
    basename="terminalboss-v3-server-associations",
)

router.register(
    r"terminalboss/v3/ticket", TBossV3TicketViewset, basename="terminalboss-v3-ticket"
)
router.register(
    r"terminalboss/v3/ticketentry",
    TBossV3TicketEntryViewset,
    basename="terminalboss-v3-ticketentry",
)
router.register(
    r"terminalboss/v3/ticketproperties",
    TBossV3TicketPropertiesViewSet,
    basename="terminalboss-v3-ticket-properties",
)
router.register(
    r"terminalboss/v3/location",
    TBossV3LocationViewset,
    basename="terminalboss-v3-location",
)
router.register(
    r"terminalboss/v3/product",
    TBossV3ProductViewset,
    basename="terminalboss-v3-product",
)
router.register(
    r"terminalboss/v3/location-product",
    TBossV3LocationProductViewset,
    basename="terminalboss-v3-location-product",
)
router.register(
    r"terminalboss/v3/vehicle",
    TBossV3VehicleViewset,
    basename="terminalboss-v3-vehicle",
)
router.register(
    r"terminalboss/v3/user", TBossV3UserViewset, basename="terminalboss-v3-user"
)
router.register(
    r"terminalboss/v3/staff", TBossV3StaffViewset, basename="terminalboss-v3-staff"
)
router.register(
    r"terminalboss/v3/driver", TBossV3DriverViewset, basename="terminalboss-v3-driver"
)
router.register(
    r"terminalboss/v3/useraccess",
    TBossV3UserAccessViewset,
    basename="terminalboss-v3-useraccess",
)
router.register(
    r"terminalboss/v3/periodic-limit",
    TBossV3PeriodicLimitViewset,
    basename="terminalboss-v3-periodic-limit",
)
router.register(
    r"terminalboss/v3/limit-period",
    TBossV3LimitPeriodViewset,
    basename="terminalboss-v3-limit-period",
)
router.register(
    r"terminalboss/v3/limit-properties",
    TBossV3LimitPropertiesViewSet,
    basename="terminalboss-v3-limit-properties",
)
router.register(
    r"terminalboss/v3/cross-border-contract",
    TBossV3CrossBorderContractViewSet,
    basename="terminalboss-v3-cross-border-contract",
)
router.register(
    r"terminalboss/v3/gcanalysis",
    TBossV3GCAnalysisViewset,
    basename="terminalboss-v3-gcanalysis",
)
router.register(
    r"terminalboss/v3/certification",
    TBossV3CertificationViewset,
    basename="terminalboss-v3-certification",
)
router.register(
    r"terminalboss/v3/usercertification",
    TBossV3UserCertificationViewset,
    basename="terminalboss-v3-usercertification",
)
router.register(
    r"terminalboss/v3/companycertification",
    TBossV3CompanyCertificationViewset,
    basename="terminalboss-v3-companycertification",
)
router.register(
    r"terminalboss/v3/vehiclecertification",
    TBossV3VehicleCertificationViewset,
    basename="terminalboss-v3-vehiclecertification",
)
router.register(
    r"terminalboss/v3/group", TBossV3GroupViewSet, basename="terminalboss-v3-group"
)
router.register(
    r"terminalboss/v3/permission",
    TBossV3PermissionsViewSet,
    basename="terminalboss-v3-permission",
)
router.register(
    r"terminalboss/v3/company",
    TBossV3CompanyViewset,
    basename="terminalboss-v3-company",
)
router.register(
    r"terminalboss/v3/companyrole",
    TBossV3CompanyRoleViewSet,
    basename="terminalboss-v3-company-role",
)
router.register(
    r"terminalboss/v3/route",
    TBossV3RouteViewset,
    basename="terminalboss-v3-route",
)
router.register(
    r"terminalboss/v3/servers", TBossV3ServerViewSet, basename="terminalboss-v3-servers"
)
router.register(
    r"terminalboss/v3/worker", TBossV3WorkerViewSet, basename="terminalboss-v3-worker"
)
router.register(
    r"terminalboss/v3/logentry",
    TBossV3LogEntryViewset,
    basename="terminalboss-v3-logentries",
)

router.register(
    r"terminalboss/v3/service-contract",
    TBossV3ServiceContractViewset,
    basename="terminalboss-v3-service-contract",
)

router.register(
    r"terminalboss/v3/code-generators",
    CodeGeneratorViewset,
    basename="terminalboss-v3-code-generators",
)

router.register(
    r"terminalboss/v3/service-contract-ticket",
    TBossV3ServiceContractTicketViewset,
    basename="terminalboss-v3-service-contract-ticket",
)

router.register(
    r"terminalboss/v3/ticket-invoice",
    TBossV3TicketInvoiceViewset,
    basename="terminalboss-v3-ticket-invoice",
)

router.register(
    r"terminalboss/v3/barcode",
    TBossV3BarcodeViewset,
    basename="terminalboss-v3-barcode",
)

router.register(
    r"terminalboss/v3/unattached-files",
    TBossV3UnattachedFilesViewSet,
    basename="terminalboss-v3-unattached-files",
)

# Terminal Boss
router.register(r"terminalboss/site", SiteUserViewset, basename="terminalboss-site")
router.register(r"terminalboss/sync", SyncDataViewSet, basename="terminalboss-sync")
router.register(
    r"terminalboss/company", TerminalBossCompanyViewset, basename="terminalboss-company"
)
router.register(
    r"terminalboss/ticketview", TicketViewViewset, basename="terminalboss-ticketview"
)
router.register(
    r"terminalboss/person", PersonTableViewSet, basename="terminalboss-person"
)
router.register(
    r"terminalboss/product", ProductTableViewSet, basename="terminalboss-product"
)
router.register(
    r"terminalboss/measurementunit",
    MeasurementUnitViewSet,
    basename="terminalboss-measurement-unit",
)
router.register(
    r"terminalboss/companytable",
    CompanyTableViewset,
    basename="terminalboss-company-table",
)
router.register(
    r"terminalboss/companytype",
    CompanyTypeViewset,
    basename="terminalboss-company-type",
)
router.register(
    r"terminalboss/allocation",
    AllocationTableViewset,
    basename="terminalboss-allocation-table",
)
router.register(
    r"terminalboss/transferlimit",
    TransferLimitViewset,
    basename="terminalboss-transferlimit",
)
router.register(
    r"terminalboss/location",
    LocationTableViewSet,
    basename="terminalboss-location-table",
)
router.register(
    r"terminalboss/synctable",
    ObjectSyncTableViewset,
    basename="terminalboss-sync-table",
)

# relations
router.register(
    r"terminalboss/personcompany",
    PersonCompanyViewset,
    basename="terminalboss-personcompany",
)
router.register(
    r"terminalboss/companytypetable",
    CompanyTypeTableViewset,
    basename="terminalboss-companytypetable",
)
router.register(
    r"terminalboss/companyhierarchy",
    CompanyHierarchyViewset,
    basename="terminalboss-companyhierarchy",
)

router.register(
    r"terminalboss/overview", OverviewViewset, basename="terminalboss-overview"
)

# dashboard
router.register(r"dashboard/rail", DashboardRailViewSet, basename="dashboard")

# partner
router.register(
    r"partner/invitations", InvitationViewSet, basename="partner-invitation"
)
router.register(
    r"partner/railcontract", PartnerRailContractViewSet, basename="partner-contract"
)
router.register(r"partner/railorder", PartnerOrderViewSet, basename="partner-order")
router.register(
    r"partner/railnomination", PartnerNominationViewSet, basename="partner-nomination"
)
router.register(
    r"partner/railshipment", PartnerRailShipmentViewSet, basename="partner-railshipment"
)
router.register(
    r"partner/terminalboss-v2-ticket",
    PartnerTicketViewViewset,
    basename="partner-terminalbossv2ticket",
)
router.register(
    r"partner/terminalboss-v3-ticket",
    PartnerV3TicketViewset,
    basename="partner-terminalbossv3ticket",
)
router.register(r"partner/picker", PartnerPickerViewSet, basename="partner-picker")


# invoice
router.register(r"invoice/invoice", InvoiceViewSet, basename="invoice")
router.register(r"invoice/picker", InvoicePickerViewSet, basename="invoice-picker")
router.register(r"invoice/item", InvoiceItemViewSet, basename="invoice-item")
router.register(r"invoice/pricing", PricingViewSet, basename="invoice-pricing")
router.register(r"invoice/product", InvoiceProductViewSet, basename="invoice-product")
router.register(
    r"invoice/product_condition",
    ProductConditionViewSet,
    basename="invoice-product-condition",
)

# customs
router.register(r"customs", CustomsInvoiceViewSet, basename="customs")

# autocomplete
router.register(r"autocomplete", AutocompleteViewSet, basename="autocomplete")

# groups and permissions
router.register(r"permissions", PermissionsViewSet, basename="permissions")
router.register(r"groups", GroupViewSet, basename="groups")

# audit (easyaudit)
router.register(r"audit/crud", CRUDEventViewSet, basename="crud-audit")
router.register(r"audit/request", RequestEventViewSet, basename="request-audit")

social_urls = [
    path("microsoft/connect/", MicrosoftConnect.as_view(), name="microsoft-connect"),
    path("microsoft/", MicrosoftLogin.as_view(), name="microsoft"),
    path("google/connect/", GoogleConnect.as_view(), name="google-connect"),
    path("google/", GoogleLogin.as_view(), name="google"),
]

printables_urls = [
    path("bol/<int:pk>/", BOLPrintView.as_view(), name="rail-bol"),
    path("preview/bol/<int:pk>/", BOLView.as_view(), name="rail-bol-preview"),
    path("bol/v3/<str:pk>/", PDFTemplateDetailView.as_view(), name="bol"),
]

cache_urls = [
    path(
        "yard_management/rail_yard_cache/",
        RailYardCacheView.as_view(),
        name="rail-yard-cache",
    )
]

router.register(r"gauge_table", GaugeTableViewset, basename="gauge-table")

from vantedge.reports.views import TestView
from vantedge.iot.api.views import EventTypeViewSet

# Register IoT ViewSets
router.register(r"iot/event-types", EventTypeViewSet, basename="eventtype")
# router.register(r"iot/events", EventViewSet, basename="event")

test_urls = [path("df/", TestView.as_view(), name="test")]
