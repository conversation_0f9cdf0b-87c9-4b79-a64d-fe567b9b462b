import mimetypes
import base64
import pdf2image
from PIL import Image
from io import BytesIO

from vantedge.attachment.extract.api import openai

def encode_image(image_path):
    with open(image_path, "rb") as image_file:
        return base64.b64encode(image_file.read()).decode("utf-8")

def pdf_to_continuous_image(pdf_bytes, dpi=300):
    # Convert PDF pages to images
    images = pdf2image.convert_from_bytes(pdf_bytes)

    # Get total width (max of all pages) and total height (sum of all pages)
    width = max(img.width for img in images)
    height = sum(img.height for img in images)

    # Create a blank white canvas
    combined_image = Image.new("RGB", (width, height), "white")

    # Paste pages onto the combined image
    y_offset = 0
    for img in images:
        combined_image.paste(img, (0, y_offset))
        y_offset += img.height  # Move down for the next page

    io_image = BytesIO()
    combined_image.save(io_image, format="JPEG")
    io_image.seek(0)

    return io_image

class BOLDataExtractor():
    def __init__(self, file, **kwargs):
        """
            file: django file object
        """
        self.file = file

    @staticmethod
    def file_path(file):
        try:
            return file.path
        except NotImplementedError:
            return file.url

    @property
    def get_file_type(self):
        file_path = BOLDataExtractor.file_path(self.file)
        mime_type, encoding = mimetypes.guess_type(file_path)

        if mime_type:
            if mime_type == "application/pdf":
                return "PDF"
            elif mime_type.startswith("image/"):
                return "Image"

        return "Other"

    def extract_data(self):
        file_path = BOLDataExtractor.file_path(self.file)
        if self.get_file_type == "Image":
            return openai.fetch_bol_data(file_path)

        if self.get_file_type == "PDF":
            image_io = pdf_to_continuous_image(self.file.read())
            image_base64 = base64.b64encode(image_io.read()).decode("utf-8")
            return openai.fetch_bol_data(image_base64)

