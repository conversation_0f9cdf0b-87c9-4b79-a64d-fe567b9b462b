# from typing import List
# from django.contrib.auth.models import Permission
# from django.db import models, router
# from django.contrib.contenttypes.fields import GenericForeignKey
# from django.contrib.contenttypes.models import ContentType
# from django.contrib.admin.options import get_content_type_for_model
# from django.db.models.signals import post_save, m2m_changed
# from model_utils.models import TimeStampedModel
# from vantedge.users.models import User
# from vantedge.users.models.application_configuration import company_permissions_handler
# from vantedge.attachment.extract.base import BOLOCRExtractor

# from vantedge.users.models import Company

# class DocumentReadingCompany(models.Model):
#     company = models.OneToOneField(
#         Company, related_name="document_reading", on_delete=models.CASCADE
#     )
#     available_permissions = models.ManyToManyField(
#         Permission,
#         blank=True,
#         limit_choices_to={"content_type__app_label": "attachment"},
#     )

#     class Meta:
#         verbose_name_plural = "Companies"

#     def __str__(self):
#         return self.company.name


# m2m_changed.connect(
#     company_permissions_handler,
#     sender=DocumentReadingCompany.available_permissions.through,
# )


# class BillOfLading(TimeStampedModel):
#     bill_of_lading_number = models.CharField(max_length=100)
#     trip_id = models.IntegerField(null=True, blank=True)
#     company = models.ForeignKey(Company, on_delete=models.CASCADE)
#     show_bill = models.BooleanField(default=True)

#     def __str__(self):
#         return str(self.id) + ' ' + self.bill_of_lading_number

#     def set_attachmnent_as_primary(self, attachment_id):
#         for attachment in self.attachments.all():
#             if attachment.id == attachment_id:
#                 attachment.primary_bill_of_lading_attachment = True
#             else:
#                 attachment.primary_bill_of_lading_attachment = False
#             attachment.save()
#             self.save()


# def extraction_spec_default():
#     result = {
#         "number": [20, 20, 120, 120],
#         "car_num": [160, 20, 120, 120],
#         "issued_date": [300, 20, 120, 120],
#         "shipping_date": [440, 20, 120, 120],
#         "origin": [580, 20, 120, 120],
#         "destination": [160, 160, 120, 120],
#         "route": [300, 160, 120, 120],
#         "stcc": [440, 160, 120, 120],
#         "net_volume": [580, 160, 120, 120],
#         "unit": [20, 160, 120, 120],
#         "consignor": [720, 20, 120, 120],
#         "consignee": [720, 160, 120, 120],
#         "careof": [20, 300, 120, 120],
#         "notify": [160, 300, 120, 120],
#     }
#     return result


# class BolType(models.Model):

#     SCHEMA = {"type": "dict", "keys": {}, "additionalProperties": True}

#     # template_bol
#     name = models.CharField(max_length=50)
#     template_file = models.FileField()
#     image = models.FileField(blank=True, null=True, upload_to="attachment/boltypes")
#     extraction_spec = models.JSONField(default=extraction_spec_default)

#     def __str__(self):
#         return self.name

#     def extract(self, attachment):
#         e = BOLOCRExtractor(
#             attachment=attachment, config=self.extraction_spec, scale_to=(4000, None)
#         )

#         e.fingerprint()
#         return e.extracted_data

    # fingerprint_coords = models.CharField(max_length=150)

    # fingerprint = models.CharField(max_length=150)
    # car_num = models.CharField(max_length=150)
    # issued_date = models.CharField(max_length=150)
    # shipping_date = models.CharField(max_length=150)
    # origin = models.CharField(max_length=150)
    # destination = models.CharField(max_length=150)
    # route = models.CharField(max_length=150)

    # stcc = models.CharField(max_length=150)
    # net_volume = models.CharField(max_length=150)
    # unit = models.CharField(max_length=150)

    # consignor = models.CharField(max_length=150)
    # consignee = models.CharField(max_length=150)
    # careof = models.CharField(max_length=150)
    # notify = models.CharField(max_length=150)
