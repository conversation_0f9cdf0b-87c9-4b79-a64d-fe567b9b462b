from django.core.management.base import BaseCommand, CommandError
from vantedge.users.models import User
from vantedge.trackandtrace.calc import trips
from django.core.management import call_command
from datetime import datetime


class Command(BaseCommand):
    help = "Test Extractor"

    # def add_arguments(self, parser):
    #     parser.add_argument('poll_ids', nargs='+', type=int)

    def handle_reports(self):
        print("Here")

    def handle(self, *args, **options):
        self.handle_reports()
