# Generated by Django 3.2.7 on 2022-01-18 02:11

from django.db import migrations, models
import vantedge.attachment.models


class Migration(migrations.Migration):

    dependencies = [
        ('attachment', '0002_attachment_uploader'),
    ]

    operations = [
        migrations.CreateModel(
            name='BolType',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=50)),
                ('template_file', models.FileField(upload_to='')),
                ('image', models.FileField(blank=True, null=True, upload_to='files/')),
                ('extraction_spec', models.JSONField(default=None)),
            ],
        ),
        migrations.CreateModel(
            name='ExtractionSpecification',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.Char<PERSON>ield(max_length=50)),
                ('attachment_type', models.CharField(choices=[('bol', 'BOL')], max_length=10, null=True)),
                ('template_file', models.FileField(upload_to='')),
                ('normalized_file', models.FileField(blank=True, null=True, upload_to='files/')),
                ('config', models.JSONField(default=dict)),
            ],
        ),
        migrations.RenameField(
            model_name='attachment',
            old_name='uploader',
            new_name='uploaded_by',
        ),
        migrations.RemoveField(
            model_name='attachment',
            name='type',
        ),
        migrations.AddField(
            model_name='attachment',
            name='attachment_type',
            field=models.CharField(choices=[('bol', 'BOL')], max_length=10, null=True),
        ),
    ]
