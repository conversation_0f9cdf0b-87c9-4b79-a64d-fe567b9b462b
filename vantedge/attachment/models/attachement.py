import pathlib
from django.db import models
from django.contrib.contenttypes.fields import GenericForeign<PERSON>ey
from django.contrib.contenttypes.models import ContentType
from django.core.files.storage import default_storage
from model_utils.models import TimeStampedModel
from vantedge.users.models import User


class AttachmentTypes(models.TextChoices):
    BOL = "bol", "BOL"


def create_path(instance, filename):
    safish_filename = pathlib.Path(filename).name.strip('. ')
    return f"{instance.target_folder}{safish_filename}"

class Attachment(TimeStampedModel):
    content_type = models.ForeignKey(ContentType, on_delete=models.CASCADE)
    object_id = models.CharField(max_length=40)
    fk_model = GenericForeignKey("content_type", "object_id")
    file = models.FileField(upload_to=create_path)
    attachment_type = models.CharField(
        max_length=10, choices=AttachmentTypes.choices, null=True
    )
    data = models.JSONField(blank=True, null=True)
    uploaded_by = models.ForeignKey(User, on_delete=models.CASCADE)
    deleted = models.BooleanField(default=False)

    def __str__(self):
        return f"File: {self.file}, attached to {self.content_type} ({self.object_id}), uploaded by: {self.uploaded_by}"

    @property
    def target_folder(self):
        return f"attachment/{str(self.uploaded_by.company.name).lower()}/{str(self.content_type).lower()}/"

    @classmethod
    def delete_specific(cls, attachment_id):
        cls.objects.filter(id=attachment_id).update(deleted=True)
        return True

    def count_not_deleted(self):
        return self.objects().filter(deleted=False).count()

    def rename_file(self, new_file_name):
        old_name = self.file.name
        self.file.save(name=create_path(self, new_file_name), content=self.file)
        default_storage.delete(old_name)
