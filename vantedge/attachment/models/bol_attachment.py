from django.db import models
from django.contrib.contenttypes.models import ContentType
from django.core.files.base import ContentFile
from django.core.files.storage import default_storage
from django.contrib.auth.models import Permission
from django.contrib.contenttypes.models import ContentType
from django.db.models.signals import m2m_changed
from model_utils.models import TimeStampedModel
from vantedge.users.models import User
from vantedge.users.models.application_configuration import company_permissions_handler
from vantedge.attachment.extract.legacy.base import BOLOCRExtractor
from pydantic import BaseModel
from typing import List, Optional, Tuple
from django_q.tasks import async_task
from vantedge.attachment.models import Attachment
from vantedge.attachment.extract.extract import BOLDataExtractor
from vantedge.trackandtrace.models.trip import Trip
import re
from schema_field.fields import JSONSchemedField
import base64
import random



from vantedge.users.models import Company

class DocumentReadingCompany(models.Model):
    company = models.OneToOneField(
        Company, related_name="document_reading", on_delete=models.CASCADE
    )
    available_permissions = models.ManyToManyField(
        Permission,
        blank=True,
        limit_choices_to={"content_type__app_label": "attachment"},
    )

    class Meta:
        verbose_name_plural = "Companies"

    def __str__(self):
        return self.company.name


m2m_changed.connect(
    company_permissions_handler,
    sender=DocumentReadingCompany.available_permissions.through,
)

# def extraction_spec_default():
#     result = {
#         "number": [20, 20, 120, 120],
#         "car_num": [160, 20, 120, 120],
#         "issued_date": [300, 20, 120, 120],
#         "shipping_date": [440, 20, 120, 120],
#         "origin": [580, 20, 120, 120],
#         "destination": [160, 160, 120, 120],
#         "route": [300, 160, 120, 120],
#         "stcc": [440, 160, 120, 120],
#         "net_volume": [580, 160, 120, 120],
#         "unit": [20, 160, 120, 120],
#         "consignor": [720, 20, 120, 120],
#         "consignee": [720, 160, 120, 120],
#         "careof": [20, 300, 120, 120],
#         "notify": [160, 300, 120, 120],
#     }
#     return result


# class BolType(models.Model):

#     SCHEMA = {"type": "dict", "keys": {}, "additionalProperties": True}

#     # template_bol
#     name = models.CharField(max_length=50)
#     template_file = models.FileField()
#     image = models.FileField(blank=True, null=True, upload_to="attachment/boltypes")
#     extraction_spec = models.JSONField(default=extraction_spec_default)

#     def __str__(self):
#         return self.name

#     def extract(self, attachment):
#         e = BOLOCRExtractor(
#             attachment=attachment, config=self.extraction_spec, scale_to=(4000, None)
#         )

#         e.fingerprint()
#         return e.extracted_data

    # fingerprint_coords = models.CharField(max_length=150)

    # fingerprint = models.CharField(max_length=150)
    # car_num = models.CharField(max_length=150)
    # issued_date = models.CharField(max_length=150)
    # shipping_date = models.CharField(max_length=150)
    # origin = models.CharField(max_length=150)
    # destination = models.CharField(max_length=150)
    # route = models.CharField(max_length=150)

    # stcc = models.CharField(max_length=150)
    # net_volume = models.CharField(max_length=150)
    # unit = models.CharField(max_length=150)

    # consignor = models.CharField(max_length=150)
    # consignee = models.CharField(max_length=150)
    # careof = models.CharField(max_length=150)
    # notify = models.CharField(max_length=150)


# class ExtractionSpecification(models.Model):

#     SCHEMA = {"type": "dict", "keys": {}, "additionalProperties": True}

#     name = models.CharField(max_length=50)
#     attachment_type = models.CharField(
#         max_length=10, choices=AttachmentTypes.choices, null=True
#     )
#     template_file = models.FileField()
#     normalized_file = models.FileField(blank=True, null=True, upload_to="files/")
#     config = models.JSONField(default=dict)

#     def __str__(self) -> str:
#         return str(self.name)

# class DocumentFieldLocationData(BaseModel):
#     word: str
#     x1: float
#     y1: float
#     x2: float
#     y2: float


# class DocumentField(BaseModel):
#     field_text: str
#     field_word_bounding_boxes: Optional[List[DocumentFieldLocationData]]
#     complete_bounding_box: Optional[Tuple[int, int, int, int]]


class BillOfLadingData(BaseModel):
    supplier: Optional[str]
    customer: Optional[str]
    product: Optional[str]
    route: Optional[str]
    origin: Optional[str]
    destination: Optional[str]
    car_number: Optional[str]
    bol_number: Optional[str]
    usg_net_volume: Optional[str]
    m3_net_volume: Optional[str]
    weight_kg: Optional[int]
    density_kg_litr: Optional[float]
    ship_date: Optional[str]
    extractable_text: Optional[bool]
    # raw_ocr_data: Optional[List[]]
    # file_dimensions: Optional[Tuple[int, int]]
    time_for_extraction: Optional[float]

# class CarData(BaseModel):
#     car_mark: str
#     car_number: str
#     seal_numbers: Optional[list[int]]
#     weight_kg: Optional[int]
#     density_kg_litr: Optional[float]
#     net_volume_usg: Optional[float]
#     net_volume_m3: Optional[float]

# class BillOfLadingData(BaseModel):
#     car_data: Optional[list[CarData]]
#     supplier: Optional[str]
#     customer: Optional[str]
#     product: Optional[str]
#     route: Optional[str]
#     origin: Optional[str]
#     destination: Optional[str]
#     bol_number: Optional[str]
#     ship_date: Optional[str]


class BillOfLading(TimeStampedModel):
    bill_of_lading_number = models.CharField(max_length=100)
    trip_id = models.IntegerField(null=True, blank=True)
    company = models.ForeignKey(Company, on_delete=models.CASCADE)
    show_bill = models.BooleanField(default=True)

    def __str__(self):
        return str(self.id) + ' ' + self.bill_of_lading_number

    def set_attachmnent_as_primary(self, attachment_id):
        for attachment in self.attachments.all():
            if attachment.id == attachment_id:
                attachment.primary_bill_of_lading_attachment = True
            else:
                attachment.primary_bill_of_lading_attachment = False
            attachment.save()
            self.save()

class BillOfLadingAttachment(Attachment):
    __name__ = "BillOfLadingAttachment"
    bol_data = JSONSchemedField(
        schema=BillOfLadingData, default=BillOfLadingData, blank=True, null=True
    )
    bill_of_lading_object = models.ForeignKey(
        BillOfLading,
        on_delete=models.CASCADE,
        related_name="attachments",
        blank=True,
        null=True,
    )
    pdf_image_file = models.FileField(
        blank=True, null=True, upload_to="attachment/bol_images"
    )

    read_state = models.CharField(max_length=10, default="Pending")
    primary_bill_of_lading_attachment = models.BooleanField(default=True)
    related_trip = models.ForeignKey(
        Trip,
        on_delete=models.SET_NULL,
        related_name="attachments",
        blank=True,
        null=True,
    )
    # company = models.ForeignKey(Company, on_delete=models.CASCADE, null=True)

    def read_bol(self, id):
        object = BillOfLadingAttachment.objects.get(id=id)
        print("bol attachment id:", object.id, object.file)
        object.read_state = "Reading"
        super(BillOfLadingAttachment, object).save()
        try:
            success, result, raw_data = BOLDataExtractor(object.file).extract_data()
            if not success:
                print(f"Fetching data from BOL faild, the reason is {result}")
                return

            if len(result["bills_of_lading"]) > 1:
                for bol in result["bills_of_lading"]:
                    print("bol data, car number", bol)
                    bill_of_lading_attachment = BillOfLadingAttachment(
                        object_id="undefined",
                        content_type=ContentType.objects.get_for_model(
                            BillOfLadingAttachment
                        ),
                        file=object.file,
                        attachment_type="BOL",
                        uploaded_by=object.uploaded_by,
                        # pdf_image_file=ContentFile(
                        #     base64.b64decode(bol["raw_image_data"]),
                        #     name=object.file.name.split("/")[-1][:-4] + ".png",
                        # ),
                        bol_data=bol,
                        read_state="Reading",
                        data=raw_data
                    )
                    bill_of_lading_attachment.save()
                    bill_of_lading_object = BillOfLading(
                        bill_of_lading_number=" ",
                        company=self.uploaded_by.company,
                        show_bill=False,
                    )
                    bill_of_lading_object.save()
                    bill_of_lading_attachment.bill_of_lading_object = bill_of_lading_object
                    bill_of_lading_object.save()
                    bill_of_lading_attachment.attach_to_bill_of_lading()
                    bill_of_lading_attachment.attach_to_trip()
                    bill_of_lading_attachment.bill_of_lading_object.save()
                    for field in bill_of_lading_attachment.bol_data:
                        if field[0] not in [
                            "raw_ocr_data",
                            "extractable_text",
                            "file_dimensions",
                            "time_for_extraction",
                            "usg_net_volume",
                            "m3_net_volume",
                        ]:
                            if field[1] in (None, ""):
                                bill_of_lading_attachment.read_state = "Warning"
                                bill_of_lading_attachment.save()

                    bill_of_lading_attachment.read_state = (
                        "Done"
                        if not bill_of_lading_attachment.read_state == "Warning"
                        else "Warning"
                    )
                    bill_of_lading_attachment.save()
                object.delete()
            else:
                result = result["bills_of_lading"][0]

                object.bol_data = result
                object.data = raw_data

                # save image to db
                # imgstr = result["raw_image_data"]
                # object.pdf_image_file = ContentFile(
                #     base64.b64decode(imgstr),
                #     name=object.file.name.split("/")[-1][:-4] + ".png",
                # )

                # attach the bill of lading attachment to a bill of lading object

                object.attach_to_bill_of_lading()
                object.attach_to_trip()

                object.bill_of_lading_object.save()
                for field in object.bol_data:
                    if field[0] not in [
                        "raw_ocr_data",
                        "extractable_text",
                        "file_dimensions",
                        "time_for_extraction",
                        "usg_net_volume",
                        "m3_net_volume",
                    ]:
                        if field[1] in (None, ""):
                            object.read_state = "Warning"
                            object.save()
                            return
                object.read_state = "Done"
                object.save()
                print("Done", object.read_state)

        except Exception as e:
            import traceback

            print("Reading Errored: ", traceback.format_tb(e.__traceback__), e)
            object.read_state = "Failed"
            super(BillOfLadingAttachment, object).save()

    def attach_to_trip(self, trip_id=None):
        if not trip_id:
            attachment_car = re.findall(
                r"[^\W\d_]+|\d+",
                self.bol_data.car_number.replace(" ", "").strip(),
            )
            if len(attachment_car) == 2:
                attachment_car_mark, attachment_car_number = attachment_car
                attachment_car_number = attachment_car_number.zfill(6)
                if len(self.bol_data.ship_date.split("-")) > 2:
                    related_trips = Trip.objects.filter(
                        company__company=self.uploaded_by.company,
                        car__mark=attachment_car_mark,
                        car__number=attachment_car_number,
                        bol_timestamp__year=self.bol_data.ship_date.split("-")[
                            0
                        ],
                        bol_timestamp__month=self.bol_data.ship_date.split("-")[
                            1
                        ],
                        bol_timestamp__day=self.bol_data.ship_date.split("-")[2],
                    )
                else:
                    return
                if related_trips.exists():
                    self.bill_of_lading_object.trip_id = related_trips.first().id
                    self.related_trip = related_trips.first()
                    self.save()
        else:
            trip = Trip.objects.get(id=trip_id)
            self.bill_of_lading_object.trip_id = trip.id
            self.related_trip = trip
            self.save()

    def attach_to_bill_of_lading(self):
        bills_of_lading = list(
            BillOfLading.objects.filter(
                bill_of_lading_number=(
                    self.bol_data.bol_number.strip()
                    if self.bol_data.bol_number
                    else ""
                ),
                show_bill=True,
                company=self.bill_of_lading_object.company,
            )
        )
        print("fd", [bil.bill_of_lading_number for bil in bills_of_lading])
        try:
            bills_of_lading = [
                bol
                for bol in bills_of_lading
                if (
                    bol.attachments.first().bol_data.ship_date
                    == self.bol_data.ship_date.strip()
                    and bol.attachments.first().bol_data.car_number
                    == self.bol_data.car_number.strip()
                )
            ]
        except:
            bills_of_lading = []
        if len(bills_of_lading):
            bill_of_lading = bills_of_lading[0]
        else:
            self.bill_of_lading_object.bill_of_lading_number = (
                self.bol_data.bol_number.strip()
                if self.bol_data.bol_number
                else ""
            )

            bill_of_lading = BillOfLading(
                bill_of_lading_number=(
                    self.bol_data.bol_number.strip()
                    if self.bol_data.bol_number
                    else ""
                ),
                company=self.uploaded_by.company,
                show_bill=True,
            )
            bill_of_lading.save()
        self.bill_of_lading_object.delete()
        self.bill_of_lading_object = bill_of_lading

    def delete_all_non_show_bills(self):
        BillOfLading.objects.filter(show_bill=False).delete()

    def save(
        self, force_insert=False, force_update=False, using=None, update_fields=None
    ):

        if self.read_state == "Pending":
            self.bill_of_lading_object = BillOfLading(
                bill_of_lading_number=" ",
                company=self.uploaded_by.company,
                show_bill=False,
            )
            self.bill_of_lading_object.save()
            super().save(force_insert, force_update, using, update_fields)
            async_task(self.read_bol, self.id)
            print("-------------------reading-------------")
            # self.read_bol(self.id)
        else:
            super().save(force_insert, force_update, using, update_fields)

