import pickle

from django.core.files import File
from django.views.generic import CreateView
from import_export.admin import ImportMixin
from pdf2image import convert_from_bytes, convert_from_path
from rest_framework.decorators import action
from rest_framework.exceptions import NotFound
from rest_framework.pagination import LimitOffsetPagination
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.viewsets import ModelViewSet
from vantedge.attachment.extract.legacy.base import FingerprintError

from vantedge.attachment.models import Attachment

from .exceptions import AttachmentExceptions
# from .models import BolType
from vantedge.attachment.extract.legacy.methods import preprocess_pdf
from .models import Attachment, AttachmentTypes
from .serializers import AttachmentSerializer
from vantedge.trackandtrace.models.trip import Trip


from vantedge.trackandtrace.models.other import (
    TrackAndTraceCompany,
    RailCar,
    Stcc,
    RailStation,
)

from vantedge.attachment.serializers import BillOfLadingAttachmentSerializer
from vantedge.attachment.models import BillOfLadingAttachment

class BOLAttachmentViewMixin:
    @action(detail=True, methods=["post"], permission_classes=[IsAuthenticated])
    def save_extracted_data(self, request, pk):
        return Response(status=400)
        # object = self.get_object()

        # user = request.user
        # for key in self.request.data:

        #     if hasattr(object, key):
        #         try:
        #             setattr(object, key, self.request.data[key])
        #             object.save()
        #         except Exception as e:
        #             print("error", key)

        #     if key == "net_volume":
        #         setattr(object, "volume", self.request.data[key])
        #         object.save()

        #     if key == "origin":
        #         city = self.request.data[key].split(",")[0]
        #         RailStation.objects.filter(city=city)[0]
        #         setattr(object, "origin", RailStation.objects.filter(city=city)[0])

        # return Response()

    @action(detail=True, methods=["post"], permission_classes=[IsAuthenticated])
    def upload_file(self, request, pk):
        obj = self.get_object()
        user = request.user
        attachments = []
        for key in self.request.data:
            attachments.append(
                obj.attachments.create(
                    fk_model=obj,
                    file=request.data[key],
                    attachment_type=self.attachment_type,
                    uploaded_by=user,
                )
            )
        # for attachment in attachments:
        #     for bol_type in BolType.objects.all():
        #         print(bol_type.extraction_spec["car_num"], bol_type)
        #         extracted_info = {}
        #         try:
        #             extracted_info = bol_type.extract(attachment)
        #             break
        #         except FingerprintError:
        #             continue

        #     if attachment.data:
        #         attachment.data["extracted"] = extracted_info
        #     else:
        #         attachment.data = {"extracted": extracted_info}
        #     attachment.save()

        return Response(AttachmentSerializer(attachments, many=True).data)

    @action(detail=True, methods=["post"], permission_classes=[IsAuthenticated])
    def delete_file(self, request, pk):
        return Response(status=400)
        attachment = BillOfLadingAttachment.objects.get(id=request.data["fileId"])
        attachment.delete()
        return Response(True)

    @action(detail=True, methods=["get"], permission_classes=[IsAuthenticated])
    def get_file(self, request, pk):
        obj = self.get_object()
        attachments = obj.attachments.filter(deleted=False)
        data = BillOfLadingAttachmentSerializer(attachments, many=True).data
        return Response(data)

    # @action(detail=True, methods=["get"], permission_classes=[IsAuthenticated])
    # def process_file(self, request, pk):
    #     attachment = None
    #     content = attachment.file.read()
    #     img = preprocess_pdf(content)
    #     return None
    #     # return extract_info(img)
