from django.apps import apps
from django.db.models import <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ield

from rest_framework.viewsets import ViewSet
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated

from vantedge.edi.models import EdiCompany
from vantedge.reports.models import ReportCompany
from vantedge.trackandtrace.models import TrackAndTraceCompany
from vantedge.wheelhouse.models import WheelhouseCompany
from vantedge.yard_management.models import YardManagementCompany

from .models import AutocompleteMixin


class AutocompleteViewSet(ViewSet):
    @action(detail=False, methods=["get"], permission_classes=[IsAuthenticated])
    def suggestions(self, request):
        suggestions = []
        app_label, model_name = self.request.query_params.get("model", ".").split(".")
        field_name = self.request.query_params.get("field")
        user_input = self.request.query_params.get("input")
        limit = self.request.query_params.get("limit", 15)

        if not app_label and model_name:
            raise ValueError("Autocomplete:  Provide both app_label and model_name.")

        try:
            model = apps.get_model(app_label, model_name)
        except LookupError:
            raise ValueError(
                f"Autocomplete:  Cannot determine model from {app_label}.{model_name}"
            )

        if not issubclass(model, AutocompleteMixin):
            raise NotImplementedError(
                f"Autocomplete:  {app_label}.{model_name} not subclassed from AutocompleteMixin."
            )

        if not hasattr(model, field_name):
            raise ValueError(
                f"Autocomplete:  {app_label}.{model_name} cannot suggest for field: {field_name}."
            )

        # get list of values for field_name, sorted by most frequent entry first
        qs = (
            model.autocomplete_qs(request.user)
            .values_list(field_name, flat=True)
            .annotate(field_count=Count(field_name))
            .order_by("-field_count")
        ).exclude(**{f"{field_name}__isnull": True})

        field = model._meta.get_field(field_name)

        if field.blank:  # exclude blanks if blank enabled
            qs = qs.exclude(**{f"{field_name}__exact": ""})

        if user_input and len(user_input):
            filters = Q(**{"__".join([field_name, "icontains"]): user_input})
            # Get a fuzzy search of text
            if isinstance(field, (CharField, TextField)):
                filters |= Q(**{"__".join([field_name, "trigram_similar"]): user_input})
            qs = qs.filter(filters)

        filters = {}
        # Apply filters and exclude empty/null data
        qs = qs.filter(**filters).distinct()
        suggestions = qs[:limit]

        return Response(suggestions)
