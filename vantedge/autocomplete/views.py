from rest_framework.decorators import action
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.status import HTTP_400_BAD_REQUEST

from django.db.models import Count, <PERSON><PERSON>ield, <PERSON>r<PERSON>ield, Integer<PERSON>ield, <PERSON>loat<PERSON>ield
from django.db.models.functions import Cast
from django.contrib.postgres.search import TrigramSimilarity
from django.core.exceptions import FieldDoesNotExist

from vant_dbreplication.utils import ReplicationHandler

from functools import wraps
from django.db.models import QuerySet

DEFAULT_LIMIT = 40


def paginate_list(func):
    @wraps(func)
    def inner(self, *args, **kwargs):
        listing = func(self, *args, **kwargs)

        assert isinstance(listing, (list, QuerySet)), "apply_pagination expects a List"

        page = self.paginate_queryset(listing)
        if page is not None:
            return self.get_paginated_response(page)
        else:
            return Response({"results": listing[:DEFAULT_LIMIT], "count": len(listing)})

    return inner


# --------


class AutoCompleteViewSetMixin:
    @paginate_list
    @action(detail=False, methods=["get"], permission_classes=[IsAuthenticated])
    def autocomplete(self, request, *args, **kwargs):
        suggestions = []

        field_name = self.request.query_params.get("field")
        user_input = self.request.query_params.get("input")
        limit = self.request.query_params.get("limit", 15)

        custom_ordering_fields = (
            self.custom_ordering_fields
            if hasattr(self, "custom_ordering_fields")
            else {}
        )

        mapping_autocomplete_fields = (
            self.mapping_autocomplete_fields
            if hasattr(self, "mapping_autocomplete_fields")
            else {}
        )

        mapping_autocomplete_field_names = (
            self.mapping_autocomplete_field_names
            if hasattr(self, "mapping_autocomplete_field_names")
            else {}
        )

        field_name = mapping_autocomplete_field_names.get(field_name, field_name)

        if hasattr(self, "filter_queryset"):
            qs = self.filter_queryset(self.get_queryset())
        else:
            qs = self.get_queryset()

        model = qs.model
        field_names = [i.name for i in model._meta.fields]
        if (
            not field_name.split("__")[0]
            in field_names  # for v3-ticket-hierarchy (e.g ticket-entry ...)
            and not hasattr(
                model, field_name.split("__")[0]
            )  # for many_to_many (e.g group & permissions)
            and field_name not in dict(custom_ordering_fields)
        ):
            # field_name__foreign_key_property (e.g railroad__name) should not cause 400
            return suggestions

        try:
            field = model._meta.get_field(field_name)
        except FieldDoesNotExist:
            field = None

        # check if object is v3 object and has association
        has_site_specific = False
        is_site_specific = False

        replication_handler= ReplicationHandler()
        AssociationModel = replication_handler.get_server_association_model(model)

        if AssociationModel:
            is_site_specific = True

            if "additional_data" in field_names and (field_name == "enabled" or field_name=="is_active"):
                if not user_input:
                    if field_name == "enabled" and qs.filter(additional_data__site_specific_config__enabled=True).exists():
                        has_site_specific = True
                    elif field_name == "is_active" and qs.filter(additional_data__site_specific_config__is_active=True).exists():
                        has_site_specific = True


        qs = qs.values_list(field_name, flat=True).exclude(
            **{f"{field_name}__isnull": True}
        )

        # if field and field.blank:
        #     qs = qs.exclude(**{f"{field_name}__exact": ""})

        if not user_input:
            qs = qs.annotate(search_weight=Count(field_name))

        else:
            cast_field = Cast(field_name, output_field=CharField())

            qs = qs.filter(**{f"{field_name}__icontains": user_input}).annotate(
                search_weight=TrigramSimilarity(cast_field, user_input)
            )

        qs = qs.distinct().order_by("-search_weight")

        suggestions = qs

        if field_name in mapping_autocomplete_fields:
            suggestions = [
                mapping_autocomplete_fields[field_name][entry] for entry in suggestions
            ]

        # v3 enable field also reading server_associations, need to add extra field
        if is_site_specific:
            mapping = {}
            if field_name == "enabled":
                mapping = {True: 'Enabled', False: 'Disabled'}
            else:
                mapping = {True: 'Active', False: 'Inactive'}
            suggestions = [mapping.get(i, i) for i in suggestions]
            if has_site_specific:
                suggestions.append('Site Specific')

        return suggestions

        # return Response({"results": suggestions, "count": len(suggestions)})
