from django.apps import AppConfig
from django.utils import timezone
from vant_ticket.signals import ticket_saved
from vant_ticket.schemas.xborder import BrokerageStatus

def find_index_by_temp_id(arr, temp_id):
    for index, item in enumerate(arr):
        if item.get('temp_id') == temp_id:
            return index
    return -1

def populate_and_send(ticket, temp_id):
    from vantedge.customs.models.invoice import CustomsInvoice
    from vantedge.tboss.models import Ticket

    index = find_index_by_temp_id(ticket.data["customs_data"], temp_id)

    ticket = Ticket.objects.get(id=ticket.id)
    ticket.data["customs_data"][index]["send_date"] = timezone.now()
    invoice = None

    try:
        invoice = CustomsInvoice.populate_custom_invoice_from_ticket(ticket, index)
        print(f"invoice {invoice.id} generated...")
    except Exception as e:
        ticket.data["customs_data"][index]["status"] = BrokerageStatus.FAILED_TO_INVOICE.value
        ticket.data["customs_data"][index]["message"] = str(e)
        ticket.save()
        CustomsInvoice.send_status(None, ticket)
        return
    else:
        ticket.data["customs_data"][index]["status"] = BrokerageStatus.AWAITING_POST_TO_BROKERAGE.value
        ticket.data["customs_data"][index]["invoice_id"] = str(invoice.id)

        invoice.send(user=None)
        print(f"trying send invoice {invoice.id} to deringer...")
        status_list = invoice.status_list[-1]

        if invoice.status == 'edi-process-failed':
            print(f"edi failed to created from invoice {invoice.id}")
            ticket.data["customs_data"][index]["status"] = BrokerageStatus.EDI_DATA_HAS_ERROR.value
            ticket.data["customs_data"][index]["message"] = status_list[1]
        elif invoice.status == 'send-failed':
            print(f"edi failed to send for invoice {invoice.id} ")
            ticket.data["customs_data"][index]["status"] = BrokerageStatus.SENT_TO_BROKERAGE_FAILED.value
            ticket.data["customs_data"][index]["message"] = status_list[1]
        elif invoice.status == 'sent':
            print(f"edi sent to deringer for invoice {invoice.id} ")
            ticket.data["customs_data"][index]["status"] = BrokerageStatus.SENT_TO_BROKERAGE.value
            ticket.data["customs_data"][index]["message"] = "Sent to Customs Broker"

        ticket.save()
        CustomsInvoice.send_status(invoice, ticket)

def check_ticket_for_custom_delivery(sender, **kwargs):
    from django_q.tasks import async_task
    from vantedge.wheelhouse.models import BolNumber

    if (
        isinstance(sender.data, dict) and
        sender.data.get("customs_data") and
        sender.data["customs_data"][-1]["status"] == BrokerageStatus.WAITING_TO_INVOICE.value
    ):
        print("creating customs invoice from vant_ticket")
        temp_id = BolNumber.generate_reference_number()
        sender.data["customs_data"][-1]["temp_id"] = temp_id
        # populate_and_send(sender, temp_id)
        async_task(
            populate_and_send,
            sender,
            temp_id,
            task_name=f"Customs invoice ticket {sender}"
        )

class CustomConfig(AppConfig):
    default_auto_field = "django.db.models.BigAutoField"
    name = "vantedge.customs"

    def ready(self) -> None:
        ticket_saved.connect(check_ticket_for_custom_delivery)
        return super().ready()
