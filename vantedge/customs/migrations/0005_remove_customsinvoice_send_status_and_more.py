# Generated by Django 4.2 on 2024-09-05 06:16

import django.contrib.postgres.fields
from django.db import migrations, models
import django.db.models.deletion
import functools
import model_utils.fields
import schema_field.fields
import vantedge.customs.models.company


class Migration(migrations.Migration):

    dependencies = [
        ('wheelhouse', '0099_facility_default_measurement_system_and_more'),
        ('tboss', '0031_servicecontractticket_deleted_and_more'),
        ('customs', '0004_remove_customsfeaturesubscription_company_and_more'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='customsinvoice',
            name='send_status',
        ),
        migrations.AddField(
            model_name='customscompany',
            name='data',
            field=schema_field.fields.JSONSchemedField(blank=True, default=vantedge.customs.models.company.CustomCompanyData, encoder=functools.partial(schema_field.fields.JSONSchemedEncoder, *(), **{'schema': (vantedge.customs.models.company.CustomCompanyData,)}), null=True, schema=(vantedge.customs.models.company.CustomCompanyData,)),
        ),
        migrations.AddField(
            model_name='customsinvoice',
            name='status_list',
            field=django.contrib.postgres.fields.ArrayField(base_field=django.contrib.postgres.fields.ArrayField(base_field=models.CharField(max_length=100), size=None), default=list, size=10),
        ),
        migrations.AddField(
            model_name='customsinvoice',
            name='tboss_buyer',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='custom_tboss_buyer_set', to='tboss.company'),
        ),
        migrations.AddField(
            model_name='customsinvoice',
            name='tboss_consignee',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='custom_tboss_consignee_set', to='tboss.company'),
        ),
        migrations.AddField(
            model_name='customsinvoice',
            name='tboss_exporter',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='custom_tboss_exporter_set', to='tboss.company'),
        ),
        migrations.AddField(
            model_name='customsinvoice',
            name='tboss_supplier',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='custom_tboss_supplier_set', to='tboss.company'),
        ),
        migrations.AddField(
            model_name='customsinvoice',
            name='term_of_sale',
            field=models.CharField(choices=[('01', 'Basic'), ('02', 'End of Month (EOM)'), ('03', 'Fixed Date'), ('04', 'Deferred or Installment'), ('08', 'Basic Discount Offered'), ('ZZ', 'Mutually Defined')], default='01', max_length=2),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='customsinvoice',
            name='term_of_transport',
            field=models.CharField(choices=[('CAF', 'Cost and Freight'), ('CIF', 'Cost, Insurance, and Freight'), ('CPT', 'Carriage Paid To'), ('DAF', 'Delivered at Frontier'), ('DAP', 'Delivered at Place'), ('DAT', 'Delivered at Terminal'), ('DDP', 'Delivered Duty Paid'), ('EXQ', 'Ex Quay - Duty Paid'), ('EXW', 'Ex Works'), ('FAS', 'Free Alongside Ship'), ('FCA', 'Free Carrier'), ('FOB', 'Free on Board'), ('FOR', 'Free on Rail'), ('FOT', 'Free on Truck')], default='CAF', max_length=3),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='customsinvoice',
            name='ticket',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='custom_invoices_ticket', to='tboss.ticket'),
        ),
        migrations.AddField(
            model_name='customsinvoice',
            name='transportation_method',
            field=models.CharField(choices=[('A', 'Air'), ('M', 'Motor (Common Carrier)'), ('R', 'Rail'), ('X', 'Intermodal (Piggyback)'), ('VE', 'Ocean Vessel')], default='R', max_length=2),
        ),
        migrations.AddField(
            model_name='customsinvoice',
            name='tsca_statement',
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name='customsinvoice',
            name='buyer',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='custom_buyer_set', to='wheelhouse.party'),
        ),
        migrations.AlterField(
            model_name='customsinvoice',
            name='car',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='custom_invoices_car', to='wheelhouse.loadedrailcar'),
        ),
        migrations.AlterField(
            model_name='customsinvoice',
            name='carrier_code',
            field=models.CharField(max_length=4),
        ),
        migrations.AlterField(
            model_name='customsinvoice',
            name='consignee',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='custom_consignee_set', to='wheelhouse.party'),
        ),
        migrations.AlterField(
            model_name='customsinvoice',
            name='exporter',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='custom_exporter_set', to='wheelhouse.party'),
        ),
        migrations.AlterField(
            model_name='customsinvoice',
            name='freight_cost',
            field=models.DecimalField(blank=True, decimal_places=4, help_text='Per Barrel', max_digits=8, null=True),
        ),
        migrations.AlterField(
            model_name='customsinvoice',
            name='packaging',
            field=models.CharField(choices=[('19', 'Tank Truck'), ('TC', 'Truck Load'), ('1P', 'Tank Load')], default='1P', max_length=20),
        ),
        migrations.AlterField(
            model_name='customsinvoice',
            name='status',
            field=model_utils.fields.StatusField(choices=[('open', 'open'), ('sending', 'sending'), ('send-failed', 'send-failed'), ('sent', 'sent'), ('acknowledge', 'acknowledge'), ('acknowledge-rejected', 'acknowledge-rejected'), ('closed', 'closed'), ('void', 'void'), ('accepted', 'accepted')], default='open', max_length=100, no_check_for_status=True),
        ),
        migrations.AlterField(
            model_name='customsinvoice',
            name='supplier',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='custom_supplier_set', to='wheelhouse.party'),
        ),
    ]
