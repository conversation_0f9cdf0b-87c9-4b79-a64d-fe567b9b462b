from django.db import models
from django.conf import settings
from django.contrib.auth.models import Permission
from django.db.models.signals import m2m_changed
from vantedge.users.models.application_configuration import company_permissions_handler
from vantedge.users.models import Company


class CustomsCompany(models.Model):
    company = models.OneToOneField(
        Company, related_name="customs", on_delete=models.CASCADE
    )
    username_ftp_deringer = models.Char<PERSON>ield(max_length=50)
    password_deringer = models.Char<PERSON>ield(max_length=50)
    account_deringer = models.Char<PERSON>ield(max_length=50)

    available_permissions = models.ManyToManyField(
        Permission, blank=True, limit_choices_to={"content_type__app_label": "customs"}
    )

    class Meta:
        verbose_name_plural = "Companies"

    def __str__(self):
        return str(self.company.name)


m2m_changed.connect(
    company_permissions_handler, sender=CustomsCompany.available_permissions.through
)
