from django.db import models
from django.contrib.auth.models import Permission
from django.db.models.signals import m2m_changed

from vantedge.users.models.application_configuration import company_permissions_handler
from vantedge.util.mixins import EasyAuditModelConfigMixin
from vantedge.users.models import Company
from vantedge.tboss.models import TBossUser

from schema_field.fields import JSONSchemedField
from pydantic import BaseModel, EmailStr
from typing import Optional, List

class CustomCompanyData(BaseModel):
    is_active: bool = True
    deringer_soap_username: Optional[str] = None
    deringer_soap_password: Optional[str] = None
    segment_terminator: Optional[str] = None
    distribution_group_email: List[Optional[EmailStr]] = []
    ftp_server_url: Optional[str] = None
    ftp_remote_locations: Optional[dict]

class CustomsCompany(EasyAuditModelConfigMixin, models.Model):
    company = models.OneToOneField(
        Company, related_name="customs", on_delete=models.CASCADE
    )
    gs_code_deringer = models.CharField(max_length=30, blank=True, null=True)
    sender_id_deringer = models.CharField(max_length=15, blank=True, null=True)
    username_ftp_deringer = models.CharField(max_length=50)
    password_deringer = models.CharField(max_length=50)
    account_deringer = models.CharField(max_length=50)
    data = JSONSchemedField(default=CustomCompanyData, schema=CustomCompanyData, blank=True, null=True)

    available_permissions = models.ManyToManyField(
        Permission, blank=True, limit_choices_to={"content_type__app_label": "customs"}
    )
    tboss_responsible_person = models.ForeignKey(
        TBossUser,
        on_delete=models.CASCADE,
        null=True,
        blank=True)

    class Meta:
        verbose_name_plural = "Companies"

    def __str__(self):
        return str(self.company.name)


m2m_changed.connect(
    company_permissions_handler, sender=CustomsCompany.available_permissions.through
)
