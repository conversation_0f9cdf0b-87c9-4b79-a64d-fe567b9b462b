from django.db import models
from django.core.validators import EmailValidator
from django.core.exceptions import ValidationError
from django.contrib.postgres.fields import Array<PERSON>ield
from django.utils import timezone
from django.conf import settings
from django.utils.functional import cached_property
from django.urls import reverse
from model_utils.models import TimeStampedModel
from model_utils.fields import StatusField
from model_utils import Choices

import datetime
import arrow
import copy

# from vant_ticket.models import Ticket
from vant_ticket.schemas.xborder import BrokerageStatus
from schema_field.fields import JSONSchemedField
from vantedge.wheelhouse.models import Party, LoadedRailCar
from vantedge.tboss.models import CrossBorderContract, Ticket
from vantedge.tboss.models import Company as TbossCompany
from vantedge.edi.comm.edi_810 import edi810_transaction_data_from_invoice
from vantedge.edi.comm.deringer import EDI810Deringer, EDI997Deringer
from vantedge.edi.models import EdiCompany, EdiDocument
from vantedge.util.units import convert_unit, conversion_factor
from vantedge.util.notifications.email import send_customs_ticket_status_update

from .company import CustomsCompany
from .schema import CustomsInvoiceDataSchema, EDI810PackageSchema, EDIDataSchema

convert_to_date = lambda x: arrow.get(x).date().strftime("%Y-%m-%d")

class CustomsInvoice(TimeStampedModel):
    # show exporter_contact in GUI
    # show TSCA in GUI
    TRANSACTION_CI = "Consolidated Invoice"
    TRANSACTION_CO = "Corrected"
    TRANSACTION_IN = "Inquiry"
    TRANSACTION_PP = "Prepaid Invoice"
    TRANSACTION_SS = "Single Shipper, Single Consignee"

    TRANSACTION_TYPE_CODES = (
        ("CI", TRANSACTION_CI),
        ("CO", TRANSACTION_CO),
        ("IN", TRANSACTION_IN),
        ("PP", TRANSACTION_PP),
        ("SS", TRANSACTION_SS),
    )

    INVOICE_PRICE_INCLUDE_CHOICES = (
        ("0", "Nothing"),
        ("91", "Freight, Brokerage"),
        ("92", "Brokerage, Duty, USC User Fees"),
        ("93", "Duty, USC User Fees"),
    )

    TERM_OF_SALES_CHOICES = (
        ("01", "Basic"),
        ("02", "End of Month (EOM)"),
        ("03", "Fixed Date"),
        ("04", "Deferred or Installment"),
        ("08", "Basic Discount Offered"),
        ("ZZ", "Mutually Defined")
    )

    TERMS_OF_TRANSPORTATION = (
        ("CAF", "Cost and Freight"),
        ("CIF", "Cost, Insurance, and Freight"),
        ("CPT", "Carriage Paid To"),
        ("DAF", "Delivered at Frontier"),
        ("DAP", "Delivered at Place"),
        ("DAT", "Delivered at Terminal"),
        ("DDP", "Delivered Duty Paid"),
        ("EXQ", "Ex Quay - Duty Paid"),
        ("EXW", "Ex Works"),
        ("FAS", "Free Alongside Ship"),
        ("FCA", "Free Carrier"),
        ("FOB", "Free on Board"),
        ("FOR", "Free on Rail"),
        ("FOT", "Free on Truck")
    )

    US_CUSTOM_CLEARANCE_OFFICE_CHOICES = (
        ("0102", "Bangor ME"),
        ("0104", "Jackman ME"),
        ("0106", "Houlton ME"),
        ("0108", "Van Buren ME"),
        ("0109", "Madewaska, ME"),
        ("0115", "Calais ME"),
        ("0203", "Richford VT"),
        ("0206", "Pittsburgh NH"),
        ("0207", "Burlington VT"),
        ("0209", "Derby Line VT"),
        ("0211", "Norton VT"),
        ("0212", "Highgate VT"),
        ("0401", "Boston MA"),
        ("0410", "E Granby-Bridgeport CT"),
        ("0411", "E Granby-Bradley Int'l Arpt"),
        ("0412", "E Granby-New Haven CT"),
        ("0701", "Ogdensburg NY"),
        ("0704", "Massena NY"),
        ("0708", "Alexandria Bay NY"),
        ("0712", "Champlain NY"),
        ("0901", "Buffalo Buffalo NY"),
        ("1703", "Atlanta-Savannah GA"),
        ("1704", "Atlanta GA"),
        ("2704", "Los Angeles CA"),
        ("3004", "Blaine WA"),
        ("3302", "Eastport ID"),
        ("3310", "Sweetgrass MT"),
        ("3316", "Piegan MT"),
        ("3318", "Rooseville MT"),
        ("3401", "Pembina ND"),
        ("3403", "Portal ND"),
        ("3422", "Dunseith ND"),
        ("3601", "Duluth MN"),
        ("3604", "Int'l Falls MN"),
        ("3801", "Detroit MI"),
        ("3802", "Port Huron MI"),
        ("3803", "Sault Ste Marie MI"),
        ("3806", "Grand Rapids MI"),
        ("3807", "Romulus MI"),
        ("3901", "Chicago IL"),
        ("4102", "Cincinnati OH"),
        ("4601", "JFK-Newark NJ"),
        ("4701", "JFK Arpt NY"),
        ("5201", "Miami FL (OCN)"),
        ("5204", "Miami-West Palm Beach FL"),
        ("5206", "Miami Intl Arpt FL"),
        ("5301", "Houston TX"),
        ("3301", "Raymond MT")
    )

    CANADA_PROVINCES_CHOICE = (
        ("XA", "Alberta"),
        ("XB", "New Brunswick"),
        ("XC", "British Columbia"),
        ("XM", "Manitoba"),
        ("XN", "Nova Scotia"),
        ("XO", "Ontario"),
        ("XP", "Prince Edward Island"),
        ("XQ", "Quebec"),
        ("XS", "Saskatchewan"),
        ("XT", "Northwest Territories"),
        ("XV", "Nunavut"),
        ("XW", "Newfoundland"),
        ("XY", "Yukon"),
    )

    SHIPMENT_METHOD_OF_PAYMENT_CHOICES = (
        ("11", "Rule 11 Shipment"),
        ("BP", "Paid by Buyer"),
        ("CA", "Advance Collect"),
        ("CC", "Collect"),
        ("CD", "Collect on Delivery"),
        ("CF", "Collect, Freight Credited Back to Customer"),
        ("DE", "Per Contract"),
        ("DF", "Defined by Buyer and Seller"),
        ("FO", "FOB Port of Call"),
        ("HP", "Half Prepaid"),
        ("MX", "Mixed"),
        ("NC", "Service Freight, No Charge"),
        ("NR", "Non Revenue"),
        ("PA", "Advance Prepaid"),
        ("PB", "Customer Pick-up/Backhaul"),
        ("PC", "Prepaid but Charged to Customer"),
        ("PD", "Prepaid by Processor"),
        ("PE", "Prepaid and Summary Bill"),
        ("PL", "Prepaid Local, Collect Outstate"),
        ("PO", "Prepaid Only"),
        ("PP", "Prepaid (by Seller)"),
        ("PS", "Paid by Seller"),
        ("PU", "Pickup"),
        ("RC", "Return Container Freight Paid by Customer"),
        ("RF", "Return Container Freight Free"),
        ("RS", "Return Container Freight Paid by Supplier"),
        ("TP", "Third Party Pay"),
        ("WC", "Weight Condition"),
    )

    PACKAGING_CHOICES = ( # Not usefull data, and not setting in 810
        ("19", "Tank Truck"),
        ("TT", "Truck Load"),
        ("1P", "Tank Load"),
    )

    TRANSPORT_METHOD_CHOICES = (
        ("A", "Air"),
        ("M", "Motor (Common Carrier)"),
        ("R", "Rail"),
        ("X", "Intermodal (Piggyback)"),
        ("VE", "Ocean Vessel")
    )

    STATUS = Choices(
        "open",
        "edi-process-failed",
        "sending",
        "send-failed",
        "sent",
        "acknowledge",
        "acknowledge-rejected",
        "closed",
        "void",
        "accepted"
    )

    STATUS_TRANSLATION = {
        0: 'Invoice Pending',
        1: 'Invoice Creation Failed',
        2: 'EDI Pending',
        3: 'EDI Creation Failed',
        4: 'EDI Transmission Failed',
        5: 'EDI Sent',
        6: 'Customs Rejected',
        7: 'Customs Accepted'
    }

    ALLOWED_STATUS_SEND = ["open", "edi-process-failed", "send-failed", "acknowledge-rejected"]

    status = StatusField()
    status_list = ArrayField( # [ [code, text], [code, text],... ]
        ArrayField(models.CharField(max_length=100, blank=False)),
        default=list,
        size=10
    )
    company = models.ForeignKey(CustomsCompany, on_delete=models.PROTECT)

    buyer = models.ForeignKey(
        Party, related_name="custom_buyer_set", on_delete=models.PROTECT, blank=True, null=True
    )
    consignee = models.ForeignKey(
        Party, related_name="custom_consignee_set", on_delete=models.PROTECT, blank=True, null=True
    )
    exporter = models.ForeignKey(
        Party, related_name="custom_exporter_set", on_delete=models.PROTECT, blank=True, null=True
    )
    supplier = models.ForeignKey(
        Party, related_name="custom_supplier_set", on_delete=models.PROTECT, blank=True, null=True
    )
    car = models.ForeignKey(
        LoadedRailCar, related_name="custom_invoices_car", blank=True, null=True, on_delete=models.PROTECT
    )

    tboss_buyer = models.ForeignKey(
        TbossCompany, related_name="custom_tboss_buyer_set", on_delete=models.SET_NULL, blank=True, null=True
    )
    tboss_consignee = models.ForeignKey(
        TbossCompany, related_name="custom_tboss_consignee_set", on_delete=models.SET_NULL, blank=True, null=True
    )
    tboss_exporter = models.ForeignKey(
        TbossCompany, related_name="custom_tboss_exporter_set", on_delete=models.SET_NULL, blank=True, null=True
    )
    tboss_supplier = models.ForeignKey(
        TbossCompany, related_name="custom_tboss_supplier_set", on_delete=models.SET_NULL, blank=True, null=True
    )
    ticket = models.ForeignKey(Ticket, related_name="custom_invoices_ticket",on_delete=models.SET_NULL, blank=True, null=True)
    customs_data_index = models.PositiveSmallIntegerField(null=True, blank=True)
    unit_price = models.DecimalField(max_digits=8, decimal_places=4)
    currency = models.CharField(max_length=3, choices=(("CAD", "CAD"), ("USD", "USD")))
    VOLUME_UNIT_TYPES = Choices("LITRE", "USG", "BARREL", "M3", "SHORT TON")
    volume_unit = models.CharField(max_length=15, choices=VOLUME_UNIT_TYPES)
    discount = models.DecimalField(max_digits=8, decimal_places=4, null=True, blank=True)
    carrier_code = models.CharField(max_length=4)
    paps_code = models.CharField(max_length=12, null=True, blank=True)
    transaction_type_code = models.CharField(
        max_length=2, choices=TRANSACTION_TYPE_CODES
    )  # BIG07

    is_consignee_related_invoice_party = models.BooleanField(default=False)  # NTE02
    invoice_price_includes = models.CharField(
        max_length=2, choices=INVOICE_PRICE_INCLUDE_CHOICES
    )

    us_custom_office = models.CharField(
        max_length=4, choices=US_CUSTOM_CLEARANCE_OFFICE_CHOICES
    )  # NTE02
    packaging = models.CharField(max_length=20, choices=PACKAGING_CHOICES, default="1P") # Not usefull data, and not setting in 810
    number_of_packages = models.PositiveSmallIntegerField(default=1)
    country_of_export = models.CharField(max_length=2, choices=CANADA_PROVINCES_CHOICE)
    est_date_reach_border = models.DateField()
    term_of_sale = models.CharField(max_length=2, choices=TERM_OF_SALES_CHOICES)
    date_of_export = models.DateField()
    date_of_invoice = models.DateField()

    term_of_transport = models.CharField(max_length=3, choices=TERMS_OF_TRANSPORTATION)
    shipment_method_of_payment = models.CharField(
        max_length=2, choices=SHIPMENT_METHOD_OF_PAYMENT_CHOICES
    )

    estimate_freight_charge = models.PositiveSmallIntegerField() # freight cost to border
    freight_cost = models.DecimalField(
        max_digits=8, decimal_places=4, null=True, help_text="Per Barrel", blank=True
    ) # this one goes to TD03 and related to shipemnt term, so ex. Exwork has no freigh cost
    basis_of_price = models.CharField(
        max_length=2, choices=(("CT", "Contract"), ("PE", "Price Per Each"))
    )
    tsca_statement = models.CharField(max_length=255, blank=True, null=True)
    transportation_method = models.CharField(max_length=2, choices=TRANSPORT_METHOD_CHOICES, default="R")
    data = JSONSchemedField(
        schema=CustomsInvoiceDataSchema, default=CustomsInvoiceDataSchema
    )

    class Meta:
        ordering = ["-id"]

    def __str__(self):
        return f"Customs Invoice {self.id}"

    def delete(self, *args, **kwargs):
        if self.status != "open":
            raise Exception("Invoice has history")

        super().delete(*args, **kwargs)

    @cached_property
    def ticket_customs_data(self):
        customs_data = copy.deepcopy(self.ticket.data["customs_data"][self.customs_data_index]["edi_data"])
        customs_data.update(self.ticket.data["customs_data"][self.customs_data_index]["print_invoice"])
        return customs_data

    @property
    def is_readonly(self):
        if self.status not in self.ALLOWED_STATUS_SEND:
            return True
        return False

    @property
    def shipment(self):
        if self.car:
            return self.car.rail_shipment
        return None

    @property
    def importer_reference_number(self):
        if self.car:
            return self.car.rail_shipment.reference_number
        if self.ticket:
            return self.ticket.code

    @property
    def shipper_reference_number(self):
        if self.car:
            return self.car.rail_shipment.reference_number
        if self.ticket:
            return self.paps_code

    @property
    def invoice_number(self):
        return self.pk

    @property
    def weight(self):
        if self.car:
            return self.car.weight
        elif self.ticket:
            net_weight = self.ticket_customs_data["net_weight"]
            weight_unit = self.ticket_customs_data["net_weight_unit"]
            weight_in_kg = convert_unit(float(net_weight), weight_unit.upper(), "KG")
            return weight_in_kg

    @property
    def agent_reference_number(self):
        # we do border clearance otherwise it should be agent reference number
        return "None"

    @property
    def latest_status_code(self):
        return self.status_list[-1][0] if self.status_list else None

    # def get_identifier(self):
    #     return "value", "type"

    # def get_invoice(self):
    #     return self.invoice_number

    def get_contract(self):
        if self.car:
            shipment = self.car.rail_shipment
            schedule = shipment.schedule
            contract = schedule.purchase_contract or schedule.sale_contract

        if self.ticket:
            contract = CrossBorderContract.objects.get(
                pk=self.ticket_customs_data["cross_border_contract_id"]
            )

        return contract

    def get_sender_id(self):
        return self.company.sender_id_deringer

    def get_gs_code(self):
        return self.company.gs_code_deringer

    def get_segment_terminator(self):
        return self.company.data.segment_terminator

    def get_container(self):
        if self.car:
            return f"{self.car.car_mark}{self.car.car_number}"
        elif self.ticket:
            return None

    # IT1
    def get_shipment_number(self):
        if self.car:
            return "001_B_001X"
        elif self.ticket:
            return "001_B_001P"

    def get_account_number(self):
        return self.company.account_deringer

    def get_ftp_username(self):
        return self.company.username_ftp_deringer

    def get_ftp_password(self):
        return self.company.password_deringer

    def get_ftp_server(self):
        return self.company.data.ftp_server_url

    def get_ftp_remote_locations(self):
        return self.company.data.ftp_remote_locations

    def get_invoice_description(self):
        # PID05
        if self.car:
            return self.car.product.data.codes.part_description
        elif self.ticket:
            return self.ticket_customs_data["product_commercial_description"]

    def get_carrier(self):
        if self.car:
            return "R"
        elif self.ticket:
            return "M"

    def get_weight_unit(self):
        # MEA04
        return "KG"

    def get_product_hts(self):
        if self.car:
            return self.car.product.data.codes.hts
        elif self.ticket:
            return self.ticket_customs_data["hts_code"]

    def get_units_shipped(self):
        # IT301
        if self.car:
            return self.car.volume_in("BARREL")
        elif self.ticket:
            # volume_data = self.ticket.data["quantity_data"]["volum"]
            # volume_unit = volume_data["volume_unit"].replace("³","3")
            # net_volume = volume_data["transferred_net_volume"]
            # convert_unit(net_volume, volume_unit.upper(), "BARREL")
            # convert_unit(self.ticket.quantity, self.ticket.display_unit, "BARREL")
            return round(float(self.ticket_customs_data["quantity_1"]), 2)

    def get_unit_of_measurment(self):
        # IT302
        return "BR"

    def get_unit_price(self):
        # IT104
        if self.car:
            conversion_factor = self.car.unit_conversion_factor("BARREL", self.volume_unit)
            ratio = convert_unit(1, "BARREL", self.volume_unit, conversion_factor)
        elif self.ticket:

            ratio = 1

        return round(float(self.unit_price) * ratio, 2)


    def get_discount_unit(self):
        if self.car:
            if self.discount is not None:
                conversion_factor = self.car.unit_conversion_factor("BARREL", self.volume_unit)
                ratio = convert_unit(1, "BARREL", self.volume_unit, conversion_factor)
                return round(float(self.discount) * ratio, 2)
        if self.ticket:
            ratio = 1
            return round(float(self.discount) * ratio, 2)

    def get_discount_percent(self):
        if self.discount:
            return (float(self.get_TDS04()) / float(self.get_TDS01())) * 100
        return 0

    def get_TDS01(self):
        # TDS01
        return round(self.get_units_shipped() * self.get_unit_price(), 2)

    def get_TDS02(self):
        if self.get_TDS03() is not None:
            tds02 = float(self.get_TDS01()) - float(self.get_TDS03()) - float(self.get_TDS04())
        else:
            tds02 = float(self.get_TDS01()) - float(self.get_TDS04())

        return round(tds02, 2)

    def get_TDS03(self):
        # if self.get_transport_term() in ["CAF"]:
        #     if self.freight_cost:
        #         return round(float(self.freight_cost) * self.get_units_shipped(), 2)
        #     return 0
        # else:
        #     return None
        if self.freight_cost is not None:
            if self.car:
                return round(float(self.freight_cost) * self.get_units_shipped(), 2)
            if self.ticket:
                return round(float(self.freight_cost),2)
        else:
            return None

    def get_TDS04(self):
        if self.car:
            if self.discount:
                return round(self.get_units_shipped() * self.get_discount_unit(), 2)
        if self.ticket:
            return self.discount

    def get_transport_term(self):
        return self.term_of_transport

    def get_location_qualifier(self):
        tranport_term = self.get_transport_term()
        if tranport_term in ["DAF", "DDP"]:
            return "LB"
        elif tranport_term in ["CAF", "CAI", "CIF", "CPT", "DDP"]:
            return "LI"
        elif tranport_term in ["EXQ", "FAS", "FOB", "FOR", "FOT"]:
            return "LJ"
        elif tranport_term in ["EXW"]:
            return "LC"

    def get_is_hazardous(self):
        if self.car:
            return self.car.product.is_dangerous_goods
        elif self.ticket:
            return bool(self.ticket.product.hazard_class)

    def get_name_of_declarant(self):
        name = ""
        if self.car:
            last_edi = self.car.rail_shipment.data.last_edi
            if last_edi:
                if user := last_edi.get_user():
                    name = f"{user.first_name} {user.last_name}"
        elif self.ticket:
            name = self.ticket_customs_data["responsible_employee"]

        return name

    @staticmethod
    def map_provice(origin):
        map_dict = {
            "AB": "XA",
            "NB": "XB",
            "BC": "XC",
            "MB": "XM",
            "NS": "XN",
            "ON": "XO",
            "PE": "XP",
            "QC": "XQ",
            "SK": "XS",
            "NT": "XT",
            "NU": "XV",
            "NL": "XW",
            "YT": "XY",
        }

        return map_dict.get(origin.upper())

    def clean(self):
        if self.invoice_price_includes not in ["0", "91"]:
            raise ValidationError(f"'Invoice Price Includes' value '{self.get_invoice_price_includes_display()}' not supported")

        if self.term_of_transport in ["DAP", "DAT", "FCA"]:
            raise ValidationError(f"'Term Of Delivery' '{self.term_of_transport}' not supported")

    def save(self, *args, **kwargs):
        self.clean()
        return super().save(*args, **kwargs)

    @staticmethod
    def populate_custom_invoice_from_car(car, strict=True):
        if CustomsInvoice.objects.filter(car=car):
            raise Exception(
                f"Car {car.car_mark}{car.car_number} already has invoice form!"
            )

        company = CustomsCompany.objects.get(
            company=car.current_location.company.company
        )

        shipment = car.rail_shipment
        schedule = shipment.schedule

        if not schedule:
            raise Exception("No schedule pattern was found")

        if strict:
            if not schedule.us_custom_office:
                raise Exception("US custom office is not defined in schedule")

            if schedule.estimated_days_to_border is None:
                raise Exception("Estimate date to border is not defined in schedule")

            if schedule.estimate_freight_charge is None:
                raise Exception("Estimate freight charge is not defined in schedule")

            if not schedule.basis_of_price:
                raise Exception("Base of price is not defined in schedule")

            if not schedule.shipment_method_of_payment:
                raise Exception("Shipment method of payment is not defined in schedule")

        if not car.product.data.codes.hts:
            raise Exception("HTS is not defined")

        contract = schedule.purchase_contract or schedule.sale_contract

        consignee = shipment.ultimate_consignee or shipment.consignee
        country_of_export = CustomsInvoice.map_provice(
            shipment.origin.data.address.state
        )

        if country_of_export is None:
            raise Exception("Origin provice not found")

        customs_invoice = CustomsInvoice(
            status="open",
            company=company,
            consignee=consignee,
            buyer=shipment.importer or consignee,
            exporter=shipment.exporter or shipment.shipper,
            supplier=shipment.exporter or shipment.shipper,
            car=car,
            unit_price=car.invoiceitem_unit_price,
            currency=car.invoiceitem_currency,
            volume_unit=car.invoiceitem_unit,
            discount=car.invoiceitem_discount_value or 0,
            freight_cost=schedule.freight_cost,
            carrier_code=shipment.railroad.name,
            transaction_type_code="SS",
            is_consignee_related_invoice_party=schedule.is_parties_related,
            country_of_export=CustomsInvoice.map_provice(
                shipment.origin.data.address.state
            ),
            packaging="1P",
            number_of_packages=1,
            invoice_price_includes=schedule.invoice_price_includes,
            us_custom_office=schedule.us_custom_office or 3403,
            est_date_reach_border=shipment.ship_date
            + datetime.timedelta(days=schedule.estimated_days_to_border or 5),
            date_of_export=shipment.ship_date,
            date_of_invoice=timezone.now().date(),
            shipment_method_of_payment=schedule.shipment_method_of_payment or "PP",
            estimate_freight_charge=schedule.estimate_freight_charge or 0,
            basis_of_price=schedule.basis_of_price or "CT",
            term_of_sale=schedule.term_of_sale,
            term_of_transport=schedule.term_of_transport,
            tsca_statement=schedule.tsca_statement,
            transportation_method="R"
        )

        customs_invoice.save()
        return customs_invoice

    @staticmethod
    def populate_custom_invoice_from_ticket(ticket, index, strict=True):
        if invoice := CustomsInvoice.objects.filter(ticket=ticket).exclude(status__in=CustomsInvoice.ALLOWED_STATUS_SEND).first():
            raise Exception(
                f"Ticket {ticket} already has pending invoice {invoice.id}!"
            )

        company = CustomsCompany.objects.get(
            company=ticket.dbreplication_client_company.client_company
        )

        customs_data = ticket.data["customs_data"][index]

        if customs_data["status"] != 0:
            raise Exception(
                f"Ticket {ticket} customs status is not WAITING_TO_INVOICE"
            )

        print_invoice_data = customs_data["print_invoice"]
        logistic_data = ticket.data["additional_fields"]["logistics"]
        contract = CrossBorderContract.objects.get(
                pk=customs_data["edi_data"]["cross_border_contract_id"]
            )

        if strict:
            if contract.delivery_terms == "CAF":
                if not print_invoice_data["estimated_freight_to_port"]:
                    raise Exception("CAF requires fireght cost")

            if not customs_data["edi_data"]["port_of_entry"]:
                raise Exception("US custom office is not defined")

            if customs_data["edi_data"]["estimated_arrival"] is None:
                raise Exception("Estimate date to border is not defined")

            if print_invoice_data["estimated_freight_to_port"] is None:
                raise Exception("Estimate freight charge is not defined")

            # Set as contract by default
            # if not customs_data.basis_of_price:
            #     raise Exception("Base of price is not defined in schedule")

            # if not print_invoice_data["shipment_payment_method"]: # defaulted to 'PP'
            #     raise Exception("Shipment method of payment is not defined in schedule")

        if not print_invoice_data["hts_code"]:
            raise Exception("HTS is not defined")

        country_of_export = print_invoice_data["country_of_origin"]

        if country_of_export is None:
            raise Exception("Origin provice not found")

        customs_invoice = CustomsInvoice(
            customs_data_index=index,
            status="open",
            company=company,
            tboss_consignee_id=customs_data["edi_data"].get("consignee_id") or None,
            tboss_buyer_id=contract.buyer.id,
            tboss_exporter_id=contract.exporter.id,
            tboss_supplier_id=contract.supplier.id,
            ticket=ticket,
            unit_price=print_invoice_data["unit_price"],
            currency=contract.currency,
            volume_unit="BARREL", # volume_unit for price
            discount= print_invoice_data["discount_amount"],
            freight_cost=contract.cost_of_freight,
            carrier_code=customs_data["edi_data"]["scac"],
            paps_code=customs_data["edi_data"]["paps"],
            transaction_type_code=contract.transaction_type,
            is_consignee_related_invoice_party=contract.buyer_related,
            country_of_export=print_invoice_data["country_of_origin"],
            packaging="TT",
            number_of_packages=1,
            invoice_price_includes=print_invoice_data["invoice_price_includes"],
            us_custom_office=customs_data["edi_data"]["port_of_entry"],
            est_date_reach_border=convert_to_date(customs_data["edi_data"]["estimated_arrival"]),
            date_of_export=convert_to_date(ticket.created),
            date_of_invoice=convert_to_date(print_invoice_data["date"]),
            shipment_method_of_payment=print_invoice_data["shipment_payment_method"],
            estimate_freight_charge=contract.estimated_freight_to_port,
            basis_of_price="CT",
            term_of_sale=contract.terms,
            term_of_transport=contract.delivery_terms,
            tsca_statement=print_invoice_data["TSCA_statement"],
            transportation_method="M"
        )

        customs_invoice.save()
        return customs_invoice

    @classmethod
    def find_control_number(cls, control_number):
        try:
            return cls.objects.get(
                data__edi__contains=[{"control_number": control_number}]
            )
        except cls.DoesNotExist:
            return None

    def send(self, user):
        invoice = CustomsInvoice.objects.select_related().get(id=self.id)
        self.status_list = self.status_list or []

        try:
            data = edi810_transaction_data_from_invoice(invoice)
            edi = EDI810Deringer(data)
        except Exception as e:
            print("EDI 810 failed to generate", str(e))
            self.status = "edi-process-failed"
            self.status_list.append(["edi process failed", str(e)[:100]])
            self.save(update_fields=["status_list", "status"])
            return

        try:
            self.status = 'sending'
            filename, control_number, out = edi.send_to_ftp()
        except Exception as e:
            print("EDI 810 failed to send", str(e))
            self.status = 'send-failed'
            self.status_list.append(["sent to ftp failed", str(e)[:100]])
            self.save(update_fields=["status_list", "status"])
            return

        self.status = "sent"
        self.status_list.append(["sent to ftp", "success"])

        edi_company = EdiCompany.objects.get(company=self.company.company)

        EdiDocument.objects.create(
            company=edi_company,
            source="Deringer",
            doc_type="810",
            data=out,
            name=filename,
            processed=True,
        )

        edi_810_package = EDI810PackageSchema(
            control_number=control_number, user_email=user.email if user else None, user_id=user.id if user else None
        )
        edi_810_package.edi_810 = EDIDataSchema(
            filename=filename, text=out, result="Sent", date=timezone.now()
        )

        self.data.edi.append(edi_810_package)

        self.save(update_fields=["data", "status", "status_list"])

    @classmethod
    def fetch_997(cls):
        result = EDI997Deringer.fetch_from_ftp()
        for edi in result:
            edi_document = EdiDocument.objects.create(
                company=edi.company.company.edi,
                source="Deringer",
                doc_type="997",
                data=edi.edi_data,
                name=edi.filename,
            )

            control_number = edi.control_number
            invoice = cls.find_control_number(control_number)
            if invoice:
                ticket = invoice.ticket
                if edi.is_validate():
                    invoice.status = "acknowledge"
                    if ticket:
                        ticket.data["customs_data"][invoice.customs_data_index]["status"] = BrokerageStatus.ACCEPTED_BY_BROKERAGE.value
                else:
                    invoice.status = "acknowledge-rejected"
                    if ticket:
                        ticket.data["customs_data"][invoice.customs_data_index]["status"] = BrokerageStatus.REJECTED_BY_BROKERAGE.value

                edi_package = invoice.data.get_edi(control_number)
                if edi_package.edi_997:
                    print(f"expecting empty edi_997 on control number {control_number}")
                    print(edi.edi_data)
                else:
                    edi_package.edi_997 = EDIDataSchema(
                        filename=edi.filename,
                        text=edi.edi_data,
                        result=edi.code,
                        date=timezone.now(),
                    )

                invoice.save(update_fields=["data", "status"])
                ticket.save(update_fields=["data"])

                edi_document.processed = True
                edi_document.save(update_fields=["processed"])

                CustomsInvoice.send_status(invoice, invoice.ticket)

            else:
                edi_document.processed = False
                edi_document.save(update_fields=["processed"])
                print(f"invoice {control_number} not found")

    @staticmethod
    def send_status(invoice, ticket, status=None):
        cc = []
        emails = []
        if ticket:
            base_company = ticket.dbreplication_client_company.client_company
            customs_company = getattr(base_company, "customs")
            if not customs_company:
                return

        context = dict(
            base_url=getattr(settings, "BASE_URL", ""),
            ticket=ticket,
            responsible_person_email_company_wide = (
                customs_company.tboss_responsible_person.email if
                customs_company.tboss_responsible_person else None
            ),
            status=status
        )

        if customs_company.data.distribution_group_email:
            emails.extend(customs_company.data.distribution_group_email)

        if ticket:
            context["url"] = reverse("terminalboss-v3-ticket-detail", args=[ticket.id])
            context["status"] = status or CustomsInvoice.STATUS_TRANSLATION.get(
                ticket.data["customs_data"][-1]["status"],
                ticket.data["customs_data"][-1]["message"]
                )

            if ticket.data["customs_data"]:
                if responsible_email := ticket.data["customs_data"][-1]["print_invoice"]["exporter_email"]:
                    validator = EmailValidator()
                    try:
                        validator(responsible_email)
                        cc.append(responsible_email)
                    except ValidationError:
                        pass

        send_customs_ticket_status_update(emails, context, cc)
