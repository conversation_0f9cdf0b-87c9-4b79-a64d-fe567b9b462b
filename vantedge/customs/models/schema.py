from typing import List, Optional
from datetime import datetime
from pydantic import BaseModel, Field


class EDIDataSchema(BaseModel):
    text: str = None
    filename: str = None
    date: datetime = None
    result: str = None


class EDI810PackageSchema(BaseModel):
    user_email: Optional[str] = None
    user_id: Optional[int] = None
    control_number: int
    edi_810: EDIDataSchema = None
    edi_997: EDIDataSchema = None
    edi_824: EDIDataSchema = None


class CustomsInvoiceDataSchema(BaseModel):
    edi: List[EDI810PackageSchema] = []

    @property
    def last_edi(self):
        return self.edi[-1] if self.edi else None

    def get_edi(self, control_number):
        if not self.edi:
            return None
        return next((i for i in self.edi if i.control_number == control_number), None)
