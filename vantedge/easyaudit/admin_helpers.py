from django.contrib import admin
from django.core.exceptions import PermissionDenied
from django.contrib.auth import get_user_model
from django.shortcuts import render

try: # Django 2.0
    from django.urls import reverse
except: # Django < 2.0
    from django.core.urlresolvers import reverse

from django.utils.translation import gettext_lazy as _
from django.utils.safestring import mark_safe

import json


def prettify_json(json_string):
    """Given a JSON string, it returns it as a
    safe formatted HTML"""
    try:
        data = json.loads(json_string)
        html = '<pre>' + json.dumps(data, sort_keys=True, indent=4) + '</pre>'
    except:
        html = json_string
    return mark_safe(html)


class EasyAuditModelAdmin(admin.ModelAdmin):

    def user_link(self, obj):
        #return mark_safe(get_user_link(obj.user))
        user = obj.user
        if user is None:
            return '-'
        try:
            user_model = get_user_model()
            url = reverse("admin:%s_%s_change" % (
                user_model._meta.app_label,
                user_model._meta.model_name,
            ), args=(user.id,))
            html = '<a href="%s">%s</a>' % (url, str(user))
        except:
            html = str(user)
        return mark_safe(html)
    user_link.short_description = 'user'

    def has_add_permission(self, request, obj=None):
        return False
