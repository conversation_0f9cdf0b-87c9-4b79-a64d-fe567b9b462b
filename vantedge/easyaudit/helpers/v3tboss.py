import re
from django.contrib.contenttypes.models import ContentType
from django.core.exceptions import ObjectDoesNotExist


UUID_PATTERN = re.compile(r"^[\da-f]{8}-([\da-f]{4}-){3}[\da-f]{12}$")
try:
    MODEL_MAPPING = {
        "location": ContentType.objects.get(app_label="vant_tboss", model="location"),
        "product": ContentType.objects.get(app_label="vant_tboss", model="product"),
    }
except Exception as e:
    print(f"Exception in fetching Content Type Models in easy audit: {e}")
    MODEL_MAPPING = {}

def get_entity(client_company, entity_name, path_tuple):
    # TBossCompany str tuple -> ModelInstance
    """
    Return an object of type `entity_name`
    from `path_tuple`.
    """
    index = path_tuple.index(entity_name)

    if len(path_tuple) > index + 1:
        # on the assumption that the identifier will come after the entity-name
        # ('/', 'api', 'external', 'v1', 'location', '<location-id/code>')
        next = index + 1
        entity_identifier = path_tuple[next]

        return get_entity_by_identifier(client_company, entity_name, entity_identifier)

    return None


def get_entity_by_identifier(client_company, model_string, identifier):
    # TBossCompany str -> ModelInstance
    """
    Return the object|instance from `model_string`
    that matches `identifier`.
    """
    model_type = MODEL_MAPPING.get(model_string)

    if model_type:
        model = model_type.model_class()
        return get_instance_by_identifier(client_company, model, identifier)

    return None


def get_instance_by_identifier(client_company, model_class, id_or_code: str):
    # TBossCompany ModelClass str -> ModelInstance
    """
    Return the object|instance from `model_class` that
    matches the given id|code.
    """
    if UUID_PATTERN.match(id_or_code):
        uuid = id_or_code
        return get_instance_by_id(client_company, model_class, uuid)
    else:
        code = id_or_code
        return get_instance_by_code(client_company, model_class, code)


def get_instance_by_id(client_company, model_class, id):
    # TBossCompany ModelClass str -> ModelInstance
    """
    Return the object|instance from `model_class` that
    matches id.
    """
    try:
        return model_class._meta.default_manager.filter(dbreplication_client_company=client_company).get(
            id=id
        )
    except ObjectDoesNotExist:
        return None


def get_instance_by_code(client_company, model_class, code: str):
    # TBossCompany ModelClass str -> ModelInstance
    """
    Return the object|instance from `model_class` that
    matches `code`.
    """
    try:
        return model_class._meta.default_manager.filter(dbreplication_client_company=client_company).get(
            code=code
        )
    except ObjectDoesNotExist:
        return None


def get_terminalboss_company(request):
    # Request -> TBossCompany
    try:
        return request.user.company.terminalboss
    except ObjectDoesNotExist:
        return None

