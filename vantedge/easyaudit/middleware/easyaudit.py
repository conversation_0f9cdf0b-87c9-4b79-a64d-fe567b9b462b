from django.contrib.auth import get_user_model
from django.core.exceptions import RequestDataTooBig
from django.utils import timezone
from ..utils import should_log_url, should_log_exceptions
from ..models import RequestEvent
from vantedge.terminalboss.models import SiteUser
from vant_dbreplication.models import DBReplicationWorker
from rest_framework.authtoken.models import Token

try:
    from threading import local
except ImportError:
    from django.utils._threading_local import local

User = get_user_model()


class MockRequest(object):
    def __init__(self, *args, **kwargs):
        user = kwargs.pop("user", None)
        self.user = user
        super(MockRequest, self).__init__(*args, **kwargs)


_thread_locals = local()


def get_current_request():
    return getattr(_thread_locals, "request", None)


def get_current_user():
    request = get_current_request()
    if request:
        return getattr(request, "user", None)


def set_current_user(user):
    try:
        _thread_locals.request.user = user
    except AttributeError:
        request = MockRequest(user=user)
        _thread_locals.request = request


def clear_request():
    try:
        del _thread_locals.request
    except AttributeError:
        pass


class EasyAuditMiddleware:
    """Makes request available to this app signals."""

    def __init__(self, get_response=None):
        self.get_response = get_response

    def __call__(self, request):
        _thread_locals.request = request
        try:
            _thread_locals.body = request.body
        except RequestDataTooBig:
            _thread_locals.body = (
                f"Large request body with files: {', '.join(request.FILES)}"
            )

        response = self.get_response(request)

        if should_log_url(request):
            user = None

            if request.user:
                if isinstance(request.user, SiteUser):
                    user = None
                elif isinstance(request.user, DBReplicationWorker):
                    user = None
                elif not request.user.is_anonymous:
                    user = request.user

            # if token is provided but is not yet set on request
            # django-ninja authenticates after this middleware executes?
            try:
                if user is None and request.headers.get("Authorization"):
                    token = request.headers.get("Authorization")
                    user_token = Token.objects.get(key=token.replace("Token ", ""))
                    user = user_token.user
            except User.DoesNotExist:
                pass
            except Token.DoesNotExist:
                pass

            data = dict(
                url=request.path,
                method=request.method,
                query_string=request.META["QUERY_STRING"],
                body=_thread_locals.body,
                response=response.content if hasattr(response, "content") else None,
                user=user,
                status=response.status_code,
                remote_ip=request.META["REMOTE_ADDR"],
                datetime=timezone.now()
            )
            RequestEvent.create_async(data)

            _thread_locals.request_logged = True

        try:
            del _thread_locals.request
        except AttributeError:
            pass

        return response

    def process_exception(self, request, exception):
        if (
            not getattr(_thread_locals, "request_logged", False)
            and should_log_exceptions()
        ):
            user = None
            if isinstance(request.user, User):
                user = request.user
            elif request.headers.get("Authorization"):
                # if token is provided but is not yet set on request
                # django-ninja authenticates after this middleware executes?
                try:
                    token = request.headers.get("Authorization")
                    user_token = Token.objects.get(key=token.replace("Token ", ""))
                    user = user_token.user
                except User.DoesNotExist:
                    pass

            data = dict(
                url=request.path,
                method=request.method,
                query_string=request.META["QUERY_STRING"],
                body=_thread_locals.body,
                user=user,
                status=500,
                remote_ip=request.META["REMOTE_ADDR"],
                datetime=timezone.now(),
            )
            RequestEvent.create_async(data)

        try:
            del _thread_locals.request
        except AttributeError:
            pass
        return None
