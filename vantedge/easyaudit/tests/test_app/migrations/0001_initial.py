# -*- coding: utf-8 -*-
# Generated by Django 1.11.2 on 2017-07-25 15:56
from __future__ import unicode_literals

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='TestForeignKey',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=50)),
            ],
        ),
        migrations.CreateModel(
            name='TestM2M',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=50)),
            ],
        ),
        migrations.CreateModel(
            name='TestModel',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(default=b'test data', max_length=50)),
            ],
        ),
        migrations.AddField(
            model_name='testm2m',
            name='test_m2m',
            field=models.ManyToManyField(to='test_app.TestModel'),
        ),
        migrations.AddField(
            model_name='testforeignkey',
            name='test_fk',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='test_app.TestModel'),
        ),
    ]
