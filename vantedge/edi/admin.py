from django.conf import settings
from django.contrib import admin
from django.urls import path, reverse
from django.db.models import <PERSON><PERSON><PERSON><PERSON>
from django.utils.html import format_html
from django_object_actions import DjangoObjectActions, takes_instance_or_queryset
from django_json_widget.widgets import JSONEditorWidget

from vantedge.edi.process import process
from vantedge.util.mixins import AppPermissionsMixin
from .models import *

@admin.register(EdiCompany)
class EdiCompanyAdmin(DjangoObjectActions, AppPermissionsMixin):
    pass


@admin.register(EdiDocument)
class EdiDocumentAdmin(DjangoObjectActions, admin.ModelAdmin):
    list_display = [
        "name",
        "company__name",
        "doc_type",
        "source",
        "processed",
        "created",
    ]
    search_fields = ["company__company__name", "source", "name"]
    changelist_actions = ["delete_all", "clear", "process", "load_cache"]
    actions = ["clear", "poll_clms"]
    change_actions = ["process"]
    list_filter = ["company", "source", "doc_type", "processed"]
    date_hierarchy = "created"

    def get_queryset(self, request):
        return super().get_queryset(request).prefetch_related("company")

    def company__name(self, obj):
        return obj.company.company.name if obj.company else ""

    def delete_all(self, request, queryset):
        EdiDocument.objects.all().delete()

    delete_all.label = "Delete All"
    delete_all.short_description = "Delete all"

    def load_cache(self, request, queryset):
        from vantedge.edi.poll import poll_cache, poll_edi

        poll_cache("railinc", "clm")

    load_cache.label = "Load Cache"
    load_cache.short_description = "Load Cache"

    def clear(self, request, queryset):
        queryset.update(processed=None)

    clear.label = "Clear"
    clear.short_description = "Clear Processed Flag"

    @takes_instance_or_queryset
    def process(self, request, qs):
        for edi_document in qs:
            edi_document.process()

    process.short_description = "Process Document(s)"  # optional


@admin.register(EdiCounter)
class EdiCounterAdmin(admin.ModelAdmin):
    pass


@admin.register(EdiLock)
class EdiLockAdmin(admin.ModelAdmin):
    raw_id_fields = ["locked_document"]

@admin.register(GeoCLM)
class GeoCLMAdmin(admin.ModelAdmin):
    raw_id_fields = ["trip"]
    list_display = ["clm_id","trip", "is_processed", "created"]
    list_filter = ["is_processed"]

    formfield_overrides = {
        JSONField: {"widget": JSONEditorWidget(options={"mode": "form"})}
    }
@admin.register(GeoTrip)
class GeoTripAdmin(admin.ModelAdmin):
    list_display = [
        "trip_identifier",
        "car", "origin",
        "destination",
        "has_waybill",
        "waybill_id",
        "is_updated",
        "created",
        "modified"
    ]
    raw_id_fields = ["waybill"]
    formfield_overrides = {
        JSONField: {"widget": JSONEditorWidget(options={"mode": "form"})}
    }

    def origin(self, obj):
        try:
            return obj.get_origin_city()
        except KeyError:
            return "N/A"

    def destination(self, obj):
        try:
            return obj.get_destination_city()
        except KeyError:
            return "N/A"

    def car(self, obj):
        try:
            return obj.get_car_data()
        except KeyError:
           return "N/A"
