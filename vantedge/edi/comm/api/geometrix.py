from urllib.parse import urljoin
from progressbar import progressbar
import requests

from django.core.cache import cache
from django.db.models import Max
from django.conf import settings

from vantedge.edi.models import EdiCompany, GeoTrip, GeoCLM
from vantedge.trackandtrace.models import Car<PERSON><PERSON>, Waybill, Trip
from vantedge.util.logger.base import send_error

target_splcs = {"Scotford": "083481", "Fort Saskatchewan":"083491", "Northgate":"070333"}

class GeoMetrixApi():
    URL_BASE = "https://data.geometrix.com/api/"
    OAUTH_URL = "https://auth.geometrix.com/oauth2/token"

    def __init__(self, company: EdiCompany,  init_clm_id: int, hour: int = None):
        self.company = company
        self.hour = hour
        self.init_clm_id = init_clm_id

    def get_oauth_params(self):
        return settings.GEO_OAUTH_PARAM

    def _request_access_token(self):
        try:
            return requests.post(self.OAUTH_URL, data=self.get_oauth_params(), timeout=200)
        except Exception as e:
            print(f"Geo access token request failed: {str(e)}")
            return None

    def _get_cache_key_access_token(self):
        cache_key = f"geo_access_token_{self.company.id}"

    def _populate_access_token(self):
        cache_key = self._get_cache_key_access_token()
        response = self._request_access_token()

        if response.status_code == 200:
            accesss_token = response.json().get("access_token")
            expires_in = response.json().get("expires_in")
            cache.set(cache_key, accesss_token, expires_in)
        else:
            print(f"Geo access token returned {response.status_code}")

    def get_access_token(self):
        cache_key = self._get_cache_key_access_token()
        if token := cache.get(cache_key):
            return token

        self._populate_access_token()
        return cache.get(cache_key)

    def _get_trip_hour_param(self):
        return self.hour or 24

    def _get_clm_id_parm(self):
        last_clm_id = GeoCLM.objects.filter(company=self.company).aggregate(max_id=Max("clm_id")).get("max_id")
        if last_clm_id is None:
            return self.init_clm_id
        return last_clm_id

    def _get_clm_url(self):
        last_clm_id = self._get_clm_id_parm()
        url = f"{self.company.company.name.lower()}/biclm2/{last_clm_id}"
        return urljoin(self.URL_BASE, url)

    def _get_trip_url(self):
        hour_param = self._get_trip_hour_param()
        url = f"{self.company.company.name.lower()}/bitrip2/{hour_param}"
        return urljoin(self.URL_BASE, url)

    def _sort_trips(self, data):
        return sorted(data, key=lambda x: x["trip"]["tripInformation"]["lastModified"], reverse=True)

    def get_trip_indentifier(self, data):
        return data["trip"]["tripInformation"]["tripIdentifier"]

    def _remove_duplicate_trips(self, data):
        data = self._sort_trips(data)
        identifiers = []
        result = []
        for trip in data:
            trip_identifier = self.get_trip_indentifier(trip)
            if trip_identifier in identifiers:
                continue
            else:
                result.append(trip)
                identifiers.append(trip_identifier)

        return result

    def _fetch_geo_trips(self):
        hour_param = self._get_trip_hour_param()
        print(f"fetching geo trips of past {hour_param} hours...")
        url = self._get_trip_url()
        access_token = self.get_access_token()

        headers = {"Authorization": f"Bearer {access_token}"}
        try:
            response = requests.get(url, headers=headers, timeout=500)
        except Exception as e:
            print(f"geo fetch trips failed, {str(e)}")
            return []

        if response.status_code != 200:
            print(f"geo fetch trips failed, {response.status_code}")
            return []

        return response.json()

    def _fetch_geo_clms(self):
        print("fetching geo clms...")
        url = self._get_clm_url()
        access_token = self.get_access_token()

        headers = {"Authorization": f"Bearer {access_token}"}
        try:
            response = requests.get(url, headers=headers, timeout=400)
        except Exception as e:
            print(f"geo fetch clms failed, {str(e)}")
            return []

        if response.status_code != 200:
            print(f"geo fetch clms failed, {response.status_code}")
            return []

        return response.json()

    def populate_geo_trip_table(self):
        if not self.get_access_token():
            return

        data = self._fetch_geo_trips()
        # geo return duplicate trip in their api,
        # here we take the last one to omit bulk create error
        data = self._sort_trips(data)
        data = self.sanitize_trips(data)
        GeoTrip.bulk_create_trips(self.company, data)

    def populate_geo_clm_table(self):
        if not self.get_access_token():
            return

        for i in range(10):
            data = self._fetch_geo_clms()
            if data:
                GeoCLM.bulk_create_clms(self.company, data)
            else:
                break

    def _filter_trips_base_on_location(self, data, splcs):
        result = []
        for trip in data:
            trip_information = trip.get("trip", {}).get("tripInformation", {})
            if trip_information.get("destnSPLC") in splcs or trip_information.get("originSPLC") in splcs:
                result.append(trip)

        print(f"{len(result)} trips with given location found")
        return result

    def sanitize_trips(self, data):
        data = self._filter_trips_base_on_location(data, target_splcs.values())
        data = self._remove_duplicate_trips(data)
        return data


def process_trips(limit=None):
    print("processing geo trips ...")
    waybills = []
    trips = []
    count_processed = 0
    count_not_processed = 0
    for trip in progressbar(list(GeoTrip.objects.filter(is_updated=True).order_by("trip_identifier"))[:limit]):
        if not trip.has_waybill:
            waybill = Waybill()
            waybill.company = trip.company.company.track_trace
            data = trip.as_waybill_dataclass()

            if data:
                try:
                    waybill.from_dataclass(data)
                    waybills.append(waybill)

                    if trip.has_waybill is False:
                        update_clm_counts = GeoCLM.objects.filter(
                            trip=trip, is_processed=False
                        ).update(is_processed=None)
                        if update_clm_counts:
                            print(f"{update_clm_counts} geo clms marked to process again for trip {trip.trip_identifier}")

                    waybill.save()
                    waybill.assign_to_shipment_car()
                    trip.waybill = waybill
                    trip.has_waybill = True
                    count_processed += 1
                except Exception as e:
                    print(f"Geo Trip failed to parse, {trip}, {str(e)}")
                    trip.has_waybill = False
                    count_not_processed += 1
            else:
                trip.has_waybill = False
                count_not_processed += 1

        trip.is_updated = False
        trips.append(trip)

    print(f"saving geo trips as waybills... waybills:{count_processed}, skipped:{count_not_processed}")

    GeoTrip.objects.bulk_update(trips, fields=["has_waybill", "waybill", "is_updated"], batch_size=1000)

def active_trips(car_events):
    print("Activating trips....")
    active_events = [{"car": event.car, "last_car_event__cycle_id": event.cycle_id, "company":event.company} for event in car_events]
    for event in progressbar(active_events):
        Trip.objects.filter(**event).update(is_active=True)

def process_clms(limit=None):
    print("processing geo clms ...")
    car_events = []
    clms = []
    count_processed = 0
    count_not_processed = 0
    for clm in progressbar(list(GeoCLM.objects.filter(is_processed=None).order_by("clm_id"))[:limit]):
        car_event = CarEvent()
        car_event.company = clm.company.company.track_trace
        event_data = clm.as_railcar_event_dataclass()

        if event_data:
            try:
                car_event.from_dataclass(None, event_data)
                car_events.append(car_event)
                clm.is_processed = True
                count_processed += 1
            except Exception as e:
                if settings.DEBUG:
                    print(f"Geo CLM failed to parse, {clm}, {str(e)}")
                else:
                    send_error(f"Geo CLM failed to parse, {clm}, {str(e)}")
                clm.is_processed = False
                count_not_processed += 1
        else:
            clm.is_processed = False
            count_not_processed += 1

        clms.append(clm)

    print(f"saving geo data as railcar events...proccessed:{count_processed}, not processed:{count_not_processed}")

    events = CarEvent.objects.bulk_create(car_events, batch_size=1000)
    GeoCLM.objects.bulk_update(clms, fields=["is_processed"], batch_size=1000)
    return events

def get_data(company: EdiCompany, init_clm_id: int, hour: int):
    geo_api = GeoMetrixApi(company=company, init_clm_id=init_clm_id, hour=hour)
    geo_api.populate_geo_trip_table()
    geo_api.populate_geo_clm_table()
    process_trips()
    car_events = process_clms()
    active_trips(car_events)
