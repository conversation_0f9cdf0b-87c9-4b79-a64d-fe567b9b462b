from .edi import <PERSON><PERSON>, SFTPMixin
from .edi_404 import <PERSON>DI404, EDI997, <PERSON>DI824

import datetime

BN_CONFIGS = {
    "production": {
        "server": "bbsftp.bnsf.com",
        "username": "edisVANTEDGE",
        "password": "1Vn5T4G5@",
        "remote_locations": {"404": "/inbound", "997": "/outbound", "824": "/outbound"},
    }
}
BN_CONFIGS["debug"] = {**BN_CONFIGS["production"]}
BN_CONFIGS["debug"]["server"] = "bbsftp-trn.bnsf.com"


class BNEDI(SFTPMixin, EDI):
    configs = BN_CONFIGS

    def post(self, transaction: str, data: str, *args, **kwargs):
        name = kwargs.get("name")
        control_number = kwargs.get("control_number")

        path = self.get_cwd(transaction)
        filename = self.get_filename(name, control_number)
        self.connection.cwd(path)
        with self.connection.open(filename, "w") as rf:
            rf.write(data)

    def gett(self, transaction, interchange_control_number):
        result = None
        self.connection.cwd(f"/out/EDI{transaction}")
        directory_structure = self.connection.listdir_attr()
        for attr in directory_structure:
            with self.connection.open(attr.filename, "r") as f:
                data = f.read().decode(encoding="utf-7")

            print(
                f"Looking for {interchange_control_number} in {attr.filename}:\n{data}"
            )
            if str(interchange_control_number) in data:
                result = data
                self.connection.remove(attr.filename)
                print("Removed", attr.filename, attr)
        return result

    def get_filename(self, name, control_number):
        return f"{name}.404.{control_number}.txt"


class EDI404BN(EDI404):
    def __init__(self, data, **kwargs):
        super().__init__(data)

    def generate_header(self):
        now = datetime.datetime.now()

        isa = f'ISA*00*{" ":10}*00*{" ":10}*ZZ*{"VANTEDGE":15}*02*{"BNSF":15}*{now:%y%m%d}*{now:%H%M}*|*00701*{self.control_number:0>9}*1*P*~'
        gs = f"GS*SR*{self.data.gs_code}*BNSF*{now:%Y%m%d}*{now:%H%M}*{self.control_number}*X*007010"

        return (isa, gs)

    def set_bx_carrier(self):
        self.xml.BX.BX05 = "BNSF"

    def send_to_ftp(self):
        name = self.data.gs_code.upper()
        control_number = self.control_number
        out = self.generate_edi()
        with BNEDI() as edi:
            filename = edi.get_filename(name, control_number)
            edi.post("404", out, control_number=control_number, name=name)
        return filename, self.control_number, out


class EDI997BN(EDI997):
    @classmethod
    def fetch_from_ftp(cls):
        fetched_edi = []
        with BNEDI() as edi:
            for filename, data in edi.fetch_folder(
                transaction="997", delete=True, pattern="bnsfedi.997.*"
            ):
                fetched_edi.append(cls(filename, data))

        return fetched_edi


class EDI824BN(EDI824):
    @classmethod
    def fetch_from_ftp(cls):
        fetched_edi = []
        with BNEDI() as edi:
            for filename, data in edi.fetch_folder(
                transaction="824", delete=True, pattern="bnsfedi.824.*"
            ):
                fetched_edi.append(cls(filename, data))

        return fetched_edi


if __name__ == "__main__":
    with BNEDI("debug") as edi:
        edi.go("clm")
        d = edi.list()
        print(edi.get(d[0]))
