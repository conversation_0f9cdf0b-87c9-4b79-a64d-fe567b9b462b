from .edi import EDI
from .edi_404 import EDI404, EDI997, <PERSON><PERSON>824

import datetime
from django.conf import settings

CP_CONFIGS = {
    "production": {
        "server": "flatcar.cpr.ca",
        "username": "vantedge01",
        "password": "A1_tif29h8n2",
        "remote_locations": {
            "clm": "/vantedge01/fromcpr",
            "404": "/vantedge01/tocpr",
            "997": "/vantedge01/fromcpr",
            "824": "/vantedge01/fromcpr",
        },
    }
}
CP_CONFIGS["debug"] = {**CP_CONFIGS["production"]}
CP_CONFIGS["debug"]["server"] = "speeder.cpr.ca"

debug = settings.DEBUG


class CPEDI(EDI):
    configs = CP_CONFIGS

    def post(self, transaction: str, data: str, *args, **kwargs):
        name = kwargs.get("name")
        control_number = kwargs.get("control_number")
        self.go(transaction)
        filename = self.get_filename(name, control_number)
        self.connection.put(None, filename, contents=data.encode())

    def gett(self, transaction, interchange_control_number):
        result = None
        self.connection.cwd(f"/out/EDIParser{transaction}")
        directory_structure = self.connection.listdir_attr()
        for attr in directory_structure:
            with self.connection.open(attr.filename, "r") as f:
                data = f.read().decode(encoding="utf-7")

            print(
                f"Looking for {interchange_control_number} in {attr.filename}:\n{data}"
            )
            if str(interchange_control_number) in data:
                result = data
                self.connection.remove(attr.filename)
                print("Removed", attr.filename, attr)
        return result

    def get_filename(self, name, control_number):
        return f"{name}.404.{control_number}.txt"


class EDI404CP(EDI404):
    def __init__(self, data, **kwargs):
        super().__init__(data)

    def generate_header(self):
        now = datetime.datetime.now()

        ISA08, GS03 = ("CPRST", "CPRST") if debug else ("CPRSP", "CPRSP")
        isa = f'ISA*00*{" ":10}*00*{" ":10}*ZZ*{self.data.gs_code:15}*ZZ*{ISA08:15}*{now:%y%m%d}*{now:%H%M}*|*00701*{self.control_number:0>9}*1*P*>'
        gs = f"GS*SR*{self.data.gs_code}*{GS03}*{now:%Y%m%d}*{now:%H%M}*{self.control_number}*X*007010"

        return (isa, gs)

    def set_bx_carrier(self):
        self.xml.BX.BX05 = "CPRS"

    def send_to_ftp(self):
        name = self.data.gs_code.upper()
        control_number = self.control_number
        out = self.generate_edi()

        with CPEDI() as edi:
            filename = edi.get_filename(name, control_number)
            edi.post("404", out, control_number=control_number, name=name)
        return filename, self.control_number, out


class EDI997CP(EDI997):
    @classmethod
    def fetch_from_ftp(cls):
        fetched_edi = []
        with CPEDI() as edi:
            for filename, data in edi.fetch_folder(
                transaction="997", delete=True, pattern="997_*"
            ):
                fetched_edi.append(cls(filename, data))

        return fetched_edi


class EDI824CP(EDI824):
    @classmethod
    def fetch_from_ftp(cls):
        fetched_edi = []
        with CPEDI() as edi:
            for filename, data in edi.fetch_folder(
                transaction="824", delete=True, pattern="824_*"
            ):
                fetched_edi.append(cls(filename, data))

        return fetched_edi


if __name__ == "__main__":
    with CPEDI("production") as edi:
        edi.go("clm")
        d = edi.list()
        print(d)
        print(edi.get(d[0]))
