from django.conf import settings
from copy import deepcopy

from .edi import <PERSON><PERSON>, SFTPMixin
from .edi_810 import EDI810, EDI997

from vantedge.customs.models import CustomsCompany


base_url = settings.BASE_URL

environment = "debug" if (
    settings.DEBUG or ("gip" in base_url) or ("plain" in base_url)
    ) else "production"

DERINGER_CONFIGS = {
    "production": {
        "server": "Secure.anderinger.com",
        "username": "",
        "password": "",
        "remote_locations": {"810": "/in", "997": "/out"},
    }
}

DERINGER_CONFIGS["debug"] = {**DERINGER_CONFIGS["production"]}
DERINGER_CONFIGS["debug"]["server"] = "Securetest.anderinger.com"

class DeringerFTPConnection(EDI):
    def __init__(self, config, username, password, **kwargs):
        self.configs = deepcopy(DERINGER_CONFIGS)
        self.configs[config]["username"] = username
        self.configs[config]["password"] = password
        if kwargs.get("server"):
            self.configs[config]["server"] = kwargs.get("server")
        if kwargs.get("remote_locations"):
            self.configs[config]["remote_locations"] = kwargs.get("remote_locations")

        super().__init__(config)

    def post(self, transaction, data, *args, **kwargs):
        filename = kwargs.get("filename")
        self.go(transaction)
        self.connection.put(None, filename, contents=data.encode())

class DeringerFTPConnectionSecure(SFTPMixin, EDI):
    def __init__(self, config, username, password, **kwargs):
        self.configs = deepcopy(DERINGER_CONFIGS)
        self.configs[config]["username"] = username
        self.configs[config]["password"] = password
        if kwargs.get("server"):
            self.configs[config]["server"] = kwargs.get("server")
        if kwargs.get("remote_locations"):
            self.configs[config]["remote_locations"] = kwargs.get("remote_locations")

        super().__init__(config)

class EDI810Deringer(EDI810):
    def send_to_ftp(self):
        out = self.generate_edi()
        filename = self.get_filename()

        if environment == "debug":
            with DeringerFTPConnection(
                config=environment,
                username=self.data.username,
                password=self.data.password,
                server=self.data.server,
                remote_locations=self.data.remote_locations
            ) as edi:
                edi.post("810", out, filename=filename)
        else:
            with DeringerFTPConnectionSecure(
                config=environment,
                username=self.data.username,
                password=self.data.password,
                server=self.data.server,
                remote_locations=self.data.remote_locations
            ) as edi:
                edi.post("810", out, filename=filename)

        print(filename, self.control_number)
        print(out)
        return filename, self.control_number, out

class EDI997Deringer(EDI997):
    @classmethod
    def fetch_from_ftp(cls):
        fetched_edi = []

        for company in CustomsCompany.objects.filter(data__is_active=True):
            if environment == "debug":
                with DeringerFTPConnection(
                    config=environment,
                    username=company.username_ftp_deringer,
                    password=company.password_deringer,
                ) as edi:
                    for filename, data in edi.fetch_folder(transaction="997", delete=True):
                        fetched_edi.append(cls(filename, data, company, company.data.segment_terminator))
            else:
                with DeringerFTPConnection(
                    config=environment,
                    username=company.username_ftp_deringer,
                    password=company.password_deringer,
                ) as edi:
                    for filename, data in edi.fetch_folder(transaction="997", delete=True):
                        fetched_edi.append(cls(filename, data, company, company.data.segment_terminator))

        return fetched_edi
