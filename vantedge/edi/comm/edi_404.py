import re
import time
import string
import datetime
from dataclasses import dataclass, field
from typing import List, Literal, Optional
from xmlrpc.client import <PERSON>ole<PERSON>
from lxml import objectify, etree
from more_itertools import grouper
from lxml.objectify import E, deannotate

from vantedge.edi.xml2edi import EDIConvert
from vantedge.util.units import convert_unit

from .utility import sanitize_string

class RoutingError(Exception):
    pass

CHAR_SET = set(string.ascii_letters + string.digits)
CHAR_SET_ENTITY_NAME = CHAR_SET.union(" -")
CARRIER_CODES = {"BNSF": "BNSF", "CPR": "CPRS", "CN": "CN", "CSXT": "CSXT"}

sanitize_name = lambda s : sanitize_string(s, allowed_chars=CHAR_SET_ENTITY_NAME)

@dataclass
class ContactClass:
    code: Literal["EC", "ED", "SH"]
    name: str
    contact_type: Literal["EM", "FX", "TE"]
    contact_value: str
    contact_type2: Optional[Literal["EM", "FX", "TE"]] = None
    contact_value2: Optional[str] = None


@dataclass
class CarDataClass:
    car_mark: str = None
    car_number: str = None
    seal_numbers: list = None
    weight: float = None
    volume: float = None
    equipment_code: str = None
    packaging_code: str = None


@dataclass
class AddressDataClass:
    name: str
    address1: str
    address2: str = None
    city: str = None
    state: str = None
    postal_code: str = None
    country: Literal["CA", "US"] = None

    def __post_init__(self):
        self.name = sanitize_name(self.name)


@dataclass
class HazardousInformation:
    unit: Literal["TK", "TKR", "C4", "1P"] = "C4"
    lading_quantity: int = 1
    un_code: int = None
    comodity_code: str = None
    measurement_code: Literal["LB", "KG", "TNK"] = "KG"
    weight: int = None
    packing_code: Literal["I", "II", "III"] = None

@dataclass
class HazardousClassification:
    classification: str
    qualifier: str = "P"
    placard_notation: str = None


@dataclass
class HazardousMaterial:
    shipping_name: str
    shipping_name_qualifier: Literal["D", "C", "I"] = "C"


@dataclass
class HazardousCanadianRequirement:
    emergency_plan: str
    emergency_number: str


@dataclass
class HazardousCertificate:
    name: str
    declaration_one: str = "ON BEHALF OF SHIPPER"
    declaration_two: str = None


@dataclass
class Edi404DataClass:
    cars: List[CarDataClass]
    total_weight: int = 0
    is_empty_shipment: Boolean = False
    total_volume: int = None
    volume_unit: str = "LITRE"
    car_type: str = None

    gs_code: str = None
    doc_number: int = None
    reference_number: str = None

    railroad: str = None
    is_correction: Boolean = None
    is_cancellation: Boolean = None

    embargo_number: str = None
    embargo_permit_number: str = None
    customs_barcode_number: str = None

    is_rule11: Boolean = False

    routing: str = None
    product_name: str = None
    product_stcc: str = None

    payment_type: Literal["Prepaid", "Collect", "Rule11"] = "Prepaid"
    billing_type: Literal["Single", "Multiple"] = "Single"

    ship_date: datetime = None
    origin_name: str = None
    origin_address: str = None

    destination_name: str = None
    destination_address: str = None

    importer: str = None
    importer_address: AddressDataClass = None
    importer_contact: ContactClass = None

    beneficial_owner: str = None
    beneficial_owner_address: AddressDataClass = None
    beneficial_owner_contact: ContactClass = None

    account_of: str = None
    account_of_address: AddressDataClass = None
    account_of_contact: ContactClass = None

    account_of_origin: str = None
    account_of_origin_address: AddressDataClass = None
    account_of_origin_contact: ContactClass = None

    account_of_destination: str = None
    account_of_destination_address: AddressDataClass = None
    account_of_destination_contact: ContactClass = None

    shipper: str = None
    shipper_address: AddressDataClass = None
    shipper_contact: ContactClass = None
    shipper_contact_erp: ContactClass = None

    ship_from: str = None
    ship_from_address: AddressDataClass = None
    ship_from_contact: ContactClass = None
    ship_from_contact_erp: ContactClass = None

    consignee: str = None
    consignee_address: AddressDataClass = None
    consignee_contact: ContactClass = None
    consignee_contact_erp: ContactClass = None

    ultimate_consignee: str = None
    ultimate_consignee_address: AddressDataClass = None
    ultimate_consignee_contact: ContactClass = None

    forwarder: str = None
    forwarder_address: AddressDataClass = None
    forwarder_contact: ContactClass = None

    is_export_us: Boolean = False

    customs_broker: str = None
    customs_broker_address: AddressDataClass = None
    customs_broker_contact: ContactClass = None

    customs_broker_us: str = None
    customs_broker_us_address: AddressDataClass = None
    customs_broker_us_contact: ContactClass = None

    is_export_mx: Boolean = False

    customs_broker_mx: str = None
    customs_broker_mx_address: AddressDataClass = None
    customs_broker_mx_contact: ContactClass = None

    care_party: str = None
    care_party_address: AddressDataClass = None
    care_party_contact: ContactClass = None

    monitoring_party: str = None
    monitoring_party_address: AddressDataClass = None
    monitoring_party_contact: ContactClass = None

    payer_party: str = None
    payer_party_address: AddressDataClass = None
    payer_party_contact: ContactClass = None

    bill_party: str = None
    bill_party_address: AddressDataClass = None
    bill_party_contact: ContactClass = None

    notify_party: str = None
    notify_party_address: AddressDataClass = None
    notify_party_contact: ContactClass = None

    notify_party_2: str = None
    notify_party_2_address: AddressDataClass = None
    notify_party_2_contact: ContactClass = None

    pickup_party: str = None
    pickup_party_address: AddressDataClass = None
    pickup_party_contact: ContactClass = None

    is_hazardous: Boolean = False
    hazardous_information: HazardousInformation = None
    hazardous_class: HazardousClassification = None
    hazardous_material: HazardousMaterial = None
    hazardous_certificate: HazardousCertificate = None
    hazardous_canadian_requirement: HazardousCanadianRequirement = None
    hazardous_contact_hm: ContactClass = None
    hazardous_contact_cn: ContactClass = None
    hazardous_contact_mx: ContactClass = None

    customs_entry_type: str = None
    is_parties_related: Boolean = False
    export_declaration_type: str = None
    export_reference_number: str = None

    export_license_number: str = None
    export_status_code: str = None
    mexican_tax_id: str = None

    unspsc: str = None
    unspsc_description: str = None
    mtc: str = None
    mhc: str = None
    mpc: str = None
    mhp: str = None
    mmt: str = None

    currency_code: str = None
    total_value: str = None


def edi404_data_from_shipment(shipment_id):
    from vantedge.wheelhouse.models import RailShipment

    shipment = RailShipment.objects.get(id=shipment_id)

    car_data_list = []
    assert len(shipment.cars.all()) > 0, "No cars in the shipment"
    for car in shipment.cars.all():
        car_data = CarDataClass()
        car_data.car_mark = car.car_mark
        car_data.car_number = car.car_number

        car_data.seal_numbers = getattr(car, "seal_numbers", [])

        car_data.weight = int(car.weight)
        car_data.volume = getattr(car, "volume", None)
        car_data.equipment_code = car.edi_compatible_equipment_code
        car_data.packaging_code = car.edi_compatible_packaging_code

        car_data_list.append(car_data)

    data = Edi404DataClass(cars=car_data_list)
    data.total_weight = int(sum(map(lambda car: int(car.weight), data.cars)))
    data.total_volume = int(
        sum(
            map(
                lambda car: int(car.volume),
                filter(lambda car: car.volume is not None, data.cars),
            )
        )
    )

    data.is_empty_shipment = shipment.is_empty_shipment
    data.car_type = shipment.cars.first().car_type

    data.gs_code = shipment.get_gs_code()
    assert data.gs_code is not None, "gs code not assinged to edi"
    data.doc_number = shipment.id
    data.reference_number = shipment.reference_number[:15]
    data.railroad = shipment.railroad.name

    if shipment.status == "cancellation-open":
        data.is_cancellation = True
    elif "correction" in shipment.status:
        data.is_correction = True

    #N9 section
    if shipment.cars.exclude(embargo_number__isnull=True).exclude(embargo_number__exact='').first():
        data.embargo_number = shipment.cars.exclude(embargo_number__isnull=True).exclude(embargo_number__exact='').first().embargo_number
        data.embargo_permit_number = shipment.cars.exclude(embargo_permit_number__isnull=True).exclude(embargo_permit_number__exact='').first().embargo_permit_number
    else:
        data.embargo_number = None
        data.embargo_permit_number = None

    if shipment.cars.exclude(customs_barcode_number__isnull=True).exclude(customs_barcode_number__exact='').first():
        data.customs_barcode_number = shipment.cars.exclude(customs_barcode_number__isnull=True).exclude(customs_barcode_number__exact='').first().customs_barcode_number
    else:
        data.customs_barcode_number = None

    if data.embargo_number or data.embargo_permit_number or data.customs_barcode_number:
        assert shipment.cars.count() == 1, "shipment has multi car and contains N9 info"

    data.routing = shipment.routing
    data.payment_type = shipment.freight_charges

    data.is_rule11 = data.payment_type == "Rule11"

    # if "/" in data.routing:
    #     if not shipment.accounting_rule_11:
    #         raise RoutingError("Bill payer party is missing in rule 11 shipment")

    if data.payment_type == "Rule11":
        if not shipment.accounting_rule_11:
            raise RoutingError("Bill payer party is missing in rule 11 shipment")
        if "/" not in data.routing:
            raise RoutingError("Routing does not match to freight charges")

    if shipment.billing_type == "Single":
        data.billing_type = "S"
    elif shipment.billing_type == "Multiple":
        data.billing_type = "M"

    data.product_name = shipment.product.data.codes.name
    data.product_stcc = shipment.product.data.codes.stcc
    data.is_hazardous = shipment.product.is_dangerous_goods

    data.ship_date = shipment.ship_date
    data.origin_name = shipment.origin.data.address.city[0:19]
    data.origin_address = shipment.origin.data.address.state
    data.destination_name = shipment.destination.data.address.city[0:30]
    data.destination_address = shipment.destination.data.address.state

    if shipment.importer:
        data.importer = sanitize_name(shipment.importer.data.address.name[:35])
        importer_address = shipment.importer.data.address
        data.importer_address = AddressDataClass(
            name=importer_address.name,
            address1=importer_address.address1,
            city=importer_address.city,
            state=importer_address.state,
            postal_code=importer_address.postal_code,
            country=importer_address.country,
        )

    if shipment.beneficial_owner:
        data.beneficial_owner = sanitize_name(shipment.beneficial_owner.data.address.name[:35])
        beneficial_owner_address = shipment.beneficial_owner.data.address
        data.beneficial_owner_address = AddressDataClass(
            name=beneficial_owner_address.name,
            address1=beneficial_owner_address.address1,
            city=beneficial_owner_address.city,
            state=beneficial_owner_address.state,
            postal_code=beneficial_owner_address.postal_code,
            country=beneficial_owner_address.country,
        )

    if shipment.account_of:
        data.account_of = sanitize_name(shipment.account_of.data.address.name[:35])
        account_of_address = shipment.account_of.data.address
        data.account_of_address = AddressDataClass(
            name=account_of_address.name,
            address1=account_of_address.address1,
            city=account_of_address.city,
            state=account_of_address.state,
            postal_code=account_of_address.postal_code,
            country=account_of_address.country,
        )

    if shipment.account_of_origin:
        data.account_of_origin = sanitize_name(shipment.account_of_origin.data.address.name[:35])
        account_of_origin_address = shipment.account_of_origin.data.address
        data.account_of_origin_address = AddressDataClass(
            name=account_of_origin_address.name,
            address1=account_of_origin_address.address1,
            city=account_of_origin_address.city,
            state=account_of_origin_address.state,
            postal_code=account_of_origin_address.postal_code,
            country=account_of_origin_address.country,
        )

    if shipment.account_of_destination:
        data.account_of_destination = sanitize_name(shipment.account_of_destination.data.address.name[:35])
        account_of_destination_address = shipment.account_of_destination.data.address
        data.account_of_destination_address = AddressDataClass(
            name=account_of_destination_address.name,
            address1=account_of_destination_address.address1,
            city=account_of_destination_address.city,
            state=account_of_destination_address.state,
            postal_code=account_of_destination_address.postal_code,
            country=account_of_destination_address.country,
        )

    data.shipper = sanitize_name(shipment.shipper.data.address.name[:35])
    shipper_addresss = shipment.shipper.data.address
    # data.shipper = shipment.wheelhouse_company.data.address.name[:35]
    # shipper_addresss = shipment.wheelhouse_company.data.address
    data.shipper_address = AddressDataClass(
        name=shipper_addresss.name,
        address1=shipper_addresss.address1,
        city=shipper_addresss.city,
        state=shipper_addresss.state,
        postal_code=shipper_addresss.postal_code,
        country=shipper_addresss.country,
    )

    if shipment.ship_from:
        data.ship_from = sanitize_name(shipment.ship_from.data.address.name[:35])
        ship_from_addresss = shipment.ship_from.data.address
        data.ship_from_address = AddressDataClass(
            name=ship_from_addresss.name,
            address1=ship_from_addresss.address1,
            city=ship_from_addresss.city,
            state=ship_from_addresss.state,
            postal_code=ship_from_addresss.postal_code,
            country=ship_from_addresss.country,
        )

    data.consignee = sanitize_name(shipment.consignee.data.address.name[:35])
    consignee_address = shipment.consignee.data.address
    data.consignee_address = AddressDataClass(
        name=consignee_address.name,
        address1=consignee_address.address1,
        city=consignee_address.city,
        state=consignee_address.state,
        postal_code=consignee_address.postal_code,
        country=consignee_address.country,
    )

    if shipment.ultimate_consignee:
        data.ultimate_consignee = sanitize_name(shipment.ultimate_consignee.data.address.name[:35])
        ultimate_consignee_address = shipment.ultimate_consignee.data.address
        data.ultimate_consignee_address = AddressDataClass(
            name=ultimate_consignee_address.name,
            address1=ultimate_consignee_address.address1,
            city=ultimate_consignee_address.city,
            state=ultimate_consignee_address.state,
            postal_code=ultimate_consignee_address.postal_code,
            country=ultimate_consignee_address.country,
        )

    if shipment.forwarder:
        data.forwarder = sanitize_name(shipment.forwarder.data.address.name[:35])
        forwarder_address = shipment.forwarder.data.address
        data.forwarder_address = AddressDataClass(
            name=forwarder_address.name,
            address1=forwarder_address.address1,
            city=forwarder_address.city,
            state=forwarder_address.state,
            postal_code=forwarder_address.postal_code,
            country=forwarder_address.country,
        )

    if shipment.custom_broker:
        data.customs_broker = sanitize_name(shipment.custom_broker.data.address.name[:35])
        customs_broker_address = shipment.custom_broker.data.address
        data.customs_broker_address = AddressDataClass(
            name=customs_broker_address.name,
            address1=customs_broker_address.address1,
            city=customs_broker_address.city,
            state=customs_broker_address.state,
            postal_code=customs_broker_address.postal_code,
            country=customs_broker_address.country,
        )

    if shipment.custom_broker_us:
        data.customs_broker_us = sanitize_name(shipment.custom_broker_us.data.address.name[:35])
        customs_broker_us_address = shipment.custom_broker_us.data.address
        data.customs_broker_us_address = AddressDataClass(
            name=customs_broker_us_address.name,
            address1=customs_broker_us_address.address1,
            city=customs_broker_us_address.city,
            state=customs_broker_us_address.state,
            postal_code=customs_broker_us_address.postal_code,
            country=customs_broker_us_address.country,
        )

    if shipment.custom_broker_mx:
        data.customs_broker_mx = sanitize_name(shipment.custom_broker_mx.data.address.name[:35])
        customs_broker_mx_address = shipment.custom_broker_mx.data.address
        data.customs_broker_mx_address = AddressDataClass(
            name=customs_broker_mx_address.name,
            address1=customs_broker_mx_address.address1,
            city=customs_broker_mx_address.city,
            state=customs_broker_mx_address.state,
            postal_code=customs_broker_mx_address.postal_code,
            country=customs_broker_mx_address.country,
        )

    if shipment.freight_payer:
        data.payer_party = sanitize_name(shipment.freight_payer.data.address.name[:35])
        payer_party_address = shipment.freight_payer.data.address
        data.payer_party_address = AddressDataClass(
            name=payer_party_address.name,
            address1=payer_party_address.address1,
            city=payer_party_address.city,
            state=payer_party_address.state,
            postal_code=payer_party_address.postal_code,
            country=payer_party_address.country,
        )

    if shipment.accounting_rule_11:
        data.bill_party = sanitize_name(shipment.accounting_rule_11.data.address.name[:35])
        bill_party_address = shipment.accounting_rule_11.data.address
        data.bill_party_address = AddressDataClass(
            name=bill_party_address.name,
            address1=bill_party_address.address1,
            city=bill_party_address.city,
            state=bill_party_address.state,
            postal_code=bill_party_address.postal_code,
            country=bill_party_address.country,
        )

    if shipment.care_party:
        data.care_party = sanitize_name(shipment.care_party.data.address.name[:35])
        care_party_address = shipment.care_party.data.address
        data.care_party_address = AddressDataClass(
            name=care_party_address.name,
            address1=care_party_address.address1,
            city=care_party_address.city,
            state=care_party_address.state,
            postal_code=care_party_address.postal_code,
            country=care_party_address.country,
        )

    if shipment.notify_party:
        data.notify_party = sanitize_name(shipment.notify_party.data.address.name[:35])
        notify_party_address = shipment.notify_party.data.address
        data.notify_party_address = AddressDataClass(
            name=notify_party_address.name,
            address1=notify_party_address.address1,
            city=notify_party_address.city,
            state=notify_party_address.state,
            postal_code=notify_party_address.postal_code,
            country=notify_party_address.country,
        )

    if shipment.notify_party_2:
        if not shipment.notify_party:
            raise Exception("Notify Party 1 is missing")
        data.notify_party_2 = sanitize_name(shipment.notify_party_2.data.address.name[:35])
        notify_party_2_address = shipment.notify_party_2.data.address
        data.notify_party_2_address = AddressDataClass(
            name=notify_party_2_address.name,
            address1=notify_party_2_address.address1,
            city=notify_party_2_address.city,
            state=notify_party_2_address.state,
            postal_code=notify_party_2_address.postal_code,
            country=notify_party_2_address.country,
        )

    if shipment.monitoring_party:
        data.monitoring_party = sanitize_name(shipment.monitoring_party.data.address.name[:35])
        monitoring_party_address = shipment.monitoring_party.data.address
        data.monitoring_party_address = AddressDataClass(
            name=monitoring_party_address.name,
            address1=monitoring_party_address.address1,
            city=monitoring_party_address.city,
            state=monitoring_party_address.state,
            postal_code=monitoring_party_address.postal_code,
            country=monitoring_party_address.country,
        )

    if shipment.pickup_party:
        data.pickup_party = sanitize_name(shipment.pickup_party.data.address.name[:35])
        pickup_party_address = shipment.pickup_party.data.address
        data.pickup_party_address = AddressDataClass(
            name=pickup_party_address.name,
            address1=pickup_party_address.address1,
            city=pickup_party_address.city,
            state=pickup_party_address.state,
            postal_code=pickup_party_address.postal_code,
            country=pickup_party_address.country,
        )

    if data.is_hazardous:
        shipper_contact_erp = shipment.shipper.data.erp
        if not shipper_contact_erp.ccn or not shipper_contact_erp.erap:
            raise Exception("Shipper's Emergency response plan is not complete")

        # data.shipper_contact_erp = ContactClass(
        #     code="EC",
        #     name=data.shipper,
        #     contact_type="TE",
        #     contact_value=shipper_contact_erp["erap_telephone"],
        # )

        # consignee_contact_erp = shipment.consignee.data.erp
        # data.consignee_contact_erp = ContactClass(
        #     code="ED",
        #     name=data.consignee,
        #     contact_type="TE",
        #     contact_value=consignee_contact_erp["erap_telephone"],
        # )

        # shipper_contact_erp = shipment.wheelhouse_company.data.erp

        data.hazardous_information = HazardousInformation(
            unit=shipment.product.data.codes.unit,
            lading_quantity=shipment.cars.count(),
            un_code=shipment.product.data.codes.un_code,
            measurement_code="KG",
            weight=int(shipment.weight),
            packing_code=shipment.product.data.codes.packing_group,
            comodity_code=shipment.product.data.codes.stcc,
        )

        data.hazardous_class = HazardousClassification(
            classification=shipment.product.data.codes.hazmat_class,
            placard_notation=shipment.product.data.codes.placared,
        )

        data.hazardous_canadian_requirement = HazardousCanadianRequirement(
            emergency_plan=shipper_contact_erp.erap,
            emergency_number=shipper_contact_erp.erap_telephone,
        )

        if shipper_contact_erp.ccn:
            data.hazardous_contact_hm = ContactClass(
                code="HM",
                name=shipper_contact_erp.ccn,
                contact_type="TE",
                contact_value=shipper_contact_erp.ccn_telephone,
            )

        if shipper_contact_erp.canutec:
            data.hazardous_contact_cn = ContactClass(
                code="CN",
                name=shipper_contact_erp.canutec,
                contact_type="TE",
                contact_value=shipper_contact_erp.canutec_telephone,
            )

        if shipper_contact_erp.mexican_cn:
            data.hazardous_contact_mx = ContactClass(
                code="MX",
                name=shipper_contact_erp.mexican_cn,
                contact_type="TE",
                contact_value=shipper_contact_erp.mexican_cn_telephone,
                contact_type2="EM",
                contact_value2=shipper_contact_erp.mexican_cn_email,
            )

        data.hazardous_material = HazardousMaterial(
            shipping_name=shipment.product.data.codes.name
        )

        hazardous_certificate = shipment.origin.data.cert

        if shipment.hazardous_certificate_person:
            data.hazardous_certificate = HazardousCertificate(
                name=shipment.hazardous_certificate_person,
                declaration_one=hazardous_certificate.declaration_one,
                declaration_two=hazardous_certificate.declaration_two,
            )
        else:
            data.hazardous_certificate = None

        if data.is_hazardous and not data.hazardous_certificate:
            raise Exception("Hazmat Certificate is missing")

    if shipment.custom_broker_us:
        data.is_export_us = True

    if shipment.custom_broker_mx:
        data.is_export_mx = True

    if data.is_export_mx or data.is_export_us:
        data.customs_entry_type = shipment.schedule.customs_entry_type
        data.is_parties_related = "Y" if shipment.schedule.is_parties_related else "N"
        data.export_declaration_type = shipment.schedule.export_declaration_type
        data.export_reference_number = (
            shipment.schedule.export_reference_number
            if data.export_declaration_type != "NDR"
            else "NO DECLARATION IS REQUIRED"
        )
        data.export_license_number = (
            shipment.shipper.data.extra_data.export_license_number
        )
        data.export_status_code = shipment.shipper.data.extra_data.export_status_code

        if data.is_export_mx and not data.export_status_code:
            raise Exception("Shipper's export status code is not selected")

        if not shipment.is_empty_shipment and data.is_export_mx:
            data.mexican_tax_id = shipment.importer and shipment.importer.data.extra_data.mexican_tax_id
            if not data.mexican_tax_id:
                raise Exception("Importer OR Importer Mexican Tax ID is missing")

        data.currency_code = shipment.contract.currency
        if not (data.currency_code and shipment.contract.volume_unit_price):
            # raise Exception("Pricing data is not in contract")
            pass
        else:
            if shipment.contract.volume_unit == "SHORT TON":
                data.total_value = round(
                    convert_unit(data.total_weight, "KG", "SHORT TON") *
                    float(shipment.contract.volume_unit_price), 2
                )
            else:
                data.total_value = round(
                    convert_unit(data.total_volume, "LITRE", shipment.contract.volume_unit) *
                    float(shipment.contract.volume_unit_price), 2
                )

    # Lets customer chose what to send, not blocking the customer
    if data.is_export_mx:
        data.unspsc = shipment.product.data.codes.unspsc
        data.unspsc_description = shipment.product.data.codes.unspsc_description
        data.mtc = shipment.product.data.codes.mtc
        data.mhc = shipment.product.data.codes.mhc
        data.mpc = shipment.product.data.codes.mpc
        data.mhp = shipment.product.data.codes.mhp
        data.mmt = shipment.product.data.codes.mmt

        export_mx_errors = ""

        if not (
            data.is_export_us
            # and data.customs_entry_type
            # and data.export_declaration_type
            # and data.export_reference_number
            # and data.export_license_number
            # and data.export_status_code
        ):
            export_mx_errors += "Transborder shipment customs data is not complete <br/>"

        if not data.unspsc:
            export_mx_errors += "Product UNSPSC is missing <br/>"

        if not data.unspsc_description:
            export_mx_errors += "Product UNSPSC description is missing <br/>"

        if not data.mtc:
            export_mx_errors += "Product Mexico Tariff Rate Code is missing <br/>"

        if not data.mhp:
            export_mx_errors += "Product Mexico Hazardous Packaging Code is missing <br/>"

        if not data.mpc:
            export_mx_errors += "Product Mexico Unit Packaging Code is missing <br/>"

        if not data.mmt:
            export_mx_errors += "Product Mexico Material Type is missing <br/>"

        if data.is_hazardous and not data.mhc:
            export_mx_errors += "Product SAT is missing <br/>"

        if export_mx_errors:
            raise Exception(export_mx_errors)

    if (
        data.customs_entry_type
        or data.export_declaration_type
        or data.export_reference_number
    ):
        if not data.is_export_us:
            raise Exception(
                "Transborder shipment data provided but there is no US customs"
            )

    return data


class EDI404(object):
    def __init__(
        self, data: Edi404DataClass, template_path="vantedge/edi/xml/templates/404.xml"
    ):
        self.template_path = template_path
        self.data = data
        self._control_number = None
        self.xml = None

    def edi404_base(self):
        with open(self.template_path) as f:
            self.xml = objectify.fromstring(f.read())

    def get_st_number(self):
        return f"{self.data.doc_number:0>9}"

    def set_st(self):
        self.xml.ST.ST02 = self.get_st_number()

    def set_payment_type(self):
        if self.data.payment_type == "Rule11":
            self.xml.BX.BX03 = "11"
        elif self.data.payment_type == "Prepaid":
            self.xml.BX.BX03 = "PP"
        elif self.data.payment_type == "Collect":
            self.xml.BX.BX03 = "CC"
        elif self.data.payment_type == "Non-Revenue":
            self.xml.BX.BX03 = "NR"

        if (
            self.data.is_export_mx or self.data.is_export_us
        ) and self.data.export_declaration_type:
            if self.data.export_declaration_type in ("CAED", "G7", "B13A"):
                self.xml.BX.BX11 = "14"
            elif self.data.export_declaration_type == "Summary Reporting":
                self.xml.BX.BX11 = "16"
            elif self.data.export_declaration_type == "NDR":
                self.xml.BX.BX11 = "30"

            self.xml.BX.BX13 = "2"

    def generate_zc1(self):
        change_code = "CA" if self.data.is_cancellation else "CO"

        first_car = self.data.cars[0]

        ZC1 = E.ZC1(
            E.ZC102(first_car.car_mark),
            E.ZC103(first_car.car_number),
            E.ZC104(self.data.reference_number),
            E.ZC105(self.data.ship_date.strftime("%Y%m%d")),
            E.ZC106(change_code),
            E.ZC107(CARRIER_CODES[self.data.railroad]),
            E.ZC108("R"),
        )

        return ZC1

    def set_bx_carrier(self):
        pass

    def edi404_route(self):
        result = []
        if not self.data.routing:
            raise RoutingError(self.data.routing, "No route specified.")
        if "/" not in self.data.routing:
            result = [E.R2(E.R201(self.data.routing), E.R202("S"))]
        else:
            parsed_route = self.data.routing.split("/")
            if len(parsed_route) % 2 != 1:
                raise RoutingError(
                    self.data.routing, "Invalid route.  Need RR/INTCHG/RR/INTCHG/RR...."
                )
            parsed_route = grouper(parsed_route, 2, incomplete="fill", fillvalue="")

            for n, (rr, intchg) in enumerate(parsed_route):
                if self.data.is_rule11:
                    n = "R" if n == 0 else n
                else:
                    n = "S" if n == 0 else n
                if intchg:
                    result.append(E.R2(E.R201(rr), E.R202(n), E.R203(intchg)))
                else:
                    result.append(E.R2(E.R201(rr), E.R202(n)))

        return result

    def edi404_car(self, car):
        result = E.N7(
            E.N701(car.car_mark),
            E.N702(car.car_number),
            E.N703(car.weight),
            E.N704("N"),
            E.N711(car.equipment_code),
        )

        if car.seal_numbers:
            M7 = E.M7()
            for i, v in enumerate(car.seal_numbers):
                setattr(M7, f"M70{i+1}", v)
            result = E.N7Loop1(result, M7)

        if self.data.is_export_mx:
            REF = E.REF(
                E.REF01("UNC"),
                E.REF02(self.data.unspsc)
            )
            result = E.Loop(result, REF)

            N9 = E.N9(
                E.N901("MTC"),
                E.N902(self.data.mtc)
            )
            result = E.Loop(result, N9)

            if self.data.is_hazardous:
                N9H = E.N9(
                E.N901("MHC"),
                E.N902(self.data.mhc)
            )
                result = E.Loop(result, N9H)

            N9MPC = E.N9(
                E.N901("MPC"),
                E.N902(self.data.mpc)
            )
            result = E.Loop(result, N9MPC)

            N9MHP = E.N9(
                E.N901("MHP"),
                E.N902(self.data.mhp)
            )

            result = E.Loop(result, N9MHP)

            N9MMT = E.N9(
                E.N901("MMT"),
                E.N902(self.data.mmt)
            )

            result = E.Loop(result, N9MMT)

            N10 = E.N10(
                E.N1001("1"),
                E.N1002(self.data.unspsc_description[:45]),
                E.N1007("K"),
                E.N1008(car.weight),
                E.N1010(car.packaging_code)
            )
            result = E.Loop(result, N10)

            if len(self.data.unspsc_description) > 45:
                N10B = E.N10(
                    E.N1002(self.data.unspsc_description[45:]),
                )
                result = E.Loop(result, N10B)

        return result

    def edi404_product(self):

        L5 = E.L5(
            E.L501(1),
            E.L502("RESIDUE" if self.data.is_empty_shipment else self.data.product_name[:50]),
            E.L503(self.data.product_stcc),
            E.L504("T"),
        )
        # L0 = E.L0(
        #     E.L001(1),
        #     E.L006(car.volume),
        #     E.L007("V"),
        #     E.L008(len(self.data.cars)),
        #     E.L009("CLD"),
        # )
        # Loop1 = E.Loop1(L5, L0)

        if self.data.is_empty_shipment:
            L5 = E.Loop(
                L5,
                E.L5(
                    E.L501(1),
                    E.L502("LAST CONTAINED")
                    )
            )

        return L5

    def generate_address(self, address):
        N3 = E.N3()
        if not address.address1:
            raise Exception("Primary address is missing")

        N3.append(E.N301(address.address1[:55]))

        if address.address2:
            N3.append(E.N302(address.address2[:55]))

        N4 = E.N4()
        if address.city:
            N4.append(E.N401(address.city[:30]))

        if address.state:
            N4.append(E.N402(address.state))

        if address.postal_code:
            N4.append(E.N403(address.postal_code[:15]))

        if address.country:
            N4.append(E.N404(address.country))

        if N4.getchildren():
            return E.LoopAddress(N3, N4)

        return N3

    def generate_contact(self, contact):
        PER = E.PER(
            E.PER01(contact.code),
            E.PER02(contact.name[:35]),
            E.PER03(contact.contact_type),
            E.PER04(contact.contact_value[:80]),
        )

        if contact.contact_value2:
            PER.append(E.PER05(contact.contact_type2))
            PER.append(E.PER06(contact.contact_value2[:80]))

        return PER

    def generate_hazardous_information(self, hazardous_information):
        LH1 = E.LH1(
            E.LH101(hazardous_information.unit),
            E.LH102(hazardous_information.lading_quantity),
            E.LH103(hazardous_information.un_code),
            E.LH105(hazardous_information.comodity_code),
            E.LH106(hazardous_information.measurement_code),
            E.LH107(hazardous_information.weight),
        )
        if self.data.is_empty_shipment:
            if self.data.car_type == "tank car":
                LH1.append(E.LH109("R"))
            else:
                LH1.append(E.LH109("P"))

        LH1.append(E.LH110(hazardous_information.packing_code))

        return LH1

    def generate_hazardous_classification(self, hazardous_class):
        LH2 = E.LH2(
            E.LH201(hazardous_class.classification), E.LH202(hazardous_class.qualifier)
        )
        if hazardous_class.placard_notation:
            LH2.append(E.LH203(hazardous_class.placard_notation))

        return LH2

    def generate_hazardous_meterial(self, hazardous_material):
        shipping_name_list = hazardous_material.shipping_name.split(" ")
        shipping_name_chunks = []
        for item in shipping_name_list:
            if len(item) > 25:
                raise Exception("not possible to generate LH3, check your product compatible name")

            is_not_empty = bool(len(shipping_name_chunks))

            last_chunk = shipping_name_chunks[-1] if is_not_empty else ""
            if len(last_chunk + " " + item) < 26:
                if is_not_empty:
                    shipping_name_chunks[-1] = last_chunk + " " + item
                else:
                    shipping_name_chunks.append(item)
            else:
                shipping_name_chunks.append(item)

        result = E.LH3(
            E.LH301(shipping_name_chunks[0]),
            E.LH302(hazardous_material.shipping_name_qualifier),
        )

        for chunk in shipping_name_chunks[1:]:
            result = E.Loop(result, E.LH3(
                E.LH301(chunk),
                E.LH302(hazardous_material.shipping_name_qualifier),
            ))

        return result

    def generate_hazardous_canadian_requirement(self, hazardous_canadian_requirement):
        LH4 = E.LH4(
            E.LH401(hazardous_canadian_requirement.emergency_plan),
            E.LH402(hazardous_canadian_requirement.emergency_number),
        )
        return LH4

    def generate_hazardous_certificate(self, hazardous_certificate):
        LH6 = E.LH6(E.LH601(hazardous_certificate.name))
        if hazardous_certificate.declaration_one:
            LH6.LH602 = "1"
            LH6.LH603 = hazardous_certificate.declaration_one
        if hazardous_certificate.declaration_two:
            LH6.LH604 = hazardous_certificate.declaration_two
        return LH6

    def generate_body(self):
        self.edi404_base()
        self.set_st()

        if self.data.is_cancellation or self.data.is_correction:
            self.xml.ST.addnext(self.generate_zc1())

        self.set_payment_type()
        self.set_bx_carrier()

        self.xml.BNX.BNX03 = self.data.billing_type

        if self.data.billing_type == "M":
            self.xml.BX.BX07 = "M"

        if self.data.total_weight == 0:
            # (E = empty equipment billing (non-revenue))
            self.xml.BX.BX07 = "E"

        self.xml.N9.N904 = self.xml.M3.M302 = self.data.ship_date.strftime("%Y%m%d")
        self.xml.N9.N905 = self.xml.M3.M303 = self.data.ship_date.strftime("%H%M")
        self.xml.N9.N902 = self.data.reference_number

        for N7 in [self.edi404_car(car) for car in self.data.cars][::-1]:
            self.xml.N9.addnext(N7)

        # Additional N9 segments
        if self.data.embargo_number:
            self.xml.M3.addnext(E.N9(E.N901("EN"), E.N902(self.data.embargo_number)))

        if self.data.embargo_permit_number:
            self.xml.M3.addnext(
                E.N9(E.N901("EB"), E.N902(self.data.embargo_permit_number))
            )

        if self.data.customs_barcode_number:
            self.xml.M3.addnext(
                E.N9(E.N901("09"), E.N902(self.data.customs_barcode_number))
            )

        if self.data.is_export_mx and self.data.export_reference_number:
            self.xml.M3.addnext(
                E.N9(E.N901("ED"), E.N902(self.data.export_reference_number))
            )
        # End of additional N9 segments

        if self.data.is_export_mx and self.data.customs_entry_type:
            self.xml.M12.M1201 = self.data.customs_entry_type
        else:
            self.xml.remove(self.xml.M12)

        self.xml.F9.F902 = self.data.origin_name
        self.xml.F9.F903 = self.data.origin_address
        self.xml.D9.D902 = self.data.destination_name
        self.xml.D9.D903 = self.data.destination_address

        # Account of
        if self.data.importer:
            self.xml.append(E.N1(E.N101("IM"), E.N102(self.data.importer)))
            self.xml.append(self.generate_address(self.data.importer_address))
            if self.data.importer_contact:
                self.xml.append(self.generate_contact(self.data.importer_contact))

            if self.data.mexican_tax_id:
                self.xml.append(E.REF(E.REF01("TJ"), E.REF02(self.data.mexican_tax_id)))

        # Account of
        if self.data.account_of:
            self.xml.append(E.N1(E.N101("AO"), E.N102(self.data.account_of)))
            self.xml.append(self.generate_address(self.data.account_of_address))
            if self.data.account_of_contact:
                self.xml.append(self.generate_contact(self.data.account_of_contact))

        # Account of origin
        if self.data.account_of_origin:
            self.xml.append(E.N1(E.N101("AP"), E.N102(self.data.account_of_origin)))
            self.xml.append(self.generate_address(self.data.account_of_origin_address))
            if self.data.account_of_origin_contact:
                self.xml.append(self.generate_contact(self.data.account_of_origin_contact))

        # Account of destination
        if self.data.account_of_destination:
            self.xml.append(E.N1(E.N101("AQ"), E.N102(self.data.account_of_destination)))
            self.xml.append(self.generate_address(self.data.account_of_destination_address))
            if self.data.account_of_contact:
                self.xml.append(self.generate_contact(self.data.account_of_destination_contact))

        # Beneficial Owner
        if self.data.beneficial_owner:
            self.xml.append(E.N1(E.N101("BN"), E.N102(self.data.beneficial_owner)))
            self.xml.append(self.generate_address(self.data.beneficial_owner_address))
            if self.data.beneficial_owner_contact:
                self.xml.append(
                    self.generate_contact(self.data.beneficial_owner_contact)
                )

        # Shipper
        self.xml.append(E.N1(E.N101("SH"), E.N102(self.data.shipper)))
        self.xml.append(self.generate_address(self.data.shipper_address))
        if self.data.shipper_contact:
            self.xml.append(self.generate_contact(self.data.shipper_contact))

        # Ship_from
        if self.data.ship_from:
            self.xml.append(E.N1(E.N101("SF"), E.N102(self.data.ship_from)))
            self.xml.append(self.generate_address(self.data.ship_from_address))
            if self.data.ship_from_contact:
                self.xml.append(self.generate_contact(self.data.ship_from_contact))

        # Consignee
        self.xml.append(E.N1(E.N101("CN"), E.N102(self.data.consignee)))
        self.xml.append(self.generate_address(self.data.consignee_address))
        if self.data.consignee_contact:
            self.xml.append(self.generate_contact(self.data.consignee_contact))

        # Ultimate Consignee
        if self.data.ultimate_consignee:
            self.xml.append(E.N1(E.N101("UC"), E.N102(self.data.ultimate_consignee)))
            self.xml.append(self.generate_address(self.data.ultimate_consignee_address))
            if self.data.ultimate_consignee_contact:
                self.xml.append(
                    self.generate_contact(self.data.ultimate_consignee_contact)
                )

        # Forwarder
        if self.data.forwarder:
            self.xml.append(E.N1(E.N101("FW"), E.N102(self.data.forwarder)))
            self.xml.append(self.generate_address(self.data.forwarder_address))
            if self.data.forwarder_contact:
                self.xml.append(
                    self.generate_contact(self.data.forwarder_contact)
                )

        # Customers Broker
        if self.data.customs_broker:
            self.xml.append(E.N1(E.N101("XQ"), E.N102(self.data.customs_broker)))
            self.xml.append(self.generate_address(self.data.customs_broker_address))
            if self.data.customs_broker_contact:
                self.xml.append(self.generate_contact(self.data.customs_broker_contact))

        # US Customers Broker
        if self.data.customs_broker_us:
            self.xml.append(E.N1(E.N101("XU"), E.N102(self.data.customs_broker_us)))
            self.xml.append(self.generate_address(self.data.customs_broker_us_address))
            if self.data.customs_broker_us_contact:
                self.xml.append(
                    self.generate_contact(self.data.customs_broker_us_contact)
                )

        # Mexican Customers Broker
        if self.data.customs_broker_mx:
            self.xml.append(E.N1(E.N101("XR"), E.N102(self.data.customs_broker_mx)))
            self.xml.append(self.generate_address(self.data.customs_broker_mx_address))
            if self.data.customs_broker_mx_contact:
                self.xml.append(
                    self.generate_contact(self.data.customs_broker_mx_contact)
                )

        # Care Party
        if self.data.care_party:
            self.xml.append(E.N1(E.N101("C1"), E.N102(self.data.care_party)))
            self.xml.append(self.generate_address(self.data.care_party_address))
            if self.data.care_party_contact:
                self.xml.append(self.generate_contact(self.data.care_party_contact))

        # payer Party
        if self.data.payer_party:
            self.xml.append(E.N1(E.N101("PF"), E.N102(self.data.payer_party)))
            self.xml.append(self.generate_address(self.data.payer_party_address))
            if self.data.payer_party_contact:
                self.xml.append(self.generate_contact(self.data.payer_party_contact))

        # Notify Party
        if self.data.notify_party:
            self.xml.append(E.N1(E.N101("N1"), E.N102(self.data.notify_party)))
            self.xml.append(self.generate_address(self.data.notify_party_address))
            if self.data.notify_party_contact:
                self.xml.append(self.generate_contact(self.data.notify_party_contact))

        if self.data.notify_party_2:
            self.xml.append(E.N1(E.N101("N2"), E.N102(self.data.notify_party_2)))
            self.xml.append(self.generate_address(self.data.notify_party_2_address))
            if self.data.notify_party_2_contact:
                self.xml.append(self.generate_contact(self.data.notify_party_2_contact))

        # Monitoring Party
        if self.data.monitoring_party:
            self.xml.append(E.N1(E.N101("ZS"), E.N102(self.data.monitoring_party)))
            self.xml.append(self.generate_address(self.data.monitoring_party_address))
            if self.data.monitoring_party_contact:
                self.xml.append(
                    self.generate_contact(self.data.monitoring_party_contact)
                )

        # Pickup Party
        if self.data.pickup_party:
            self.xml.append(E.N1(E.N101("PU"), E.N102(self.data.pickup_party)))
            self.xml.append(self.generate_address(self.data.pickup_party_address))
            if self.data.pickup_party_contact:
                self.xml.append(self.generate_contact(self.data.pickup_party_contact))

        # Bill of Party
        if self.data.bill_party:
            self.xml.append(E.N1(E.N101("11"), E.N102(self.data.bill_party)))
            self.xml.append(self.generate_address(self.data.bill_party_address))
            if self.data.bill_party_contact:
                self.xml.append(self.generate_contact(self.data.bill_party_contact))

        for R2 in self.edi404_route():
            self.xml.append(R2)

        LX = E.LX(E.LX01(1))
        self.xml.append(LX)

        self.xml.append(self.edi404_product())
        self.xml.append(
            E.L0(
                E.L001(1),
                E.L004(self.data.total_weight),
                E.L005("N"),
                E.L008(len(self.data.cars)),
                E.L009(self.data.cars[0].packaging_code),
                E.L011("K"),
            )
        )

        # self.xml.append(
        #     E.L0(
        #         E.L001(1),
        #         E.L006(total_volume),
        #         E.L007("V"),
        #         E.L008(len(self.data.cars)),
        #         E.L009("CLD"),
        #     )
        # )

        if (
            self.data.is_export_mx
            and self.data.export_license_number
            and self.data.total_value
        ):
            self.xml.append(
                E.X1(
                    E.X102(self.data.export_license_number),
                    E.X103(self.data.export_status_code),
                    E.X110(len(self.data.cars)),
                    E.X111(self.data.total_value)
                )
            )

        if self.data.is_hazardous:
            self.xml.append(E.LS(E.LS01("800")))
            self.xml.append(
                self.generate_hazardous_information(self.data.hazardous_information)
            )

            self.xml.append(
                self.generate_hazardous_classification(self.data.hazardous_class)
            )

            self.xml.append(
                self.generate_hazardous_meterial(self.data.hazardous_material)
            )

            if self.data.hazardous_canadian_requirement:
                self.xml.append(
                    self.generate_hazardous_canadian_requirement(
                        self.data.hazardous_canadian_requirement
                    )
                )

            if self.data.hazardous_contact_hm:
                self.xml.append(self.generate_contact(self.data.hazardous_contact_hm))

            if self.data.hazardous_contact_cn:
                self.xml.append(self.generate_contact(self.data.hazardous_contact_cn))

            if self.data.is_export_mx and self.data.hazardous_contact_mx:
                self.xml.append(self.generate_contact(self.data.hazardous_contact_mx))

            self.xml.append(E.LE(E.LE01("800")))

            if self.data.hazardous_certificate:
                self.xml.append(
                    self.generate_hazardous_certificate(self.data.hazardous_certificate)
                )

        if (
            self.data.is_export_mx
            and self.data.export_license_number
            and self.data.total_value
        ):
            self.xml.append(
                E.XH(
                    E.XH01(self.data.currency_code),
                    E.XH02(self.data.is_parties_related),
                    E.XH03("595"),
                )
            )

        deannotate(self.xml, xsi_nil=True)

    @property
    def control_number(self):
        if self._control_number is None:
            self._control_number = int(str((time.time() / 3) + self.data.doc_number).replace(".", "")[1:10])

        return self._control_number

    def generate_header(self):
        now = datetime.datetime.now()

        isa = f'ISA*00*{" ":10}*00*{" ":10}*ZZ*{"VANTEDGE":15}*02*{"CN":15}*{now:%y%m%d}*{now:%H%M}*|*00701*{self.control_number:0>9}*1*P*>'
        gs = f"GS*SR*{self.data.gs_code}*CN*{now:%Y%m%d}*{now:%H%M}*{self.control_number}*X*007010"

        return (isa, gs)

    def generate_footer(self, txt_data):
        segment_cnt = txt_data.strip().count("\n") + 2
        se = f"SE*{segment_cnt}*{self.get_st_number()}"
        ge = f"GE*1*{self.control_number}"
        iea = f"IEA*1*{self.control_number:0>9}"

        return (se, ge, iea)

    def convert_to_txt(self):
        return EDIConvert(self.xml, 404).edi.strip()

    def generate_edi(self):
        self.generate_body()
        body = self.convert_to_txt()
        header = self.generate_header()
        footer = self.generate_footer(body)

        data = []
        data.extend(header)
        data.extend([body])
        data.extend(footer)

        result = "\n".join(data).upper()
        return result + "\n"

    def dump_xml(self):
        return etree.tostring(self.xml).decode("utf-7")

    def send_to_ftp(self):
        raise NotImplementedError


class EDI997(object):
    AK5_CODES = {
        "A": "Accepted",
        "E": "Accepted with Errors noted",
        "M": "Rejected; message authentication",
        "P": "Partially accepted",
        "R": "Rejected",
        "W": "Rejected",
        "X": "Rejected",
    }

    def __init__(self, filename, edi_data):
        self.filename = filename
        self.edi_data = edi_data

    def ak5_status(self):
        return re.findall(".*AK5\*(\w{1}).*", self.edi_data)[0]

    def is_validate(self):
        if "AK5*A" in self.edi_data:
            return True
        else:
            return False

    @property
    def control_number(self):
        return int(re.findall("AK1\*(.*)", self.edi_data)[0].split("*")[1])

    @property
    def code(self):
        return self.AK5_CODES[self.ak5_status()]

    @property
    def doc_number(self):
        return re.findall("AK2\*.*\*(.*)", self.edi_data)[0]

    @property
    def company(self):
        return re.findall("GS\*(.*)", self.edi_data)[0].split("*")[2]


class EDI824(object):
    ACCEPTED = "accepted"
    REJECTED = "rejected"
    ACCEPTED_WITH_ERROR = "accept_with_error"

    def __init__(self, filename, edi_data):
        self.filename = filename
        self.edi_data = edi_data

    @property
    def control_number(self):
        return int(re.findall("(OTI\*.*)", self.edi_data)[0].split("*")[8])

    def is_validate(self):
        if "OTI*TR" in self.edi_data:
            return self.REJECTED
        elif "OTI*TA" in self.edi_data:
            return self.ACCEPTED
        elif "OTI*TE" in self.edi_data:
            return self.ACCEPTED_WITH_ERROR

    def fetch_from_ftp(self):
        raise NotImplementedError

    @property
    def company(self):
        return re.findall("GS\*(.*)", self.edi_data)[0].split("*")[2]


if __name__ == "__main__":
    edi_data = edi404_data_from_shipment(10)
    edi = EDI404(template_path="vantedge/edi/xml/templates/404.xml", data=edi_data)
    edi.generate_edi()
