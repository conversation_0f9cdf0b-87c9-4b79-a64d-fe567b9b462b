from django.utils import timezone

from dataclasses import dataclass, fields
import datetime
import re
from decimal import Decimal, ROUND_05UP
from typing import Literal, Optional

from lxml import objectify
from lxml.objectify import E
from more_itertools import quantify

from vantedge.edi.xml2edi import <PERSON>DIConvert
from vantedge.edi.models import EdiCounter

VALID_COUNTRIES = ["CA", "US"]
COUNTRY_MAP = {"USA": "US", "US": "US", "CA": "CA", "CANADA": "CA"}

def numeric_to_decimal(value):
    if isinstance(value, Decimal):
        pass
    elif isinstance(value, float) or isinstance(value, int):
        value = Decimal(value)

    return value.quantize(Decimal(".01"))


def move_precision(value):
    return str(numeric_to_decimal(value)).replace(".", "")

def format_irs_number(value):
    value = str(value).replace("-", "")
    return value[:2] + "-" + value[2:]

class ValidationError(Exception):
    def __init__(self, field, value, *args, message=None):
        super().__init__(args)
        if message:
            raise Exception(message)

        if value is None or value.strip() == "":
            value = "EMPTY"
        raise ValueError(
            {field: f"value {value} for field '{field}' is not validated!"}
        )


@dataclass
class partyData:
    name: str
    city: str
    state: str
    address1: str
    postal_code: str
    country: str

    def __post_init__(self):
        if len(self.state) != 2:
            raise ValidationError("State", self.state)

        if self.country.upper() not in VALID_COUNTRIES:
            raise ValidationError("Country", self.country)

        if len(self.postal_code) < 4:
            raise ValidationError("Postal Code", self.postal_code)

        if len(self.address1) < 4:
            raise ValidationError("Address", self.address1)


@dataclass
class BuyerData(partyData):
    irs: str

    def __post_init__(self):
        if not self.irs:
            raise ValidationError("IRS", self.irs)

        return super().__post_init__()


@dataclass
class ConsigneeData(partyData):
    irs: str

    def __post_init__(self):
        if not self.irs:
            raise ValidationError("IRS", self.irs)

        return super().__post_init__()


@dataclass
class SupplierData(partyData):
    manufacture_code: str


@dataclass
class ExporterData(partyData):
    email: str
    manufacture_code: str


@dataclass
class PaymentInfo:
    location_qualifier: str  # literal[LB, LC,...]
    transport_term: str = "CAF"  # Cost and Freight
    census_schedule: str = "D"
    shipment_method_of_payment: str = "PP"


@dataclass
class Shipment:
    transportation_method: str
    name_of_declarant: str
    origin_address: str  # [XA, XB, XO, XS....]
    importer_reference_number: str #Importer Reference Number
    shipper_reference_number: str


@dataclass
class InvoicePerformaData:
    doc_number: str
    sender_id: str
    gs_code: str
    segment_terminator: Optional[str]
    username: str
    password: str
    server: Optional[str]
    remote_locations: Optional[str]

    exporter: ExporterData
    supplier: SupplierData

    ultimate_consignee: ConsigneeData
    buyer: BuyerData

    transit_control_number: str
    shipment_number: str
    est_date_reach_border: datetime.date
    date_of_export: datetime.date
    agent_reference_number: str

    invoice_price_includes: str  # Literal[0, 91, 92, 93]
    us_custom_office: str  # Literal["0102","0104", "0106"]
    packaging: str  # = Literal["19", "51", "TC"] # ?
    tsca_statement: str
    end_use: str

    product_description: str
    weight: int
    weight_unit: str  # = Literal["KG", "LB"] #?
    quantity: Decimal
    tariff_number: str

    unit_price: Decimal
    basis_of_price: str

    tds1: Decimal
    tds2: Decimal
    tds3: Decimal
    tds4: Decimal

    discount_percent: float

    monetary_amount: int
    containers: str
    freight_charge_unit: str  # CAD, USD

    origin_country: str  # = "CA"

    date_of_invoice: datetime.date
    invoice_number: str
    transaction_type: str  # = "SS"  # Literal["CI", "CO", "IN", "PP", "SS"]
    term_of_sale: str

    is_consignee_related_invoice_party: bool
    currency: str  # CAD, USD

    account_number: str
    payment_info: PaymentInfo
    shipment: Shipment
    is_hazardous: bool

    paps: str
    scac: str

    def __post_init__(self):
        for field in fields(self):
            value = getattr(self, field.name)
            if value is None or value == "":
                if field.name == "containers" and self.shipment.transportation_method == "M":
                    continue
                if field.name in ["tds3", "server", "remote_locations"]:
                    continue
                raise ValidationError(field.name, value)


def edi810_transaction_data_from_invoice(invoice):
    if not invoice.get_sender_id():
        raise ValidationError("Sender ID not defined in the system", None)

    if not invoice.get_gs_code():
        raise ValidationError("GS CODE not defined in the system", None)

    if not invoice.get_ftp_username():
        raise ValidationError("Username not defined in the system", None)

    try:
        if invoice.car:
            assert invoice.consignee.data.extra_data.irs
        if invoice.ticket:
            assert (invoice.ticket_customs_data["consignee_IRS_number"] or invoice.tboss_consignee.data.customs.irs_number)
            assert (invoice.ticket_customs_data["buyer_IRS_number"] or invoice.tboss_buyer.data.customs.irs_number)
    except:
        raise ValidationError("IRS", None)

    try:
        if invoice.car:
            assert invoice.exporter.data.extra_data.manufacture_code
        if invoice.ticket:
            assert invoice.ticket_customs_data["exporter_mid"]
    except:
        raise ValidationError("Exporter Manufacture Code", None)

    try:
        if invoice.car:
            assert invoice.supplier.data.extra_data.manufacture_code
        if invoice.ticket:
            assert invoice.ticket_customs_data["supplier_mid"]
    except:
        raise ValidationError("Supplier Manufacture Code", None)

    # if invoice.invoice_price_includes != "0":
    #     raise ValidationError(
    #         "invoice_price_includes",
    #         invoice.get_invoice_price_includes_display(),
    #         f"Not supported value {invoice.get_invoice_price_includes_display()}!",
    #     )

    if not invoice.get_account_number():
        raise ValidationError("deringer account is not set", None)

    if not invoice.get_product_hts():
        raise ValidationError("Product HTS is not empty", None)

    if invoice.car:
        end_use = ""
        exporter_contact = invoice.exporter.data.contact
        if not exporter_contact or not exporter_contact[0].email:
            raise ValidationError("Exporter contact email", None)


        exporter = ExporterData(
            name=invoice.exporter.data.address.name,
            city=invoice.exporter.data.address.city,
            state=invoice.exporter.data.address.state,
            address1=invoice.exporter.data.address.address1,
            postal_code=invoice.exporter.data.address.postal_code,
            country=invoice.exporter.data.address.country,
            email=exporter_contact[0].email,
            manufacture_code=invoice.exporter.data.extra_data.manufacture_code,
        )

        supplier = SupplierData(
            name=invoice.supplier.data.address.name,
            city=invoice.supplier.data.address.city,
            state=invoice.supplier.data.address.state,
            address1=invoice.supplier.data.address.address1,
            postal_code=invoice.supplier.data.address.postal_code,
            country=invoice.supplier.data.address.country,
            manufacture_code=invoice.supplier.data.extra_data.manufacture_code,
        )

        ultimate_consignee = ConsigneeData(
            name=invoice.consignee.data.address.name,
            city=invoice.consignee.data.address.city,
            state=invoice.consignee.data.address.state,
            address1=invoice.consignee.data.address.address1,
            postal_code=invoice.consignee.data.address.postal_code,
            country=invoice.consignee.data.address.country,
            irs=invoice.consignee.data.extra_data.irs,
        )

        buyer = BuyerData(
            name=invoice.buyer.data.address.name,
            city=invoice.buyer.data.address.city,
            state=invoice.buyer.data.address.state,
            address1=invoice.buyer.data.address.address1,
            postal_code=invoice.buyer.data.address.postal_code,
            country=invoice.buyer.data.address.country,
            irs=invoice.buyer.data.extra_data.irs,
        )


    if invoice.ticket:
        contract = invoice.get_contract()
        if not invoice.ticket_customs_data["exporter_name"]:
            raise ValidationError("Exporter contact email", None)

        end_use=contract.get_end_use_display()

        exporter = ExporterData(
            name=invoice.ticket_customs_data["exporter_name"],
            city=invoice.ticket_customs_data["exporter_address"]["city"],
            state=invoice.ticket_customs_data["exporter_address"]["province"],
            address1=invoice.ticket_customs_data["exporter_address"]["street"],
            postal_code=invoice.ticket_customs_data["exporter_address"]["postal_code"],
            country=COUNTRY_MAP[invoice.ticket_customs_data["exporter_address"]["country"].upper()],
            # email=invoice.ticket_customs_data["exporter_email"],
            email=contract.responsible_employee.email,
            manufacture_code=invoice.ticket_customs_data["exporter_mid"]
        )

        supplier = SupplierData(
            name=invoice.ticket_customs_data["supplier_name"],
            city=invoice.ticket_customs_data["supplier_address"]["city"],
            state=invoice.ticket_customs_data["supplier_address"]["province"],
            address1=invoice.ticket_customs_data["supplier_address"]["street"],
            postal_code=invoice.ticket_customs_data["supplier_address"]["postal_code"],
            country=COUNTRY_MAP[invoice.ticket_customs_data["supplier_address"]["country"].upper()],
            manufacture_code=invoice.ticket_customs_data["supplier_mid"]
        )

        consignee_irs_number = (invoice.ticket_customs_data["consignee_IRS_number"] or
                                invoice.tboss_consignee.data.customs.irs_number)
        ultimate_consignee = ConsigneeData(
            name=invoice.ticket_customs_data["consignee_name"],
            city=invoice.ticket_customs_data["consignee_address"]["city"],
            state=invoice.ticket_customs_data["consignee_address"]["province"],
            address1=invoice.ticket_customs_data["consignee_address"]["street"],
            postal_code=invoice.ticket_customs_data["consignee_address"]["postal_code"],
            country=COUNTRY_MAP[invoice.ticket_customs_data["consignee_address"]["country"].upper()],
            irs=format_irs_number(consignee_irs_number)
        )

        buyer_irs_number = (invoice.ticket_customs_data["buyer_IRS_number"] or
                            invoice.tboss_buyer.data.customs.irs_number)
        buyer = BuyerData(
            name=invoice.ticket_customs_data["buyer_name"],
            city=invoice.ticket_customs_data["buyer_address"]["city"],
            state=invoice.ticket_customs_data["buyer_address"]["province"],
            address1=invoice.ticket_customs_data["buyer_address"]["street"],
            postal_code=invoice.ticket_customs_data["buyer_address"]["postal_code"],
            country=COUNTRY_MAP[invoice.ticket_customs_data["buyer_address"]["country"].upper()],
            irs=format_irs_number(buyer_irs_number)
        )

    return InvoicePerformaData(
        doc_number=str(invoice.id).zfill(4),
        sender_id=invoice.get_sender_id(),
        gs_code=invoice.get_gs_code(),
        segment_terminator=invoice.get_segment_terminator(),
        username=invoice.get_ftp_username(),
        password=invoice.get_ftp_password(),
        server=invoice.get_ftp_server(),
        remote_locations=invoice.get_ftp_remote_locations(),
        exporter=exporter,
        supplier=supplier,
        ultimate_consignee=ultimate_consignee,
        buyer=buyer,
        transit_control_number=1,
        shipment_number=invoice.get_shipment_number(),
        est_date_reach_border=invoice.est_date_reach_border.strftime("%Y%m%d"),
        date_of_export=invoice.date_of_export.strftime("%Y%m%d"),
        agent_reference_number=invoice.agent_reference_number,
        invoice_price_includes=invoice.invoice_price_includes,
        us_custom_office=invoice.us_custom_office,
        monetary_amount=invoice.estimate_freight_charge,
        freight_charge_unit=invoice.currency,
        origin_country=invoice.country_of_export,
        date_of_invoice=invoice.date_of_invoice.strftime("%Y%m%d"),
        invoice_number=invoice.invoice_number,
        transaction_type=invoice.transaction_type_code,
        term_of_sale=invoice.term_of_sale,
        is_consignee_related_invoice_party=invoice.is_consignee_related_invoice_party,
        currency=invoice.currency,
        account_number=invoice.get_account_number(),
        payment_info=PaymentInfo(
            location_qualifier=invoice.get_location_qualifier(),
            transport_term=invoice.get_transport_term(),
            census_schedule="D",
            shipment_method_of_payment=invoice.shipment_method_of_payment,
        ),
        containers=invoice.get_container(),
        packaging=invoice.packaging,
        tsca_statement=invoice.tsca_statement or "",
        end_use=end_use,
        product_description=invoice.get_invoice_description(),
        weight_unit="KG",
        weight=invoice.weight,
        quantity=invoice.get_units_shipped(),
        tariff_number=invoice.get_product_hts().replace(".", ""),
        tds1=invoice.get_TDS01(),
        tds2=invoice.get_TDS02(),
        tds3=invoice.get_TDS03(),
        tds4=invoice.get_TDS04(),
        discount_percent=invoice.get_discount_percent(),
        basis_of_price=invoice.basis_of_price,
        unit_price=invoice.get_unit_price(),
        shipment=Shipment(
            transportation_method=invoice.transportation_method,
            name_of_declarant=invoice.get_name_of_declarant(),
            origin_address=invoice.country_of_export,
            importer_reference_number=invoice.importer_reference_number,
            shipper_reference_number=invoice.shipper_reference_number
        ),
        is_hazardous=invoice.get_is_hazardous(),
        paps=invoice.shipper_reference_number,
        scac=invoice.carrier_code
    )


class EDI810(object):
    def __init__(self, data, template_path="vantedge/edi/xml/templates/810.xml"):
        self.template_path = template_path
        self.data = data
        self._control_number = None
        self.xml = None
        self.edi810_base()

    def edi810_base(self):
        with open(self.template_path) as f:
            self.xml = objectify.fromstring(f.read())

    def ST(self):
        return E.ST(E.ST01("810"), E.ST02(self.data.doc_number))

    def generate_address(self, address):
        N3 = E.N3()

        N3.append(E.N301(address.address1))

        if getattr(address, "address2", None):
            N3.append(E.N302(address.address2))

        N4 = E.N4()
        if address.city:
            N4.append(E.N401(address.city))

        if address.state:
            N4.append(E.N402(address.state))

        if address.postal_code:
            N4.append(E.N403(address.postal_code))

        if address.country:
            N4.append(E.N404(address.country))

        if N4.getchildren():
            return E.LoopAddress(N3, N4)

        return N3

    def N1(self):
        # Buyer (Conditional)
        if self.data.buyer:
            self.xml.append(
                E.N1(
                    E.N101("BY"),
                    E.N102(self.data.buyer.name),
                    E.N103("ZZ"),
                    E.N104(self.data.buyer.irs),
                )
            )
            self.xml.append(self.generate_address(self.data.buyer))

        # ultimate consignee
        self.xml.append(
            E.N1(
                E.N101("UC"),
                E.N102(self.data.ultimate_consignee.name),
                E.N103("ZZ"),
                E.N104(self.data.ultimate_consignee.irs),
            )
        )
        self.xml.append(self.generate_address(self.data.ultimate_consignee))

        # Exporter
        self.xml.append(
            E.N1(
                E.N101("EX"),
                E.N102(self.data.exporter.name),
                E.N103("ZZ"),
                E.N104(self.data.exporter.manufacture_code),
            )
        )
        self.xml.append(self.generate_address(self.data.exporter))

        # Supplier
        self.xml.append(
            E.N1(
                E.N101("SU"),
                E.N102(self.data.supplier.name),
                E.N103("ZZ"),
                E.N104(self.data.supplier.manufacture_code),
            )
        )
        self.xml.append(self.generate_address(self.data.supplier))

    def BIG(self):
        return E.BIG(
            E.BIG01(self.data.date_of_invoice),
            E.BIG02(self.data.invoice_number),
            E.BIG06("C"),
            E.BIG07(self.data.transaction_type),
        )

    def NTE_party_relation(self):
        return E.NTE(
            E.NTE01("CAC"),
            E.NTE02("Y" if self.data.is_consignee_related_invoice_party else "N"),
        )

    def NTE_invoice_price_includes(self):
        return E.NTE(E.NTE01("BFD"), E.NTE02(self.data.invoice_price_includes))

    def NTE_us_custom_office(self):
        return E.NTE(E.NTE01("LOC"), E.NTE02(self.data.us_custom_office))

    def NTE_transmit_country(self):
        return E.NTE(E.NTE01("CUS"), E.NTE02(self.data.origin_country))

    def CUR(self):
        return E.CUR(E.CUR01("ZZ"), E.CUR02(self.data.currency))

    def REF_shipment_number(self):
        return E.REF(E.REF01("1O"), E.REF02(self.data.shipment_number))

    def REF_reference_identification(self):
        return E.REF(E.REF01("CR"), E.REF03(self.data.shipment.importer_reference_number))

    # missing item in plains BRD
    # REF*ZZ*SOL*Y

    def PER_account_number(self):
        return E.PER(
            E.PER01("ZZ"),
            E.PER02(self.data.account_number),
            E.PER03("EM"),
            E.PER04(self.data.exporter.email),
        )

    def ITD(self):
        return E.ITD(E.ITD01(self.data.term_of_sale))

    def DTM_entry(self):
        return E.DTM(E.DTM01("058"), E.DTM02(self.data.est_date_reach_border))

    def DTM_export(self):
        return E.DTM(E.DTM01("168"), E.DTM02(self.data.date_of_export))

    def FOB(self):
        return E.FOB(
            E.FOB01(self.data.payment_info.shipment_method_of_payment),
            E.FOB02(self.data.payment_info.census_schedule),
            E.FOB03("CA"),
            E.FOB04("01"),
            E.FOB05(self.data.payment_info.transport_term),
            E.FOB06("ZZ"),
            E.FOB07(self.data.payment_info.location_qualifier),
        )

    def PID(self):
        return E.PID(E.PID01("F"), E.PID05(self.data.product_description))

    def MEA(self):
        return E.MEA(E.MEA01("WT"), E.MEA03(self.data.weight), E.MEA04("KG"))

    def PKG(self):
        return E.PKG(E.PKG01("F"), E.PKG05(f"{self.data.tsca_statement}, {self.data.end_use}"))

    def PAM(self):
        return E.PAM(E.PAM04("06"), E.PAM05(self.data.monetary_amount))

    def PAM_discount(self):
        return E.PAM(
            E.PAM13("18"), E.PAM14(numeric_to_decimal(self.data.discount_percent))
        )

    def N9(self):
        # FN is for Forwarder's/Agent's Reference Number
        # US Customs (USC) location code 'NONE'
        return E.N9(E.N901("FN"), E.N902("NONE"))

    def MSG(self):
        return E.MSG(E.MSG01(self.data.containers))

    def IT1(self):
        return E.IT1(
            E.IT101("1"),
            E.IT102(self.data.quantity),
            E.IT103("BR"),
            E.IT104(self.data.unit_price),
            E.IT105(self.data.basis_of_price),
            E.IT106("MF"),
            E.IT107(self.data.supplier.manufacture_code),
            E.IT108("CE"),
            E.IT109("1"),
            E.IT110("TP"),
            E.IT111(self.data.tariff_number),
            E.IT112("CH"),
            E.IT113(self.data.shipment.origin_address),
            E.IT114("ZZ"),
            E.IT115("S"),
        )

    def IT3_primary(self):
        return E.IT3(E.IT301(self.data.quantity), E.IT302("BR"))

    def CTP(self):
        return E.CTP(E.CTP02("SUM"), E.CTP03(numeric_to_decimal(self.data.tds1)))

    def PID_item_description(self):
        return E.PID(E.PID01("F"), E.PID(self.data.product_description))

    def TDS(self):
        return E.TDS(
            E.TDS01(move_precision(self.data.tds1)),
            E.TDS02(move_precision(self.data.tds2)),
            E.TDS03(
                move_precision(self.data.tds3) if self.data.tds3 is not None else "0"
            ),
            E.TDS04(move_precision(self.data.tds4)),
        )

    def CAD(self):
        return E.CAD(
            E.CAD01(self.data.shipment.transportation_method),
            E.CAD04(self.data.scac),
            E.CAD05(self.data.shipment.name_of_declarant),
            E.CAD07("SI"),
            E.CAD08(self.data.paps),
        )

    def ISS(self):
        return E.ISS(E.ISS01(self.data.quantity), E.ISS02(self.data.packaging))
        # return E.ISS(E.ISS01("1"), E.ISS02("19"))

    def CTT(self):
        return E.CTT(E.CTT01("1"), E.CTT02(move_precision(self.data.tds1)))

    def REF_PG(self):
        return E.REF(E.REF01("ZZ"), E.REF02("PG"), E.REF03("ZZ>>ZZ>EPA>ZZ>TS1"))

    def REF_PG_TSCA(self):
        return E.REF(
            E.REF01("ZZ"),
            E.REF02("PG_TSCA"),
            E.REF03("EPA"),
            E.REF04("ZZ>>ZZ>EPA>ZZ>TS1"),
        )

    def REF_PG_TSCA1(self):
        return E.REF(
            E.REF01("ZZ"),
            E.REF02("PG_TSCA1"),
            E.REF03(self.data.product_description),
            E.REF04(f"ZZ>N>ZZ>{'EP4' if self.data.is_hazardous else 'EP5'}>ZZ>Y"),
        )

    def REF_PG_TSCA2(self):
        return E.REF(
            E.REF01("ZZ"),
            E.REF02("PG_TSCA2"),
            E.REF03("Cert Name"),
            E.REF04("ZZ>CI>ZZ><EMAIL>>ZZ>9198375566"),
        )

    def generate_transaction_set(self):
        self.edi810_base()
        self.xml.append(self.ST())
        self.xml.append(self.BIG())
        self.xml.append(self.NTE_party_relation())
        self.xml.append(self.NTE_invoice_price_includes())
        self.xml.append(self.NTE_us_custom_office())
        self.xml.append(self.NTE_transmit_country())
        self.xml.append(self.CUR())
        self.xml.append(self.REF_shipment_number())
        self.xml.append(self.REF_reference_identification())
        self.xml.append(self.PER_account_number())
        self.N1()
        self.xml.append(self.ITD())
        self.xml.append(self.DTM_entry())
        self.xml.append(self.DTM_export())
        self.xml.append(self.FOB()) # not in sample
        self.xml.append(self.PID())
        self.xml.append(self.MEA())
        self.xml.append(self.PKG())
        self.xml.append(self.PAM())
        if self.data.discount_percent:
            self.xml.append(self.PAM_discount())
        self.xml.append(self.N9())
        if self.data.containers:
            self.xml.append(self.MSG())
        self.xml.append(self.IT1())
        self.xml.append(self.IT3_primary())
        self.xml.append(self.CTP())
        self.xml.append(self.PID())
        self.xml.append(self.TDS())
        self.xml.append(self.CAD())
        self.xml.append(self.ISS())
        self.xml.append(self.CTT())
        # self.xml.append(self.REF_PG())
        # self.xml.append(self.REF_PG_TSCA())
        # self.xml.append(self.REF_PG_TSCA1())
        # self.xml.append(self.REF_PG_TSCA2())

    def convert_to_txt(self):
        return EDIConvert(self.xml, 404).edi.strip()

    @property
    def control_number(self):
        if self._control_number is None:
            self._control_number = EdiCounter.get(810)

        return self._control_number

    def generate_header(self):
        now = timezone.now()

        isa = f'ISA*00*{" ":10}*00*{" ":10}*ZZ*{self.data.sender_id:15}*12*{"8025245975AS2":15}*{now:%y%m%d}*{now:%H%M}*U*00401*{self.control_number:0>9}*1*P*>'
        gs = f"GS*IN*{self.data.gs_code}*8025245975AS2*{now:%Y%m%d}*{now:%H%M}*{self.control_number}*X*004010"

        return (isa, gs)

    def generate_footer(self, txt_data):
        segment_cnt = txt_data.strip().count("\n") + 2
        se = f"SE*{segment_cnt}*{self.data.doc_number}"
        ge = f"GE*1*{self.control_number}"
        iea = f"IEA*1*{self.control_number:0>9}"

        return (se, ge, iea)

    def generate_edi(self):
        self.generate_transaction_set()
        body = self.convert_to_txt()
        header = self.generate_header()
        footer = self.generate_footer(body)

        data = []
        data.extend(header)
        data.extend([body])
        data.extend(footer)

        result = "\n".join(data).upper()
        result = result + "\n"

        if self.data.segment_terminator is not None:
            result = result.replace("\n", self.data.segment_terminator)

        return result

    def get_filename(self):
        return f"edi_810_{self.data.gs_code}_{self.control_number}.txt"

    def send_to_ftp(self):
        raise NotImplementedError


class EDI997(object):
    AK5_CODES = {
        "A": "Accepted",
        "E": "Accepted with Errors noted",
        "M": "Rejected; message authentication",
        "P": "Partially accepted",
        "R": "Rejected",
        "W": "Rejected",
        "X": "Rejected",
    }

    def __init__(self, filename, edi_data, company, segment_terminator=""):
        self.company = company
        self.filename = filename
        self.edi_data = edi_data
        self.segment_terminator = segment_terminator

    def remove_segment_terminator(self, value):
        value = value.replace("\r", "")
        if self.segment_terminator and value.endswith(self.segment_terminator):
            return value[:-1]
        return value

    def ak5_status(self):
        status = re.findall(".*AK5\*(\w{1}).*", self.edi_data)[0]
        return self.remove_segment_terminator(status)


    def is_validate(self):
        if "AK5*A" in self.edi_data:
            return True
        else:
            return False

    @property
    def control_number(self):
        control_number = re.findall("AK1\*(.*)", self.edi_data)[0].split("*")[1]
        return int(self.remove_segment_terminator(control_number))

    @property
    def code(self):
        return self.AK5_CODES[self.ak5_status()]

    @property
    def doc_number(self):
        doc_number = re.findall("AK2\*.*\*(.*)", self.edi_data)[0]
        return self.remove_segment_terminator(doc_number)

