from .edi import EDI
from django.conf import settings
from django.db.models import Q
from django_q.tasks import async_task
import random, string

RAILINC_CONFIGS = {
    "production": {
        "server": settings.RAILINC_FTP_HOST,
        "username": settings.RAILINC_FTP_USER,
        "password": settings.RAILINC_FTP_PASS,
        "remote_locations": {"clu": "CER/", "cll": "CER/"},
    }
}
RAILINC_CONFIGS["debug"] = {
    "server": "",
    "username": "",
    "password": "",
    "remote_locations": RAILINC_CONFIGS["production"]["remote_locations"],
    "passive": False,
}

# TODO
# Include external post in the class


def post(transaction, data, config):
    with RailincEDI(config) as edi:
        if edi._config["server"] == "":
            print(
                transaction,
                f"{data.encode()} => {''.join(random.choices(string.ascii_uppercase + string.digits, k=10)) + '.txt'}",
            )
            return data
        edi.go(transaction)
        edi.connection.put(
            None,
            "".join(random.choices(string.ascii_uppercase + string.digits, k=10))
            + ".txt",
            contents=data.encode(),
        )
        return data


class RailincEDI(EDI):
    configs = RAILINC_CONFIGS

    def __init__(self, config=None):
        if not config:
            if (
                hasattr(settings, "AZURE_CONTAINER")
                and settings.AZURE_CONTAINER == "wheelhouse-prod"
            ):
                config = "production"
            else:
                config = "debug"
        super().__init__(config)

    def post(self, transaction, data):
        if settings.DEBUG:
            post(transaction, data, self._config_type)
        else:
            async_task(post, transaction, data, self._config_type)

    def update_permanant_fleet(self, fleetId):
        from ...trackandtrace.models import RailCar
        from ..models import EdiCompany

        query = Q(fleet_status=RailCar.FLEET_STATUS.permanent)
        query = query & Q(
            company=EdiCompany.objects.get(railinc_fleet=fleetId).company.track_trace
        )

        fleet = RailCar.objects.filter(query)
        clu_message = f"CLU TO RRDC {fleetId.ljust(9)}P             \n"
        for car in fleet:
            clu_message += f"P{car.mark}{car.number}\n"
        car_count = str(len(fleet)).zfill(8)
        clu_message += f"EOM{car_count}\n"
        self.post("clu", clu_message)

    def query_fleet(self, fleetId):
        cll_message = f"CLL TO RRDC {fleetId.ljust(9)}              \nEOM\n"
        self.post("cll", cll_message)

