from lxml.objectify import E, deannotate
from lxml import etree
from vantedge.edi.xml2edi import <PERSON><PERSON><PERSON><PERSON><PERSON>
from vantedge.edi.comm import CNEDI

if __name__ != "__main__":
    from vantedge.wheelhouse.models.rail import RailShipment, RailCar, RoutingError
else:
    class RoutingError(Exception):
        pass

class EDIError(Exception):
    def __init__(self, edi_code = None, doc_num = None, message = 'EDIParser Exception'):
        self.edi_code = edi_code
        self.doc_num = doc_num
        self.message = message

def edi404_base(doc_number):
    import os
    from lxml import objectify
    print(os.getcwd())
    with open('vantedge/edi/xml/templates/404.xml') as f:
        xml = objectify.fromstring(f.read())
    xml.ST.ST02 = f'{doc_number:0>9}'
    return xml


def edi404_route(route):
    from more_itertools import grouper
    result = []
    if not route.routing:
        raise RoutingError(route, 'No route specified.')
    if '/' not in route.routing:
        result = [E.R2(E.R201(route.routing), E.R202('S'))]
    else:
        parsed_route = route.routing.split('/')
        if len(parsed_route) % 2 != 1:
            raise RoutingError(route, 'Invalid route.  Need RR/INTCHG/RR/INTCHG/RR....')
        parsed_route = grouper(parsed_route, 2, '')

        for n, (rr, intchg) in enumerate(parsed_route):
            n = 'S' if n == 0 else n
            result.append(E.R2(E.R201(rr), E.R202(n), E.R203(intchg)))

    return result


def edi404_car(car):
    N7 = E.N7(E.N701(car.car_mark), E.N702(car.car_number), E.N703(100), E.N704('N'), E.N711('RR'))
    M7 = E.M7(E.M701(car.seal_number))
    N7Loop1 = E.N7Loop1(N7, M7)
    return N7Loop1


def edi404_product(product):
    L5 = E.L5(E.L501(1), E.L502(product.name), E.L503(product.data['codes']['stcc']), E.L504('T'))
    return L5


def edi404_shipment(shipment):
    xml = edi404_base(shipment.id)
    xml.N9.N904 = xml.M3.M302 = shipment.ship_date.strftime('%Y%m%d')
    xml.N9.N905 = xml.M3.M303 = shipment.ship_date.strftime('%H%M')
    xml.N9.N902 = shipment.id

    for N7 in [edi404_car(car) for car in shipment.cars.all()][::-1]:
        xml.N9.addnext(N7)
    xml.F9.F902 = shipment.origin.name
    xml.F9.F903 = shipment.origin.data['address']['state']
    xml.D9.D902 = shipment.destination.name
    xml.D9.D903 = shipment.destination.data['address']['state']

    # Shipper
    xml.append(E.N1(E.N101('SH'), E.N102('Petrogas')))
    xml.append(E.N3(E.N301('Address Line')))

    # Consignee
    xml.append(E.N1(E.N101('CN'), E.N102('Petrogas')))
    xml.append(E.N3(E.N301('Address Line')))

    # Customers Broker
    xml.append(E.N1(E.N101('XU'), E.N102('Petrogas')))
    xml.append(E.N3(E.N301('Address Line')))

    for R2 in edi404_route(shipment.route):
        xml.append(R2)

    xml.append(E.LX(E.LX01(1)))
    # L0
    xml.append(edi404_product(shipment.product))
    total_weight = int(sum(map(lambda car: car.weight, shipment.cars.all())))
    xml.append(E.L0(E.L001(1), E.L004(total_weight), E.L005('N'), E.L008(1), E.L009('CLD')))

    deannotate(xml, xsi_nil=True)
    with CNEDI() as edi:
        out = EDIConvert(xml, 404)
        control_number, posted_edi = edi.post('404', out.edi)
        shipment.data = {'edi': {'control_number': control_number, '404': posted_edi, '997': None, '824': None}}
        shipment.edi = posted_edi
        shipment.status = 'bol-sent'
        shipment.save()
        with open('vantedge/edi/out/404.txt', 'w') as f:
            f.write(posted_edi)
        with open('vantedge/edi/out/404.xml', 'w') as f:
            f.write(etree.tostring(xml).decode('utf-7'))


def get_control_number(shipment):
    if 'edi' in shipment.data:
        control_number = shipment.data['edi'].get('control_number', None)
        if not control_number:
            raise EDIError(message = f'Attempting to check EDIParser response without a control number for shipment {shipment.id}')
        return control_number

    raise EDIError(message = f'No EDIParser in data for shipment {shipment.id}')

def edi997_shipment(shipment):
    import time
    control_number = get_control_number(shipment)

    with CNEDI() as edi:
        result = None
        tries = 10
        while not result and tries:
            tries -= 1
            time.sleep(1)
            result = edi.get(997, control_number)
        if result:
            with open('vantedge/edi/out/997.txt', 'w') as f:
                f.write(result)
        shipment.data['edi']['997'] = result
        if 'AK5*R' in result:
            shipment.status = 'bol-rejected'
        else:
            shipment.status = 'bol-acknowledged'
        shipment.save()
    return result

def edi824_shipment(shipment):
    import time
    control_number = get_control_number(shipment)

    with CNEDI() as edi:
        result = None
        tries = 10
        while not result and tries:
            tries -= 1
            time.sleep(1)
            result = edi.get(824, control_number)
        if result:
            with open('vantedge/edi/out/997.txt', 'w') as f:
                f.write(result)
        shipment.data['edi']['824'] = result
        if 'OTI*TR' in result:
            shipment.status = 'bol-rejected'
        else:
            shipment.status = 'bol-accepted'
        shipment.save()
    return result

if __name__ == "__main__":
    pass
