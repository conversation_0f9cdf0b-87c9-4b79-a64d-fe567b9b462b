from django.core.management.base import BaseCommand

from vantedge.wheelhouse.models.rail import RailShipment
from vantedge.wheelhouse.models.railroad import Railroad


class Command(BaseCommand):
    help = "fetch and process 997 and 824, just pass the railroad name"

    def add_arguments(self, parser):
        parser.add_argument("railroad", nargs="+", type=str)

    def handle(self, *args, **options):
        railroads = Railroad.objects.filter(name__in=options["railroad"])

        for railroad in railroads:
            RailShipment.fetch_997(railroad)
            RailShipment.fetch_824(railroad)
