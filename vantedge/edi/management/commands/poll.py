from django.core.management.base import BaseCommand, CommandError
from django.conf import settings
from vantedge.edi.poll import poll_edi, poll_cache
from vantedge.edi.process import process

POLL_SOURCES = {"cpr": ["clm", "417", "423"], "cn": ["clm", "417"], "csxt": ["322"]}
POLL_SOURCES = {"railinc": ["clm", "cll", "417"], "cpr": ["423"]}
# POLL_SOURCES = {"cpr": ["423"]}


class Command(BaseCommand):
    help = "Poll Railroad Data"

    def add_arguments(self, parser):
        parser.add_argument("source", nargs="?", type=str)
        parser.add_argument("doc_type", nargs="?", type=str)
        parser.add_argument(
            "--skip_process",
            action="store_true",
            help="Do not automatically process the data",
        )
        parser.add_argument(
            "--cache-only", action="store_true", help="Do not use live sources"
        )

    def handle(self, *args, **options):
        for source in [options["source"]] if options["source"] else POLL_SOURCES.keys():
            for doc_type in (
                [options["doc_type"]] if options["doc_type"] else POLL_SOURCES[source]
            ):
                poll_cache(source, doc_type)
                print("Polling:", source, doc_type)
                if not (options["cache_only"]):
                    poll_edi(source, doc_type)
                if not (options["skip_process"]):
                    process(source, doc_type)
                else:
                    print("Skipping Processing")
