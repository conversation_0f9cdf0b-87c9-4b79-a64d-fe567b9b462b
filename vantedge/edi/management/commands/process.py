from django.core.management.base import BaseCommand, CommandError
from django.conf import settings
from vantedge.edi.models import EdiDocument
from vantedge.edi.poll import poll
from vantedge.edi.parse.clm import CLM
from vantedge.trackandtrace.models import CarEvent
from vantedge.edi.process import process

class Command(BaseCommand):
    help = 'Process Edi'

    def add_arguments(self, parser):
        parser.add_argument('source', nargs='?', type=str)
        parser.add_argument('doctype', nargs='?', type=str)


    def handle(self, *args, **options):
        self.process(**options)
