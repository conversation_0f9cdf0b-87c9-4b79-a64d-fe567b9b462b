from django.core.management.base import BaseCommand
from vantedge.edi.comm import RailincEDI
from vantedge.edi.models import EdiCompany

class Command(BaseCommand):
  help = "Query fleet from Railinc"

  def handle(self, *args, **options):
    with RailincEDI('production') as edi:
      for company in EdiCompany.objects.all():
        if company.railinc_fleet:
          edi.query_fleet(company.railinc_fleet)