# Generated by Django 3.2.13 on 2022-09-07 16:34

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('edi', '0008_auto_20220825_1133'),
    ]

    operations = [
        migrations.CreateModel(
            name='EdiLock',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_locked', models.BooleanField(default=False)),
                ('locked_datetime', models.DateTimeField(blank=True, null=True)),
                ('lock_lifespan', models.PositiveSmallIntegerField(help_text='in seconds', null=True)),
                ('locked_document', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='edi.edidocument')),
            ],
        ),
    ]
