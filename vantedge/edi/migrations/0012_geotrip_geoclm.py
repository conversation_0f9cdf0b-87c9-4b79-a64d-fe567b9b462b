# Generated by Django 4.2 on 2024-02-29 19:28

from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import model_utils.fields


class Migration(migrations.Migration):

    dependencies = [
        ('trackandtrace', '0050_rename_railmovementprice_railrate_and_more'),
        ('edi', '0011_remove_edifeaturesubscription_company_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='GeoTrip',
            fields=[
                ('created', model_utils.fields.AutoCreatedField(default=django.utils.timezone.now, editable=False, verbose_name='created')),
                ('modified', model_utils.fields.AutoLastModifiedField(default=django.utils.timezone.now, editable=False, verbose_name='modified')),
                ('trip_identifier', models.PositiveBigIntegerField(primary_key=True, serialize=False)),
                ('data', models.JSONField()),
                ('company', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='edi.edicompany')),
            ],
            options={
                'ordering': ['-trip_identifier'],
            },
        ),
        migrations.CreateModel(
            name='GeoCLM',
            fields=[
                ('created', model_utils.fields.AutoCreatedField(default=django.utils.timezone.now, editable=False, verbose_name='created')),
                ('modified', model_utils.fields.AutoLastModifiedField(default=django.utils.timezone.now, editable=False, verbose_name='modified')),
                ('clm_id', models.PositiveBigIntegerField(primary_key=True, serialize=False)),
                ('is_processed', models.BooleanField(default=None, null=True)),
                ('data', models.JSONField()),
                ('company', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='edi.edicompany')),
                ('trip', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='edi.geotrip')),
            ],
            options={
                'ordering': ['-clm_id'],
            },
        ),
    ]
