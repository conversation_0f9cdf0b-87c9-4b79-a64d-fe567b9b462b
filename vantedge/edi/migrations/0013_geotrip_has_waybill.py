# Generated by Django 4.2 on 2024-03-04 20:16

from django.db import migrations, models


def update_past_trips(apps, schema_editor):
    GeoTrip = apps.get_model("edi", "GeoTrip")
    GeoTrip.objects.all().update(has_waybill=False)


class Migration(migrations.Migration):

    dependencies = [
        ('edi', '0012_geotrip_geoclm'),
    ]

    operations = [
        migrations.AddField(
            model_name='geotrip',
            name='has_waybill',
            field=models.BooleanField(default=None, null=True),
        ),
        migrations.RunPython(update_past_trips)
    ]
