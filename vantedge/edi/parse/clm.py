import struct
from zoneinfo import ZoneInfo
from datetime import datetime
from dateutil.parser import parse, ParserError
from django.utils import timezone
from vantedge.edi.comm.dataclasses import RailCarEventDataclass
from vantedge.edi.comm.utility import convert_rail_time
from typing import List
from vantedge.util.logger.base import send_error
from vantedge.trackandtrace.models.other import RailStation
import re


def convert_field(field: str, data: str) -> str:
    result = data
    if "timestamp" in field or "wb_date" in field:
        """
        try:
            now = datetime.now()
            result = datetime.strptime(f"{now.year}{data}", "%Y%m%d%H%M")
            if (now-result).days > 190:
                result = datetime.strptime(f"{now.year+1}{data}", "%Y%m%d%H%M")
            elif (now-result).days < -190:
                result = datetime.strptime(f"{now.year-1}{data}", "%Y%m%d%H%M")
            result = timezone.get_current_timezone().localize(result, is_dst=False)
        except ValueError:
            result = None
        """

        # examples of timestamps:
        # 03080352 - event_timestamp
        # 031005 - delivery_timestamp
        # 2022.03.02 15:39:00 = bol_timestamp
        # 2022-03-12 wb_date

        # weird dates
        # 00000000 - delivery_timestamp
        # 9999.12.31 23:59:00 - bol_timestamp

        if not result:
            print(f"field {field} has no data")
            return None

        now = datetime.now()
        if not re.match(r"^2[0-1]\d{2}", result) and len(result) < 9:
            # add year and fill trailing with zeroes since delivery_timestamp column is missing minutes
            result = f"{now.year}{result}".ljust(12, "0")
        elif len(result) < 9:
            result = f"{now.year}{result}".ljust(12, "0")

        try:
            result = parse(result)
            # check if the timestamp is +- 20 years
            if (now - result).days not in range(-7305, 7305):
                result = None
        except ParserError:
            print(f"Failed to parse {field} with format {result}")
            result = None
    if field == "loaded":
        result = data == "L"

    return result


def decode_struct(struct_template, fields, data, target=None):
    decoded = struct.Struct(struct_template).unpack_from(bytes(data, "utf8"))
    if "car_mark" in fields:
        print(f"processing car data {decoded[0].decode()}{decoded[1].decode()}")
    result = zip(fields, map(lambda f: str(f, "utf-8").strip(), decoded))
    result = {data[0]: convert_field(data[0], data[1]) for data in result}

    if target:
        for field in result:
            setattr(target, field, result[field])

    return result


class CLMRecord:
    CLM_FMTS = {
        "D": {
            "fields": [
                "car_mark",
                "car_number",
                "location_city",
                "aei",
                "location_state",
                "evt_time_qual",
                "MM",
                "dd",
                "timezone",
                "hhmm",
                "loaded",
                "event_code",
                "train_id",
                "destination_city",
                "destination_state",
                "reporting_railroad",
            ],
            "struct_template": "4s 1x 6s 1x 9s 1s 2s 1s 2s 1x 2s 1s 4s 1x 1s 1x 1s 3x 6s 1x 9s 1x 2s 1x 4s",
        },
        "I": {
            "fields": [
                "car_mark",
                "car_number",
                "location_splc",
                "event_timestamp",
                "loaded",
                "event_code",
                "train_id",
                "destination_splc",
                "reporting_railroad",
                "destination_splc",
                "eta_event_code",
                "delivery_timestamp",
                "aei",
            ],
            "struct_template": "4s 6s 9s 8s 1s 1s 6s 9s 4s 9s 1s 6s 1s",
        },
        "I-RRDC": {
            "fields": [
                "car_mark",
                "car_number",
                "location_splc",
                "event_timestamp",
                "loaded",
                "event_code",
                "train_id",
                "clm_destination_splc",
                "reporting_railroad",
                "destination_splc",
                "eta_event_code",
                "delivery_timestamp",
                "weight",
                "bol_timestamp",
                "bol_rr",
                "bol",
                "care_of_name",
                "consignee_name",
                "freightpayer_name",
                "shipper_name",
                "wb_date",
                "origin_splc",
                "origin_city",
                "origin_333",
                "origin_state",
                "origin_railroad",
                "destination_splc",
                "destination_railroad",
                "hazmat",
                "commodity_description",
                "commodity_stcc",
                "cycle_id",
                "trip_seq",
            ],
            "struct_template": "4s 6s 9s 8s 1s 1s 6s 9s 4s 9s 1s 6s 2x 6s 1x 19s 1x 4s 1x 30s 1x 35s 1x 35s 1x 35s 1x 35s 1x 10s 1x 9s 1x 30s 1x 9s 1x 2s 1x 4s 1x 9s 1x 4s 1x 1s 1x 15s 1x 7s 1x 5s 1x 3s",
        },
        # Added to process railinc old alliance release dates
        "Y": {
            "fields": [
                "car_mark",
                "car_number",
                "location_splc",
                "event_timestamp",
                "loaded",
                "event_code",
                "train_id",
                "destination_splc",
                "reporting_railroad",
            ],
            "struct_template": "4s 6s 9s 8s 1s 1s 6s 9s 4s",
        },
    }

    def convert_I(self) -> None:
        location_splc = self.record["location_splc"]
        origin_splc = self.record["origin_splc"]

        origin_city = self.record["origin_city"]
        origin_state = self.record["origin_state"]
        origin_railroad = self.record["origin_railroad"]

        event_location = RailStation.get_station_by_splc(location_splc)
        origin_station = RailStation.get_station_by_splc(origin_splc)

        # check origin by city state if cannot be located by origin_splc
        if not origin_station:
            origin_station = RailStation.get_station_by_city_state(
                origin_city, origin_state, origin_railroad
            )

        timestamps = [
            "bol_timestamp",
            "wb_date",
            "event_timestamp",
            "delivery_timestamp",
        ]

        for ts in timestamps:
            if self.record[ts] and type(self.record[ts]) is datetime:
                station = event_location
                splc = location_splc
                waybill_date = (
                    self.record["bol_timestamp"]
                    or self.record["wb_date"]
                    or timezone.now()
                )

                if ts in [
                    "bol_timestamp",
                    "wb_date",
                ]:  # use origin_station for bol_timestamp and wb_date
                    station = origin_station
                    splc = origin_splc
                    waybill_date = None

                # if there is no station, save timestamp as unaware?
                if station and station.timezone:
                    tz = ZoneInfo(station.timezone)
                    self.record[ts] = self.record[ts].replace(tzinfo=tz)
                else:
                    print(
                        f"No rail station located for splc {splc}, saving {ts} as naive datetime."
                    )
                    # set the waybill date as naive
                    waybill_date = (
                        waybill_date.replace(tzinfo=None) if waybill_date else None
                    )

                # guess the year event_timestamp and delivery_timestamp
                if waybill_date:
                    self.record[ts] = self.record[ts].replace(year=waybill_date.year)

                    if (not waybill_date.tzinfo and self.record[ts].tzinfo) or (
                        not self.record[ts].tzinfo and waybill_date.tzinfo
                    ):
                        days_since = (
                            (
                                waybill_date.replace(tzinfo=None)
                                - self.record[ts].replace(tzinfo=None)
                            )
                        ).days
                    else:
                        days_since = (waybill_date - self.record[ts]).days

                    if days_since > 190:
                        self.record[ts] = self.record[ts].replace(
                            year=waybill_date.year + 1
                        )
                    elif days_since < -190:
                        self.record[ts] = self.record[ts].replace(
                            year=waybill_date.year - 1
                        )

    def convert_Y(self) -> None:
        location_splc = self.record.get("location_splc")
        event_location = RailStation.get_station_by_splc(location_splc)
        if event_location and event_location.timezone:
            tz = ZoneInfo(event_location.timezone)
            self.record["event_timestamp"] = self.record["event_timestamp"].replace(
                tzinfo=tz
            )

        waybill_date = timezone.now()
        self.record["event_timestamp"] = self.record["event_timestamp"].replace(
            year=waybill_date.year
        )
        days_since = (waybill_date - self.record["event_timestamp"]).days
        if days_since > 190:
            self.record["event_timestamp"] = self.record["event_timestamp"].replace(
                year=waybill_date.year + 1
            )
        elif days_since < -190:
            self.record["event_timestamp"] = self.record["event_timestamp"].replace(
                year=waybill_date.year - 1
            )

    def convert_D(self) -> None:
        now = datetime.datetime.now()
        self.record["event_timestamp"] = convert_rail_time(
            f'{now.year}{self.record["MM"]}{self.record["dd"]}{self.record["hhmm"]}'
        )
        if (now - self.record["event_timestamp"]).days < -190:
            self.record["event_timestamp"] = convert_rail_time(
                f'{now.year-1}{self.record["MM"]}{self.record["dd"]}{self.record["hhmm"]}'
            )
        elif (now - self.record["event_timestamp"]).days > 190:
            self.record["event_timestamp"] = convert_rail_time(
                f'{now.year+1}{self.record["MM"]}{self.record["dd"]}{self.record["hhmm"]}'
            )

    def as_railcar_event_dataclass(self) -> RailCarEventDataclass:
        result: RailCarEventDataclass = RailCarEventDataclass(
            **{
                field: self.record[field]
                for field in self.record
                if field in RailCarEventDataclass.__dataclass_fields__
            }
        )
        result.weight = result.weight if result.weight != "" else 0

        return result

    def __init__(self, source: str, fmt: str, data: str):
        if (key := f"{fmt}-{source}") in self.CLM_FMTS:
            fields, struct_template = self.CLM_FMTS[key].values()
        elif (key := fmt) in self.CLM_FMTS:
            fields, struct_template = self.CLM_FMTS[key].values()
        else:
            raise KeyError(f"Unknown CLM Format {fmt}-{source}")

        # decoded = struct.Struct(struct_template).unpack_from(bytes(data, 'utf8'))
        # self.record = zip(fields, map(lambda f: str(f, 'utf-8').strip(), decoded))
        # self.record = {data[0]: convert_field(data[0], data[1]) for data in self.record}
        try:
            self.record = decode_struct(struct_template, fields, data)
        except Exception as e:
            send_error(
                "Failed to decode SOURCE: %s, FMT: %s, DATA: %s ",
                source,
                fmt,
                str(data),
            )
            print(
                "Failed to decode", "SOURCE:", source, "FMT:", fmt, "DATA:", str(data)
            )
            raise e

        # print("CONVERTING FMT", fmt)
        getattr(self, "convert_" + fmt)()
        self.record["source_data"] = {"format": fmt, "data": data}


class CLM(object):
    def __init__(self, data: str = None):
        self.subscription = None
        self.fmt: str = None
        self.source: str = None
        self.filedate: str = None
        self.clm_records: List[CLMRecord] = []
        self.records: List[RailCarEventDataclass] = []
        self.data: str = None
        if data:
            self.data = data
        if self.data:
            self.parse()

    def parse_header(self, header: str):
        header_struct = {
            "fields": ["fmt", "source", "subscription", "filedate"],
            "struct_template": "3x 1s 2x 1x 4s 1x 9s 1x 8s",
        }
        decode_struct(
            header_struct["struct_template"],
            header_struct["fields"],
            header,
            target=self,
        )
        # self.fmt, self.source, self.subscription, self.filedate = header.split()[:4]

    def parse(self):
        lines = self.data.splitlines()
        self.parse_header(lines[0])

        # Check if we know how to process this CLM
        if self.fmt not in ["D", "I", "Y"]:
            print(f"Ignoring CLM with unknown format {self.fmt} ({lines[0]})")
            return

        # CLM file has no events
        if "UNCHANGED" in lines[1]:
            return

        # Process Remaining Data
        for clm_row in lines[1:]:
            if (
                "NOT AUTHORIZED" in clm_row
                or "EOM" in clm_row
                or "No Data" in clm_row
                or len(clm_row) < 5
            ):
                continue
            decoded = CLMRecord(self.source, self.fmt, clm_row)
            self.clm_records.append(decoded)
            self.records.append(decoded.as_railcar_event_dataclass())


# result = CLM(filename='/home/<USER>/code/vantedge/back/data/ediclm/cn/200830071028638.txt')
# print(result.records)
# print(CLMRecord('I', 'CTCX30055731557000008300542EA 39728324740000CN  324740000Z090118 ').record)
# print(CLMRecord('D', 'BNSF 808222 EASTPORT  ID 08 24 0701 L J   UP     TACOMA    WA CPRS').record)

if __name__ == "__main__":
    with open(
        "/home/<USER>/code/vantedge/data/edi_cache/railinc/clm/USOIL_CLM.0C6BCDDD.20200925135406313"
    ) as f:
        r = CLM(f.read())
        print(r.records[0].commodity_stcc)
        # print(r.records)
