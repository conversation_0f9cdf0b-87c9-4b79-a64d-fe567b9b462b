from vantedge.edi.models import EdiDocument
from vantedge.edi.parse.clm import CLM
from vantedge.edi.parse.edi import EDIParser
from vantedge.trackandtrace.models import CarEvent


def process(source: str = None, doc_type: str = None):
    edi_doc_qs = EdiDocument.objects.filter(processed__isnull=True)
    if doc_type:
        edi_doc_qs = edi_doc_qs.filter(doc_type=doc_type)
    if source:
        edi_doc_qs = edi_doc_qs.filter(source=source)

    for edi_record in edi_doc_qs:
        edi_record.process()
