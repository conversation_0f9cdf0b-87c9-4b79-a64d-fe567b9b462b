from django.test import SimpleTestCase
from unittest.mock import Mock, patch
from dateutil.parser import parse, ParserError
from vantedge.edi.parse.clm import convert_field


@patch("vantedge.edi.parse.clm.datetime")
class ConvertFieldTest(SimpleTestCase):
    def setUp(self):
        self.test = convert_field
        self.field = "timestamp"
        self.data = ""
        return super().setUp()

    def test_valid(self, datetime):
        datetime.now.return_value = parse("202207270800")

        self.data = "03080352"
        result = self.test(self.field, self.data)
        self.assertEqual(parse(f"2022{self.data}"), result)

        self.data = "031005"
        result = self.test(self.field, self.data)
        self.assertEqual(parse(f"2022{self.data}".ljust(12, "0")), result)

        self.data = "2022.03.02 15:39:00"
        result = self.test(self.field, self.data)
        self.assertEqual(parse(f"{self.data}"), result)

        self.data = "2022-03-02"
        result = self.test(self.field, self.data)
        self.assertEqual(parse(f"{self.data}"), result)

    def test_invalid(self, datetime):
        datetime.now.return_value = parse("202207270800")

        self.data = "00000000"
        result = self.test(self.field, self.data)
        # self.assertRaises(ParserError, self.test, **{"data": self.data, "field": self.field})
        self.assertEqual(None, result)

        self.data = "9999.12.31 23:59:00"
        result = self.test(self.field, self.data)
        # self.assertRaises(ParserError, self.test, **{"data": self.data, "field": self.field})
        self.assertEqual(None, result)


"""     def test_past_events(self, datetime):
        datetime.now.return_value = parse("202207270800")

        self.data = "12250630"
        result = self.test(self.field, self.data)
        self.assertEqual(parse(f"2021{self.data}"), result) """

