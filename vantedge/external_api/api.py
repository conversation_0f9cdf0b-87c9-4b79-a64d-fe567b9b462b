from ninja import <PERSON><PERSON><PERSON>
from rest_framework import status
from rest_framework import routers

from .utils import PaginationError
from .auth import APIHeaderToken

from .location import location_router, location_product_router
from .report import ExternalReportViewSet
from .ticket import ExternalTBossV3TicketViewset
from .ticket_invoice import ExternalTicketInvoiceViewset
from .file_upload import UploaderViewset
from .clm_sync import CLMSyncViewSet


api = NinjaAPI(
    auth=APIHeaderToken(),
    urls_namespace="external_api_v2",  # Changed namespace to be more distinct
    version="2.0.0",  # Changed version to be more distinct
    title="External API"
)

api.add_router("/location/", location_router)
api.add_router("/location/", location_product_router)


@api.exception_handler(PaginationError)
def pagination_error(request, exc):
    return api.create_response(
        request,
        {"message": "Limit paramter out of range"},
        status=status.HTTP_416_REQUESTED_RANGE_NOT_SATISFIABLE,
    )


router = routers.SimpleRouter()
router.register(r"report", ExternalReportViewSet)
router.register(r"ticket", ExternalTBossV3TicketViewset)
router.register(r"ticket-invoice", ExternalTicketInvoiceViewset, basename='external-invoice')
router.register(r'uploader', UploaderViewset, basename='uploader')

router.register(r"clm_sync", CLMSyncViewSet, basename="clm-sync")
router_urls = router.urls
