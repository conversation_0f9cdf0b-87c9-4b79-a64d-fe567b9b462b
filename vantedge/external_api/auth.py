from ninja.security import django_auth, APIKeyHeader
from django.contrib.auth import get_user_model

from rest_framework.authtoken.models import Token

User = get_user_model()


class InvalidToken(Exception):
    pass


class APIHeaderToken(APIKeyHeader):
    param_name = "Authorization"

    def authenticate(self, request, token, **kwargs):
        if token:
            try:
                user_token = Token.objects.get(key=token.replace("Token ", ""))
                user = user_token.user

                if user.is_active:
                    return user

            except (User.DoesNotExist, Token.DoesNotExist):
                # Return None to indicate authentication failure
                # This will result in a 401 Unauthorized response
                return None
