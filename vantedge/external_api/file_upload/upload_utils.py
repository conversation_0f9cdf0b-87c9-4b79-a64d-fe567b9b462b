import os
from enum import Enum

from azure.core.exceptions import ResourceExistsError
from azure.storage.blob import BlobServiceClient
from django.conf import settings
from rest_framework.response import Response


class UploadTo(Enum):
    LOCAL = 'local'
    AZURE = 'azure'


class BaseSelectedUploader:
    ''' Abstract class for file uploaders. '''
    REQUIRED_PERMISSION = None
    FILE_FIELD_NAME = None

    def validate_file(self, file):
        pass

    def set_file_path_and_name(self, request):
        pass

    def upload_file(self, directory, file, upload_to=UploadTo.AZURE):
        '''
        This method is used to upload the file to the specified location.
        If azure is not available, then the file will be uploaded to local storage.
        '''
        print("upload_file")
        uploader = UploadMethod()
        if upload_to == UploadTo.AZURE:
            if settings.DEFAULT_FILE_STORAGE == "storages.backends.azure_storage.AzureStorage":
                return uploader.upload_file_to_blob(directory, file)

        # Fall back to local storage
        upload_to = UploadTo.LOCAL
        if upload_to == UploadTo.LOCAL:
            return uploader.upload_file_locally(file, directory)


class UploadMethod:
    def upload_file_to_blob(self, directory, file):
        ''' Provide a method to upload the file to Azure Blob Storage. '''
        try:
            if hasattr(settings, 'AZURE_CONNECTION_STRING') and hasattr(settings, 'AZURE_CONTAINER'):
                connection_string = settings.AZURE_CONNECTION_STRING
                container_name = settings.AZURE_CONTAINER
            elif (hasattr(settings, 'AZURE_ACCOUNT_NAME') and
                  hasattr(settings, 'AZURE_ACCOUNT_KEY') and
                  hasattr(settings, 'AZURE_CONTAINER')):
                connection_string = (
                    f'DefaultEndpointsProtocol=https;AccountName={settings.AZURE_ACCOUNT_NAME};'
                    f'AccountKey={settings.AZURE_ACCOUNT_KEY};'
                )
                container_name = settings.AZURE_CONTAINER
            else:
                raise ValueError("Azure connection Misconfigured. Please check the settings.py file.")

            blob_service_client = BlobServiceClient.from_connection_string(connection_string)
            blob_client = blob_service_client.get_blob_client(
                container=container_name,
                blob=directory + '/' + file.name)
            try:
                if file.open():
                    blob_client.upload_blob(file)
                else:
                    with file.open() as f:
                        blob_client.upload_blob(f)
            except ResourceExistsError:
                if file.name.startswith('_duplicate_'):
                    return Response({'error': 'File already exists'}, status=409)
                file.name = '_duplicate_' + file.name
                return self.upload_file_to_blob(directory, file)
            return Response({'message': 'File uploaded successfully'}, status=201)
        except Exception as e:
            print("Error uploading file", e)
            return Response({'error': 'Error uploading file'}, status=500)

    def upload_file_locally(self, file, directory):
        ''' Provide a method to upload the file to local storage. '''
        try:
            FILE_PATH = settings.MEDIA_ROOT + "/" + directory
            if not os.path.exists(FILE_PATH):
                os.makedirs(FILE_PATH)

            FILE_PATH = FILE_PATH + "/" + file.name
            with open(FILE_PATH, 'wb+') as destination:
                for chunk in file.chunks():
                    destination.write(chunk)
        except Exception as e:
            print("Error uploading file", e)
            return Response({'error': 'Error uploading file'}, status=500)

        return Response({'message': 'File uploaded successfully'}, status=201)