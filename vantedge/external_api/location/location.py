from django.core.exceptions import ValidationError
from django.db import transaction
from django.db.models import Q
from django.db.models.deletion import ProtectedError
from django.db.utils import IntegrityError
from django.http import HttpResponse
from pydantic.error_wrappers import ValidationError as PydanticValidationError
from ninja import Router
from ninja.pagination import paginate
from typing import List, Union
from vant_tboss.models import LocationData, AddressData, TDGData, StorageData
from vantedge.external_api.schemas import (
    SuccessResponse,
    ErrorResponse,
    BulkErrorResponse,
    LocationSchema,
    CreateLocationSchema,
    CreateLocationProductSchema,
    UpdateLocationSchema,
    PatchLocationSchema,
)
from vantedge.external_api.utils import (
    get_terminalboss_company,
    check_pagination,
    get_instance_identifier,
)
from vantedge.tboss.models import (
    Location,
    Product,
    LocationProduct,
    Company,
)
from pprint import pformat

router = Router()

LOCATION_TYPE_CHOICES = dict(Location.LocationType.choices)
LOCATION_TYPE_LABEL_CHOICE = {
    label: key for key, label in LOCATION_TYPE_CHOICES.items()
}


class PydanticErrorNotesWrapper(PydanticValidationError):
    def __str__(self) -> str:
        error_msg = super().__str__()
        if self.__notes__:
            error_msg = "\n".join([error_msg] + self.__notes__)
        return error_msg


# --------------------- location-list
@router.get(
    "",
    response=List[LocationSchema],
    summary="Paginated list view for Locations.",
)
@paginate(pass_parameter="pagination_info")
def get_locations(request, **kwargs):
    info = kwargs["pagination_info"]
    check_pagination(info)

    company = get_terminalboss_company(request)

    if not company:
        return Location.objects.none()

    return Location.objects.filter(dbreplication_client_company=company)


@router.post(
    "",
    summary="Endpoint to create one or many locations. If no_transaction=1 is provided on the URL, partial success will be allowed and only return successfully created locations.",
    response={
        200: Union[LocationSchema, List[Union[LocationSchema, BulkErrorResponse]]],
        400: Union[BulkErrorResponse, List[BulkErrorResponse]],
    },
)
def bulk_create_locations(
    request,
    payload: Union[CreateLocationSchema, List[CreateLocationSchema]],
    no_transaction: bool = False,
):
    company = get_terminalboss_company(request)
    # TODO provide server? or can locations be shared
    tboss_server = company.dbreplicationserver_set.filter(name='Wheelhouse').first()
    extra_kwargs = {
        "dbreplication_client_company": company,
        "created_by_server": tboss_server,
        "modified_by_server": tboss_server,
    }

    response_code = 200
    returnval = None
    errors = []
    if isinstance(payload, CreateLocationSchema):
        try:
            returnval, created = update_or_create_location(payload, **extra_kwargs)
        except Exception as e:
            return HttpResponse([str(e)], status=400)

    # partial success
    elif no_transaction:
        returnval = []
        for loc_data in payload:
            try:
                with transaction.atomic():
                    location, created = update_or_create_location(
                        loc_data, **extra_kwargs
                    )
                    returnval.append(location)
            except Exception as e:
                returnval.append(
                    {"error": str(e), "payload": loc_data.dict(exclude_unset=True)}
                )
                errors.append(e)

        # if all buckets of the payload failed, set status code = 400
        if len(errors) == len(returnval):
            response_code = 400

    # rollback on error
    else:
        with transaction.atomic():
            try:
                returnval = []
                for loc_data in payload:
                    location, created = update_or_create_location(
                        loc_data, **extra_kwargs
                    )
                    returnval.append(location)
            except Exception as e:
                return HttpResponse([str(e)], status=400)

    return response_code, returnval


@router.patch(
    "",
    summary="Endpoint to update or create one or many locations. If no_transaction=1 is provided on the URL, partial success will be allowed and only return successfully updated/created locations.",
    response={
        200: Union[LocationSchema, List[Union[LocationSchema, BulkErrorResponse]]],
        400: Union[BulkErrorResponse, List[BulkErrorResponse]],
    },
)
def patch_locations(
    request,
    payload: Union[UpdateLocationSchema, List[UpdateLocationSchema]],
    no_transaction: bool = False,
):
    return bulk_update_locations(request, payload, no_transaction)


@router.put(
    "",
    summary="Endpoint to update or create one or many locations. If no_transaction=1 is provided on the URL, partial success will be allowed and only return successfully updated/created locations.",
    response={
        200: Union[LocationSchema, List[Union[LocationSchema, BulkErrorResponse]]],
        400: Union[BulkErrorResponse, List[BulkErrorResponse]],
    },
)
def put_locations(
    request,
    payload: Union[UpdateLocationSchema, List[UpdateLocationSchema]],
    no_transaction: bool = False,
):
    return bulk_update_locations(request, payload, no_transaction)


def bulk_update_locations(
    request,
    payload: Union[UpdateLocationSchema, List[UpdateLocationSchema]],
    no_transaction: bool = False,
):
    company = get_terminalboss_company(request)
    # TODO provide server? or can locations be shared
    tboss_server = company.dbreplicationserver_set.filter(name='Wheelhouse').first()
    extra_kwargs = {
        "dbreplication_client_company": company,
        "created_by_server": tboss_server,
        "modified_by_server": tboss_server,
    }
    response_code = 200
    errors = []
    returnval = None
    if isinstance(payload, UpdateLocationSchema):
        try:
            returnval, created = update_or_create_location(payload, **extra_kwargs)
        except Exception as e:
            return HttpResponse([str(e)], status=400)

    # partial success
    elif no_transaction:
        returnval = []
        for loc_data in payload:
            try:
                with transaction.atomic():
                    location, created = update_or_create_location(
                        loc_data, **extra_kwargs
                    )
                    returnval.append(location)
            except Exception as e:
                returnval.append(
                    {"error": str(e), "payload": loc_data.dict(exclude_unset=True)}
                )
                errors.append(e)

        # if all buckets of the payload failed, set status code = 400
        if len(errors) == len(returnval):
            response_code = 400

    # rollback on error
    else:
        try:
            returnval = []
            with transaction.atomic():
                for loc_data in payload:
                    location, created = update_or_create_location(
                        loc_data, **extra_kwargs
                    )
                    returnval.append(location)
        except Exception as e:
            return HttpResponse([str(e)], status=400)

    return response_code, returnval


# --------------------- location-detail
@router.get(
    "{location_id}/",
    response={
        200: LocationSchema,
        404: ErrorResponse,
    },
    summary="Retrieve a location (by code or id).",
)
def get_location(request, location_id: str):
    query = None
    company = get_terminalboss_company(request)

    query = Q(**{"id": get_instance_identifier(Location, location_id, company)})
    try:
        l = Location.objects.get(query)
    except Location.DoesNotExist:
        return 404, {"error": "Location Not Found"}

    return 200, l


@router.patch(
    "{location_id}/",
    response={
        200: LocationSchema,
        404: ErrorResponse,
    },
    summary="Update a location (by code or id).",
)
def patch_location(request, location_id: str, payload: PatchLocationSchema):
    query = None
    company = get_terminalboss_company(request)

    query = Q(**{"id": get_instance_identifier(Location, location_id, company)})
    try:
        l = Location.objects.get(query)
    except Location.DoesNotExist:
        return 404, {"error": "Location Not Found"}

    try:
        l = patch_update_location(l, company, payload)
    except Exception as e:
        return 404, {"error": f"{e}"}

    return 200, l


@router.delete(
    "{location_id}/",
    response={
        200: SuccessResponse,
        400: ErrorResponse,
        404: ErrorResponse,
    },
    summary="Delete a location (by code or id).",
)
def delete_location(request, location_id: str):
    query = None
    company = get_terminalboss_company(request)

    query = Q(**{"id": get_instance_identifier(Location, location_id, company)})

    try:
        l = Location.objects.get(query)
        l.delete()
    except ProtectedError:
        return 400, {"error": "Location is referenced elsewhere and cannot be deleted"}
    except Location.DoesNotExist:
        return 404, {"error": "Location Not Found"}

    return 200, {"message": "successfully deleted the location"}


# --------------------- helpers
def query_related_model(cls, tenant, lookup_kwargs: dict, lookup_fields=["id", "code"]):
    lookup_by = {k: v for k, v in lookup_kwargs.items() if k in lookup_fields and v}
    try:
        if not lookup_by:
            raise ValidationError(f"No lookup fields provided")

        queryset = cls._meta.default_manager.filter(dbreplication_client_company=tenant)
        return queryset.get(**lookup_by)
    except Exception as e:
        raise Exception(
            f"Unable to query related model {cls.__name__} with lookup_fields {lookup_by}. Error: {e}"
        )


def update_or_create_location(
    payload: Union[CreateLocationSchema, UpdateLocationSchema], **kwargs
):
    # only populate values provided by user
    payload_data = {**payload.dict(exclude_unset=True), **kwargs}
    tenant = kwargs.get("dbreplication_client_company")
    location_products = []
    companies = []

    if "location_products" in payload_data:
        location_products = payload_data.pop("location_products", [])

    if "company_set" in payload_data:
        companies = dict_list_to_obj_list(
            payload_data.pop("company_set", []), Company, tenant
        )

    location_data = LocationData()
    if "data" in payload_data:
        data = payload_data.get("data")
        data["address"] = data.get("address") or AddressData()
        data["tdg"] = data.get("tdg") or TDGData()
        data["storage"] = data.get("storage") or StorageData()
        data["bsw_overage"] = data.get("bsw_overage") or 0
        location_data = LocationData(**data)

        payload_data["data"] = location_data

    if "location_type" in payload_data:
        payload_data["location_type"] = LOCATION_TYPE_LABEL_CHOICE.get(
            payload_data.get("location_type")
        )

    if "parent" in payload_data:
        payload_data["parent"] = query_related_model(
            Location, tenant=tenant, lookup_kwargs=payload_data.get("parent", {}) or {}
        )

    # perform operation based on schema type
    try:
        # put/patch
        if isinstance(payload, UpdateLocationSchema):
            lookup_kwargs = {"dbreplication_client_company": tenant}
            payload_id = None
            if "id" in payload_data:
                payload_id = payload_data.pop("id")

            if payload_id:

                if "created_by_server" in payload_data:
                    # pop the created_by_server if instance already exists
                    created_by_server = payload_data.pop("created_by_server")

                # looking up by id throws error if it doesnt exist
                lookup_kwargs.update(pk=payload_id)
                created = False
                try:
                    location = Location.objects.get(Q(**lookup_kwargs))
                except Location.DoesNotExist as e:
                    raise Exception(f"{e} query: (id={payload_id})")
                for k, v in payload_data.items():
                    setattr(location, k, v)
                location.save()
            else:
                lookup_kwargs.update(code=payload_data.get("code"))

                location_qs = Location.objects.filter(Q(**lookup_kwargs))

                if not location_qs.exists():
                    # if the lookup fails,
                    # validate CreationSchema for new creation of instance
                    try:
                        # only populate values provided by user
                        input_data = payload.dict(exclude_unset=True)
                        CreateLocationSchema(**input_data)
                    except PydanticValidationError as e:
                        error = PydanticErrorNotesWrapper(
                            errors=e.raw_errors, model=e.model
                        )
                        error.add_note(f"payload: {pformat(input_data)}")
                        raise error
                elif "created_by_server" in payload_data:
                    # pop the created_by_server if location already exists
                    created_by_server = payload_data.pop("created_by_server")

                location, created = location_qs.update_or_create(
                    **lookup_kwargs, defaults=payload_data
                )
        # post
        else:
            created = True
            payload_data["associated_to_site"] = False
            location = Location.objects.create(**payload_data)
    except IntegrityError:
        raise ValidationError(
            f'Unable to create location. Location with code {payload_data.get("code")} already exists.'
        )

    for location_product_data in location_products:
        kwargs["location"] = location
        location_product, location_product_created = update_or_create_location_product(
            location_product_data, **kwargs
        )

    if companies:
        location.company_set.set(companies)

    return location, created


def update_or_create_location_product(payload: dict, **kwargs):
    payload_data = {**payload, **kwargs}

    payload_id = None
    if "id" in payload_data:
        payload_id = payload_data.pop("id")

    tenant = kwargs.get("dbreplication_client_company")

    if "product" in payload_data:
        payload_data["product"] = query_related_model(
            Product, tenant=tenant, lookup_kwargs=payload_data.get("product", {}) or {}
        )

    try:
        lookup_kwargs = {
            "dbreplication_client_company": tenant,
            "location": payload_data.get("location"),
        }

        if payload_id:
            if "created_by_server" in payload_data:
                # pop the created_by_server if location product already exists
                created_by_server = payload_data.pop("created_by_server")

            lookup_kwargs.update(pk=payload_id)
            created = False
            # looking up by id throws error if it doesnt exist
            try:
                location_product = LocationProduct.objects.get(Q(**lookup_kwargs))
            except LocationProduct.DoesNotExist as e:
                raise Exception(f"{e} query: (id={payload_id})")
            for k, v in payload_data.items():
                setattr(location_product, k, v)
            location_product.save()
        else:
            lookup_kwargs.update(product=payload_data.get("product"))
            location_product_qs = LocationProduct.objects.filter(Q(**lookup_kwargs))
            if not location_product_qs.exists():
                # if the lookup fails,
                # validate CreationSchema for new creation of instance
                try:
                    CreateLocationProductSchema(**payload)
                except PydanticValidationError as e:
                    error = PydanticErrorNotesWrapper(
                        errors=e.raw_errors, model=e.model
                    )
                    error.add_note(f"payload: {pformat(payload)}")
                    raise error
            elif "created_by_server" in payload_data:
                # pop the created_by_server if location already exists
                created_by_server = payload_data.pop("created_by_server")

            location_product, created = location_product_qs.update_or_create(
                **lookup_kwargs, defaults=payload_data
            )

        return location_product, created

    except IntegrityError:
        product = payload_data["product"]
        location = kwargs.get("location")
        raise ValidationError(
            f"Unable to associate product {product} with location {location} as a relation already exists"
        )


def patch_update_location(
    location: Location, company: Company, payload: PatchLocationSchema
):
    """
    Update location with payload
    """
    tenant = company
    # only populate values provided by user
    payload_data = payload.dict(exclude_unset=True)

    location_data = LocationData()
    if "data" in payload_data:
        data = payload_data.get("data")
        data["address"] = data.get("address") or AddressData()
        data["tdg"] = data.get("tdg") or TDGData()
        data["storage"] = data.get("storage") or StorageData()
        data["bsw_overage"] = data.get("bsw_overage") or 0
        location_data = LocationData(**data)

        payload_data["data"] = location_data

    if "location_type" in payload_data:
        payload_data["location_type"] = LOCATION_TYPE_LABEL_CHOICE.get(
            payload_data.get("location_type")
        )

    if "parent" in payload_data:
        payload_data["parent"] = query_related_model(
            Location, tenant=tenant, lookup_kwargs=payload_data.get("parent", {}) or {}
        )

    if "company_set" in payload_data:
        companies = payload_data.get("company_set")
        payload_data["company_set"] = dict_list_to_obj_list(companies, Company, tenant)

    for k, v in payload_data.items():
        if k == "company_set":
            location.company_set.set(v)
        else:
            setattr(location, k, v)

    location.save()

    return location


def dict_list_to_obj_list(dict_list, cls, tenant):
    # [dict,] -> [Model Instance,]
    """
    Converts given list of entity dicts (e.g { company_code..., company_id... })
    to list of entities (e.g [Company,])
    """
    return [
        query_related_model(cls, tenant=tenant, lookup_kwargs=related_entity)
        for related_entity in dict_list
    ]
