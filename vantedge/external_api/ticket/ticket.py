from rest_framework.viewsets import ReadOnlyModelViewSet
from rest_framework import status
from rest_framework.response import Response
from rest_framework.pagination import LimitOffsetPagination
from rest_framework.permissions import IsAuthenticated

from vantedge.tboss.views.mixins.viewsets import TBossCompanyMixin, SyncLogTimeMixin
from vantedge.tboss.views.ticket import TicketFilter
from vantedge.tboss.serializers import TicketExternalSerializer
from vantedge.tboss.models import Ticket


class ForcePaginatedReadOnlyModelViewSet(ReadOnlyModelViewSet):
    def list(self, request, *args, **kwargs):
        limit = request.query_params.get("limit")
        try:
            limit = int(limit)
        except:
            limit = None

        if limit is None or limit > 100:
            return Response(
                "limit paramter out of range",
                status=status.HTTP_416_REQUESTED_RANGE_NOT_SATISFIABLE,
            )
        return super().list(request, *args, **kwargs)


class ExternalTBossV3TicketViewset(
    SyncLogTimeMixin, TBossCompanyMixin, ForcePaginatedReadOnlyModelViewSet
):
    serializer_class = TicketExternalSerializer
    pagination_class = LimitOffsetPagination
    permission_classes = [IsAuthenticated]
    filterset_class = TicketFilter
    queryset = Ticket.objects.select_related().all()

    def get_serializer_context(self):
        context = super().get_serializer_context()
        context["utz"] = utz = self.request.user.company.applicationconfiguration.company_universal_timezone
        return context
