from django.urls import reverse
from django.template import Template, Context
from django.http.response import HttpResponse
from django.conf import settings
from django.core.files import File
from django.contrib.contenttypes.models import ContentType
from django.utils.timezone import get_current_timezone, now

from rest_framework.viewsets import GenericViewSet
from rest_framework.renderers import TemplateHTMLRenderer
from rest_framework.decorators import action
from rest_framework.response import Response
from pydantic import BaseModel

from vantedge.tboss.views.ticket_invoice import PrintableInvoiceViewsetMixin
from vantedge.util.notifications.email import send_invoice_approval_responded


class ExternalTicketInvoiceViewset(GenericViewSet, PrintableInvoiceViewsetMixin):
    template_name = 'ticket_invoice.html'
    authentication_classes = []

    def get_queryset(self):
        from vantedge.tboss.models import TicketInvoice
        return TicketInvoice.objects.all()

    @action(methods=["GET"], detail=False, renderer_classes=[TemplateHTMLRenderer])
    def invoice_approval_page(self, request):
        ticket_invoice, unique_identifier = self.get_ticket_invoice(request)

        if not ticket_invoice:
            return Response({})

        (balance_due,
         charges_table,
         sub_total,
         tax,
         ticket_type,
         top_table,
         total_oil_volume,
         total_solid_volume,
         total_water_volume,
         total_quantity) = self.create_ticket_invoice_context(ticket_invoice)

        url_approve_invoice = self.reverse_action("approve-invoice")

        context = {
            'ticket_invoice': ticket_invoice,
            'top_table': top_table,
            'charges_table': charges_table,
            'sub_total': sub_total,
            'tax': tax,
            'balance_due': balance_due,
            'ticket_type': ticket_type,
            'total_solid_volume': total_solid_volume,
            'total_water_volume': total_water_volume,
            'total_oil_volume': total_oil_volume,
            'total_quantity': total_quantity,
            'unique_identifier': unique_identifier,
            'url_approve_invoice': url_approve_invoice,
            'invoice_pdf': f'{self.reverse_action("print-invoice", kwargs={"pk": ticket_invoice.id})}'
        }

        return Response(context)

    @staticmethod
    def create_ticket_invoice_context(ticket_invoice):
        from vantedge.tboss.models import Ticket, ServiceContractTicket

        service_contract_tickets = ServiceContractTicket.objects.filter(ticket_invoice=ticket_invoice)
        tickets = Ticket.objects.filter(id__in=[ticket.ticket.id for ticket in service_contract_tickets])
        locations = ''

        for location in ticket_invoice.locations.all():
            locations += location.name + ', '

        top_table = dict(
            customer_name=ticket_invoice.customer.name,
            producer_name=ticket_invoice.producer.name,
            facility_name=ticket_invoice.created_by_server.name,
            contract_name=ticket_invoice.service_contract.name,
            contact_name=ticket_invoice.data.contacts[0].contact_name
            if ticket_invoice.data.contacts
            and ticket_invoice.data.contacts[0].contact_name != ''
            else '_',
            invoice_date=ticket_invoice.data.invoice_date.date()
            if ticket_invoice.data.invoice_date != ''
            else '_',
            due_date=ticket_invoice.data.due_date
            if ticket_invoice.data.due_date != ''
            else '_',
            contact_email=ticket_invoice.data.contacts[0].contact_email
            if ticket_invoice.data.contacts
            and ticket_invoice.data.contacts[0].contact_email != ''
            else '_',
            billing_address=ticket_invoice.data.billing_address
            if ticket_invoice.data.billing_address != ''
            else '_',
            po_number=ticket_invoice.data.po_number
            if ticket_invoice.data.po_number is not None and ticket_invoice.data.po_number != ''
            else '_',
            payment_terms=ticket_invoice.data.payment_terms
            if ticket_invoice.data.payment_terms != ''
            else '_',
            locations=locations
        )

        (charges_table,
         ticket_type,
         total_oil_volume,
         total_solid_volume,
         total_water_volume,
         total_quantity) = ticket_invoice.create_charges_table(tickets)

        sub_total = ticket_invoice.data.total_cost
        tax = ticket_invoice.data.tax
        balance_due = ticket_invoice.data.balance_due

        return (balance_due,
                charges_table,
                sub_total,
                tax,
                ticket_type,
                top_table,
                total_oil_volume,
                total_solid_volume,
                total_water_volume,
                total_quantity)

    @staticmethod
    def get_ticket_invoice(request):
        from vantedge.tboss.models import TicketInvoice

        unique_identifier = request.GET.get("unique_identifier")
        ticket_invoice = TicketInvoice.find_invoice(unique_identifier)

        return ticket_invoice, unique_identifier

    @action(methods=["POST"], detail=False)
    def approve_invoice(self, request):
        from vantedge.tboss.models import TicketInvoice
        from vantedge.attachment.models import Attachment
        unique_identifier = request.data.get("unique_identifier")
        accepted = request.data.get("accepted")
        note = request.data.get("note")
        attachment = request.data.get("attachment")
        attachment = None if not isinstance(attachment, File) else attachment
        accepted = True if accepted == 'true' else False

        ticket_invoice = TicketInvoice.find_invoice(unique_identifier)
        if not ticket_invoice:
            return Response(status=400)

        if ticket_invoice.data.status not in ['Approval Sent']:
            return Response({"message": f"Invoice is in {ticket_invoice.data.status} status"}, status=400)

        if accepted:
            ticket_invoice.set_approved(note)
        elif accepted is False:
            ticket_invoice.set_rejected(note)

        if attachment:
            if attachment.size > 25 * 1024 * 1024:
                pass
            else:
                attachment_name = attachment.name.split('.')
                attachment_name.insert(
                    -1, f'-(External)-${now().astimezone(get_current_timezone()).strftime("%Y%m%d-%H%M")}.pdf'
                )
                attachment.name = "".join(attachment_name[0:-1])
                Attachment(
                    content_type=ContentType.objects.get_for_model(ticket_invoice),
                    object_id=ticket_invoice.id,
                    file=attachment,
                    uploaded_by=ticket_invoice.created_by
                ).save()

        context = dict(
            status=ticket_invoice.data.status,
            code=ticket_invoice.code,
            note=note,
            has_attachement='Yes' if attachment else 'No',
            invoice_url=request.build_absolute_uri(f"/company/terminalboss/v3/ticket-invoice/{ticket_invoice.id}")
        )

        approval_request = ticket_invoice.find_approval_request(unique_identifier)
        if approval_request:
            send_invoice_approval_responded(approval_request.requester, context)
        return Response()

    @action(methods=["GET"], detail=False, renderer_classes=[TemplateHTMLRenderer])
    def invoice_payment_page(self, request):
        ticket_invoice, unique_identifier = self.get_ticket_invoice(request)

        if not ticket_invoice:
            return Response({})

        (balance_due,
         charges_table,
         sub_total, tax,
         ticket_type,
         top_table,
         total_oil_volume,
         total_solid_volume,
         total_water_volume,
         total_quantity) = self.create_ticket_invoice_context(ticket_invoice)

        context = {
            'ticket_invoice': ticket_invoice,
            'top_table': top_table,
            'charges_table': charges_table,
            'sub_total': sub_total,
            'tax': tax,
            'balance_due': balance_due,
            'ticket_type': ticket_type,
            'total_solid_volume': total_solid_volume,
            'total_water_volume': total_water_volume,
            'total_oil_volume': total_oil_volume,
            'total_quantity': total_quantity,
            'unique_identifier': unique_identifier,
            'invoice_pdf': f'{self.reverse_action("print-invoice", kwargs={"pk": ticket_invoice.id})}'
        }

        return Response(context)
