import re
from django.core.exceptions import ObjectDoesNotExist
from django.db.models import Model

UUID_PATTERN = re.compile(r"^[\da-f]{8}-([\da-f]{4}-){3}[\da-f]{12}$")
MAX_PAGINATION_LIMIT = 100


class PaginationError(Exception):
    pass


def is_uuid(string):
    return UUID_PATTERN.match(string)


def check_pagination(pagination_info: dict):
    try:
        limit = int(pagination_info.limit)
    except:
        limit = None

    if limit is None or limit > MAX_PAGINATION_LIMIT:
        raise PaginationError()


def get_terminalboss_company(request):
    try:
        return request.auth.company.terminalboss
    except ObjectDoesNotExist:
        return None


def get_instance_id_by_code(cls: Model, code: str, company):
    """
    Return the id (uuid) of the instance of `cls` matching the
    given code.
    """
    try:
        return cls._meta.default_manager.get(
            code=code, dbreplication_client_company=company
        ).id
    except ObjectDoesNotExist:
        return None


def get_instance_identifier(cls: Model, id_or_code: str, company):
    """
    Return the identifier for a given instance (by id or code)
    for the given terminalboss company.
    """
    if is_uuid(id_or_code):
        uuid = id_or_code
        return uuid
    else:
        code = id_or_code
        return get_instance_id_by_code(cls, code, company)
