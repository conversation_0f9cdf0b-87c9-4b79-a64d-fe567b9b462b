from django.db.models import Q
from rest_framework import serializers

from vantedge.wheelhouse.models import TankCar, HopperCar
from .models.form import Form, Field, FormData

class FormSerializer(serializers.ModelSerializer):

    class Meta:
        model = Form
        fields = "__all__"
        read_only_fields = ["company"]

    def validate(self, attrs):
        user = self.context["request"].user
        super().validate(attrs)

        if self.instance:
            if self.instance.company.company != user.company:
                raise serializers.ValidationError("User company not match!")
        else:
            attrs["company"] = user.company.forms

        return attrs

class FieldSerializer(serializers.ModelSerializer):
    class Meta:
        model = Field
        fields = "__all__"

    def to_representation(self, instance):
        data = super().to_representation(instance)

        field_name = instance.name.lower()

        # schema section
        if instance.get_schema_type():
            data["schema"] = {
                field_name : {
                    "type": instance.get_schema_type() 
                }
            }
            for field in ["enum", "maximum", "minmum", "description", "default_value_type"]:
                if getattr(instance, field):
                    data["schema"][field_name][field] = getattr(instance, field)

            if instance.get_schema_type() == "string" and instance.format:
                data["schema"][field_name]["format"] = instance.format

        # schema ui section
        data["schema_ui"] = {
            "type": instance.get_schema_ui_type()
        }
        if instance.get_schema_ui_type() == "label":
            data["schema_ui"]["text"] = instance.description
        else:
            data["schema_ui"]["scope"] = f"#/properties/{field_name}"

        for field in ["label"]:
            if getattr(instance, field):
                data["schema_ui"][field] = getattr(instance, field)
        
        return data


class FormDataSerializer(serializers.ModelSerializer):
    form = FormSerializer()

    # :to="{name:'form:open', query: { formStructureId: 1 }}"
    class Meta:
        model = FormData
        fields = "__all__"
        read_only_fields = ["created", "modified"]

    def get_car_data(self, data):
        return data.get("rail_car", None)

    def validate_rail_car(self, data):
        if not (
            data.get("car_mark") or
            data.get("car_numner") or
            data.get("car_type")
        ):
            return False
        return True
     
    def to_internal_value(self, data):
        form_instance = None
        if form_data := data.get("form", None):
            try:
                form_instance = Form.objects.get(id=form_data["id"])
            except:
                raise serializers.ValidationError({"form structre": "form structure does not exist"})

        rail_car_data = self.get_car_data(data)

        data =  super().to_internal_value(data)
        if form_instance:
            data["form_instance"] = form_instance

        if rail_car_data:
            data["rail_car_data"] = rail_car_data

        return data

    def validate(self, attrs):
        user = self.context["request"].user
        super().validate(attrs)

        if self.instance:
            if self.instance.form.company.company != user.company:
                raise serializers.ValidationError("User company not match!")
        elif attrs["form"]["company"] != user.company.forms:
            raise serializers.ValidationError("User company not match!")

        if rail_car_data := attrs.get("rail_car_data", None):
            print('validating cat cdaf')
            if self.validate_rail_car(rail_car_data):
                raise serializers.ValidationError("car data is not complete")
        return attrs

    def get_or_create_related_rail_car(self, form, car_data):
        car_type = car_data["car_type"]
        if car_type == "tank car":
            model = TankCar
        elif car_type == 'hopper car':
            model = HopperCar

        car, created = model.objects.get_or_create(
            car_mark= car_data["car_mark"],
            car_number=car_data["car_number"],
        )
        return car

    def create(self, validated_data):
        validated_data.pop("form")
        rail_car_data = validated_data.pop("rail_car_data", None)
        form_instance = validated_data.pop("form_instance")
        if form_instance.is_rail_car_inspection_form:
            if not rail_car_data:
                raise serializers.ValidationError({"car data": "car data is not compelet!"})

        form = FormData(form=form_instance, **validated_data)
        form.save()

        if form_instance.is_rail_car_inspection_form:
            self.get_or_create_related_rail_car(form, rail_car_data)

        return form

    def update(self, instance, validated_data):
        validated_data.pop("form", None)
        validated_data.pop("form_instance", None)

        return super().update(instance, validated_data)
