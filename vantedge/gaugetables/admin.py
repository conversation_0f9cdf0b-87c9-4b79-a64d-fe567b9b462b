from typing import Any
from django.contrib import admin
from django import forms
from vantedge.gaugetables.models import GaugeTable
from .import_guage import read_csv_data
from django.core.exceptions import ValidationError


class GaugeForm(forms.ModelForm):
    gauge_file = forms.FileField(
        required=False,
        help_text="CSV file, 'Outage Level';'Outage volume' unit should be USG",
    )

    class Meta:
        model = GaugeTable
        fields = "__all__"

    def clean(self) -> dict[str, Any]:
        cleaned_data = super().clean()
        if cleaned_data.get("gauge_file"):
            try:
                read_csv_data(cleaned_data["gauge_file"])
            except Exception as e:
                raise ValidationError(f"gauge file parsing error: {e}")

    def save(self, commit: bool = ...) -> Any:
        if self.cleaned_data.get("gauge_file"):
            self.cleaned_data["gauge_file"].seek(0)
            data = read_csv_data(self.cleaned_data["gauge_file"])

            for key, value in data.items():
                setattr(self.instance, key, value)

        return super().save(commit)


@admin.register(GaugeTable)
class GaugeTableAdmin(admin.ModelAdmin):
    list_display = ["car_mark", "car_number", "capacity", "tare_weight", "is_scraped"]
    search_fields = ["car_number"]
    form = GaugeForm
