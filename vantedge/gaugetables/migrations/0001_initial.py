# Generated by Django 3.2.11 on 2022-04-05 02:05

from django.db import migrations, models
import functools
import vantedge.gaugetables.models
import schema_field.fields


class Migration(migrations.Migration):

    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="GaugeTable",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("car_mark", models.CharField(blank=True, max_length=5, null=True)),
                ("car_number", models.CharField(blank=True, max_length=15, null=True)),
                ("capacity", models.FloatField(blank=True, null=True)),
                (
                    "gauge_table",
                    schema_field.fields.JSONSchemedField(
                        blank=True,
                        default=vantedge.gaugetables.models.GaugeTableSchema,
                        encoder=functools.partial(
                            schema_field.fields.JSONSchemedEncoder,
                            *(),
                            **{
                                "schema": (
                                    vantedge.gaugetables.models.GaugeTableSchema,
                                )
                            }
                        ),
                        null=True,
                        schema=(vantedge.gaugetables.models.GaugeTableSchema,),
                    ),
                ),
                ("is_scraped", models.BooleanField(default=None, null=True)),
            ],
            options={"unique_together": {("car_mark", "car_number")}},
        )
    ]
