# Generated by Django 4.2.7 on 2023-12-06 15:43

from django.db import migrations
import django.utils.timezone
import model_utils.fields


class Migration(migrations.Migration):

    dependencies = [
        ('gaugetables', '0004_gaugetable_scrape_url_gaugetable_tare_weight'),
    ]

    operations = [
        migrations.AddField(
            model_name='gaugetable',
            name='created',
            field=model_utils.fields.AutoCreatedField(default=django.utils.timezone.now, editable=False, verbose_name='created'),
        ),
        migrations.AddField(
            model_name='gaugetable',
            name='modified',
            field=model_utils.fields.AutoLastModifiedField(default=django.utils.timezone.now, editable=False, verbose_name='modified'),
        ),
    ]
