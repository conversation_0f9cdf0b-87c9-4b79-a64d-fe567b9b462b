from django.db import models
from schema_field.fields import JSONS<PERSON>med<PERSON><PERSON>
from typing import List, Optional
from pydantic import BaseModel

from model_utils.models import TimeStampedModel

class Outage(BaseModel):
    outage: float = 0
    volume: float = 0


class GaugeTableSchema(BaseModel):
    gauge_table: List[Outage] = []
    metric_gauge_table: List[Outage] = []
    gauge_number: str = ""


class GaugeTable(TimeStampedModel):
    car_mark = models.CharField(max_length=5)
    car_number = models.CharField(max_length=15)
    capacity = models.FloatField(blank=True, null=True)
    metric_capacity = models.FloatField(blank=True, null=True)
    tare_weight = models.IntegerField(blank=True, null=True, help_text="car tare weight in lb")
    gauge_table = JSONSchemedField(
        schema=GaugeTableSchema, default=GaugeTableSchema, blank=True, null=True
    )
    is_scraped = models.BooleanField(null=True, default=None)
    scrape_url = models.TextField(blank=True, null=True) # url of successful scrape


    class Meta:
        unique_together = ("car_mark", "car_number")

    @property
    def max_allowed_weight_on_rail(self):
        return 263000

    @property
    def max_allowed_net_weight(self):
        if self.tare_weight is None:
            return None

        return self.max_allowed_weight_on_rail - self.tare_weight
