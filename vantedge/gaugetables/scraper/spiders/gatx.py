import scrapy

class GATXSpider(scrapy.Spider):
    name = 'gatx'
    allowed_domains = ['gatx.com']
    start_urls = ['https://www.gatx.com/gauge-table/']

    def __init__(self, car_mark, car_number, metric, yield_output_func, *args, **kwargs):
        self.car_mark = car_mark
        self.car_number = car_number
        self.metric = metric
        self.yield_output_func = yield_output_func
        super(GATXSpider, self).__init__(*args, **kwargs)

    def parse(self, response):
        yield scrapy.FormRequest(str(f"https://www.gatx.com//api//gauge-csv-export?car_mark={self.car_mark.lstrip('0')}&car_numbers={self.car_number}&table_numbers=&unit={'Liters' if self.metric else 'Gallons'}&submit_type=download"),
            callback=self.process_gauge_table,
            dont_filter=True,
        )

    def process_gauge_table(self, response):
        if len(response.body.decode('utf-8').split('\n')) < 20:
            return
        result_rows = response.body.decode("utf-8").split("\n")[8:-3]

        result = {
            "car_number": self.car_mark + " " + self.car_number,
            "gauge_table_number": response.body.decode("utf-8")
            .split("\n")[5]
            .split(',')[1],
            "capacity": response.body.decode("utf-8")
            .split("\n")[2]
            .split(',')[1]
            .replace(",", ""),
            "tare_weight": int(float(response.body.decode("utf-8").split("\n")[4].split(',')[-1].replace(",",""))),
            "scrape_url": response.url,
            "gauge_table": [],
        }
        for row in result_rows:
            row = row.split(',')
            if len(row) < 2:
                break
            for i, entry in enumerate(row[::2]):
                result['gauge_table'].append(
                    {
                        "outage": row[2*i],
                        "volume": row[(2*i)+1]
                    }
                )
        result['gauge_table'] = sorted(result['gauge_table'], key=lambda x: float(x['outage']))
        self.yield_output_func(result)
        return result

