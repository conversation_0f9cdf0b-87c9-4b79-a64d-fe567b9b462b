import requests
import csv

class MRCRailSpider:
    def __init__(self, car_mark, car_number, metric, *args, **kwargs):
        self.car_mark = car_mark
        self.car_number = car_number
        self.metric = metric

    def crawl(self):
        s = requests.Session()
        s.headers.update({'User-Agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/102.0.5005.61 Safari/537.36'})
        response = s.get(f"https://www.mrc-rail.com/wp-content/themes/DH-Blank-Theme/tankgaugedata/filtercsvdata.php?realCarId={self.car_mark}&carNumber={self.car_number}&measurementType={'liter' if self.metric else 'gallon'}&downloadtype=CSV")
        return self.parse_csv(response)

    def parse_csv(self, response):
        if len(response.text) < 10:
            return
        reader = csv.reader(response.text.splitlines())
        result = {
            "car_number": self.car_mark + " " + self.car_number,
            "gauge_table_number": '',
            "capacity": '',
            "tare_weight": "",
            "scrape_url": f"https://www.mrc-rail.com/wp-content/themes/DH-Blank-Theme/tankgaugedata/filtercsvdata.php?realCarId={self.car_mark}&carNumber={self.car_number}&measurementType={'liter' if self.metric else 'gallon'}&downloadtype=CSV",
            "gauge_table": [],
        }
        headers = next(reader)
        for row in reader:
            result["gauge_table"].append({
                'outage': row[4],
                'volume': row[7] if self.metric else row[5]
            })
        result['capacity'] = str(int(max(float(row['volume']) for row in result['gauge_table'])))
        return result
