import requests
from bs4 import BeautifulSoup as bs

class TEIXSpider:
    def __init__(self, car_mark, car_number, metric, *args, **kwargs):
        self.car_mark = car_mark
        self.car_number = car_number
        self.metric = metric

    def crawl(self):
        s = requests.Session()
        s.headers.update({'User-Agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/102.0.5005.61 Safari/537.36'})
        response = s.post("http://www.teix.com/ViewTable.asp", data={
            'CarInit': self.car_mark,
            'CarNum': self.car_number,
            'Submit': 'Gauge Table'
        })
        if 'Invalid' in response.text or 'Not Available' in response.text:
            print (response.text)
            return

        soup = bs(response.text, features='lxml')
        table = soup.findAll("table")[1]
        table_data = []
        
        for row in table.findAll('tr'):
            row_data = []
            for l in row.findAll('td'):
                if l.find('sup'):
                    l.find('sup').extract()
                row_data.append(l.getText().strip())
            table_data.append(row_data)
        result = {
            "car_number": self.car_mark + " " + self.car_number,
            "gauge_table_number": table_data[0][3],
            "capacity":  str(int(float(table_data[1][3].replace(',', ''))*3.785)) if self.metric else table_data[1][3].replace(',', ''),
            "gauge_table": [],
            "scrape_url": response.url
        }
        for row in table_data[4:]:
            for i, cell in enumerate(row):
                if i % 2 == 0 and cell:
                    result['gauge_table'].append({
                        'outage': row[i],
                        'volume': str(int(float(row[i+1].replace(',', ''))*3.785)) if self.metric else row[i+1].replace(',','')
                    })
        result['gauge_table'] = sorted(result['gauge_table'], key=lambda x: float(x['outage']))
        return result
