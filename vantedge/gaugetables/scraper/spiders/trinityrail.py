import scrapy
import urllib
from scrapy.utils.response import open_in_browser

class Trinity<PERSON><PERSON><PERSON>er(scrapy.Spider):
    name = "trinityrail"
    allowed_domains = ["tasonline.gotilc.com"]
    start_urls = ["https://tasonline.gotilc.com/GTPublicWeb/MainWeb/GageSelect.aspx/"]

    def __init__(self, car_mark, car_number, metric, yield_output_func, *args, **kwargs):
        self.car_mark = car_mark
        self.car_number = car_number
        self.metric = metric
        self.yield_output_func = yield_output_func
        super(TrinityrailSpider, self).__init__(*args, **kwargs)








    def parse(self, response):
        
        yield scrapy.FormRequest(
            "https://tasonline.gotilc.com/GTPublicWeb/MainWeb/GageSelect.aspx/",
            formdata={
                "ctl00$TRNContentPlaceHolder$ddCarMark_FilterSec": self.car_mark,
                "__VIEWSTATE": response.css(
                    "input#__VIEWSTATE::attr(value)"
                ).extract_first(),
            },
            callback=self.parse_number,
            dont_filter=True,
        )



    def parse_number(self, response):
        payload = {
            "ctl00$TRNContentPlaceHolder$ddCarMark_FilterSec":  self.car_mark,
            "ctl00$TRNContentPlaceHolder$txtCarNo_FilterSec": self.car_number,
            "ctl00$TRNContentPlaceHolder$btnDataToText": "Download to Text File",
            "ctl00$TRNContentPlaceHolder$rblUnits_UnitsSec:": "rdoUnits4" if self.metric else "rdoUnits1",
            "ctl00$TRNContentPlaceHolder$rblType_TypeSec": "rdoType1",
            "ctl00$TRNContentPlaceHolder$RCE_ClientState": "320,38",
        }
        yield scrapy.FormRequest.from_response(
            response,
            formdata=payload,
            callback=self.parse_captcha,
            dont_filter=True,
        )

    def parse_captcha(self, response):


        headers = {
            "Cookie": response.request.headers.get('Cookie'),
            "Host": 'tasonline.gotilc.com'
        }
        payload = {
            "ctl00$TRNContentPlaceHolder$hdnClipBoardData": "",
            "ctl00$TRNContentPlaceHolder$hdnCarNumArch": "",
            "ctl00$TRNContentPlaceHolder$hdnNotFoundCars": "",
            "ctl00$TRNContentPlaceHolder$hdnbtnText": "",
            "ctl00$TRNContentPlaceHolder$rblGTType_Filter": "CarMark",
            "ctl00$TRNContentPlaceHolder$ddCarMark_FilterSec": self.car_mark,
            "ctl00$TRNContentPlaceHolder$lseCarMark_ClientState": "",
            "ctl00$TRNContentPlaceHolder$txtCarNumberFrom": "",
            "ctl00$TRNContentPlaceHolder$txtCarNumberTo": "",
            "ctl00$TRNContentPlaceHolder$txtCarNo_FilterSec": self.car_number,
            "ctl00$TRNContentPlaceHolder$RCE_ClientState": "320,38",
            "ctl00$TRNContentPlaceHolder$rblUnits_UnitsSec:": "rdoUnits4" if self.metric else "rdoUnits1",
            "ctl00$TRNContentPlaceHolder$rblType_TypeSec": "rdoType1",
            "ctl00$TRNContentPlaceHolder$txtCaptcha": response.css('[id="TRNContentPlaceHolder_txtCaptcha"]::attr(value)').extract_first(),
            "ctl00$TRNContentPlaceHolder$txtverification": response.css('[id="TRNContentPlaceHolder_txtCaptcha"]::attr(value)').extract_first(),
            "ctl00$TRNContentPlaceHolder$btnDataToText": "Download to Text File",
            "ctl00$TRNContentPlaceHolder$txtEmailPDF": "",
            "ctl00$TRNContentPlaceHolder$hdnCarRangeList": "",
            # "__EVENTTARGET": "",
            # "__EVENTARGUMENT": "",
            # "__LASTFOCUS": "",
            # "__VIEWSTATE": response.css(
            #     "input#__VIEWSTATE::attr(value)"
            # ).extract_first(),
            # "__VIEWSTATEGENERATOR": response.css(
            #     "input#__VIEWSTATEGENERATOR::attr(value)"
            # ).extract_first(),
            # "__VIEWSTATEENCRYPTED": ""
        }
        yield scrapy.FormRequest.from_response(
            response,
            formdata=payload,
            callback=self.parse_download,
            headers=headers,
            dont_filter=True,
        )

    def parse_download(self, response):
        if 'Please provide Valid Car Number Information.' in response.body.decode("utf-8"):
            return
        
        headers = {
            "Cookie": response.request.headers.get('Cookie'),
            "Host": 'tasonline.gotilc.com'
        }

        yield scrapy.FormRequest(
            "https://tasonline.gotilc.com/GTPublicWeb/MainWeb/GaugeTableReport.aspx",
            callback=self.process_gauge_table,
            dont_filter=True,
            headers=headers,
            meta={"rec_response": response},
        )

    def process_gauge_table(self, response):

        result_rows = [
            row
            for row in response.body.decode("utf-8").split("\r\n")
            if len(row.split()) == 3
        ]

        result = {
            "car_number": self.car_mark + " " + self.car_number,
            "gauge_table_number": response.body.decode("utf-8")
            .split("\r\n")[0]
            .split()[8],
            "capacity": response.body.decode("utf-8")
            .split("\r\n")[0]
            .split()[16]
            .replace(",", ""),
            "tare_weight": int(float(response.body.decode("utf-8").split("\r\n")[0].split()[29].replace(",", ""))),
            "scrape_url": "https://tasonline.gotilc.com/GTPublicWeb/MainWeb/GageSelect.aspx/",
            "gauge_table": [],
        }
        for row in result_rows:
            row = row.split()
            result["gauge_table"].append(
                {
                    "outage": row[1].strip().replace(",", ""),
                    "volume": row[2].strip().replace(",", ""),
                }
            )
        self.yield_output_func(result)
        return result
