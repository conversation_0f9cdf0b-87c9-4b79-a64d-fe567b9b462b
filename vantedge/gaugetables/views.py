import logging
from django.db.models.functions import Cast
from django.db.models import IntegerField

from rest_framework.mixins import CreateModelMixin
from rest_framework.response import Response
from rest_framework.viewsets import GenericViewSet
from rest_framework.decorators import action
from rest_framework.permissions import IsAuthenticated
from rest_framework import status
from django_q.tasks import async_task

from .scraper.scrape_gauge import scrape_gauge_table
from .models import GaugeTable
from .serializers import GaugeTableSerializer

logger = logging.getLogger(__name__)


def log_gauge_table_error(exception, car):
    logger.exception(f"Scrape Gauge Table Error. Exception: {exception}. Car: {car}.")


def update_tare_weight(car_mark, car_number):
    retrieved_gauge_table = GaugeTable.objects.get(
        car_mark=car_mark, car_number=car_number
    )

    try:
        gauge_table_dict = scrape_gauge_table(car_mark, car_number, False)
        retrieved_gauge_table.scrape_url = gauge_table_dict.get("scrape_url")
        retrieved_gauge_table.tare_weight = gauge_table_dict.get("tare_weight") or None
        retrieved_gauge_table.save()
    except Exception as e:
        log_gauge_table_error(e, car=f"{car_mark}{car_number}")
        return f"Scrape failed {e}"
    return "Tare Update Success"

def save_gauge_table(car_mark, car_number, force=False):
    retrieved_gauge_table, created = GaugeTable.objects.get_or_create(
        car_mark=car_mark, car_number=car_number
    )

    if retrieved_gauge_table.is_scraped:
        if force:  # check if gauge table is blank
            retrieved_gauge_table.is_scraped = None
        else:
            return "Table already exists"

    try:
        gauge_table_dict = scrape_gauge_table(car_mark, car_number, False)
        metric_gauge_table_dict = scrape_gauge_table(car_mark, car_number, True)
        retrieved_gauge_table.capacity = float(
            str(gauge_table_dict["capacity"]).replace(",", "")
        )
        retrieved_gauge_table.metric_capacity = float(
            str(metric_gauge_table_dict["capacity"]).replace(",", "")
        )
        retrieved_gauge_table.gauge_table = {
            "gauge_number": gauge_table_dict["gauge_table_number"],
            "gauge_table": gauge_table_dict["gauge_table"],
            "metric_gauge_table": metric_gauge_table_dict["gauge_table"],
        }
        retrieved_gauge_table.tare_weight = gauge_table_dict.get("tare_weight") or None
        retrieved_gauge_table.scrape_url = gauge_table_dict.get("scrape_url")

        retrieved_gauge_table.is_scraped = True
        retrieved_gauge_table.save()
        return "Scrape success"
    except Exception as e:
        log_gauge_table_error(e, car=f"{car_mark}{car_number}")
        retrieved_gauge_table.is_scraped = False
        retrieved_gauge_table.save()
        return f"Scrape failed {e}"


def validate_then_save_gauge_table(car_mark, car_number, user):
    retrieved_gauge_table = GaugeTable.objects.filter(
        car_mark=car_mark, car_number=car_number
    ).first()

    if retrieved_gauge_table:
        save_gauge_table(car_mark, car_number)
    else:
        try:
            # attempt fetches only imperial
            scrape_result = scrape_gauge_table(car_mark, car_number, False)
        except Exception as e:
            scrape_result = {}

        if scrape_result:
            # saves both imperial and metric
            save_gauge_table(car_mark, car_number)
        else:
            print(f"{user} is looking for gauge data {car_mark}{car_number} !!!")

class GaugeTableViewset(CreateModelMixin, GenericViewSet):
    serializer_class = GaugeTableSerializer
    queryset = GaugeTable.objects.all()

    def get_serializer(self, *args, **kwargs):
        if self.action == "create":
            return super().get_serializer(many=True, *args, **kwargs)

        return super().get_serializer(*args, **kwargs)

    @action(detail=False, methods=["GET"], permission_classes=[IsAuthenticated])
    def rail_car_gauge_table(self, request):
        car_number = request.query_params.get("carNumber", "")
        car_mark = request.query_params.get("carMark", "").upper()

        try:
            if len(car_number) > 6:
                raise ValueError

            if len(car_mark) > 4:
                raise ValueError

            if not car_mark.isalpha():
                raise ValueError

            int_car_number = int(car_number)
        except ValueError:
            return Response(
                {
                    "gauge_table_number": "",
                    "capacity_usg": "",
                    "gauge_table_usg": [],
                    "capacity_litre": "",
                    "gauge_table_litre": [],
                    "tare_weight": None,
                    "max_allowed_net_weight": None,
                    "car_exists": False,
                },
                status=status.HTTP_404_NOT_FOUND,
            )

        retrieved_gauge_table = (
            GaugeTable.objects.annotate(
                car_number_int=Cast("car_number", IntegerField())
            )
            .filter(car_mark=car_mark, car_number_int=int_car_number)
            .first()
        )

        if retrieved_gauge_table and retrieved_gauge_table.is_scraped is True:
            return Response(
                {
                    "gauge_table_number": retrieved_gauge_table.gauge_table.gauge_number,
                    "capacity_usg": retrieved_gauge_table.capacity,
                    "gauge_table_usg": [
                        {"outage_level": row.outage, "outage_volume": row.volume}
                        for row in retrieved_gauge_table.gauge_table.gauge_table
                    ],
                    "capacity_litre": retrieved_gauge_table.metric_capacity,
                    "gauge_table_litre": [
                        {"outage_level": row.outage, "outage_volume": row.volume}
                        for row in retrieved_gauge_table.gauge_table.metric_gauge_table
                    ],
                    "tare_weight": retrieved_gauge_table.tare_weight,
                    "max_allowed_net_weight": retrieved_gauge_table.max_allowed_net_weight,
                    "car_exists": True,
                }
            )

        # Queues to validate car and update gauge table
        # validate_then_save_gauge_table(car_mark, car_number, request.user)

        async_task(
            validate_then_save_gauge_table,
            car_mark,
            car_number,
            request.user,
            group="Fetch Gauge Table",
            task_name=f"Checking for {car_mark}{car_number}'s gauge table",
        )

        return Response(
            {
                "gauge_table_number": "",
                "capacity_usg": "",
                "gauge_table_usg": [],
                "capacity_litre": "",
                "gauge_table_litre": [],
                "tare_weight": None,
                "max_allowed_net_weight": None,
                "car_exists": bool(retrieved_gauge_table),
            },
            status=status.HTTP_404_NOT_FOUND,
        )

    def create(self, request, *args, **kwargs):
        if not self.request.user.has_perm("gaugetables.add_gaugetable"):
            return Response(status=status.HTTP_403_FORBIDDEN)

        return super().create(request, *args, **kwargs)

    @action(detail=False, methods=["POST"], permission_classes=[IsAuthenticated])
    def get_max_permitted_filling(self, request):
        """
        result is list of tuples which
        (first value is temperature,
        second value is with safety factor 0,
        and next value is with given safety factor)
        """
        from vant_calcs.mpms import MPMS
        from vant_calcs.tools import lng_load_target_factor
        from vantedge.util.units.convert import convert_unit

        unit = request.data.get("unit")
        try:
            relative_density = float(request.data.get("relative_density"))
            temperature_min = float(request.data.get("temperature_min"))
            temperature_max = float(request.data.get("temperature_max"))
            safety_margin = float(request.data.get("safety_margin"))
            target_temp = float(request.data.get("target_temp"))
        except:
            return Response(
                ["Values are not valid"], status=status.HTTP_400_BAD_REQUEST
            )

        try:
            correction_factor_at_15c = MPMS().ctl_24e(relative_density, 59)
        except:
            return Response(
                ["Values are not calculable"], status=status.HTTP_400_BAD_REQUEST
            )

        if temperature_min == temperature_max:
            if unit == "Metric":
                temperature_f = convert_unit(temperature_min, "CELSIUS", "FAHRENHEIT")
            else:
                temperature_f = temperature_min

            try:
                correction_factor = MPMS().ctl_24e(relative_density, temperature_f)
                # print(relative_density, temperature_f, target_temp, correction_factor)
                result = (
                    temperature_min,
                    round(
                        lng_load_target_factor(
                            relative_density, temperature_f, target_temp, 0
                        ),
                        5,
                    ),
                    None
                    if safety_margin is None
                    else round(
                        lng_load_target_factor(
                            relative_density, temperature_f, target_temp, safety_margin
                        ),
                        5,
                    ),
                    correction_factor,
                    correction_factor_at_15c,
                )
            except:
                return Response(
                    ["Values are not calculable"], status=status.HTTP_400_BAD_REQUEST
                )
            return Response([result])

        result = []
        for i in range(int(temperature_min), int(temperature_max) + 1):
            if unit == "Metric":
                temperature_f = convert_unit(i, "CELSIUS", "FAHRENHEIT")
            else:
                temperature_f = i
            try:
                correction_factor = MPMS().ctl_24e(relative_density, temperature_f)
                result.append(
                    (
                        i,
                        round(
                            lng_load_target_factor(
                                relative_density, temperature_f, target_temp, 0
                            ),
                            5,
                        ),
                        None
                        if safety_margin is None
                        else round(
                            lng_load_target_factor(
                                relative_density,
                                temperature_f,
                                target_temp,
                                safety_margin,
                            ),
                            5,
                        ),
                        correction_factor,
                        correction_factor_at_15c,
                    )
                )
            except:
                return Response(
                    ["Values are not calculable"], status=status.HTTP_400_BAD_REQUEST
                )
        return Response(result)
