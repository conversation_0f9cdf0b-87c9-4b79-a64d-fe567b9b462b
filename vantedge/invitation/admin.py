from django.contrib import admin
from django.http import HttpResponseRedirect
from django.db.models import <PERSON><PERSON><PERSON><PERSON>

from .models.company import InvitationCompany
from .models.order import Order, Nomination
from .models.invitation import WheelhouseInvitation

from .widgets import JSONWidget


@admin.register(InvitationCompany)
class InvitationCompanyAdmin(admin.ModelAdmin):
    pass


@admin.register(WheelhouseInvitation)
class InvitationAdmin(admin.ModelAdmin):
    list_display = [
        "__str__",
        "company",
        "status",
    ]

    formfield_overrides = {JSONField: {"widget": JSONWidget}}
    # https://books.agiliq.com/projects/django-admin-cookbook/en/latest/custom_button.html
    change_form_template = "invitation/admin/change_form.html"

    def response_change(self, request, obj):
        if "_update-permissions-for-users" in request.POST:
            # NOTE:
            # invitation-partner-permission methods have their own status checks
            obj.prepare_partnership()
            obj.dissolve_partnership()

            self.message_user(
                request, f"{obj.invitee_company} users permissions updated."
            )
            return HttpResponseRedirect(".")

        return super().response_change(request, obj)


@admin.register(Order)
class OrderAdmin(admin.ModelAdmin):
    pass


@admin.register(Nomination)
class NominationAdmin(admin.ModelAdmin):
    pass
