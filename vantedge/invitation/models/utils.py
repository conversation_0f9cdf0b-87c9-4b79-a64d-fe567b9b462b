from django.db import models
from django.contrib.auth.models import Permission, Group
from datetime import date

from vantedge.users.models import User, Company, ApplicationConfiguration
from vantedge.wheelhouse.models import (
    RailShipment,
    Party,
    Contract,
    RailShippingSchedule,
)
from vantedge.wheelhouse.models.location import Facility
from vantedge.terminalboss.models import TicketView as V2Ticket, CompanyTable
from vantedge.tboss.models.proxies import Ticket as V3Ticket, Company as V3Company
import logging

logger = logging.getLogger(__name__)


# ---------------------


def get_model_by_field(field):
    if field in get_party_fields():
        return Party
    if field in get_facility_fields():
        return Facility


def get_entity_fields(a: models.Model, b: models.Model) -> list[str]:
    """
    Return field_names of B in A
    """
    fields = [
        f.name
        for f in a._meta.fields
        if (f.related_model is b)
    ]

    return fields


def get_party_fields() -> list[str]:
    """Return field_names of `Party` in `RailShippingSchedule`."""
    return get_entity_fields(RailShippingSchedule, Party)


def get_facility_fields() -> list[str]:
    """Return field_names of `Facility` in `RailShippingSchedule`."""
    return get_entity_fields(RailShippingSchedule, Facility)


def get_field_by_name(fields, name):
    # [ModelField] str -> ModelField
    """
    Return field from FIELDS by NAME or None.

    FIELDS must be enumerable with each FIELD having `name` property.
    """
    field = next((f for f in fields if f.name == name), None)

    return field


# --------------------- requests & urls


def get_frontend_url(request):
    """
    Return the URL for WH frontend based on incoming
    REQUEST.
    """
    return f"{request.scheme}://{request.META['HTTP_HOST']}"


# --------------------- resource-predicates


def is_v2_ticket(resource: str) -> bool:
    return resource == "terminalboss_v2_ticket"


def is_v3_ticket(resource: str) -> bool:
    return resource == "terminalboss_v3_ticket"


def is_rail_shipment(resource: str) -> bool:
    return resource == "rail_shipment"


def is_rail_contract(resource: str) -> bool:
    return resource == "rail_contract"


# --------------------- filter-builders


def get_resource_party(resource: str, party_id: int) -> Company | Party:
    """
    Returns the matching party based on the resource.
    """
    if is_v2_ticket(resource):
        return CompanyTable.objects.get(id=party_id)

    elif is_v3_ticket(resource):
        return V3Company.objects.get(id=party_id)

    return Party.objects.get(id=party_id)


def get_resource_parties(resource: str, party_ids: list[int]) -> Company | Party:
    """
    Returns the matching parties based on the resource.
    """
    parties = []

    for p_id in party_ids:
        party = get_resource_party(resource, p_id)
        parties.append(party)

    return parties


def get_resource_field_key(resource: str, field: str) -> str:
    """
    Returns the field-key based on the resource.
    """
    if is_v2_ticket(resource):
        return f"{field}_name"

    if is_v3_ticket(resource):
        # NOTE:
        # V3Tickets could have different forms for ticket.data
        # a map function from ticket[by-type] -> field can be injected here.
        return f"data__additional_fields__{field}_id"

    if is_rail_shipment(resource):
        return f"{field}"


def get_resource_party_value(resource: str, party: Party | CompanyTable) -> Party | str :
    """
    Returns the party-value based on the resource.

    TerminalBOSS V2 queries are based on 'name' so this is important.
    """
    if is_v2_ticket(resource):
        return party.name

    if is_v3_ticket(resource):
        return party.id # based on UUID

    if is_rail_shipment(resource):
        return party


def get_party_field_constraint_filters(
    resource: str, party: Party | CompanyTable | V3Company, fields: list[str]
) -> models.Q:
    """
    Returns party field constraints as Q object.

    The field constraints for each party are captured as
    OR separated Q nodes.

    -> (p2 as care_party or monitoring_party)
    """
    query = models.Q()

    for field in fields:
        field_key = get_resource_field_key(resource, field)
        party_value = get_resource_party_value(resource, party)
        query |= models.Q(**{field_key: party_value})

    return query


def get_parties_field_constraint_filters(
    resource: str, parties: list[Party | CompanyTable | V3Company], fields: list[str]
) -> models.Q:
    """
    Returns parties field constraints as Q object.

    The field constraints for a group of parties are captured as OR
    separated Q nodes.

    r1 -> (p2 as care_party or monitoring_party) OR
    (p50 as care_party or monitoring_party)
    """
    query = models.Q()

    for party in parties:
        query |= get_party_field_constraint_filters(resource, party, fields)

    return query


def get_partner_party_filter(data: dict) -> models.Q:
    """
    Returns partner-party constraints as Q object.
    """
    resource = data.resource
    party_id = data.partner.party
    fields = data.partner.party_field_constraints
    party = get_resource_party(resource, party_id)

    query = get_party_field_constraint_filters(resource, party, fields)

    return query


def get_sharing_rules_filter(data: dict) -> models.Q:
    """
    Returns sharing-rules constraints as Q object.

    ---------
    PartiesData\n
    {
        "parties": [2, 50],
        "party_field_constraints": [
            "care_party",
            "monitoring_party"
        ]
    }\n
    *interpretation*\n
    This is one rule r1 that means a filter where p2 or p50 are care_party or monitoring_party.
    The resulting Q node will be:\n
    r1 -> (p2 as care_party or monitoring_party) OR (p50 as care_party or monitoring_party)

    ---------
    The total sharing rules filter becomes: r1 [... & rN]
    """
    resource = data.resource
    rules = data.rules

    rules_query = models.Q()
    for rule in rules:
        party_ids = rule.parties
        rule_parties = get_resource_parties(resource, party_ids)
        rule_fields = rule.party_field_constraints

        rule_query = models.Q()
        rule_query = get_parties_field_constraint_filters(
            resource, rule_parties, rule_fields
        )

        rules_query &= rule_query

    return rules_query


def get_partnership_filters(data: dict) -> models.Q:
    """
    Returns a Q object for the given data in a partnership.
    """
    if is_v3_ticket(data.resource):
        return get_sharing_rules_filter(data)

    partner_rules = get_partner_party_filter(data)
    sharing_rules = get_sharing_rules_filter(data)

    return partner_rules & sharing_rules


# --------------------- from perspective of inviter_company


def contract_is_shared(contract: Contract, company: Company) -> bool:
    """
    True if contract is shared with any partners
    based on any accepted rail-contract invitations.
    """
    is_shared = False

    if hasattr(company, 'invitation_company'):
        invitation_company = company.invitation_company
        invitations = invitation_company.invitations.filter(data__resource="rail_contract", status="Accepted")

        for invitation in invitations:
            if contract:
                contract_owner = contract.buyer or contract.seller
                is_shared = invitation.data.partner.party == contract_owner.id

                if is_shared:
                    break

    return is_shared


def is_expired_order(order) -> bool:
    """
    True if the order (contract associated with order)
    is expired.
    """
    _is_expired = True
    contract = order.contract

    if contract:
        today = date.today()
        _is_expired = contract.end_date < today

    return _is_expired



# --------------------- from perspective of invitee_company


def get_company_rail_shipment_invites(invitee_company: Company):
    # Company -> Qs[WheelhouseInvitation]
    """
    Return all accepted rail-shipment invites for the given company.
    """
    return invitee_company.invites.filter(status="Accepted").filter(
        data__resource="rail_shipment"
    )


def get_company_rail_shipment_partner_parties(invitee_company: Company):
    # Company -> Qs[Party]
    """
    Return the rail shipment party entities representing the given
    company's partnerships.
    """
    parties = None
    party_ids = get_company_rail_shipment_invites(invitee_company).values_list(
        "data__partner__party", flat=True
    )

    party_ids = list(map(int, party_ids))

    parties = Party.objects.filter(id__in=party_ids)

    return parties


def get_company_rail_contract_invites(invitee_company: Company):
    # Company -> Qs[WheelhouseInvitation]
    """
    Return all accepted rail-contract invites for the given company.
    """
    return invitee_company.invites.filter(status="Accepted").filter(
        data__resource="rail_contract"
    )


def get_company_rail_contract_partner_parties(invitee_company: Company):
    # Company -> Qs[Party]
    """
    Return the rail contract party entities representing the given
    company's partnerships.
    """
    parties = None
    party_ids = get_company_rail_contract_invites(invitee_company).values_list(
        "data__partner__party", flat=True
    )

    party_ids = list(map(int, party_ids))

    parties = Party.objects.filter(id__in=party_ids)

    return parties


def get_company_terminalboss_v2_ticket_invites(invitee_company: Company):
    # Company -> Qs[WheelhouseInvitation]
    """
    Return all accepted terminalboss-v2-ticket invites for the given company.
    """
    return invitee_company.invites.filter(status="Accepted").filter(
        data__resource="terminalboss_v2_ticket"
    )


def get_company_terminalboss_v2_ticket_partner_parties(invitee_company: Company):
    # Company -> Qs[CompanyTable]
    """
    Return the terminal boss v2 party entities representing the given
    company's partnerships.
    """
    parties = None
    party_ids = get_company_terminalboss_v2_ticket_invites(invitee_company).values_list(
        "data__partner__party", flat=True
    )

    party_ids = list(map(int, party_ids))

    parties = CompanyTable.objects.filter(id__in=party_ids)

    return parties


def get_inviter_client_companies(invitee_company: Company):
    """
    Return client companies for each partnership with INVITEE_COMPANY for
    V3 Sync entity (e.g Ticket).
    """
    inviter_client_companies = list()

    for invitation in get_company_terminalboss_v3_ticket_invites(invitee_company):
        inviter_company = invitation.company

        if hasattr(inviter_company, 'terminalboss'):
            inviter_client_companies.append(inviter_company.terminalboss)

    return inviter_client_companies


def get_company_terminalboss_v3_ticket_invites(invitee_company: Company):
    # Company -> Qs[WheelhouseInvitation]
    """
    Return all accepted terminalboss-v3-ticket invites for the given company.
    """
    return invitee_company.invites.filter(status="Accepted").filter(
        data__resource="terminalboss_v3_ticket"
    )


def get_company_terminalboss_v3_ticket_partner_parties(invitee_company: Company):
    # Company -> Qs[V3Company]
    """
    Return the terminal boss v3 party entities representing the given
    company's partnerships.
    """
    parties = None
    party_ids = get_company_terminalboss_v3_ticket_invites(invitee_company).values_list(
        "data__partner__party", flat=True
    )

    party_ids = list(party_ids)

    parties = V3Company.objects.filter(id__in=party_ids)

    return parties


# --------------------- partner-permission-updates


def permission_list_to_obj(permissions: list[str]) -> list[Permission]:
    # [str] -> [Permission]
    """Convert list of permission-strings to list of permission-objects."""
    result = []
    for permission in permissions:
        if "." in permission:
            app_label, codename = permission.split(".")
            permission = Permission.objects.get(
                codename=codename, content_type__app_label=app_label
            )
        else:
            permission = Permission.objects.get(codename=permission)

        result.append(permission)
    return result


def add_permissions_to_user_and_app_config(user: User, company: Company, permissions: list[str]):
    """
    Add permissions to USER, and to COMPANY's `app_config`.
    """
    for permission in permission_list_to_obj(permissions):
        user.user_permissions.add(permission)

    add_permissions_to_company_app_config(company, permissions)


def add_permissions_to_company_app_config(company: Company, permissions: list[str]):
    """Add permission to the given company's `app_config`"""
    company_app_config = ApplicationConfiguration.objects.get(company=company)

    for permission in permission_list_to_obj(permissions):
        company_app_config.available_permissions.add(permission)

    company_app_config.save()


def add_permissions_to_company_user_set(company: Company, permissions: list[str]):
    """
    Add permissions to each user in the `company`.

    Exclude superusers, and staff. For the same reason, does not
    affect app-config.
    """

    for user in company.user_set.exclude(
        models.Q(is_superuser=True) | models.Q(is_staff=True)
    ):
        for permission in permission_list_to_obj(permissions):
            user.user_permissions.add(permission)


def remove_permissions_from_user_and_app_config(user: User, company: Company, permissions: list[str]):
    """
    Remove permissions from USER, and from COMPANY's `app_config`.
    """

    for permission in permission_list_to_obj(permissions):
        user.user_permissions.remove(permission)

    remove_permissions_from_company_user_set(company, permissions)


def remove_permissions_from_company_app_config(
    company: Company, permissions: list[str]
):
    """
    Remove permission from the given company's `app_config`.
    """
    company_app_config = ApplicationConfiguration.objects.get(company=company)

    for permission in permission_list_to_obj(permissions):
        company_app_config.available_permissions.remove(permission)

    company_app_config.save()


def remove_permissions_from_company_user_set(company: Company, permissions: list[str]):
    """
    Remove permissions from each user in the `company`.

    Exclude superusers, staff, and users that are members of multiple companies. For
    the same reason, does not affect app-config.
    """

    for user in company.user_set.exclude(
        models.Q(is_superuser=True) | models.Q(is_staff=True)
    ):
        if user.assigned_companies.count() > 1:
            return

        for permission in permission_list_to_obj(permissions):
            user.user_permissions.remove(permission)


# --------------------- partner-group-updates


def group_list_to_obj(groups: list[str]) -> list[Group]:
    """Convert list of permission-strings to list of permission-objects."""
    result = []

    for group_name in groups:
        g = Group.objects.get(name=group_name)

        result.append(g)

    return result


def add_groups_to_user_and_app_config(user: User, company: Company, groups: list[str]):
    """
    Add groups to USER, and to COMPANY's `app-config`.
    """
    for g in group_list_to_obj(groups):
        user.groups.add(g)

    add_groups_to_company_app_config(company, groups)


def add_groups_to_company_app_config(company: Company, groups: list[str]):
    """Add group to the given company's `app_config`."""
    company_app_config = ApplicationConfiguration.objects.get(company=company)

    for g in group_list_to_obj(groups):
        company_app_config.available_groups.add(g)

    company_app_config.save()


def add_groups_to_company_user_set(company: Company, groups: list[str]):
    """
    Add groups to each user in the `company`.

    Exclude superusers and staff.
    """

    for user in company.user_set.exclude(
        models.Q(is_superuser=True) | models.Q(is_staff=True)
    ):
        for g in group_list_to_obj(groups):
            user.groups.add(g)


def remove_groups_from_user_and_app_config(user: User, company: Company, groups: list[str]):
    """
    Remove groups from USER, and from COMPANY's `app-config`.
    """
    for g in group_list_to_obj(groups):
        user.groups.remove(g)

    remove_groups_from_company_app_config(company, groups)


def remove_groups_from_company_app_config(
    company: Company, groups: list[str]
):
    """
    Remove group from the given company's `app_config`
    """
    company_app_config = ApplicationConfiguration.objects.get(company=company)

    for g in group_list_to_obj(groups):
        company_app_config.available_groups.remove(g)

    company_app_config.save()


def remove_groups_from_company_user_set(company: Company, groups: list[str]):
    """
    Remove groups from each user in the `company`.

    Exclude superusers, staff, and users that are members of multiple companies.
    """
    for user in company.user_set.exclude(
        models.Q(is_superuser=True) | models.Q(is_staff=True)
    ):
        if user.assigned_companies.count() > 1:
            return

        for g in group_list_to_obj(groups):
            user.groups.remove(g)


# --------------------- partner-resource-queries


def get_partner_terminalboss_v2_ticket_ids(company: Company) -> list[int]:
    # Company -> [V2Ticket-ID]
    """Returns IDs for v2 tickets shared with the given company."""

    invitations = get_company_terminalboss_v2_ticket_invites(company)

    if invitations.exists():
        filtered_resources = V2Ticket.objects.none()

        for invitation in invitations:
            invitation_data = invitation.data
            partnership_filters = get_partnership_filters(invitation_data)

            filtered_resources = filtered_resources._combinator_query(
                "union",
                V2Ticket.objects.filter(partnership_filters),
            )

        terminalboss_v2_parties__companies_query = (
            get_company_terminalboss_v2_ticket_partner_parties(company).values_list(
                "company_id", flat=True
            )
        )

        return (
            V2Ticket.objects.filter(
                company_id__in=terminalboss_v2_parties__companies_query
                # match linked terminal_boss_company only
            )
            .filter(id__in=filtered_resources.values("id"))
            .values_list("id", flat=True)
        )
    else:
        return V2Ticket.objects.none().values_list("id", flat=True)


def get_partner_terminalboss_v3_ticket_ids(company: Company) -> list[int]:
    # Company -> [V3Ticket-ID]
    """Returns IDs for v3 tickets shared with the given company."""

    invitations = get_company_terminalboss_v3_ticket_invites(company)

    if invitations.exists():
        filtered_resources = V3Ticket.objects.none()

        for invitation in invitations:
            invitation_data = invitation.data
            partnership_filters = get_partnership_filters(invitation_data)

            filtered_resources = filtered_resources._combinator_query(
                "union",
                V3Ticket.objects.filter(partnership_filters),
            )
        return (
            V3Ticket.objects.filter(
                dbreplication_client_company__in=get_company_terminalboss_v3_ticket_partner_parties(company).values_list("dbreplication_client_company", flat=True)
                # match linked terminal_boss_company only
            )
            .filter(id__in=filtered_resources.values("id"))
            .values_list("id", flat=True)
        )
    else:
        return V3Ticket.objects.none().values_list("id", flat=True)


def get_partner_rail_contract_ids(company: Company) -> list[int]:
    # Company -> [Contract-ID]
    """Returns IDs for contracts shared with given company."""
    invitations = get_company_rail_contract_invites(company)

    if invitations.exists():
        filtered_resources = Contract.objects.none()

        for invitation in invitations:
            invitation_data = invitation.data
            party = invitation_data.partner.party

            partnership_filters = models.Q(
                models.Q(**{"buyer": party}) | models.Q(**{"seller": party})
            )

            filtered_resources = filtered_resources._combinator_query(
                "union", Contract.objects.filter(partnership_filters)
            )

        return Contract.objects.filter(
            id__in=filtered_resources.values("id")
        ).values_list("id", flat=True)

    else:
        return Contract.objects.none().values_list("id", flat=True)


def get_partner_rail_shipment_ids(company: Company) -> list[int]:
    # Company -> [RailShipment-ID]
    """Returns IDs for shipments shared with given company."""
    invitations = get_company_rail_shipment_invites(company)

    if invitations.exists():
        filtered_resources = RailShipment.objects.none()

        for invitation in invitations:
            invitation_data = invitation.data
            partnership_filters = get_partnership_filters(invitation_data)

            filtered_resources = filtered_resources._combinator_query(
                "union", RailShipment.objects.filter(partnership_filters)
            )

        return RailShipment.objects.filter(
            id__in=filtered_resources.values("id")
        ).values_list("id", flat=True)

    else:
        return RailShipment.objects.none().values_list("id", flat=True)


# --------------------- partner-resource-contract-defaults


def get_entity_defaults(invitation_defaults: dict, entity_type: str) -> dict:
    """
    Return entity defaults given INVITATION_RULES and ENTITY_TYPE.

    {
        'monitoring_party': P_m
        'care_party': P_c
    }

    Where P_m & P_c are ids of Party instances from the inviter
    """
    d = {}

    for entity_data in invitation_defaults:

        if entity_data.type == entity_type:
            entity_id = entity_data.entity
            entity_field = entity_data.field

            if entity_field:
                d[entity_field] = entity_id

    return d


def get_contract_default_facilities_by_partnership(
    inviter_company: Company, invitee_company: Company
) -> dict:
    """
    Return all default facilities from THE contract-invite partnership between
    INVITER_COMPANY & INVITEE_COMPANY.
    """
    maybe_invite = get_company_rail_contract_invites(invitee_company).filter(
        company__company=inviter_company
    )

    default_facilities = {}

    if maybe_invite:
        invite = maybe_invite.first()
        defaults = invite.data.defaults

        default_facilities = get_entity_defaults(defaults, 'facility')

    return default_facilities


def get_contract_fleets_by_partnership(
    inviter_company: Company, invitee_company: Company
) -> list:
    """
    Return all fleets from THE contract-invite partnership between
    INVITER_COMPANY & INVITEE_COMPANY.
    """
    maybe_invite = get_company_rail_contract_invites(invitee_company).filter(
        company__company=inviter_company
    )

    fleets = []

    if maybe_invite:
        invite = maybe_invite.first()
        fleets = invite.data.fleets

    return fleets


def get_contract_default_parties_by_partnership(
    inviter_company: Company, invitee_company: Company
) -> dict:
    """
    Return all default parties from THE contract-invite partnership between
    INVITER_COMPANY & INVITEE_COMPANY.
    """
    maybe_invite = get_company_rail_contract_invites(invitee_company).filter(
        company__company=inviter_company
    )

    default_parties = {}

    if maybe_invite:
        invite = maybe_invite.first()
        defaults = invite.data.defaults

        default_parties = get_entity_defaults(defaults, 'party')

    return default_parties


def get_entity_defaults_by_partnership(
    inviter_company: Company, invitee_company: Company
) -> dict:
    data = {}

    party_defaults = get_contract_default_parties_by_partnership(inviter_company, invitee_company)
    facility_defaults = get_contract_default_facilities_by_partnership(inviter_company, invitee_company)

    for field, sellers_defaults in party_defaults.items():
        data[field] = sellers_defaults

    for field, sellers_defaults in facility_defaults.items():
        data[field] = sellers_defaults

    return data


def get_nomination_readonly_fields(order):
    # Order -> list[str]
    """Return all readonly fields that are set by inviter-company defaults."""
    s_fields = RailShippingSchedule._meta.get_fields()
    readonly_fields = []

    seller = order.get_seller()
    buyer = order.get_buyer()

    default_parties = get_contract_default_parties_by_partnership(seller, buyer)
    default_facilities = get_contract_default_facilities_by_partnership(seller, buyer)

    for f in s_fields:
        if f.name in default_parties.keys():
            readonly_fields.append(f)

        if f.name in default_facilities.keys():
            readonly_fields.append(f)

    return readonly_fields


# --------------------- partner-entity--pair-management

def get_facility_ids_from_order(order):
    ids = []

    for nomination in order.nomination_set.all():
        for field in get_facility_fields():
            ids.append(nomination.data.get(field))

    ids = set(ids)

    return ids


def get_party_ids_from_order(order):
    ids = []

    for nomination in order.nomination_set.all():
        for field in get_party_fields():
            ids.append(nomination.data.get(field))

    ids = set(ids)

    return ids
