import re
from django.db import models
from django.core.exceptions import ObjectDoesNotExist

from vantedge.terminalboss.models import CompanyTable
from vantedge.wheelhouse.models import Party
from vantedge.wheelhouse.models import RailShippingSchedule
from vantedge.invitation.models.utils import (
    is_expired_order,
    get_model_by_field,
    get_entity_fields,
    get_party_fields,
    get_facility_fields,
    get_contract_default_parties_by_partnership,
    get_contract_default_facilities_by_partnership,
)
from vantedge.wheelhouse.utils.shipment_rules import validate_rail_schedule

def check_resource_party_is_valid(company, resource, party_id):
    """
    Check if the inviter company is associated with the given resource party.
    """
    # pylint: disable=raise-missing-from
    if resource == "rail_shipment":
        try:
            Party.objects.get(company=company.wheelhouse, id=party_id)
        except Exception:
            raise AssertionError(
                f"Invalid invitation - {company.wheelhouse} does not have this Wheelhouse party."
            )

    if resource == "terminalboss_v2_ticket":
        try:
            CompanyTable.objects.get(
                company=company.terminalbosscompany,
                id=party_id,
            )
        except Exception:
            raise AssertionError(
                f"Invalid invitation - {company.terminalboss} does not have this TerminalBOSS V2 party."
            )

    # pylint: enable=raise-missing-from


def check_email_is_valid(email):
    regex = r"\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b"
    if email and re.fullmatch(regex, email):
        return email
    else:
        raise AssertionError("Invalid invitation - enter a valid email.")


def validate_invitation(company, email, resource, party_id):
    check_email_is_valid(email)
    check_resource_party_is_valid(company, resource, party_id)


# ------ nomination


def validate_forecast(nomination, order, forecast_payload):
    """
    Validate forecast against ...
    """
    check_not_expired(order)
    check_rail_schedule_info(forecast_payload)
    check_forecast_against_shipped_quantity(nomination, forecast_payload)
    check_forecast_against_contract_quantity(nomination, order, forecast_payload)
    check_nomination_entity(order, forecast_payload)


def check_not_expired(order):
    contract = order.contract

    assert not is_expired_order(order), f"Invalid forecast - The contract expired on {contract.end_date}."


def check_rail_schedule_info(forecast_payload):
    is_valid, msg = validate_rail_schedule(forecast_payload)

    assert is_valid, f"Invalid forecast - {msg}"

def check_nomination_entity(order, forecast_payload):
    """
    Check that nomination_entity belongs to invitee and is paired
    with inviter in ORDER.
    """
    seller = order.get_seller()
    buyer = order.get_buyer()

    default_parties = list(
        get_contract_default_parties_by_partnership(seller, buyer).keys()
    )
    default_facilities = list(
        get_contract_default_facilities_by_partnership(seller, buyer).keys()
    )
    default_fields = default_parties + default_facilities
    relevant_entity_fields = list(set(get_party_fields() + get_facility_fields()) - set(default_fields))

    for k, v in forecast_payload.items():
        if v and (k in relevant_entity_fields):
            error_or_entity_from_invitee(k, v, buyer)

def error_or_entity_from_invitee(field, entity_id, invitee):
    model = get_model_by_field(field)

    try:
        model.objects.get(pk=entity_id, company__company=invitee)
    except ObjectDoesNotExist:
        raise AssertionError(f"Invalid forecast - The {field} does not exist.")


def check_forecast_against_shipped_quantity(nomination, forecast_payload):
    # Nomination dict -> None
    """
    Error if FORECAST_QUANTITY is less than the shipped quantity.
    """
    schedule = nomination.schedule

    if schedule:
        quantity = get_int_or_zero(forecast_payload.get("cars_quantity"))
        shipped_quantity = schedule.shipments_car_count()

        if shipped_quantity > quantity:
            raise AssertionError(
                f"Invalid forecast - cannot be less than the shipped quantity ({shipped_quantity} shipped)"
            )


def check_forecast_against_contract_quantity(nomination, order, forecast_payload):
    # Order int -> None
    """
    Error if FORECAST_QUANTITY exceeds the maximum for the contract.
    """
    forecast_quantity = get_int_or_zero(forecast_payload.get("cars_quantity"))

    if forecast_quantity:
        contract_max = order.contract.total_volume
        other_nominations = order.nomination_set.exclude(id=nomination.id)

        current_contract_total = 0

        for n in other_nominations:
            current_contract_total += get_nomination_total(n)

        if contract_max:
            if current_contract_total + forecast_quantity > contract_max:
                raise AssertionError(
                    f"Invalid forecast - total nomination quantities ({current_contract_total + forecast_quantity}) exceed total contract quantity ({contract_max})"
                )

def get_nomination_total(nomination):
    # Nomination -> int
    """
    Return the nominated quantity or the approved (schedule) plus any shipments
    """
    schedule = nomination.schedule

    nomination_quantity = get_int_or_zero(nomination.data.get("cars_quantity"))

    if schedule:
        return max(nomination_quantity, schedule.shipments_car_count())

    return nomination_quantity

def get_int_or_zero(value):
    try:
        return int(value)
    except Exception:
        return 0

# ------ delete-guards


def relied_on_by_partner_entities(entity) -> bool:
    # Facility|Party -> bool
    """
    True if ENTITY is relied upon somewhere in the
    partner-portal ecosystem.
    """
    return used_in_any_nomination(entity) or used_in_any_schedule(entity)


def used_in_any_nomination(entity) -> bool:
    # Facility|Party -> bool
    """
    True if ENTITY is used in any nomination
    """
    from vantedge.invitation.models.order import Nomination

    in_use_query = models.Q()
    model = entity._meta.model

    for field in get_entity_fields(RailShippingSchedule, model):
        in_use_query |= models.Q(**{f"data__{field}": entity.id})

    return Nomination.objects.filter(in_use_query).exists()


def used_in_any_schedule(entity) -> bool:
    # Facility|Party -> bool
    """
    True if ENTITY is used in any rail-shipping-schedule
    """
    in_use_query = models.Q()
    model = entity._meta.model

    for field in get_entity_fields(RailShippingSchedule, model):
        in_use_query |= models.Q(**{field: entity.id})

    return RailShippingSchedule.objects.filter(in_use_query).exists()
