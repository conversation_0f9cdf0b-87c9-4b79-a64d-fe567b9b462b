import re
from rest_framework import serializers

from ..models import WheelhouseInvitation
from vantedge.wheelhouse.models import Facility

class PartnerWheelhouseInvitationSerializer(serializers.ModelSerializer):
    company = serializers.SerializerMethodField()
    inviter = serializers.SlugRelatedField(read_only=True, slug_field="email")

    def get_company(self, obj):
        return obj.company.company

    class Meta:
        model = WheelhouseInvitation
        fields = [
            "id",
            "email",
            "company",
            "inviter",
            "status",
            "time_accepted",
            "time_sent",
            "time_created",
            "data",
        ]
        read_only_fields = [
            "status",
        ]


class WheelhouseInvitationSerializer(serializers.ModelSerializer):
    company = serializers.SerializerMethodField()
    partner = serializers.SerializerMethodField()

    def get_company(self, obj):
        return obj.company.company

    def get_partner(self, obj):
        return obj.invitee_company

    class Meta:
        model = WheelhouseInvitation
        fields = [
            "id",
            "email",
            "company",
            "inviter",
            "status",
            "time_accepted",
            "time_sent",
            "time_created",
            "data",
            "partner",
        ]
        read_only_fields = [
            "status",
        ]
        extra_kwargs = {"inviter": {"write_only": True}}

    def validate_email(self, email):
        regex = r"\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b"
        if re.fullmatch(regex, email):
            return email
        else:
            raise serializers.ValidationError("Enter a valid email")

    def validate(self, attrs):
        super().validate(attrs)
        if attrs.get("data").get("resource") == "rail_contract":
            defaults = attrs["data"].get("defaults")
            if isinstance(defaults, list):
                origin_id = None
                for item in defaults:
                    matching_items = [another_item for another_item in defaults if another_item["type"] == item["type"] and another_item["field"] == item["field"]]
                    if len(matching_items) > 1:
                        raise serializers.ValidationError(f"more than one {item['field']} is defined")
                    if item["type"] == "facility" and item["field"] == "origin":
                        origin_id = item["entity"]

                if origin_id is None:
                    raise serializers.ValidationError("Origin is not defined")
                origin = Facility.objects.filter(id=origin_id, company=self.context["request"].user.company.wheelhouse).first()
                if not origin:
                    raise serializers.ValidationError("Origin is not in-house facility")
            else:
                raise serializers.ValidationError("You need to set origin as default")

        return attrs