from rest_framework import serializers

from vantedge.terminalboss.wheelhouse.serializers import TicketViewSerializer
from vantedge.tboss.serializers import TicketListSerializer


class PartnerTicketViewSerializer(TicketViewSerializer):
    partner = serializers.SerializerMethodField()

    def get_partner(self, obj):
        return obj.company.company


class PartnerV3TicketSerializer(TicketListSerializer):
    partner = serializers.SerializerMethodField()

    def get_partner(self, obj):
        return obj.dbreplication_client_company
