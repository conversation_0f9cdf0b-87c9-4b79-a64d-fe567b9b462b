from datetime import *
from dateutil.relativedelta import *

from django.db.models import Q
import django_filters as filters

from ..models.order import Order, Nomination


class OrderFilter(filters.FilterSet):
    class Meta:
        model = Order
        fields = []


class NominationFilter(filters.FilterSet):
    order_id = filters.NumberFilter(method="order_id_filter")

    def order_id_filter(self, queryset, name, value):
        return queryset.filter(Q(order_id=value))

    class Meta:
        model = Nomination
        fields = []
