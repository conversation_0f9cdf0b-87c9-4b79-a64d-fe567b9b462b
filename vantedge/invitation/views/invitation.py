import datetime
from django.shortcuts import get_object_or_404
from constance import config
from django.http import HttpResponseRedirect

from rest_framework import status, permissions, viewsets
from rest_framework.decorators import action
from rest_framework.response import Response

from vantedge.users.models import Company

from ..models import WheelhouseInvitation
from ..serializers import (
    WheelhouseInvitationSerializer,
    PartnerWheelhouseInvitationSerializer,
)
from ..models.utils import get_frontend_url


class InvitationPermission(permissions.BasePermission):
    def has_permission(self, request, view):
        if view.action in [
            # "create",
            "update",
            "partial_update",
            "destroy",
            "send_invitation",
            "revoke_invitation",
            "create_invitation",
        ]:
            if request.user.has_perm("invitation.manage_invitations"):
                return bool(request.user and request.user.is_authenticated)

        if view.action in ["list", "retrieve", "get_incoming_invitations"]:
            return bool(request.user and request.user.is_authenticated)

        if view.action in ["accept_invitation"]:
            return True
        return False


class InvitationViewSet(viewsets.ModelViewSet):
    serializer_class = WheelhouseInvitationSerializer
    queryset = WheelhouseInvitation.objects.all()
    permission_classes = [InvitationPermission]

    def get_queryset(self):
        company = self.request.user.company
        if hasattr(company, "invitation_company"):
            invitation_company = getattr(company, "invitation_company")
            return super().get_queryset().filter(company_id=invitation_company.id)
        return WheelhouseInvitation.objects.none()

    # --------
    def perform_create(self, serializer):
        serializer.save(inviter=self.request.user)

    def perform_update(self, serializer):
        serializer.save(inviter=self.request.user)

    def perform_destroy(self, instance):
        # Break party linkage before destroying
        instance.revoke_invitation()
        return super().perform_destroy(instance)

    # --------
    def destroy(self, request, *args, **kwargs):
        """Prevent delete-action for audit integrity"""
        return Response(status=status.HTTP_403_FORBIDDEN)

    @action(
        detail=False,
        methods=["post"],
        url_path="create-invitation",
        url_name="create_invitation",
    )
    def create_invitation(self, request):
        """
        Create an invitation.

        If there exists an invitation (not REVOKED) that
        Forbids duplicate invitations by checking for a match on party,
        resource, and company where the invitation is not REVOKED.
        """
        try:
            invitation = WheelhouseInvitation.create(
                inviter=request.user,
                email=request.data["email"],
                data=request.data["data"],
            )
        except AssertionError as e:
            return Response([str(e)], status=status.HTTP_403_FORBIDDEN)

        return Response(self.get_serializer(invitation).data, status.HTTP_201_CREATED)

    @action(
        detail=True,
        methods=["post"],
        url_path="send-invitation",
        url_name="send_invitation",
    )
    def send_invitation(self, request, pk):
        """
        Send a Wheelhouse invitation unless it has already been sent.\n
        If no invitation exists, `send_invitation` will make a new one and then send
        """
        invitation = self.get_object()

        if invitation:
            if invitation.status == WheelhouseInvitation.Status.SENT:
                return Response(
                    ["Error - Invitation has been sent already."],
                    status=status.HTTP_403_FORBIDDEN,
                )

            invitee_company = (
                Company.objects.prefetch_related("user_set")
                .filter(user__email__iexact=invitation.email)
                .first()
            )
            if invitee_company is None:
                invitation.send_invitation(request, is_registered=False)
                return Response(
                    [
                        f"Warning - Sent invitation but '{invitation.email}' has no Wheelhouse account."
                    ],
                    status.HTTP_202_ACCEPTED,
                )

            invitation.send_invitation(request)
            return Response(
                self.get_serializer(invitation).data, status.HTTP_202_ACCEPTED
            )

    @action(
        detail=False,
        methods=["get"],
        url_path="accept-invitation",
        url_name="accept_invitation",
    )
    def accept_invitation(self, request):
        """
        Accept a Wheelhouse invitation on the following conditions:
        - not revoked
        - not already accepted
        - not expired
        """
        invitation = get_object_or_404(
            WheelhouseInvitation, pk=request.query_params["id"]
        )
        if invitation.status == WheelhouseInvitation.Status.REVOKED:
            return Response(
                ["Error - Invitation has been revoked"],
                status=status.HTTP_403_FORBIDDEN,
            )
        if invitation.status == WheelhouseInvitation.Status.ACCEPTED:
            return Response(
                ["Error - Invitation has been accepted already"],
                status=status.HTTP_403_FORBIDDEN,
            )
        if invitation.is_expired:
            return Response(
                [
                    f"Error - Invitation expired on {invitation.time_sent + datetime.timedelta(days=config.INVITATION_EXPIRY)}"
                ],
                status=status.HTTP_403_FORBIDDEN,
            )

        invitation.accept_invitation()
        url = get_frontend_url(request)

        return HttpResponseRedirect(url)

    @action(
        detail=True,
        methods=["post"],
        url_path="revoke-invitation",
        url_name="revoke_invitation",
    )
    def revoke_invitation(self, request, pk):
        """
        Send a Wheelhouse invitation unless it has already been revoked.
        """
        invitation = self.get_object()

        if invitation.status == WheelhouseInvitation.Status.REVOKED:
            return Response(
                ["Error - Invitation has been revoked already."],
                status=status.HTTP_403_FORBIDDEN,
            )
        invitation.revoke_invitation()
        return Response(self.get_serializer(invitation).data, status.HTTP_200_OK)

    # --------
    @action(
        detail=False,
        methods=["get"],
        url_path="get-incoming-invitations",
        url_name="get_incoming_invitations",
    )
    def get_incoming_invitations(self, request):
        """
        Incoming invitations to invitee-company.

        All invitations where recipient is in invitee-company user_set.
        """
        company = self.request.user.company

        incoming_invitations = WheelhouseInvitation.objects.filter(
            invitee_company=company
        )

        serializer = PartnerWheelhouseInvitationSerializer(
            incoming_invitations, many=True
        )
        return Response(serializer.data)
