import html

from django.template.loader import get_template
from django.template import Template, Context
from django.http.response import HttpResponse

from rest_framework.decorators import action
from rest_framework.permissions import IsAuthenticated

from vantedge.printables.views import PDFKitGenerator
from vantedge.terminalboss.wheelhouse.views import TicketViewViewset
from vantedge.terminalboss.models import TicketView as V2Ticket
from vantedge.printables.models import PrintableTemplate
from vantedge.printables.utils import (
    generate_zip,
    BASIC_STYLESHEETS,
)
from ..models.utils import (
    get_partner_terminalboss_v2_ticket_ids,
    get_company_terminalboss_v2_ticket_partner_parties,
)
from ..serializers.ticket import PartnerTicketViewSerializer


def get_partner_template(company, code, ticket):
    # Company str V2Ticket -> PrintableTemplate
    """
    Get the partner printable object.
    Given company, code, ticket.

    Restriction on company, only the partner company printable-templates are exposed.
    If the ticket-owner has no printable-templates, then the print will be blank.
    """
    ticket_owner_company_id = ticket.company.company.id

    matching_party_company_ids = get_company_terminalboss_v2_ticket_partner_parties(
        company
    ).values_list("company__company_id", flat=True)
    # since each ticket has one owner, `matching-ticket-company-id` has one or no elements
    matching_ticket_company_id = set(matching_party_company_ids).intersection(
        [ticket_owner_company_id]
    )

    printable = (
        PrintableTemplate.objects.filter(company_id__in=matching_ticket_company_id)
        .filter(code=code)
        .first()
    )

    return printable


def generate_partner_pdf(company, code, ticket):
    # Company str RailShipment -> File
    """
    Generate the PDF for print-action

    PDF is generated based on the following:\n
    - base_template\n
    - context\n
    - printable\n
    """
    base_template = get_template("printables/base.html")
    printable = get_partner_template(company, code, ticket)

    context = dict(object=ticket)

    if printable:
        base_template = get_template(printable.base)
        attachments = {a.attachment_type: a for a in printable.attachments.all()}
        context.update(attachments=attachments, template=printable)
        sections = {
            section: Template(
                "{% load static %} {% spaceless %}"
                + html.unescape(getattr(printable, section))
                + "{% endspaceless %}"
            ).render(Context(context))
            for section in ["header", "content", "footer", "css"]
        }
        context.update(sections)

    return PDFKitGenerator(
        main_html=base_template.render(context),
        css=BASIC_STYLESHEETS,
    ).render_pdf()


def generate_partner_bols_from_tickets(company, code, tickets):
    # Company str Qs[V2Ticket] -> Dict{ str: File }
    """
    Returns a dictionary of partner BOLs from a list of RailShipments.
    """
    pdf_dict = {}

    for ticket in tickets:
        pdf = generate_partner_pdf(company, code, ticket)
        pdf_dict[f"{ticket.bol}.pdf"] = pdf

    return pdf_dict


def generate_partner_bols_and_zip(company, code, tickets):
    # Company str Qs[V2Ticket] -> File
    """
    Generates or fetches BOLs from a list of TBossV2Tickets,
    and returns them zipped.
    """
    pdf_dict = generate_partner_bols_from_tickets(company, code, tickets)
    zipped_pdfs = generate_zip(pdf_dict, "tbossv2ticket-bols")

    return zipped_pdfs


class PartnerTicketViewViewset(TicketViewViewset):
    serializer_class = PartnerTicketViewSerializer

    def get_queryset(self):
        company = self.request.user.company

        return V2Ticket.objects.filter(
            id__in=get_partner_terminalboss_v2_ticket_ids(company)
        ).annotate(
            # add custom_ordering_fields from TicketViewset
            **dict(self.custom_ordering_fields)
        )

    def get_printable(self):
        """
        Get the printable object.

        Restriction on company, only the partner company printable-templates are exposed.
        If the ticket-owner has no printable-templates, then the print will be blank.
        """
        code = self.request.query_params.get("code")
        company = self.request.user.company

        # only use the ticket-owner's printable-template
        # get the intersect of partner companies and this ticket's company
        ticket_owner_company_id = self.get_object().company.company.id

        matching_party_company_ids = get_company_terminalboss_v2_ticket_partner_parties(
            company
        ).values_list("company__company_id", flat=True)
        # since each ticket has one owner, `matching-ticket-company-id` has one or no elements
        matching_ticket_company_id = set(matching_party_company_ids).intersection(
            [ticket_owner_company_id]
        )

        printable = (
            PrintableTemplate.objects.filter(company_id__in=matching_ticket_company_id)
            .filter(code=code)
            .first()
        )

        return printable

    @action(detail=False, methods=["get"], permission_classes=[IsAuthenticated])
    def print_multiple(self, request):
        """
        Responds with a zip of pdfs tagged with the print-code
        """
        company = request.user.company
        code = request.query_params.get("code")

        ticket_ids = request.query_params.getlist("ticket_ids[]")
        ticket_ids = [int(id) for id in ticket_ids]
        tickets = V2Ticket.objects.filter(id__in=ticket_ids)

        zipped_pdfs = generate_partner_bols_and_zip(company, code, tickets)

        response = HttpResponse(zipped_pdfs, content_type="application/zip")
        response["Content-Disposition"] = 'attachment; filename="bols.zip"'
        return response

