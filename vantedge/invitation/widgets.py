# https://stackoverflow.com/questions/48145992/showing-json-field-in-django-admin
import json
import logging

from django.forms import widgets


logger = logging.getLogger(__name__)


class JSONWidget(widgets.Textarea):
    def format_value(self, value):
        try:
            value = json.dumps(json.loads(value), indent=2, sort_keys=True)

            # sizing:
            row_lengths = [len(r) for r in value.split("\n")]
            self.attrs["rows"] = min(max(len(row_lengths) + 2, 10), 30)
            self.attrs["cols"] = min(max(max(row_lengths) + 2, 40), 120)

            return value
        except Exception as e:
            logger.warning(f"Error while formatting JSON: {e}")
            return super(JSONWidget, self).format_value(value)
