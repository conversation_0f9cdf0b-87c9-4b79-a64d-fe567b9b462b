# Generated by Django 3.2.13 on 2022-08-26 19:47

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('invoice', '0003_auto_20220819_1042'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='invoiceitem',
            options={'ordering': ['-is_compound', 'pk']},
        ),
        migrations.AlterField(
            model_name='invoiceitem',
            name='unit',
            field=models.CharField(choices=[('PERCENT', 'PERCENT'), ('LITRE', 'LITRE'), ('ITEM', 'ITEM'), ('USG', 'USG'), ('BARREL', 'BARREL'), ('M3', 'M3'), ('KG', 'KG')], max_length=20),
        ),
        migrations.AlterField(
            model_name='product',
            name='unit',
            field=models.CharField(choices=[('PERCENT', 'PERCENT'), ('LITRE', 'LITRE'), ('ITEM', 'ITEM'), ('USG', 'USG'), ('BARREL', 'BARREL'), ('M3', 'M3'), ('KG', 'KG')], max_length=20),
        ),
        migrations.AlterField(
            model_name='unitprice',
            name='unit',
            field=models.CharField(choices=[('PERCENT', 'PERCENT'), ('LITRE', 'LITRE'), ('ITEM', 'ITEM'), ('USG', 'USG'), ('BARREL', 'BARREL'), ('M3', 'M3'), ('KG', 'KG')], max_length=20),
        ),
    ]
