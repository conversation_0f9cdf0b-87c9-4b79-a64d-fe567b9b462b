from django.contrib.auth.models import Permission
from django.conf import settings
from django.db import models
from django.db.models.signals import m2m_changed
from vantedge.util.mixins import EasyAuditModelConfigMixin
from vantedge.users.models.application_configuration import company_permissions_handler

class InvoiceCompany(EasyAuditModelConfigMixin, models.Model):
    company = models.OneToOneField(
        settings.INVOICE_COMPANY_MODEL, related_name="invoice", on_delete=models.CASCADE
    )

    available_permissions = models.ManyToManyField(
        Permission, blank=True, limit_choices_to={"content_type__app_label": "invoice"}
    )

    class Meta:
        verbose_name_plural = "Companies"

    def __str__(self):
        return str(self.company.name)


m2m_changed.connect(
    company_permissions_handler, sender=InvoiceCompany.available_permissions.through
)
