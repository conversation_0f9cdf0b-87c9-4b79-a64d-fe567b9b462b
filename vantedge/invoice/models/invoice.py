from django.core.exceptions import ValidationError

from django.db import models
from django.db import transaction
from django.utils import timezone
from django.conf import settings

from django.contrib.contenttypes.fields import GenericForeignKey
from django.contrib.contenttypes.models import ContentType

from model_utils import Choices
from model_utils.fields import StatusField
from model_utils.models import TimeStampedModel
from decimal import Decimal

from schema_field.fields import J<PERSON>NSchemedField
from .company import InvoiceCompany

# from .pricing import UnitPrice
from .schema import MixPriceSchema, PriceSchema, dict_to_price_schema
from .base import BaseUnitPrice
from .product import ProductRelation, Product
from vantedge.util.units import convert_unit


DEFAULT_DECIMAL_PLACES = 2


def today():
    return timezone.now().date()


class Invoice(TimeStampedModel):
    PERFORMA = "PERFORMA"
    INVOICE = "INVOICE"

    STATUS = Choices("created", "sent")
    INVOICE_TYPES = ((PERFORMA, PERFORMA), (INVOICE, INVOICE))

    status = StatusField()
    company = models.ForeignKey(InvoiceCompany, on_delete=models.PROTECT)
    customer = models.ForeignKey(
        settings.INVOICE_CUSTOMER_MODEL,
        related_name="invoices",
        on_delete=models.PROTECT,
    )
    invoice_type = models.CharField(max_length=30, choices=INVOICE_TYPES)
    invoice_number = models.CharField(max_length=30, blank=True, null=True)
    purchase_order = models.CharField(max_length=50, blank=True, null=True)
    payable_days = models.PositiveSmallIntegerField(blank=True, null=True)
    is_payed = models.BooleanField(default=False)
    date_invoice = models.DateField(default=today)
    date_payment = models.DateTimeField(blank=True, null=True)
    total_sub = JSONSchemedField(schema=MixPriceSchema, default=MixPriceSchema)
    total_non_compound = JSONSchemedField(schema=MixPriceSchema, default=MixPriceSchema)
    gst = JSONSchemedField(schema=MixPriceSchema, default=MixPriceSchema)
    total_grand = JSONSchemedField(schema=MixPriceSchema, default=MixPriceSchema)

    class Meta:
        ordering = ["-id"]

    def delete(self, *args, **kwargs):
        if self.is_readonly:
            raise Exception("Readonly invoice is not deletable")
        else:
            super().delete(*args, **kwargs)

    def save(self, *args, **kwargs):
        add_related_invoices = False
        invoice_base = kwargs.pop("invoice_base", None)
        if not self.id:
            add_related_invoices = True
        super().save(*args, **kwargs)
        if add_related_invoices and invoice_base:
            self.add_related_invoice_items(invoice_base)

    @property
    def is_readonly(self):
        return self.is_payed

    @property
    def is_calculated(self):
        return True if len(self.total_grand.price_list) > 0 else False

    def reset_price(self):
        self.total_sub = MixPriceSchema()
        self.total_non_compound = MixPriceSchema()
        # self.gst = MixPriceSchema()
        self.total_grand = MixPriceSchema()

        for invoice_item in self.invoice_items.filter(is_compound=False):
            invoice_item.price = None
            invoice_item.save(skip_invoice_calculation=True)

        self.save(update_fields=["total_sub", "total_non_compound", "total_grand"])

    def add_related_invoice_items(self, invoice_base):
        related_invoices = ProductRelation.get_related_products(
            self.company, invoice_base
        )

        for invoice_item in related_invoices:
            InvoiceItemOrigin.create_invoice_item_base(self, invoice_item, None)

    def calc_total_sub(self, compound_invoice_items):
        total_sub = dict()
        for item in compound_invoice_items:
            total_sub[item.currency] = total_sub.get(item.currency, 0) + item.price

        return MixPriceSchema(price_list=dict_to_price_schema(total_sub))

    def calc_non_compound(self, non_compound_invoice_items):
        non_compound_total = MixPriceSchema()

        for invoice_item in non_compound_invoice_items:
            currency = invoice_item.currency
            price = (
                (getattr(self.total_sub, currency, 0) or 0)
                * invoice_item.unit_price
                / 100
            )
            invoice_item.price = price
            invoice_item.save(skip_invoice_calculation=True)
            non_compound_total = non_compound_total + MixPriceSchema(
                price_list=[PriceSchema(currency=currency, value=price)]
            )

        return non_compound_total

    def calc_gst(self):
        # GST field removed because it is not applied to all invoices
        # return self.total_sub.copy() * self.company.gst_percent / 100
        raise NotImplemented

    def calc_total_grand(self):
        return self.total_sub + self.total_non_compound

    def calc_price(self):

        invoice_items = self.invoice_items.select_for_update()

        with transaction.atomic():
            # first reset the values, in case something goes wrong have
            self.reset_price()

            # if no real value in invoice then return empty
            if not self.invoice_items.filter(is_compound=True):
                return

            # first sub total
            compound_items = invoice_items.filter(is_compound=True)
            self.total_sub = self.calc_total_sub(compound_items)

            # second non compound values
            non_compound_items = invoice_items.filter(is_compound=False)
            self.total_non_compound = self.calc_non_compound(non_compound_items)

            # add gst
            # self.gst = self.calc_gst()
            self.total_grand = self.calc_total_grand()

            self.save(update_fields=["total_sub", "total_non_compound", "total_grand"])

    def invoice_data_format(self):
        """
            only same products and discounts are adding on top of each other
        """
        railcars = []
        compound_products = {}
        non_compound_products = {}

        result = {
            "railcars": railcars,
            "basic_items": [],
            "compound_products": [],
            "non_compound_products": [],
            "discount": None,
        }

        for invoice_item in self.invoice_items.all():
            item_data = {
                "name": invoice_item.name,
                "quantity": invoice_item.quantity,
                "description": invoice_item.description,
                "unit": invoice_item.unit,
                "unit_price": invoice_item.unit_price,
                "currency": invoice_item.currency,
                "price": invoice_item.price,
                "is_compound": invoice_item.is_compound,
            }
            if not hasattr(invoice_item, "invoiceitemorigin"):
                result["basic_items"].append(item_data)
                continue

            else:
                if hasattr(
                    invoice_item.invoiceitemorigin.content_object, "rail_shipment"
                ):
                    # this is car, just add and one item
                    item_data[
                        "bol"
                    ] = (
                        invoice_item.invoiceitemorigin.content_object.rail_shipment.reference_number
                    )
                    railcars.append(item_data)
                    continue

                if invoice_item.invoiceitemorigin.object_id is None:
                    if result.get("discount", None) is None:
                        result["discount"] = item_data
                        continue
                    else:
                        result["discount"]["quantity"] = (
                            result["discount"]["quantity"] + invoice_item.quantity
                        )
                        result["discount"]["price"] = (
                            result["discount"]["price"] + invoice_item.price
                        )

                else:
                    key = f"{invoice_item.invoiceitemorigin.content_type.id}{invoice_item.invoiceitemorigin.object_id}"
                    if item_data["is_compound"]:
                        p = compound_products
                    else:
                        p = non_compound_products

                    if p.get(key, None) is None:
                        p[key] = item_data
                    else:
                        p[key]["quantity"] = p[key]["quantity"] + invoice_item.quantity
                        p[key]["price"] = p[key]["price"] + invoice_item.price

        result["compound_products"] = compound_products.values()
        result["non_compound_products"] = non_compound_products.values()

        return result


class InvoiceItem(BaseUnitPrice):
    invoice = models.ForeignKey(
        Invoice, related_name="invoice_items", on_delete=models.CASCADE
    )
    quantity = models.DecimalField(max_digits=9, decimal_places=2)
    price = models.DecimalField(
        max_digits=11, decimal_places=DEFAULT_DECIMAL_PLACES, blank=True, null=True
    )

    class Meta:
        ordering = ["-is_compound", "pk"]

    def __str__(self) -> str:
        return f"{self.name}: {self.price}"

    def has_origin(self):
        return getattr(self, "invoiceitemorigin", False)

    @property
    def is_readonly(self):
        # some rows like tax items are not deletable, for future update
        return self.invoice.is_readonly

    @property
    def is_invocable(self):
        # sometimes we need only one invoice for one object, this is handy for that
        return True

    def is_quantity_inherited(self):
        if self.has_origin():
            return getattr(self.invoiceitemorigin, "quantity_inherited", False)
        return False

    def related_invoice_items(self):
        return InvoiceItemOrigin.objects.filter(related_invoice_item=self)

    def delete(self, *args, **kwargs):
        if self.is_readonly:
            raise ValidationError("Can not delete readonly item")

        self.related_invoice_items().delete()

        result = super().delete(*args, **kwargs)
        self.invoice.calc_price()
        return result

    def save(self, *args, **kwargs):

        if not self.is_invocable:
            raise ValidationError("This item can not added to invoice")

        if self.invoice.is_readonly or self.is_readonly:
            raise ValidationError("This item is readonly")

        skip_invoice_calculation = kwargs.pop("skip_invoice_calculation", False)

        # non compound values calculated in invoice and then saved here
        with transaction.atomic():
            calc_price = self.calc_price()
            if calc_price is not None:
                self.price = calc_price

            super().save(*args, **kwargs)

            for invoice_item in self.related_invoice_items():
                if invoice_item.quantity_inherited:
                    conversion_factor = invoice_item.invoiceitem_unit_conversion_factor(self.unit, invoice_item.unit)
                    invoice_item.quantity = convert_unit(
                        self.quantity, self.unit, invoice_item.unit, conversion_factor
                    )

                invoice_item.calc_price()
                invoice_item.save(skip_invoice_calculation=True)

        if not skip_invoice_calculation:
            self.invoice.calc_price()

    def calc_price(self):
        if not self.is_compound:
            return None

        if self.unit == InvoiceItem.PERCENT:
            if self.has_origin():
                related_invoice_currency = (
                    self.invoiceitemorigin.related_invoice_item.currency
                )
                related_invoice_price = (
                    self.invoiceitemorigin.related_invoice_item.price
                )
            else:
                related_invoice_currency = self.related_invoice_item.currency
                related_invoice_price = self.related_invoice_item.price

            if self.currency != related_invoice_currency:
                return 0

            return round(
                float(related_invoice_price) * float(self.unit_price) / 100,
                DEFAULT_DECIMAL_PLACES,
            )

        return round(
            float(self.unit_price) * float(self.quantity), DEFAULT_DECIMAL_PLACES
        )


class InvoiceItemOrigin(InvoiceItem):
    """
        If item has unit price object then take it,
        in case of price need calculation then fill the calculated_unit_price
    """

    # unit_price_object = models.ForeignKey(
    #     UnitPrice, null=True, on_delete=models.PROTECT
    # )
    content_type = models.ForeignKey(ContentType, on_delete=models.PROTECT)
    object_id = models.PositiveIntegerField(null=True)
    content_object = GenericForeignKey("content_type", "object_id")
    calculated_unit_price = models.DecimalField(max_digits=10, decimal_places=3)
    calculated_quantity = models.DecimalField(max_digits=9, decimal_places=2)
    quantity_inherited = models.BooleanField(default=False)
    related_invoice_item = models.ForeignKey(
        "self", on_delete=models.CASCADE, null=True
    )

    @classmethod
    def create_invoice_item_base(cls, invoice, product, related_invoice_item=None):
        content_type = ContentType.objects.get_for_model(product)

        if related_invoice_item is None:
            quantity = getattr(product, "invoiceitem_quantity")
        else:
            # if product is non compound there is only one needed in invoice; like HST
            if not getattr(
                product, "invoiceitem_is_compound"
            ) and invoice.invoice_items.filter(
                invoiceitemorigin__content_type=content_type,
                invoiceitemorigin__object_id=product.id,
            ):
                return

            if product.quantity_inherited:
                quantity = related_invoice_item.quantity
                if product.unit != related_invoice_item.unit:
                    try:
                        origin_unit = related_invoice_item.unit
                        conversion_factor = related_invoice_item.invoiceitem_unit_conversion_factor(origin_unit, product.unit)
                        quantity = convert_unit(quantity, origin_unit, product.unit, conversion_factor)
                    except:
                        raise Exception(
                            f"units {related_invoice_item.name} ({related_invoice_item.unit}) and {product.name} ({product.unit}) are not convertable"
                        )

            else:
                quantity = product.invoiceitem_quantity

        invoice_item = cls(
            invoice=invoice,
            content_type=content_type,
            object_id=product.id,
            name=getattr(product, "invoiceitem_name"),
            description=getattr(product, "invoiceitem_description"),
            quantity=quantity,
            calculated_quantity=quantity,
            unit=getattr(product, "invoiceitem_unit"),
            unit_price=getattr(product, "invoiceitem_unit_price"),
            is_compound=getattr(product, "invoiceitem_is_compound"),
            calculated_unit_price=getattr(product, "invoiceitem_unit_price"),
            quantity_inherited=getattr(product, "quantity_inherited", False),
            currency=getattr(product, "invoiceitem_currency", InvoiceItem.CAD),
            related_invoice_item=related_invoice_item,
        )

        invoice_item.save(skip_invoice_calculation=True)
        return invoice_item

    @classmethod
    def create_invoice_item_discount(cls, invoice, related_invoice_item):

        product = related_invoice_item.content_object

        discount_value = product.invoiceitem_discount_value

        if discount_value is None:
            return

        discount_value = -1 * discount_value
        discount_unit = product.invoiceitem_discount_unit
        quantity = related_invoice_item.quantity

        if (
            discount_unit != BaseUnitPrice.PERCENT
            and discount_unit != related_invoice_item.unit
        ):
            try:
                origin_unit = related_invoice_item.unit
                conversion_factor = related_invoice_item.invoiceitem_unit_conversion_factor(origin_unit, discount_unit)
                quantity = convert_unit(quantity, origin_unit, discount_unit, conversion_factor)
            except Exception as e:
                raise Exception(
                    f"units {related_invoice_item.name} ({related_invoice_item.unit}) and {product.name} ({product.unit}) are not convertable"
                ) from e

        invoice_item = cls(
            invoice=invoice,
            content_type=ContentType.objects.get_for_model(Product),
            object_id=None,
            name=f"Discount {related_invoice_item.name}",
            description=f"Discount {related_invoice_item.description}",
            quantity=quantity,
            calculated_quantity=quantity,
            unit=discount_unit,
            unit_price=discount_value,
            is_compound=True,
            calculated_unit_price=discount_value,
            quantity_inherited=True,
            currency=related_invoice_item.currency,
            related_invoice_item=related_invoice_item,
        )

        invoice_item.save(skip_invoice_calculation=True)
        return invoice_item

    @classmethod
    def create_invoice_item(cls, invoice, product):
        with transaction.atomic():
            initial_invoice_item = cls.create_invoice_item_base(invoice, product)
            cls.create_invoice_item_discount(invoice, initial_invoice_item)

            related_products = ProductRelation.get_related_products(
                invoice.company, product
            )

            for related_prduct in related_products:
                cls.create_invoice_item_base(
                    invoice, related_prduct, related_invoice_item=initial_invoice_item
                )

            invoice.calc_price()

