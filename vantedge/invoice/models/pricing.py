from django.db import models

from django.contrib.contenttypes.fields import GenericForeign<PERSON>ey
from django.contrib.contenttypes.models import ContentType

from .base import BaseUnitPrice
from .company import InvoiceCompany
from .pricing_callback import get_price_callback


class UnitPrice(BaseUnitPrice):
    company = models.ForeignKey(
        InvoiceCompany, null=True, blank=True, on_delete=models.CASCADE
    )
    content_type = models.ForeignKey(ContentType, on_delete=models.CASCADE)
    object_id = models.PositiveIntegerField(blank=True, null=True)
    content_object = GenericForeignKey("content_type", "object_id")
    date_price = models.DateField()

    class Meta:
        ordering = ["-date_price"]

    def __str__(self) -> str:
        return f"{self.content_object} : {self.unit_price} PER {self.unit}"

    def convert_unit_price(self, target_unit):
        pass

    @classmethod
    def calculate_loaded_car(cls, company, content_type, object_id, **kwargs):
        """
            returns (unit, unit_price, currency)
        """
        unit_price = (
            UnitPrice.objects.filter(
                company=company, content_type=content_type, object_id=object_id
            )
            .values("unit", "currency")
            .annotate("unit")
            .aggregate(avg=models.Avg("unit_price"))
        )

        return unit_price.unit, unit_price.avg, unit_price.currency

    @classmethod
    def find_price(cls, company, content_type, object_id, **kwargs):
        price_callback = get_price_callback(company, content_type)
        if price_callback:
            unit, unit_price, currency = getattr(cls, price_callback)(
                company, content_type, object_id, **kwargs
            )
            return False, (unit, unit_price, currency)
        else:
            unit_price = (
                UnitPrice.objects.filter(
                    company=company, content_type=content_type, object_id=object_id
                )
                .order_by("-price_date")
                .first()
            )
            if unit_price is None:
                raise Exception("Price Not found")

        return unit_price, False

    # @classmethod
    # def get_price(cls, product, quantity, unit):
    #     unit_price = (
    #         cls.objects.filter(
    #             company__company=product.company.company,
    #             content_type=ContentType.objects.get_for_model(product),
    #         )
    #         .order_by("-price_date")
    #         .first()
    #     )

    #     if unit_price:
    #         price = unit_price.convert_unit_price(unit)
    #         return price * quantity

    #     return None

