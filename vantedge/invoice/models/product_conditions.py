from django.db import models
from django.db.models.query import QuerySet
from django.contrib.contenttypes.models import ContentType

from functools import reduce

from schema_field.fields import J<PERSON>NSchemedField

from vantedge.wheelhouse.models import Party, Facility
from vantedge.wheelhouse.models import Product as LoadedProduct
from vantedge.wheelhouse.models.validations import CA_STATES, US_STATES
from ...wheelhouse.models.railroad import Railroad

from .product import Product
from .schema import DataCondition


def rgetattr(obj, attr, *args):
    def _getattr(obj, attr):
        return getattr(obj, attr, *args)

    return reduce(_getattr, [obj] + attr.split("."))


class ConfigRawData:
    def __init__(self, company, content_type) -> None:
        self.company = company
        self.content_type = content_type
        super().__init__()

    @property
    def configurations(self):
        return {
            "loadedrailcar": {
                "columns": {
                    "shipper": {
                        "lable": "Shipper",
                        "options": Party.objects.annotate(
                            label=models.F("name")
                        ).filter(
                            company=self.company.wheelhouse,
                            party_types__contains=["shipper"],
                        ),
                        "attribute": "rail_shipment.shipper.id",
                        "filter_available": ["equals", "exclude"],
                    },
                    "consignee": {
                        "lable": "Consignee",
                        "options": Party.objects.annotate(
                            label=models.F("name")
                        ).filter(
                            company=self.company.wheelhouse,
                            party_types__contains=["consignee"],
                        ),
                        "attribute": "rail_shipment.consignee.id",
                        "filter_available": ["equals", "exclude"],
                    },
                    "freight_payer": {
                        "lable": "Freight Payer",
                        "options": Party.objects.annotate(
                            label=models.F("name")
                        ).filter(
                            company=self.company.wheelhouse,
                            party_types__contains=["freight_payer"],
                        ),
                        "attribute": "rail_shipment.freight_payer.id",
                        "filter_available": ["equals", "exclude"],
                    },
                    "custom_broker_us": {
                        "lable": "US Custom Broker",
                        "options": Party.objects.annotate(
                            label=models.F("name")
                        ).filter(
                            company=self.company.wheelhouse,
                            party_types__contains=["custom_broker_us"],
                        ),
                        "attribute": "rail_shipment.custom_broker_us.id",
                        "filter_available": ["equals", "exclude"],
                    },
                    "notify_party": {
                        "lable": "Notify Party",
                        "options": Party.objects.annotate(
                            label=models.F("name")
                        ).filter(
                            company=self.company.wheelhouse,
                            party_types__contains=["notify_party"],
                        ),
                        "attribute": "rail_shipment.notify_party.id",
                        "filter_available": ["equals", "exclude"],
                    },
                    "care_party": {
                        "lable": "Care Party",
                        "options": Party.objects.annotate(
                            label=models.F("name")
                        ).filter(
                            company=self.company.wheelhouse,
                            party_types__contains=["care_party"],
                        ),
                        "attribute": "rail_shipment.care_party.id",
                        "filter_available": ["equals", "exclude"],
                    },
                    "pickup_party": {
                        "lable": "Pickup Party",
                        "options": Party.objects.annotate(
                            label=models.F("name")
                        ).filter(
                            company=self.company.wheelhouse,
                            party_types__contains=["pickup_party"],
                        ),
                        "attribute": "rail_shipment.pickup_party.id",
                        "filter_available": ["equals", "exclude"],
                    },
                    "railroad": {
                        "lable": "Rail Road",
                        "options": Railroad.objects.annotate(label=models.F("name")),
                        "attribute": "rail_shipment.railroad.id",
                        "filter_available": ["equals", "exclude"],
                    },
                    "product": {
                        "lable": "Product",
                        "options": LoadedProduct.objects.annotate(
                            label=models.F("name")
                        ).filter(company=self.company.wheelhouse),
                        "attribute": "rail_shipment.product.id",
                        "filter_available": ["equals", "exclude"],
                    },
                    "destination": {
                        "lable": "Destination",
                        "options": Facility.objects.annotate(
                            label=models.F("name")
                        ).filter(company=self.company.wheelhouse),
                        "attribute": "rail_shipment.destination.id",
                        "filter_available": ["equals", "exclude"],
                    },
                    "origin": {
                        "lable": "Origin",
                        "options": Facility.objects.annotate(
                            label=models.F("name")
                        ).filter(company=self.company.wheelhouse),
                        "attribute": "rail_shipment.origin.id",
                        "filter_available": ["equals", "exclude"],
                    },
                    "origin_province": {
                        "lable": "Origin Province",
                        "options": [
                            dict(value=province, label=province)
                            for province in CA_STATES + US_STATES
                        ],
                        "attribute": "rail_shipment.origin.data.address.state",
                        "filter_available": ["equals", "exclude"],
                    },
                    "destination_province": {
                        "lable": "Destination Province",
                        "options": [
                            dict(value=province, label=province)
                            for province in CA_STATES + US_STATES
                        ],
                        "attribute": "rail_shipment.destination.data.address.state",
                        "filter_available": ["equals", "exclude"],
                    },
                }
            }
        }

    def options(self):
        options = {}
        columns = self.configurations.get(self.content_type, {"columns": {}})["columns"]
        options.update(columns)
        for column in columns:
            options[column].pop("attribute")
            if isinstance(options[column]["options"], QuerySet):
                options[column]["options"] = (
                    options[column]["options"]
                    .annotate(value=models.F("id"))
                    .values("value", "label")
                )

        return options

    def get_config_content_type(self):
        return self.configurations[self.content_type]["columns"]

    def get_column(self, column):
        columns = self.get_config_content_type()
        return columns[column]


class ProductCondition(models.Model):
    name = models.CharField(max_length=50)
    product = models.ForeignKey(
        Product, on_delete=models.CASCADE, related_name="conditions"
    )
    data = JSONSchemedField(schema=DataCondition, default=DataCondition)

    def is_condition_appliable(self, obj):
        content_type_model = ContentType.objects.get_for_model(obj).model
        config_data = ConfigRawData(
            company=self.product.company.company, content_type=content_type_model
        )

        for item in self.data.conditions:
            attr_value = rgetattr(
                obj, config_data.get_column(item.column)["attribute"], None
            )
            if item.filter == "equals":
                if attr_value not in item.values:
                    return False
            elif item.filter == "exclude":
                if attr_value in item.values:
                    return False
        return True

