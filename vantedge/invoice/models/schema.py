from typing import List
from decimal import Decimal
from enum import Enum
from pydantic import BaseModel

CURRENCY_CHOICES = ("USD", "CAD")


class CurrencyEnum(str, Enum):
    USD = "USD"
    CAD = "CAD"


def dict_to_price_schema(dict_obj):
    return [PriceSchema(currency=item, value=dict_obj[item]) for item in dict_obj]


class ExchangeSchema(BaseModel):
    currency_source: CurrencyEnum
    currency_target: CurrencyEnum
    value: Decimal


class PriceSchema(BaseModel):
    currency: CurrencyEnum
    value: Decimal

    def __getattribute__(self, __name: str):
        if __name in CURRENCY_CHOICES:
            if self.currency == __name:
                return self.value
            return None
        return super().__getattribute__(__name)

    def __add__(self, other):
        if other.currency == self.currency:
            return PriceSchema(
                currency=self.currency.name, value=self.value + other.value
            )

        return (self.copy(), other.copy())

    def __sub__(self, other):
        if other.currency == self.currency:
            return PriceSchema(
                currency=self.currency.name, value=self.value - other.value
            )

        new_other = other.copy()
        new_other.value = -new_other.value
        return (self.copy(), new_other)

    def __mul__(self, other):
        return PriceSchema(currency=self.currency.name, value=self.value * other)

    def __truediv__(self, other):
        return PriceSchema(currency=self.currency.name, value=self.value / other)


class MixPriceSchema(BaseModel):
    price_list: List[PriceSchema] = []
    exchange: List[ExchangeSchema] = []

    def __getattribute__(self, __name: str):
        if __name in CURRENCY_CHOICES:
            for item in self.price_list:
                if item.currency == __name:
                    return item.value
            return None
        return super().__getattribute__(__name)

    def __setattr__(self, name, value):
        if name in CURRENCY_CHOICES:
            for item in self.price_list:
                if item.currency == name:
                    item.value = value
                    return self

            self.price_list.append(PriceSchema(currency=name, value=value))
            return self
        return super().__setattr__(name, value)

    def __add__(self, other):
        result = self.copy()
        result.price_list = [item.copy() for item in self.price_list]

        for price in other.price_list:
            value = (getattr(self, price.currency) or 0) + price.value
            setattr(result, price.currency, value)

        return result

    def __mul__(self, other):
        result = self.copy()
        price_list = []

        for item in result.price_list:
            price_list.append(item * other)

        result.price_list = price_list
        return result

    def __truediv__(self, other):
        result = self.copy()
        price_list = []

        for item in result.price_list:
            price_list.append(item / other)

        result.price_list = price_list
        return result


class BaseCondition(BaseModel):
    column: str
    values: list
    filter: str


class DataCondition(BaseModel):
    conditions: list[BaseCondition] = []
