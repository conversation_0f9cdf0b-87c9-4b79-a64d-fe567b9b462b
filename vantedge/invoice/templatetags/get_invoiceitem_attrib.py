from django import template

register = template.Library()


def get_invoiceitem_attrib(value, arg):
    if arg == "bol":
        if hasattr(value, "invoiceitemorigin"):
            if hasattr(value.invoiceitemorigin.content_object, "rail_shipment"):
                return (
                    value.invoiceitemorigin.content_object.rail_shipment.reference_number
                )

    return ""


register.filter("get_invoiceitem_attrib", get_invoiceitem_attrib)

