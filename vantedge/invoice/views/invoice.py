from django.shortcuts import get_object_or_404
from django.db import transaction

from rest_framework.viewsets import ModelViewSet
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework import status

from vantedge.printables.views import PrintableViewset
from vantedge.invoice.models import Invoice
from vantedge.wheelhouse.models.railcar import LoadedRailCar

from ..models import Invoice, InvoiceItem, InvoiceItemOrigin, InvoiceCompany
from .serializers.invoice import (
    InvoiceSerializer,
    InvoiceListSerializer,
    InvoiceItemSerializer,
    InvoiceItemOriginSerializer,
)


class InvoiceViewSet(PrintableViewset):
    serializer_class = InvoiceSerializer
    queryset = Invoice.objects.all()

    def get_queryset(self):
        qs = super().get_queryset()
        return qs.filter(company__company=self.request.user.company)

    def get_serializer_class(self):
        if self.action == "list":
            return InvoiceListSerializer
        return super().get_serializer_class()

    def perform_create(self, serializer):
        company = InvoiceCompany.objects.get(company=self.request.user.company)
        serializer.save(company=company)

    def perform_update(self, serializer):
        company = InvoiceCompany.objects.get(company=self.request.user.company)
        serializer.save(company=company)

    @action(detail=False, methods=["POST"])
    def invoice_cars(self, request):
        invoice_id = request.data.get("invoice_id")
        cars_id = request.data.get("cars")

        invoice_company = InvoiceCompany.objects.get(company=self.request.user.company)

        cars = LoadedRailCar.objects.filter(
            id__in=cars_id, current_location__company__company=self.request.user.company
        )

        customer = cars.first().rail_shipment.consignee
        for car in cars:
            if car.rail_shipment.consignee != customer:
                return Response(
                    {"error": "cars have different consignees"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

        try:
            with transaction.atomic():
                if invoice_id:
                    invoice = get_object_or_404(
                        Invoice, pk=invoice_id, company=invoice_company
                    )
                else:
                    invoice = Invoice(
                        invoice_type=Invoice.PERFORMA,
                        company=invoice_company,
                        customer=customer,
                    )
                    invoice.save()

                for car in cars:
                    InvoiceItemOrigin.create_invoice_item(invoice, car)

                return Response(InvoiceSerializer(invoice, many=False).data)
        except Exception as e:
            return Response([str(e)], status=status.HTTP_400_BAD_REQUEST)


class InvoiceItemViewSet(ModelViewSet):
    serializer_class = InvoiceItemSerializer
    queryset = InvoiceItem.objects.all()

    def get_queryset(self):
        qs = super().get_queryset()
        return qs.filter(invoice__company__company=self.request.user.company)


class InvoiceItemOriginViewSet(ModelViewSet):
    serializer_class = InvoiceItemOriginSerializer
    queryset = InvoiceItemOrigin.objects.all()

    def get_queryset(self):
        qs = super().get_queryset()
        return qs.filter(invoice__company__company=self.request.user.company)
