from django.conf import settings
from rest_framework import serializers

from ...models import Invoice, InvoiceItem, InvoiceItemOrigin


class InvoiceListSerializer(serializers.ModelSerializer):
    customer = serializers.SerializerMethodField()

    def get_customer(self, obj):
        return obj.customer.name

    class Meta:
        model = Invoice
        fields = "__all__"


class InvoiceItemSerializer(serializers.ModelSerializer):
    id = serializers.IntegerField(required=False)
    quantity_inherited = serializers.SerializerMethodField()

    class Meta:
        model = InvoiceItem
        read_only_fields = ["created", "modified", "price"]
        fields = "__all__"

    def get_quantity_inherited(self, obj):
        return obj.is_quantity_inherited()

    def validate(self, attrs):
        if self.instance and self.instance.is_readonly:
            raise serializers.ValidationError("Invoice is readonly")

        return super().validate(attrs)


class InvoiceItemOriginSerializer(serializers.ModelSerializer):
    class Meta:
        model = InvoiceItemOrigin
        read_only_fields = ["unit_price_object", "price"]
        fields = "__all__"


class InvoiceSerializer(serializers.ModelSerializer):
    invoice_items = InvoiceItemSerializer(many=True, read_only=True)

    class Meta:
        model = Invoice
        read_only_fields = [
            "created",
            "modified",
            "total_sub",
            "total_non_compound",
            "gst",
            "total_grand",
            "company",
        ]
        fields = "__all__"

    def validate(self, attrs):
        if self.instance and self.instance.is_readonly:
            raise serializers.ValidationError("Invoice is readonly")

        return super().validate(attrs)
