from django.contrib.contenttypes.models import ContentType

from rest_framework import serializers

from ...models import Product, ProductCondition, ConfigRawData, ProductRelation


class ProductRelationSerializer(serializers.ModelSerializer):
    class Meta:
        model = ProductRelation
        fields = "__all__"


class ProductConditionSerializer(serializers.ModelSerializer):
    class Meta:
        model = ProductCondition
        fields = "__all__"

    def validate(self, attrs):
        content_type = self.context["request"].data.get("content_type")
        if not ContentType.objects.filter(model=content_type).exists():
            raise serializers.ValidationError("content type is not valid")

        if attrs["product"]:
            if (
                not attrs["product"].company
                == self.context["request"].user.company.invoice
            ):
                raise serializers.ValidationError("Company not match!")

        config_data = ConfigRawData(
            company=self.context["request"].user.company, content_type=content_type
        )
        acceptable_columns = config_data.get_config_content_type().keys()

        if attrs["data"]:
            conditions = attrs["data"].get("conditions")
            if not conditions:
                raise serializers.ValidationError("condition key is not there")

            for item in conditions:
                for key in item.keys():
                    if key not in ["column", "values", "filter"]:
                        raise serializers.ValidationError(
                            f"Key {key} is not acceptable"
                        )

                    if item.get("column", None) not in acceptable_columns:
                        raise serializers.ValidationError(
                            f"Key {item.get('column', None)} is not acceptable"
                        )

        return super().validate(attrs)


class ProductSerializer(serializers.ModelSerializer):
    relations = ProductRelationSerializer(many=True, read_only=True)
    conditions = ProductConditionSerializer(many=True, read_only=True)

    def validate(self, attrs):

        if attrs.get("unit") == Product.PERCENT or attrs.get("is_compound") is False:
            if attrs["quantity_inherited"] == True:
                raise serializers.ValidationError(
                    "Quantity can not inherited for percent or non-compound types"
                )

        return super().validate(attrs)

    def save(self, **kwargs):
        content_type = self.context["request"].data.get("content_type")
        assign_to_content_type = self.context["request"].data.get(
            "assign_to_content_type"
        )

        instance = super().save(**kwargs)

        if content_type:
            content_type = ContentType.objects.get(model=content_type)
            product_relation, created = ProductRelation.objects.get_or_create(
                product_content_type=content_type,
                company=self.context["request"].user.company.invoice,
            )

            if assign_to_content_type:
                product_relation.apply_products.add(instance)
            else:
                product_relation.apply_products.remove(instance)

        return instance

    class Meta:
        model = Product
        read_only_fields = ["company", "created", "modified"]
        fields = "__all__"

