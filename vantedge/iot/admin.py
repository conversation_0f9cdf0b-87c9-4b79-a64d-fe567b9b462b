from django.contrib import admin
from django.db import models
from django_json_widget.widgets import JSONEditorWidget
from django_object_actions import DjangoObjectActions, takes_instance_or_queryset

from .models import EventType, Event


@admin.register(EventType)
class EventTypeAdmin(DjangoObjectActions, admin.ModelAdmin):
    """Admin interface for EventType model."""

    list_display = [
        "name",
        "company",
        "parent",
        "description",
        "is_active",
        "max_events_per_location",
        "retention_days",
        "display_order",
    ]
    list_filter = ["company", "is_active"]
    search_fields = ["name", "description"]
    raw_id_fields = ["parent"]
    readonly_fields = ["id"]
    fieldsets = [
        (
            None,
            {
                "fields": [
                    "id",
                    "name",
                    "description",
                    "company",
                    "parent",
                    "is_active",
                    "display_order",
                ]
            },
        ),
        ("Data Retention", {"fields": ["max_events_per_location", "retention_days"]}),
        ("Alert Configuration", {"fields": ["alert_threshold"]}),
    ]

    formfield_overrides = {models.JSONField: {"widget": JSONEditorWidget}}

    actions = ["cleanup_events"]
    change_actions = ["cleanup_events"]

    @takes_instance_or_queryset
    def cleanup_events(self, request, queryset):
        """Clean up old events for the selected event types."""
        total_deleted = 0
        for event_type in queryset:
            deleted_count = event_type.clean_old_events()
            total_deleted += deleted_count

        self.message_user(
            request,
            f"Cleaned up {total_deleted} events for {queryset.count()} event type(s).",
        )

    cleanup_events.short_description = "Clean up old events"


@admin.register(Event)
class EventAdmin(admin.ModelAdmin):
    """Admin interface for Event model."""

    list_display = ["timestamp", "company", "location", "event_type", "device_ref"]
    list_filter = ["company", "location", "event_type"]
    search_fields = [
        "company__name",
        "location__name",
        "event_type__name",
        "device_ref",
    ]
    date_hierarchy = "timestamp"
    readonly_fields = ["id", "timestamp"]
    raw_id_fields = ["company", "location", "event_type"]

    fieldsets = [
        (
            None,
            {
                "fields": [
                    "id",
                    "timestamp",
                    "company",
                    "location",
                    "event_type",
                    "device_ref",
                ]
            },
        ),
        ("Event Data", {"fields": ["data"]}),
    ]

    # Use JSON widget for the data field
    formfield_overrides = {
        models.JSONField: {"widget": JSONEditorWidget},
    }

    # No actions needed for now
