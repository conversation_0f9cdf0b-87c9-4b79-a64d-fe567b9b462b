from django_filters import FilterSet, Cha<PERSON><PERSON><PERSON><PERSON>, U<PERSON>DFilter
from django.db.models import Q

from vantedge.iot.models import Event, EventType


class EventFilter(FilterSet):
    """Filter for Event model with event type filtering capabilities."""
    
    # Event type filtering by ID
    event_type = UUIDFilter(field_name="event_type__id")
    event_type_id = UUIDFilter(field_name="event_type__id")
    
    # Event type filtering by code
    event_type_code = CharFilter(field_name="event_type__code", lookup_expr="exact")
    event_type_code__icontains = CharFilter(field_name="event_type__code", lookup_expr="icontains")
    
    # Event type filtering by name
    event_type_name = CharFilter(field_name="event_type__name", lookup_expr="exact")
    event_type_name__icontains = CharFilter(field_name="event_type__name", lookup_expr="icontains")
    
    # Device reference filtering
    device_ref = Char<PERSON>ilter(field_name="device_ref", lookup_expr="icontains")
    
    # Location filtering
    location = UUIDFilter(field_name="location__id")
    location_id = UUIDFilter(field_name="location__id")

    class Meta:
        model = Event
        fields = {
            "timestamp": ["exact", "lt", "lte", "gt", "gte"],
        }
