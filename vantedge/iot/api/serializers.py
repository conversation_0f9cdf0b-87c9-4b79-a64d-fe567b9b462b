from rest_framework import serializers
from vantedge.iot.models import EventType, Event


class EventTypeSerializer(serializers.ModelSerializer):
    """Serializer for EventType model."""
    
    class Meta:
        model = EventType
        fields = [
            'id', 'name', 'description', 'company', 'parent',
            'max_events_per_location', 'retention_days', 'is_active',
            'alert_threshold', 'display_order'
        ]
        read_only_fields = ['id']
    
    # Convert foreign keys to IDs in output
    company_id = serializers.PrimaryKeyRelatedField(
        source='company', read_only=True
    )
    parent_id = serializers.PrimaryKeyRelatedField(
        source='parent', read_only=True, allow_null=True
    )


class EventSerializer(serializers.ModelSerializer):
    """Serializer for Event model."""
    
    class Meta:
        model = Event
        fields = [
            'id', 'timestamp', 'company', 'location', 'event_type',
            'data', 'device_ref', 'created_by'
        ]
        read_only_fields = ['id', 'timestamp', 'company', 'created_by']
    
    # Convert foreign keys to IDs in output
    company_id = serializers.PrimaryKeyRelatedField(
        source='company', read_only=True
    )
    location_id = serializers.PrimaryKeyRelatedField(
        source='location', read_only=True, allow_null=True
    )
    event_type_id = serializers.PrimaryKeyRelatedField(
        source='event_type', read_only=True
    )
    
    def validate(self, attrs):
        """Validate the event data."""
        # Ensure event_type is active
        event_type = attrs.get('event_type')
        if event_type and not event_type.is_active:
            raise serializers.ValidationError({
                'event_type': f"Event type {event_type.name} is not active"
            })
        
        return attrs
    
    def create(self, validated_data):
        """Create a new event with the company from the request."""
        # Get the company from the request user
        request = self.context.get('request')
        if not request or not hasattr(request, 'user') or not hasattr(request.user, 'company'):
            raise serializers.ValidationError({
                'company': "User must be associated with a company to create events."
            })
        
        # Set the company and created_by from the request
        validated_data['company'] = request.user.company
        validated_data['created_by'] = request.user
        
        # Create the event
        return super().create(validated_data)


class EventCreateSerializer(serializers.ModelSerializer):
    """Serializer for creating Event instances."""
    
    event_type_id = serializers.UUIDField(write_only=True)
    location_id = serializers.IntegerField(required=False, allow_null=True, write_only=True)
    
    class Meta:
        model = Event
        fields = ['event_type_id', 'location_id', 'data', 'device_ref']
    
    def validate_event_type_id(self, value):
        """Validate that the event_type exists and is accessible to the user."""
        request = self.context.get('request')
        if not request or not hasattr(request, 'user') or not hasattr(request.user, 'company'):
            raise serializers.ValidationError(
                "User must be associated with a company to create events."
            )
        
        try:
            # Get the event type, ensuring it's accessible to the user
            event_type = EventType.objects.filter(
                id=value
            ).filter(
                # Either belongs to the user's company or is global (company=None)
                models.Q(company=request.user.company) | models.Q(company=None)
            ).first()
            
            if not event_type:
                raise serializers.ValidationError(
                    f"Event type with ID {value} not found or not accessible."
                )
            
            if not event_type.is_active:
                raise serializers.ValidationError(
                    f"Event type {event_type.name} is not active."
                )
            
            return value
        except EventType.DoesNotExist:
            raise serializers.ValidationError(
                f"Event type with ID {value} not found or not accessible."
            )
    
    def create(self, validated_data):
        """Create a new event with the company from the request."""
        request = self.context.get('request')
        
        # Extract IDs from validated data
        event_type_id = validated_data.pop('event_type_id')
        location_id = validated_data.pop('location_id', None)
        
        # Create the event with proper relationships
        event = Event(
            company=request.user.company,
            event_type_id=event_type_id,
            location_id=location_id,
            created_by=request.user,
            **validated_data
        )
        
        # Run validation and save
        event.full_clean()
        event.save()
        
        return event
