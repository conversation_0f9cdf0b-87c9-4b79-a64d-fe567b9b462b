from django.urls import path, include
from rest_framework.routers import <PERSON>fault<PERSON><PERSON><PERSON>

from vantedge.iot.api.views import EventTypeViewSet, EventViewSet

# Create a router and register our viewsets with it
router = DefaultRouter()
router.register(r'event-types', EventTypeViewSet)
router.register(r'events', EventViewSet)

# The API URLs are now determined automatically by the router
urlpatterns = [
    path('', include(router.urls)),
]
