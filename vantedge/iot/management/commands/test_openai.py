from django.core.management.base import BaseCommand
from openai import OpenAI


class Command(BaseCommand):
    help = "Quick test of ChatGPT using GPT-4 (new OpenAI SDK format)"

    def handle(self, *args, **options):
        client = OpenAI(
            api_key="***********************************************************************************************************************************************************************"
        )

        chat = client.chat.completions.create(
            model="gpt-3.5-turbo",
            messages=[
                {"role": "system", "content": "You are a helpful assistant."},
                {"role": "user", "content": "Say hello!"},
            ],
        )

        print(chat.choices[0].message.content)
