from django.core.exceptions import ValidationError
from typing import Dict, Any
from django.db import models
from django.contrib.auth import get_user_model
from django.utils import timezone

from vantedge.iot.models.event_type import EventType
from vantedge.users.models import Company
from vantedge.util.classes import ProxyCompanyMixin
from vantedge.wheelhouse.models import Location
from vantedge.util.json.util import clean_json_field

User = get_user_model()


"""Models for IoT (Internet of Things) data management.

This module provides models for storing and managing IoT events and their types.
It allows tracking of various events across different company locations.
"""


class ProxyEventMixin(ProxyCompanyMixin):
    can_handle_event_types = None

    @classmethod
    def can_handle_event_type(cls, instance):
        return instance.event_type.code in cls.can_handle_event_types

    @classmethod
    def can_handle(cls, instance):
        return cls.can_handle_company(instance) and cls.can_handle_ticket_type(instance)


class Event(ProxyEventMixin, models.Model):
    class Meta:
        verbose_name = "Event"
        verbose_name_plural = "Events"

    # Handle all companies by default
    can_handle_company_names = None
    data_schema = dict

    # id = models.UUIDField(primary_key=True, default=uuid7, editable=False)

    company = models.ForeignKey(
        Company,
        on_delete=models.CASCADE,
        help_text="Company this event belongs to.",
    )

    timestamp = models.DateTimeField(
        default=timezone.now, help_text="When this event occurred."
    )

    location = models.ForeignKey(
        Location,
        null=True,
        blank=True,
        on_delete=models.CASCADE,
        help_text="Location where this event occurred.",
    )
    event_type = models.ForeignKey(
        EventType,
        on_delete=models.CASCADE,
        help_text="Type of event.",
    )
    event_sub_type = models.CharField(
        max_length=25,
        help_text="Sub-type of event.",
    )

    # Additional fields for event tracking and management
    device_ref = models.CharField(
        max_length=50,
        blank=True,
        null=True,
        help_text="Identifier of the device that generated this event.",
    )

    message = models.CharField(
        max_length=255,
        blank=True,
        null=True,
        help_text="Human-readable message describing the event.",
    )

    # Track who created the event
    created_by = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        help_text="Account that created this event.",
    )

    data = models.JSONField(help_text="JSON containing event details.")

    class Meta:
        ordering = ["-timestamp"]  # Most recent events first
        verbose_name = "Event"
        verbose_name_plural = "Events"
        indexes = [
            models.Index(fields=["-timestamp"]),
            models.Index(fields=["company", "location", "event_type"]),
            models.Index(fields=["event_type", "location"]),
        ]

    def __str__(self):
        return f"{self.event_type} at {self.location} ({self.timestamp})"

    def clean_data(self):
        """Clean and validate the data field using the Pydantic model.

        Following Django's clean_<fieldname> convention for field-specific validation.

        Returns:
            dict: Validated and possibly transformed data

        Raises:
            ValidationError: If the data is invalid according to the Pydantic model
        """
        return clean_json_field(self.data, self.data_schema)

    def clean(self):
        """Validate the event data."""
        super().clean()

        # Validate that event_type is active
        if not self.event_type.is_active:
            raise ValidationError(
                {"event_type": f"Event type {self.event_type.name} is not active"}
            )

        # Validate data using Pydantic model if available
        validated_data = self.clean_data()
        if validated_data is not self.data:
            self.data = validated_data

    def save(self, *args, **kwargs):
        """Override save to run validation and handle business logic."""
        # Run validation
        self.full_clean()

        # Save the event
        super().save(*args, **kwargs)

        # Run any post-save business logic
        self.process_event()

    def process_event(self):
        """Process the event based on its type and data.

        This method can be extended to handle specific business logic
        for different event types.
        """
        # Default implementation does nothing
        pass

    @classmethod
    def create_from_request(cls, data: Dict[str, Any], user: User) -> "Event":
        """Create an event from API request data, using the authenticated user's company.

        Args:
            data: The event data from the API request
            user: The authenticated user making the request

        Returns:
            The created Event instance (will be cast to appropriate proxy model)

        Raises:
            ValidationError: If the event data is invalid
        """
        # Get the event type to determine which validator to use
        event_type_id = data.get("event_type_id")
        event_type = EventType.objects.get(id=event_type_id)

        # Create a new event instance using EventProxy to ensure proper casting
        event = Event(
            company=user.company,  # Use the authenticated user's company
            location_id=data.get("location_id"),
            event_type=event_type,
            data=data.get("data", {}),
            device_ref=data.get("device_ref"),
            created_by=user,
        )

        # Save the event (this will run standard validation)
        event.save()

        return event
