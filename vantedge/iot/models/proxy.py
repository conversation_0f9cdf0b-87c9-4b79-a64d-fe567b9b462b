from .base import Event
from .schemas import TruckEventData, TransferEventData


class SightingEvent(Event):
    class Meta:
        proxy = True
        verbose_name = "Sighting"
        verbose_name_plural = "Sightings"

    can_handle_event_types = ["SIGHTING"]
    can_handle_company_names = None  # Handle all companies
    is_leaf = True
    data_schema = TruckEventData

    # No need to override clean() as the base Event class now handles Pydantic validation

    def process_event(self):
        """Process truck-specific events."""
        event_type = self.data.get("event")
        truck_id = self.data.get("truck_id")

        if event_type == "arrival":
            # Logic for truck arrival
            print(f"Truck {truck_id} arrived at {self.location}")
        elif event_type == "departure":
            # Logic for truck departure
            print(f"Truck {truck_id} departed from {self.location}")


class TransferEvent(Event):
    """Proxy model for transfer-related events."""

    class Meta:
        proxy = True
        verbose_name = "Transfer Event"
        verbose_name_plural = "Transfer Events"

    can_handle_event_types = ["Transfer"]
    can_handle_company_names = None  # Handle all companies
    is_leaf = True
    data_schema = TransferEventData

    # No need to override clean() as the base Event class now handles Pydantic validation

    def process_event(self):
        """Process transfer-specific events."""
        event_type = self.data.get("event")
        transfer_id = self.data.get("transfer_id")
        truck_id = self.data.get("truck_id")

        if event_type == "unload_start":
            # Logic for unload start
            print(f"Started unloading truck {truck_id} with transfer {transfer_id}")
        elif event_type == "unload_complete":
            # Logic for unload complete
            quantity = self.data.get("quantity_transferred")
            units = self.data.get("units")
            print(
                f"Completed unloading {quantity} {units} from truck {truck_id} with transfer {transfer_id}"
            )
