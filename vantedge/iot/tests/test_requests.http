# IoT API Test Requests
# =====================
#
# Before using these requests:
# 1. Run the load_event_types command to create the necessary event types:
#    python manage.py load_event_types [--company COMPANY_NAME]
#
# 2. Get the event type IDs by running the List Event Types request below
#    and update the @truckEventTypeId and @transferEventTypeId variables
#
# 3. Update the @companyId and @locationId variables with valid values
#
# 4. Make sure you have a valid authentication token

### Variables
@baseUrl = http://localhost:8000/api/iot
@token = 01fdd517e2ff350ad4a93d820976dc48545bfc79
@eventId = 00000000-0000-0000-0000-000000000000
@companyId = 35
@locationId = 283

# Event Type IDs - Update these after running the load_event_types command
@truckEventTypeId = 32140458-e701-4775-b9ff-6eea5d248c13
@transferEventTypeId = 00000000-0000-0000-0000-000000000000

### List Event Types
GET {{baseUrl}}/event-types
Authorization: Token {{token}}

### Get Event Type by ID
GET {{baseUrl}}/event-types/{{eventTypeId}}
Authorization: Token {{token}}

# Event Type creation is only available through the admin interface

# Event Type updates are only available through the admin interface

# Event Type deletion is only available through the admin interface

# Event cleanup is handled automatically based on retention settings

### List Events
GET {{baseUrl}}/events
Authorization: Token {{token}}

### List Events with Filters
GET {{baseUrl}}/events?company_id={{companyId}}&event_type_id={{truckEventTypeId}}
Authorization: Token {{token}}

### List Events for a Specific Truck
GET {{baseUrl}}/events?company_id={{companyId}}&device_ref=gate-system
Authorization: Token {{token}}

### List All Events for a Specific Truck (using data field)
# Note: This requires server-side support for filtering on JSON data fields
GET {{baseUrl}}/events?company_id={{companyId}}&data__truck_id=T-12345
Authorization: Token {{token}}

### Get Event by ID
GET {{baseUrl}}/events/{{eventId}}
Authorization: Token {{token}}

### Create Generic Event
POST {{baseUrl}}/events
Authorization: Token {{token}}
Content-Type: application/json

{
  "location_id": "{{locationId}}",
  "event_type_id": "{{truckEventTypeId}}",
  "device_ref": "sensor-001",
  "data": {
    "temperature": 22.5,
    "humidity": 45,
    "battery": 98
  }
}

### Create Truck Arrival Event
POST {{baseUrl}}/events
Authorization: Token {{token}}
Content-Type: application/json

{
  "location_id": "{{locationId}}",
  "event_type_id": "{{truckEventTypeId}}",
  "device_ref": "gate-system",
  "data": {
    "event": "arrival",
    "truck_id": "T-12345",
    "driver": "John Doe",
    "carrier": "ABC Trucking",
    "scheduled_appointment": "2025-04-26T14:00:00Z",
    "actual_arrival": "2025-04-26T13:55:23Z",
    "cargo": {
      "type": "bulk",
      "product": "Crude Oil",
      "quantity": 8500,
      "units": "gallons"
    }
  }
}

### Create Truck Unload Start Event
POST {{baseUrl}}/events
Authorization: Token {{token}}
Content-Type: application/json

{
  "location_id": "{{locationId}}",
  "event_type_id": "{{transferEventTypeId}}",
  "device_ref": "terminal-system",
  "data": {
    "event": "unload_start",
    "truck_id": "T-12345",
    "transfer_id": "TRF-789",
    "start_time": "2025-04-26T14:10:05Z",
    "operator": "Jane Smith",
    "bay_number": "B-12",
    "initial_readings": {
      "truck_meter": 8500,
      "terminal_meter": 45678.5,
      "temperature": 18.3,
      "pressure": 42.1
    },
    "safety_checks_completed": true
  }
}

### Create Truck Unload Complete Event
POST {{baseUrl}}/events
Authorization: Token {{token}}
Content-Type: application/json

{
  "location_id": "{{locationId}}",
  "event_type_id": "{{transferEventTypeId}}",
  "device_ref": "terminal-system",
  "data": {
    "event": "unload_complete",
    "truck_id": "T-12345",
    "transfer_id": "TRF-789",
    "start_time": "2025-04-26T14:10:05Z",
    "end_time": "2025-04-26T14:55:18Z",
    "duration_minutes": 45.22,
    "operator": "Jane Smith",
    "bay_number": "B-12",
    "final_readings": {
      "truck_meter": 0,
      "terminal_meter": 54178.5,
      "temperature": 18.5,
      "pressure": 41.8
    },
    "quantity_transferred": 8500,
    "units": "gallons",
    "samples_collected": true,
    "notes": "Transfer completed without issues"
  }
}

### Create Truck Departure Event
POST {{baseUrl}}/events
Authorization: Token {{token}}
Content-Type: application/json

{
  "location_id": "{{locationId}}",
  "event_type_id": "{{truckEventTypeId}}",
  "device_ref": "gate-system",
  "data": {
    "event": "departure",
    "truck_id": "T-12345",
    "driver": "John Doe",
    "departure_time": "2025-04-26T15:05:42Z",
    "total_time_on_site_minutes": 70.32,
    "paperwork_completed": true
  }
}

### Delete Event
DELETE {{baseUrl}}/events/{{eventId}}
Authorization: Token {{token}}

### Get a valid token (if you need to generate one)
# This assumes you have a token endpoint in your API
# You may need to adjust this based on your actual authentication setup
POST http://localhost:8000/api/token-auth/
Content-Type: application/json

{
  "username": "your_username",
  "password": "your_password"
}
