# Generated by Django 3.2.7 on 2022-10-24 15:24

from django.conf import settings
import django.contrib.postgres.fields
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('wheelhouse', '0043_auto_20221011_1659'),
    ]

    operations = [
        migrations.CreateModel(
            name='Note',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('note_type', models.CharField(blank=True, choices=[('Info', 'Info'), ('Warning', 'Warning'), ('Alert', 'Alert')], default='Info', max_length=50, null=True)),
                ('title', models.CharField(blank=True, max_length=75, null=True)),
                ('text', models.TextField(default='')),
                ('effective_date', models.DateTimeField(auto_now_add=True, null=True, verbose_name='start')),
                ('expiry_date', models.DateTimeField(blank=True, null=True, verbose_name='end')),
                ('recipient_type', models.CharField(blank=True, choices=[('Person', 'Person'), ('Company', 'Company'), ('Role', 'Role')], default='Company', max_length=50, null=True)),
                ('recipients', django.contrib.postgres.fields.ArrayField(base_field=models.CharField(blank=True, max_length=15, null=True), blank=True, null=True, size=None)),
                ('distribute_via_sms', models.BooleanField(default=True)),
                ('distribute_via_email', models.BooleanField(default=True)),
                ('distribute_via_push_notification', models.BooleanField(default=True)),
                ('location', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='wheelhouse.location')),
            ],
            options={
                'verbose_name_plural': 'Notes',
                'ordering': ['-effective_date'],
            },
        ),
        migrations.CreateModel(
            name='PushNotification',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('seen', models.BooleanField(default=False)),
                ('note', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='push_notifications', to='notification.note')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='push_notifications', to=settings.AUTH_USER_MODEL)),
            ],
        ),
    ]
