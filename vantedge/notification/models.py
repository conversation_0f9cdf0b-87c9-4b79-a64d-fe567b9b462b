from django.db import models
from django.db.models import Q
from django.conf import settings

from vantedge.users.models import User

from vantedge.wheelhouse.models import Location


class NoteUser(models.Model):
    user = models.ForeignKey(
        User, on_delete=models.CASCADE, related_name="schedule_notes"
    )
    note = models.ForeignKey("Note", on_delete=models.CASCADE)
    read_at = models.DateTimeField(null=True, blank=True)


class Note(models.Model):
    class RecipientType(models.TextChoices):
        PERSON = "Person", "Person"
        COMPANY = "Company", "Company"
        ROLE = "Role", "Role"

    class NoteType(models.TextChoices):
        INFO = "Info", "Info"
        WARNING = "Warning", "Warning"
        ALERT = "Alert", "Alert"

    location = models.ForeignKey(
        Location, blank=True, null=True, on_delete=models.SET_NULL
    )

    note_type = models.CharField(
        choices=NoteType.choices,
        max_length=50,
        default=NoteType.INFO,
        blank=True,
        null=True,
    )
    title = models.CharField(max_length=75, null=True, blank=True)
    text = models.TextField(default="")
    effective_date = models.DateTimeField(
        "start", null=True, blank=True, auto_now_add=True
    )
    expiry_date = models.DateTimeField("end", null=True, blank=True)
    recipient_type = models.CharField(
        choices=RecipientType.choices,
        max_length=50,
        default=RecipientType.COMPANY,
        blank=True,
        null=True,
    )
    template_context = models.JSONField(default=dict, blank=True, null=True)
    recipients = models.ManyToManyField(User, through=NoteUser)
    distribute_via_sms = models.BooleanField(default=True)
    distribute_via_email = models.BooleanField(default=True)
    distribute_via_push_notification = models.BooleanField(default=False)
    is_auto = models.BooleanField(default=False)
    sender = models.ForeignKey(
        User, related_name="notes", null=True, blank=True, on_delete=models.PROTECT
    )

    class Meta:
        verbose_name_plural = "Notes"
        ordering = ["-effective_date"]
        ordering = ("-id",)

    def add_recipients(self, recipients, recipient_type=None):
        recipient_type = recipient_type or self.recipient_type
        recipient_user_ids = []

        if recipient_type == Note.RecipientType.PERSON:
            recipient_user_ids = recipients

        elif recipient_type == Note.RecipientType.ROLE:
            q = Q()
            for role in recipients:
                q |= Q(roles__contains=[role])
            recipient_user_ids = User.objects.filter(is_active=True).filter(q).values_list("id", flat=True)

        elif recipient_type == Note.RecipientType.COMPANY:
            recipient_user_ids = (
                User.objects.filter(company__in=recipients, is_active=True)
                .filter(
                    Q(roles__contains=[User.Role.COMMERCIAL])
                    | Q(roles__contains=[User.Role.CARRIER_DISPATCH])
                    | Q(roles__contains=[User.Role.SITE_ADMINISTRATION])
                    | Q(roles__contains=[User.Role.DRIVER])
                )
                .values_list("id", flat=True)
            )

        for user_id in recipient_user_ids:
            NoteUser(note=self, user_id=user_id).save()

    def print_note(self):
        print(f"""
        Notification Data:
               title: {self.title}
               text: {self.text}
               recipients: {self.recipients.all()[:3]}
               number_of_recipients: {self.recipients.all().count()}
               sender: {self.sender}
               is_auto: {self.is_auto}
               send_email: {self.distribute_via_email}
               send_sms: {self.distribute_via_sms}
              """)

    def send(self):
        from .notify import note_sender
        if settings.DEBUG:
            self.print_note()
            self.distribute_via_sms = False

        note_sender(self)


class PushNotification(models.Model):
    user = models.ForeignKey(
        User, related_name="push_notifications", on_delete=models.CASCADE
    )
    note = models.ForeignKey(
        Note, related_name="push_notifications", on_delete=models.CASCADE
    )
    seen = models.BooleanField(default=False)

    def __str__(self):
        return f"{self.note.title}|{self.user.first_name}|{self.user.last_name}"
