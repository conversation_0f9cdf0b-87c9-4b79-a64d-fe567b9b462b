from itertools import zip_longest
import threading
import time

SLEEP_TIME = 0.1
GROUP_EMAIL_COUNT = 40

from django_q.tasks import async_task
from django.core import mail
from django.core.mail import EmailMessage


class EmailThread(threading.Thread):
    def __init__(self, subject, plain_message, from_email, to_emails, html_message=""):
        self.subject = subject
        self.plain_message = plain_message
        self.from_email = from_email
        self.to_emails = to_emails
        self.html_message = html_message
        super(EmailThread, self).__init__()

    def run(self):
        for to_email in self.to_emails:
            mail_send = mail.send_mail(
                self.subject,
                self.plain_message,
                self.from_email,
                [to_email],
                html_message=self.html_message,
            )
            print(f"Mail send successfully to {to_email}")
            time.sleep(SLEEP_TIME)


def send(subject, body, from_email, to, bcc):
    if len(to) > GROUP_EMAIL_COUNT:
        to_email_chunks = [to[i:i + GROUP_EMAIL_COUNT] for i in range(0, len(to), GROUP_EMAIL_COUNT)]
    else:
        to_email_chunks = [to]

    if len(bcc) > GROUP_EMAIL_COUNT:
        bcc_email_chunks = [bcc[i:i + GROUP_EMAIL_COUNT] for i in range(0, len(bcc), GROUP_EMAIL_COUNT)]
    else:
        bcc_email_chunks = [bcc]

    targets = list(zip_longest(to_email_chunks, bcc_email_chunks, fillvalue=[]))
    for target in targets:
        print(f"sending notification email, subject: {subject}")
        EmailMessage(
            subject=subject, body=body, from_email=from_email, to=target[0], bcc=target[1]
        ).send(fail_silently=False)


def send_email(subject, plain_message, from_email, to_emails, bcc_send=True):
    bcc_send = bcc_send and len(to_emails) > 1

    if bcc_send:
        async_task(
            send,
            subject,
            plain_message,
            from_email,
            [],
            to_emails,
            group="Notification Email",
        )

    else:
        for to_email in to_emails:
            async_task(
                send,
                subject,
                plain_message,
                from_email,
                [to_email],
                [],
                group="Notification Email",
            )
