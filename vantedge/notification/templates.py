from django.utils import timezone

from .models import Note


def get_related_people(location, driver):
    people_list = []
    exclude_list = []
    driver_company = driver.company
    people_list.extend(driver_company.get_dispatchers())
    company = location.company.company
    people_list.extend(company.get_dispatchers())
    people_list.extend(company.get_site_administrators())

    location_company = location.company.company
    # removing people if they set notification_locations and location not match
    result = filter_people(exclude_list, location, location_company, people_list)
    return result


def filter_people(exclude_list, location, location_company, people_list):
    for person in people_list:
        if person.app_config.location_notifications_enabled:
            person_location_notification = person.notification_locations_company(
                location_company
            )

            if person_location_notification:
                if location.id not in person_location_notification:
                    exclude_list.append(person)

        else:
            exclude_list.append(person)

    result = set(people_list) - set(exclude_list)
    return result


def template_booking(**kwargs):
    slot = kwargs["slot"]
    location = slot.location
    user = kwargs["user"]
    sender = kwargs["sender"]

    note = Note(
        title="Load Booking",
        text=f"Location \"{location}\" on {timezone.localtime(slot.start_date).date()} from {timezone.localtime(slot.start_date).strftime('%H:%M')} to {timezone.localtime(slot.end_date).strftime('%H:%M')} is booked for you.",
        recipient_type=Note.RecipientType.PERSON,
        location=location,
        is_auto=True,
        sender=sender,
    )
    note.save()
    note.add_recipients([user.id])
    note.send()

    related_people = get_related_people(location, user)

    note = Note(
        title="Load Booking",
        text=f"Location \"{location}\" on {timezone.localtime(slot.start_date).date()} from {timezone.localtime(slot.start_date).strftime('%H:%M')} to {timezone.localtime(slot.end_date).strftime('%H:%M')} is booked for {user}",
        recipient_type=Note.RecipientType.PERSON,
        location=location,
        distribute_via_sms=False,
        is_auto=True,
        sender=sender,
    )
    note.save()
    note.add_recipients([user.id for user in related_people])
    note.send()


def template_booking_canceled(booking, sender, score):
    slot = booking.slot
    location = slot.location
    user = booking.user
    user_score = user.score

    score_text = ""
    if user_score:
        last_cancellation = booking.get_user_last_cancellation(user, location)
        last_cancellation_time = last_cancellation.data.owners[0].created_at if last_cancellation else None
        if last_cancellation_time:
            next_available_time_text = ""
            next_available_time = location.score_to_time(last_cancellation_time, user.score)
            next_available_time_pretty = next_available_time.strftime("%Y-%m-%d %H:%M")
            if timezone.now() < next_available_time:
                next_available_time_text = f"; next available slot will be after {next_available_time_pretty}"
            if score:
                score_text = f" {score} points added to the driver. Total accumulated points for the driver: {user_score}"
            else:
                score_text = f" No negative points added. Total accumulated points for the driver: {user_score}"
            score_text += f"{next_available_time_text}."

    note = Note(
        title="Booking Canceled!!!",
        text=f"Booking for location \"{location}\" on {timezone.localtime(slot.start_date).date()} from {timezone.localtime(slot.start_date).strftime('%H:%M')} to {timezone.localtime(slot.end_date).strftime('%H:%M')} is Canceled!" + score_text,
        recipient_type=Note.RecipientType.PERSON,
        location=location,
        is_auto=True,
        sender=sender,
    )
    note.save()
    note.add_recipients([user.id])
    note.send()

    related_people = get_related_people(location, user)
    note = Note(
        title="Booking Canceled!!!",
        text=f"Booking for location \"{location}\" on {timezone.localtime(slot.start_date).date()} from {timezone.localtime(slot.start_date).strftime('%H:%M')} to {timezone.localtime(slot.end_date).strftime('%H:%M')} assigned for {user} is Canceled!" + score_text,
        recipient_type=Note.RecipientType.PERSON,
        location=location,
        distribute_via_sms=False,
        is_auto=True,
        sender=sender,
    )

    note.save()
    note.add_recipients([user.id for user in related_people])
    note.send()


def send_note(**kwargs):
    from vantedge.users.models import User

    people_list = []
    exclude_list = []

    location = kwargs.get("location", None)
    company = kwargs.get("company", None)
    roles = kwargs.get("roles", [])
    users = kwargs.get("users", [])
    title = kwargs.get("title", "")
    sender = kwargs.get("sender", None)
    message = kwargs["message"]
    distribute_via_sms = kwargs.get("sms", False)
    distribute_via_email = kwargs.get("email", False)
    additional_user_ids = kwargs.get("additional_user_ids", [])

    people_list.extend(users)

    if location:
        company = location.company.company

    if roles:
        # do nothing if role provided but company is not
        if company:
            for role in roles:
                if role == User.Role.CARRIER_DISPATCH:
                    people_list.extend(company.get_dispatchers())
                elif role == User.Role.SITE_ADMINISTRATION:
                    people_list.extend(company.get_site_administrators())
                elif role == User.Role.DRIVER:
                    people_list.extend(company.get_drivers())

    # removing people if they set notification_locations and location not match
    people_list = filter_people(exclude_list, location, company, people_list)

    note = Note(
        title=title,
        text=message,
        recipient_type=Note.RecipientType.PERSON,
        location=location,
        is_auto=True,
        distribute_via_sms=distribute_via_sms,
        distribute_via_email=distribute_via_email,
        sender=sender,
    )
    note.save()
    user_ids = [user.id for user in people_list]
    user_ids.extend(additional_user_ids)
    note.add_recipients(user_ids)
    note.send()

