from vantedge.notification.templates import get_related_people
from vantedge.wheelhouse.tests.base.base_api_test_case import BaseTestCase
from vantedge.wheelhouse.tests.factories.app_config_data import AppConfigDataFactory
from vantedge.wheelhouse.tests.factories.company import WheelhouseCompanyFactory
from vantedge.wheelhouse.tests.factories.location import LocationFactory
from vantedge.wheelhouse.tests.factories.notification_location import NotificationLocationFactory
from vantedge.wheelhouse.tests.factories.user import UserFactory


class TestTemplates(BaseTestCase):
    def test_get_related_people__return_user(self):
        related_person = UserFactory(
            roles=['Site Administration']
        )
        wheelhouse_company = WheelhouseCompanyFactory()
        related_person.company = wheelhouse_company.company
        location = LocationFactory(company_id=wheelhouse_company.id)
        notification_location = NotificationLocationFactory(company=related_person.company.id, locations=[location.id])
        app_config = AppConfigDataFactory(
            allow_email=True,
            allow_push_notifications=True,
            allow_sms=True,
            location_notifications_enabled=True,
            notification_locations=[notification_location]
        )
        related_person.app_config = app_config
        related_person.save()

        people = get_related_people(location, self.user)

        self.assertEqual(len(people), 1)
        self.assertEqual(list(people)[0], related_person)

    def test_get_related_people__location_notifications_disabled__return_none(self):
        related_person = UserFactory(
            roles=['Site Administration']
        )
        wheelhouse_company = WheelhouseCompanyFactory()
        related_person.company = wheelhouse_company.company
        location = LocationFactory(company_id=wheelhouse_company.id)
        notification_location = NotificationLocationFactory(company=related_person.company.id, locations=[location.id])
        app_config = AppConfigDataFactory(
            allow_email=True,
            allow_push_notifications=True,
            allow_sms=True,
            location_notifications_enabled=False,
            notification_locations=[notification_location]
        )
        related_person.app_config = app_config
        related_person.save()

        people = get_related_people(location, self.user)

        self.assertEqual(len(people), 0)
