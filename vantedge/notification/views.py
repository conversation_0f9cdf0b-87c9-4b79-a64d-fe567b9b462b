from rest_framework.permissions import IsAuthenticated
from rest_framework.viewsets import GenericViewSet
from rest_framework.mixins import CreateModelMixin, RetrieveModelMixin, ListModelMixin
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.pagination import LimitOffsetPagination
from rest_framework import permissions

from django_filters import FilterSet, DateTime<PERSON>ilter, BooleanFilter
from django.db.models import Prefetch

from vantedge.wheelhouse.models import Location
from .models import Note, NoteUser
from .serializer import NoteSerializer


class ReadOnlyPermission(permissions.BasePermission):
    def has_permission(self, request, view):
        if request.user.is_superuser or request.user.is_staff:
            return True

        if request.method not in permissions.SAFE_METHODS:
            if request.user.has_perm("notification.notification_read_only_access"):
                return False

        return True


class NoteFilter(FilterSet):
    is_auto = BooleanFilter(field_name="is_auto")
    effective_date__gte = DateTimeFilter(field_name="effective_date", lookup_expr="gte")
    effective_date__lte = DateTimeFilter(field_name="effective_date", lookup_expr="lte")
    class Meta:
        model = Note
        fields = ["effective_date", "location", "is_auto"]


class NoteViewSet(GenericViewSet, CreateModelMixin, RetrieveModelMixin, ListModelMixin):
    serializer_class = NoteSerializer
    queryset = Note.objects.all()
    permission_classes = [IsAuthenticated, ReadOnlyPermission]
    pagination_class = LimitOffsetPagination
    filterset_class = NoteFilter

    def get_queryset(self):
        return Note.objects.filter(
            location__in=Location.company_locations(
                self.request.user.company.wheelhouse
            ).prefetch_related(
                "sender",
                "sender__company",
                Prefetch(
                    "recipients", queryset=NoteUser.objects.select_related("user")
                ),
            )
        )

    @action(methods=["GET"], detail=False)
    def note_types(self, request):
        return Response(
            [
                dict(label=note_type.label, value=note_type.value)
                for note_type in Note.NoteType
            ]
        )

    @action(detail=False, methods=["GET"], permission_classes=[IsAuthenticated])
    def count(self, request):
        return Response(self.filter_queryset(self.get_queryset()).count())
