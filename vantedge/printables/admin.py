from django.contrib import admin
from django.contrib.contenttypes.admin import GenericTabularInline
from vantedge.attachment.models import Attachment
from .models import PrintableTemplate


class AttachmentInline(GenericTabularInline):
    model = Attachment
    extra = 1


@admin.register(PrintableTemplate)
class PrintableTemplateAdmin(admin.ModelAdmin):
    list_display = ("company", "code", "base")
    inlines = [AttachmentInline]
