import logging

logger = logging.getLogger(__name__)


def generate_report(reportId, target="distribution", user_email=None):
    from vantedge.reports.models import Report

    report = Report.objects.get(id=reportId)
    print(f"Processing report {report.name}")
    try:
        report.generate(target, user_email)
    except Exception as e:
        logger.exception(
            f"Auto Generate Report Failure - [{report.company}][{report}]: [{e}]"
        )
