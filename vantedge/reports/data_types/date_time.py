from .date import date
from ..utils import process_for_query, process_for_query_Q
from .date import timezoned_query as timezoned_query_with_site
from dateutil.parser import parse
from django.db.models import Q
import datetime
from zoneinfo import ZoneInfo

from vantedge.terminalboss.models import SiteUser


def timezoned_query(report, internal_query_function):
    if report.source == "TerminalBossTicket":
        site_list = (
            report.configuration["columns"].get("origin_site", {}).get("filters", [])
        )
        if site_list:
            site_user = SiteUser.objects.filter(
                username__in=site_list, company__company=report.company.company
            )
        else:
            site_user = SiteUser.objects.filter(company__company=report.company.company)

        query = Q()
        for site in site_user:
            tz = site.site_timezone
            query |= internal_query_function(tz, site_user__username=site.username)

    else:
        tz = ZoneInfo(report.configuration.get("timezone"))
        query = internal_query_function(tz)

    return query


def process_date_exact(report, extra_filter, orm_filter, label, filters):
    def internal_query_function(tz, **kwargs):
        site_user = kwargs.pop("site_user", None)
        hour = site_user.day_start.hour if site_user else 0
        minute = site_user.day_start.minute if site_user else 0

        q = Q()
        if filters:
            start = parse(filters).replace(
                hour=hour, minute=minute, second=0, microsecond=0, tzinfo=tz
            )
            end = (
                start + datetime.timedelta(days=1) - datetime.timedelta(microseconds=1)
            )
            q &= Q(**{f"{label}__gte": start, f"{label}__lte": end})
            if kwargs:
                q &= Q(**kwargs)

        return q

    query = timezoned_query_with_site(report, internal_query_function)
    return process_for_query_Q(extra_filter, query)


def process_date_excludes(report, extra_filter, orm_filter, label, filters):
    def internal_query_function(tz, **kwargs):
        site_user = kwargs.pop("site_user", None)
        hour = site_user.day_start.hour if site_user else 0
        minute = site_user.day_start.minute if site_user else 0

        q = Q()
        if filters:
            start = parse(filters).replace(
                hour=hour, minute=minute, second=0, microsecond=0, tzinfo=tz
            )
            end = (start + datetime.timedelta(days=1)) - datetime.timedelta(microseconds=1)
            
            q &= ~Q(**{f"{label}__gte": start, f"{label}__lte": end})
            
            if kwargs:
                q &= Q(**kwargs)

        return q

    query = timezoned_query(report, internal_query_function)
    return process_for_query_Q(extra_filter, query)


def process_datetime_between(report, extra_filter, orm_filter, label, filters):
    fromFilter = filters[0].get("from")
    toFilter = filters[0].get("to")

    return_query = Q()

    def internal_query_function(tz, **kwargs):
        q = Q()
        if fromFilter:
            dtFrom = parse(fromFilter).replace(tzinfo=tz)
            q &= Q(**{orm_filter[0].format(label): dtFrom})
        if toFilter:
            dtTo = parse(toFilter).replace(tzinfo=tz)
            q &= Q(**{orm_filter[1].format(label): dtTo})

        if kwargs:
            q &= Q(**kwargs)

        return q

    if filters:  # throws NoneType object is not iterable
        query = timezoned_query(report, internal_query_function)
        return_query = (return_query | query) if return_query else query

    return process_for_query_Q(extra_filter, return_query)


def process_datetime_aware(report, extra_filter, orm_filter, label, filters):
    def internal_query_function(tz, **kwargs):
        q = Q()
        if filters:
            dateSelected = parse(filters).replace(tzinfo=tz)
            q &= Q(**{orm_filter.format(label): dateSelected})
            if kwargs:
                q &= Q(**kwargs)

        return q

    query = timezoned_query(report, internal_query_function)
    return process_for_query_Q(extra_filter, query)


def process_date_yesterday_time_range(report, extra_filter, orm_filter, label, filters):
    end_time = filters[0]["to"]
    start_time = filters[0]["from"]

    def internal_query_function(tz, **kwargs):
        yesterday = datetime.datetime.now(tz) - datetime.timedelta(days=1)
        start = yesterday.replace(
            hour=int(start_time.split(":")[0]),
            minute=int(start_time.split(":")[1]),
            second=0,
            microsecond=0,
        )
        end = yesterday.replace(
            hour=int(end_time.split(":")[0]),
            minute=int(end_time.split(":")[1]),
            second=0,
            microsecond=0,
        )

        q = {f"{label}__gte": start, f"{label}__lt": end}
        q.update(kwargs)
        return Q(**q)

    query = timezoned_query(report, internal_query_function)
    return process_for_query_Q(extra_filter, query)


def process_date_today_time_range(report, extra_filter, orm_filter, label, filters):
    end_time = filters[0]["to"]
    start_time = filters[0]["from"]

    def internal_query_function(tz, **kwargs):
        today = datetime.datetime.now(tz)
        start = today.replace(
            hour=int(start_time.split(":")[0]),
            minute=int(start_time.split(":")[1]),
            second=0,
            microsecond=0,
        )
        end = today.replace(
            hour=int(end_time.split(":")[0]),
            minute=int(end_time.split(":")[1]),
            second=0,
            microsecond=0,
        )
        q = {f"{label}__gte": start, f"{label}__lt": end}
        q.update(kwargs)
        return Q(**q)

    query = timezoned_query(report, internal_query_function)
    return process_for_query_Q(extra_filter, query)


def process_date_yesterday_today_time_range(
    report, extra_filter, orm_filter, label, filters
):
    end_time = filters[0]["to"]
    start_time = filters[0]["from"]

    def internal_query_function(tz, **kwargs):
        today = datetime.datetime.now(tz)
        yesterday = today - datetime.timedelta(days=1)
        start = yesterday.replace(
            hour=int(start_time.split(":")[0]),
            minute=int(start_time.split(":")[1]),
            second=0,
            microsecond=0,
        )
        end = today.replace(
            hour=int(end_time.split(":")[0]),
            minute=int(end_time.split(":")[1]),
            second=0,
            microsecond=0,
        )

        q = {f"{label}__gte": start, f"{label}__lt": end}
        q.update(kwargs)
        return Q(**q)

    query = timezoned_query(report, internal_query_function)
    return process_for_query_Q(extra_filter, query)


date_time = {
    "options": {
        "equals": {
            "label": "Exact",
            "search_function": process_date_exact,
            "input": "date",
        },
        "excludes": {
            "label": "Excludes",
            "search_function": process_date_excludes,
            "input": "date",
        },
        "empty": {"label": "Empty", "orm_string": "{0}__isnull", "input": "checkbox"},
        "equalsDT": {
            "label": "Exact (Date/Time)",
            "search_function": process_datetime_aware,
            "input": "dateTime",
        },
        "GTDT": {
            "label": "> (Date/Time)",
            "orm_string": "{0}__gte",
            "search_function": process_datetime_aware,
            "input": "dateTime",
        },
        "LTDT": {
            "label": "< (Date/Time)",
            "orm_string": "{0}__lte",
            "search_function": process_datetime_aware,
            "input": "dateTime",
        },
        "betweenDT": {
            "label": "Between",
            "orm_string": ["{0}__gte", "{0}__lte"],
            "search_function": process_datetime_between,
            "input": "dateRangeDT",
        },
        "yesterdayTimeRange": {
            "label": "Yesterday Between",
            "orm_string": ["{0}__gte", "{0}__lte"],
            "search_function": process_date_yesterday_time_range,
            "input": "yesterdayRangeTime",
        },
        "todayTimeRange": {
            "label": "Today Between",
            "orm_string": ["{0}__gte", "{0}__lte"],
            "search_function": process_date_today_time_range,
            "input": "todayTimeRange",
        },
        "yesterdayTodayTimeRange": {
            "label": "Yesterday to Today",
            "orm_string": ["{0}__gte", "{0}__lte"],
            "search_function": process_date_yesterday_today_time_range,
            "input": "yesterdayTodayTimeRange",
        },
        **{
            key: date["options"][key]
            for key in date["options"]
            if key not in ["equals", "GT", "LT", "between"]
        },
    }
}
