from datetime import timedelta
from django.db.models import Q
from ..utils import process_for_query, process_for_query_Q


def process_duration(report, extra_filter, orm_filter, label, filters):
    query = Q()
    if filters:
        return process_for_query(
            extra_filter, orm_filter.format(label), timedelta(days=int(filters))
        )
    return process_for_query_Q(extra_filter, query)


def process_duration_between(report, extra_filter, orm_filter, label, filters):
    query = Q()
    if filters:
        days_from, days_to = filters
        query &= Q(
            **{
                f"{label}__gte": timedelta(days=days_from),
                f"{label}__lte": timedelta(days=days_to),
            }
        )
    return process_for_query_Q(extra_filter, query)


duration = {
    "options": {
        "equals": {
            "label": "Exact Days",
            "search_function": process_duration,
            "input": "number",
        },
        "GT": {
            "label": "> # Days",
            "orm_string": "{0}__gte",
            "search_function": process_duration,
            "input": "number",
        },
        "LT": {
            "label": "< # Days",
            "orm_string": "{0}__lte",
            "search_function": process_duration,
            "input": "number",
        },
        "between": {
            "label": "Between",
            "search_function": process_duration_between,
            "input": "doubleNumber",
        },
    }
}
