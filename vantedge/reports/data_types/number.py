from django.db.models import Q
from ..utils import process_for_query, process_for_query_Q


def process_number(report, extra_filter, orm_filter, label, filters):
    query = Q()
    if filters:
        return process_for_query(extra_filter, orm_filter.format(label), filters)
    return process_for_query_Q(extra_filter, query)

def process_number_exclude(report, extra_filter, orm_filter, label, filters):
    query = Q()
    if filters:
        filters = list(map(lambda f: float(f), filters))
        query &= ~Q(**{orm_filter.format(label): filters})
    return process_for_query_Q(extra_filter, query)

def process_numbers(report, extra_filter, orm_filter, label, filters):
    query = Q()
    if filters:
        filters = list(map(lambda f: float(f), filters))
        return process_for_query(extra_filter, orm_filter.format(label), filters)
    return process_for_query_Q(extra_filter, query)


def process_number_between(report, extra_filter, orm_filter, label, filters):
    query = Q()
    if filters:
        number_from, number_to = filters
        query &= Q(
            **{f"{label}__gte": int(number_from), f"{label}__lte": int(number_to)}
        )
    return process_for_query_Q(extra_filter, query)


number = {
    "options": {
        "equals": {
            "label": "Exact",
            "input": "select",
            "orm_string": "{0}__in",
            "search_function": process_numbers,
        },
        "GT": {
            "label": ">",
            "orm_string": "{0}__gt",
            "input": "number",
            "search_function": process_number,
        },
        "LT": {
            "label": "<",
            "orm_string": "{0}__lt",
            "input": "number",
            "search_function": process_number,
        },
        "between": {
            "label": "Between",
            "search_function": process_number_between,
            "input": "doubleNumber",
        },
        "excludes": {
            "label": "Excludes",
            "input": "select",
            "orm_string": "{0}__in",
            "search_function": process_number_exclude,
        },
    }
}
