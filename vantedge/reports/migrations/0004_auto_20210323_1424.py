# Generated by Django 3.1.6 on 2021-03-23 20:24

from django.db import migrations, models
import vantedge.util.json.encoder
import json


def calc_report_json(apps, schema_editor):
    Report = apps.get_model("reports", "Report")
    reports = Report.objects.all()
    for report in reports:
        if report.schedule == "Daily":
            report.schedule = [{"date": "", "interval": "Daily", "time": "08:00"}]
        else:
            report.schedule = {}
    Report.objects.bulk_update(reports, ["schedule"])


class Migration(migrations.Migration):

    dependencies = [("reports", "0003_auto_20210323_1423")]

    operations = [
        migrations.RunPython(calc_report_json),
        migrations.AlterField(
            model_name="report",
            name="schedule",
            field=models.JSONField(
                blank=True,
                encoder=vantedge.util.json.encoder.VantedgeEncoder,
                null=True,
            ),
        ),
    ]
