# Generated by Django 3.1.6 on 2021-03-23 20:24

from django.db import migrations, models
import vantedge.util.json.encoder
import json, arrow

def fix_loaded_columns(apps, schema_editor):
    Report = apps.get_model('reports', 'Report')

    reports = Report.objects.all()
    for report in reports:
        if report.configuration:
            if report.configuration["columns"]["loaded"]["filters"] == ['Loaded']:
                report.configuration["columns"]["loaded"]["filters"] = True
            elif report.configuration["columns"]["loaded"]["filters"] == ['Empty']:
                report.configuration["columns"]["loaded"]["filters"] = False
            elif report.configuration["columns"]["loaded"]["filters"] == True or report.configuration["columns"]["loaded"]["filters"] == False:
                report.configuration["columns"]["loaded"]["filters"] = report.configuration["columns"]["loaded"]["filters"]
            else:
                report.configuration["columns"]["loaded"]["filters"] = None
    Report.objects.bulk_update(reports, ['configuration'])

class Migration(migrations.Migration):

    dependencies = [
        ('reports', '0005_schedule_next_run'),
    ]

    operations = [
        migrations.RunPython(fix_loaded_columns, migrations.RunPython.noop),
    ]
