# Generated by Django 3.2.7 on 2022-11-28 17:18

from django.db import migrations, models

replace_space = lambda items : [field.replace(" ", "_") for field in items]

def fix_annotations(apps, schema_editor):
    Report = apps.get_model('reports', 'Report')

    reports = Report.objects.filter(source="LoadedRailCar")
    for report in reports:
        if report.configuration:
            configuration_columns = {}
            sort_order = []
            report.configuration["order"] = replace_space(report.configuration["order"])
            for item in report.configuration["sort_order"]:
                if isinstance(item, dict):
                    sort_order.append(item)
                    item["name"].replace(" ", "_")
                else:
                    sort_order.append(item.replace(" ", "_"))

            report.configuration["additional_filters"] = replace_space(report.configuration["order"])
            for key in report.configuration["columns"].keys():
                if " " in key:
                    new_key = key.replace(" ", "_")
                    configuration_columns[new_key] = report.configuration["columns"][key]
                    configuration_columns[new_key]["id"] = configuration_columns[new_key]["id"].replace(" ", "_")
                    configuration_columns[new_key]["label"] = configuration_columns[new_key]["label"].replace(" ", "_")
                else:
                    configuration_columns[key] = report.configuration["columns"][key]

        report.save()

class Migration(migrations.Migration):

    dependencies = [
        ('reports', '0014_alter_report_source'),
    ]

    operations = [
        migrations.RunPython(fix_annotations, migrations.RunPython.noop),
    ]
