import re

from django.db import migrations


def camel_to_snake(item):
    return re.sub(r'(?<!^)(?=[A-Z])', '_', item).lower()

def migrate_camel_to_snake_report(report):
    if report.configuration:

        sort_order = []
        report.configuration["order"] = [camel_to_snake(item) for item in report.configuration["order"]]

        for item in report.configuration["sort_order"]:
            if isinstance(item, dict):
                sort_order.append(item)
                item["name"] = camel_to_snake(item["name"])
            else:
                sort_order.append(camel_to_snake(item))

        report.configuration["additional_filters"] = [camel_to_snake(item) for item in report.configuration["additional_filters"]]

    report.save()

def migrate_camel_to_snake_reports(apps, schema_editor):
    Report = apps.get_model('reports', 'Report')
    reports = Report.objects.all()

    for report in reports:
        migrate_camel_to_snake_report(report)

class Migration(migrations.Migration):

    dependencies = [
        ('reports', '0016_reportcompany_available_permissions'),
    ]

    operations = [
        migrations.RunPython(migrate_camel_to_snake_reports, migrations.RunPython.noop),
    ]
