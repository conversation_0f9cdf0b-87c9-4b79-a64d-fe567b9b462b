# Generated by Django 4.2 on 2023-09-25 22:48

from django.db import migrations


def init_report_config_columns_alt_label(apps, schema_editor):
    """
    Set alt_label based on label for all reports.
    """
    Report = apps.get_model("reports", "Report")
    for report in Report.objects.all():
        columns = report.configuration["columns"]

        for column in columns:
            label = report.configuration["columns"][column]["label"]
            report.configuration["columns"][column]["alt_label"] = label

        report.save(update_fields=["configuration"])


def del_report_config_columns_alt_label(apps, schema_editor):
    """
    Remove alt_label for all reports.
    """
    Report = apps.get_model("reports", "Report")
    for report in Report.objects.all():
        columns = report.configuration["columns"]

        for column in columns:
            report.configuration["columns"][column].pop("alt_label")

        report.save(update_fields=["configuration"])


class Migration(migrations.Migration):
    dependencies = [
        ("reports", "0020_alter_report_source"),
    ]

    operations = [
        migrations.RunPython(
            init_report_config_columns_alt_label, del_report_config_columns_alt_label
        ),
    ]
