# Generated by Django 4.2 on 2023-11-22 23:01

from django.db import migrations
from vantedge.reports import configuration


REPLACEMENT_COLUMN = {
    # --- loadedrailcar [& shared]
    "net_volume_(l)": "net_volume",
    "net_volume_(usg)": "net_volume",
    # --
    "outage_volume_(l)": "outage_volume",
    "outage_volume_(usg)": "outage_volume",
    # --
    "load_temperature_(c)": "load_temperature",
    "load_temperature_(f)": "load_temperature",
    # --
    "density_(kg/l)": "density",
    "density_(lb/usg)": "density",
    # --
    "shell_capacity_(l)": "shell_capacity",
    "shell_capacity_(usg)": "shell_capacity",
    # --
    "weight_(kg)": "weight",
    # --- mileage
    "loaded_miles": "loaded_distance",
    "empty_miles": "empty_distance",
    "total_miles": "total_distance",
    "excess_empty": "excess_empty_distance",
}

RESET_COLUMN = {
    # --- loadedrailcar [& shared]
    "net_volume (l)": "net_volume_(l)",
    "net_volume (usg)": "net_volume_(usg)",
    # --
    "outage_volume (l)": "outage_volume_(l)",
    "outage_volume (usg)": "outage_volume_(usg)",
    # --
    "load_temperature (c)": "load_temperature_(c)",
    "load_temperature (f)": "load_temperature_(f)",
    # --
    "density (kg/l)": "density_(kg/l)",
    "density (lb/usg)": "density_(lb/usg)",
    # --
    "shell_capacity (l)": "shell_capacity_(l)",
    "shell_capacity (usg)": "shell_capacity_(usg)",
    # --
    "weight (kg)": "weight_(kg)",
}


def convert_report_quantities_to_standard(apps, schema_editor):
    """
    Standardize reports by source-type
    """
    Report = apps.get_model("reports", "Report")

    for report in Report.objects.all():
        source_type = report.source

        match source_type:
            case configuration.ReportSourceType.TRIPS:
                convert_to_standard(report)
            case configuration.ReportSourceType.ROUNDTRIPS:
                convert_to_standard(report)
            case configuration.ReportSourceType.DETENTION:
                convert_to_standard(report)
            case configuration.ReportSourceType.MILEAGE:
                convert_to_standard(report)
            case configuration.ReportSourceType.TERMINALBOSSV2TICKET:
                convert_to_standard(report)
            case configuration.ReportSourceType.TERMINALBOSSV3TICKET:
                # Most of these fields are built dynamically
                pass
            case configuration.ReportSourceType.SHAREDTERMINALBOSSV2TICKET:
                convert_to_standard(report)
            case configuration.ReportSourceType.SHAREDRAILSHIPMENT:
                convert_to_standard(report)
            case configuration.ReportSourceType.SHAREDLOADEDRAILCAR:
                prep_for_conversion(report)
                convert_to_standard(report)
            case configuration.ReportSourceType.LOADEDRAILCAR:
                prep_for_conversion(report)
                convert_to_standard(report)
            case configuration.ReportSourceType.INVOICEITEM:
                convert_to_standard(report)
            case configuration.ReportSourceType.SCHEDULE:
                convert_to_standard(report)
            case _:
                pass


# --- reset columns


def prep_for_conversion(report):
    # Report -> None
    """
    Convert columns/fields to prepare for
    `convert_to_standard`.
    """
    data_columns = get_data_columns(report)
    config_columns = report.configuration["columns"]

    reset_columns(data_columns, config_columns, report)

    report.save(update_fields=["configuration"])


def reset_columns(data_columns, config_columns, report):
    # dict dict Report -> None
    """
    Fixing this:
    config-columns-keys -> ['commodity', ..., 'net_volume (l)']*
    data-columns-keys -> ['commodity', ..., 'net_volume_(l)']
    report-column-order -> ['commodity', ..., 'net_volume_(l)']

    Should be:
    config-columns-keys -> ['commodity', ..., 'net_volume_(l)']
    """
    columns_to_reset = [
        c
        for c in config_columns.keys()
        if (
            c.replace(" ", "_") in data_columns.keys()
        )  # correct form is in data-columns
        and (
            c.replace(" ", "_") in report.configuration["order"]
        )  # correct form is in config-order
        and (
            c not in report.configuration["order"]
        )  # [but] bad* form is in not in config-order
    ]

    for col_key in columns_to_reset:
        reset_col_key = RESET_COLUMN.get(col_key)

        if reset_col_key:
            config_column = config_columns[col_key]

            report.configuration["columns"][reset_col_key] = {
                **config_column,
            }


# ---


def convert_to_standard(report):
    # Report -> None
    """
    Convert columns/fields to their standardized uom.

    These values are hard-coded in the source-type for
    each field.
    """
    data_columns = get_data_columns(report)
    config_columns = report.configuration["columns"]

    apply_new_standard(data_columns, config_columns, report)
    replace_obsolete_columns(data_columns, config_columns, report)
    standardize_columns(data_columns, config_columns, report)

    report.save(update_fields=["configuration"])


# --- apply standard


def apply_new_standard(data_columns, config_columns, report):
    # dict dict Report -> None
    """
    Overwrite the `report` configuration for each column with
    configurations from data_columns
    """
    for col_key in data_columns:
        if col_key in config_columns:
            data_column = data_columns[col_key]
            config_column = config_columns[col_key]

            report.configuration["columns"][col_key] = {**config_column, **data_column}


# --- remove obsolete


def replace_obsolete_columns(data_columns, config_columns, report):
    # dict dict Report -> None
    """
    Replace obsolete columns with the corresponding new
    standard column from the revised configuration
    """
    for col_key in config_columns.copy():
        if config_columns[col_key].get("obsolete"):
            replace_column_with_standard(col_key, data_columns, report)


def replace_column_with_standard(col_key, data_columns, report):
    # str Report -> None
    """
    Replace column (with `REPLACEMENT_COLUMN`) without
    duplication.
    """
    replacement_col_key = REPLACEMENT_COLUMN[col_key]
    data_column = data_columns[replacement_col_key]

    if replacement_col_key not in report.configuration["order"]:
        add_column(replacement_col_key, data_column, report)

    remove_column(col_key, report)


def add_column(col_key, data_column, report):
    # str Report -> None
    """
    Add a column by the given `col_key` to the report
    and initialize its configuration.
    """
    report.configuration["columns"][col_key] = {
        "label": col_key,
        "alt_label": col_key,
        "format": data_column.get("format"),
        "precision": data_column.get("precision"),
        "uom": data_column.get("uom"),
        # "unit": data_column.get('unit'),
        "id": col_key,
        "filters": [],
        "data_type": data_column.get("type"),
        "group": data_column.get("group"),
        "caption": data_column.get("caption"),
        "filter_chosen": "equals",
    }
    report.configuration["order"].append(col_key)


def remove_column(col_key, report):
    # str Report -> None
    report.configuration["columns"].pop(col_key)
    report.configuration["order"] = [
        x for x in report.configuration["order"] if x != col_key
    ]


# --- standardize


def standardize_columns(data_columns, config_columns, report):
    # dict dict Report -> None
    for col_key in data_columns.keys():
        if col_key in config_columns:
            data_column = data_columns[col_key]

            if data_column.get("uom"):
                standardize_column(data_column, col_key, report)


def standardize_column(data_column, col_key, report):
    # dict str Report -> None
    uom_value = data_column.get("uom")
    precision_value = data_column.get("precision")

    report.configuration["columns"][col_key]["uom"] = uom_value
    report.configuration["columns"][col_key]["precision"] = precision_value


# --- helpers


def get_data_columns(report):
    # str -> dict
    return configuration.get_raw_data_class(report.source)(report).columns


class Migration(migrations.Migration):
    dependencies = [
        ("reports", "0023_alter_report_source"),
    ]

    operations = [
        # This is irrevesible ...
        # how can one tell which REPLACEMENT_COLUMN was used
        migrations.RunPython(convert_report_quantities_to_standard),
    ]
