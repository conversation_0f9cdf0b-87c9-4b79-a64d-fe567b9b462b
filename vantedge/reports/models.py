# from django.db import models
from io import BytesIO
import json
import time
import arrow
import copy
import logging
import zoneinfo
import pandas as pd
import pdpipe as pdp
from dirtyfields import DirtyFieldsMixin
from django.conf import settings
from django.core.exceptions import ValidationError
from django.utils import timezone
from django.contrib.auth.models import Permission
from django.contrib.gis.db import models
from django.contrib.postgres.fields import ArrayField
from django_q.tasks import async_task
from django.db.models.signals import m2m_changed
from model_utils import Choices
from model_utils.models import TimeStampedModel

from vant_email.base import PriorityEmailMessage
from vantedge.reports import configuration
from vantedge.reports.common import generate_report

from vantedge.users.models import Company
from vantedge.users.models.application_configuration import company_permissions_handler
from vantedge.util.json.encoder import VantedgeEncoder
from vantedge.util.download.utils import save_as_uuid_download
from vantedge.util.mixins import EasyAuditModelConfigMixin

from .data_types import data_types
from .utils import process_for_query, process_for_query_Q, process_for_query_OR
from .format import process_display_format
from .units import process_uom, convert_quantity, label_columns_with_unit
from .email import CompanyEmailSender
logger = logging.getLogger(__name__)


class ReportCompany(EasyAuditModelConfigMixin, models.Model):
    company = models.OneToOneField(
        Company, related_name="reporting", on_delete=models.CASCADE
    )

    available_permissions = models.ManyToManyField(
        Permission, blank=True, limit_choices_to={"content_type__app_label": "reports"}
    )

    configuration = models.JSONField(default=dict, blank=True, null=True)

    class Meta:
        verbose_name_plural = "Companies"

    def __str__(self):
        return self.company.name


m2m_changed.connect(
    company_permissions_handler, sender=ReportCompany.available_permissions.through
)


def validate_report_source(value):
    report_sources = dict(configuration.SOURCE)
    if value not in report_sources:
        raise ValidationError(f"{value} is not a valid report source type")


class Report(EasyAuditModelConfigMixin, DirtyFieldsMixin, TimeStampedModel):
    XLSX = "XLSX"
    CSV = "CSV"
    FILE_FORMAT_CHOICES = (
        (CSV, CSV),
        (XLSX, XLSX)
    )

    SCHEDULE = Choices(("Daily", "Daily"), ("No Automation", "No Automation"))

    company = models.ForeignKey(
        ReportCompany, on_delete=models.CASCADE, related_name="reports"
    )
    name = models.CharField(max_length=100, default="New Report", blank=True, null=True)
    source = models.CharField(
        # Remove choices to prevent migration for new reports
        # choices=configuration.SOURCE,
        validators=[validate_report_source],
        max_length=50,
        default="Trips",
        blank=True,
        null=True,
    )
    schedule = models.JSONField(encoder=VantedgeEncoder, null=True, blank=True)
    # distribution = models.CharField(max_length=500, default="", blank=True, null=True)
    distribution = ArrayField(
        models.CharField(max_length=500, default="", blank=True, null=True),
        default=list,
        blank=True,
    )
    configuration = models.JSONField(encoder=VantedgeEncoder, null=True, blank=True)
    file_format = models.CharField(max_length=10, choices=FILE_FORMAT_CHOICES, default=XLSX)


    def get_data_classname(self):
        if self.source:
            return configuration.get_raw_data_class(self.source)(self).__class__.__name__
        return ''

    def get_data_columns(self) -> dict:
        if not hasattr(self, "_columns"):
            self._columns = configuration.get_raw_data_class(self.source)(self).columns
        return self._columns
        # return data_columns[f"{self.source.lower()}_columns"]

    def get_selected_columns(self):
        """
        returns a list of selected columns of the report
        """
        if not self.configuration:
            return []

        return self.configuration.get("order", [])

    def get_data_type(self, column):
        data_column = self.get_data_columns()
        return data_column[column].get("type", "")
        # options = data_types[data_type]["options"]
        # return [{ "label": options[key]["label"], "value": key } for key in options]

    def get_uom(self, column):
        data_column = self.get_data_columns()
        return data_column[column].get("uom", "")

    def get_obsolete(self, column):
        data_column = self.get_data_columns()
        return data_column[column].get("obsolete", "")

    def get_format(self, column):
        data_column = self.get_data_columns()
        return data_column[column].get("format", "")

    def get_precision(self, column):
        data_column = self.get_data_columns()
        return data_column[column].get("precision", "")

    def get_caption(self, column):
        data_column = self.get_data_columns()
        return data_column[column].get("caption", "")

    def get_group(self, column):
        data_column = self.get_data_columns()
        return data_column[column].get("group", "")

    def get_label(self, column):
        data_column = self.get_data_columns()
        return data_column[column].get("label", column)

    def auto_config(self, force=True):
        if not self.configuration:
            self.configuration = {"source": None}

        source_data_columns = self.get_data_columns()

        if "additional_filters" not in self.configuration:
            self.configuration["additional_filters"] = []

        if "order" not in self.configuration:
            self.configuration["order"] = []

        if "sort_order" not in self.configuration:
            self.configuration["sort_order"] = []

        if "columns" not in self.configuration:
            self.configuration["columns"] = {}

        if "timezone" not in self.configuration:
            self.configuration["timezone"] = settings.TIME_ZONE

        if force or self.source != self.configuration["source"]:
            self.configuration["source"] = self.source
            self.configuration["order"] = self.configuration.get("order", [])
            self.configuration["sort_order"] = self.configuration.get("sort_order", [])
            self.configuration["columns"] = self.configuration.get("columns", {})
            for column in source_data_columns.keys():
                underscore_column = column
                if self.configuration["columns"].get(underscore_column, False):
                    self.configuration["columns"][underscore_column] = {
                        **self.configuration["columns"][underscore_column],
                        "data_type": self.get_data_type(underscore_column),
                        "group": self.get_group(underscore_column),
                        "caption": self.get_caption(underscore_column),
                        "filter_chosen": self.configuration["columns"][
                            underscore_column
                        ].get("filter_chosen", "equals"),
                    }
                else:
                    self.configuration["columns"][underscore_column] = {
                        "label": self.get_label(underscore_column),
                        "alt_label": self.get_label(underscore_column),
                        "format": self.get_format(underscore_column),   # for date-time type
                        "precision": self.get_precision(underscore_column),    # for number type
                        "uom": self.get_uom(underscore_column), # standard for a quantity [from query/...py] (immutable)
                        # "unit": self.get_uom(underscore_column),
                        "unit": "",
                        "obsolete": self.get_obsolete(underscore_column),
                        "id": underscore_column,
                        "filters": [],
                        "data_type": self.get_data_type(underscore_column),
                        "group": self.get_group(underscore_column),
                        "caption": self.get_caption(underscore_column),
                        "filter_chosen": "equals",
                    }

            for column in copy.deepcopy(self.configuration["columns"]):
                if not source_data_columns.get(column, False):
                    del self.configuration["columns"][column]

            self.configuration = self.configuration

    def get_raw_data(self, query=None, sort=False, fields=[]) -> pd.DataFrame:
        if self.source:
            result = configuration.get_raw_data_class(self.source)(self).get_raw_data(
                self.company, query, sort, fields
            )
            return result
        return pd.DataFrame()

    def get_labeled_data(self, df: pd.DataFrame) -> pd.DataFrame:
        # Set the column name of the data columns to the column label if it exists
        if self.source:
            labeled_df = configuration.get_raw_data_class(self.source)(
                self, df
            ).get_labeled_data()
            return labeled_df
        return df

    def get_processed_data(self, force=True):
        self.save(force=force)

        query = None  # query
        if "columns" in self.configuration and (
            "filter_chosen" in json.dumps(self.configuration, default=str)
            or "filterChosen" in json.dumps(self.configuration, default=str)
        ):  # Check if old version or not
            for column in self.configuration["columns"].values():
                filter_chosen = (
                    "filter_chosen"
                    if "filter_chosen" in column.keys()
                    else "filterChosen"
                )
                data_type = "data_type" if "data_type" in column.keys() else "dataType"
                orm_filter = data_types[column[data_type]]["options"][
                    column[filter_chosen]
                ].get("orm_string", "{0}")
                search_function = data_types[column[data_type]]["options"][
                    column[filter_chosen]
                ].get("search_function", False)
                # Changed this
                label = "qc_" + column["label"]
                if search_function:
                    base_unit = column.get('uom')

                    if base_unit:
                        query = search_function(
                            self,
                            query,
                            orm_filter,
                            label,
                            convert_quantity(column, self),
                        )
                    else:
                        query = search_function(
                            self, query, orm_filter, label, column["filters"]
                        )

                # FIXME: this needs some testing to ensure that it works if we switch it to `if not column["filters"]:`
                elif column["filters"] or column["filters"] == False: #noqa E712
                    if isinstance(column["filters"], list):
                        if isinstance(orm_filter, list) and len(
                            column["filters"]
                        ) == len(orm_filter):
                            for i, filtr in enumerate(orm_filter):
                                if column["filters"][i]:
                                    query = process_for_query(
                                        query, filtr.format(label), column["filters"][i]
                                    )
                        elif orm_filter == "{0}__icontains":
                            close_query = None
                            filtr = [c for c in column["filters"] if c]
                            for i in filtr:
                                close_query = process_for_query_OR(
                                    close_query, orm_filter.format(label), i
                                )
                            query = process_for_query_Q(query, close_query)
                        else:
                            filtr = [c for c in column["filters"] if c]
                            if filtr:
                                if isinstance(orm_filter, list):
                                    orm_filtr = orm_filter[0].format(label) + "__in"
                                else:
                                    orm_filtr = orm_filter.format(label) + "__in"
                                    query = process_for_query(query, orm_filtr, filtr)
                    else:
                        if isinstance(orm_filter, list):
                            query = process_for_query(
                                query, orm_filter[0].format(label), column["filters"]
                            )
                        else:
                            query = process_for_query(
                                query, orm_filter.format(label), column["filters"]
                            )
        sort = False
        if len(self.configuration["sort_order"]) > 0:
            sort = [
                ("-" if so["sort"] == "desc" else "") + f'qc_{so["name"]}'
                for so in self.configuration["sort_order"]
            ]
        selected_columns = self.get_selected_columns()
        source_data_columns = self.get_data_columns().keys()
        fields = list(filter(lambda col: col in selected_columns, source_data_columns))
        df = self.get_raw_data(query, sort, fields)

        if (
            "columns" in self.configuration
            and not df.empty
            and "filter_chosen" not in json.dumps(self.configuration, default=str)
            and "filterChosen" not in json.dumps(self.configuration, default=str)
        ):  # Check if old version or not
            for column in self.configuration["columns"].values():
                if column["filters"]:
                    df = pdp.ValKeep(column["filters"], column["id"]).apply(df)

        if "order" in self.configuration and not df.empty:
            df = df[self.configuration["order"]]

        df = self.get_labeled_data(df)

        df = self.get_unit_format(df)

        df = self.get_display_format(df)

        df = self.get_standardized_columns(df)

        df = self.set_timezone(df)

        return df

    def get_display_format(self, df: pd.DataFrame) -> pd.DataFrame:
        return process_display_format(self.configuration, df)

    def get_unit_format(self, df: pd.DataFrame) -> pd.DataFrame:
        return process_uom(self, df)

    def get_standardized_columns(self, df: pd.DataFrame) -> pd.DataFrame:
        return label_columns_with_unit(self.configuration["columns"], df, self)

    def set_timezone(self, df:pd.DataFrame) -> pd.DataFrame:
        cols_datetime = [c for c in df.dtypes.keys() if "datetime64[ns," in str(df.dtypes[c])]
        if self.configuration.get("timezone"):
            tz = zoneinfo.ZoneInfo(self.configuration.get("timezone"))
        else:
            tz = timezone.get_current_timezone()
        for col in cols_datetime:
            df[col] = df[col].dt.tz_convert(tz)
        return df

    @staticmethod
    def get_next_day_date(day_name):
        days_of_week = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday']

        today = arrow.now()
        today_index = today.weekday()  # Monday=0, Sunday=6
        target_index = days_of_week.index(day_name)
        days_until = (target_index - today_index + 7) % 7
        next_day_date = today.shift(days=days_until)
        return next_day_date.strftime('%Y-%m-%d')

    def calc_next_runs(self, now=None):
        if not now:
            now = arrow.now()

        day = now.format("DD")
        month = now.format("MM")
        year = now.format("YYYY")
        schedule_tz = (
            zoneinfo.ZoneInfo(self.configuration.get("timezone"))
            if self.configuration.get("timezone")
            else timezone.get_current_timezone()
        )

        if self.schedule:
            for schedule in self.schedule:
                next_run_timestamp = None
                if schedule["interval"] == "Daily":
                    next_run_timestamp = arrow.get(
                        f'{year}-{month}-{day} {schedule["time"]}', "YYYY-MM-DD HH:mm"
                    ).replace(tzinfo=schedule_tz)
                    while next_run_timestamp <= now:
                        next_run_timestamp = next_run_timestamp.shift(days=+1)

                elif schedule["interval"] == "Weekly":
                    if schedule.get("next_run"):
                        next_run_timestamp = arrow.get(schedule["next_run"]).replace(tzinfo=schedule_tz)
                        while next_run_timestamp <= now:
                            next_run_timestamp = next_run_timestamp.shift(days=+7)
                    else:
                        next_day_date = self.get_next_day_date(schedule["date"])
                        next_run_timestamp = arrow.get(
                            f'{next_day_date} {schedule["time"]}', "YYYY-MM-DD HH:mm"
                        ).replace(tzinfo=schedule_tz)
                        # if schedule created for today's weekday but the time is past
                        if next_run_timestamp <= now:
                            next_run_timestamp.shift(days=+7)

                elif schedule["interval"] == "Monthly":
                    schedule_day = (
                        schedule["date"]
                        if len(str(schedule["date"])) == 2
                        else f'0{schedule["date"]}'
                    )
                    next_run_timestamp = arrow.get(
                        f'{year}-{schedule_day} {schedule["time"]}', "YYYY-DD HH:mm"
                    ).replace(tzinfo=schedule_tz)

                    while next_run_timestamp <= now:
                        next_run_timestamp = next_run_timestamp.shift(months=+1)

                schedule["next_run"] = next_run_timestamp.datetime

    def save(self, force=True, *args, **kwargs):
        # add full_clean to validate source on save
        self.full_clean()

        # Configuration is reset if the source is changed
        if "source" in self.get_dirty_fields():
            self.configuration = None
        self.auto_config(force)
        if "schedule" in self.get_dirty_fields() and self.schedule:
            self.calc_next_runs()
        super().save(*args, **kwargs)

    def snakecase_to_word(self, word):
        return word.upper().replace("_", " ")

    def create_report_byteio(self, df=None):
        result = BytesIO()
        if df is None:
            df = self.get_processed_data()
        columns = {name: self.snakecase_to_word(name) for name in df.columns}
        df.rename(columns=columns, inplace=True)

        for col in [
            c for c in df.dtypes.keys() if "datetime64[ns," in str(df.dtypes[c])
        ]:
            df[col] = df[col].dt.tz_localize(None)

        if self.file_format == Report.CSV:
            df.to_csv(result, index=False)
            return result
        else:
            writer = pd.ExcelWriter(result)
            df.to_excel(writer, sheet_name="sheet1", index=False)

            for column in df:
                if hasattr(df[column].astype(str), 'map'):
                    column_length = max(df[column].astype(str).map(len).max(), len(column)) + 1
                    col_idx = df.columns.get_loc(column)
                    writer.sheets["sheet1"].set_column(col_idx, col_idx, column_length)

            writer.close()
            return result

    def generate(self, target="distribution", user_email=None, trigger="Scheduled"):
        if user_email and user_email not in self.distribution:
            self.distribution.append(user_email)

        recipients = self.distribution if target == "distribution" or not user_email else [user_email]
        df = self.get_processed_data()
        result = self.create_report_byteio(df)
        #breakpoint()
        context = dict(
            report=self,
            row_count=len(df),
            run_date=timezone.now(),
            trigger_type=trigger,
        )
        if len(result.getvalue()) > (10*1024*1024):
            async_task(
                self.send_download,
                f"{self.name} (Wheelhouse Automated Report)",
                result,
                recipients,
                context,
                task_name=f"sending report email: {self.id}: {self.name}"
            )
            return
        mail_sender = CompanyEmailSender(self.company.company)
        email = mail_sender.templated_message(
            to=recipients,
            subject=f"{self.name} (Wheelhouse Automated Report)",
            template_name="automated_report",
            context=context,
            priority=1
        )
        email.attach(f"{self.name}.{self.file_format.lower()}", result.getvalue())

        email_sent = False
        for i in range(3):
            try:
                email.send()
                print(f'Email sent to {", ".join(recipients)}')
                email_sent = True
                break
            except Exception as e:
                print(f"Email sent try ({i+1}) failed: {str(e)}")
                time.sleep(10)

        if not email_sent:
            # enqueue another task to send email
            print(
                f"Could not send the report at this time. Scheduling another task for {self.name}."
            )
            logger.exception(
                f"Could not send the report at this time. Scheduling another task for {self.name}."
            )
            async_task(
                generate_report,
                self.id,
                target,
                user_email,
                group="Generate Report",
                task_name=f"{self.name}",
            )

    def send_download(self, title, result, user_list, context):
        length = None
        if result is None:
            df = self.get_processed_data()
            length = len(df)
            result = self.create_report_byteio(df)
        file_path = save_as_uuid_download(result, "xlsx")
        context = dict(
            **context,
            report=self,
            row_count=length,
            run_date=timezone.now(),
            file_path=file_path,
        )
        mail_sender = CompanyEmailSender(self.company.company)
        email = mail_sender.templated_message(
            subject=title,
            template_name="download_report",
            to=user_list,
            context=context,
            priority=1
        )
        email.send()

    def __str__(self):
        return self.name
