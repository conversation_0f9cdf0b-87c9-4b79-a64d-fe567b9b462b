from .base import RawData
from vantedge.trackandtrace.models.event import CarEvent
from django.db.models import Case, CharField, F, QuerySet, When, IntegerField, Q, OuterRef
from django.db.models import Value as V
from django.db.models.functions import Concat, Now, Round, Extract, Coalesce, ExtractSecond, ExtractMinute, ExtractHour


class CarEventRawData(RawData):
    columns = {
        "car":  { 
            "type": "string",
            "annotation": Concat("car__mark", "car__number"),
            "group": "Equipment"
        },
        "event_timestamp": {
            "type": "dateTime", 
            "annotation": F("event_timestamp"),
            "group": "Timestamps"
        },
        "delivery_timestamp": {
            "type": "dateTime", 
            "annotation": F("delivery_timestamp"),
            "group": "Timestamps"
        },
        "arrival_timestamp": {
            "type": "dateTime", 
            "annotation": F("arrival_timestamp"),
            "group": "Timestamps"
        },
        "schedule_timestamp": {
            "type": "dateTime", 
            "annotation": F("schedule_timestamp"),
            "group": "Timestamps"
        },
        "time_since_last_event": {
            "type": "string", 
            "annotation": Concat(
                 Extract(F("time_since_last_event"), "HOUR"),  # Extract hours
        V("h "),
        Extract(F("time_since_last_event"), "MINUTE"),  # Extract minutes
        V("m "),
        Round(Extract(F("time_since_last_event"), "SECOND")),  # Extract seconds
        V("s"),
        output_field=CharField()),
            "group": "Timestamps"
        },
        "bol_timestamp": {
            "type": "dateTime", 
            "annotation": F("bol_timestamp"),
            "group": "Timestamps"
        },
        "railroad_eta": {
            "type": "dateTime", 
            "annotation": F("railroad_eta"),
            "group": "Timestamps"
        },

        "wb": {
            "type": "string",
            "annotation": F("wb"),
            "group": "Reference Numbers"
        },
        "bol": {
            "type": "string",
            "annotation": F("bol"),
            "group": "Reference Numbers"
        },
        "carrier_number": {
            "type": "string",
            "annotation": F("carrier_number"),
            "group": "Reference Numbers"
        },
        "customer_number": {
            "type": "string",
            "annotation": F("customer_number"),
            "group": "Reference Numbers"
        },
        "shipper_number": {
            "type": "string",
            "annotation": F("shipper_number"),
            "group": "Reference Numbers"
        },

        "consignee_name": {
            "type": "string",
            "annotation": F("consignee_name"),
            "group": "Names"
        },
        "shipper_name": {
            "type": "string",
            "annotation": F("shipper_name"),
            "group": "Names"
        },
        "care_of_name": {
            "type": "string",
            "annotation": F("care_of_name"),
            "group": "Names"
        },
        "freightpayer_name": {
            "type": "string",
            "annotation": F("freightpayer_name"),
            "group": "Names"
        },

        "loaded": {
            "type": "boolean",
            "annotation": F("loaded"),
            "group": "Contents"
        },
        "stcc_id": {
            "type": "string",
            "annotation": F("stcc__stcc"),
            "group": "Contents"
        },
        "weight": {
            "type": "number",
            "annotation": F("weight"),
            "group": "Contents"
        },
        "volume": {
            "type": "number",
            "annotation": F("weight"),
            "group": "Contents"
        },

        "location_city": {
            "type": "string",
            "annotation": F("location__city"),
            "group": "Locations"
        },
        "location_province": {
            "type": "string",
            "annotation": F("location__state"),
            "group": "Locations"
        },
        "origin_city": {
            "type": "string",
            "annotation": F("origin__city"),
            "group": "Locations"
        },
        "origin_province": {
            "type": "string",
            "annotation": F("origin__state"),
            "group": "Locations"
        },
        "destination_city": {
            "type": "string",
            "annotation": F("destination__city"),
            "group": "Locations"
        },
        "destination_province": {
            "type": "string",
            "annotation": F("destination__state"),
            "group": "Locations"
        },

        "train_id": {
            "type": "string",
            "annotation": F("train_id"),
            "group": "Waybill"
        },
        "reporting_railroad": {
            "type": "string",
            "annotation": F("reporting_railroad"),
            "group": "Waybill"
        },
        "origin_railroad": {
            "type": "string",
            "annotation": F("origin_railroad"),
            "group": "Waybill"
        },
        "destination_railroad": {
            "type": "string",
            "annotation": F("destination_railroad"),
            "group": "Waybill"
        },

        "distance_since_last_event": {
            "type": "number",
            "annotation": F("distance_since_last_event"),
            "group": "Event"
        },
        "is_invalid": {
            "type": "boolean",
            "annotation": F("is_invalid"),
            "group": "Event"
        },
        "invalidity_reason": {
            "type": "string",
            "annotation": F("invalidity_reason"),
            "group": "Event"
        },
    }

    required_fields = ["car"]

    def get_base_filter(self) -> QuerySet:
        selected_columns = self.get_selected_columns()
        query = Q(company=self.company.company.track_trace)

        query &= Q(trip__is_active=True)
        return CarEvent.objects.filter(query)