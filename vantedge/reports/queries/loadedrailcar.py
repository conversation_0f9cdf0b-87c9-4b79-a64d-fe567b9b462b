from .base import RawData
from vantedge.wheelhouse.models import LoadedRailCar
from django.db.models import Case, CharField, FloatField, Q, F, When, Value
from django.db.models.functions import Concat, Round, Replace
from vantedge.wheelhouse.templatetags.units import (
    convert,
    quantity,
    convert_custom_factor,
)

# used only in annotation to enable filtering
USG_CONVERSION_FACTOR = 0.264172
KGL_LBGAL_CONVERSION_FACTOR = 8.345404

# override and defer conversion to 'convert, quantity' functions to match what is used in BOL generation


class LoadedRailCarData(RawData):
    columns = {
        "commodity": {
            "type": "number",
            "annotation": F("product__data__codes__stcc"),
            "group": "Product",
        },
        "commodity_description": {
            "type": "string",
            "annotation": F("product__name"),
            "group": "Product",
        },
        "car": {
            "type": "string",
            "annotation": Concat("car_mark", "car_number"),
            "group": "Equipment",
        },
        # "shipment_status": {
        #     "type": "string",
        #     "annotation": F("rail_shipment__status"),
        #     "group": "Shipment",
        # },
        "consignee": {
            "type": "string",
            "annotation": F("rail_shipment__consignee__name"),
            "group": "Parties",
        },
        "shipper": {
            "type": "string",
            "annotation": F("rail_shipment__shipper__name"),
            "group": "Parties",
        },
        "freight_payer": {
            "type": "string",
            "annotation": F("rail_shipment__freight_payer__name"),
            "group": "Parties",
        },
        "custom_broker": {
            "type": "string",
            "annotation": F("rail_shipment__custom_broker__name"),
            "group": "Parties",
        },
        "notify_party": {
            "type": "string",
            "annotation": F("rail_shipment__notify_party__name"),
            "group": "Parties",
        },
        "care_party": {
            "type": "string",
            "annotation": F("rail_shipment__care_party__name"),
            "group": "Parties",
        },
        "pickup_party": {
            "type": "string",
            "annotation": F("rail_shipment__pickup_party__name"),
            "group": "Parties",
        },
        "ultimate_consignee": {
            "type": "string",
            "annotation": F("rail_shipment__ultimate_consignee__name"),
            "group": "Parties",
        },
        "custom_broker_us": {
            "type": "string",
            "annotation": F("rail_shipment__custom_broker_us__name"),
            "group": "Parties",
        },
        "importer": {
            "type": "string",
            "annotation": F("rail_shipment__importer__name"),
            "group": "Parties",
        },
        "exporter": {
            "type": "string",
            "annotation": F("rail_shipment__exporter__name"),
            "group": "Parties",
        },
        "date_arrived": {
            "type": "date",
            "annotation": F("rail_shipment__arrive_date"),
            "group": "Shipment",
        },
        "date_loaded": {
            "type": "date",
            "annotation": F("load_date"),
            "group": "Shipment",
        },
        "date_shipped": {
            "type": "date",
            "annotation": F("rail_shipment__ship_date"),
            "group": "Shipment",
        },
        "shipment_id": {
            "type": "string",
            "annotation": F("rail_shipment__id"),
            "group": "Shipment",
        },
        "bol": {
            "type": "string",
            "annotation": F("rail_shipment__reference_number"),
            "group": "Shipment",
        },
        "seller_party": {
            "type": "string",
            "annotation": Case(
                When(
                    rail_shipment__schedule__sale_contract__isnull=False,
                    then=F("rail_shipment__schedule__sale_contract__seller__name"),
                ),
                When(
                    rail_shipment__schedule__purchase_contract__isnull=False,
                    then=F("rail_shipment__schedule__purchase_contract__seller__name"),
                ),
                output_field=CharField(),
            ),
            "group": "Parties",
        },
        "buyer_party": {
            "type": "string",
            "annotation": Case(
                When(
                    rail_shipment__schedule__sale_contract__isnull=False,
                    then=F("rail_shipment__schedule__sale_contract__buyer__name"),
                ),
                When(
                    rail_shipment__schedule__purchase_contract__isnull=False,
                    then=F("rail_shipment__schedule__purchase_contract__buyer__name"),
                ),
                output_field=CharField(),
            ),
            "group": "Parties",
        },
        "contract_start_date": {
            "type": "date",
            "annotation": Case(
                When(
                    rail_shipment__schedule__sale_contract__isnull=False,
                    then=F("rail_shipment__schedule__sale_contract__start_date"),
                ),
                When(
                    rail_shipment__schedule__purchase_contract__isnull=False,
                    then=F("rail_shipment__schedule__purchase_contract__start_date"),
                ),
                output_field=CharField(),
            ),
            "group": "Contract",
        },
        "contract_end_date": {
            "type": "date",
            "annotation": Case(
                When(
                    rail_shipment__schedule__sale_contract__isnull=False,
                    then=F("rail_shipment__schedule__sale_contract__end_date"),
                ),
                When(
                    rail_shipment__schedule__purchase_contract__isnull=False,
                    then=F("rail_shipment__schedule__purchase_contract__end_date"),
                ),
                output_field=CharField(),
            ),
            "group": "Contract",
        },
        "price_unit": {
            "type": "number",
            "annotation": Case(
                When(
                    rail_shipment__schedule__sale_contract__isnull=False,
                    then=F("rail_shipment__schedule__sale_contract__volume_unit_price"),
                ),
                When(
                    rail_shipment__schedule__purchase_contract__isnull=False,
                    then=F("rail_shipment__schedule__purchase_contract__volume_unit_price"),
                ),
                output_field=CharField(),
            ),
            "group": "Contract",
        },
        "currency": {
            "type": "string",
            "annotation": Case(
                When(
                    rail_shipment__schedule__sale_contract__isnull=False,
                    then=F("rail_shipment__schedule__sale_contract__currency"),
                ),
                When(
                    rail_shipment__schedule__purchase_contract__isnull=False,
                    then=F("rail_shipment__schedule__purchase_contract__currency"),
                ),
                output_field=CharField(),
            ),
            "group": "Contract",
        },
        "price_base": {
            "type": "string",
            "annotation": Case(
                When(
                    rail_shipment__schedule__sale_contract__isnull=False,
                    then=F("rail_shipment__schedule__sale_contract__volume_unit"),
                ),
                When(
                    rail_shipment__schedule__purchase_contract__isnull=False,
                    then=F("rail_shipment__schedule__purchase_contract__volume_unit"),
                ),
                output_field=CharField(),
            ),
            "group": "Contract",
        },
        "discount": {
            "type": "number",
            "annotation": Case(
                When(
                    rail_shipment__schedule__sale_contract__isnull=False,
                    then=F("rail_shipment__schedule__sale_contract__discount"),
                ),
                When(
                    rail_shipment__schedule__purchase_contract__isnull=False,
                    then=F("rail_shipment__schedule__purchase_contract__discount"),
                ),
                output_field=CharField(),
            ),
            "group": "Contract",
        },
        "seller_contract_number": {
            "type": "string",
            "annotation": Case(
                When(
                    rail_shipment__schedule__sale_contract__isnull=False,
                    then=F("rail_shipment__schedule__sale_contract__seller_number"),
                ),
                When(
                    rail_shipment__schedule__purchase_contract__isnull=False,
                    then=F("rail_shipment__schedule__purchase_contract__seller_number"),
                ),
                output_field=CharField(),
            ),
            "group": "Contract",
        },
        "buyer_contract_number": {
            "type": "string",
            "annotation": Case(
                When(
                    rail_shipment__schedule__sale_contract__isnull=False,
                    then=F("rail_shipment__schedule__sale_contract__buyer_number"),
                ),
                When(
                    rail_shipment__schedule__purchase_contract__isnull=False,
                    then=F("rail_shipment__schedule__purchase_contract__buyer_number"),
                ),
                output_field=CharField(),
            ),
            "group": "Contract",
        },
        "shipping_schedule_name": {
            "type": "string",
            "annotation": F("rail_shipment__schedule__name"),
            "group": "Contract",
        },
        "shipment_origin": {
            "type": "string",
            "annotation": Replace(
                Concat(
                    "rail_shipment__origin__data__address__name",
                    Value(" ("),
                    "rail_shipment__origin__data__address__state",
                    Value(")"),
                    output_field=CharField(),
                ), Value('"'), Value("")),
            "group": "Shipment"
        },
        "shipment_origin_city": {
            "type": "string",
            "annotation": F("rail_shipment__origin__data__address__name"),
            "group": "Shipment",
        },
        "shipment_origin_state": {
            "type": "string",
            "annotation": F("rail_shipment__origin__data__address__state"),
            "group": "Shipment",
        },
        "shipment_origin_country": {
            "type": "string",
            "annotation": F("rail_shipment__origin__data__address__country"),
            "group": "Shipment",
        },
        "shipment_destination": {
            "type": "string",
            "annotation": Replace(
                Concat(
                    "rail_shipment__destination__data__address__name",
                    Value(" ("),
                    "rail_shipment__destination__data__address__state",
                    Value(")"),
                    output_field=CharField(),
            ),Value('"'), Value("")),
            "group": "Shipment",
        },
        "shipment_destination_city": {
            "type": "string",
            "annotation": F("rail_shipment__destination__data__address__city"),
            "group": "Shipment",
        },
        "shipment_destination_state": {
            "type": "string",
            "annotation": F("rail_shipment__destination__data__address__state"),
            "group": "Shipment",
        },
        "shipment_destination_country": {
            "type": "string",
            "annotation": F("rail_shipment__destination__data__address__country"),
            "group": "Shipment",
        },
        "rail_rate": {
            "type": "string",
            "annotation": Case(
                When(
                    (Q(waybill__isnull=False) & ~Q(waybill__trip__movement_cost={})),
                    then=F("waybill__trip__movement_cost"),
                ),
                default=None,
                output_field=CharField(),
            ),
            "group": "Status"
        },
        "last_location": {
            "type": "string",
            "annotation": Case(
                When(
                    waybill__isnull=False,
                    then=Concat(
                        F("waybill__trip__last_car_event__location__name"),
                        Value(" ("),
                        F("waybill__trip__last_car_event__location__state"),
                        Value(" )"),
                    ),
                ),
                default=Value("--"),
            ),
            "group": "Trip",
        },
        "event_code_label": {
            "type": "string",
            "annotation": F("waybill__trip__last_car_event__event_code__label"),
            "group": "Trip",
        },
        "event_code": {
            "type": "string",
            "annotation": F("waybill__trip__last_car_event__event_code__code"),
            "group": "Trip",
        },
        "fleet": {
            "type": "string",
            "annotation": F("waybill__trip__car__fleet"),
            "group": "Trip",
        },
        "gross_volume": {
            "type": "number",
            "annotation": Case(
                When((Q(tankcar__shell_capacity__isnull=False) & Q(tankcar__volume_outage__isnull=False)),
                      then=Round(F("tankcar__shell_capacity") - F("tankcar__volume_outage"), precision=0)
                ),
                default=Value(0.0),
            ),
            "uom": "liter",
            "precision": "2",
            "group": "Equipment",
        },
        "net_volume": {
            "type": "number",
            "annotation": Case(
                When(tankcar__volume__isnull=False,
                     then=Round(
                         ((F("tankcar__shell_capacity")-F("tankcar__volume_outage"))*
                          F("tankcar__temperature_correction_factor")/F("tankcar__temperature_correction_factor_at_15c")),
                         precision=0)
                    ),
                default=Value(0.0),
            ),
            "precision": "2",
            "group": "Equipment",
        },
        "net_volume_usg": {
            "type": "number",
            "annotation": Case(
                When(tankcar__volume__isnull=False,
                     then=Round(
                         ((F("tankcar__shell_capacity")-F("tankcar__volume_outage"))*
                          F("tankcar__temperature_correction_factor")/F("tankcar__gal_to_liter_conversion_factor")),
                         precision=0)
                    ),
                default=Value(0.0),
            ),
            "precision": "2",
            "group": "Equipment",
        },
        # <holding these for compatibility with old report configurations>
        "net_volume_(l)": {
            "type": "number",
            "annotation": Case(
                When(tankcar__volume__isnull=False, then=Round(F("tankcar__volume"), precision=0)),
                default=Value(0.0),
            ),
            "obsolete": True,
            "group": "Equipment",
        },
        "net_volume_(usg)": {
            "type": "number",
            "annotation": Case(
                When(
                    Q(tankcar__volume__isnull=False)
                    & ~Q(tankcar__gal_to_liter_conversion_factor=0),
                    then=Round(
                        F("tankcar__volume")
                        / F("tankcar__gal_to_liter_conversion_factor"),
                        precision=1,
                    ),
                ),
                default=Value(0.0),
            ),
            "obsolete": True,
            "group": "Equipment",
        },
        # < --- >
        "outage_volume": {
            "type": "number",
            "annotation": Case(
                When(tankcar__volume__isnull=False, then=Round(F("tankcar__volume_outage"), precision=0)),
                default=Value(0.0),
            ),
            "uom": "liter",
            "precision": "2",
            "group": "Equipment",
        },
        "outage_volume_(l)": {
            "type": "number",
            "annotation": Case(
                When(tankcar__volume__isnull=False, then=Round(F("tankcar__volume_outage"), precision=0)),
                default=Value(0.0),
            ),
            "obsolete": True,
            "group": "Equipment",
        },
        "outage_volume_(usg)": {
            "type": "number",
            "annotation": Case(
                When(
                    Q(tankcar__volume__isnull=False)
                    & ~Q(tankcar__gal_to_liter_conversion_factor=0),
                    then=Round(
                        F("tankcar__volume_outage")
                        / F("tankcar__gal_to_liter_conversion_factor"),
                        precision=1
                    )
                ),
                default=Value(0.0),
            ),
            "obsolete": True,
            "group": "Equipment",
        },
        "load_temperature": {
            "type": "number",
            "annotation": Case(
                When(
                    tankcar__volume__isnull=False, then=Round(F("tankcar__load_temperature"), precision=1)
                ),
                default=Value(0.0),
            ),
            "uom": "degree_Celsius",
            "precision": "1",
            "group": "Equipment",
        },
        "load_temperature_(c)": {
            "type": "number",
            "annotation": Case(
                When(
                    tankcar__volume__isnull=False, then=Round(F("tankcar__load_temperature"), precision=1)
                ),
                default=Value(0.0),
            ),
            "obsolete": True,
            "group": "Equipment",
        },
        "load_temperature_(f)": {
            "type": "number",
            "annotation": Case(
                When(
                    tankcar__volume__isnull=False,
                    then=F("tankcar__load_temperature") * (9 / 5) + 32,
                ),
                default=Value(0.0),
            ),
            "obsolete": True,
            "group": "Equipment",
        },
        "temperature_corr._factor": {
            "type": "number",
            "annotation": Case(
                When(
                    tankcar__volume__isnull=False,
                    then=Round(F("tankcar__temperature_correction_factor"), precision=4),
                ),
                default=Value(0.0),
            ),
            "group": "Equipment",
        },
        "density": {
            "type": "number",
            "annotation": Case(
                When(tankcar__volume__isnull=False, then=Round(F("tankcar__density"), precision=4)),
                default=Value(0.0),
            ),
            "uom": "kg/L",
            "precision": "2",
            "group": "Equipment",
        },
        "density_(kg/l)": {
            "type": "number",
            "annotation": Case(
                When(tankcar__volume__isnull=False, then=Round(F("tankcar__density"), precision=4)),
                default=Value(0.0),
            ),
            "obsolete": True,
            "group": "Equipment",
        },
        "density_(lb/usg)": {
            "type": "number",
            "annotation": Case(
                When(
                    tankcar__volume__isnull=False,
                    then=F("tankcar__density") * KGL_LBGAL_CONVERSION_FACTOR,
                ),
                default=Value(0.0),
            ),
            "obsolete": True,
            "group": "Equipment",
        },
        "shell_capacity": {
            "type": "number",
            "annotation": Case(
                When(tankcar__volume__isnull=False, then=Round(F("tankcar__shell_capacity"), precision=0)),
                default=Value(0.0),
            ),
            "uom": "liter",
            "precision": "2",
            "group": "Equipment",
        },
        "shell_capacity_(l)": {
            "type": "number",
            "annotation": Case(
                When(tankcar__volume__isnull=False, then=Round(F("tankcar__shell_capacity"), precision=0)),
                default=Value(0.0),
            ),
            "obsolete": True,
            "group": "Equipment",
        },
        "shell_capacity_(usg)": {
            "type": "number",
            "annotation": Case(
                When(
                    Q(tankcar__volume__isnull=False)
                    & ~Q(tankcar__gal_to_liter_conversion_factor=0),
                    then=Round(
                        F("tankcar__shell_capacity")
                        / F("tankcar__gal_to_liter_conversion_factor"),
                        precision=1
                    )
                ),
                default=Value(0.0),
            ),
            "obsolete": True,
            "group": "Equipment",
        },
        "car_types": {
            "type": "string",
            "annotation": Case(
                When(tankcar__volume__isnull=False, then=Value("Tank Car")),
                When(hoppercar__weight__isnull=False, then=Value("Hopper Car")),
            ),
            "group": "Equipment",
        },
        "weight": {
            "type": "number",
            "annotation": Case(
                When(hoppercar__weight__isnull=False, then=F("hoppercar__weight")),
                When(
                    tankcar__gal_to_liter_conversion_factor__isnull=False,
                    then=Round(F("tankcar__volume") * F("tankcar__density") * F("tankcar__temperature_correction_factor_at_15c"), precision=0)
                ),
            ),
            "uom": "kg",
            "precision": "2",
            "group": "Equipment",
        },
        "weight_(kg)": {
            "type": "number",
            "annotation": Case(
                When(hoppercar__weight__isnull=False, then=F("hoppercar__weight")),
                When(
                    tankcar__gal_to_liter_conversion_factor__isnull=False,
                    then=Round(F("tankcar__volume") * F("tankcar__density"), precision=0)
                ),
            ),
            "obsolete": True,
            "group": "Equipment",
        },
        "gal_to_liter_conver_factor": {
            "type": "number",
            "annotation": Case(
                When(
                    tankcar__gal_to_liter_conversion_factor__isnull=False,
                    then=F("tankcar__gal_to_liter_conversion_factor"),
                ),
                default=Value(0.0),
            ),
            "group": "Product",
        },
    }

    required_fields = [
        "net_volume_(usg)",
        "net_volume",
        # "outage_volume_(usg)",
        "outage_volume",
        # "shell_capacity_(usg)",
        "shell_capacity",
        # "density_(lb/usg)",
        "density_(kg/l)",
        # "load_temperature_(f)",
        "load_temperature",
        "shipment_origin",
        "shipment_destination",
        "temperature_corr._factor",
        "gal_to_liter_conver_factor",
    ]

    def get_base_filter(self):
        return LoadedRailCar.objects.filter(
            current_location__company=self.company.company.wheelhouse,
            # filter on shipped cars
            rail_shipment__status__in=[
                "bol-accepted",
                "bol-accepted-with-error",
                "closed",
                "correction-accepted",
                "correction-accepted-with-error"
            ],
        )

    # def extra_data_manipulation(self):
    #     super().extra_data_manipulation()

    #     self.df["density_(lb/usg)"] = (
    #         self.df["density_(kg/l)"]
    #         .apply(lambda x: convert(x, "kg/L->lbs/gal") if x else 0)
    #         .astype(float)
    #     )

    #     self.df["load_temperature_(f)"] = (
    #         self.df["load_temperature_(c)"]
    #         .apply(lambda x: convert(x, "degC->degF") if x else 0)
    #         .astype(float)
    #     )

    #     # truncate columns by significant digits
    #     column_sigdigs = (
    #         ("density_(lb/usg)", 2),
    #         ("load_temperature_(f)", 0),
    #     )
    #     for column, sigdig in column_sigdigs:
    #         if column in self.df and not self.df[column].empty:
    #             self.df[column] = (
    #                 self.df[column].fillna(0).apply(lambda x: quantity(x, sigdig))
    #             )
