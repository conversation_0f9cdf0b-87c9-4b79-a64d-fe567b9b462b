from django.db.models import F, <PERSON>, When, Q, Value, <PERSON><PERSON><PERSON><PERSON>
from .base import RawData
from numpy import nan

from ...tboss.models import Ticket


class AltagasRawData(RawData):
    columns = {
        'ticket_number': {"type": "string", "annotation": F("data__report__code"), "group": "Reference"},
        'Truck_BOL_number': {"type": "string", "annotation": F("data__report__carrier_ref"), "group": "Reference"},
        'load_type': {"type": "string", "annotation": F("data__report__load_type"), "group": "Reference"},
        'erap': {"type": "string", "annotation": F("data__report__erap"), "group": "Reference"},
        'canada_lpgerc': {"type": "string", "annotation": F("data__report__erac"), "group": "Reference"},
        'start_time': {"type": "dateTime", "annotation": F("start_time"), "group": "Times"},
        'stop_time': {"type": "dateTime", "annotation": F("end_time"), "group": "Times"},
        'LSD/NTS_location': {"type": "string", "annotation": F("data__report__location"), "group": "Entities"},
        'point_of_origin': {"type": "string", "annotation": F("data__report__origin"), "group": "Entities"},
        'destination': {"type": "string", "annotation": F("data__report__destination"), "group": "Entities"},
        'facility': {"type": "string", "annotation": F("data__report__facility"), "group": "Entities"},
        'station_description': {"type": "string", "annotation": F("data__report__riser"), "group": "Entities"},
        'company': {"type": "string", "annotation": F("data__report__company"), "group": "Entities"},
        'product_type': {"type": "string", "annotation": F("data__report__product"), "group": "Entities"},
        'driver': {"type": "string", "annotation": F("data__report__driver"), "group": "Entities"},
        'standard_density': {"type": "number", "annotation": F("data__report__standard_density"), "group": "Quantities"},
        'pressure': {
            "type": "number",
            "annotation": F("data__report__pressure"),
            "uom": "psi",
            "precision": "3",
            "group": "Quantities"
        },
        'temperature': {
            "type": "number",
            "annotation": F("data__report__temperature"),
            "uom": "degree_Celsius",
            "precision": "1",
            "group": "Quantities"
        },
        'gross_volume': {
            "type": "number",
            "annotation": F("data__report__gross_volume"),
            "uom": "cubic_m",
            "precision": "4",
            "group": "Quantities"
        },
        'net_volume': {
            "type": "number",
            "annotation": F("data__report__net_volume"),
            "group": "Quantities",
            "uom": "cubic_m",
            "precision": "4"
        },
        'mass': {
            "type": "number",
            "annotation": F("data__report__mass"),
            "uom": "kg",
            "precision": "1",
            "group": "Quantities"
        },
        'ctl': {"type": "number", "annotation": F("data__report__ctl"), "group": "Quantities"},
        'cpl': {"type": "number", "annotation": F("data__report__cpl"), "group": "Quantities"},
        'ctpl': {"type": "number", "annotation": F("data__report__ctpl"), "group": "Quantities"},
        'corrected_density': {"type": "number", "annotation": F("data__report__corrected_density"), "group": "Quantities"},
        'water_cut': {
            "type": "number",
            "annotation": F("data__report__water_cut"),
            "group": "Quantities"
        }, #precentge
        'water_volume': {
            "type": "number",
            "annotation": F("data__report__water_quantity"),
            "group": "Quantities",
            "uom": "cubic_m",
            "precision": "4"
        },
        'max_water_cut': {"type": "number", "annotation": F("data__report__max_water_cut"), "group": "Quantities"}, 
        'max_water_cut_volume': {"type": "number", "annotation": F("data__report__max_water_cut_volume"), "group": "Quantities"}, 
        'ppm': {"type": "number", "annotation": F("data__report__ppm"), "group": "Quantities"},
        'meter_factor': {"type": "number", "annotation": F("data__report__meter_factor"), "group": "Device"},
        'flow_rate': {"type": "number", "annotation": F("data__report__flow_rate"), "group": "Device"}, # m3/h
        'preset_volume': {
            "type": "number",
            "annotation": F("data__report__preset_volume"),
            "uom": "cubic_m",
            "precision": "1",
            "group": "Device"
        },
        'indicated_volume': {
            "type": "number",
            "annotation": F("data__report__indicated_volume"),
            "uom": "cubic_m",
            "precision": "1",
            "group": "Device"},
        'ticket_status': {
            "type": "string",
            "annotation": Case(
                When(
                    Q(status="po"),
                    then=Value("Posted")
                ),
                When(
                    Q(status="dr"),
                    then=Value("Draft")
                ),
                When(
                    Q(status="vo"),
                    then=Value("Void")
                ),
                output_field=CharField(),
            ),
            "group": "Reference"
        },
        "ticket_type": {
            "type": "string",
            "annotation": F("data__report__ticket_type"),
            "group": "Reference"
        },
        "created_by": {
            "type": "string",
            "annotation": F("data__report__created_by"),
            "group": "Reference"
        },
        "quantity": {
            "type": "number",
            "uom": "liter",
            "annotation": F("data__report__quantity"),
            "group": "Quantities"
        },
        "user": {
            "type": "string",
            "annotation": F("data__report__user"),
            "group": "Reference"
        },
        "consignor": {
            "type": "string",
            "annotation": F("data__report__consignor"),
            "group": "Entities"
        },
        "shipper": {
            "type": "string",
            "annotation": F("data__report__shipper"),
            "group": "Entities"
        },
        "account": {
            "type": "string",
            "annotation": F("data__report__account"),
            "group": "Reference"
        },
        "consignee": {
            "type": "string",
            "annotation": F("data__report__consignee"),
            "group": "Entities"
        },
        "carrier": {
            "type": "string",
            "annotation": F("data__report__carrier"),
            "group": "Entities"
        },
        "odorant": {
            "type": "number",
            "annotation": F("data__report__odorant"),
            "group": "Quantities"
        },
        "limit": {
            "type": "string",
            "annotation": F("data__report__limit"),
            "group": "Reference"
        },
        "location_name": {
            "type": "string",
            "annotation": F("data__report__location_name"),
            "group": "Entities"
        }
    }

    def get_base_filter(self):
        return Ticket.objects.filter(created_by_server__dbreplication_client_company_id="b27fa557-4dc3-42cf-9a0d-cc04f73e8e1b")

    def __init__(self, *args, **kwargs):
        """
        Additional data fields columns
        """
        self.limit_rows = 30000
        super().__init__(*args, **kwargs)

    def extra_data_manipulation(self):
        super().extra_data_manipulation()
        self.df = self.df.replace(nan, None)
