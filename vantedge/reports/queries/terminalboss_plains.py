from django.db.models import F
from .base import RawData
import pandas as pd
from numpy import nan

from ...tboss.models import Ticket
from vantedge.customs.models import CustomsInvoice
from vantedge.tboss.models import CrossBorderContract

class PlainsRawData(RawData):
    columns = {
        'Average_Product_Temperature': {"type": "number", "annotation": F("data__quantity_data__observed_temperature"), "uom": "degree_Celsius", "precision": "1", "group": "Quantities"},
        'Carrier_BOL': {"type": "string", "annotation": F("data__additional_fields__logistics__carrier_bol"), "group": "Reference"},
        'BSW': {"type": "number", "annotation": F("data__quantity_data__volume__water_percent"), "group": "Quantities"},
        'Carrier_Code': {"type": "string", "annotation": F("data__additional_fields__logistics__carrier_code"), "group": "Reference"},
        'Carrier': {"type": "string", "annotation": F("data__additional_fields__logistics__carrier_name"), "group": "Reference"},
        'Created_By': {"type": "string", "annotation": F("created_by__name"), "group": "Reference"},
        'Customer_Code': {"type": "string", "annotation": F("data__print_data__customer_code"), "group": "Reference"},
        'Customer_Name': {"type": "string", "annotation": F("data__print_data__customer_name"), "group": "Reference"},
        'Customer': {"type": "string", "annotation": F("data__print_data__customer_name"), "group": "Reference"},
        'Producer_Code': {"type": "string", "annotation": F("data__print_data__producer_code"), "group": "Reference"},
        'Producer_Name': {"type": "string", "annotation": F("data__print_data__producer_name"), "group": "Reference"},
        'Producer': {"type": "string", "annotation": F("data__print_data__producer_name"), "group": "Reference"},
        'Creation_Date': {"type": "dateTime", "annotation": F("created"), "group": "Timestamps"},
        'Last_Modified': {"type": "dateTime", "annotation": F("modified"), "group": "Timestamps"},
        'Device': {"type": "string", "annotation": F("data__print_data__device_name"), "group": "Reference"},
        'Driver_Number': {"type": "string", "annotation": F("data__additional_fields__logistics__driver_number"), "group": "Reference"},
        'Driver': {"type": "string", "annotation": F("data__additional_fields__logistics__driver_name"), "group": "Reference"},
        'Emulsion_Gross_Volume_GV': {"type": "number", "annotation": F("data__quantity_data__volume__transferred_gross_volume"), "uom": "cubic_m", "precision": "3", "group": "Quantities"},
        'Emulsion_Standard_Volume_SEV': {"type": "number", "annotation": F("data__quantity_data__volume__gross_standard_volume"), "uom": "cubic_m", "precision": "3", "group": "Quantities"},
        'Entrained_Air_Alarm': {"type": "number", "annotation": F("data__quantity_data__volume__additional__entrained_air_alarm"), "uom": "cubic_m", "precision": "3", "group": "Quantities"},
        'Export_Status': {"type": "string", "annotation": F("data__misc__exported"), "group": "Reference"},
        'Gross_Volume': {"type": "number", "annotation": F("data__quantity_data__volume__gross_standard_volume"), "uom": "cubic_m", "precision": "3", "group": "Quantities"},
        'Inventory_Adjustment': {"type": "number", "annotation": F("data__quantity_data__volume__additional__inventory_adjustment"), "uom": "cubic_m", "precision": "3", "group": "Quantities"},
        'Location': {"type": "string", "annotation": F("location__name"), "group": "Reference"},
        'Location_Code': {"type": "string", "annotation": F("location__code"), "group": "Reference"},
        'Gross_Weight': {"type": "number", "annotation": F("data__quantity_data__weight__gross_weight"), "uom": "kg", "precision": "1", "group": "Quantities"},
        'Indicated_Volume_IV': {"type": "number", "annotation": F("data__quantity_data__volume__indicated_volume"), "uom": "cubic_m", "precision": "3", "group": "Quantities"},
        'Location_Address': {"type": "string", "annotation": F("location__data__address"), "group": "Reference"},
        'Location_Product_Density': {"type": "number", "annotation": F("data__quantity_data__volume__oil_density"), "uom": "kg/m3", "precision": "3", "group": "Quantities"},
        'Meter_Factor': {"type": "number", "annotation": F("data__quantity_data__volume__meter_factor"), "uom": "cubic_m", "precision": "3", "group": "Quantities"},
        'Meter_ID': {"type": "string", "annotation": F("data__misc__meter_id"), "group": "Reference"},
        'Minimum_Density': {"type": "number", "annotation": F("data__quantity_data__volume__additional__minimum_density"), "uom": "kg/m3", "precision": "3", "group": "Quantities"},
        'Modified_By': {"type": "string", "annotation": F("modified_by__name"), "group": "Reference"},
        'Net_Volume': {"type": "number", "annotation": F("data__quantity_data__volume__transferred_net_volume"), "uom": "cubic_m", "precision": "3", "group": "Quantities"},
        'Net_Volume_By_Transfer_Direction': {"type": "number", "annotation": F("data__quantity_data__volume__additional__net_volume_by_transfer_direction"), "uom": "cubic_m", "precision": "3", "group": "Quantities"},
        'Net_Weight': {"type": "number", "annotation": F("data__quantity_data__weight__net_weight"), "uom": "kg", "precision": "3", "group": "Quantities"},
        'Notes': {"type": "string", "annotation": F("data__notes"), "group": "Reference"},
        'Product': {"type": "string", "annotation": F("product__name"), "group": "Reference"},
        'Product_Code': {"type": "string", "annotation": F("product__code"), "group": "Reference"},
        'Road_Ban': {"type": "string", "annotation": F("data__additional_fields__logistics__roadban_percentage"), "group": "Reference"},
        'Route_Code': {"type": "string", "annotation": F("data__additional_fields__logistics__route_name"), "group": "Reference"},
        'Route_Key': {"type": "string", "annotation": F("data__additional_fields__logistics__route_code"), "group": "Reference"},
        'Route': {"type": "string", "annotation": F("data__additional_fields__logistics__route_name"), "group": "Reference"},
        'Scale_In_Time': {"type": "dateTime", "annotation": F("data__quantity_data__in_time"), "group": "Reference"},
        'Scale_Out_Time': {"type": "dateTime", "annotation": F("data__quantity_data__out_time"), "group": "Reference"},
        'Site': {"type": "string", "annotation": F("created_by_server__name"), "group": "Reference",},
        'Standard_Product_Density': {"type": "number", "annotation": F("data__quantity_data__volume__std_density"), "uom": "kg/m3", "precision": "3", "group": "Quantities"},
        'Standard_Water_Cut': {"type": "number", "annotation": F("data__quantity_data__volume__misc__standard_water_cut"), "group": "Quantities"},
        'State': {"type": "string", "annotation": F("data__additional_fields__legacy_data__state"), "group": "Reference"},
        'Status': {"type": "string", "annotation": F("data__additional_fields__legacy_data__legacy_status"), "group": "Reference"},
        'Corrected_Density_15_C': {"type": "number", "annotation": F("data__quantity_data__volume__std_density"), "uom": "kg/m3", "precision": "3", "group": "Quantities"},
        'Stockholder_Code': {"type": "string", "annotation": F("data__additional_fields__stockholder_code"), "group": "Reference"},
        'Stockholder': {"type": "string", "annotation": F("data__print_data__stockholder_name"), "group": "Reference"},
        'Tare_Weight': {"type": "number", "annotation": F("data__quantity_data__weight__tare_weight"), "uom": "kg", "precision": "3", "group": "Quantities"},
        'Terminal_ID': {"type": "string", "annotation": F("data__print_data__terminal_id"), "group": "Reference"},
        'Transfer_Direction': {"type": "string", "annotation": F("ticket_type"), "group": "Reference"},
        'Ticket_Type': {"type": "string", "annotation": F("ticket_type"), "group": "Reference"},
        'Ticket_Close_Time': {"type": "dateTime", "annotation": F("end_time"), "group": "Timestamps"},
        'Ticket_Number': {"type": "string", "annotation": F("code"), "group": "Reference"},
        'ticket_open_time': {"type": "dateTime", "annotation": F("start_time"), "group": "Timestamps"},
        'Accumulated_Mass': {"type": "number", "annotation": F("data__quantity_data__volume__accumulated_mass"), "uom": "kg", "precision": "3", "group": "Quantities"},
        'Axles': {"type": "string", "annotation": F("data__print_data__axles"), "group": "Reference"},
        'Tractor_Code': {"type": "string", "annotation": F("data__additional_fields__logistics__tractor_code"), "group": "Reference"},
        'Tractor': {"type": "string", "annotation": F("data__additional_fields__logistics__tractor_name"), "group": "Reference"},
        'Vehicle': {"type": "string", "annotation": F("data__additional_fields__logistics__trailer_1"), "group": "Reference"},
        'CPL': {"type": "number", "annotation": F("data__quantity_data__volume__cPL"), "precision": "3", "group": "Quantities"},
        'CTL': {"type": "number", "annotation": F("data__quantity_data__volume__cTL"), "precision": "3", "group": "Quantities"},
        'Legacy_Type': {"type": "string", "annotation": F("data__additional_fields__legacy_data__ticket_type"), "group": "Reference"},
        'Auto/Manual': {"type": "boolean", "annotation": F("is_manual"), "group": "Reference"},
        'Valid_Measured_Oil_Density': {"type": "number", "annotation": F("data__quantity_data__volume_data__misc__valid_measured_oil_density"), "uom": "kg/m3", "precision": "3", "group": "Quantities"},
        'Valid_Measured_Water_Density': {"type": "number", "annotation": F("data__quantity_data__volume_data__misc__valid_measured_water_density"), "uom": "kg/m3", "precision": "3", "group": "Quantities"},
        'Water_Standard_Volume_SVW': {"type": "number", "annotation": F("data__quantity_data__volume_data__misc__water_standard_volumeSVW"), "uom": "cubic_m", "precision": "3", "group": "Quantities"},
        'Water_Gross_Volume_GVW': {"type": "number", "annotation": F("data__quantity_data__volume__water_volume"), "uom": "cubic_m", "precision": "3", "group": "Quantities"},
        'Destination_Location_LSD': {"type": "string", "annotation": F("data__additional_fields__receiving_location_name"), "group": "Reference"},
        'terminal_name': {"type": "string", "annotation": F("data__print_data__terminal_name"), "group": "Reference"},
        'receiving_location_name': {"type": "string", "annotation": F("data__additional_fields__receiving_location_name"), "group": "Reference"},
        'receiving_location_code': {"type": "string", "annotation": F("data__additional_fields__receiving_location_code"), "group": "Reference"},
        'source_location_code': {"type": "string", "annotation": F("data__additional_fields__source_location_code"), "group": "Reference"},
        'source_location_name': {"type": "string", "annotation": F("data__additional_fields__source_location_name"), "group": "Reference"},
        'invoice_status': {"type": "string", "annotation": F("data__customs_data__-1__status"), "group": "Custom Data"},
        'invoice_status_code': {"type": "string", "annotation": F("data__customs_data__-1__status"), "group": "Custom Data"},
        'invoice_date': {"type": "dateTime", "annotation": F("data__customs_data__-1__print_invoice__date"), "group": "Custom Data"},
        'contract_name': {"type": "string", "annotation": F("data__customs_data__-1__print_invoice__contract_name"), "group": "Custom Data"},
        'contract_number': {"type": "string", "annotation": F("data__customs_data__-1__print_invoice__contract_number"), "group": "Custom Data"},
        'exporter_name': {"type": "string", "annotation": F("data__customs_data__-1__print_invoice__exporter_name"), "group": "Custom Data"},
        'exporter_mid': {"type": "string", "annotation": F("data__customs_data__-1__print_invoice__exporter_mid"), "group": "Custom Data"},
        'exporter_address': {"type": "string", "annotation": F("data__customs_data__-1__print_invoice__exporter_address__street"), "group": "Custom Data"},
        'exporter_city': {"type": "string", "annotation": F("data__customs_data__-1__print_invoice__exporter_address__city"), "group": "Custom Data"},
        'exporter_province': {"type": "string", "annotation": F("data__customs_data__-1__print_invoice__exporter_address__province"), "group": "Custom Data"},
        'exporter_country': {"type": "string", "annotation": F("data__customs_data__-1__print_invoice__exporter_address__country"), "group": "Custom Data"},
        'shipper_name': {"type": "string", "annotation": F("data__customs_data__-1__print_invoice__supplier_name"), "group": "Custom Data"},
        'shipper_mid': {"type": "string", "annotation": F("data__customs_data__-1__print_invoice__supplier_mid"), "group": "Custom Data"},
        'shipper_address': {"type": "string", "annotation": F("data__customs_data__-1__print_invoice__supplier_address__street"), "group": "Custom Data"},
        'shipper_city': {"type": "string", "annotation": F("data__customs_data__-1__print_invoice__supplier_address__city"), "group": "Custom Data"},
        'shipper_province': {"type": "string", "annotation": F("data__customs_data__-1__print_invoice__supplier_address__province"), "group": "Custom Data"},
        'shipper_country': {"type": "string", "annotation": F("data__customs_data__-1__print_invoice__supplier_address__country"), "group": "Custom Data"},
        'consignee_name': {"type": "string", "annotation": F("data__customs_data__-1__print_invoice__consignee_name"), "group": "Custom Data"},
        'consignee_mid': {"type": "string", "annotation": F("data__customs_data__-1__print_invoice__consignee_mid"), "group": "Custom Data"},
        'consignee_irs': {"type": "string", "annotation": F("data__customs_data__-1__print_invoice__consignee_IRS_number"), "group": "Custom Data"},
        'consignee_address': {"type": "string", "annotation": F("data__customs_data__-1__print_invoice__consignee_address__street"), "group": "Custom Data"},
        'consignee_city': {"type": "string", "annotation": F("data__customs_data__-1__print_invoice__consignee_address__city"), "group": "Custom Data"},
        'consignee_province': {"type": "string", "annotation": F("data__customs_data__-1__print_invoice__consignee_address__province"), "group": "Custom Data"},
        'consignee_country': {"type": "string", "annotation": F("data__customs_data__-1__print_invoice__consignee_address__country"), "group": "Custom Data"},
        'buyer_name': {"type": "string", "annotation": F("data__customs_data__-1__print_invoice__buyer_name"), "group": "Custom Data"},
        'buyer_mid': {"type": "string", "annotation": F("data__customs_data__-1__print_invoice__buyer_mid"), "group": "Custom Data"},
        'buyer_irs': {"type": "string", "annotation": F("data__customs_data__-1__print_invoice__buyer_IRS_number"), "group": "Custom Data"},
        'buyer_address': {"type": "string", "annotation": F("data__customs_data__-1__print_invoice__buyer_address__street"), "group": "Custom Data"},
        'buyer_city': {"type": "string", "annotation": F("data__customs_data__-1__print_invoice__buyer_address__city"), "group": "Custom Data"},
        'buyer_province': {"type": "string", "annotation": F("data__customs_data__-1__print_invoice__buyer_address__province"), "group": "Custom Data"},
        'buyer_country': {"type": "string", "annotation": F("data__customs_data__-1__print_invoice__buyer_address__country"), "group": "Custom Data"},
        'country_of_origin': {"type": "string", "annotation": F("data__customs_data__-1__print_invoice__country_of_origin"), "group": "Custom Data"},
        'port_of_entry': {"type": "string", "annotation": F("data__customs_data__-1__print_invoice__port_of_entry"), "group": "Custom Data"},
        'delivery_terms': {"type": "string", "annotation": F("data__customs_data__-1__edi_data__cross_border_contract_id"), "group": "Custom Data"},
        'terms_of_sale': {"type": "string", "annotation": F("data__customs_data__-1__edi_data__cross_border_contract_id"), "group": "Custom Data"},
        'contract_effective_start_date': {"type": "string", "annotation": F("data__customs_data__-1__edi_data__cross_border_contract_id"), "group": "Custom Data"},
        'contract_effective_end_date': {"type": "string", "annotation": F("data__customs_data__-1__edi_data__cross_border_contract_id"), "group": "Custom Data"},
        'invoice_price_includes': {"type": "string", "annotation": F("data__customs_data__-1__print_invoice__invoice_price_includes"), "group": "Custom Data"},
        'estimated_freight_charge_to_border': {"type": "string", "annotation": F("data__customs_data__-1__print_invoice__estimated_freight_to_port"), "group": "Custom Data"},
        'buyer_related': {"type": "string", "annotation": F("data__customs_data__-1__print_invoice__parties_related"), "group": "Custom Data"},
        'product_commercial_description': {"type": "string", "annotation": F("data__customs_data__-1__print_invoice__product_commercial_description"), "group": "Custom Data"},
        'net_weight': {"type": "string", "annotation": F("data__customs_data__-1__print_invoice__net_weight"), "group": "Custom Data"},
        'net_weight_unit': {"type": "string", "annotation": F("data__customs_data__-1__print_invoice__net_weight_unit"), "group": "Custom Data"},
        'volume_bbl': {"type": "string", "annotation": F("data__customs_data__-1__print_invoice__quantity_1"), "group": "Custom Data"},
        'volume_m3': {"type": "string", "annotation": F("data__customs_data__-1__print_invoice__quantity_2"), "group": "Custom Data"},
        'unit_price_bbl': {"type": "string", "annotation": F("data__customs_data__-1__print_invoice__unit_price"), "group": "Custom Data"},
        'unit_price_m3': {"type": "string", "annotation": F("data__customs_data__-1__print_invoice__unit_price_2"), "group": "Custom Data"},
        'shipper_id_number': {"type": "string", "annotation": F("data__customs_data__-1__print_invoice__paps"), "group": "Custom Data"},
        'standard_carrier_alpha_code': {"type": "string", "annotation": F("data__customs_data__-1__print_invoice__scac"), "group": "Custom Data"},
        'response_emaployee_email_address': {"type": "string", "annotation": F("data__customs_data__-1__print_invoice__exporter_email"), "group": "Custom Data"},
        'tsca_statement': {"type": "string", "annotation": F("data__customs_data__-1__print_invoice__TSCA_statement"), "group": "Custom Data"},
        'end_use': {"type": "string", "annotation": F("data__customs_data__-1__print_invoice__end_use"), "group": "Custom Data"}
    }

    def get_base_filter(self):
        return Ticket.objects.filter(dbreplication_client_company__name="Plains")

    def round_column(self, val, digits):
        if val:
            return round(val, digits)
        return val

    def extra_data_manipulation(self):
        super().extra_data_manipulation()

        column_sigdigs = (
            ("net_weight", 2),
            ("volume_bbl", 2),
            ("volume_m3", 3),
            ("unit_price_bbl", 3),
            ("unit_price_m3", 3),
        )

        for column, sigdig in column_sigdigs:
            if column in self.df and not self.df[column].empty:
                self.df[column] = pd.to_numeric(self.df[column], errors='coerce').apply(
                    lambda val: self.round_column(val, sigdig)
                )

        port_of_enteries = dict(CustomsInvoice.US_CUSTOM_CLEARANCE_OFFICE_CHOICES)
        invoice_price_includes = dict(CustomsInvoice.INVOICE_PRICE_INCLUDE_CHOICES)
        country_of_origin = dict(CustomsInvoice.CANADA_PROVINCES_CHOICE)
        terms_of_transport = dict(CustomsInvoice.TERMS_OF_TRANSPORTATION)
        terms_of_sale = dict(CustomsInvoice.TERM_OF_SALES_CHOICES)

        contract_dates = {
            str(item["id"]): (item["effective_start_date"], item["effective_end_date"]) for item in
            CrossBorderContract.objects.values("id", "effective_start_date", "effective_end_date")
        }

        delivery_terms = {
            str(item["id"]): item["delivery_terms"] for item in
            CrossBorderContract.objects.values("id", "delivery_terms")
        }

        terms = {
            str(item["id"]): item["terms"] for item in
            CrossBorderContract.objects.values("id", "terms")
        }

        self.df = self.df.replace(nan, None)

        if "port_of_entry" in self.df:
            self.df["port_of_entry"] = self.df["port_of_entry"].apply(
                lambda val: port_of_enteries.get(val, val)
            )

        if "delivery_terms" in self.df:
            self.df["delivery_terms"] = self.df["delivery_terms"].apply(
                lambda val: terms_of_transport.get(delivery_terms.get(val, val))
            )

        if "terms_of_sale" in self.df:
            self.df["terms_of_sale"] = self.df["terms_of_sale"].apply(
                lambda val: terms_of_sale.get(terms.get(val, val))
            )

        if "invoice_price_includes" in self.df:
            self.df["invoice_price_includes"] = self.df["invoice_price_includes"].apply(
                lambda val: invoice_price_includes.get(val, val)
            )

        if "country_of_origin" in self.df:
            self.df["country_of_origin"] = self.df["country_of_origin"].apply(
                lambda val: country_of_origin.get(val, val)
            )

        if "invoice_status" in self.df:
            self.df["invoice_status"] = self.df["invoice_status"].apply(
                lambda val: CustomsInvoice.STATUS_TRANSLATION.get(val, val)
            )

        if "contract_effective_start_date" in self.df:
            self.df["contract_effective_start_date"] = self.df["contract_effective_start_date"].apply(
                lambda val: contract_dates.get(val, ("", ""))[0]
            )

        if "contract_effective_end_date" in self.df:
            self.df["contract_effective_end_date"] = self.df["contract_effective_end_date"].apply(
                lambda val: contract_dates.get(val, ("", ""))[1]
            )


    def __init__(self, *args, **kwargs):
        """
        Additional data fields columns
        """
        self.limit_rows = 30000
        super().__init__(*args, **kwargs)