from .base import RawData
from django.db.models import Q, <PERSON>, <PERSON><PERSON>, <PERSON>, Value, When, <PERSON>r<PERSON><PERSON>
from django.db.models.functions import Concat
from django.core.cache import caches

from vantedge.terminalboss.models import TicketView
import pandas as pd
from zoneinfo import ZoneInfo
from dateutil.parser import isoparse
from datetime import datetime

from vantedge.terminalboss.models import ProductTable


class TerminalBossTicketData(RawData):
    report = None

    columns = {
        "site_ticket_identifier": {
            "type": "string",
            "annotation": Concat(
                F("site_user__username"),
                Value("-"),
                F("ticket_id"),
                output_field=CharField(),
            ),
            "group": "Information",
        },
        "ticket_key": {
            "type": "number",
            "annotation": F("ticket_key"),
            "group": "Information",
        },
        "ticket_id": {
            "type": "number",
            "annotation": F("ticket_id"),
            "group": "Information",
        },
        "original_key_x_ref": {
            "type": "number",
            "annotation": F("original_key_x_ref"),
            "group": "Information",
        },
        "ticket_type": {
            "type": "number",
            "annotation": F("ticket_type"),
            "group": "Information",
        },
        "ticket_status": {
            "type": "string",
            "annotation": F("ticket_status"),
            "choices": dict(TicketView.TICKET_STATUS),
            "group": "Information",
        },
        "origin_site": {
            "type": "string",
            "annotation": F("site_user__username"),
            "group": "Information",
        },
        "driver_id": {
            "type": "string",
            "annotation": F("driver_id"),
            "group": "Parties",
        },
        "driver_name": {
            "type": "string",
            "annotation": F("driver_name"),
            "group": "Parties",
        },
        "terminal_id": {
            "type": "string",
            "annotation": F("terminal_id"),
            "group": "Transaction",
        },
        "ticket_open_time": {
            "type": "dateTime",
            "annotation": F("ticket_open_time"),
            "group": "Timestamps",
        },
        "ticket_close_time": {
            "type": "dateTime",
            "annotation": F("ticket_close_time"),
            "group": "Timestamps",
        },
        "production_month": {
            "type": "dateTime",
            "annotation": F("ticket_open_time"),
            "group": "Timestamps",
        },
        "transaction_type_name": {
            "type": "string",
            "annotation": F("transaction_type_name"),
            "group": "Transaction",
        },
        "bol": {"type": "string", "annotation": F("bol"), "group": "Information"},
        "purchase_order": {
            "type": "string",
            "annotation": F("additional_data__po"),
            "group": "Information",
        },
        "product_code": {
            "type": "string",
            "annotation": F("product_code"),
            "group": "Information",
        },
        "product_name": {
            "type": "string",
            "annotation": F("product_name"),
            "group": "Information",
        },
        "measured_density": {
            "type": "number",
            "annotation": F("measured_density"),
            "uom": "kg/L",
            "precision": "2",
            "group": "Metrics",
        },
        "measured_temperature": {
            "type": "number",
            "annotation": F("measured_temperature"),
            "uom": "degree_Celsius",
            "precision": "1",
            "group": "Metrics",
        },
        "transferred_gross_volume": {
            "type": "number",
            "annotation": F("transferred_gross_volume"),
            "uom": "cubic_m",
            "precision": "2",
            "group": "Volumes",
        },
        "transferred_net_volume": {
            "type": "number",
            "annotation": F("transferred_net_volume"),
            "precision": "2",
            "group": "Volumes",
        },
        "net_transferred_volume_usg": {
            "type": "number",
            "annotation": F("transferred_net_volume"),
            "group": "Volumes",
        },
        "preset_volume": {
            "type": "number",
            "annotation": F("preset_volume"),
            "uom": "cubic_m",
            "precision": "2",
            "group": "Volumes",
        },
        "additive_volume": {
            "type": "number",
            "annotation": F("additive_volume"),
            "uom": "cubic_m",
            "precision": "2",
            "group": "Volumes",
        },
        "average_pressure": {
            "type": "number",
            "annotation": F("average_pressure"),
            "uom": "pascal",
            "precision": "2",
            "group": "Metrics",
        },
        "device_name": {
            "type": "string",
            "annotation": F("device_name"),
            "group": "Transaction",
        },
        "carrier_name": {
            "type": "string",
            "annotation": F("carrier_name"),
            "group": "Parties",
        },
        "stockholder_name": {
            "type": "string",
            "annotation": F("stockholder_name"),
            "group": "Parties",
        },
        "customer_name": {
            "type": "string",
            "annotation": F("customer_name"),
            "group": "Parties",
        },
        "account_name": {
            "type": "string",
            "annotation": F("account_name"),
            "group": "Parties",
        },
        "producer_name": {
            "type": "string",
            "annotation": F("producer_name"),
            "group": "Parties",
        },
        "plant_name": {
            "type": "string",
            "annotation": F("plant_name"),
            "group": "Transaction",
        },
        "tractor_name": {
            "type": "string",
            "annotation": F("tractor_name"),
            "group": "Transaction",
        },
        "trailer_one_name": {
            "type": "string",
            "annotation": F("trailer1_name"),
            "group": "Transaction",
        },
        "trailer_two_name": {
            "type": "string",
            "annotation": F("trailer2_name"),
            "group": "Transaction",
        },
        "location_code": {
            "type": "string",
            "annotation": F("location_code"),
            "group": "Locations",
        },
        "location_name": {
            "type": "string",
            "annotation": F("location_name"),
            "group": "Locations",
        },
        "date_created": {
            "type": "dateTime",
            "annotation": F("date_created"),
            "group": "Timestamps",
        },
        "last_modified_time": {
            "type": "dateTime",
            "annotation": F("last_modified_time"),
            "group": "Timestamps",
        },
        # additional data
        "shippers_name": {
            "type": "string",
            "annotation": F("additional_data__shippers_name"),
            "group": "Parties",
        },
        "transaction_cpl": {
            "type": "number",
            "annotation": F("additional_data__transaction_cpl"),
            "group": "Transaction",
        },
        "transaction_ctl": {
            "type": "number",
            "annotation": F("additional_data__transaction_ctl"),
            "group": "Transaction",
        },
        "erap": {
            "type": "string",
            "annotation": F("additional_data__erap"),
            "group": "ERP Contact",
        },
        "erap_phone_number": {
            "type": "string",
            "annotation": F("additional_data__erap_phone_number"),
            "group": "ERP Contact",
        },
        "hour_emergency_24": {
            "type": "string",
            "annotation": F("additional_data__hour_emergency24"),
            "group": "ERP Contact",
        },
        "product_requires_odorization": {
            "type": "boolean",
            "annotation": F("additional_data__product_requires_odorization"),
            "group": "Information",
        },
        "net_temperature_volume": {
            "type": "number",
            "annotation": F("additional_data__net_temperature_volume"),
            "uom": "cubic_m",
            "precision": "2",
            "group": "Volumes",
        },
        "oil_gross_standard_volume_gsvo": {
            "type": "number",
            "annotation": F("additional_data__oil_gross_standard_volume_gsvo"),
            "uom": "cubic_m",
            "precision": "2",
            "group": "Volumes",
        },
        "water_standard_volume_svw": {
            "type": "number",
            "annotation": F("additional_data__water_standard_volume_svw"),
            "uom": "cubic_m",
            "precision": "2",
            "group": "Volumes",
        },
        "oil_net_standard_volume_nsvo": {
            "type": "number",
            "annotation": F("additional_data__oil_net_standard_volume_nsvo"),
            "uom": "cubic_m",
            "precision": "2",
            "group": "Volumes",
        },
        "emulsion_standard_volume_sev": {
            "type": "number",
            "annotation": F("additional_data__emulsion_standard_volume_sev"),
            "uom": "cubic_m",
            "precision": "2",
            "group": "Volumes",
        },
        "measured_water_density_at_15_begin_of_load": {
            "type": "number",
            "annotation": F(
                "additional_data__measured_water_density_at15begin_of_load"
            ),
            "uom": "kg/L",
            "precision": "2",
            "group": "Metrics",
        },
        "measured_oil_density_at_15_begin_of_load": {
            "type": "number",
            "annotation": F("additional_data__measured_oil_density_at15begin_of_load"),
            "uom": "kg/L",
            "precision": "2",
            "group": "Metrics",
        },
        "carbon_dioxide": {
            "type": "number",
            "annotation": F("additional_data__co2"),
            "uom": "",
            "precision": "1",
            "group": "NGL Readings",
        },
        "ethane": {
            "type": "number",
            "annotation": F("additional_data__ethane"),
            "uom": "",
            "precision": "5",
            "group": "NGL Readings",
        },
        "propane": {
            "type": "number",
            "annotation": F("additional_data__propane"),
            "uom": "",
            "precision": "5",
            "group": "NGL Readings",
        },
        "methane": {
            "type": "number",
            "annotation": F("additional_data__methane"),
            "uom": "",
            "precision": "5",
            "group": "NGL Readings",
        },
        "n_butane": {
            "type": "number",
            "annotation": F("additional_data__n_butane"),
            "uom": "",
            "precision": "5",
            "group": "NGL Readings",
        },
        "n_pentane": {
            "type": "number",
            "annotation": F("additional_data__n_pentane"),
            "uom": "",
            "precision": "5",
            "group": "NGL Readings",
        },
        "n_hexane": {
            "type": "number",
            "annotation": F("additional_data__n_hexane"),
            "uom": "",
            "precision": "5",
            "group": "NGL Readings",
        },
        "nitrogen": {
            "type": "number",
            "annotation": F("additional_data__nitrogen"),
            "uom": "",
            "precision": "5",
            "group": "NGL Readings",
        },
        "iso_butane": {
            "type": "number",
            "annotation": F("additional_data__iso_butane"),
            "uom": "",
            "precision": "5",
            "group": "NGL Readings",
        },
        # ------------------------- ColeVille Start-----------------------------
        "n_heptane": {
            "type": "number",
            "annotation": F("additional_data__n_heptane"),
            "uom": "",
            "precision": "5",
            "group": "NGL Readings",
        },
        "ethane_net_volume": {
            "type": "number",
            "annotation": F("additional_data__ethane_net_volume"),
            "uom": "",
            "precision": "5",
            "group": "NGL Readings",
        },
        "iso_butane_net_volume": {
            "type": "number",
            "annotation": F("additional_data__iso_butane_net_volume"),
            "uom": "",
            "precision": "5",
            "group": "NGL Readings",
        },
        "iso_pentane_net_volume": {
            "type": "number",
            "annotation": F("additional_data__iso_pentane_net_volume"),
            "uom": "",
            "precision": "5",
            "group": "NGL Readings",
        },
        "n_butane_net_volume": {
            "type": "number",
            "annotation": F("additional_data__n_butane_net_volume"),
            "uom": "",
            "precision": "5",
            "group": "NGL Readings",
        },
        "n_heptane_net_volume": {
            "type": "number",
            "annotation": F("additional_data__n_heptane_net_volume"),
            "uom": "",
            "precision": "5",
            "group": "NGL Readings",
        },
        "n_hexane_net_volume": {
            "type": "number",
            "annotation": F("additional_data__n_hexane_net_volume"),
            "uom": "",
            "precision": "5",
            "group": "NGL Readings",
        },
        "n_pentane_net_volume": {
            "type": "number",
            "annotation": F("additional_data__n_pentane_net_volume"),
            "uom": "",
            "precision": "5",
            "group": "NGL Readings",
        },
        "propane_net_volume": {
            "type": "number",
            "annotation": F("additional_data__propane_net_volume"),
            "uom": "",
            "precision": "5",
            "group": "NGL Readings",
        },
        # ------------------------- ColeVille End-----------------------------

        "iso_pentane": {
            "type": "number",
            "annotation": F("additional_data__iso_pentane"),
            "uom": "",
            "precision": "5",
            "group": "NGL Readings",
        },
        "additive_ratio": {
            "type": "number",
            "annotation": F("additional_data__additive_ratio"),
            "group": "Metrics",
        },
        "location_type": {
            "type": "number",
            "annotation": F("additional_data__location_type"),
            "group": "Locations",
        },
        "liquid_type": {
            "type": "number",
            "annotation": F("additional_data__liquid_type"),
            "group": "Metrics",
        },
        "standard_water_cut": {
            "type": "number",
            "annotation": F("additional_data__standard_water_cut"),
            "group": "Metrics",
        },
        "reference_density": {
            "type": "number",
            "annotation": F("additional_data__reference_density"),
            "uom": "kg/L",
            "precision": "2",
            "group": "Metrics",
        },
        "product_density": {
            "type": "number",
            "annotation": F("additional_data__product_density"),
            "uom": "kg/L",
            "precision": "2",
            "group": "Metrics",
        },
        "water_density": {
            "type": "number",
            "annotation": F("additional_data__water_density"),
            "uom": "kg/L",
            "precision": "2",
            "group": "Metrics",
        },
        "allocation_code": {
            "type": "number",
            "annotation": F("additional_data__allocation_code"),
            "group": "Information",
        },
        # "allocation_key": {
        #     "type": "number",
        #     "annotation": F("additional_data__allocation_key"),
        #     "group": "Information",
        # },
    }

    required_fields = [
        "product_code",
        "ticket_status",
        "production_month",
    ]

    def get_base_filter(self):
        return TicketView.reporting.exclude_parents().filter(
            company=self.company.company.terminalbosscompany
        )

    def __init__(self, *args, **kwargs):
        """
        Additional data fields columns
        """
        self.limit_rows = 30000
        super().__init__(*args, **kwargs)

    def get_site_timezones(self, origin_site):
        tboss_company = self.company.company.terminalbosscompany
        return tboss_company.siteuser_set.get(username=origin_site).timezone

    def get_product(self, product_code):
        cache_key = (
            f"product_{self.company.company.name.replace(' ', '')}_{product_code}"
        )
        mem_cache = caches["memory"]
        if product := mem_cache.get(cache_key):
            return product

        product = ProductTable.objects.get(
            company__company=self.report.company.company, product_code=product_code
        )
        mem_cache.set(cache_key, product, 30)
        return product

    def get_usg_conversion_factor(self, product_code):
        product = self.get_product(product_code)
        return product.litre_to_gallon_conversion_factor

    def timestamped_extra_data_manipulation(self):
        if not self.get_selected_columns():
            return

        # display the timestamps as timezone aware
        for site_name in self.df["origin_site"].unique():
            tz_name = self.get_site_timezones(site_name)
            tz = ZoneInfo(tz_name)

            for column in self.df.columns:
                if column in self.columns and self.columns[column]["type"] in [
                    "dateTime"
                ]:
                    # pydantic datetime comes in as a string
                    # so check if its a datetime instance, if not -> init and format
                    # \u200B hidden character but if you remove that %Z will not working if there only one site
                    self.df.update(
                        self.df.loc[self.df.origin_site == site_name, column].apply(
                            lambda a: None
                            if (not a or pd.isnull(a))
                            else (
                                a.astimezone(tz).strftime("\u200B%Y-%m-%d %H:%M %Z")
                                if (isinstance(a, datetime))
                                else (
                                    isoparse(a)
                                    .astimezone(tz)
                                    .strftime("%Y-%m-%d %H:%M %Z")
                                )
                            )
                        )
                    )

    def extra_data_manipulation(self):
        if "origin_site" in self.df.columns:
            self.timestamped_extra_data_manipulation()
        else:
            super().extra_data_manipulation()

        # fill numeric nan cols with 0
        numeric_columns = self.df.select_dtypes(
            include=["number"], exclude=["datetime", "timedelta"]
        ).columns
        self.df[numeric_columns] = self.df[numeric_columns].fillna(0)

        if "net_transferred_volume_usg" in self.df:
            self.df["net_transferred_volume_usg"] = self.df.apply(
                lambda x: round(x.net_transferred_volume_usg
                * self.get_usg_conversion_factor(x.product_code),2),
                axis=1,
            )

        # match the rounding in reports and ticket generation
        # column_sigdigs = (
        #     ("reference_density", 0),
        #     ("measured_temperature", 2),
        #     ("average_pressure", 0),
        #     ("transferred_gross_volume", 3),
        #     ("transferred_net_volume", 3),
        #     ("additive_volume", 3),
        #     ("preset_volume", 3),
        #     ("net_temperature_volume", 3),
        #     ("net_transferred_volume_usg", 0),
        #     ("carbon_dioxide", 2),
        #     ("ethane", 2),
        #     ("propane", 2),
        #     ("methane", 2),
        #     ("n_butane", 2),
        #     ("n_pentane", 2),
        #     ("n_hexane", 2),
        #     ("nitrogen", 2),
        #     ("iso_butane", 2),
        #     ("iso_pentane", 2),
        # )
        # for column, sigdig in column_sigdigs:
        #     if column in self.df and not self.df[column].empty:
        #         self.df[column] = self.df[column].fillna(0).round(sigdig)

        choices = dict(TicketView.TICKET_STATUS)

        self.df.loc[(self.df["ticket_status"] == 0), "ticket_status"] = choices[
            TicketView.ORIGINAL
        ]
        self.df.loc[(self.df["ticket_status"] == 1), "ticket_status"] = choices[
            TicketView.VOID
        ]
        self.df.loc[(self.df["ticket_status"] == 2), "ticket_status"] = choices[
            TicketView.MODIFIED
        ]

        # do string splice instead to make it faster
        # self.df["production_month"] = pd.to_datetime(
        #     self.df["production_month"]
        # ).dt.to_period("M")
        self.df["production_month"] = (
            self.df["production_month"].astype(str).str.slice(0, 7)
        )

        # self.df = self.df[:10000]  # set a limit of 10000 rows
