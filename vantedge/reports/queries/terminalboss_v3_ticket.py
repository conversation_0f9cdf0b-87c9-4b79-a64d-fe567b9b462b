from .base import RawData
from django.db.models import (
    F,
    Value,
    Char<PERSON>ield,
    OuterRef,
    Subquery,
)

from django.db.models.functions import Cast, Replace
from vantedge.tboss.models import Ticket
from typing import Any

import uuid, logging

logger = logging.getLogger(__name__)


class TerminalBossV3TicketData(RawData):
    columns = {
        "is_manual": {
            "type": "boolean",
            "annotation": F("is_manual"),
            "group": "Ticket Reference",
            "caption": "All Sites",
        },
        "ticket_code": {
            "type": "string",
            "annotation": F("code"),
            "group": "Ticket Reference",
            "caption": "All Sites",
        },
        "ticket_type": {
            "type": "string",
            "annotation": F("ticket_type"),
            "group": "Ticket Reference",
            "caption": "All Sites",
        },
        "ticket_status": {
            "type": "string",
            "annotation": F("status"),
            "choices": dict(Ticket.TransactionStatus.choices),
            "group": "Ticket Reference",
            "caption": "All Sites",
        },
        "limit": {
            "type": "string",
            "annotation": F("limit__name"),
            "group": "Ticket Reference",
            "caption": "All Sites",
        },
        "created_by": {
            "type": "string",
            "annotation": F("created_by__name"),
            "group": "Parties Associated",
            "caption": "All Sites",
        },
        "modified_by": {
            "type": "string",
            "annotation": F("modified_by__name"),
            "group": "Parties Associated",
            "caption": "All Sites",
        },
        "administered_by": {
            "type": "string",
            "annotation": F("user__name"),
            "group": "Parties Associated",
            "caption": "All Sites",
        },
        "created_by_server": {
            "type": "string",
            "annotation": F("created_by_server__name"),
            "group": "Ticket Reference",
            "caption": "All Sites",
        },
        "company": {
            "type": "string",
            "annotation": F("company__name"),
            "group": "Parties Associated",
            "caption": "All Sites",
        },
        "company_id": {
            "type": "string",
            "annotation": F("company_id"),
            "group": "Parties Associated",
            "caption": "All Sites",
        },
        "product": {
            "type": "string",
            "annotation": F("product__name"),
            "group": "Product Information",
            "caption": "All Sites",
        },
        "location": {
            "type": "string",
            "annotation": F("location__name"),
            "group": "Product Information",
            "caption": "All Sites",
        },
        # "lsd": {
        #     "type": "string",
        #     "annotation": F("location__additional_data__source_data__lsd"),
        #     "group": "Product Information",
        # },
        # "uwi": {
        #     "type": "string",
        #     "annotation": F("location__additional_data__source_data__uwi"),
        #     "group": "Product Information",
        # },
        "quantity": {
            "type": "string",
            "annotation": F("quantity"),
            "group": "Product Measurements",
            "caption": "All Sites",
        },
        "quantity_unit": {
            "type": "string",
            "annotation": F("properties__base_unit"),
            "group": "Product Measurements",
            "caption": "All Sites",
        },
        # "density": {
        #     "type": "string",
        #     "annotation": F("density"),
        #     "group": "Product Measurements",
        # },
        # "density_unit": {
        #     "type": "string",
        #     "annotation": F("density_unit"),
        #     "group": "Product Measurements",
        # },
        "start_time": {
            "type": "dateTime",
            "annotation": F("start_time"),
            "group": "Timestamps",
            "caption": "All Sites",
        },
        "end_time": {
            "type": "dateTime",
            "annotation": F("end_time"),
            "group": "Timestamps",
            "caption": "All Sites",
        },
        "created_timestamp": {
            "type": "dateTime",
            "annotation": F("created"),
            "group": "Timestamps",
            "caption": "All Sites",
        },
        "modified_timestamp": {
            "type": "dateTime",
            "annotation": F("modified"),
            "group": "Timestamps",
            "caption": "All Sites",
        },
    }

    outer_refs = {
        # "source": {
        #     "type_cast": CharField,
        #     "model": Location,
        #     "field": "name",
        #     "string_path": "additional_data",
        # }
    }

    def __init__(self, *args, **kwargs):
        """
        Additional data fields columns
        """
        self.limit_rows = 30000
        super().__init__(*args, **kwargs)
        # queryset = self.get_base_filter().values("data", "additional_data")
        # additional_data_columns = self.retrieve_additional_columns(queryset=queryset)
        from vant_dbreplication.models.server import DBReplicationServer

        data_columns = Ticket._data_columns(self.report.company.company.terminalboss)
        data_report_columns = {}
        references = data_columns.get("references")
        server_names = []
        for server_key, data_fields in data_columns.get("data_fields", {}).items():
            server = DBReplicationServer.objects.get(pk=uuid.UUID(server_key))
            server_names.append(server.name)
            server_reference = references.get(server_key, {})
            for class_name, class_fields in data_fields.items():
                properties = class_fields.get("properties", {})
                string_path = "data__"
                data_report_columns = self.properties_inspect(
                    properties,
                    string_path,
                    server_reference,
                    data_report_columns,
                    server,
                )
        self.columns = {**self.columns, **data_report_columns}
        configuration_labels = self.get_configuration_labels()
        # Set columns from all the sites with the caption
        for column in self.columns:
            servers = (
                self.columns[column].get("caption", "").replace(" ", "").split(",")
            )
            if server_names == servers:
                self.columns[column]["caption"] = "All Sites"

            # if column key exists in configuration_labels, this takes precendence
            if column in configuration_labels:
                label = configuration_labels.get(column)
                if label:
                    self.columns[column]["label"] = label

    def properties_inspect(
        self,
        properties: dict,
        string_path: str,
        references: dict,
        properties_annotation: dict,
        server: Any,
    ):
        """
        Recursively translate schema definition to django annotations
        """
        pa = properties_annotation.copy()
        for field_name, field in properties.items():
            # enable all the columns provided
            # if string_path == "data__" and field_name not in [
            #     "print_data",
            #     "quantity_data",
            # ]:
            #     continue

            annotation_path = f"{string_path}{field_name}"
            field_type = field.get("type")
            if (
                "anyOf" in field
                and isinstance(field.get("anyOf"), list)
                and len(field.get("anyOf")) > 0
                and "type" in field.get("anyOf")[0]
            ):
                field_type = field.get("anyOf")[0].get("type")
            format = field.get("format")
            # if type object, lookup using 'references' by title
            if field_type in [None, "object"]:
                allof = "all_of"
                if "allOf" in field:
                    allof = "allOf"
                reference = references.get(
                    field.get(allof, [{}])[0]
                    .get("$ref", "")
                    .replace("#/definitions/", "")
                    .replace("#/$defs/", ""),
                    {},
                )
                # get the properties of the reference
                reference_properties = reference.get("properties", {})
                reference_columns = self.properties_inspect(
                    reference_properties, f"{annotation_path}__", references, pa, server
                )
                pa = {**pa, **reference_columns}
            elif field_type not in [
                "boolean",
                "string",
                "date-time",
                "integer",
                "number",
            ]:
                # exclude columns that do not fall into the list of types defined
                continue
            else:
                prefix = ""
                group = "Other"
                annotation_type = None
                annotation = F(annotation_path)

                if field_name in self.columns:
                    prefix = f"data__"

                if field_type == "boolean":
                    annotation_type = "boolean"
                    group = "Ticket Reference"

                elif field_type == "string":
                    if format == "date":
                        annotation_type = "date"
                        group = "Timestamps"
                    elif format == "date-time":
                        annotation_type = "dateTime"
                        group = "Timestamps"
                    else:
                        annotation_type = "string"
                elif field_type in ["number", "integer"]:
                    # decimals, floats, integers
                    annotation_type = "number"

                if (
                    "index" in field_name
                    or "number" in field_name
                    or "id" in field_name
                    or "name" in field_name
                    or "ticket" in field_name
                    or "canutec" in field_name
                    or "chemtrec" in field_name
                    or "source" in field_name
                    or "destination" in field_name
                    or "bol" in field_name
                    or "address" in field_name
                    or "erap" in field_name
                    or "riser" in field_name
                    or "account" in field_name
                    or "riser" in field_name
                    or "tractor" in field_name
                    or "trailer" in field_name
                ):
                    group = "Ticket Reference"

                elif "time" in field_name or "date" in field_name:
                    group = "Timestamps"

                elif (
                    "unit" in field_name
                    or "volume" in field_name
                    or "factor" in field_name
                    or "temperature" in field_name
                    or "density" in field_name
                    or "water" in field_name
                    or "flow" in field_name
                    or "pressure" in field_name
                    or "mass" in field_name
                    or "preset" in field_name
                    or "device" in field_name
                    or "additive" in field_name
                    or "batch" in field_name
                ):
                    group = "Product Measurements"
                elif (
                    "admin" in field_name
                    or "carrier" in field_name
                    or "driver" in field_name
                    or "receiver" in field_name
                    or "shipper" in field_name
                    or "consignee" in field_name
                    or "consignor" in field_name
                ):
                    group = "Parties Associated"
                column_key = f"{prefix}{field_name}"
                column_label = column_key.lower()
                column = {
                    "type": annotation_type,
                    "annotation": annotation,
                    "group": group,
                    "label": column_label,
                    "caption": server.name,
                }
                underscorized_column = column_key.lower()
                if not underscorized_column in pa:
                    pa[underscorized_column] = column
                elif pa[underscorized_column].get("annotation") != annotation:
                    prefixed_underscorized_column = annotation_path.replace(
                        "data__", "", 1
                    )

                    # try until there are no more '__' patterns in annotation path to find a keyword not taken by another field
                    # with a different annotation_path
                    # e.g. because "average_temperature": F("data__print_data__average_temperature") or F("data__quantity_data__average_temperature")
                    # so we want to prefix the quantity_data column as "quantity_data__average_temperature": F("data__quantity_data__average_temperature")

                    while (
                        prefixed_underscorized_column in pa
                        and pa[prefixed_underscorized_column].get("annotation")
                        != annotation
                    ):
                        double_underscore = prefixed_underscorized_column.find("__")
                        if double_underscore >= 0:
                            prefixed_underscorized_column = (
                                prefixed_underscorized_column[double_underscore + 2 :]
                            )
                        else:
                            break

                    if prefixed_underscorized_column not in pa:
                        if "print_data" in annotation_path:
                            column_label += "_(bol)"
                        elif "quantity_data" in annotation_path:
                            column_label += "_(measurement)"
                        column["label"] = column_label
                        underscorized_column = prefixed_underscorized_column
                        pa[underscorized_column] = column
                    elif (
                        pa[prefixed_underscorized_column].get("annotation")
                        == annotation
                        and not server.name
                        in pa[prefixed_underscorized_column]["caption"]
                    ):
                        pa[prefixed_underscorized_column][
                            "caption"
                        ] += f", {server.name}"
                    else:  # underscorized_column == column_key.lower():
                        logger.warning(
                            f"Cannot find a keyword not taken by another field for annotation_path {annotation_path} for server{server.name}. Column not added as selectable columns."
                        )
                        continue

                elif not server.name in pa[underscorized_column]["caption"]:
                    pa[underscorized_column]["caption"] += f", {server.name}"

        return pa

    def get_base_filter(self):
        # set type casts
        type_casts = {}
        selected_columns = {}

        """
        Ticket({'additional_data': { 'location_key'' }})
        CASE WHEN STATEMENTS VS SUBQUERY

        >>> from timeit import default_timer as Timer
        >>> def ann():
        ...     start = Timer()
        ...     tickets = Ticket.objects.annotate(key = KeyTransform('station', 'additional_data'))
        ...     whens = [
        ...             When(key=str(a.get('id')), then=Value(a.get('name'))) for a in locations
        ...     ]
                for i in range(0, 5):
                    locations = Location.objects.values('id', 'name')
                    tickets = tickets.annotate(**{fname'{i}': Case(*whens, default=Value(''), output_field=CharField())})
        ...     tickets = tickets.annotate(name=Case(*whens, default=Value(''), output_field=CharField()))
        ...     ltickets = list(tickets)
        ...     print(f'{abs((Timer() - start)):.4f} seconds')
        ...     return ltickets
        ...
        >>> ant = ann()
        10.7493 seconds

        >>> def cast():
        ...     start = Timer()
        ...     type_casts = {}
        ...     outer_refs = {
                 "station": {
                        "type_cast": CharField,
                        "model": Location,
                        "field": "name",
                        "string_path": "additional_data",
                    }
                }
        ...     for outer_ref_key, outer_ref_info in outer_refs.items():
        ...             string_path = outer_ref_info.get("string_path", "additional_data")
        ...             model = outer_ref_info.get("model")
        ...             outer_ref_field = outer_ref_info.get("field")
        ...             annotation_string_path = f"type_cast_{string_path}__{outer_ref_key}"
        ...             mapped_type_cast_field = f"map_{annotation_string_path}"
        ...             type_cast = Replace(
        ...                     Cast(
        ...                             F(f"{string_path}__{outer_ref_key}"),
        ...                             output_field=outer_ref_info.get("type_cast")(),
        ...                     ),
        ...                     Value('"'),
        ...                     Value(""),
        ...             )
        ...             model_qs = model.objects.annotate(primary_key=Cast('pk', CharField())).filter(primary_key=OuterRef(annotation_string_path))
        ...             mapped_type_cast = Subquery(model_qs.values(outer_ref_field)[:1], output_field=CharField())
        ...             type_casts.setdefault(annotation_string_path, type_cast)
        ...             type_casts.setdefault(mapped_type_cast_field, mapped_type_cast)
        ...             tickets = Ticket.objects.filter().annotate(**type_casts)
        ...             ltickets = list(tickets)
        ...             print(f'{abs((Timer() - start)):.4f} seconds')
        ...     return ltickets
        ...
        >>> c = cast()
        18.7344 seconds
        """

        for outer_ref_key, outer_ref_info in self.outer_refs.items():
            string_path = outer_ref_info.get("string_path", "additional_data")
            model = outer_ref_info.get("model")
            outer_ref_field = outer_ref_info.get("field")
            annotation_string_path = f"type_cast_{string_path}__{outer_ref_key}"
            mapped_type_cast_field = f"map_{annotation_string_path}"

            # type cast jsonb string to charfield string
            type_cast = Replace(
                Cast(
                    F(f"{string_path}__{outer_ref_key}"),
                    output_field=outer_ref_info.get("type_cast")(),
                ),
                Value('"'),
                Value(""),
            )
            model_qs = model.objects.annotate(
                primary_key=Cast("pk", CharField())
            ).filter(primary_key=OuterRef(annotation_string_path))
            mapped_type_cast = Subquery(
                model_qs.values(outer_ref_field)[:1], output_field=CharField()
            )
            type_casts.setdefault(annotation_string_path, type_cast)
            type_casts.setdefault(mapped_type_cast_field, mapped_type_cast)
        return Ticket.objects.filter(
            dbreplication_client_company=self.report.company.company.terminalboss
        ).annotate(**type_casts)

    def extra_data_manipulation(self):
        # Skip extra manipulation if no selected columns
        if not self.get_selected_columns():
            return

        super().extra_data_manipulation()
        ticket_status_choices = dict(Ticket.TransactionStatus.choices)

        if "ticket_status" in self.df:
            self.df["ticket_status"] = self.df["ticket_status"].apply(
                lambda s: ticket_status_choices.get(s, s)
            )

        # for all numeric columns, replace NaN with None
        numeric_columns = self.df.select_dtypes(
            include=["number"], exclude=["datetime", "timedelta"]
        ).columns
        self.df[numeric_columns] = self.df[numeric_columns].fillna(0)

        # set the rounding for numeric columns
        for numeric_col in numeric_columns:
            if self.df[numeric_col].empty:
                continue
            # sigdigs of two
            elif any(
                keyword in numeric_col
                for keyword in [
                    "pressure",
                    "volume",
                    "temperature",
                    "density",
                    "mass",
                    "flow_rate",
                ]
            ):
                self.df[numeric_col] = self.df[numeric_col].fillna(0).round(2)
            elif any(keyword in numeric_col for keyword in ["factor", "watercut"]):
                self.df[numeric_col] = self.df[numeric_col].fillna(0).round(5)

        # set booleans to "True" or "False"
        mask = self.df.applymap(type) != bool
        bool_mapping = {True: "True", False: "False"}
        self.df = self.df.where(mask, self.df.replace(bool_mapping))

    # def retrieve_additional_columns(self, queryset):
    #     columns_dict = {}
    #     cached_columns = {}
    #     cache_key = "v3ticketreport_additionalcolumns"
    #     # check the cache
    #     if self.report:
    #         client_company_id = self.report.company.id
    #         cached_columns = cache.get(cache_key, {})
    #         if not cached_columns:
    #             cache.set(cache_key, {}, None)
    #         columns_dict = cached_columns.get(client_company_id, {})
    #         if columns_dict:
    #             print("cached!")
    #             return columns_dict

    #     for obj in queryset:
    #         data = {**obj.get("data"), **obj.get("additional_data")}
    #         string_path = "data"
    #         columns_dict = self.traverse_additional_data(
    #             data, string_path, columns_dict
    #         )

    #     print("store cache")
    #     cached_columns[client_company_id] = columns_dict
    #     cache.set(cache_key, cached_columns, None)
    #     return columns_dict

    # def traverse_additional_data(
    #     self, additional_data: dict, string_path: str, columns_dict: dict
    # ) -> dict:
    #     """
    #     Traverses additional data column and checks the type of field to create annotation
    #     """
    #     keys = list(filter(lambda k: k not in columns_dict, additional_data.keys()))
    #     for key in keys:
    #         keyval = additional_data.get(key)
    #         annotation_string_path = f"{string_path}__{key}"
    #         if isinstance(keyval, dict):
    #             columns_dict = self.traverse_additional_data(
    #                 keyval, annotation_string_path, columns_dict
    #             )
    #         elif isinstance(keyval, list):
    #             # exclude list of data for now
    #             continue
    #         else:
    #             prefix = ""
    #             group = "Other"
    #             type = "string"
    #             annotation = F(annotation_string_path)
    #             if key in self.columns:
    #                 prefix = "site_"

    #             if isinstance(keyval, bool):
    #                 type = "boolean"
    #                 group = "Ticket Reference"
    #             elif isinstance(keyval, (int, float)):
    #                 type = "number"
    #             elif isinstance(keyval, datetime.date) or "date" in key:
    #                 type = "date"
    #                 group = "Timestamps"
    #             elif isinstance(keyval, datetime.datetime) or "time" in key:
    #                 type = "dateTime"
    #                 group = "Timestamps"

    #             if (
    #                 "index" in key
    #                 or "number" in key
    #                 or "id" in key
    #                 or "name" in key
    #                 or "ticket" in key
    #                 or "canutec" in key
    #                 or "chemtrec" in key
    #                 or "source" in key
    #                 or "destination" in key
    #                 or "bol" in key
    #             ):
    #                 group = "Ticket Reference"
    #             elif (
    #                 "unit" in key
    #                 or "volume" in key
    #                 or "factor" in key
    #                 or "temperature" in key
    #                 or "density" in key
    #                 or "water" in key
    #                 or "flow" in key
    #                 or "pressure" in key
    #                 or "mass" in key
    #                 or "preset" in key
    #                 or "device" in key
    #                 or "additive" in key
    #                 or "batch" in key
    #             ):
    #                 group = "Product Measurements"
    #             elif (
    #                 "admin" in key
    #                 or "carrier" in key
    #                 or "driver" in key
    #                 or "receiver" in key
    #                 or "shipper" in key
    #             ):
    #                 group = "Parties Associated"

    #             # type cast annotations for foreign keys in additional_data
    #             column_key = f"{prefix}{key}"
    #             if key in self.outer_refs:
    #                 type_cast_mapped_field = f"map_type_cast_{annotation_string_path}"
    #                 annotation = F(type_cast_mapped_field)
    #             column = {
    #                 "type": type,
    #                 "annotation": annotation,
    #                 "group": group,
    #                 "label": camelize(column_key.lower()),
    #                 "keyval": keyval,
    #             }
    #             if not column_key in columns_dict or not columns_dict[column_key].get(
    #                 "keyval"
    #             ):
    #                 columns_dict[camel_to_underscore(column_key.lower())] = column
    #     return columns_dict
