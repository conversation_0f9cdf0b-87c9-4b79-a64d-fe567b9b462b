from .base import RawData
from vantedge.trackandtrace.models import Trip, RailCar
from django.db.models import Case, CharField, F, When, DateTimeField, Q, OuterRef
from django.db.models import Value as V
from django.db.models.functions import Concat, Now, Round, Extract
import math


class TripsRawData(RawData):
    LATEST_TRIPS = "latestTrips"
    additional_filters = [LATEST_TRIPS]
    columns = {
        "commodity": {"type": "number", "annotation": F("stcc_id"), "group": "Product"},
        "commodity_description": {
            "type": "string",
            "annotation": F("stcc__name"),
            "group": "Product",
        },
        "un_code": {
            "type": "string",
            "annotation": F("stcc__product__data__codes__un_code"),
            "group": "Product"
        },
        "ship_date": {
            "type": "dateTime",
            "annotation": Case(
                When(
                    Q(
                        bol_timestamp__isnull=False,
                        bol_timestamp__lt=F("departure_timestamp"),
                    ),
                    then=F("bol_timestamp"),
                ),
                default=F("departure_timestamp"),
                output_field=CharField(),
            ),
            "group": "Timestamps",
        },
        "eta_date": {
            "type": "dateTime",
            "annotation": F("eta_timestamp"),
            "group": "Timestamps",
        },
        "railroad_eta_date": {
            "type": "dateTime",
            "annotation": F("last_car_event__railroad_eta"),
            "group": "Timestamps",
        },
        "original_eta_date": {
            "type": "dateTime",
            "annotation": F("original_eta_timestamp"),
            "group": "Timestamps",
        },
        "release_date": {
            "type": "dateTime",
            "annotation":F("detention_stop_event__event_timestamp"
            ),  # F("release_timestamp"),
            "group": "Timestamps",
        },
        "cp_date": {
            "type": "dateTime",
            "annotation": F("cp_timestamp"),
            "group": "Timestamps",
        },
        "ap_date": {
            "type": "dateTime",
            "annotation": F("ap_timestamp"),
            "group": "Timestamps",
        },
        "delivery_date": {
            "type": "dateTime",
            "annotation": F("arrival_timestamp"),
            "group": "Timestamps",
        },
        "bol": {
            "type": "string",
            "annotation": F("last_car_event__bol"),
            "group": "Reference",
        },
        "car": {
            "type": "string",
            "annotation": Concat("car__mark", "car__number"),
            "group": "Equipment",
        },
        "shipper": {"type": "string", "annotation": F("shipper"), "group": "Parties"},
        "origin": {
            "type": "string",
            "annotation": Concat("origin__name", V(" ("), "origin__state", V(")")),
            "group": "Locations",
        },
        "origin_railroad": {
            "type": "string",
            "annotation": F("last_car_event__origin_railroad"),
            "group": "Railroad",
        },
        "destination": {
            "type": "string",
            "annotation": Concat(
                "destination__name", V(" ("), "destination__state", V(")")
            ),
            "group": "Locations",
        },
        "destination_railroad": {
            "type": "string",
            "annotation": F("last_car_event__destination_railroad"),
            "group": "Locations",
        },
        "loaded": {
            "type": "boolean",
            "annotation": F("last_car_event__loaded"),
            "group": "Equipment",
        },
        "active": {"type": "boolean", "annotation": F("is_active"), "group": "Event"},
        "location": {
            "type": "string",
            "annotation": Concat(
                "last_car_event__location__name",
                V(" ("),
                "last_car_event__location__state",
                V(")"),
            ),
            "group": "Locations",
        },
        "event_code": {
            "type": "string",
            "annotation": F("last_car_event__event_code_id"),
            "group": "Event",
        },
        "car_home": {
            "type": "string",
            "annotation": F("car__home"),
            "group": "Equipment",
        },
        "car_fleet": {
            "type": "string",
            "annotation": F("car__fleet"),
            "group": "Equipment",
        },
        "car_subfleet": {
            "type": "string",
            "annotation": F("car__subfleet"),
            "group": "Equipment",
        },
        "car_division": {
            "type": "string",
            "annotation": F("car__division"),
            "group": "Equipment",
        },
        "car_pool": {
            "type": "string",
            "annotation": F("car__pool"),
            "group": "Equipment",
        },
        "car_project": {
            "type": "string",
            "annotation": F("car__project"),
            "group": "Equipment",
        },
        "car_group": {
            "type": "string",
            "annotation": F("car__group"),
            "group": "Equipment",
        },
        "car_last_contents": {
            "type": "string",
            "annotation": F("car__last_contents"),
            "group": "Equipment",
        },
        "car_lease_company": {
            "type": "string",
            "annotation": F("car__lease_from_company"),
            "group": "Lease",
        },
        "car_lease_contract": {
            "type": "string",
            "annotation": F("car__lease_from_contract_number"),
            "group": "Lease",
        },
        "car_lease_rider": {
            "type": "string",
            "annotation": F("car__lease_from_rider_number"),
            "group": "Lease",
        },
        "event_code_label": {
            "type": "string",
            "annotation": F("last_car_event__event_code__label"),
            "group": "Event",
        },
        "event_code_description": {
            "type": "string",
            "annotation": F("last_car_event__event_code__description"),
            "group": "Event",
        },
        "location_city": {
            "type": "string",
            "annotation": F("last_car_event__location__name"),
            "group": "Locations",
        },
        "location_state": {
            "type": "string",
            "annotation": F("last_car_event__location__state"),
            "group": "Locations",
        },
        "location_country": {
            "type": "string",
            "annotation": F("last_car_event__location__country"),
            "group": "Locations",
        },
        "origin_city": {
            "type": "string",
            "annotation": F("last_car_event__origin__name"),
            "group": "Locations",
        },
        "origin_state": {
            "type": "string",
            "annotation": F("last_car_event__origin__state"),
            "group": "Locations",
        },
        "origin_country": {
            "type": "string",
            "annotation": F("last_car_event__origin__country"),
            "group": "Locations",
        },
        "destination_city": {
            "type": "string",
            "annotation": F("last_car_event__destination__name"),
            "group": "Locations",
        },
        "destination_state": {
            "type": "string",
            "annotation": F("last_car_event__destination__state"),
            "group": "Locations",
        },
        "destination_country": {
            "type": "string",
            "annotation": F("last_car_event__destination__country"),
            "group": "Locations",
        },
        "current_position_railroad": {
            "type": "string",
            "annotation": F("last_car_event__location__railroad"),
            "group": "Railroad",
        },
        "last_event_date": {
            "type": "dateTime",
            "annotation": F("last_car_event__event_timestamp"),
            "group": "Timestamps",
        },
        "bol_timestamp": {
            "type": "dateTime",
            "annotation": F("last_car_event__bol_timestamp"),
            "group": "Timestamps",
        },
        "quantity": {
            "type": "number",
            "annotation": F("last_car_event__weight"),
            "uom": "kg",
            "precision": "2",
            "group": "Equipment",
        },
        "unit": {"type": "string", "annotation": V("LBS"), "group": "Reference"},
        "freight_payor": {
            "type": "string",
            "annotation": F("last_car_event__freightpayer_name"),
            "group": "Parties",
        },
        "status": {
            "type": "string",
            "annotation": Case(
                When(arrival_timestamp__isnull=True, then=V("Enroute")),
                default=V("Arrived"),
                output_field=CharField(),
            ),
            "group": "Status",
        },
        "days_on_site": {
            "type": "number",
            "annotation": Now() - F("arrival_timestamp"),
            "group": "Status",
        },
        "days_en_route": {
            "type": "number",
            "annotation": Case(
                When(arrival_timestamp__lt=Now(), then=F("arrival_timestamp")),
                default=Now(),
                output_field=DateTimeField(),
            )
            - F("departure_timestamp"),
            "group": "Status",
        },
        "consignee": {
            "type": "string",
            "annotation": F("last_car_event__consignee_name"),
            "group": "Parties",
        },
        "care_of_party": {
            "type": "string",
            "annotation": F("last_car_event__care_of_name"),
            "group": "Parties",
        },
        "volume": {
            "type": "number",
            "annotation": F("last_car_event__volume"),
            "uom": "liter",
            "precision": "2",
            "group": "Equipment",
        },
        "owner": {
            "type": "string",
            "annotation": F("last_car_event__car__lease_from_company"),
            "group": "Parties",
        },
        "contract_buyer_party": {
            "type": "string",
            "annotation": Case(
                When(
                    pattern__seller_contract__isnull=False,
                    then=F("pattern__sale_contract__buyer__name"),
                ),
                When(
                    pattern__purchase_contract__isnull=False,
                    then=F("pattern__purchase_contract__buyer__name"),
                ),
            ),
            "group": "Parties",
        },
        "contract_seller_party": {
            "type": "string",
            "annotation": Case(
                When(
                    pattern__seller_contract__isnull=False,
                    then=F("pattern__sale_contract__seller__name"),
                ),
                When(
                    pattern__purchase_contract__isnull=False,
                    then=F("pattern__purchase_contract__seller__name"),
                ),
            ),
            "group": "Parties",
        },
        "rail_rate_cost": {
            "type": "number",
            "annotation": Case(
                When(
                    movement_cost__isnull=False,
                    then=F("movement_cost__cost_detail__total_cost__amount"),
                ),
                default=None,
                output_field=CharField(),
            ),
            "group": "Rail Rate"
        },
        "rail_rate_base_charge": {
            "type": "number",
            "annotation": Case(
                When(
                    movement_cost__isnull=False,
                    then=F("movement_cost__cost_detail__base_charge__amount"),
                ),
                default=None,
                output_field=CharField(),
            ),
            "group": "Rail Rate"
        },
        "rail_rate_charge_rule_11": {
            "type": "number",
            "annotation": Case(
                When(
                    movement_cost__isnull=False,
                    then=F("movement_cost__cost_detail__charge_rule_11__amount"),
                ),
                default=None,
                output_field=CharField(),
            ),
            "group": "Rail Rate"
        },
        "rail_rate_surcharge_fuel_cost": {
            "type": "number",
            "annotation": Case(
                When(
                    movement_cost__isnull=False,
                    then=F("movement_cost__cost_detail__surcharge_fuel_cost__amount"),
                ),
                default=None,
                output_field=CharField(),
            ),
            "group": "Rail Rate"
        },
        "rail_rate_mileage": {
            "type": "number",
            "annotation": Case(
                When(
                    movement_cost__isnull=False,
                    then=F("movement_cost__cost_detail__mileage"),
                ),
                default=None,
                output_field=CharField(),
            ),
            "group": "Rail Rate"
        },
        "rail_rate_currency": {
            "type": "string",
            "annotation": Case(
                When(
                    movement_cost__isnull=False,
                    then=F("movement_cost__cost_detail__currency"),
                ),
                default=None,
                output_field=CharField(),
            ),
            "group": "Rail Rate"
        },
        "rail_rate_id": {
            "type": "number",
            "annotation": Case(
                When(
                    movement_cost__isnull=False,
                    then=F("movement_cost__rate_snapshot__id"),
                ),
                default=None,
                output_field=CharField(),
            ),
            "group": "Rail Rate"
        },
        "rail_rate_surcharge_fuel": {
            "type": "number",
            "annotation": Case(
                When(
                    movement_cost__isnull=False,
                    then=F("movement_cost__rate_snapshot__surcharge_fuel"),
                ),
                default=None,
                output_field=CharField(),
            ),
            "group": "Rail Rate"
        },
        "detention_days": {
            "type": "string",
            "annotation": Extract(F("detention_time"), "DAY"),
            "group": "Detention"
        },
        "detention_tier_one": {
            "type": "number",
            "annotation": F("detention_data__detention_scheme__0__rate"),
            "group": "Detention"
        },
        "detention_tier_two": {
            "type": "number",
            "annotation": F("detention_data__detention_scheme__1__rate"),
            "group": "Detention"
        },
        "detention_tier_three": {
            "type": "number",
            "annotation": F("detention_data__detention_scheme__2__rate"),
            "group": "Detention"
        },
        "detention_cost": {
            "type": "number",
            "annotation": F("detention_cost"),
            "group": "Detention"
        }
    }

    required_fields = ["last_event_date", "car"]

    def get_base_filter(self):
        selected_columns = self.get_selected_columns()
        query = Q(company=self.company.company.track_trace)
        # if 'active' column is not selected on the report, filter the base queryset via is_active=True
        if "active" not in selected_columns:
            query &= Q(is_active=True)

        return Trip.objects.filter(query)

    def extra_data_manipulation(self):
        super().extra_data_manipulation()

        if "days_en_route" in self.df.columns:
            self.df["days_en_route"] = self.df["days_en_route"].apply(
                lambda a: (
                    "N/A"
                    if math.isnan(a.days)
                    else ("0" if a.days <= 0 else str(a.days))
                )
                if a
                else "N/A"
            )
        if "days_on_site" in self.df.columns:
            self.df["days_on_site"] = self.df["days_on_site"].apply(
                lambda a: (
                    "N/A"
                    if math.isnan(a.days)
                    else ("0" if a.days <= 0 else str(a.days))
                )
                if a
                else "N/A"
            )
        if "loaded" in self.df.columns:
            self.df["loaded"] = self.df["loaded"].apply(
                lambda loaded: "Loaded" if loaded else "Empty"
            )

        if "active" in self.df.columns:
            self.df["active"] = self.df["active"].apply(
                lambda active: "Active" if active else "Inactive"
            )

        if "rail_rate_surcharge_fuel" in self.df.columns:
            self.df["rail_rate_surcharge_fuel"] = self.df["rail_rate_surcharge_fuel"].apply(
        lambda rail_rate_surcharge_fuel: rail_rate_surcharge_fuel if rail_rate_surcharge_fuel != "null" else ""
            )

        if "rail_rate_mileage" in self.df.columns:
            self.df["rail_rate_mileage"] = self.df["rail_rate_mileage"].apply(
        lambda rail_rate_mileage: rail_rate_mileage if rail_rate_mileage != "null" else ""
            )

        # filter the dataframe to only show the latest trips
        if hasattr(self, "report"):
            additional_filters = self.report.configuration.get("additional_filters")
            if self.LATEST_TRIPS in additional_filters:
                self.df = (
                    self.df.sort_values("last_event_date")
                    .drop_duplicates("car", keep="last")
                    .sort_index()
                )
