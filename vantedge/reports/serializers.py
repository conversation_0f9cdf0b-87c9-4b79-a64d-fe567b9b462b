from rest_framework import serializers
from django_auto_prefetching import AutoPrefetchViewSetMixin
from .models import Report
from .data_types import data_types as dt
import copy

class CurrentUserReportCompany(serializers.CurrentUserDefault):
    def __call__(self, serializer_field):
        user = super().__call__(serializer_field)
        return user.company.reporting


class ReportSerializer(serializers.ModelSerializer):
    company = serializers.HiddenField(default=CurrentUserReportCompany())
    data_types = serializers.SerializerMethodField()

    def get_data_types(self, obj):
        data_types = copy.deepcopy(dt) # As to not modify the base datatype dict since I guess that's a thing
        for data_type in data_types:
            for option in data_types[data_type]['options']:
                try:
                    del data_types[data_type]['options'][option]['orm_string']
                except KeyError:
                    pass
                try:
                    del data_types[data_type]['options'][option]['search_function']
                except KeyError:
                    pass
        return data_types
    class Meta:
        model = Report
        fields = "__all__"
