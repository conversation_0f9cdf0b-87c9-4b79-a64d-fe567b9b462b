from django.contrib.auth import get_user_model
from django.core.management import call_command
from django.urls import reverse
from django.test import SimpleTestCase, TestCase
from vantedge.reports.models import Report, ReportCompany
from vantedge.reports.views import ReportViewSet
from rest_framework.test import APIRequestFactory, force_authenticate

User = get_user_model()


class ReportTests(TestCase):
    """
    Run command to update test fixture
    python manage.py dumpdata reports users.company > fixtures/test_report_fixture.json
    """

    fixtures = ["fixtures/test_report_fixture.json"]

    def setUp(self):
        # Create auth user for views using api request factory
        self.username = "report_tester"
        self.password = "K1l0watt5"
        self.user = User.objects.create_superuser(
            self.username, "<EMAIL>", self.password
        )
        self.viewset = ReportViewSet

    def test_report_samples(self):
        factory = APIRequestFactory()
        report_view = self.viewset.as_view({"get": "retrieve"})
        companies = ReportCompany.objects.all()
        for report_company in companies:
            self.user.company = report_company.company
            self.user.save()
            for report in Report.objects.filter(company=report_company):
                request = factory.get(
                    reverse("report-sample", kwargs={"pk": report.pk})
                )
                force_authenticate(request, user=self.user)
                response = report_view(request, pk=report.pk)
                self.assertEqual(response.status_code, 200)
