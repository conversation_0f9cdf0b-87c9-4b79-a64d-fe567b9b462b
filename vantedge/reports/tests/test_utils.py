from typing import Annotated
from django.db.models import F
from pydantic import BaseModel

from vantedge.reports.utils import report_columns_from_pydantic_model
from vantedge.wheelhouse.tests.base.base_api_test_case import BaseTestCase


class SimpleModel(BaseModel):
    column_name: str = 'default name'

class DateModel(BaseModel):
    date: str = 'date'
    send_date: str = 'datetime'

class NestedModels(BaseModel):
    description: str = 'default description'
    nested_model: SimpleModel = SimpleModel(name='default name')
    nested_model_new_group: Annotated[SimpleModel, {"group": "New Group"}]

class ReportColumnsFromSimplePydanticModelTests(BaseTestCase):

    def test_with_default_parameters(self):
        columns = report_columns_from_pydantic_model(SimpleModel, "data__path")
        expected_simple_columns = {
            "SimpleModel__column_name": {
                "type": "string",
                'annotation': F("data__path__column_name"),
                "group": "Other",
                "label": "Column Name",
            }
        }
        self.assertEqual(expected_simple_columns, columns)


    def test_customizing_parameters(self):
        field_name_prefix = "custom_field_name_prefix"
        group = "custom_group_name"
        expected_simple_columns = {
            f"{field_name_prefix}__column_name": {
                "type": "string",
                'annotation': F("data__path__column_name"),
                "group": group,
                "label": "Column Name",
            }
        }
        columns = report_columns_from_pydantic_model(
            SimpleModel,
            "data__path",
            field_name_prefix=field_name_prefix,
            group=group,
        )

        self.assertEqual(expected_simple_columns, columns)

    def test_date_column__return_datetime_type(self):
        field_name_prefix = "custom_field_name_prefix"
        group = "custom_group_name"
        expected_simple_columns = {
            f"{field_name_prefix}__date": {
                "type": "date",
                'annotation': F("data__path__date"),
                "group": group,
                "label": "Date",
            },
            f"{field_name_prefix}__send_date": {
                "type": "dateTime",
                'annotation': F("data__path__send_date"),
                "group": group,
                "label": "Send Date",
            }
        }
        columns = report_columns_from_pydantic_model(
            DateModel,
            "data__path",
            field_name_prefix=field_name_prefix,
            group=group,
        )

        self.assertEqual(expected_simple_columns, columns)


class ReportColumnsFromNestedPydanticModelTests(BaseTestCase):
    def test_with_default_parameters(self):
        columns = report_columns_from_pydantic_model(NestedModels, "data__path")
        expected_nested_columns = {
            "NestedModels__description": {
                "type": "string",
                'annotation': F("data__path__description"),
                "group": "Other",
                "label": "Description",
            },
            "NestedModels__nested_model__column_name": {
                "type": "string",
                'annotation': F("data__path__nested_model__column_name"),
                "group": "Other",
                "label": "Column Name",
            },
            "NestedModels__nested_model_new_group__column_name": {
                "type": "string",
                'annotation': F("data__path__nested_model_new_group__column_name"),
                "group": "New Group",
                "label": "Column Name",
            }
        }
        self.assertEqual(expected_nested_columns, columns)
