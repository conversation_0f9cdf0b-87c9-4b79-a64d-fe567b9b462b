import enum
import inspect
from pydantic import BaseModel

from django.db.models import F, Q

def process_for_query(query, key, value):
  if query:
    return query & Q(**{ key: value })
  return Q(**{ key: value })

def process_for_query_Q(query, new_query):
  if query:
    return query & new_query
  return new_query

def process_for_query_OR(query, key, value):
  if query:
    return query | Q(**{ key: value })
  return Q(**{ key: value })

def report_columns_from_pydantic_model(
        model: type[BaseModel],
        annotation_prefix: str,
        field_name_prefix: str = None,
        group: str = 'Other',
        max_depth: int = 3
    ):
    """
    Convert pydantic schema to the report column structure.  If the field
    type is not specified in pydantic schema (eg: Enums), then the type
    is assumed to be string.

    Fields from nested pydantic models will be included, up to `max_depth`
    levels deep.

    model: a pydantic model validating data in JSON columns

    field_name_prefix: makes the field name unique within the report columns.

    annotation_prefix: provides a path to retrieve data from the model.

    group: used by the UI to organize the columns.

    A report column belongs to a group used by the UI to organize the
    columns.   A new group can be specified inside a pydantic model.
    See `pydantic_nested_model_group_name` for more information.
    """
    if max_depth <= 0:
        return {}

    if field_name_prefix is None:
        field_name_prefix = model.__name__

    new_columns = {}
    for field_name, field in model.__fields__.items():
        field_type = _pydantic_field_type(field)
        if field_type == "unknown":
            # dicts, lists, anything unknown are skipped
            continue

        if field_type == "nested_model":
            columns_from_nested_pydantic_model = report_columns_from_pydantic_model(
                field.type_,
                annotation_prefix=f'{annotation_prefix}__{field_name}',
                group=_pydantic_nested_model_group_name(model, field_name) or group,
                field_name_prefix=f"{field_name_prefix}__{field_name}",
                max_depth=max_depth-1
            )
            new_columns = {**new_columns, **columns_from_nested_pydantic_model}
        else:
            new_field_name = f"{field_name_prefix}__{field_name}"
            label = field_name.replace("_", " ").title()

            if label == "Date":
                field_type = 'date'
            elif label.endswith("Date"):
                field_type = 'dateTime'

            new_columns[new_field_name] = {
                "type": field_type,
                "label": label,
                "group": group,
                "annotation": F(f"{annotation_prefix}__{field_name}"),
            }

    return new_columns

def _pydantic_nested_model_group_name(model, field_name: str):
    """
    Get the custom group name for a nested pydantic model field.

    Note: the custom group name is an annotation on the field so the
    Pydantic model could be re-used in multiple nested fields.

    class MyDataModel(BaseModel):
        name: str = 'default name'
        measurements: Annotated[MeasurementModel, {"group": "Measurements"}]

    Fields in the MeasurementModel will be grouped under "Measurements".
    """
    try:
        return inspect.get_annotations(model)[field_name].__metadata__[0]['group']
    except:
        return None

def _pydantic_field_type(field):
    """
    Convert pydantic field type to the report column type.

    returns:
        nested_model: if the field type is a nested pydantic model
        unknown: if the field type is not recognized

        string, number, etc from `reports.data_types.data_types`
    """
    field_type = "string"
    match field.type_.__name__:
        case "str":
            field_type = "string"
        case "int":
            field_type = "number"
        case "float":
            field_type = "number"
        case "bool":
            field_type = "boolean"
        case "datetime":
            field_type = "dateTime"
        case "Literal":
            field_type = "string"
        case "dict" | "list":
            field_type = "unknown"
        case _:
            if isinstance(field.type_, enum.Enum | enum.EnumType):
                field_type = "string"
            elif issubclass(field.type_, BaseModel):
                field_type = "nested_model"
            else:
                field_type = "unknown"

    return field_type
