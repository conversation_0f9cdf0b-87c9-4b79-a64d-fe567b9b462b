import pandas as pd
from django_auto_prefetching import AutoPrefetchViewSetMixin
from django_q.tasks import async_task

from rest_framework import status
from rest_framework.decorators import action
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.viewsets import ModelViewSet
from rest_pandas import PandasSimpleView

from .queries import TripsRawData, DetentionRawData, RoundTripsRawData
from .models import Report
from .serializers import ReportSerializer
from vantedge.invitation.models.utils import (
    get_company_rail_shipment_invites,
    get_company_terminalboss_v2_ticket_invites,
)

class TestView(PandasSimpleView):
    def get_data(self, request, *args, **kwargs):
        from django.conf import settings

        return pd.read_csv(f"{str(settings.ROOT_DIR)}/data/df.csv")


class ReportViewSet(AutoPrefetchViewSetMixin, ModelViewSet):
    serializer_class = ReportSerializer
    queryset = Report.objects.all()
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        user = self.request.user
        return Report.objects.filter(company__company_id=user.company.id).order_by(
            "modified", "-name"
        )

    def retrieve(self, request, *args, **kwargs):
        obj = self.get_object()
        obj.save()
        return super().retrieve(request, args, kwargs)

    @action(detail=False, methods=["get"])
    def sources(self, request):
        filtered_source = []
        request_company = request.user.company

        if hasattr(request_company, "track_trace"):
            filtered_source += [
                {"label": "Trips", "value": "Trips"},
                {"label": "Round Trips", "value": "RoundTrips"},
                {"label": "Detention", "value": "Detention"},
                {"label": "Mileage", "value": "Mileage"},
                {"label": "Car Events", "value": "CarEvent"}
            ]

        if hasattr(request_company, "wheelhouse"):
            filtered_source += [{"label": "Loaded Rail Cars", "value": "LoadedRailCar"}]

        # check if requesting company has TBoss Infrastructure
        if hasattr(request_company, "terminalbosscompany"):
            filtered_source += [
                {"label": "TerminalBoss Tickets", "value": "TerminalBossTicket"}
            ]

        if hasattr(request_company, "terminalboss"):
            if "altagas" in request_company.name.lower():
                filtered_source += [
                    {"label": "AltaGasTBOSS Tickets", "value": "AltaGasTBOSS"}
                ]
            elif "green impact partners" in request_company.name.lower():
                filtered_source += [
                    {"label": "Green Impact Partners Tickets", "value": "GIPTBOSS"}
                ]
            elif "plains midstream" in request_company.name.lower():
                filtered_source += [
                    {"label": "PlainsTBOSS Tickets", "value": "PlainsTBOSS"}
                ]
            else:
                filtered_source += [
                    {"label": "TerminalBoss V3 Tickets", "value": "TerminalBossV3Ticket"},
                ]

        if hasattr(request_company, "invoice"):
            filtered_source += [{"label": "Invoice Items", "value": "InvoiceItem"}]

        if hasattr(request_company, "schedule"):
            filtered_source += [{"label": "Schedule", "value": "Schedule"}]

        if get_company_rail_shipment_invites(request_company).exists():
            filtered_source += [
                {
                    "label": "Rail Shipments (shared)",
                    "value": "SharedRailshipment",
                },
                {
                    "label": "Loaded Rail Cars (shared)",
                    "value": "SharedLoadedrailcar",
                },
            ]

        if get_company_terminalboss_v2_ticket_invites(request_company).exists():
            filtered_source += [
                {
                    "label": "TerminalBOSS V2 Tickets (shared)",
                    "value": "SharedTerminalbossv2ticket",
                },
            ]

        return Response(filtered_source)

    @action(detail=True, methods=["get"])
    def additional_filters(self, request, pk):
        obj = self.get_object()
        source = request.GET.get("source", obj.source)
        additional_filters = []
        if source == "Trips":
            additional_filters = TripsRawData.additional_filters
        elif source == "Detention":
            additional_filters = DetentionRawData.additional_filters
        elif source == "RoundTrips":
            additional_filters = RoundTripsRawData.additional_filters
        return Response(additional_filters)

    @action(detail=True, methods=["get"])
    def sample(self, request, pk):
        report = self.get_object()
        report.limit_rows = 10000

        try:
            result = report.get_processed_data(False).to_dict(orient="records")
        except AssertionError as e:
            return Response([str(e)], status=status.HTTP_400_BAD_REQUEST)

        return Response(result)

    @action(detail=True, methods=["put"])
    def email(self, request, pk):
        target = request.data.get("target")
        user_email = request.user.email
        report = self.get_object()
        report.limit_rows = 30000
        report.generate(target, user_email, trigger="Manual")
        return Response()

    @action(detail=True, methods=["GET"])
    def download(self, request, pk):
        report = self.get_object()
        report.limit_rows = 30000
        async_task(
            report.send_download,
            f"{report.name} (Wheelhouse Report Download)",
            None,
            [request.user.email],
            context=dict(trigger_type="Manual"),
            task_name=f"Download {report.name}",
        )
        return Response()

    @action(detail=True, methods=["post"])
    def clone(self, request, pk):
        report = self.get_object()
        report.id = None
        report.name = f"{report.name} (copy)"
        report.save()
        return Response(self.get_serializer_class()(report).data)

    @action(detail=True, methods=["get"])
    def choices(self, request, pk):
        report = self.get_object()
        report.limit_rows = 10000
        limit = int(request.GET.get("limit", 200))
        column = request.GET.get("column")
        input_value = str(request.GET.get("input", "")).strip()

        df: pd.DataFrame = report.get_raw_data(fields=[column])

        if column not in df.columns:
            return Response({"error": "Column not found"}, status=400)

        options = df[column].dropna().astype(str).unique()

        # filter the options based on the input
        if input_value:
            options = [choice for choice in options if input_value in str(choice)]

        return Response(sorted(options)[:limit])
