# Generated by Django 4.2 on 2024-03-21 16:04

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('wheelhouse', '0084_remove_location_booking_needs_volume_and_more'),
        ('schedule', '0008_remove_schedulefeature_feature_and_more'),
    ]

    operations = [
        migrations.RenameField(
            model_name='booking',
            old_name='description',
            new_name='description_internal',
        ),
        migrations.AddField(
            model_name='booking',
            name='description_public',
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='booking',
            name='producer',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, to='wheelhouse.party'),
        ),
        migrations.AddField(
            model_name='booking',
            name='product',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, to='wheelhouse.product'),
        ),
        migrations.AlterField(
            model_name='booking',
            name='user',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, to=settings.AUTH_USER_MODEL),
        ),
    ]
