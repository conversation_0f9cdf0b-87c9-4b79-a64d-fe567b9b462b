from django.contrib.auth.models import Permission
from django.contrib.postgres.constraints import ExclusionConstraint

from django.db import models, transaction
from django.db.models import Prefetch, Q, Func, Count
from django.db.models.functions import Extract
from django.utils import timezone

from django.contrib.postgres.fields import (
    DateTimeRangeField,
    RangeBoundary,
    RangeOperators,
)

from pytz import UTC
from datetime import datetime, timedelta
from django.db.models.signals import m2m_changed
from model_utils.models import TimeStampedModel

from schema_field.fields import JSONSchemedField
from vantedge.users.models import User, Company
from vantedge.users.models.application_configuration import company_permissions_handler
from vantedge.wheelhouse.models import Location, Product, Party
from vantedge.util.token.composer import generate_booking_token
from vantedge.util.mixins import EasyAuditModelConfigMixin
from vantedge.workflow.models.mixin import WorkflowMixin
from vantedge.notification.templates import (
    template_booking,
    template_booking_canceled,
    send_note,
)

from .schemas import BookingOwner, LockData, SlotData, BookingData, SlotTemplateData
from .utils import calc_cancelation_score_earned

class TsTzRange(Func):
    function = "TSTZRANGE"
    output_field = DateTimeRangeField()


class ScheduleCompany(EasyAuditModelConfigMixin, models.Model):
    company = models.OneToOneField(
        Company, related_name="schedule", on_delete=models.CASCADE
    )

    is_schedule_owner = models.BooleanField(default=False)
    related_companies = models.ManyToManyField("self")

    available_permissions = models.ManyToManyField(
        Permission, blank=True, limit_choices_to={"content_type__app_label": "schedule"}
    )

    def __str__(self) -> str:
        return f"{self.company}"

    @classmethod
    def user_accessible_companies(cls, user):
        user_schdule_company_qs = ScheduleCompany.objects.filter(company=user.company)
        if user_schdule_company_qs:
            user_schdule_company = user_schdule_company_qs.first()
            if user_schdule_company.is_schedule_owner:
                return (
                    user_schdule_company_qs
                    | user_schdule_company.related_companies.all()
                ).distinct()

        return user_schdule_company_qs

    class Meta:
        verbose_name_plural = "Schedule Companies"


m2m_changed.connect(
    company_permissions_handler, sender=ScheduleCompany.available_permissions.through
)



class Slot(TimeStampedModel):
    class Status(models.TextChoices):
        UNASSIGNED = "Unassigned", "Unassigned"
        ASSIGNED = "Assigned", "Assigned"
        BLOCKED = "Blocked", "Blocked"

    name = models.CharField(max_length=100, default=None, blank=True, null=True)
    location = models.ForeignKey(
        Location, related_name="slots", on_delete=models.PROTECT
    )
    start_date = models.DateTimeField("start")
    end_date = models.DateTimeField("end")
    status = models.CharField(
        choices=Status.choices, max_length=50, default=Status.UNASSIGNED
    )
    data = JSONSchemedField(schema=SlotData, default=SlotData, blank=True, null=True)

    class Meta:
        verbose_name_plural = "Slots"
        ordering = ["start_date"]
        constraints = [
            ExclusionConstraint(
                name="exclude_overlapping_slots",
                expressions=(
                    (
                        TsTzRange("start_date", "end_date", RangeBoundary()),
                        RangeOperators.OVERLAPS,
                    ),
                    ("location", RangeOperators.EQUAL),
                ),
            )
        ]

    def __str__(self):
        return f"{self.location}: {self.start_date.astimezone(timezone.get_current_timezone()).strftime('%Y-%m-%d %H:%M')}->{self.end_date.astimezone(timezone.get_current_timezone()).strftime('%Y-%m-%d %H:%M')}"

    @classmethod
    def get_location_slots(cls, user, company, location, date):
        # for slot owner company
        if location.company.company == company.company:
            bookings = Booking.objects.all()
        # for slot booking companies
        else:
            bookings = Booking.objects.filter(company=company)

        if user.is_superuser or user.is_carrier_dispatch or user.is_site_administrator:
            pass

        else:
            bookings = bookings.filter(user=user)

        bookings = bookings.select_related().order_by("id")
        prefetch = Prefetch("bookings", queryset=bookings)
        qs = location.slots.prefetch_related(prefetch).all()

        if date:
            qs = qs.filter(start_date__date=date)
        return qs

    @classmethod
    def get_first_available_period(
        cls, location, start_date: datetime, end_date: datetime, period: timedelta
    ):
        pass

    @classmethod
    def create_slots(
        cls, location, start_date: datetime, end_date: datetime, period: timedelta
    ):

        slots = []

        start_date = start_date.astimezone(UTC)
        end_date = end_date.astimezone(UTC)
        assert start_date < end_date, "start date is not smaller than end date"

        if (end_date - start_date) / period > 500:
            raise Exception("Too many slots to create!")

        this_end_date = start_date + period
        while this_end_date <= end_date:

            if cls.objects.filter(
                location=location, start_date__lt=this_end_date, end_date__gt=start_date
            ).exists():
                pass
            else:
                slot = cls(
                    location=location, start_date=start_date, end_date=this_end_date
                )
                slot.save()
                slots.append(slot)
            start_date = this_end_date
            this_end_date = start_date + period

        return slots

    @classmethod
    def alter_slots_based_on_template(cls, location, template, start_date, end_date):
        modified_slots = dict(create=0, delete=0, block=0, unblock=0)
        start_date = start_date.astimezone(UTC)
        end_date = end_date.astimezone(UTC)

        assert start_date < end_date, "start date is not smaller than end date"

        slots_per_week = template.count_slots()

        if ((end_date - start_date).days / 7) * slots_per_week > 700:
            raise Exception("Too many slots to create!")

        for slot_start_time, slot_end_time, action in template.get_date_slots(
            start_date, end_date
        ):
            if action == "create":
                if cls.objects.filter(
                    location=location,
                    start_date__lt=slot_end_time,
                    end_date__gt=slot_start_time,
                ).exists():
                    pass
                else:
                    slot = cls(
                        location=location,
                        start_date=slot_start_time,
                        end_date=slot_end_time,
                    )
                    slot.save()
                    modified_slots["create"] += 1

            elif action == "delete":
                affected_slots = cls.delete_slots(
                    location, slot_start_time, slot_end_time
                )
                modified_slots["delete"] += affected_slots

            elif action in ["block", "unblock"]:
                slots = cls.objects.filter(
                    location=location,
                    start_date__lt=slot_end_time,
                    end_date__gt=slot_start_time,
                )
                if not slots:
                    slot = cls(
                        location=location,
                        start_date=slot_start_time,
                        end_date=slot_end_time,
                    )
                    slot.save()
                    slots = [slot]

                if action == "block":
                    for slot in slots:
                        slot.set_status_block("template applied", None)
                if action == "unblock":
                    for slot in slots:
                        slot.set_status_unblock()

        return []

    @classmethod
    def delete_slots(cls, location, start_date: datetime, end_date: datetime):
        result = cls.objects.filter(
            location=location,
            status__in=[cls.Status.UNASSIGNED, cls.Status.BLOCKED],
            start_date__gte=start_date,
            end_date__lte=end_date,
        ).delete()
        return result[0]

    @classmethod
    def delete_slots_id(cls, location, ids):
        result = cls.objects.filter(
            location=location, status=cls.Status.UNASSIGNED, id__in=ids
        ).delete()
        return result[0]

    def set_status(self, status: Status):
        assert status in self.Status, "status is not defined"
        self.status = status
        self.save()

    def set_status_block(self, reason, user):
        if self.status == self.Status.ASSIGNED:
            booking = self.bookings.get(status=Booking.Status.BOOKED)
            booking.cancel_booking("Canceling booking to Blocking the slot", user, internal=True)
            self.refresh_from_db()

        if self.status == self.Status.UNASSIGNED:
            lock_data = LockData(lockout_date=timezone.now(), lockout_reason=reason)
            self.data.lock_data.append(lock_data)
            self.set_status(self.Status.BLOCKED)

    def set_status_unblock(self):
        if self.status == self.Status.BLOCKED:
            lock_data = self.data.lock_data[-1]
            lock_data.unlock_date = timezone.now()
            self.set_status(self.Status.UNASSIGNED)

    # @classmethod
    # def set_qs_status_block(cls, qs, reason):
    #     return qs.filter(status=cls.Status.UNASSIGNED).update(status=cls.Status.BLOCKED)

    # @classmethod
    # def set_qs_status_unblock(cls, qs):
    #     return qs.filter(status=cls.Status.BLOCKED).update(status=cls.Status.UNASSIGNED)

    @classmethod
    def statics(cls, location, year, month):
        slot_status_count = (
            Slot.objects.filter(
                location=location, start_date__year=year, start_date__month=month
            )
            .values("status")
            .annotate(slot_date=Extract("start_date", "day"))
            .annotate(count=Count("*"))
            .order_by("status")
        )

        booking_status_count = (
            Booking.objects.filter(
                slot__location=location,
                slot__start_date__year=year,
                slot__start_date__month=month,
            )
            .values("status")
            .annotate(slot_date=Extract("slot__start_date", "day"))
            .annotate(count=Count("*"))
            .order_by("status")
        )

        return dict(
            slot_status_count=slot_status_count,
            booking_status_count=booking_status_count,
        )


class Booking(WorkflowMixin, TimeStampedModel):
    class Status(models.TextChoices):
        BOOKED = "Booked", "Booked"
        IN_PROGRESS = "In Progress", "In Progress"
        USED = "Used", "Used"
        CANCELED = "Canceled", "Canceled"
        MISSED = "Missed", "Missed"

    slot = models.ForeignKey(Slot, related_name="bookings", on_delete=models.CASCADE)
    user = models.ForeignKey(User, blank=True, null=True, on_delete=models.PROTECT)
    company = models.ForeignKey(ScheduleCompany, on_delete=models.CASCADE)
    product = models.ForeignKey(Product, on_delete=models.PROTECT, null=True, blank=True)
    producer = models.ForeignKey(Party, on_delete=models.PROTECT, null=True, blank=True)
    data = JSONSchemedField(schema=BookingData, default=BookingData)
    status = models.CharField(
        choices=Status.choices, max_length=50, default=Status.BOOKED
    )
    description_public = models.CharField(max_length=255, blank=True, null=True)
    description_internal = models.CharField(max_length=255, blank=True, null=True)

    class Meta:
        constraints = [
            models.UniqueConstraint(
                fields=["slot", "status"],
                condition=Q(status="Booked"),
                name="one_booked_slot_constraint",
            )
        ]

    def __str__(self) -> str:
        return f"{self.company} : {self.status} | {self.slot} "

    @classmethod
    def private_fields(cls):
        return ["description_internal"]

    @classmethod
    def readonly_fields(cls):
        return ["description_public"]

    @classmethod
    def get_user_last_booking(cls, user, location=None, status=[], order_by="-created"):
        qs = cls.objects.filter(user=user)
        if location:
            qs = qs.filter(slot__location=location)
        if status:
            qs = qs.filter(status__in=status)

        return qs.order_by(order_by).first()

    @classmethod
    def get_user_last_cancellation(cls, user, location):
        # not applying location for now as score system is applied to all locations
        return Booking.get_user_last_booking(user, status=[cls.Status.MISSED, cls.Status.CANCELED], order_by="-data__canceled_at")

    @classmethod
    def create_booking(
        cls, slot, user, company, product, producer, description_public, description_internal, data):

        truck = data.get("truck")
        truck_volume = data.get("truck_volume")
        lsd = data.get("lsd")
        density = data.get("density")
        action = data.get("action")
        created_by = data.get("created_by")

        with transaction.atomic():
            slot = Slot.objects.select_for_update().get(id=slot.id)

            assert slot.status == Slot.Status.UNASSIGNED, "Slot is not open any more"

            slot.status = Slot.Status.ASSIGNED
            slot.save()
            now = timezone.now()

            data = BookingData(
                owners=[
                    BookingOwner(
                        created_by=created_by.id,
                        created_at=now,
                        claimed_by=user.id if user else None,
                        claimed_at=now if user else None,
                    )
                ],
                truck=truck,
                truck_volume=truck_volume,
                lsd=lsd,
                density=density,
                action=action
            )

            booking = cls(
                slot=slot,
                user=user,
                company=company,
                product=product,
                producer=producer,
                data=data,
            )

            if created_by.company == slot.location.company.company:
                booking.description_public=description_public
                booking.description_internal=description_internal

            booking.save()

            template_booking(slot=booking.slot, user=booking.user, sender=created_by)
            return booking

    def update_booking(self, user, product, producer, description_public, description_internal,  data):
        updated_by = data.get("updated_by")

        with transaction.atomic():
            booking = Booking.objects.select_for_update().get(id=self.id)
            old_user = booking.user
            assert (
                booking.status not in [Booking.Status.USED, Booking.Status.IN_PROGRESS]
            ), "Cannot update an in-progress or used booking"

            is_booking_for_new_user = user != old_user

            if is_booking_for_new_user:
                template_booking_canceled(booking, updated_by, score=None)

                owner_data = booking.data.owners[0]
                owner_data.removed_by = updated_by.id
                owner_data.removed_at = now = timezone.now()

                new_owner_data = BookingOwner(
                    created_by=updated_by.id,
                    created_at=now,
                    claimed_by=user.id if user else None,
                    claimed_at=now if user else None,
                )
                booking.data.owners.insert(0, new_owner_data)
                booking.user = user

            booking.product = product
            booking.producer = producer
            booking.data = data

            if booking.slot.location.company.company == updated_by.company:
                booking.description_public = description_public
                booking.description_internal = description_internal

            if booking.slot.location.generate_token:
                token = generate_booking_token(booking)
                booking.data.token = token

            booking.save()

            if is_booking_for_new_user:
                template_booking(slot=booking.slot, user=booking.user, sender=updated_by)

        self.refresh_from_db()

    def cancel_booking(self, canceled_reason, canceled_by, internal=False):
        score = None
        with transaction.atomic():
            booking = Booking.objects.select_for_update().get(id=self.id)
            slot = Slot.objects.select_for_update().get(id=booking.slot.id)

            if not internal:
                score = calc_cancelation_score_earned(booking)
                if score:
                    booking.user.add_score(score)

            assert (
                booking.status == Booking.Status.BOOKED
            ), "Can not cancel an active or used booking"
            if booking.status == Booking.Status.CANCELED:
                raise Exception("Booking already canceled")

            slot.status = Slot.Status.UNASSIGNED
            slot.save()
            booking.status = self.Status.CANCELED
            booking.data.canceled_by = canceled_by.id
            booking.data.canceled_at = timezone.now()
            booking.data.canceled_reason = canceled_reason
            booking.save()

            template_booking_canceled(booking, sender=canceled_by, score=score)

        self.refresh_from_db()

    def remove_user(self, removed_by):
        assert self.status != Booking.Status.CANCELED, "Can not update canceled booking"

        if not self.user:
            return

        self.user = None
        self.data.owners[-1].removed_by = removed_by
        self.data.owner[-1].removed_at = timezone.now()
        self.save()
        self.refresh_from_db()

    def assign_user(self, user, created_by):
        assert self.user is None, "Booking already has user"
        assert self.status != Booking.Status.CANCELED, "Can not update canceled booking"

        now = timezone.now()
        self.user = user
        self.data.owners.append(
            BookingOwner(
                created_by=created_by.id,
                created_at=now,
                claimed_by=user.id,
                claimed_at=now,
            )
        )
        self.save()
        self.refresh_from_db()

    def set_data(self, **kwargs):
        with transaction.atomic():
            booking = Booking.objects.select_for_update().get(id=self.id)
            in_progress_status_fields = [
                "check_in_at",
                "loading_start",
                "loading_end",
                "summoned_loading_at",
                "accepted_at",
            ]
            closed_status_fields = ["check_out_at"]

            for field, value in kwargs.items():
                if field in in_progress_status_fields + closed_status_fields:
                    setattr(booking.data, field, value)

            booking.status = booking.Event.BOOKED
            if any(
                [getattr(booking.data, field) for field in in_progress_status_fields]
            ):
                booking.status = booking.Event.IN_PROGRESS

            if any([getattr(booking.data, field) for field in closed_status_fields]):
                booking.status = booking.Event.USED
                if booking.user.score < 0:
                    booking.user.reset_score()
                    send_note(
                        location=booking.slot.location,
                        company = booking.company,
                        users = [booking.user],
                        title = "Your negative score cleared",
                        message = "You successfully completed a load, Your negative score is cleared",
                        distribute_via_sms = True,
                        distribute_via_email = True,
                    )

            booking.save(update_fields=["status", "data"])
            self.refresh_from_db()
            return booking

    def is_ok_set_data(self, user, data):
        slot = self.slot
        if time_window := slot.location.force_booking_data_time_window:
            if checkin := data.get("check_in_at"):
                past_time = int((checkin - slot.start_date).total_seconds() / 60)
                if abs(past_time) > time_window:
                    if past_time > 0:
                        error = "Allowed 'Check In' time window is passed"
                    else:
                        error = f"'Check In' {time_window} minutes before slot time"

                    if user.is_driver:
                        return False, error

        return True, None

    # workflow static constraint check
    def is_ok_static_constraint(self, context):
        if "check_in_at" in context["action_args"]:
            return self.is_ok_set_data(context["user"], {"check_in_at": timezone.now()})

        return True, None

    # workflow action
    def set_action_data(self, context):
        app_config = getattr(context["user"].company, "applicationconfiguration", None)
        location_company = self.slot.location.company

        if (
            location_company != context["user"].company.wheelhouse
            or not context["user"].is_site_administrator
        ) and "summoned_loading_at" in context["action_args"]:
            raise Exception("You do not have permission to update")

        if not self.user == context["user"]:
            if (
                context["user"].company.wheelhouse == location_company
                and (
                    context["user"].is_site_administrator
                    or context["user"].is_carrier_dispatch
                )
            ) or (
                context["user"].company.schedule == self.company
                and app_config
                and app_config.schedule_dispatchers_set_booking_activity
                and context["user"].is_carrier_dispatch
            ):
                pass
            else:
                raise Exception("You do not have permission to update")

        value = None if context.get("reverse", False) else timezone.now()

        for field in context["action_args"]:
            self.set_data(**{field: value})

            if field == "summoned_loading_at":
                context["notification_users"] = [self.user]
                context["notification_context"][
                    "booking_url"
                ] = self.get_app_booking_url(context)
            else:
                context["notification_context"] = dict(
                    name=self.user.name,
                    username=self.user.username,
                    user_telephone=self.user.telephone,
                )

        return self

    def update_context(self, context):
        context = super().update_context(context)
        context["start_time"] = (
            abs((self.slot.start_date - timezone.now()).total_seconds()) / 60
        )
        context["location"] = self.slot.location.id
        context["notification_location"] = self.slot.location
        context["notification_company"] = None
        context["notification_roles"] = []
        context["notification_users"] = []
        context["notification_context"] = {}
        context["notification_additional_user_ids"] = []
        return context

    def get_app_booking_url(self, context):
        result = "{schema}://app.{domain}/#/{booking_state}/?bookingId={booking_id}"

        params = dict(
            schema=context["SCHEMA"],
            domain=context["HTTP_HOST"],
            booking_state="active"
            if self.status in [Booking.Status.BOOKED, Booking.Status.IN_PROGRESS]
            else "past",
            booking_id=self.id,
        )
        return result.format(**params)


class SlotTemplate(EasyAuditModelConfigMixin, models.Model):
    name = models.CharField(max_length=100)
    company = models.ForeignKey(
        ScheduleCompany, related_name="slots", on_delete=models.PROTECT
    )
    data = JSONSchemedField(schema=SlotTemplateData, default=SlotTemplateData)


    def __str__(self) -> str:
        return self.name

    class Meta:
        ordering = ["id"]
        unique_together = ("name", "company")

    def get_date_slots(self, start_time, end_time):
        start_time = timezone.localtime(start_time).replace(
            hour=0, minute=0, second=0, microsecond=0
        )
        end_time = timezone.localtime(end_time).replace(
            hour=0, minute=0, second=0, microsecond=0
        )
        day = start_time.weekday()
        n = 0

        # go to first day with data
        while n < 8:
            if not self.data.get_day_data(day).periods:
                n += 1
                start_time = start_time + timedelta(hours=24)
                day = start_time.weekday()
            else:
                break
        # if all days are empty
        if n > 6:
            return

        while True:
            if start_time >= end_time:
                return
            day = start_time.weekday()
            for slot in self.data.get_day_data(day).periods:
                start_time = start_time.replace(
                    hour=slot.start.hour, minute=slot.start.minute
                )

                if slot.end.hour == 0 and slot.end.minute == 0:
                    slot_end_time = start_time.replace(
                        hour=slot.end.hour, minute=slot.end.minute
                    ) + timedelta(hours=24)
                else:
                    slot_end_time = start_time.replace(
                        hour=slot.end.hour, minute=slot.end.minute
                    )

                if start_time >= slot_end_time:
                    assert False, "start time is smaller that slot end time"

                yield (start_time, slot_end_time, slot.action)
            start_time = start_time.replace(
                hour=0, minute=0, second=0, microsecond=0
            ) + timedelta(hours=24)

    def count_slots(self):
        return self.data.count_slots()

    def apply_template(self, location, start_date, end_date):
        try:
            Slot.alter_slots_based_on_template(location, self, start_date, end_date)
            print(f"template {self.id} {self.name} applied")
        except Exception as e:
            print(f"Apply template falied, id:{self.id} name:{self.name}, reason:{str(e)}")
