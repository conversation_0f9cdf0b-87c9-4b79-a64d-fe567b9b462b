from django.utils import timezone
from django_filters import FilterSet, DateT<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, BooleanFilter

from ..models import Booking, Slot


class BookingFilter(FilterSet):
    active = BooleanFilter(method="get_active_bookings")
    past = BooleanFilter(method="get_past_bookings")

    def get_active_bookings(self, queryset, name, value):
        return queryset.filter(
            slot__status=Slot.Status.ASSIGNED,
            status__in=[Booking.Status.BOOKED, Booking.Status.IN_PROGRESS],
        ).order_by("-slot__start_date")

    def get_past_bookings(self, queryset, name, value):
        return (
            queryset.filter(slot__status=Slot.Status.ASSIGNED)
            .exclude(status__in=[Booking.Status.BOOKED, Booking.Status.IN_PROGRESS])
            .order_by("-slot__start_date")
        )

    class Meta:
        model = Booking

        fields = {
            "slot__location": ["exact"],
            "slot__start_date": ["lt", "gt"],
            "slot__end_date": ["lt", "gt"],
            "status": ["icontains"],
        }

