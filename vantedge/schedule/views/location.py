from rest_framework import viewsets
from rest_framework.permissions import IsAuthenticated
from rest_framework import status
from rest_framework.mixins import RetrieveModelMixin, UpdateModelMixin, ListModelMixin
from rest_framework.response import Response
from rest_framework.decorators import action

from vantedge.util.token.composer import generate_time_token
from vantedge.wheelhouse.models import Location
from .serializers.location import LocationSerializer


class ScheduleLocationViewSet(
    viewsets.GenericViewSet, RetrieveModelMixin, UpdateModelMixin, ListModelMixin
):
    serializer_class = LocationSerializer
    queryset = Location.objects.all()
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        if not hasattr(self.request.user.company, "wheelhouse"):
            return Location.objects.none()

        return Location.company_shared_schedule_locations(
            self.request.user.company.wheelhouse
        )

    def partial_update(self, request, *args, **kwargs):
        is_readonly_access = (
            not request.user.is_superuser
            and not request.user.is_staff
            and request.user.has_perm("schedule.location_read_only_access")
        )

        if (
            is_readonly_access
            or not self.request.user.is_site_administrator
            or self.request.user.company != self.get_object().company.company
        ):
            return Response(status=status.HTTP_403_FORBIDDEN)
        return super().partial_update(request, *args, **kwargs)

    @action(methods=["POST"], detail=False)
    def generate_token(self, request):
        location_id = request.data.get("location_id", None)

        if not Location.objects.filter(
            company=request.user.company.wheelhouse, pk=location_id
        ).first():
            return Response(status=status.HTTP_401_UNAUTHORIZED)

        location = Location.objects.get(id=location_id)
        token = generate_time_token(location_key=location.location_key)
        return Response({"token": token})

