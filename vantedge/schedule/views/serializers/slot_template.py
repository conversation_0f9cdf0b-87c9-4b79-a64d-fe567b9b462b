from rest_framework.serializers import ModelSerializer, ValidationError

from ...models import SlotTemplate
from ...schemas import SlotTemplateData

class SlotTemplateSerializer(ModelSerializer):
    def validate(self, attrs):
        if not self.instance:
            if name := attrs.get("name", None):
                if SlotTemplate.objects.filter(name=name, company=self.context["request"].user.company.schedule):
                    raise ValidationError("Same Template name exists!")

        if attrs.get("data"):
            try:
                for day_data in attrs["data"]["week"]:
                    for period in day_data.get("periods"):
                        if period.get("end") in ["24:00", "24:00:00"]:
                            period["end"] = "00:00:00"

                data = SlotTemplateData(**attrs["data"])
                data.sort_week_data()
                attrs["data"] = data
            except Exception as e:
                print(str(e))
                raise ValidationError("Time table data is not acceptable")

        return super().validate(attrs)

    def save(self, **kwargs):
        kwargs["company"] = self.context["request"].user.company.schedule
        return super().save(**kwargs)

    def to_representation(self, instance):
        template_data = super().to_representation(instance)

        for day_data in template_data["data"].week:
            for period in day_data.periods:
                if str(period.end) in ["00:00:00", "00:00"]:
                    period.end = "24:00:00"
        return template_data

    class Meta:
        model = SlotTemplate
        fields = "__all__"
        read_only_fields = ("company",)
