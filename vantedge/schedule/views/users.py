from django.db.models import Prefetch

from rest_framework import viewsets
from rest_framework.permissions import IsAuthenticated
from rest_framework import mixins
from vantedge.schedule.models import ScheduleCompany

from vantedge.users.models import User, Company
from .serializers.users import UserSerializer, CompanySerializer


class UserViewSet(viewsets.GenericViewSet, mixins.ListModelMixin):
    serializer_class = UserSerializer
    queryset = User.objects.filter(is_active=True)
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        qs = super().get_queryset()
        qs = qs.filter(company__schdule__in=ScheduleCompany.objects.all())
        user = self.request.user

        if not user.company.schedule:
            return

        if user.is_carrier_dispatch:
            return qs.filter(roles__contains=User.Role.DRIVER)

        if user.is_driver:
            return qs.filter(id=user.id)


class ScheduleCompanyViewSet(viewsets.GenericViewSet, mixins.ListModelMixin):
    serializer_class = CompanySerializer
    queryset = Company.objects.all()
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        qs = super().get_queryset()
        qs = qs.filter(
            schedule__in=ScheduleCompany.user_accessible_companies(self.request.user)
        )

        user = self.request.user
        user_set = User.objects.none()

        if user.is_carrier_dispatch or user.is_site_administrator:
            user_set = User.objects.filter(
                roles__contains=[User.Role.DRIVER], is_active=True
            )

        elif user.is_driver:
            qs = qs.filter(schedule__company=user.company)
            user_set = User.objects.filter(id=user.id)

        else:
            qs = qs.none()

        prefetch = Prefetch("user_set", queryset=user_set)
        qs = qs.prefetch_related(prefetch).all()

        return qs
