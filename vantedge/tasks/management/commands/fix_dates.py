from django.core.management.base import BaseCommand, CommandError
from django.conf import settings
from vantedge.users.models import User
from vantedge.trackandtrace.models import ClmEventCode, CarEvent, Trip
from vantedge.edi.models import EdiDocument
import csv
import pandas as pd


class Command(BaseCommand):
    help = "Fix Dates"

    def handle(self, *args, **options):
        Trip.objects.all().delete()
        EdiDocument.objects.all().delete()
        CarEvent.objects.all().delete()
