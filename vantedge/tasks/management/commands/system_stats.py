import shutil

from django.core.management.base import BaseCommand

from util.logger.base import send_error, log_info

byte = lambda x: x
byte_to_megabyte = lambda x: round(x / (1024 * 1024), ndigits=2)
byte_to_gigabyte = lambda x: round(byte_to_megabyte(x) / 1024, ndigits=2)


def get_disk_report(unit: str = "b"):
    data = {"total": None, "used": None, "free": None}
    s = shutil.disk_usage(".")

    if unit == "b":
        converter = byte
    elif unit == "m":
        converter = byte_to_megabyte
    elif unit == "g":
        converter = byte_to_gigabyte
    else:
        return data

    data["total"] = converter(s.total)
    data["used"] = converter(s.used)
    data["free"] = converter(s.free)

    return data


def report_disk_space(threshold_diskspace=2):
    disk_report = get_disk_report(unit="g")
    used_space = disk_report["used"]
    free_space = disk_report["free"]

    if free_space <= threshold_diskspace:
        send_error("Disk space report, free: %s, used: %s", free_space, used_space)
    else:
        log_info("Disk space report, free: %s, used: %s", free_space, used_space)


class Command(BaseCommand):
    help = "System Stats"

    def add_arguments(self, parser):

        parser.add_argument(
            "-threshold-diskspace",
            type=int,
            nargs="?",
            help="If threshold(GB) value is smaller than than disk space then send alert",
            default=2,
        )

    def handle(self, *args, **options):
        report_disk_space(options["threshold_diskspace"])
