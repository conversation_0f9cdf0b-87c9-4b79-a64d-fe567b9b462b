from django.db import models
from django.contrib import admin
from django.utils.translation import gettext_lazy as _
from django_json_widget.widgets import JSONEditorWidget
from vant_dbreplication.admin import DBReplicationAdminMixin


class TerminalBossReplicationAdminMixin(DBReplicationAdminMixin):

    formfield_overrides = {
        models.JSONField: {"widget": JSONEditorWidget(options={"mode": "code"})}
    }

    # client_company_fieldsets = (
    #     (_("Client Company"), {"fields": ("dbreplication_client_company",)}),
    # )

    dbreplication_readonly_fields = (
        "dbreplication_state",
        "created",
        "modified",
        "server_subscription_status",
        "dbreplication_config",
    )
    dbreplication_list_display = (
        "dbreplication_client_company",
        "created_by_server",
        "modified_by_server",
        "dbreplication_state",
    )
    dbreplication_editable_fields = (
        "additional_data",
        "dbreplication_client_company",
        "created_by_server",
        "modified_by_server",
    )

    required_fields = (
        "dbreplication_client_company",
        "created_by_server",
        "modified_by_server",
    )

    def get_form(self, request, obj=None, **kwargs):
        form = super().get_form(request, obj, **kwargs)
        for field in self.required_fields:
            if field in form.base_fields:
                form.base_fields[field].required = True
        return form

    # def get_fieldsets(self, request, obj):
    #     fieldsets = super().get_fieldsets(request, obj)
    #     fieldsets = self.client_company_fieldsets + tuple(fieldsets)
    #     return fieldsets

    def get_readonly_fields(self, request, obj):
        readonly_fields = super().get_readonly_fields(request, obj)
        if obj:
            readonly_fields += ("created_by_server", "dbreplication_client_company")
        else:
            readonly_fields += ("modified_by_server",)
        return readonly_fields

class AssociationAdminMixin(admin.ModelAdmin):
    formfield_overrides = {
        models.JSONField: {"widget": JSONEditorWidget(options={"mode": "code"})}
    }
    search_fields = [ "entity__name", "server__name" ]
    list_display = ['entity', 'server', "created", "modified" ]
    list_filter = [ "server", "server__dbreplication_client_company"]
