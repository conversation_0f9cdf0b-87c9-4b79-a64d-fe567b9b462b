from django.apps import AppConfig
from vant_dbreplication.signals.notification_signal import merge_conflict_signal, replication_error_signal

def merge_conflict_notification(sender, **kwargs):
    from vantedge.util.notifications.email import send_merge_conflict_notification
    from vantedge.users.models import User
    instance = kwargs.get("instance")
    resolved = kwargs.get("resolved", False)
    print(f"merge conflict signal {instance}, resolved: {resolved}")
    target_company = instance.dbreplication_client_company.client_company
    target_users = User.objects.filter(assigned_companies__in=[target_company], groups__name="Replication Notification")
    emails = target_users.values_list("email", flat=True)
    if emails:
        send_merge_conflict_notification(list(set(emails)), instance, resolved)

def replication_error_notification(sender, **kwargs):
    pass

class TerminalBossConfig(AppConfig):
    name = "vantedge.tboss"
    verbose_name = "TerminalBoss V3"

    def ready(self):
        merge_conflict_signal.connect(merge_conflict_notification)
        replication_error_signal.connect(replication_error_notification)
        import vantedge.tboss.signals
