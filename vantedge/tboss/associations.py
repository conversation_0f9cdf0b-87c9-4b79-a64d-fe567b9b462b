from datetime import datetime
from dateutil.parser import parse
from django.conf import settings
from django.contrib.contenttypes.models import ContentType
from django.db import transaction
from django.db.models import Q
from vant_dbreplication.utils import ReplicationHandler
from vant_dbreplication.models import DBReplicationServerSubscription
from vant_dbreplication.utils.schemas import EntityState
from vantedge.tboss.utils import convert_datetimes_to_tz
from vantedge.tboss.models import ServerAssociationSchema

from vantedge.tboss.models import (
    TBossCertificationServerAssociation,
    TBossCompanyCertificationServerAssociation,
    TBossUserCertificationServerAssociation,
    TBossVehicleCertificationServerAssociation,
    TBossCompanyServerAssociation,
    TBossLocationServerAssociation,
    TBossProductServerAssociation,
    TBossUserServerAssociation,
    TBossVehicleServerAssociation,
    TBossUserAccessServerAssociation
)
from rest_framework import serializers
import inspect, pytz

replication_handler = ReplicationHandler()

class BaseAssociationSerializer(serializers.ModelSerializer):
    server_name = serializers.CharField(source="server.name", read_only=True)

    def to_internal_value(self, data):
        # convert all datetimes to server timezone
        internal_value = super().to_internal_value(data)
        server = internal_value.get("server")
        if server:
            tz = pytz.timezone(server.timezone)
            internal_value = convert_datetimes_to_tz(internal_value, tz)
        return internal_value


    def to_representation(self, instance):
        # convert all datetimes to server timezone
        # TODO convert nested json dates
        representation = dict(super().to_representation(instance))
        server = instance.server
        datetime_fmt = "%Y-%m-%dT%H:%M:%S.%f"
        if server:
            tz = pytz.timezone(server.timezone)
            for key, value in representation.items():
                if (
                    isinstance(self.fields.get(key), serializers.DateTimeField)
                    and value
                ):
                    representation[key] = (
                        parse(value).astimezone(tz).strftime(datetime_fmt)
                    )
                elif isinstance(value, dict):
                    representation[key] = convert_datetimes_to_tz(value, tz, datetime_fmt)
        return representation


def AssociationSerializer(cls_model, fields="__all__", read_only_fields=None):


    handler = ReplicationHandler()
    cls = BaseAssociationSerializer
    # factory method for creating serializers
    # https://jfine-python-classes.readthedocs.io/en/latest/type-name-bases-dict.html#type-name-bases-dict

    AssociationModel = handler.get_server_association_model(cls_model)
    if not AssociationModel:
        return serializers.ModelSerializer

    Meta_kwargs = {
        "model": AssociationModel,
        "exclude": ['entity',],
    }

    if fields != "__all__":
        Meta_kwargs.setdefault("fields", fields)
        Meta_kwargs.pop("exclude")

    if read_only_fields:
        Meta_kwargs.setdefault("read_only_fields", read_only_fields)
    Meta = type("Meta", (object,), Meta_kwargs)

    Serializer = type("AssociationSerializer", (cls,), {"Meta": Meta})
    return Serializer


class TBossV3ServerAssociationSerializerMixin(serializers.ModelSerializer):

    @transaction.atomic
    def create(self, validated_data):
        ModelClass = self.Meta.model
        association_set_field = self.get_fields().get('server_associations').source
        server_associations_set = validated_data.pop(association_set_field, [])

        instance = super().create(validated_data)

        replication_config = instance.dbreplication_config
        AssociationModel = replication_handler.get_server_association_model(ModelClass)

        if not AssociationModel or not replication_config or not replication_config.replicate_changes_using_server_associations:
            return instance

        log_entries = instance.fetch_log_entries().all()

        if not log_entries.exists():
            print('Skipped handling associations. Could not find logentry')
            return instance

        site_specific_config = instance.additional_data.get('site_specific_config') or {}

        # Iterate through all log entries on create since there could be more than one
        for log_entry in log_entries:

            entity_state = log_entry.entity_state
            RelatedModel = AssociationModel._meta.get_field("entity").related_model
            related_model_fields = {
                field.name: field for field in RelatedModel._meta.fields
            }
            server_specific_fields = replication_config.server_specific_fields
            enabled = []

            for association_data in server_associations_set:
                server = association_data.get('server')

                if server.name == 'Wheelhouse':
                    continue

                status = DBReplicationServerSubscription.Status.PENDING

                if not server.replication_flag:
                    status = DBReplicationServerSubscription.Status.APPLIED

                # mask the entity state with the server specific fields
                previous_state = association_data.get('entity_state')

                # set the entity as enabled if associated with the server
                for field in AssociationModel.active_flags():
                    previous_state['fields'][field] = True

                # if object has a field configured to be the same for all active sites
                # just use the instance field
                for field, specific in site_specific_config.items():
                    if not specific:
                        previous_state['fields'][field] = entity_state.fields[field]


                masked_data = replication_handler.mask_server_specific_fields(
                    dict(entity_state), dict(previous_state), server_specific_fields
                )

                data = ServerAssociationSchema(
                    server_specific_fields=server_specific_fields
                )
                association, created = AssociationModel.objects.update_or_create(
                    entity=instance,
                    server=server,
                    defaults={
                        "data": data,
                        "entity_state": masked_data
                    }
                )

                if not log_entry:
                    print(f'Log Entry could not be found for the instance: {instance}. Could not create server subscription for server {association.server}')
                    continue

                server_subscription, created = DBReplicationServerSubscription.objects.update_or_create(
                    dbreplication_logentry=log_entry,
                    server=association.server,
                    dbreplication_client_company=association.server.dbreplication_client_company,
                    defaults={"masked_entity_state": masked_data, "status": status}
                )
                enabled.append(server.pk)

            # populate to other servers as disabled with default server specific values
            for server in replication_config.servers.exclude(Q(pk__in=enabled)|Q(name='Wheelhouse')|Q(replication_flag=False)):
                default_server_specific_fields = {}

                status = DBReplicationServerSubscription.Status.PENDING

                if not server.replication_flag:
                    status = DBReplicationServerSubscription.Status.APPLIED

                for f in list(
                    set(
                        server_specific_fields + AssociationModel.active_flags()
                    )
                ):
                    field = related_model_fields.get(f)

                    if not field:
                        continue

                    # Set the entity to be disabled
                    if (
                        f in AssociationModel.active_flags()
                        and f in entity_state.fields
                    ):
                        default_server_specific_fields[f] = False
                    else:
                        default_server_specific_fields[f] = field.get_default()

                previous_state = EntityState(
                    fields=default_server_specific_fields
                )
                masked_data = replication_handler.mask_server_specific_fields(
                    dict(entity_state), dict(previous_state), server_specific_fields
                )
                server_subscription, created = DBReplicationServerSubscription.objects.update_or_create(
                    dbreplication_logentry=log_entry,
                    server=server,
                    dbreplication_client_company=server.dbreplication_client_company,
                    defaults={"masked_entity_state": masked_data, "status": status}
                )

        return instance

    @transaction.atomic
    def update(self, instance, validated_data):
        ModelClass = self.Meta.model
        association_set_field = self.get_fields().get('server_associations').source
        server_associations_set = validated_data.pop(association_set_field, [])
        instance = super().update(instance, validated_data)

        replication_config = instance.dbreplication_config
        server_specific_fields = replication_config.server_specific_fields
        AssociationModel = replication_handler.get_server_association_model(ModelClass)

        if not AssociationModel or not replication_config or not replication_config.replicate_changes_using_server_associations:
            return instance

        RelatedModel = AssociationModel._meta.get_field("entity").related_model
        related_model_fields = {
            field.name: field for field in RelatedModel._meta.fields
        }

        log_entries = instance.fetch_log_entries().filter(created__gte=instance.modified)

        site_specific_config = instance.additional_data.get('site_specific_config') or {}

        # Iterate through all log entries that has been created since last modified since there could be more than one
        for log_entry in log_entries:

            if not log_entry:
                print('Skipped handling associations. Could not find logentry')
                return instance

            entity_state = log_entry.entity_state

            default_server_specific_fields = {}

            for f in list(
                set(
                    server_specific_fields + AssociationModel.active_flags()
                )
            ):
                field = related_model_fields.get(f)

                if not field:
                    continue

                # Set the entity to be disabled
                if (
                    f in AssociationModel.active_flags()
                    and f in entity_state.fields
                ):
                    default_server_specific_fields[f] = False
                else:
                    default_server_specific_fields[f] = field.get_default()

            enabled = []

            for association_data in server_associations_set:
                server = association_data.get('server')

                if server.name == 'Wheelhouse':
                    continue

                status = DBReplicationServerSubscription.Status.PENDING

                if not server.replication_flag:
                    status = DBReplicationServerSubscription.Status.APPLIED

                # mask the entity state with the server specific fields
                previous_state = association_data.get('entity_state')
                for field in AssociationModel.active_flags():
                    previous_state['fields'][field] = True

                # if object has a field configured to be the same for all active sites
                # just use the instance field
                for field, specific in site_specific_config.items():
                    if not specific:
                        previous_state['fields'][field] = entity_state.fields.get(field, False)

                masked_data = replication_handler.mask_server_specific_fields(
                    dict(entity_state), dict(previous_state), server_specific_fields
                )

                data = ServerAssociationSchema(
                    server_specific_fields=server_specific_fields
                )
                association, created = AssociationModel.objects.update_or_create(
                    entity=instance,
                    server=server,
                    defaults={
                        "data": data,
                        "entity_state": masked_data
                    }
                )
                enabled.append(server)

                # create server subscriptions using the masked data
                server_subscription, created = DBReplicationServerSubscription.objects.update_or_create(
                    dbreplication_logentry=log_entry,
                    server=association.server,
                    dbreplication_client_company=association.server.dbreplication_client_company,
                    defaults={"masked_entity_state": masked_data, "status": status}
                )


            # delete the server associations that were not part of the set to mark them inactive
            # and create inactive server subscriptions
            set_disabled = []
            inactive = AssociationModel.objects.filter(entity=instance).exclude(server__in=enabled)
            for association in inactive:
                server = association.server
                set_disabled.append(server)

                if server.name == 'Wheelhouse':
                    continue

                status = DBReplicationServerSubscription.Status.PENDING

                if not server.replication_flag:
                    status = DBReplicationServerSubscription.Status.APPLIED

                previous_state = association.entity_state
                for field in AssociationModel.active_flags():
                    previous_state.fields[field] = False

                masked_data = replication_handler.mask_server_specific_fields(
                    dict(entity_state), dict(previous_state), server_specific_fields
                )

                # create server subscriptions using the masked data
                server_subscription, created = DBReplicationServerSubscription.objects.update_or_create(
                    dbreplication_logentry=log_entry,
                    server=server,
                    dbreplication_client_company=server.dbreplication_client_company,
                    defaults={"masked_entity_state": masked_data, "status": status}
                )

                # for users, vehicles, and companies
                # if the association is removed, also disable any active certifications linked to it
                certifications = []
                CertificationAssociationModel = None
                if hasattr(instance, 'usercertification_set'):
                    certifications = instance.usercertification_set.all()
                    CertificationAssociationModel = TBossUserCertificationServerAssociation
                elif hasattr(instance, 'vehiclecertification_set'):
                    certifications = instance.vehiclecertification_set.all()
                    CertificationAssociationModel = TBossVehicleCertificationServerAssociation
                elif hasattr(instance, 'companycertification_set'):
                    certifications = instance.companycertification_set.all()
                    CertificationAssociationModel = TBossCompanyCertificationServerAssociation

                # disable all the related certifications if the instance is disabled for the site
                for certification in certifications:
                    association = CertificationAssociationModel.objects.filter(entity=certification, server=server).first()
                    if association:
                        certification.save()
                        certification_log_entries = certification.fetch_log_entries().filter(created__gte=certification.modified)
                        if certification_log_entries.exists():
                            for cert_logentry in certification_log_entries:

                                cert_entity_state = cert_logentry.entity_state

                                previous_state = association.entity_state
                                for field in CertificationAssociationModel.active_flags():
                                    previous_state.fields[field] = False

                                cert_replication_config = certification.dbreplication_config

                                server_specific_fields = cert_replication_config.server_specific_fields

                                cert_masked_data = replication_handler.mask_server_specific_fields(
                                    dict(cert_entity_state), dict(previous_state), server_specific_fields
                                )

                                # create server subscriptions using the masked data
                                cert_server_subscription, cert_subscription_created = DBReplicationServerSubscription.objects.update_or_create(
                                    dbreplication_logentry=cert_logentry,
                                    server=server,
                                    dbreplication_client_company=server.dbreplication_client_company,
                                    defaults={"masked_entity_state": cert_masked_data, "status": status}
                                )
                        association.delete()

            inactive.delete()

            excluded_server_ids = [s.id for s in enabled] + [s.id for s in set_disabled]
            print(excluded_server_ids)
            # update the non associated server subscriptions to retain the masked_specific_fields
            for server in replication_config.servers.exclude(Q(name="Wheelhouse")|Q(id__in=excluded_server_ids)|Q(replication_flag=False)):
                previous_state = instance.fetch_server_subscriptions().filter(server=server, masked_entity_state__masked_server_specific_fields__associated_to_site__isnull=False).order_by('-created').first()
                if not previous_state:
                    previous_state = EntityState(fields=default_server_specific_fields)
                else:
                    previous_state = previous_state.masked_entity_state
                masked_data = replication_handler.mask_server_specific_fields(
                    dict(entity_state), dict(previous_state), server_specific_fields
                )

                status = DBReplicationServerSubscription.Status.PENDING

                if not server.replication_flag:
                    status = DBReplicationServerSubscription.Status.APPLIED

                # update server subscriptions using the masked data
                server_subscription, created = DBReplicationServerSubscription.objects.update_or_create(
                    dbreplication_logentry=log_entry,
                    server=server,
                    dbreplication_client_company=server.dbreplication_client_company,
                    defaults={"masked_entity_state": masked_data, "status": status}
                )

        return instance
