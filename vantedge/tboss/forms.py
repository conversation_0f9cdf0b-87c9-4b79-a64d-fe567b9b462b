from django.contrib.auth import forms as admin_forms
from django.utils.translation import gettext_lazy as _
from vantedge.tboss.models import TBossUser


class TBossUserChangeForm(admin_forms.UserChangeForm):
    class Meta(admin_forms.UserChangeForm.Meta):
        model = TBossUser


class TBossUserCreationForm(admin_forms.UserCreationForm):
    class Meta(admin_forms.UserCreationForm.Meta):
        model = TBossUser

        error_messages = {
            "username": {"unique": _("This username has already been taken.")}
        }
