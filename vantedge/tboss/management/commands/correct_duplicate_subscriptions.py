import logging
from progressbar import progressbar
from django.db.models import Count
from django.core.management.base import BaseCommand

from vant_dbreplication.models import DBReplicationServerSubscription, DBReplicationLogEntry


logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = "Calculate report fields"

    def add_arguments(self, parser):
        return super().add_arguments(parser)

    def handle(self, *args, **options):
        qs_duplicates = DBReplicationServerSubscription.objects.filter(
            dbreplication_logentry__status=DBReplicationLogEntry.Status.IN_PROGRESS
            ).values("dbreplication_logentry", "server").annotate(c=Count("id")).filter(c=2)

        total_count = qs_duplicates.count()
        items_processed = 0
        logger.info("total count is: %s", total_count)
        while items_processed <= total_count:
            items_processed += 1000
            updated_logentries = []

            for duplicate_item in progressbar(qs_duplicates[0:1000]):
                logentry_id = duplicate_item["dbreplication_logentry"]
                server = duplicate_item["server"]

                subscriptions = list(DBReplicationServerSubscription.objects.filter(
                    dbreplication_logentry=logentry_id,server=server))

                if len(subscriptions) != 2:
                    logger.info("more than 2 subscriptions found, logentry: %s, server: %s", logentry_id, server)
                    continue

                subscription_for_delete = None
                subscription_to_keep = None
                for subscription in subscriptions:
                    if subscription.status == DBReplicationServerSubscription.Status.PENDING.value:
                        subscription_for_delete = subscription
                    if subscription.status == DBReplicationServerSubscription.Status.APPLIED.value:
                        subscription_to_keep = subscription

                if subscription_for_delete and subscription_to_keep:
                    subscription_for_delete.delete()
                    log_entry = subscription_to_keep.dbreplication_logentry

                    if not log_entry.subscribed_logentries.exclude(
                        status=DBReplicationServerSubscription.Status.APPLIED
                        ):
                        log_entry.status = DBReplicationLogEntry.Status.MERGED
                        updated_logentries.append(log_entry)

                else:
                    logger.info("subscriptions status are not as expected,id: %s", log_entry.id)
                    continue

            DBReplicationLogEntry.objects.bulk_update(updated_logentries, fields=["status"])
