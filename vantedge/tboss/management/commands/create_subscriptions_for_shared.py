from django.core.management.base import BaseCommand, <PERSON><PERSON><PERSON>r, Command<PERSON>arser
from progressbar import progressbar
from inquirer.themes import BlueComposure
from vant_dbreplication.models import DBReplicationServer, DBReplicationLogEntry, DBReplicationServerSubscription
from vant_dbreplication.utils import Replication<PERSON>and<PERSON>
from vant_dbreplication.utils.schemas import EntityState
from vantedge.tboss import models as tbossv3
import arrow, inquirer

class Command(BaseCommand):
    help = "Create server subscriptions for a shared content type"

    def handle(self, *args, **options):
        theme = BlueComposure()
    
        company_choices = list(
            tbossv3.TerminalBossClientCompany.objects.values_list("name", flat=True)
        )
        company_list = [
            inquirer.List(
                "company_selection",
                message="Select a terminalbossv3 company",
                choices=company_choices,
            )
        ]
        company_prompt = inquirer.prompt(company_list, theme=theme)

        tboss_company = tbossv3.TerminalBossClientCompany.objects.get(
            name=company_prompt["company_selection"]
        )
        server_choices = list(
              DBReplicationServer.objects.filter(dbreplication_client_company=tboss_company).values_list("name", flat=True)
        )
        server_list = [
              inquirer.List(
                    "server_selection",
                    message="Select a tboss v3 server to create initial server subscriptions for",
                    choices=server_choices
              )
        ]
        server_prompt = inquirer.prompt(server_list, theme=theme)
        server_to_initialize = DBReplicationServer.objects.get(name=server_prompt["server_selection"], dbreplication_client_company=tboss_company)

        shared_models = list(
            filter(
                lambda config: config.replicate_changes_using_server_associations,
                server_to_initialize.replication_configs.all()
            )
        )

        handler = ReplicationHandler()

        for replication_config in shared_models:
            content_type = replication_config.content_type
            model = content_type.model_class()
            AssociationModel = handler.get_server_association_model(model)
            RelatedModel = AssociationModel._meta.get_field("entity").related_model
            related_model_fields = {
                field.name: field for field in RelatedModel._meta.fields
            }
            for server in replication_config.servers.exclude(pk=server_to_initialize.pk):
                server_subscriptions = []
                # create subscriptions from the server logs
                server_logs = DBReplicationLogEntry.objects.filter(origin_server=server)
                print(f"Detected {server_logs.count()} logs for {model} from {server}...")
                for log in progressbar(server_logs):
                    default = {}

                    for f in list(set(replication_config.server_specific_fields + AssociationModel.active_flags())):
                        field = related_model_fields.get(f)
                        if f in AssociationModel.active_flags() and f in log.entity_state.fields:
                            default[f] = False
                        else:
                            default[f] = field.get_default()

                    previous_state = EntityState(fields=default)
                    masked_data = handler.mask_server_specific_fields(dict(log.entity_state), dict(previous_state), replication_config.server_specific_fields)
                    server_subscriptions.append(
                        DBReplicationServerSubscription(
                            server=server,
                            dbreplication_logentry=log,
                            dbreplication_client_company=log.dbreplication_client_company,
                            masked_entity_state=masked_data,
                            status=DBReplicationServerSubscription.Status.INITIAL if log.initial else DBReplicationServerSubscription.Status.PENDING,
                            created=log.created,
                            modified=log.created
                        )
                    )
                print(f"Bulk creating subscriptions...")
                subscriptions = DBReplicationServerSubscription.objects.bulk_create(server_subscriptions, 2000)
                print(f"Done!")
