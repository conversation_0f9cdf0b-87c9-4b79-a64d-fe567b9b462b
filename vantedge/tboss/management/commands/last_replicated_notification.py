from constance import config
from django.core.management.base import BaseCommand, CommandError
from django.contrib.auth.models import Group
import arrow
from vant_dbreplication.models import DBReplicationWorker
from vantedge.util.notifications.email import async_send_email

class Command(BaseCommand):
    help = "Last Replicated Notification"

    def handle(self, *args, **options):
        replication_notification_group, created = Group.objects.get_or_create(name="Replication Notification")

        now = arrow.now().datetime

        workers = DBReplicationWorker.objects.filter(is_active=True)
        for worker in workers:
            server = worker.server
            tenant = server.dbreplication_client_company.client_company

            elapsed_in_minutes = 0
            if worker.last_replicated:
                elapsed_in_minutes = (now - worker.last_replicated).total_seconds() / 60
            elif worker.last_seen:
                elapsed_in_minutes = (now - worker.last_seen).total_seconds() / 60
            else:
                continue

            if elapsed_in_minutes >= config.REPLICATION_LAST_SEEN_EMAIL_THROTTLE:
                users = replication_notification_group.user_set.filter(assigned_companies__in=[tenant])
                emails = users.values_list("email", flat=True)
                if emails:
                    print(f"Sync Health Alert for site {server}. Sending email notification to: {emails}")
                    async_send_email(list(set(emails)), "TerminalBOSS Sync Health Check", "sync_health_check_notification", { "context": worker })
