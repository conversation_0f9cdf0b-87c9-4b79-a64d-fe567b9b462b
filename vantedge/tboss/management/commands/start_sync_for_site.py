from django.core.management import BaseCommand, CommandError, call_command
from django.contrib.contenttypes.models import ContentType
from vant_dbreplication import models as vant_dbreplication
from vantedge.tboss import models as tboss
from vant_tboss import models as vant_tboss
from vant_ticket import models as vant_ticket
from vant_user import models as vant_user
from vant_limit import models as vant_limit
from vant_ledger import models as vant_ledger
from vant_dbreplication.utils import Replication<PERSON>andler
from progressbar import progressbar

BATCH_SIZE = 2000

SHARED_TABLES = [
    tboss.TBossGroup,
    tboss.TBossPermission,
    vant_user.User,
    vant_user.UserAccess,
    vant_tboss.Product,
    vant_tboss.CompanyRole,
    vant_tboss.Company,
    vant_tboss.Location,
    vant_tboss.Vehicle,
    vant_tboss.GCAnalysis,
    vant_tboss.LocationProduct,
    vant_tboss.Certification,
    vant_tboss.UserCertification,
    vant_tboss.VehicleCertification,
    vant_tboss.CompanyCertification
]

SITE_SPECIFIC_TABLES = [
    vant_ticket.Ticket,
    vant_ticket.TicketEntry,
    vant_ticket.TicketProperties,
    vant_limit.Limit,
    vant_limit.LimitProperties,
    vant_limit.LimitPeriod,
    vant_tboss.Reading,
    vant_tboss.WheelhouseDevice,
    vant_tboss.Route,
    vant_ledger.AssociatedSignature
]

class Command(BaseCommand):
    help = "Ensure that server subscription logs are set to initial state for a site"

    def add_arguments(self, parser):
        parser.add_argument(
            "--server",
            required=True,
            type=str,
            help=f"Replication server / site / facility",
        )

    def handle(self, *args, **options):

        handler = ReplicationHandler()

        # get the server
        server = vant_dbreplication.DBReplicationServer.objects.get(name=options["server"])

        print(f"Ensuring server replication flag and worker is active...")
        # turn on worker and ensure server replication flag is turned on
        if not server.replication_flag:
            server.replication_flag = True
            server.save()
            print(f"Server replication flag set to active.")

        worker = server.replication_workers.first()
        if not worker:
            raise ValueError(f"No replication workers are set up for server {server}")

        if not worker.is_active:
            worker.is_active = True
            worker.save()
            print(f"Worker set to active.")

        print(f"Worker and server are active.")

        # get the tenant
        tenant = server.dbreplication_client_company

        # fetch all initial log entries and ensure that for shared objects, there is a subscription log for server
        initial_subscription_entries = vant_dbreplication.DBReplicationServerSubscription.objects.filter(server=server, dbreplication_logentry__initial=True)
        print(f"Detected {initial_subscription_entries.count()} initial subscription entries for server {server}")


        initial_subscription_entries = initial_subscription_entries.exclude(status=vant_dbreplication.DBReplicationServerSubscription.Status.INITIAL)
        print(f"Updating {initial_subscription_entries.count()} initial subscription entries for server {server}")

        initial_subscription_entries.update(status=vant_dbreplication.DBReplicationServerSubscription.Status.INITIAL)

        create_subscriptions = []
        update_subscriptions = []

        # fetch all log entries that happened after initial and ensure a subscription log for server is available
        for shared_table in SHARED_TABLES:
            content_type = ContentType.objects.get_for_model(shared_table)

            AssociationModel = handler.get_server_association_model(content_type.model_class())

            if not AssociationModel:
                print(f"No association model for content type {content_type}")
                continue

            print(f"Checking log entries for model {content_type}")

            log_entries = tenant.dbreplicationlogentry_set.filter(initial=False, content_type=content_type)
            for log in progressbar(log_entries.iterator()):
                server_subscription = log.subscribed_logentries.filter(server=server).first()

                # if subscription already exists, just set status to PENDING
                if server_subscription:
                    if server_subscription.status != vant_dbreplication.DBReplicationServerSubscription.Status.PENDING:
                        server_subscription.status = vant_dbreplication.DBReplicationServerSubscription.Status.PENDING
                        update_subscriptions.append(server_subscription)
                else:

                    try:
                        entity = log.sync_entity
                        entity_state = handler.serialize_state(entity)
                    except Exception as e:
                        # print(f"Skipping log, could not get entity from {log}.")
                        continue

                    association = AssociationModel.objects.filter(entity=entity, server=server).first()
                    if association:
                        entity_state = association.entity_state
                    else:
                        entity_state['masked_server_specific_fields']['associated_to_site'] = False

                    create_subscriptions.append(
                        vant_dbreplication.DBReplicationServerSubscription(
                            dbreplication_client_company=log.dbreplication_client_company,
                            dbreplication_logentry=log,
                            server=server,
                            masked_entity_state=entity_state,
                            created=log.created,
                            modified=log.modified
                        )
                    )

        print(f"Creating {len(create_subscriptions)} subscriptions for server {server}...")
        created_subscriptions = vant_dbreplication.DBReplicationServerSubscription.objects.bulk_create(create_subscriptions, batch_size=BATCH_SIZE)
        print(f"Updating {len(update_subscriptions)} subscriptions for server {server}...")
        updated_subscriptions = vant_dbreplication.DBReplicationServerSubscription.objects.bulk_update(update_subscriptions, ['status'], batch_size=BATCH_SIZE)
