from django.db import transaction
from django.core.management.base import BaseCommand, CommandParser
from progressbar import progressbar

from vant_ticket.models import Ticket
from vant_tboss.models.location import LocationProduct
from vant_calcs.uom import DensityChoices


class Command(BaseCommand):
    help = "Update product densities"

    def add_arguments(self, parser: CommandParser):
        parser.add_argument(
            '--update_type',
            choices=['tickets_only', 'location_products_only', 'all'],
            default='tickets_only',
            help='Specify whether to update only tickets or all products. Defaults to tickets_only.'
        )


    def handle(self, *args, **options):
        # TODO this must be run in WH with all and in TerminalBoss Gull Lake with tickets_only
        update_type = options['update_type']
        self.stdout.write("Updating ticket data densities...")
        dbreplication_client_company_id = "536a0ccd60a948d895f68603a9fd1ab5" # Plains
        created_by_server_id="196a97abd99848048c1fc9a2bbf2de67" # Gull Lake Production
        proper_density_unit = DensityChoices.kg_per_m3

        if update_type != 'location_products_only':
            updated_tickets = []
            tickets = Ticket.objects.filter(dbreplication_client_company_id=dbreplication_client_company_id, created_by_server_id=created_by_server_id)

            for ticket in progressbar(tickets):
                if 'print_data' in ticket.data:
                    if 'density_unit' in ticket.data['print_data']:
                        ticket.data['print_data']['density_unit'] = proper_density_unit

                if 'quantity_data' in ticket.data:
                    if 'volume' in ticket.data['quantity_data']:
                        if 'density_unit' in ticket.data['quantity_data']['volume']:
                            ticket.data['quantity_data']['volume']['density_unit'] = proper_density_unit

                updated_tickets.append(ticket)

            with transaction.atomic():
                Ticket.objects.bulk_update(updated_tickets, ['data'], 2000)
            print(f'Updated {len(updated_tickets)} tickets.')


        if update_type != 'tickets_only':
            self.stdout.write("Updating location-product densities...")

            updated_count = 0
            location_products = LocationProduct.objects.filter(dbreplication_client_company_id=dbreplication_client_company_id).exclude(density_unit=proper_density_unit)

            with transaction.atomic():
                for lp in progressbar(list(location_products)):
                    lp.density_unit = proper_density_unit
                    lp.save()
                    updated_count += 1
            print(f'Updated {updated_count} location products.')
