# Generated by Django 3.2.7 on 2023-05-09 17:19

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('vant_tboss', '0003_alter_gcanalysis_c1_alter_gcanalysis_c2_and_more'),
        ('vant_limit', '0001_initial'),
        ('tboss', '0002_proxies'),
    ]

    operations = [
        migrations.CreateModel(
            name='LimitProperties',
            fields=[
            ],
            options={
                'abstract': False,
                'proxy': True,
                'indexes': [],
                'constraints': [],
            },
            bases=('vant_limit.limitproperties',),
        ),
        migrations.CreateModel(
            name='Reading',
            fields=[
            ],
            options={
                'abstract': False,
                'proxy': True,
                'indexes': [],
                'constraints': [],
            },
            bases=('vant_tboss.reading',),
        ),
    ]
