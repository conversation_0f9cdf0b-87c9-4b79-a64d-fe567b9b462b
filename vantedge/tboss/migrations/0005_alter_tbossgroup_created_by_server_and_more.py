# Generated by Django 4.2 on 2023-06-22 21:48

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    dependencies = [
        (
            "vant_dbreplication",
            "0003_remove_dbreplicationlogentry_dbreplication_state_and_more",
        ),
        ("tboss", "0004_tboss_driver_proxy"),
    ]

    operations = [
        migrations.AlterField(
            model_name="tbossgroup",
            name="created_by_server",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="%(class)s_created_entities",
                to="vant_dbreplication.dbreplicationserver",
            ),
        ),
        migrations.AlterField(
            model_name="tbossgroup",
            name="dbreplication_client_company",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="%(class)s_set",
                to="tboss.terminalbossclientcompany",
                verbose_name="Client Company",
            ),
        ),
        migrations.AlterField(
            model_name="tbossgroup",
            name="dbreplication_config",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="%(class)s_set",
                to="vant_dbreplication.dbreplicationconfig",
            ),
        ),
        migrations.AlterField(
            model_name="tbossgroup",
            name="modified_by_server",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="%(class)s_modified_entities",
                to="vant_dbreplication.dbreplicationserver",
            ),
        ),
        migrations.AlterField(
            model_name="tbosspermission",
            name="created_by_server",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="%(class)s_created_entities",
                to="vant_dbreplication.dbreplicationserver",
            ),
        ),
        migrations.AlterField(
            model_name="tbosspermission",
            name="dbreplication_client_company",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="%(class)s_set",
                to="tboss.terminalbossclientcompany",
                verbose_name="Client Company",
            ),
        ),
        migrations.AlterField(
            model_name="tbosspermission",
            name="dbreplication_config",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="%(class)s_set",
                to="vant_dbreplication.dbreplicationconfig",
            ),
        ),
        migrations.AlterField(
            model_name="tbosspermission",
            name="modified_by_server",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="%(class)s_modified_entities",
                to="vant_dbreplication.dbreplicationserver",
            ),
        ),
    ]
