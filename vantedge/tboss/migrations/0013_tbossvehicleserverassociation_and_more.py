# Generated by Django 4.2 on 2023-11-14 03:41

from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import functools
import model_utils.fields
import schema_field.fields
import uuid
import vant_dbreplication.utils.schemas
import vantedge.tboss.models.association


class Migration(migrations.Migration):
    dependencies = [
        ("vant_dbreplication", "0005_dbreplicationlogentry_initial_and_more"),
        ("vant_tboss", "0017_company_lockout_end_location_lockout_end_and_more"),
        ("vant_user", "0008_user_lockout_end_alter_user_lockout_date"),
        ("tboss", "0012_associatedsignature"),
    ]

    operations = [
        migrations.CreateModel(
            name="TBossVehicleServerAssociation",
            fields=[
                (
                    "created",
                    model_utils.fields.AutoCreatedField(
                        default=django.utils.timezone.now,
                        editable=False,
                        verbose_name="created",
                    ),
                ),
                (
                    "modified",
                    model_utils.fields.AutoLastModifiedField(
                        default=django.utils.timezone.now,
                        editable=False,
                        verbose_name="modified",
                    ),
                ),
                (
                    "id",
                    model_utils.fields.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "data",
                    schema_field.fields.JSONSchemedField(
                        blank=True,
                        default=vantedge.tboss.models.association.ServerAssociationSchema,
                        encoder=functools.partial(
                            schema_field.fields.JSONSchemedEncoder,
                            *(),
                            **{
                                "schema": (
                                    vantedge.tboss.models.association.ServerAssociationSchema,
                                )
                            }
                        ),
                        null=True,
                        schema=(
                            vantedge.tboss.models.association.ServerAssociationSchema,
                        ),
                    ),
                ),
                (
                    "entity_state",
                    schema_field.fields.JSONSchemedField(
                        blank=True,
                        default=vant_dbreplication.utils.schemas.EntityState,
                        encoder=functools.partial(
                            schema_field.fields.JSONSchemedEncoder,
                            *(),
                            **{
                                "schema": (
                                    vant_dbreplication.utils.schemas.EntityState,
                                )
                            }
                        ),
                        help_text="JSON State Representation of Entity",
                        null=True,
                        schema=(vant_dbreplication.utils.schemas.EntityState,),
                    ),
                ),
                (
                    "entity",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="vehicle_server_associations",
                        to="vant_tboss.vehicle",
                    ),
                ),
                (
                    "server",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="vehicle_associations",
                        to="vant_dbreplication.dbreplicationserver",
                    ),
                ),
            ],
            options={
                "verbose_name": "TBoss Vehicle Server Association",
                "verbose_name_plural": "TBoss Vehicle Server Associations",
            },
        ),
        migrations.CreateModel(
            name="TBossVehicleCertificationServerAssociation",
            fields=[
                (
                    "created",
                    model_utils.fields.AutoCreatedField(
                        default=django.utils.timezone.now,
                        editable=False,
                        verbose_name="created",
                    ),
                ),
                (
                    "modified",
                    model_utils.fields.AutoLastModifiedField(
                        default=django.utils.timezone.now,
                        editable=False,
                        verbose_name="modified",
                    ),
                ),
                (
                    "id",
                    model_utils.fields.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "data",
                    schema_field.fields.JSONSchemedField(
                        blank=True,
                        default=vantedge.tboss.models.association.ServerAssociationSchema,
                        encoder=functools.partial(
                            schema_field.fields.JSONSchemedEncoder,
                            *(),
                            **{
                                "schema": (
                                    vantedge.tboss.models.association.ServerAssociationSchema,
                                )
                            }
                        ),
                        null=True,
                        schema=(
                            vantedge.tboss.models.association.ServerAssociationSchema,
                        ),
                    ),
                ),
                (
                    "entity_state",
                    schema_field.fields.JSONSchemedField(
                        blank=True,
                        default=vant_dbreplication.utils.schemas.EntityState,
                        encoder=functools.partial(
                            schema_field.fields.JSONSchemedEncoder,
                            *(),
                            **{
                                "schema": (
                                    vant_dbreplication.utils.schemas.EntityState,
                                )
                            }
                        ),
                        help_text="JSON State Representation of Entity",
                        null=True,
                        schema=(vant_dbreplication.utils.schemas.EntityState,),
                    ),
                ),
                (
                    "entity",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="vehicle_certification_server_associations",
                        to="vant_tboss.vehiclecertification",
                    ),
                ),
                (
                    "server",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="vehicle_certification_associations",
                        to="vant_dbreplication.dbreplicationserver",
                    ),
                ),
            ],
            options={
                "verbose_name": "TBoss Vehicle Certification Server Association",
                "verbose_name_plural": "TBoss Vehicle Certification Server Associations",
            },
        ),
        migrations.CreateModel(
            name="TBossUserServerAssociation",
            fields=[
                (
                    "created",
                    model_utils.fields.AutoCreatedField(
                        default=django.utils.timezone.now,
                        editable=False,
                        verbose_name="created",
                    ),
                ),
                (
                    "modified",
                    model_utils.fields.AutoLastModifiedField(
                        default=django.utils.timezone.now,
                        editable=False,
                        verbose_name="modified",
                    ),
                ),
                (
                    "id",
                    model_utils.fields.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "data",
                    schema_field.fields.JSONSchemedField(
                        blank=True,
                        default=vantedge.tboss.models.association.ServerAssociationSchema,
                        encoder=functools.partial(
                            schema_field.fields.JSONSchemedEncoder,
                            *(),
                            **{
                                "schema": (
                                    vantedge.tboss.models.association.ServerAssociationSchema,
                                )
                            }
                        ),
                        null=True,
                        schema=(
                            vantedge.tboss.models.association.ServerAssociationSchema,
                        ),
                    ),
                ),
                (
                    "entity_state",
                    schema_field.fields.JSONSchemedField(
                        blank=True,
                        default=vant_dbreplication.utils.schemas.EntityState,
                        encoder=functools.partial(
                            schema_field.fields.JSONSchemedEncoder,
                            *(),
                            **{
                                "schema": (
                                    vant_dbreplication.utils.schemas.EntityState,
                                )
                            }
                        ),
                        help_text="JSON State Representation of Entity",
                        null=True,
                        schema=(vant_dbreplication.utils.schemas.EntityState,),
                    ),
                ),
                (
                    "entity",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="user_server_associations",
                        to="vant_user.user",
                    ),
                ),
                (
                    "server",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="%(class)s_set",
                        to="vant_dbreplication.dbreplicationserver",
                    ),
                ),
            ],
            options={
                "verbose_name": "TBoss User Server Association",
                "verbose_name_plural": "TBoss User Server Associations",
            },
        ),
        migrations.CreateModel(
            name="TBossUserCertificationServerAssociation",
            fields=[
                (
                    "created",
                    model_utils.fields.AutoCreatedField(
                        default=django.utils.timezone.now,
                        editable=False,
                        verbose_name="created",
                    ),
                ),
                (
                    "modified",
                    model_utils.fields.AutoLastModifiedField(
                        default=django.utils.timezone.now,
                        editable=False,
                        verbose_name="modified",
                    ),
                ),
                (
                    "id",
                    model_utils.fields.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "data",
                    schema_field.fields.JSONSchemedField(
                        blank=True,
                        default=vantedge.tboss.models.association.ServerAssociationSchema,
                        encoder=functools.partial(
                            schema_field.fields.JSONSchemedEncoder,
                            *(),
                            **{
                                "schema": (
                                    vantedge.tboss.models.association.ServerAssociationSchema,
                                )
                            }
                        ),
                        null=True,
                        schema=(
                            vantedge.tboss.models.association.ServerAssociationSchema,
                        ),
                    ),
                ),
                (
                    "entity_state",
                    schema_field.fields.JSONSchemedField(
                        blank=True,
                        default=vant_dbreplication.utils.schemas.EntityState,
                        encoder=functools.partial(
                            schema_field.fields.JSONSchemedEncoder,
                            *(),
                            **{
                                "schema": (
                                    vant_dbreplication.utils.schemas.EntityState,
                                )
                            }
                        ),
                        help_text="JSON State Representation of Entity",
                        null=True,
                        schema=(vant_dbreplication.utils.schemas.EntityState,),
                    ),
                ),
                (
                    "entity",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="user_certification_server_associations",
                        to="vant_tboss.usercertification",
                    ),
                ),
                (
                    "server",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="user_certification_associations",
                        to="vant_dbreplication.dbreplicationserver",
                    ),
                ),
            ],
            options={
                "verbose_name": "TBoss User Certification Server Association",
                "verbose_name_plural": "TBoss User Certification Server Associations",
            },
        ),
        migrations.CreateModel(
            name="TBossProductServerAssociation",
            fields=[
                (
                    "created",
                    model_utils.fields.AutoCreatedField(
                        default=django.utils.timezone.now,
                        editable=False,
                        verbose_name="created",
                    ),
                ),
                (
                    "modified",
                    model_utils.fields.AutoLastModifiedField(
                        default=django.utils.timezone.now,
                        editable=False,
                        verbose_name="modified",
                    ),
                ),
                (
                    "id",
                    model_utils.fields.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "data",
                    schema_field.fields.JSONSchemedField(
                        blank=True,
                        default=vantedge.tboss.models.association.ServerAssociationSchema,
                        encoder=functools.partial(
                            schema_field.fields.JSONSchemedEncoder,
                            *(),
                            **{
                                "schema": (
                                    vantedge.tboss.models.association.ServerAssociationSchema,
                                )
                            }
                        ),
                        null=True,
                        schema=(
                            vantedge.tboss.models.association.ServerAssociationSchema,
                        ),
                    ),
                ),
                (
                    "entity_state",
                    schema_field.fields.JSONSchemedField(
                        blank=True,
                        default=vant_dbreplication.utils.schemas.EntityState,
                        encoder=functools.partial(
                            schema_field.fields.JSONSchemedEncoder,
                            *(),
                            **{
                                "schema": (
                                    vant_dbreplication.utils.schemas.EntityState,
                                )
                            }
                        ),
                        help_text="JSON State Representation of Entity",
                        null=True,
                        schema=(vant_dbreplication.utils.schemas.EntityState,),
                    ),
                ),
                (
                    "entity",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="product_server_associations",
                        to="vant_tboss.product",
                    ),
                ),
                (
                    "server",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="product_associations",
                        to="vant_dbreplication.dbreplicationserver",
                    ),
                ),
            ],
            options={
                "verbose_name": "TBoss Product Server Association",
                "verbose_name_plural": "TBoss Product Server Associations",
            },
        ),
        migrations.CreateModel(
            name="TBossLocationServerAssociation",
            fields=[
                (
                    "created",
                    model_utils.fields.AutoCreatedField(
                        default=django.utils.timezone.now,
                        editable=False,
                        verbose_name="created",
                    ),
                ),
                (
                    "modified",
                    model_utils.fields.AutoLastModifiedField(
                        default=django.utils.timezone.now,
                        editable=False,
                        verbose_name="modified",
                    ),
                ),
                (
                    "id",
                    model_utils.fields.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "data",
                    schema_field.fields.JSONSchemedField(
                        blank=True,
                        default=vantedge.tboss.models.association.ServerAssociationSchema,
                        encoder=functools.partial(
                            schema_field.fields.JSONSchemedEncoder,
                            *(),
                            **{
                                "schema": (
                                    vantedge.tboss.models.association.ServerAssociationSchema,
                                )
                            }
                        ),
                        null=True,
                        schema=(
                            vantedge.tboss.models.association.ServerAssociationSchema,
                        ),
                    ),
                ),
                (
                    "entity_state",
                    schema_field.fields.JSONSchemedField(
                        blank=True,
                        default=vant_dbreplication.utils.schemas.EntityState,
                        encoder=functools.partial(
                            schema_field.fields.JSONSchemedEncoder,
                            *(),
                            **{
                                "schema": (
                                    vant_dbreplication.utils.schemas.EntityState,
                                )
                            }
                        ),
                        help_text="JSON State Representation of Entity",
                        null=True,
                        schema=(vant_dbreplication.utils.schemas.EntityState,),
                    ),
                ),
                (
                    "entity",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="location_server_associations",
                        to="vant_tboss.location",
                    ),
                ),
                (
                    "server",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="location_associations",
                        to="vant_dbreplication.dbreplicationserver",
                    ),
                ),
            ],
            options={
                "verbose_name": "TBoss Location Server Association",
                "verbose_name_plural": "TBoss Location Server Associations",
            },
        ),
        migrations.CreateModel(
            name="TBossCompanyServerAssociation",
            fields=[
                (
                    "created",
                    model_utils.fields.AutoCreatedField(
                        default=django.utils.timezone.now,
                        editable=False,
                        verbose_name="created",
                    ),
                ),
                (
                    "modified",
                    model_utils.fields.AutoLastModifiedField(
                        default=django.utils.timezone.now,
                        editable=False,
                        verbose_name="modified",
                    ),
                ),
                (
                    "id",
                    model_utils.fields.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "data",
                    schema_field.fields.JSONSchemedField(
                        blank=True,
                        default=vantedge.tboss.models.association.ServerAssociationSchema,
                        encoder=functools.partial(
                            schema_field.fields.JSONSchemedEncoder,
                            *(),
                            **{
                                "schema": (
                                    vantedge.tboss.models.association.ServerAssociationSchema,
                                )
                            }
                        ),
                        null=True,
                        schema=(
                            vantedge.tboss.models.association.ServerAssociationSchema,
                        ),
                    ),
                ),
                (
                    "entity_state",
                    schema_field.fields.JSONSchemedField(
                        blank=True,
                        default=vant_dbreplication.utils.schemas.EntityState,
                        encoder=functools.partial(
                            schema_field.fields.JSONSchemedEncoder,
                            *(),
                            **{
                                "schema": (
                                    vant_dbreplication.utils.schemas.EntityState,
                                )
                            }
                        ),
                        help_text="JSON State Representation of Entity",
                        null=True,
                        schema=(vant_dbreplication.utils.schemas.EntityState,),
                    ),
                ),
                (
                    "entity",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="company_server_associations",
                        to="vant_tboss.company",
                    ),
                ),
                (
                    "server",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="company_associations",
                        to="vant_dbreplication.dbreplicationserver",
                    ),
                ),
            ],
            options={
                "verbose_name": "TBoss Company Server Association",
                "verbose_name_plural": "TBoss Company Server Associations",
            },
        ),
        migrations.CreateModel(
            name="TBossCompanyCertificationServerAssociation",
            fields=[
                (
                    "created",
                    model_utils.fields.AutoCreatedField(
                        default=django.utils.timezone.now,
                        editable=False,
                        verbose_name="created",
                    ),
                ),
                (
                    "modified",
                    model_utils.fields.AutoLastModifiedField(
                        default=django.utils.timezone.now,
                        editable=False,
                        verbose_name="modified",
                    ),
                ),
                (
                    "id",
                    model_utils.fields.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "data",
                    schema_field.fields.JSONSchemedField(
                        blank=True,
                        default=vantedge.tboss.models.association.ServerAssociationSchema,
                        encoder=functools.partial(
                            schema_field.fields.JSONSchemedEncoder,
                            *(),
                            **{
                                "schema": (
                                    vantedge.tboss.models.association.ServerAssociationSchema,
                                )
                            }
                        ),
                        null=True,
                        schema=(
                            vantedge.tboss.models.association.ServerAssociationSchema,
                        ),
                    ),
                ),
                (
                    "entity_state",
                    schema_field.fields.JSONSchemedField(
                        blank=True,
                        default=vant_dbreplication.utils.schemas.EntityState,
                        encoder=functools.partial(
                            schema_field.fields.JSONSchemedEncoder,
                            *(),
                            **{
                                "schema": (
                                    vant_dbreplication.utils.schemas.EntityState,
                                )
                            }
                        ),
                        help_text="JSON State Representation of Entity",
                        null=True,
                        schema=(vant_dbreplication.utils.schemas.EntityState,),
                    ),
                ),
                (
                    "entity",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="company_certification_server_associations",
                        to="vant_tboss.companycertification",
                    ),
                ),
                (
                    "server",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="company_certification_associations",
                        to="vant_dbreplication.dbreplicationserver",
                    ),
                ),
            ],
            options={
                "verbose_name": "TBoss Company Certification Server Association",
                "verbose_name_plural": "TBoss Company Certification Server Associations",
            },
        ),
        migrations.CreateModel(
            name="TBossCertificationServerAssociation",
            fields=[
                (
                    "created",
                    model_utils.fields.AutoCreatedField(
                        default=django.utils.timezone.now,
                        editable=False,
                        verbose_name="created",
                    ),
                ),
                (
                    "modified",
                    model_utils.fields.AutoLastModifiedField(
                        default=django.utils.timezone.now,
                        editable=False,
                        verbose_name="modified",
                    ),
                ),
                (
                    "id",
                    model_utils.fields.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "data",
                    schema_field.fields.JSONSchemedField(
                        blank=True,
                        default=vantedge.tboss.models.association.ServerAssociationSchema,
                        encoder=functools.partial(
                            schema_field.fields.JSONSchemedEncoder,
                            *(),
                            **{
                                "schema": (
                                    vantedge.tboss.models.association.ServerAssociationSchema,
                                )
                            }
                        ),
                        null=True,
                        schema=(
                            vantedge.tboss.models.association.ServerAssociationSchema,
                        ),
                    ),
                ),
                (
                    "entity_state",
                    schema_field.fields.JSONSchemedField(
                        blank=True,
                        default=vant_dbreplication.utils.schemas.EntityState,
                        encoder=functools.partial(
                            schema_field.fields.JSONSchemedEncoder,
                            *(),
                            **{
                                "schema": (
                                    vant_dbreplication.utils.schemas.EntityState,
                                )
                            }
                        ),
                        help_text="JSON State Representation of Entity",
                        null=True,
                        schema=(vant_dbreplication.utils.schemas.EntityState,),
                    ),
                ),
                (
                    "entity",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="certification_server_associations",
                        to="vant_tboss.certification",
                    ),
                ),
                (
                    "server",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="certification_associations",
                        to="vant_dbreplication.dbreplicationserver",
                    ),
                ),
            ],
            options={
                "verbose_name": "TBoss Certification Server Association",
                "verbose_name_plural": "TBoss Certification Server Associations",
            },
        ),
        migrations.AddConstraint(
            model_name="tbossvehicleserverassociation",
            constraint=models.UniqueConstraint(
                fields=("server", "entity"), name="unique_vehicle_server_association"
            ),
        ),
        migrations.AddConstraint(
            model_name="tbossvehiclecertificationserverassociation",
            constraint=models.UniqueConstraint(
                fields=("server", "entity"),
                name="unique_vehicle_certification_server_association",
            ),
        ),
        migrations.AddConstraint(
            model_name="tbossuserserverassociation",
            constraint=models.UniqueConstraint(
                fields=("server", "entity"), name="unique_user_server_association"
            ),
        ),
        migrations.AddConstraint(
            model_name="tbossusercertificationserverassociation",
            constraint=models.UniqueConstraint(
                fields=("server", "entity"),
                name="unique_user_certification_server_association",
            ),
        ),
        migrations.AddConstraint(
            model_name="tbossproductserverassociation",
            constraint=models.UniqueConstraint(
                fields=("server", "entity"), name="unique_product_server_association"
            ),
        ),
        migrations.AddConstraint(
            model_name="tbosslocationserverassociation",
            constraint=models.UniqueConstraint(
                fields=("server", "entity"), name="unique_location_server_association"
            ),
        ),
        migrations.AddConstraint(
            model_name="tbosscompanyserverassociation",
            constraint=models.UniqueConstraint(
                fields=("server", "entity"), name="unique_company_server_association"
            ),
        ),
        migrations.AddConstraint(
            model_name="tbosscompanycertificationserverassociation",
            constraint=models.UniqueConstraint(
                fields=("server", "entity"),
                name="unique_company_certification_server_association",
            ),
        ),
        migrations.AddConstraint(
            model_name="tbosscertificationserverassociation",
            constraint=models.UniqueConstraint(
                fields=("server", "entity"),
                name="unique_certification_server_association",
            ),
        ),
    ]
