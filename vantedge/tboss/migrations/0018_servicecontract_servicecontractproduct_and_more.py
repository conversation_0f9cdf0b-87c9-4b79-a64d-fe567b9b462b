# Generated by Django 4.2 on 2024-02-26 16:34

import django.core.serializers.json
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import functools
import model_utils.fields
import schema_field.fields
import uuid
import vant_utils.models


class Migration(migrations.Migration):
    dependencies = [
        ("vant_tboss", "0028_product_associations"),
        ("vant_dbreplication", "0005_dbreplicationlogentry_initial_and_more"),
        ("tboss", "0017_packages_update"),
    ]

    operations = [
        migrations.CreateModel(
            name="ServiceContract",
            fields=[
                (
                    "created",
                    model_utils.fields.AutoCreatedField(
                        default=django.utils.timezone.now,
                        editable=False,
                        verbose_name="created",
                    ),
                ),
                (
                    "modified",
                    model_utils.fields.AutoLastModifiedField(
                        default=django.utils.timezone.now,
                        editable=False,
                        verbose_name="modified",
                    ),
                ),
                (
                    "id",
                    model_utils.fields.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "dbreplication_state",
                    models.CharField(
                        choices=[
                            ("locked", "Locked"),
                            ("never_replicated", "Never Replicated"),
                            ("in_progress", "In Progress"),
                            ("replicated", "Replicated"),
                            ("incomplete", "Incomplete"),
                            ("not_logged", "Not Logged"),
                        ],
                        default="never_replicated",
                        max_length=30,
                        verbose_name="Replication State",
                    ),
                ),
                (
                    "additional_data",
                    models.JSONField(
                        blank=True,
                        default=dict,
                        encoder=django.core.serializers.json.DjangoJSONEncoder,
                    ),
                ),
                (
                    "associated_to_site",
                    models.BooleanField(
                        blank=True,
                        default=True,
                        help_text="Flag to associate the object to the site",
                        null=True,
                    ),
                ),
                ("name", models.CharField(max_length=150)),
                ("code", models.CharField(max_length=100)),
                ("effective_start_date", models.DateTimeField(blank=True, null=True)),
                ("effective_end_date", models.DateTimeField(blank=True, null=True)),
                ("enabled", models.BooleanField(blank=True, default=True)),
                (
                    "data",
                    schema_field.fields.JSONSchemedField(
                        default=dict,
                        encoder=functools.partial(
                            schema_field.fields.JSONSchemedEncoder,
                            *(),
                            **{"schema": dict}
                        ),
                        null=True,
                        schema=dict,
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("draft", "Draft"),
                            ("active", "Active"),
                            ("expired", "Expired"),
                        ],
                        default="draft",
                        max_length=10,
                    ),
                ),
                (
                    "created_by_server",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="%(class)s_created_entities",
                        to="vant_dbreplication.dbreplicationserver",
                    ),
                ),
                (
                    "customer",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="customer_set",
                        to="vant_tboss.company",
                    ),
                ),
                (
                    "dbreplication_client_company",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="%(class)s_set",
                        to="tboss.terminalbossclientcompany",
                        verbose_name="Client Company",
                    ),
                ),
                (
                    "dbreplication_config",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="%(class)s_set",
                        to="vant_dbreplication.dbreplicationconfig",
                    ),
                ),
                ("locations", models.ManyToManyField(to="vant_tboss.location")),
                (
                    "modified_by_server",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="%(class)s_modified_entities",
                        to="vant_dbreplication.dbreplicationserver",
                    ),
                ),
                (
                    "producer",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="producer_set",
                        to="vant_tboss.company",
                    ),
                ),
            ],
            options={
                "verbose_name": "Service Contract",
                "verbose_name_plural": "Service Contracts",
                "ordering": ["name"],
                "abstract": False,
            },
            bases=(vant_utils.models.CustomJsonDataMixin, models.Model),
        ),
        migrations.CreateModel(
            name="ServiceContractProduct",
            fields=[
                (
                    "id",
                    model_utils.fields.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "dbreplication_state",
                    models.CharField(
                        choices=[
                            ("locked", "Locked"),
                            ("never_replicated", "Never Replicated"),
                            ("in_progress", "In Progress"),
                            ("replicated", "Replicated"),
                            ("incomplete", "Incomplete"),
                            ("not_logged", "Not Logged"),
                        ],
                        default="never_replicated",
                        max_length=30,
                        verbose_name="Replication State",
                    ),
                ),
                (
                    "additional_data",
                    models.JSONField(
                        blank=True,
                        default=dict,
                        encoder=django.core.serializers.json.DjangoJSONEncoder,
                    ),
                ),
                (
                    "associated_to_site",
                    models.BooleanField(
                        blank=True,
                        default=True,
                        help_text="Flag to associate the object to the site",
                        null=True,
                    ),
                ),
                (
                    "price",
                    models.DecimalField(decimal_places=2, default=0, max_digits=8),
                ),
                (
                    "created_by_server",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="%(class)s_created_entities",
                        to="vant_dbreplication.dbreplicationserver",
                    ),
                ),
                (
                    "dbreplication_client_company",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="%(class)s_set",
                        to="tboss.terminalbossclientcompany",
                        verbose_name="Client Company",
                    ),
                ),
                (
                    "dbreplication_config",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="%(class)s_set",
                        to="vant_dbreplication.dbreplicationconfig",
                    ),
                ),
                (
                    "modified_by_server",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="%(class)s_modified_entities",
                        to="vant_dbreplication.dbreplicationserver",
                    ),
                ),
                (
                    "product",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="vant_tboss.product",
                    ),
                ),
                (
                    "service_contract",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="tboss.servicecontract",
                    ),
                ),
            ],
            options={
                "verbose_name": "Service Contract Product",
                "verbose_name_plural": "Service Contract Products",
            },
        ),
        migrations.AddField(
            model_name="servicecontract",
            name="products",
            field=models.ManyToManyField(
                through="tboss.ServiceContractProduct", to="vant_tboss.product"
            ),
        ),
        migrations.AddConstraint(
            model_name="servicecontractproduct",
            constraint=models.UniqueConstraint(
                fields=("service_contract", "product"),
                name="tboss_servicecontractproduct_unique_service_contract_product",
            ),
        ),
        migrations.AddConstraint(
            model_name="servicecontract",
            constraint=models.UniqueConstraint(
                fields=("code", "dbreplication_config"),
                name="tboss_servicecontract_unique_code_config",
            ),
        ),
        migrations.AddConstraint(
            model_name="servicecontract",
            constraint=models.UniqueConstraint(
                condition=models.Q(("dbreplication_config", None)),
                fields=("code",),
                name="tboss_servicecontract_unique_code",
            ),
        ),
    ]
