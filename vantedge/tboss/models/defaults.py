from django.apps import apps
from django.contrib.auth.models import Permission<PERSON>anager, GroupManager
from django.contrib.contenttypes import models as contenttypes
from django.contrib.auth import models as auth_models
from django.db import models
from django.db.models import Q
from django.db.models.fields import <PERSON><PERSON>ield, TextField, UUIDField
from django.forms import ValidationError
from django.utils.translation import gettext_lazy as _
from model_utils.models import UUIDModel, TimeStampedModel
from phonenumber_field.modelfields import <PERSON><PERSON><PERSON>ber<PERSON>ield
from vant_dbreplication.models import DBReplicationMixin
import uuid

# class ContentType(DBReplicationMixin):
#     class Meta:
#         verbose_name = _("Content Type")
#         verbose_name_plural = _("Content Types")

#     app_label = models.CharField(max_length=100)
#     model = models.Char<PERSON>ield(_('python model class name'), max_length=100)
#     objects = contenttypes.ContentTypeManager()

#     def __str__(self):
#         return f"{self.created_by_server} TBOSS Content Type {self.app_label}.{self.model}"

#     def natural_key(self):
#         return (self.app_label, self.model)


class TBossPermissionManager(PermissionManager):
    def get_by_natural_key(self, codename, app_label, model, server):
        # Permissions will be site specific so no need to query replication config
        return self.get(
            codename=codename,
            content_type=contenttypes.ContentType.objects.db_manager(
                self.db
            ).get_by_natural_key(app_label, model),
            dbreplication_config__servers__id__in=[server.id],
        )


class TBossGroupManager(GroupManager):
    def get_by_natural_key(self, name, server):
        # Groups may or may not be shared amongs sites so add replication config lookup here
        return self.get(name=name, dbreplication_config__servers__id__in=[server.id])


class TBossPermission(DBReplicationMixin, TimeStampedModel):
    class Meta:
        permissions = (
            ("add_permission", "Can add permissions"),
            ("change_permission", "Can change permissions"),
            ("delete_permission", "Can delete permissions"),
            ("view_permission", "Can view permissions"),
        )
        verbose_name = _("Permission")
        verbose_name_plural = _("Permissions")
        ordering = ["content_type__app_label", "content_type__model", "codename"]

    name = models.CharField(_("name"), max_length=255)
    content_type = models.ForeignKey(
        contenttypes.ContentType,  # ContentType,
        models.CASCADE,
        verbose_name=_("content type"),
    )
    codename = models.CharField(_("codename"), max_length=100)

    objects = TBossPermissionManager()

    def __str__(self):
        return "%s | %s" % (self.content_type, self.name)

    def natural_key(self):
        return (self.codename,) + self.content_type.natural_key()

    natural_key.dependencies = ["contenttypes.contenttype"]


class TBossGroup(DBReplicationMixin, TimeStampedModel):
    class Meta:
        permissions = (
            ("add_group", "Can add groups"),
            ("change_group", "Can change groups"),
            ("delete_group", "Can delete groups"),
            ("view_group", "Can view groups"),
        )
        verbose_name = _("Group")
        verbose_name_plural = _("Groups")

    name = models.CharField(_("name"), max_length=150)
    permissions = models.ManyToManyField(
        TBossPermission, verbose_name=_("permissions"), blank=True
    )

    objects = TBossGroupManager()

    def __str__(self):
        return self.name

    def natural_key(self):
        return (self.name,)


# class TBossAbstractUser(auth_models.AbstractUser):
#     username_validator = auth_models.UnicodeUsernameValidator()
#     username = models.CharField(
#         _("username"),
#         max_length=150,
#         help_text=_(
#             "Required. 150 characters or fewer. Letters, digits and @/./+/-/_ only."
#         ),
#         validators=[username_validator],
#     )

#     class Meta(auth_models.AbstractUser.Meta):
#         abstract = True


# class TBossUser(UUIDModel, TBossAbstractUser, DBReplicationMixin, TimeStampedModel):
#     class Meta:
#         permissions = (
#             ("add_user", "Can add users"),
#             ("change_user", "Can change users"),
#             ("delete_user", "Can delete users"),
#             ("view_user", "Can view users"),
#         )
#         verbose_name = "User"
#         verbose_name_plural = "Users"

#     #: First and last name do not cover name patterns around the globe
#     name = models.CharField(_("Full Name"), blank=True, max_length=255)
#     first_name = None  # type: ignore
#     last_name = None  # type: ignore
#     telephone = PhoneNumberField(blank=True, null=True)
#     pin = models.CharField(max_length=10, blank=True, null=True)
#     code = models.CharField(max_length=10, blank=True, null=True)

#     effective_start_date = DateField(blank=True, null=True)
#     effective_end_date = DateField(blank=True, null=True)

#     address = TextField(blank=True, null=True)

#     # altered related_name as it clashes with wheelhouse.User
#     groups = models.ManyToManyField(
#         TBossGroup,
#         verbose_name=_("groups"),
#         blank=True,
#         help_text=_(
#             "The groups this user belongs to. A user will get all permissions "
#             "granted to each of their groups."
#         ),
#         related_name="tbossuser_sets",
#         related_query_name="user",
#     )

#     # altered related_name as it clashes with wheelhouse.User
#     user_permissions = models.ManyToManyField(
#         TBossPermission,
#         verbose_name=_("user permissions"),
#         blank=True,
#         help_text=_("Specific permissions for this user."),
#         related_name="tbossuser_sets",
#         related_query_name="user",
#     )

#     def clean(self):
#         super().clean()
#         if self.created_by_server:
#             if (
#                 self.created_by_server.dbreplication_client_company
#                 != self.dbreplication_client_company
#             ):
#                 raise ValidationError(
#                     "created_by_server should be under the same client company as the instance"
#                 )
#         if self.modified_by_server:
#             if (
#                 self.modified_by_server.dbreplication_client_company
#                 != self.dbreplication_client_company
#             ):
#                 raise ValidationError(
#                     "modified_by_server should be under the same client company as the instance"
#                 )

#     def validate_unique(self, exclude=None):
#         super().validate_unique(exclude)

#         if not self.code:
#             return

#         # check for uniqueness of code
#         unique_query = Q(code=self.code)
#         manager = self._meta.default_manager

#         # code should be unique per replication config group
#         # if hasattr(self, "dbreplication_config"):
#         #     pass

#         # make code unique per client_company
#         if hasattr(self, "dbreplication_client_company"):
#             unique_query &= Q(
#                 dbreplication_client_company=self.dbreplication_client_company
#             )

#         if self.pk:
#             unique_query &= ~Q(pk=self.pk)

#         if manager.filter(unique_query).exists():
#             raise ValidationError(
#                 f"The code {self.code} already exists for the client company {self.dbreplication_client_company}."
#             )
