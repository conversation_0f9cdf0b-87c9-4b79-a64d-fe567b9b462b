import uuid
from typing import Optional, List

import arrow
from django.conf import settings
from django.contrib import admin
from django.contrib.contenttypes.fields import GenericRelation
from django.contrib.contenttypes.models import ContentType
from django.db.models import Q, F
from django.dispatch import receiver
from django.forms import ValidationError
from django.utils.translation import gettext_lazy as _
from polymorphic.models import PolymorphicManager
from safedelete import SOFT_DELETE_CASCADE
from safedelete.signals import post_softdelete
from vant_calcs.uom import get_unit_decimal_places, WeightChoices, VolumeChoices, UOM
# from vant_device import models as vant_device
from vant_ledger import models as vant_ledger
from vant_limit import models as vant_limit
from vant_tboss import models as vant_tboss
from vant_ticket import models as vant_ticket
from vant_user import models as vant_user

from vantedge.attachment.models import Attachment
from vantedge.tboss.models.client_company import TerminalBossClientCompany
from vantedge.util.classes import ProxyTicketMixin
from vantedge.util.json.util import camelize, camel_to_underscore


class Company(vant_tboss.Company):
    class Meta(vant_tboss.Company.Meta):
        proxy = True
        verbose_name = _("Company")
        verbose_name_plural = _("Companies")


class CompanyRole(vant_tboss.CompanyRole):
    class Meta(vant_tboss.CompanyRole.Meta):
        proxy = True


class Certification(vant_tboss.Certification):
    class Meta(vant_tboss.Certification.Meta):
        proxy = True
        verbose_name = _("Certification")
        verbose_name_plural = _("Certifications")


class Location(vant_tboss.Location):
    class Meta(vant_tboss.Location.Meta):
        proxy = True
        verbose_name = _("Location")
        verbose_name_plural = _("Locations")


class LocationProduct(vant_tboss.LocationProduct):
    class Meta(vant_tboss.LocationProduct.Meta):
        proxy = True
        verbose_name = _("Location Product")
        verbose_name_plural = _("Location Products")


class GCAnalysis(vant_tboss.GCAnalysis):
    class Meta(vant_tboss.GCAnalysis.Meta):
        proxy = True


class Reading(vant_tboss.Reading):
    class Meta(vant_tboss.Reading.Meta):
        proxy = True


class Vehicle(vant_tboss.Vehicle):
    class Meta(vant_tboss.Vehicle.Meta):
        proxy = True
        verbose_name = _("Vehicle")
        verbose_name_plural = _("Vehicles")


class Product(vant_tboss.Product):
    class Meta(vant_tboss.Product.Meta):
        proxy = True
        verbose_name = _("Product")
        verbose_name_plural = _("Products")


class UserCertification(vant_tboss.UserCertification):
    class Meta(vant_tboss.UserCertification.Meta):
        proxy = True
        verbose_name = _("User Certification")
        verbose_name_plural = _("User Certifications")


class VehicleCertification(vant_tboss.VehicleCertification):
    class Meta(vant_tboss.VehicleCertification.Meta):
        proxy = True
        verbose_name = _("Vehicle Certification")
        verbose_name_plural = _("Vehicle Certifications")


class CompanyCertification(vant_tboss.CompanyCertification):
    class Meta(vant_tboss.CompanyCertification.Meta):
        proxy = True
        verbose_name = _("Company Certification")
        verbose_name_plural = _("Company Certifications")


def properties_inspect(
    properties: dict,
    path: str,
    references: dict,
    properties_annotation: dict,
    server_key: str,
) -> dict:
    """
    Recursively translate schema definition to more readable
    """
    base_fields = list(map(lambda f: f.name, Ticket._meta.fields))
    for field_name, field in properties.items():
        field_type = field.get("type")
        field_path = f"{path}{field_name}"
        format = field.get("format")
        # if type object, lookup using 'references' by title
        if field_type in [None, "object"]:
            reference = references.get(
                field.get("all_of", [{}])[0]
                .get("$ref", "")
                .replace("#/definitions/", ""),
                {},
            )
            # get the properties of the reference
            reference_properties = reference.get("properties")
            reference_columns = properties_inspect(
                reference_properties, f"{field_path}__", references, {}, server_key
            )
            properties_annotation = {**properties_annotation, **reference_columns}
        elif field_type not in [
            "boolean",
            "string",
            "date-time",
            "integer",
            "number",  # decimals, floats
            "array",
            "list",
        ]:
            # exclude columns that do not fall into the list of types defined
            continue
        else:
            suffix = ""
            annotation = F(field_path)
            if field_name in base_fields:
                from vant_dbreplication.models.server import DBReplicationServer

                server = DBReplicationServer.objects.get(pk=uuid.UUID(server_key))
                suffix = f"({server.name})"  # add a suffix to the server if field already exists as a base field

            column_key = f"{field_name}{suffix}"
            column = {
                "annotation": annotation,
                "type": field_type,
                "format": format,
                "path": field_path,
                "label": camelize(column_key.lower()),
            }
            if column_key not in properties_annotation:
                properties_annotation[camel_to_underscore(column_key.lower())] = column
    return properties_annotation


def remove_whitepsace(string: str) -> str:
    return string.strip().replace(" ", "")


class Ticket(ProxyTicketMixin, vant_ticket.Ticket):
    _safedelete_policy = SOFT_DELETE_CASCADE

    class Meta(vant_ticket.Ticket.Meta):
        proxy = True
        verbose_name = _("Ticket")
        verbose_name_plural = _("Tickets")

    attachments = GenericRelation(Attachment)

    # TODO:  Remove if only for GIP Ticket Schemas?
    # def __init__(self, *args, **kwargs):
    #     """ Override init to inject the ticket data schema """
    #     field_mapping = {f.name: v for f, v in zip(self._meta.fields, args)}
    #     if field_mapping.get('ticket_type'):
    #         ticket_type = field_mapping.get('ticket_type')
    #         self.data_schema = None
    #         self.data_schema = TICKET_SCHEMAS.get(ticket_type)
    #     super().__init__(*args, **kwargs)

    # TODO:
    # Calling save does a lot of things behind the scenes
    # for these proxies.
    # A faster/better way may be doing these steps manually in bulk
    # - bulk change status
    # - bulk create dbreplication-log-entry
    # - bulk propagate dbreplication-servers-subscription
    @classmethod
    def set_status(cls, ticket_id, status: vant_ledger.Transaction.TransactionStatus):
        """Update transaction status"""
        assert (
            status in vant_ledger.Transaction.TransactionStatus
        ), "Status is not valid"
        ticket = Ticket.objects.get(id=ticket_id)
        ticket.status = status
        ticket.save()

    @classmethod
    def get_company_data_columns(cls, company: TerminalBossClientCompany) -> dict:
        columns = {}
        data_columns = cls._data_columns(company)
        references = data_columns.get("references")
        for server_key, data_fields in data_columns.get("data_fields", {}).items():
            server_reference = references.get(server_key, {})
            for class_name, class_fields in data_fields.items():
                properties = class_fields.get("properties", {})
                string_path = "data__"
                columns = properties_inspect(
                    properties, string_path, server_reference, columns, server_key
                )
        return columns

    @classmethod
    def _data_columns(cls, company) -> dict:
        """
        Fetch the columns from the Ticket DBReplicationConfigs from the company
        """
        from vant_dbreplication.models import DBReplicationConfig

        base_fields = list(map(lambda f: f.name, cls._meta.fields))
        custom_fields = {}
        data_fields = {}
        references = {}

        content_type = ContentType.objects.get_for_model(cls)
        replication_configs = DBReplicationConfig.objects.filter(
            dbreplication_client_company=company, content_type=content_type
        ).order_by("created")
        for config in replication_configs:
            for schema_key, ticket_schemas in config.additional_data.items():
                # exclude configuration if its not the schema config
                if "schemas-" not in schema_key:
                    continue
                server_key = schema_key.replace("schemas-", "")
                custom_fields.setdefault(server_key, {})
                data_fields.setdefault(server_key, {})
                references.setdefault(server_key, {})
                for ticket_model, ticket_schema in ticket_schemas.items():
                    definition_list_key = "definitions"
                    # locate schema definitions
                    definitions = {}
                    if "definitions" in ticket_schema:
                        definitions = ticket_schema.get("definitions")
                    elif "$defs" in ticket_schema:
                        definition_list_key = "$defs"
                        definitions = ticket_schema.get(definition_list_key)
                    elif (
                        "data" in ticket_schema
                        and "schema" in ticket_schema.get("data")
                        and "definitions" in ticket_schema.get("data").get("schema")
                    ):
                        definitions = {
                            v.get("title"): v
                            for k, v in ticket_schema.get("data")
                            .get("schema")
                            .get("definitions")
                            .items()
                        }
                    references[server_key].update(definitions)

                    # locate data properties from definitions
                    ticket_schema_copy = ticket_schema.copy()
                    ticket_data_properties = {}
                    if "data" in ticket_schema and "schema" in ticket_schema.get(
                        "data"
                    ):
                        data_schema_title = (
                            ticket_schema.get("data").get("schema").get("title")
                        )
                        search_by_title = {
                            v.get("title"): k for k, v in definitions.items()
                        }
                        ticket_schema_copy = (
                            definitions.get(search_by_title.get(data_schema_title))
                            or {}
                        )

                    elif "properties" in ticket_schema:
                        ticket_schema_copy = ticket_schema_copy.get("properties") or {}

                        if "definitions" in ticket_schema_copy.get("data"):
                            definitions = ticket_schema_copy.get("data").get(
                                "definitions"
                            )
                        elif "$defs" in ticket_schema_copy.get("data"):
                            definition_list_key = "$defs"
                            definitions = ticket_schema_copy.get("data").get(
                                definition_list_key
                            )

                        references[server_key].update(definitions)

                    # found data properties from definitions
                    if ticket_schema_copy and ticket_schema_copy.get("properties"):
                        ticket_data_properties = ticket_schema_copy

                    for (
                        ticket_field_name,
                        ticket_data_schema,
                    ) in ticket_schema_copy.items():
                        if (
                            ticket_field_name not in base_fields
                            and ticket_field_name not in custom_fields[server_key]
                        ):
                            custom_fields[server_key][ticket_field_name] = (
                                ticket_data_schema
                            )
                        elif ticket_field_name == "data":
                            data_schema_title = remove_whitepsace(
                                ticket_data_schema.get("title")
                            )
                            field_format = ticket_data_schema.get("format")

                            if (
                                field_format == "json-string"
                                or not ticket_data_schema.get("properties")
                            ):
                                continue

                            ticket_data_properties = ticket_data_schema

                    data_fields[server_key][data_schema_title] = ticket_data_properties

        val = {
            "custom_fields": custom_fields,
            "data_fields": data_fields,
            "references": references,
        }
        return val

    def set_properties(self):
        """
        Set properties based on site if entity is
        linked to a site AND has no assigned properties
        """
        server = self.created_by_server

        if server and not self.properties:
            self.properties = TicketProperties.objects.filter(
                created_by_server=server
            ).first()

    @property
    def quantity_display(self):
        """
        Display quantity in a string readable format based on the
        `display_unit` in the corresponding `TicketProperties` instance.
        """
        if self.quantity and self.properties:
            quantity_base = UOM(f"{self.quantity} {self.properties.base_unit}")
            result = quantity_base.to(self.properties.display_unit).magnitude
            standard_decimal_places = get_unit_decimal_places(
                self.properties.display_unit
            )
            decimal_places = standard_decimal_places if standard_decimal_places else 0
            return (
                f"{result:.{decimal_places}f}" + " " + str(self.properties.display_unit)
            )
        return ""

    def save(self, *args, **kwargs):
        self.set_properties()
        super().save(*args, **kwargs)

    def get_latest_invoice(self):
        if isinstance(self.data, dict) and self.data.get("customs_data", []):
            return self.data["customs_data"][-1]["print_invoice"]

@receiver(post_softdelete)
def delete_ticket_entries_on_soft_delete(sender, instance, **kwargs):
    if isinstance(instance, Ticket):
        ticket_entries = instance.entry_set.all()

        for ticket_entry in ticket_entries:
            ticket_entry.delete()


class TicketPropertiesManager(PolymorphicManager):
    def get_queryset(self):
        # override polymorphic manager get_queryset()
        qs = super(PolymorphicManager, self).get_queryset()
        return qs


class TicketProperties(vant_ticket.TicketProperties):
    class Meta(vant_ticket.TicketProperties.Meta):
        proxy = True
        verbose_name = _("Ticket Template")
        verbose_name_plural = _("Ticket Templates")

    objects = TicketPropertiesManager()

    def __str__(self):
        return f"TicketProperties: {self.class_name} {self.created_by_server} ({self.base_unit})"


"""
Some weird bug for PolymorphicModel and Proxy models where content types do not match
affecting the polymorphic_ctype_id assignment. Might be related to sync?

Results in proxies.TicketEntry.objects.all() != vant_ticket.TicketEntry.objects.all()

Whats weird is that proxies.Ticket.objects.all() == vant_ticket.Ticket.objects.all()
even if Tickets also inerit from PolymorphicModel

Content Type Mismatch:

=======================================================

In [10]: from vant_ticket import models as vant_ticket

In [11]: from vantedge.tboss.models import *

In [12]: ContentType.objects.get_for_model(TicketEntry, for_concrete_model=False).id
Out[12]: 205

In [13]: ContentType.objects.get_for_model(vant_ticket.TicketEntry, for_concrete_model=False).id
Out[13]: 224

In [14]: ContentType.objects.get_for_model(TicketEntry, for_concrete_model=True).id
Out[14]: 224

In [15]: ContentType.objects.get_for_model(vant_ticket.TicketEntry, for_concrete_model=True).id
Out[15]: 224

======================================================

Queries Mismatch:

======================================================

In [7]: Ticket.objects.count()
Out[7]: 45992

In [8]: vant_ticket.Ticket.objects.count()
Out[8]: 45992

In [9]: vant_ticket.TicketEntry.objects.count()
Out[9]: 11635

In [10]: TicketEntry.objects.count()
Out[10]: 2

"""


class TicketEntryManager(PolymorphicManager):
    def get_queryset(self):
        # override polymorphic manager get_queryset()
        qs = super(PolymorphicManager, self).get_queryset()
        return qs


class TicketEntry(vant_ticket.TicketEntry):
    class Meta(vant_ticket.TicketEntry.Meta):
        proxy = True
        verbose_name = _("Ticket Entry")
        verbose_name_plural = _("Ticket Entries")

    objects = TicketEntryManager()

    @property
    @admin.display(description="Quantity")
    def quantity_base(self):
        """
        Return pint quantity of the ticket in base units
        This is overridden for the case where there are ticket entries
        that are weight based for a ticket that is volume based or vice versa.
        If it falls into that scenario, the ticket_base_unit will use the entry.display_unit as the base_unit
        """
        weight_choices = dict(WeightChoices.choices)
        volume_choices = dict(VolumeChoices.choices)
        ticket_base_unit = self.ticket.base_unit
        if (
            self.display_unit in weight_choices
            and ticket_base_unit not in weight_choices
        ) or (
            self.display_unit in volume_choices
            and ticket_base_unit not in volume_choices
        ):
            return UOM(f"{self.quantity} {self.display_unit}")
        return super().quantity_base


class PeriodicLimit(vant_limit.PeriodicLimit):
    class Meta(vant_limit.PeriodicLimit.Meta):
        proxy = True

    def get_active_period(self):
        """
        Returns the first period associated to the Limit that is_effective() today and enabled.
        If there are no active periods then returns None.
        """
        today = arrow.now(settings.TIME_ZONE).datetime
        active_period = (
            LimitPeriod.objects.filter(
                limit=self, effective_start_date__lte=today, enabled=True
            )
            .exclude(effective_end_date__lt=today)
            .first()
        )
        return active_period

    @property
    def base_unit(self):
        if self.properties:
            return self.properties.base_unit
        return super().base_unit

    @property
    def default_display_unit(self):
        if self.properties:
            return self.properties.default_display_unit
        return super().default_display_unit

    @property
    def base_unit(self):
        if self.properties:
            return self.properties.base_unit
        return super().base_unit

    @property
    def default_display_unit(self):
        if self.properties:
            return self.properties.default_display_unit
        return super().default_display_unit

    @property
    def quantity_display(self):
        """
        Display quantity in a string readable format based on the
        `default_display_unit` in the corresponding `LimitProperties` instance.
        """
        if self.quantity and self.properties:
            quantity_base = UOM(f"{self.quantity} {self.properties.base_unit}")
            result = quantity_base.to(self.properties.default_display_unit).magnitude
            standard_decimal_places = get_unit_decimal_places(
                self.properties.default_display_unit
            )
            decimal_places = standard_decimal_places if standard_decimal_places else 0
            return (
                f"{result:.{decimal_places}f}"
                + " "
                + str(self.properties.default_display_unit)
            )
        return ""

    def set_properties(self):
        """
        Set properties based on site if entity is
        linked to a site AND has no assigned properties
        """
        server = self.created_by_server

        if server and not self.properties:
            self.properties = LimitProperties.objects.filter(
                created_by_server=server
            ).first()

    def roll_forward(self):
        super().roll_forward()

        DB_REPLICATION_FIELDS = ["dbreplication_client_company", "created_by_server"]
        for limit_period in LimitPeriod.objects.filter(limit=self):
            if not limit_period.created_by_server:
                for field in DB_REPLICATION_FIELDS:
                    setattr(limit_period, field, getattr(self, field))
                limit_period.save()


class LimitPeriod(vant_limit.LimitPeriod):
    class Meta(vant_limit.LimitPeriod.Meta):
        proxy = True

    @property
    def base_unit(self):
        if hasattr(self, "limit") and self.limit and self.limit.properties:
            return self.limit.properties.base_unit
        return super().base_unit

    @property
    def default_display_unit(self):
        if hasattr(self, "limit") and self.limit and self.limit.properties:
            return self.limit.properties.default_display_unit
        return super().default_display_unit


class LimitProperties(vant_limit.LimitProperties):
    class Meta(vant_limit.LimitProperties.Meta):
        proxy = True

    def __str__(self):
        return f"LimitProperties: {self.name}-{self.class_name} {self.created_by_server} ({self.base_unit})"


class TBossUserManager(vant_user.TBossUserManager):
    def get_staff_members(self):
        return self.filter(is_staff=True)


class UserDataSchema(vant_user.ExtraUserDetails):
    product_set: Optional[List[str]] = []


class TBossUser(vant_user.User):
    class Meta(vant_user.User.Meta):
        proxy = True
        verbose_name = "User"
        verbose_name_plural = "Users"

    data_schema = UserDataSchema

    objects = TBossUserManager()

    def validate_unique(self, exclude=None):
        super(vant_user.User, self).validate_unique(exclude)

        # check for uniqueness of code
        unique_query = Q()
        error_dict = {}
        manager = self._meta.default_manager

        if self.pk:
            unique_query &= ~Q(pk=self.pk)

        # code should be unique per replication config group
        if self.dbreplication_config is not None:
            replication_config = self.dbreplication_config
            server_group_ids = list(
                replication_config.servers.values_list("pk", flat=True)
            )
            unique_query &= Q(
                Q(created_by_server__in=server_group_ids)
                | Q(modified_by_server__in=server_group_ids)
            )

            if self.code:
                unique_code_query = unique_query & Q(code=self.code)
                if manager.filter(unique_code_query).exists():
                    server_names = list(
                        replication_config.servers.values_list(
                            "name", flat=True
                        ).distinct()
                    )
                    error_dict["code"] = (
                        f"The code {self.code} already exists on server(s): {', '.join(server_names)}"
                    )

            unique_username_query = unique_query & Q(username=self.username)
            if manager.filter(unique_username_query).exists():
                server_names = list(
                    replication_config.servers.values_list("name", flat=True).distinct()
                )
                error_dict["username"] = (
                    f"The username {self.username} already exists on server(s): {', '.join(server_names)}"
                )

        if error_dict:
            raise ValidationError(error_dict)


class TBossUserAccess(vant_user.UserAccess):
    class Meta(vant_user.UserAccess.Meta):
        proxy = True


class Route(vant_tboss.Route):
    class Meta(vant_tboss.Route.Meta):
        proxy = True


class AssociatedSignature(vant_ledger.AssociatedSignature):
    class Meta(vant_ledger.AssociatedSignature.Meta):
        proxy = True


class CrossBorderContract(vant_tboss.CrossBorderContract):
    class Meta(vant_tboss.CrossBorderContract.Meta):
        proxy = True
