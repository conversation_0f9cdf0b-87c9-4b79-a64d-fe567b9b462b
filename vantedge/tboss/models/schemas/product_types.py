from enum import Enum
class ProductAndServiceTypes(str, Enum):
    '''
    Enum for product parents.
    This schema is used for Green Impact Partners product/service/prices.
    This is used to categorize the products and services in the system.
    Each product product/service/prices will have a parent type to categorize it.

    Parent types are:
    - Processing Service
        - Children are different types of processing services
            - Children of this are streams.
    - Transfer Types
        - Children of this are the transfer types.
    - Transfer Products
        - Children of this are the transfer products: Oil, Effluent Water, Fresh Water, Solid, Landfill.
    - Price Book Service
        - These are the services that have a related price.
    - Price Book Product
        - These are the products that have a related price.
    '''
    PROCESSING_SERVICE = 'Processing Service'
    TRANSFER_TYPES = 'Transfer Types'
    TRANSFER_PRODUCTS = 'Transfer Products'
    PRICE_BOOK_SERVICE = 'Price Book Service'
    PRICE_BOOK_PRODUCT = 'Price Book Product'