import datetime
from concurrent.futures import <PERSON>hr<PERSON><PERSON><PERSON><PERSON>xecutor
from io import BytesIO
from typing import Dict, List, Literal, Optional
from uuid import UUID, uuid4

import requests
from custom_apps.green_impact_partners.schemas import GIPTicketStatus
from django.conf import settings
from django.contrib.contenttypes.fields import GenericRelation
from django.db import connection, models
from django.db.models import Q
from django.template import Context, Template
from django.utils import timezone
from django.utils.timezone import now
from django.utils.translation import gettext_lazy as _
from model_utils.models import TimeStampedModel, UUIDModel
from pydantic import BaseModel, Field
from rest_framework.reverse import reverse
from schema_field.fields import J<PERSON>NSchemedField
from vant_dbreplication.models import DBReplicationMixin
from vant_tboss.models import Location

# Vantedge imports
from vant_utils.models import ActiveMixin, CustomJsonDataMixin, NamedMixin

from vantedge.attachment.models import Attachment
from vantedge.printables.models import PrintableTemplate
from vantedge.printables.views import PDFKitGenerator
from vantedge.tboss.constants.ticket_invoice import InvoicePaymentTerms
from vantedge.tboss.models import ServiceContract
from vantedge.users.models import User
from vantedge.util.notifications.email import send_invoice_approval, send_invoice_payment
from vantedge.util.pandell.apis import Pandell
from vantedge.util.pidx.main import PIDX
from vantedge.util.pidx.schemas.data import InvoiceItem, InvoiceProperties


class PrintableInvoiceMixin(object):
    """
    Provides method for printing HTML content
    for Ticket Invoices.
    """

    class TopTable(BaseModel):
        customer_name: str = ' '
        producer_name: str = ' '
        facility_name: str = ' '
        contract_name: str = ' '
        contact_name: Optional[str] = ' '
        contact_email: str = ' '
        billing_address: str = ' '
        po_number: Optional[str] = ' '
        payment_terms: str = ' '
        afe_number: Optional[str] = ' '
        cost_center_number: Optional[str] = ' '
        major: Optional[str] = ' '
        minor: Optional[str] = ' '
        invoice_date: Optional[str] = ' '
        due_date: str = ' '
        locations: str = ' '
        # Used by the `create_print_data_table` tag to *not* print a field
        # TODO(wnh): This should be cleaned up, maybe only pass in info that should be printed?
        dont_print_list: List[str] = []

    class ChargesTable(BaseModel):
        ticket: str
        description: str
        carrier_company: str
        quantity: str
        uom: str
        trucking_ticket_number: Optional[str]
        solid_percentage: Optional[str]
        solid_volume: Optional[str]
        water_percentage: Optional[str]
        water_volume: Optional[str]
        oil_percentage: Optional[str]
        oil_volume: Optional[str]
        rate: str
        total: str

    def printed_invoice(self):
        from vantedge.tboss.models import ServiceContractTicket
        from vantedge.tboss.models.proxies import Ticket
        from vantedge.tboss.views import pdfUtils

        def generate_ticket_pdf(sct):
            """Generate a single ticket PDF"""
            try:
                ticket = sct.ticket
                ticket_info = {
                    "ticket": ticket,
                    "template_type": "BOL",
                    "version": 1,
                    "force_footer": True
                }
                ticket_pdf = pdfUtils.generate_pdf(ticket_info)
                ticket_pdf = pdfUtils.append_attachments_to_pdf(instance=ticket, pdf_file=ticket_pdf)
                return ticket_pdf
            finally:
                connection.close()

        def generate_invoice_pdf(html_rendered):
            try:
                ticket_invoice_pdf =  PDFKitGenerator(
                    css=settings.STATIC_ROOT + "/css/pdf-bulma.css",
                    main_html=html_rendered,
                    options={
                        "page-size": "A4",
                        "encoding": "UTF-8",
                        "enable-local-file-access": None,
                        "orientation": "Landscape",  # Set orientation to Landscape
                    }
                ).render_pdf()
                ticket_invoice_pdf = pdfUtils.stamp_pdf_with_footer_image(ticket_invoice_pdf)
                return ticket_invoice_pdf
            finally:
                connection.close()

        ticket_invoice = self
        locations = ""
        for location in ticket_invoice.locations.all():
            locations += location.name + ', '
        # Remove the last comma
        locations = locations[:-2]
        top_table = self.TopTable(
            customer_name=ticket_invoice.customer.name,
            producer_name=ticket_invoice.producer.name,
            facility_name=ticket_invoice.created_by_server.name,
            contract_name=ticket_invoice.service_contract.name,
            contact_name=ticket_invoice.data.contacts[0].contact_name if ticket_invoice.data.contacts and
                                                                         ticket_invoice.data.contacts[
                                                                             0].contact_name != '' else '_',
            invoice_date=str(ticket_invoice.data.invoice_date.date()) if ticket_invoice.data.invoice_date != None else '_',
            due_date=str(ticket_invoice.data.due_date) if ticket_invoice.data.due_date != None else '_',
            contact_email=ticket_invoice.data.contacts[0].contact_email if ticket_invoice.data.contacts and
                                                                           ticket_invoice.data.contacts[
                                                                               0].contact_email != '' else '_',
            billing_address=ticket_invoice.data.billing_address if ticket_invoice.data.billing_address != '' else '_',
            po_number=ticket_invoice.data.po_number if ticket_invoice.data.po_number is not None and ticket_invoice.data.po_number != '' else '_',
            payment_terms=ticket_invoice.data.payment_terms if ticket_invoice.data.payment_terms != '' else '_',
            locations=locations
        )

        if ticket_invoice.data.jobutrax:
            j = ticket_invoice.data.jobutrax
            top_table.major = j.major
            top_table.minor = j.minor
            if j.cost_center_number:
                top_table.cost_center_number = j.cost_center_number
                top_table.dont_print_list = ['afe_number']
            elif j.afe_number:
                top_table.afe_number = j.afe_number
                top_table.dont_print_list = ['cost_center_number']
            else:
                top_table.dont_print_list = ['afe_number', 'cost_center_number', 'major', 'minor']

        service_contract_tickets = ServiceContractTicket.objects.filter(ticket_invoice=ticket_invoice)
        tickets = Ticket.objects.filter(id__in=[ticket.ticket.id for ticket in service_contract_tickets])
        (charges_table,
         ticket_type,
         total_oil_volume,
         total_solid_volume,
         total_water_volume,
         total_quantity) = self.create_charges_table(tickets)

        sub_total = ticket_invoice.data.total_cost
        tax = ticket_invoice.data.tax
        balance_due = ticket_invoice.data.balance_due
        company_id = ticket_invoice.dbreplication_client_company.client_company.id
        printableTemplateCode = "ticket_invoice"
        printable = PrintableTemplate.objects.get(code=printableTemplateCode, company__id=company_id)
        context = {
            'ticket_invoice': ticket_invoice,
            'top_table': top_table,
            'charges_table': charges_table,
            'sub_total': sub_total,
            'tax': tax,
            'ticket_type': ticket_type,
            'balance_due': balance_due,
            'total_solid_volume': total_solid_volume,
            'total_water_volume': total_water_volume,
            'total_oil_volume': total_oil_volume,
            'total_quantity': total_quantity,
        }
        template = Template(printable.content)
        html_rendered = template.render(Context(context))
        ticket_pdfs = []
        service_contract_tickets = ServiceContractTicket.objects.filter(
            ticket_invoice=ticket_invoice
        ).order_by("ticket__created")
        with ThreadPoolExecutor(max_workers=3) as executor:
            # Generate ticket PDFs and invoice PDF concurrently
            ticket_pdfs_future = executor.submit(
                lambda: list(executor.map(generate_ticket_pdf, service_contract_tickets))
            )
            invoice_pdf_future = executor.submit(generate_invoice_pdf, html_rendered)
            # Wait for the results
            ticket_pdfs = ticket_pdfs_future.result()
            ticket_invoice_pdf = invoice_pdf_future.result()
            ticket_pdfs.insert(0, ticket_invoice_pdf)

        invoice_and_tickets_pdf = pdfUtils.merge_PDFs(ticket_pdfs)
        pdf = pdfUtils.append_attachments_to_pdf(ticket_invoice, invoice_and_tickets_pdf)
        pdf = pdfUtils.compress_pdf(pdf)
        return pdf

    def create_charges_table(self, tickets):
        charges_table = []
        ticket_type = ''
        total_solid_volume = 0
        total_water_volume = 0
        total_oil_volume = 0
        total_quantity = 0

        for ticket in tickets.order_by('created'):
            calculate_service_totals = False

            if not ticket_type:
                ticket_type = ticket.ticket_type

            sorted_products_payload = dict(
                sorted(
                    ticket.data.additional_fields.products_payload.items(),
                    key=lambda item: not item[1].get('autoCalculateQuantity', False)
                )
            )

            for productName, data in sorted_products_payload.items():
                solid_percentage = None
                solid_volume = None
                water_percentage = None
                water_volume = None
                oil_percentage = None
                oil_volume = None

                if data['autoCalculateQuantity']:
                    solid_percentage = ticket.data.quantity_data.solid_percentage
                    solid_volume = ticket.data.quantity_data.solid_volume
                    water_percentage = ticket.data.quantity_data.water_percentage
                    water_volume = ticket.data.quantity_data.water_volume
                    oil_percentage = ticket.data.quantity_data.oil_percentage
                    oil_volume = ticket.data.quantity_data.oil_volume
                    calculate_service_totals = True

                charges_table.append(self.ChargesTable(
                    ticket=ticket.code,
                    description=productName,
                    carrier_company=ticket.data.print_data.carrier_name,
                    quantity=data["quantity"],
                    uom=data["uom"],
                    trucking_ticket_number=ticket.data.print_data.trucking_ticket_number,
                    solid_percentage=solid_percentage,
                    solid_volume=solid_volume,
                    water_percentage=water_percentage,
                    water_volume=water_volume,
                    oil_percentage=oil_percentage,
                    oil_volume=oil_volume,
                    rate=data["price"],
                    total=data["totalCost"]
                ))

                total_quantity = total_quantity + float(data["quantity"])

            if ticket.data.quantity_data.oil_volume is not None and calculate_service_totals:
                total_oil_volume = total_oil_volume + float(ticket.data.quantity_data.oil_volume)

            if ticket.data.quantity_data.water_volume is not None and calculate_service_totals:
                total_water_volume = total_water_volume + float(ticket.data.quantity_data.water_volume)

            if ticket.data.quantity_data.solid_volume is not None and calculate_service_totals:
                total_solid_volume = total_solid_volume + float(ticket.data.quantity_data.solid_volume)

        return charges_table, ticket_type, total_oil_volume, total_solid_volume, total_water_volume, total_quantity


class InvoiceStatus(models.TextChoices):
    OPEN = "Open", _("Open")
    APPROVAL_SENT = "Approval Sent", _("Approval Sent")
    APPROVAL_REJECTED = "Approval Rejected", _("Approval Rejected")
    APPROVED = "Approved", _("Approved")
    INVOICE_SENT = "Invoice Sent", _("Invoice Sent")
    PAID = "Paid", _("Paid")
    VOID = "Void", _("Void")
    COMPLETE = "Complete", _("Complete")


class ApprovalRequestSchema(BaseModel):
    requester: str
    request_date: timezone.datetime
    unique_identifier: str


class PaymentRequestSchema(BaseModel):
    requester: str
    request_date: timezone.datetime
    unique_identifier: str


class JobutraxRequest(BaseModel):
    request_date: timezone.datetime = None
    attachment_status: Literal["Queued", "Sent", "Rejected", "Approved"] = None
    attachment_sent: timezone.datetime = None
    attachment_message: str = None
    invoice_status: Literal["Queued", "Sent", "Rejected", "Approved"] = None
    invoice_sent: timezone.datetime = None
    invoice_message: str = None
    cost_center_number: str = None
    afe_number: str = None
    major: str = None
    minor: str = None


class ContactSchema(BaseModel):
    contact_email: str = None
    contact_name: Optional[str] = None


class InvoiceTickets(BaseModel):
    id: UUID = Field(default_factory=uuid4)
    url: str = None
    dirty: bool = None
    model: str = None
    schema_: Dict = Field(alias='schema', default={})
    ticket: UUID = Field(default_factory=uuid4)
    options: Dict = {}
    ticket_invoice: str = None
    service_contract: UUID = Field(default_factory=uuid4)
    ticket_invoice_name: str = None


class InvoiceData(BaseModel):
    payment_terms: Optional[str] = None
    contacts: List[ContactSchema] = []
    billing_address: Optional[str] = None
    internal_notes: Optional[str] = None
    invoice_memo: Optional[str] = None
    invoice_date: Optional[timezone.datetime] = None
    due_date: Optional[datetime.date] = None
    status: Optional[str] = None
    ticket_ids: Optional[list[InvoiceTickets]] = []
    total_cost: Optional[float] = None
    tax: Optional[float] = None
    tax_rate: Optional[float] = None
    balance_due: Optional[float] = None
    approved: Optional[bool] = None
    approval_note: Optional[str] = None
    reject_note: Optional[str] = None
    approval_date: Optional[timezone.datetime] = None
    export_data: Optional[Dict] = None
    approval_requests: Optional[List[ApprovalRequestSchema]] = None
    payment_requests: Optional[List[PaymentRequestSchema]] = None
    jobutrax: JobutraxRequest = JobutraxRequest()
    po_number: Optional[str] = None


class TicketInvoice(UUIDModel, CustomJsonDataMixin, TimeStampedModel, ActiveMixin, NamedMixin, PrintableInvoiceMixin,
                    DBReplicationMixin):
    class Meta(NamedMixin.Meta):
        ordering = ["-created"]
        verbose_name = _("Ticket Invoice")
        verbose_name_plural = _("Ticket Invoices")

    data = JSONSchemedField(schema=InvoiceData, default=InvoiceData, null=True)
    customer = models.ForeignKey("vant_tboss.Company", on_delete=models.PROTECT, related_name="invoice_set", null=True,
                                 blank=True)
    producer = models.ForeignKey("vant_tboss.Company", on_delete=models.PROTECT, related_name="invoices_set", null=True,
                                 blank=True)
    service_contract = models.ForeignKey(ServiceContract, on_delete=models.PROTECT, related_name="invoices_set",
                                         null=True, blank=True)
    locations = models.ManyToManyField(Location, related_name="invoices_set", blank=True, default=None)
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, related_name="service_contract_ticket_set",
                                   null=True, blank=True)
    attachments = GenericRelation(Attachment)

    def remove_tickets_from_invoice(self):
        from vantedge.tboss.models import ServiceContractTicket
        # Reset the tickets to a default state and remove the invoiced tickets.
        service_contract_tickets = ServiceContractTicket.objects.filter(
            ticket__data__additional_fields__state=GIPTicketStatus.INVOICED,
            ticket_invoice=self
        )
        for service_contract_ticket in service_contract_tickets.all():
            service_contract_ticket.ticket.data.additional_fields.state = GIPTicketStatus.APPROVED
            service_contract_ticket.ticket.save(update_fields=['data'])
        ServiceContractTicket.objects.filter(ticket_invoice=self).update(ticket_invoice=None)

    @staticmethod
    def update_total_cost(tickets):
        from vantedge.tboss.models import ServiceContractTicket, Ticket
        from vantedge.tboss.models.service_contract import custom_round

        service_contract_tickets = ServiceContractTicket.objects.filter(ticket__in=tickets)
        # getting unique ticket invocies
        ticket_invoice_ids = set(
            service_contract_tickets.exclude(
                ticket_invoice__isnull=True).values_list("ticket_invoice_id", flat=True)
            )

        for ticket_invoice in ticket_invoice_ids:
            ticket_invoice = TicketInvoice.objects.get(id=ticket_invoice)
            # getting all are tickets in ticket invoice
            ticket_ids = [service_contract.ticket for service_contract in ticket_invoice.data.ticket_ids]
            costs = Ticket.objects.filter(
                id__in=ticket_ids
                ).values_list("data__additional_fields__ticket_total_cost", flat=True)

            ticket_invoice.data.total_cost = custom_round(sum(costs),2)
            ticket_invoice.save(update_fields=["data"])


    def delete(self, *args, **kwargs):
        if self.data.status == InvoiceStatus.OPEN:
            self.remove_tickets_from_invoice()
            super().delete(*args, **kwargs)
        else:
            raise ValueError("Cannot delete a non-open invoice")

    def save(self, *args, **kwargs):
        # If the contract does not require approval, approve the invoice
        if not self.service_contract.data.ticket_approval_required:
            self.data.approved = True
            self.data.approval_note = "Contract does not require ticket approval."
        # If approval is required set the note to approval required
        if self.service_contract.data.ticket_approval_required and self.data.approved == False:
            self.data.approved = False
            self.data.approval_note = "Approval Required."
        return super().save(*args, **kwargs)

    @property
    def tickets(self):
        return list(map(lambda item: item.ticket, self.data.ticket_ids))

    @classmethod
    def find_invoice(cls, unique_identifier):
        return cls.objects.filter(
            Q(data__approval_requests__contains=[{'unique_identifier': unique_identifier}]) |
            Q(data__payment_requests__contains=[{'unique_identifier': unique_identifier}])
        ).first()

    def find_approval_request(self, unique_identifier):
        if not self.data.approval_requests:
            return None
        items = [i for i in self.data.approval_requests if i.unique_identifier == unique_identifier]
        return (items and items[0]) or None

    def set_approved(self, note):
        self.data.approved = True
        self.data.status = InvoiceStatus.APPROVED
        self.data.approval_date = timezone.now()
        self.data.approval_note = note

        self.save(update_fields=["data"])

    def set_rejected(self, note):
        self.data.status = InvoiceStatus.APPROVAL_REJECTED
        self.data.approval_date = timezone.now()
        self.data.reject_note = note

        self.save(update_fields=["data"])

    def send_approval(self, request):
        user = request.user
        if not self.data.status in [
            InvoiceStatus.OPEN,
            InvoiceStatus.APPROVAL_REJECTED,
            InvoiceStatus.APPROVAL_SENT,
            InvoiceStatus.APPROVED
        ]:
            return self.code, False, f"Status Invoice {self.code} is {self.data.status}"

        if not self.data.approval_requests:
            self.data.approval_requests = []

        emails = [item.contact_email for item in self.data.contacts]

        if not emails:
            return self.code, False, f"Company {self.customer.name} has no contact"

        unique_identifier = str(uuid4())
        url_approve_page = reverse("external-invoice-invoice-approval-page")

        url_approve_page = request.build_absolute_uri(f"{url_approve_page}?unique_identifier={unique_identifier}")

        context = dict(
            sender=user.name,
            code=self.code,
            url_approve_page=url_approve_page,
        )

        send_invoice_approval(emails, context)

        if not self.data.approval_requests:
            self.data.approval_requests = []

        self.data.status = InvoiceStatus.APPROVAL_SENT
        self.data.approval_requests.append({
            "requester": user.username,
            "request_date": timezone.now(),
            "unique_identifier": unique_identifier
        })

        self.save(update_fields=["data"])
        return self.code, True, f"Invoice {self.code} sent for approval"

    def send_payment(self, request):
        user = request.user

        if not self.data.status == InvoiceStatus.APPROVED and not self.data.status == InvoiceStatus.INVOICE_SENT:
            return self.code, False, f"Invoice {self.code} needs to be approved. Status: {self.data.status}"

        if not self.data.payment_requests:
            self.data.payment_requests = []

        emails = [item.contact_email for item in self.data.contacts]

        if not emails:
            return self.code, False, f"Invoice {self.code} does not contain any contacts"

        unique_identifier = str(uuid4())
        url_payment_page = reverse("external-invoice-invoice-payment-page")

        url_payment_page = request.build_absolute_uri(f"{url_payment_page}?unique_identifier={unique_identifier}")

        context = dict(
            sender=user.name,
            code=self.code,
            url_payment_page=url_payment_page,
        )

        send_invoice_payment(emails, context, user.email)

        self.data.status = InvoiceStatus.INVOICE_SENT

        self.data.payment_requests.append({
            "requester": user.username,
            "request_date": timezone.now(),
            "unique_identifier": unique_identifier
        })

        self.save(update_fields=["data"])

        return self.code, True, f"Invoice {self.code} sent for payment"

    def generate_pidx(self, filename, filetype):
        ticket_invoice = self
        service_contract_tickets = ticket_invoice.service_contract_ticket_set.all()

        invoice_properties = InvoiceProperties(
            InvoiceNumber=ticket_invoice.code,
            InvoiceDate=ticket_invoice.data.invoice_date.strftime("%Y-%m-%d"),
            SellerDUNS=ticket_invoice.dbreplication_client_company.data.duns_number,
            BuyerDUNS=ticket_invoice.customer.data.integration.duns_number,
            JobLocationIdentifier=ticket_invoice.locations.first().code if ticket_invoice.locations.first() else 'No Location',
            FileName=filename,
            FileType=filetype
        )

        invoice_items = []
        for i, service_contract_ticket in enumerate(service_contract_tickets):
            sold_products = list(filter(lambda x: x["quantity"] > 0,
                                        service_contract_ticket.ticket.data.additional_fields.products_payload.values()))
            for j, sold_product in enumerate(sold_products):
                invoice_items.append(
                    InvoiceItem(
                        LineItemNumber=i + j,
                        Quantity=sold_product["quantity"],
                        UnitOfMeasureCode=sold_product["uom"],
                        LineItemDescription=sold_product["productName"],
                        FieldTicketNumber=service_contract_ticket.ticket.code,
                        FieldTicketDate=service_contract_ticket.ticket_invoice.data.invoice_date.strftime("%Y-%m-%d"),
                        MonetaryAmount=sold_product["price"],
                        TaxRate=service_contract_ticket.ticket_invoice.data.tax_rate * 100,
                        TaxMonetaryAmount=service_contract_ticket.ticket_invoice.data.tax,
                        AFENumber=service_contract_ticket.ticket_invoice.data.jobutrax.afe_number or '',
                        CostCenterNumber=service_contract_ticket.ticket_invoice.data.jobutrax.cost_center_number or '',
                        Major=service_contract_ticket.ticket_invoice.data.jobutrax.major or '',
                        Minor=service_contract_ticket.ticket_invoice.data.jobutrax.minor or ''
                    )
                )

        pidx = PIDX(invoice_properties, invoice_items)
        pidx.generate()
        return pidx.to_string()

    def send_jobutrax(self):
        if (self.data.jobutrax.invoice_status == "Sent"):
            return

        pandell = Pandell(client_company=self.dbreplication_client_company)
        attachment_pdf = self.printed_invoice()
        filename = f"{self.code}.pdf"
        response_attachment = pandell.send_invoice_attachment(self.code, filename, attachment_pdf)

        self.data.jobutrax.attachment_message = response_attachment.text
        self.data.jobutrax.attachment_sent = timezone.now()

        if response_attachment.ok:
            self.data.jobutrax.attachment_status = "Sent"
        else:
            self.data.jobutrax.attachment_status = "Rejected"

        xml_data = self.generate_pidx(filename, "application/pdf")
        response = pandell.create_invoice(xml_data)
        self.data.jobutrax.invoice_message = response.text
        self.data.jobutrax.invoice_sent = timezone.now()

        if response.ok:
            self.data.jobutrax.invoice_status = "Sent"
            self.data.jobutrax.invoice_message = "Invoice was successfully sent to Jobutrax"
            self.data.status = InvoiceStatus.INVOICE_SENT
        else:
            self.data.jobutrax.invoice_status = "Rejected"

        self.save(update_fields=["data"])

        return response.ok

    @staticmethod
    def format_billing_address(address):
        street = address.street
        city = address.city
        province = address.province
        country = address.country
        postal_code = address.postal_code

        address_parts = [street, city, province, country, postal_code]

        formatted_address = ', '.join(part for part in address_parts if part)

        return formatted_address

    @staticmethod
    def create_invoice_serializer_data_by_contract(contract_id, invoice_tickets, tax_rate, created_by_user):
        from vantedge.tboss.models import ServiceContractTicket

        service_contract = ServiceContract.objects.get(id=contract_id)
        created_by_server = service_contract.created_by_server
        customer_id = service_contract.customer_id
        producer_id = service_contract.producer_id
        total_cost = 0
        contract_ticket_ids = []

        for ticket in invoice_tickets[contract_id]:
            if ticket.data.additional_fields.ticket_total_cost:
                total_cost = total_cost + ticket.data.additional_fields.ticket_total_cost

            service_contract_ticket = ServiceContractTicket.objects.filter(
                ticket=ticket,
                service_contract=service_contract
            ).first()
            ticket_id = {
                'id': service_contract_ticket.id,
                'service_contract': service_contract.id,
                'ticket': ticket.id
            }
            contract_ticket_ids.append(ticket_id)

        tax = round(total_cost * tax_rate, 2)
        balance_due = round(total_cost + tax, 2)
        today = now()
        billing_address = TicketInvoice.format_billing_address(service_contract.customer.data.address)

        invoice_data = {
            'approval_date': None,
            'approval_note': '',
            'approved': False,
            'balance_due': balance_due,
            'invoice_date': today.strftime('%Y-%m-%d %H:%M'),
            'due_date': (today + datetime.timedelta(days=30)).strftime('%Y-%m-%d'),
            'payment_terms': InvoicePaymentTerms.NET_30,
            'status': InvoiceStatus.OPEN,
            'tax': tax,
            'tax_rate': tax_rate,
            'ticket_ids': contract_ticket_ids,
            'total_cost': total_cost,
            'billing_address': billing_address,
            'po_number': service_contract.data.po_number,
        }

        serializer_data = {
            'customer': customer_id,
            'producer': producer_id,
            'created_by': created_by_user.id,
            'created_by_server': created_by_server.id,
            'service_contract': service_contract.id,
            'data': invoice_data
        }

        return serializer_data
