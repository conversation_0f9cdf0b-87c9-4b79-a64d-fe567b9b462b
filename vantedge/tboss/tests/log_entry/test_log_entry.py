from django.contrib.contenttypes.models import ContentType
from rest_framework import status

from vantedge.tboss.models import ServiceContractProduct, ServiceContract
from vantedge.tboss.tests.factories.log_entry import LogEntryFactory
from vantedge.tboss.tests.factories.product import ProductFactory
from vantedge.tboss.tests.factories.service_contract import ServiceContractFactory
from vantedge.tboss.tests.factories.service_contract_product import ServiceContractProductFactory
from vantedge.tboss.tests.factories.sync.server import DBReplicationServerFactory
from vantedge.wheelhouse.tests.base.base_api_test_case import BaseTestCase


class TestLogEntry(BaseTestCase):
    endpoint = 'terminalboss/v3/logentry/'

    def test_log_entry__product_has_entries__contract_returns_product_entries(self):
        service_contract_product_content_type = ContentType.objects.get_for_model(ServiceContractProduct)
        service_contract_content_type = ContentType.objects.get_for_model(ServiceContract)
        product = ProductFactory()
        service_contract = ServiceContractFactory()
        service_contract_product = ServiceContractProductFactory(
            product=product,
            service_contract=service_contract,
            dbreplication_client_company=self.user.company.terminalboss
        )
        dbreplication_server = DBReplicationServerFactory()
        service_contract_product_log_entry = LogEntryFactory(
            content_type=service_contract_product_content_type,
            entity_id=service_contract_product.id,
            origin_server=dbreplication_server,
            action_type='updated',
            status='updated',
            dbreplication_client_company=self.user.company.terminalboss
        )
        service_contract_log_entry = LogEntryFactory(
            content_type=service_contract_content_type,
            entity_id=service_contract.id,
            origin_server=dbreplication_server,
            action_type='updated',
            status='updated',
            dbreplication_client_company=self.user.company.terminalboss
        )

        response = self.get(self.endpoint)

        self.assertEqual(status.HTTP_200_OK, response.status_code)
        self.assertEqual(2, len(response.data))
        self.assertEqual(str(service_contract_product_log_entry.entity_id), response.data[0]['entity_id'])
        self.assertEqual(str(service_contract_log_entry.entity_id), response.data[1]['entity_id'])
