from io import <PERSON><PERSON>
from unittest import mock

from django.core.management import call_command
from vant_tboss.models import Company

from vantedge.tboss.tests.factories.company import TBossCompanyFactory, TerminalBossClientCompanyFactory
from vantedge.wheelhouse.tests.base.base_api_test_case import BaseTestCase


class TestCompanyDispatchCommand(BaseTestCase):
    def test_update_company_dispatch_command__dispatch_is_object__updates_dispatch_to_list(self):
        gip_tboss_company = TerminalBossClientCompanyFactory(name='Green Impact Partners')
        tboss_company = TBossCompanyFactory(
            name='fake company',
            data={
                "integration": {"sap_code": 4723847},
                "dispatch": {
                    "contact": "john",
                    "phone": '************',
                    "email": '<EMAIL>',
                    "notes": 'some notes would go here'
                }
            },
            dbreplication_client_company=gip_tboss_company,
        )

        output = StringIO()
        with mock.patch('sys.stdout', new=output):
            call_command('update_company_dispatch')

        updated_company = Company.objects.get(id=tboss_company.id)
        updated_dispatch = [{
            "contact": "john",
            "phone": '************',
            "email": '<EMAIL>',
            "notes": 'some notes would go here'
        }]

        self.assertEqual(updated_dispatch, updated_company.data.dispatch)
        self.assertIsInstance(updated_company.data.dispatch, list)
        self.assertIn('Number of updated companies: 1', output.getvalue())

    def test_update_company_dispatch_command__dispatch_is_list__skips_company(self):
        dispatch_data = [{
            "contact": "john",
            "phone": '************',
            "email": '<EMAIL>',
            "notes": 'some notes would go here'
        }]
        gip_tboss_company = TerminalBossClientCompanyFactory(name='Green Impact Partners')
        tboss_company = TBossCompanyFactory(
            name='fake company',
            data={
                "integration": {"sap_code": 4723847},
                "dispatch": dispatch_data
            },
            dbreplication_client_company=gip_tboss_company,
        )

        output = StringIO()
        with mock.patch('sys.stdout', new=output):
            call_command('update_company_dispatch')

        company = Company.objects.get(id=tboss_company.id)

        self.assertEqual(company.data.dispatch, dispatch_data)
        self.assertIn('All companies already in correct dispatch format', output.getvalue())

    def test_update_company_dispatch_command__multiple_tboss_companies__skips_non_gip_company(self):
        gip_tboss_company = TerminalBossClientCompanyFactory(name='Green Impact Partners')
        TBossCompanyFactory(
            name='non gip company',
            data={
                "integration": {"sap_code": 12345},
                "dispatch": {
                    "contact": "john",
                    "phone": '************',
                    "email": '<EMAIL>',
                    "notes": 'some notes would go here'
                }
            }
        )
        TBossCompanyFactory(
            name='gip company',
            data={
                "integration": {"sap_code": 4723847},
                "dispatch": {
                    "contact": "john",
                    "phone": '************',
                    "email": '<EMAIL>',
                    "notes": 'some notes would go here'
                }
            },
            dbreplication_client_company=gip_tboss_company,
        )

        output = StringIO()
        with mock.patch('sys.stdout', new=output):
            call_command('update_company_dispatch')

        tboss_companies = Company.objects.all()

        self.assertEqual(2, len(tboss_companies))
        self.assertIn('Number of updated companies: 1', output.getvalue())

        gip_company = Company.objects.get(dbreplication_client_company=gip_tboss_company)

        updated_dispatch = [{
            "contact": "john",
            "phone": '************',
            "email": '<EMAIL>',
            "notes": 'some notes would go here'
        }]

        self.assertEqual(updated_dispatch, gip_company.data.dispatch)
        self.assertIsInstance(gip_company.data.dispatch, list)

        non_tboss_company = Company.objects.get(dbreplication_client_company__isnull=True)

        self.assertNotEqual(updated_dispatch, non_tboss_company.data.dispatch)
        self.assertNotIsInstance(non_tboss_company.data.dispatch, list)
