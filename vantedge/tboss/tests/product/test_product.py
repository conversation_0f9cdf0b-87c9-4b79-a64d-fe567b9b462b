from django.contrib.contenttypes.models import ContentType
from rest_framework import status

from vantedge.tboss.models import Product
from vantedge.tboss.tests.factories.product import ProductFactory
from vantedge.tboss.tests.factories.sync.config import DBReplicationConfigFactory
from vantedge.tboss.tests.factories.sync.server import DBReplicationServerFactory
from vantedge.wheelhouse.tests.base.base_api_test_case import BaseTestCase


class TestProduct(BaseTestCase):
    endpoint = 'terminalboss/v3/product/'

    def test_post__product_choices__return_filtered_products(self):
        replication_server = DBReplicationServerFactory()
        content_type = ContentType.objects.get_for_model(Product)
        replication_config = DBReplicationConfigFactory(bidirectional=False, content_type=content_type)
        replication_config.servers.add(replication_server)
        parent_product = ProductFactory(
            dbreplication_client_company=self.user.company.terminalboss,
            dbreplication_config=replication_config
        )
        expected_with_parent_filter = ProductFactory(
            parent=parent_product,
            dbreplication_client_company=self.user.company.terminalboss,
            dbreplication_config=replication_config
        )
        product_no_parent = ProductFactory(
            dbreplication_client_company=self.user.company.terminalboss,
            dbreplication_config=replication_config
        )

        data = {
            'parent__name': parent_product.name,
            'servers': [replication_server.id]
        }

        response = self.post(self.endpoint + 'choices/', data)

        self.assertEqual(status.HTTP_200_OK, response.status_code)
        self.assertEqual(1, response.data['count'])
        self.assertEqual(expected_with_parent_filter.name, response.data['suggestions'][0]['label'])
        self.assertNotEquals(product_no_parent.name, response.data['suggestions'][0]['label'])
        self.assertTrue(Product.objects.filter(pk=product_no_parent.pk).exists())

    def test_post__filter_exclude_product_code__return_filtered_products(self):
        replication_server = DBReplicationServerFactory()
        content_type = ContentType.objects.get_for_model(Product)
        replication_config = DBReplicationConfigFactory(bidirectional=False, content_type=content_type)
        replication_config.servers.add(replication_server)
        expected_product = ProductFactory(
            dbreplication_client_company=self.user.company.terminalboss,
            dbreplication_config=replication_config,
            code='Find Me'
        )

        filtered_product = ProductFactory(
            dbreplication_client_company=self.user.company.terminalboss,
            dbreplication_config=replication_config,
            code='Ignore Me'
        )

        data = {
            'product__code__exclude': 'Ignore Me',
            'servers': [replication_server.id]
        }

        response = self.post(self.endpoint + 'choices/', data)

        self.assertEqual(status.HTTP_200_OK, response.status_code)
        self.assertEqual(1, response.data['count'])
        self.assertEqual(expected_product.name, response.data['suggestions'][0]['label'])
        self.assertNotEquals(filtered_product.name, response.data['suggestions'][0]['label'])
        self.assertTrue(Product.objects.filter(pk=filtered_product.pk).exists())
