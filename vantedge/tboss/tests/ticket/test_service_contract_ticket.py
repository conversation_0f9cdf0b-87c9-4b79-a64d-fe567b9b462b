from unittest import mock

from custom_apps.green_impact_partners.schemas import GIPTicketStatus
from vantedge.tboss.models import TicketInvoice, InvoiceData
from vantedge.tboss.tests.factories.location import LocationFactory
from vantedge.tboss.tests.factories.product import ProductFactory
from vantedge.tboss.tests.factories.service_contract import ServiceContractFactory
from vantedge.tboss.tests.factories.service_contract_ticket import ServiceContractTicketFactory
from vantedge.tboss.tests.factories.tboss_user import TBossUserFactory
from vantedge.tboss.tests.factories.ticket import TicketFactory
from vantedge.tboss.tests.factories.ticket_invoice import TicketInvoiceFactory
from vantedge.wheelhouse.tests.base.base_api_test_case import BaseTestCase


class TestServiceContractTicket(BaseTestCase):
    def test_delete__service_contract_ticket__update_invoice_status(self):
        expected_status = GIPTicketStatus.VOID
        service_contract = ServiceContractFactory(data={'ticket_approval_required': False})
        invoice_data = InvoiceData(
            tax=887.5,
            status="Draft",
            approved=False,
            due_date="2024-06-29",
            tax_rate=0.05,
            ticket_ids=[],
            total_cost=17750,
            balance_due=18637.5,
            contact_name="bob",
            invoice_date="2024-05-30 00:00",
            invoice_memo="",
            approval_date=None,
            approval_note="Approval Required.",
            contact_email="",
            payment_terms="Net 30",
            internal_notes="",
            billing_address=""
        )
        invoice = TicketInvoiceFactory(service_contract=service_contract, data=invoice_data)
        product = ProductFactory()
        location = LocationFactory()
        user = TBossUserFactory()

        with mock.patch('vantedge.util.classes.ProxyCompanyMixin.can_handle_company', return_value=True):
            ticket = TicketFactory(
                user=user,
                company=service_contract.producer,
                location=location,
                product=product
            )
            service_contract_ticket = ServiceContractTicketFactory(
                service_contract=service_contract,
                ticket_invoice=invoice,
                ticket=ticket
            )

            self.assertEqual(invoice.data.status, 'Draft')

            service_contract_ticket.delete()

            updated_invoice = TicketInvoice.objects.get(id=invoice.id)
            self.assertEqual(updated_invoice.data.status, expected_status)

    def test_delete__multiple_service_contracts__status_not_updated(self):
        expected_status = GIPTicketStatus.DRAFT
        service_contract = ServiceContractFactory(data={'ticket_approval_required': False})
        invoice_data = InvoiceData(
            tax=887.5,
            status="Draft",
            approved=False,
            due_date="2024-06-29",
            tax_rate=0.05,
            ticket_ids=[],
            total_cost=17750,
            balance_due=18637.5,
            contact_name="bob",
            invoice_date="2024-05-30 00:00",
            invoice_memo="",
            approval_date=None,
            approval_note="Approval Required.",
            contact_email="",
            payment_terms="Net 30",
            internal_notes="",
            billing_address=""
        )
        invoice = TicketInvoiceFactory(service_contract=service_contract, data=invoice_data)
        product = ProductFactory()
        location = LocationFactory()
        user = TBossUserFactory()

        with mock.patch('vantedge.util.classes.ProxyCompanyMixin.can_handle_company', return_value=True):
            ticket = TicketFactory(
                user=user,
                company=service_contract.producer,
                location=location,
                product=product
            )
            ticket_2 = TicketFactory(
                user=user,
                company=service_contract.producer,
                location=location,
                product=product
            )
            service_contract_ticket = ServiceContractTicketFactory(
                service_contract=service_contract,
                ticket_invoice=invoice,
                ticket=ticket
            )
            ServiceContractTicketFactory(
                service_contract=service_contract,
                ticket_invoice=invoice,
                ticket=ticket_2
            )

            self.assertEqual(invoice.data.status, expected_status)

            service_contract_ticket.delete()

            updated_invoice = TicketInvoice.objects.get(id=invoice.id)
            self.assertEqual(updated_invoice.data.status, expected_status)

    def test_delete__invoice_price_contains_data__price_set_to_zero(self):
        service_contract = ServiceContractFactory(data={'ticket_approval_required': False})
        balance_due = 999
        total_cost = 200
        tax = 234
        invoice_data = InvoiceData(
            tax=tax,
            status="Draft",
            approved=False,
            due_date="2024-06-29",
            tax_rate=0.05,
            ticket_ids=[],
            total_cost=total_cost,
            balance_due=balance_due,
            contact_name="bob",
            invoice_date="2024-05-30 00:00",
            invoice_memo="",
            approval_date=None,
            approval_note="Approval Required.",
            contact_email="",
            payment_terms="Net 30",
            internal_notes="",
            billing_address=""
        )
        invoice = TicketInvoiceFactory(service_contract=service_contract, data=invoice_data)
        product = ProductFactory()
        location = LocationFactory()
        user = TBossUserFactory()

        with mock.patch('vantedge.util.classes.ProxyCompanyMixin.can_handle_company', return_value=True):
            ticket = TicketFactory(
                user=user,
                company=service_contract.producer,
                location=location,
                product=product
            )
            service_contract_ticket = ServiceContractTicketFactory(
                service_contract=service_contract,
                ticket_invoice=invoice,
                ticket=ticket
            )

            self.assertEqual(invoice.data.balance_due, balance_due)
            self.assertEqual(invoice.data.total_cost, total_cost)
            self.assertEqual(invoice.data.tax, tax)

            service_contract_ticket.delete()

            updated_invoice = TicketInvoice.objects.get(id=invoice.id)
            self.assertEqual(updated_invoice.data.balance_due, 0)
            self.assertEqual(updated_invoice.data.total_cost, 0)
            self.assertEqual(updated_invoice.data.tax, 0)

    def test_delete__ticket_deleted__invoice_set_to_zero(self):
        service_contract = ServiceContractFactory(data={'ticket_approval_required': False})
        balance_due = 999
        total_cost = 200
        tax = 234
        invoice_data = InvoiceData(
            tax = tax,
            status = "Draft",
            approved = False,
            due_date = "2024-06-29",
            tax_rate = 0.05,
            ticket_ids = [],
            total_cost = total_cost,
            balance_due = balance_due,
            contact_name = "bob",
            invoice_date = "2024-05-30 00:00",
            invoice_memo = "",
            approval_date = None,
            approval_note = "Approval Required.",
            contact_email = "",
            payment_terms = "Net 30",
            internal_notes = "",
            billing_address = ""
        )

        invoice = TicketInvoiceFactory(service_contract=service_contract, data=invoice_data)
        product = ProductFactory()
        location = LocationFactory()
        user = TBossUserFactory()

        with mock.patch('vantedge.util.classes.ProxyCompanyMixin.can_handle_company', return_value=True):
            ticket = TicketFactory(
                user=user,
                company=service_contract.producer,
                location=location,
                product=product
            )
            ServiceContractTicketFactory(
                service_contract=service_contract,
                ticket_invoice=invoice,
                ticket=ticket
            )

            self.assertEqual(invoice.data.balance_due, balance_due)
            self.assertEqual(invoice.data.total_cost, total_cost)
            self.assertEqual(invoice.data.tax, tax)

            ticket.delete()

            updated_invoice = TicketInvoice.objects.get(id=invoice.id)
            self.assertEqual(updated_invoice.data.balance_due, 0)
            self.assertEqual(updated_invoice.data.total_cost, 0)
            self.assertEqual(updated_invoice.data.tax, 0)
