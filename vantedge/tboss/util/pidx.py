from vantedge.tboss.models import TicketInvoice
from vantedge.util.pidx.schemas.data import InvoiceProperties, InvoiceItem
from vantedge.util.pidx.main import PIDX

def generate_pidx(ticket_invoice_id):


    ticket_invoice = TicketInvoice.objects.get(id=ticket_invoice_id)
    service_contract_tickets = ticket_invoice.service_contract_ticket_set.all()

    invoice_properties = InvoiceProperties(
        InvoiceNumber = ticket_invoice.code,
        InvoiceDate = ticket_invoice.data["invoice_date"],
        SellerDUNS = "123456789",
        BuyerDUNS = "987654321",
        JobLocationIdentifier = ticket_invoice.locations.first().code,
        FileName = "generate_pdf.pdf",
        FileType = "pdf"
    )


    invoice_items = []
    for i, service_contract_ticket in enumerate(service_contract_tickets):
        sold_products = list(filter(lambda x: x["quantity"] > 0, service_contract_ticket.ticket.data.additional_fields.products_payload.values()))
        for j, sold_product in enumerate(sold_products):
            invoice_items.append(
                InvoiceItem(
                    LineItemNumber = i+j,
                    Quantity =  sold_product["quantity"],
                    UnitOfMeasureCode = sold_product["uom"],
                    LineItemDescription =  sold_product["productName"],
                    FieldTicketNumber = service_contract_ticket.ticket.code,
                    FieldTicketDate = service_contract_ticket.ticket_invoice.data["invoice_date"],
                    MonetaryAmount = sold_product["price"],
                    TaxRate = service_contract_ticket.ticket_invoice.data["tax_rate"],
                    TaxMonetaryAmount = service_contract_ticket.ticket_invoice.data["tax_rate"],
                    AFENumber=service_contract_ticket.ticket_invoice.data.jobutrax.afe_number or '',
                    CostCenterNumber=service_contract_ticket.ticket_invoice.data.jobutrax.cost_center_number or '',
                    Major=service_contract_ticket.ticket_invoice.data.jobutrax.major or '',
                    Minor=service_contract_ticket.ticket_invoice.data.jobutrax.minor or ''
                )
            )

    pidx = PIDX(invoice_properties, invoice_items)
    pidx.generate()
    return pidx.to_string()

