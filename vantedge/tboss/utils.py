from datetime import datetime
from dateutil.parser import parse, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>

def get_ticket_quantity_display(cls, obj):
    # ------- ensuring that the WH Ticket proxy method is used
    full_obj_dict = obj.__dict__.copy()
    full_obj_dict_keys = list(full_obj_dict.keys())
    for field in full_obj_dict_keys:
        if not hasattr(cls, field) or not getattr(cls, field):
            full_obj_dict.pop(field)
    return cls(**full_obj_dict).quantity_display


def convert_datetimes_to_tz(data, timezone, date_format=""):
    for key, val in data.items():
        if isinstance(val, datetime):
            data[key] = timezone.localize(val.replace(tzinfo=None))
            if date_format:
                data[key] = data[key].strftime(date_format)

        # causes issues with numbers
        # elif isinstance(val, str) and val:
        #     try:
        #         data[key] = parse(val)
        #         if data[key] and date_format:
        #             data[key] = data[key].strftime(date_format)
        #     except Exception as e:
        #         pass

        elif isinstance(val, dict):
            data[key] = convert_datetimes_to_tz(val, timezone)
    return data


def no_sync_signals_decorator(func):
    '''
    Decorator to disable the tboss -> wheelhouse sync signals for the decorated function.
    '''
    def wrapper(*args, **kwargs):
        from vant_dbreplication.signals.log_entry_signal import no_sync_signals
        with no_sync_signals():
            return func(*args, **kwargs)
    return wrapper
