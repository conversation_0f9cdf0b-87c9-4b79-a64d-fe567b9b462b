from django.apps import apps
from django_filters import Filter<PERSON>et, <PERSON><PERSON>n<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>
from django.db.models import Q, Case, When
from django.http import QueryDict

from rest_framework.response import Response
from rest_framework.decorators import action
from rest_framework.pagination import LimitOffsetPagination
from rest_framework.permissions import IsAuthenticated
from rest_framework.viewsets import ModelViewSet

from .mixins.viewsets import (
    MultiSerializerViewSetMixin,
    TBossCompanyMixin,
    ChoiceOptionsMixin,
    FormConfigurationMixin,
    is_valid_uuid
)
from .mixins.filters import (
    DBReplicationFilterMixin,
    TimeStampedFilterMixin,
    NameFilterMixin,
)
from vantedge.autocomplete.views import AutoCompleteViewSetMixin
from vantedge.tboss.serializers import (
    CertificationSerializer,
    CertificationListSerializer,
    CertificationSelectSerializer,
)
from vantedge.tboss.models import (
  Certification,
  UserCertification,
  VehicleCertification,
  CompanyCertification
)
from vant_dbreplication.models import DBReplicationServer
from vant_dbreplication.utils import ReplicationHandler
from util.mixins import SearchFilterMixin
import json

OPTIONS_MAP = {"certification_type": Certification.CertificationType.choices}


replication_handler = ReplicationHandler()

def get_company_wheelhouse_server(company):
    # Company -> DBReplicationServer
    """
    Return the wheelhouse server for the given company.
    """
    if hasattr(company, 'terminalboss'):
        client_company = company.terminalboss

        return (
            DBReplicationServer.objects.filter(dbreplication_client_company=client_company)
            .filter(name__contains='Wheelhouse').first()
        )

def get_model_from_type(types):
    # [str] -> Model
    """
    Return the corresponding model for the given types,
    if types are not all for the same model then return
    their base model.
    """
    COMPANY_CERTIFICATION_TYPES = ["COM", "CAR"]
    USER_CERTIFICATION_TYPES = ["USR", "DRV", "INS"]
    VEHICLE_CERTIFICATION_TYPES = ["VEH", "VTR", "VTL"]

    if (all(x in types for x in COMPANY_CERTIFICATION_TYPES)):
        return CompanyCertification
    elif (all(x in types for x in USER_CERTIFICATION_TYPES)):
        return UserCertification
    elif (all(x in types for x in VEHICLE_CERTIFICATION_TYPES)):
        return VehicleCertification

    # if there is a mix of types:
    return Certification

class CertificationFilter(
    SearchFilterMixin, DBReplicationFilterMixin, TimeStampedFilterMixin, NameFilterMixin
):
    search_query = CharFilter(method="search_filter")
    certification_type__pattern_match = CharFilter(
        field_name="certification_type",
        method="pattern_match_choices_filter",
    )
    certification_type = CharFilter(method="choice_filter")
    certification_type__in = CharFilter(method="choice_filter")
    certification_type__exclude = CharFilter(method="choice_exclude")

    certification_server_associations__server__name__pattern_match = CharFilter(
        field_name="certification_server_associations__server__name", lookup_expr="icontains"
    )
    certification_server_associations__server__name = CharFilter(method="m2m_name_filter")
    certification_server_associations__server__name__in = CharFilter(method="m2m_name_filter")
    certification_server_associations__server__name__exclude = CharFilter(method="m2m_name_exclude")

    include_pk = CharFilter(method="include_pk_filter")

    def pattern_match_choices_filter(self, qs, field_name, value):
        query = Q()
        choices = {label: code for code, label in OPTIONS_MAP[field_name]}
        values = []

        for key in choices:
            if value in key:
                values.append(choices.get(key))

        if value:
            query = Q(**{f"{field_name}__in": values})

        return qs.filter(query)

    def choice_filter(self, qs, field_name, value):
        query = Q()
        field_name = field_name.split("__in")[0]
        choices = {label: code for code, label in OPTIONS_MAP[field_name]}
        values = [
            choices.get(v) for v in value.split(",") if choices.get(v) is not None
        ]

        if values:
            query = Q(**{f"{field_name}__in": values})
        return qs.filter(query)

    def choice_exclude(self, qs, field_name, value):
        query = Q()
        field_name = field_name.split("__exclude")[0]

        choices = {label: code for code, label in OPTIONS_MAP[field_name]}
        values = [
            choices.get(v) for v in value.split(",") if choices.get(v) is not None
        ]
        if values:
            query = Q(**{f"{field_name}__in": values})
        return qs.exclude(query)

    def include_pk_filter(self, qs, field_name, value):
        return qs | self.queryset.filter(pk=value)

    class Meta:
        model = Certification
        fields = {
            **DBReplicationFilterMixin.Meta.fields,
            **TimeStampedFilterMixin.Meta.fields,
            **NameFilterMixin.Meta.fields,
            "id": ["in", "exact"],
            "name": ["in", "exact"],
            "code": ["in", "exact"],
        }


class TBossV3CertificationViewset(
    TBossCompanyMixin,
    MultiSerializerViewSetMixin,
    AutoCompleteViewSetMixin,
    ChoiceOptionsMixin,
    FormConfigurationMixin,
    ModelViewSet,
):
    queryset = Certification.objects.all()
    serializer_class = CertificationSerializer
    pagination_class = LimitOffsetPagination
    permission_classes = [IsAuthenticated]
    filterset_class = CertificationFilter
    ordering_fields = [
        # "dbreplication_client_company",
        # "additional_data",
        # "dbreplication_config",
        "created_by_server",
        "modified_by_server",
        "dbreplication_state",
        "created",
        "modified",
        "id",
        "name",
        "code",
        "certification_type",
        "days_active",
        "certification_server_associations__server__name"
    ]
    serializer_action_classes = {
        "list": CertificationListSerializer,
        "detail": CertificationSerializer,
        "choices": CertificationSelectSerializer,
    }

    mapping_autocomplete_fields = {
        "certification_type": dict(Certification.CertificationType.choices)
    }

    def filter_queryset(self, queryset):
        qs = super().filter_queryset(queryset)
        return qs

    def perform_create(self, serializer):
        company = self.get_terminalboss_company()
        serializer.save(dbreplication_client_company=company)

    @action(detail=False, methods=["get"])
    def count(self, request):
        qs = self.filter_queryset(self.get_queryset())
        return Response(qs.count())

    @action(detail=False, methods=["get"])
    def types(self, request):
        types = map(
            lambda l: {"value": l[0], "label": l[1]},
            Certification.CertificationType.choices,
        )
        return Response(types)

    @action(detail=False, methods=["GET", "POST"])
    def choices(self, request):
        suggestions = []

        # Putting everything on URL query params does not work if there are alot of selected ids
        # Add support for POST requests to have it on the body
        if request.method == 'POST':
            body_params = json.loads(request.body.decode())
            query_dict = QueryDict('', mutable=True)
            query_dict.update(body_params)
        else:
            query_dict = request.query_params

        user_input = query_dict.get("input")
        types = query_dict.get("type")
        server_ids = query_dict.getlist("servers[]") or query_dict.get("servers")
        selected_ids = query_dict.getlist("selected_ids[]") or query_dict.get("selected_ids")
        limit = int(query_dict.get("limit", 50))
        offset = int(query_dict.get("offset", 0))
        company = request.user.company

        qs = self.filter_queryset(self.get_queryset())
        types = types.split(",")
        model = qs.model

        if types:
            qs = qs.filter(certification_type__in=types)

        if server_ids:
            if isinstance(server_ids, list):
                qs = (
                    qs
                    .filter(dbreplication_config__servers__id__in=server_ids)
                )
            else:
                qs = (
                    qs
                    .filter(dbreplication_config__servers__id=server_ids)
                )

        # get qs based on type
        model_association = replication_handler.get_server_association_model(model)

        CLIENT_COMPANY_WHEELHOUSE_SERVER = get_company_wheelhouse_server(company)

        # filter suggestions based on association to the given site(s)/server(s):
        if model_association:
            assoc_qs = None
            associated_entity_ids = list()
            is_client_company_wheelhouse_server = False

            if server_ids:
                if isinstance(server_ids, list):
                    assoc_qs = model_association.objects.filter(server_id__in=server_ids)
                    is_client_company_wheelhouse_server = (
                        CLIENT_COMPANY_WHEELHOUSE_SERVER and
                        server_ids == [str(CLIENT_COMPANY_WHEELHOUSE_SERVER.id)]
                    )
                else:
                    assoc_qs = model_association.objects.filter(server_id=server_ids)
                    is_client_company_wheelhouse_server = (
                        CLIENT_COMPANY_WHEELHOUSE_SERVER and
                        server_ids == CLIENT_COMPANY_WHEELHOUSE_SERVER.id
                    )

                associated_entity_ids = list(assoc_qs.values_list('entity_id', flat=True))

                # entity_ids that are "active" for the given site(s)/server(s)
                # no need to check for association if server_ids = CLIENT_COMPANY_WHEELHOUSE_SERVER
                if not is_client_company_wheelhouse_server:
                    qs = qs.filter(id__in=associated_entity_ids)

            elif CLIENT_COMPANY_WHEELHOUSE_SERVER:
                from_wheelhouse_entity_ids = list()
                from_wheelhouse_entity_ids = list(
                    self.filter_queryset(self.get_queryset())
                        .filter(created_by_server=CLIENT_COMPANY_WHEELHOUSE_SERVER)
                        .values_list('id', flat=True)
                )

                qs = qs.filter(id__in=from_wheelhouse_entity_ids)

            else:
                qs = model.objects.none()

        if user_input and len(user_input):
            query = Q(
                Q(name__icontains=user_input) | Q(code__icontains=user_input)
            )

            if hasattr(model, "id") and is_valid_uuid(user_input):
                query |= Q(id=user_input)

            qs = qs.filter(query)

        elif selected_ids:
            if not isinstance(selected_ids, list):
                qs = qs | model.objects.filter(id=selected_ids)
            else:
                qs = qs | model.objects.filter(id__in=selected_ids)
                preserved = Case(*[When(pk=pk, then=pos) for pos, pk in enumerate(selected_ids)])
                qs = qs.order_by(preserved)

        count = qs.distinct().count()
        suggestions = qs.distinct()[offset:limit]
        serializer = self.get_serializer_class()(suggestions, many=True)

        return Response({"suggestions": serializer.data, "count": count, "limit": limit, "offset": offset})
