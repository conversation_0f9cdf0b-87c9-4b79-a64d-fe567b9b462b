from django.apps import apps
from django_filters import <PERSON><PERSON><PERSON><PERSON><PERSON>
from django.db.models import <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, TextField

from rest_framework.response import Response
from rest_framework.decorators import action
from rest_framework.pagination import LimitOffsetPagination
from rest_framework.permissions import IsAuthenticated
from rest_framework.viewsets import ModelViewSet
from rest_framework import status

from .mixins.viewsets import (
    MultiSerializerViewSetMixin,
    TBossCompanyMixin,
    ChoiceOptionsMixin,
    FormConfigurationMixin
)
from .mixins.filters import (
    DBReplicationFilterMixin,
    TimeStampedFilterMixin,
    ActiveFilterMixin,
    NameFilterMixin,
    LockoutFilterMixin,
)
from vantedge.autocomplete.views import AutoCompleteViewSetMixin
from vantedge.util.download.mixin import DownloadTableMixin
from vantedge.tboss.serializers import (
    CompanySerializer,
    CompanySelectSerializer,
    CompanyListSerializer,
)
from vantedge.tboss.models import Company, CompanyRole
from util.mixins import SearchFilterMixin


class CompanyFilter(
    SearchFilterMixin,
    DBReplicationFilterMixin,
    TimeStampedFilterMixin,
    ActiveFilterMixin,
    NameFilterMixin,
    LockoutFilterMixin,
):
    search_query = CharFilter(method="search_filter")
    roles__name__pattern_match = CharFilter(method="role_filter")
    roles__name = CharFilter(method="role_filter")
    roles__name__in = CharFilter(method="role_filter")
    roles__name__exclude = CharFilter(method="role_exclude")

    user_set__name__pattern_match = CharFilter(
        field_name="user_set__name", lookup_expr="icontains"
    )
    user_set__name = CharFilter(method="m2m_name_filter")
    user_set__name__in = CharFilter(method="m2m_name_filter")
    user_set__name__exclude = CharFilter(method="m2m_name_exclude")

    associations__name__pattern_match = CharFilter(
        field_name="associations__name", lookup_expr="icontains"
    )
    associations__name = CharFilter(method="m2m_name_filter")
    associations__name__in = CharFilter(method="m2m_name_filter")
    associations__name__exclude = CharFilter(method="m2m_name_exclude")

    company_server_associations__server__name__pattern_match = CharFilter(
        field_name="company_server_associations__server__name", lookup_expr="icontains"
    )
    company_server_associations__server__name = CharFilter(method="m2m_name_filter")
    company_server_associations__server__name__in = CharFilter(method="m2m_name_filter")
    company_server_associations__server__name__exclude = CharFilter(method="m2m_name_exclude")

    def role_filter(self, qs, field_name, value):
        choices = {label: code for code, label in CompanyRole.CompanyRoleChoices.choices}
        if "pattern_match" in field_name:
            statuses = []
            for label, code in choices.items():
                if str(value).lower() in label.lower():
                    statuses.append(code)
        else:
            statuses = [
                choices.get(v) for v in value.split(",") if choices.get(v) is not None
            ]
        if statuses:
            return qs.filter(roles__name__in=statuses)
        return qs

    def role_exclude(self, qs, field_name, value):
        choices = {label: code for code, label in CompanyRole.CompanyRoleChoices.choices}
        statuses = [
            choices.get(v) for v in value.split(",") if choices.get(v) is not None
        ]
        if statuses:
            return qs.exclude(roles__name__in=statuses)
        return qs

    class Meta:
        model = Company
        fields = {
            **DBReplicationFilterMixin.Meta.fields,
            **TimeStampedFilterMixin.Meta.fields,
            **ActiveFilterMixin.Meta.fields,
            **NameFilterMixin.Meta.fields,
            **LockoutFilterMixin.Meta.fields,
            "id": ["in", "exact"],
            "name": ["in", "exact"],
            "code": ["in", "exact"],
        }


class TBossV3CompanyViewset(
    FormConfigurationMixin,
    TBossCompanyMixin,
    ChoiceOptionsMixin,
    MultiSerializerViewSetMixin,
    AutoCompleteViewSetMixin,
    DownloadTableMixin,
    ModelViewSet,
):
    queryset = Company.objects.all().annotate(locations_count=Count("location_set"))
    serializer_class = CompanySerializer
    pagination_class = LimitOffsetPagination
    permission_classes = [IsAuthenticated]
    filterset_class = CompanyFilter
    custom_ordering_fields = [
        ["locations_count", Count("location_set")],
    ]
    mapping_autocomplete_fields = {
        "roles__name": dict(CompanyRole.CompanyRoleChoices.choices)
    }
    ordering_fields = [
        "effective_start_date",
        "effective_end_date",
        "dbreplication_state",
        "created_by_server",
        "modified_by_server",
        "dbreplication_config",
        "created",
        "modified",
        "id",
        "name",
        "code",
        "lockout_date",
        "enabled",
        "company_server_associations__server__name",
        "roles__name",
    ]
    serializer_action_classes = {
        "list": CompanyListSerializer,
        "detail": CompanySerializer,
        "choices": CompanySelectSerializer,
    }
    table_name = "Company"

    def create(self, request, *args, **kwargs):
        # --------------------------------
        if not bool(request.data.get("associations")):
            request.data["associations"] = list()

        if not bool(request.data.get("user_set")):
            request.data["user_set"] = list()
        # --------------------------------

        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        self.perform_create(serializer)
        headers = self.get_success_headers(serializer.data)
        return Response(
            serializer.data, status=status.HTTP_201_CREATED, headers=headers
        )

    def filter_queryset(self, queryset):
        qs = super().filter_queryset(queryset)
        custom_fields = dict(self.custom_ordering_fields)
        ordering = str(self.request.query_params.get("ordering", ""))
        order = ""
        if ordering.startswith("-"):
            order = "-"
            ordering = ordering[1:]

        if ordering in custom_fields:
            annotation = custom_fields.get(ordering)
            qs = qs.annotate(**{ordering: annotation}).order_by(f"{order}{ordering}")
        return qs

    def perform_create(self, serializer):
        company = self.get_terminalboss_company()
        serializer.save(dbreplication_client_company=company)

    @action(detail=False, methods=["get"])
    def count(self, request):
        qs = self.filter_queryset(self.get_queryset())
        return Response(qs.count())
