import uuid
from django_filters import <PERSON><PERSON>n<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, FilterSet
from django.db.models import Q
from django.core.exceptions import ObjectDoesNotExist

from rest_framework.response import Response
from rest_framework import status
from rest_framework.decorators import action
from rest_framework.pagination import LimitOffsetPagination
from rest_framework.permissions import IsAuthenticated
from rest_framework.viewsets import ModelViewSet, ReadOnlyModelViewSet



from .mixins.viewsets import (
    ChoiceOptionsMixin,
    FormConfigurationMixin,
    MultiSerializerViewSetMixin,
    TBossCompanyMixin,
)
from .mixins.filters import (
    DBReplicationFilterMixin,
    TimeStampedFilterMixin,
    ActiveFilterMixin,
    NameFilterMixin,
    LockoutFilterMixin,
)
from vantedge.autocomplete.views import AutoCompleteViewSetMixin
from vantedge.tboss.serializers import (
    PeriodicLimitSerializer,
    PeriodicLimitListSerializer,
    LimitPeriodSerializer,
    PeriodicLimitSelectSerializer,
    LimitPropertiesSerializer,
)
from vantedge.tboss.models import PeriodicLimit, LimitPeriod, LimitProperties
from vantedge.util.download.mixin import DownloadTableMixin
from util.mixins import SearchFilterMixin


OPTIONS_MAP = {
    "period_type": PeriodicLimit.PeriodLength.choices,
    "status": PeriodicLimit.Status.choices,
}


class PeriodicLimitFilter(
    SearchFilterMixin,
    DBReplicationFilterMixin,
    TimeStampedFilterMixin,
    ActiveFilterMixin,
    NameFilterMixin,
    LockoutFilterMixin,
):
    search_query = CharFilter(method="search_filter")
    location__name__pattern_match = CharFilter(
        field_name="location__name", lookup_expr="icontains"
    )
    location__name = CharFilter(method="escape_comma_filter")
    location__name__in = CharFilter(method="escape_comma_filter")
    location__name__exclude = CharFilter(method="escape_comma_exclude")

    product__name__pattern_match = CharFilter(
        field_name="product__name", lookup_expr="icontains"
    )
    product__name__exclude = BaseInFilter(field_name="product__name", exclude=True)

    buyer__name__pattern_match = CharFilter(
        field_name="buyer__name", lookup_expr="icontains"
    )
    buyer__name__exclude = BaseInFilter(field_name="buyer__name", exclude=True)

    seller__name__pattern_match = CharFilter(
        field_name="seller__name", lookup_expr="icontains"
    )
    seller__name__exclude = BaseInFilter(field_name="seller__name", exclude=True)

    carrier_set__name__pattern_match = CharFilter(
        field_name="product__name", lookup_expr="icontains"
    )
    carrier_set__name = CharFilter(method="m2m_name_filter")
    carrier_set__name__in = CharFilter(method="m2m_name_filter")
    carrier_set__name__exclude = CharFilter(method="m2m_name_exclude")

    period_type__pattern_match = CharFilter(
        field_name="period_type",
        method="pattern_match_choices_filter",
    )
    period_type = CharFilter(method="choice_filter")
    period_type__in = CharFilter(method="choice_filter")
    period_type__exclude = CharFilter(method="choice_exclude")

    status__pattern_match = CharFilter(
        field_name="status",
        method="pattern_match_choices_filter",
    )
    status = CharFilter(method="choice_filter")
    status__in = CharFilter(method="choice_filter")
    status__exclude = CharFilter(method="choice_exclude")

    def pattern_match_choices_filter(self, qs, field_name, value):
        query = Q()
        choices = {label: code for code, label in OPTIONS_MAP[field_name]}
        values = []

        for key in choices:
            if value in key:
                values.append(choices.get(key))

        if value:
            query = Q(**{f"{field_name}__in": values})

        return qs.filter(query)

    def choice_filter(self, qs, field_name, value):
        query = Q()
        field_name = field_name.split("__in")[0]
        choices = {label: code for code, label in OPTIONS_MAP[field_name]}
        values = [
            choices.get(v) for v in value.split(",") if choices.get(v) is not None
        ]

        if values:
            query = Q(**{f"{field_name}__in": values})
        return qs.filter(query)

    def choice_exclude(self, qs, field_name, value):
        query = Q()
        field_name = field_name.split("__exclude")[0]

        choices = {label: code for code, label in OPTIONS_MAP[field_name]}
        values = [
            choices.get(v) for v in value.split(",") if choices.get(v) is not None
        ]
        if values:
            query = Q(**{f"{field_name}__in": values})
        return qs.exclude(query)

    class Meta:
        model = PeriodicLimit
        fields = {
            **DBReplicationFilterMixin.Meta.fields,
            **TimeStampedFilterMixin.Meta.fields,
            **ActiveFilterMixin.Meta.fields,
            **NameFilterMixin.Meta.fields,
            **LockoutFilterMixin.Meta.fields,
            "name": ["in", "exact"],
            "id": ["in", "exact"],
            "product__name": ["in", "exact"],
            "carrier_set__name": ["in", "exact"],
            "seller__name": ["in", "exact"],
            "buyer__name": ["in", "exact"],
            "location__name": ["in", "exact"],
            "status": ["in", "exact"],
            "period_type": ["in", "exact"],
        }


class TBossV3PeriodicLimitViewset(
    ChoiceOptionsMixin,
    FormConfigurationMixin,
    TBossCompanyMixin,
    MultiSerializerViewSetMixin,
    AutoCompleteViewSetMixin,
    DownloadTableMixin,
    ModelViewSet
):
    queryset = PeriodicLimit.objects.all()
    serializer_class = PeriodicLimitSerializer
    pagination_class = LimitOffsetPagination
    filterset_class = PeriodicLimitFilter
    permission_classes = [IsAuthenticated]
    ordering_fields = [
        "created",
        "modified",
        "id",
        "dbreplication_state",
        "created_by_server",
        "modified_by_server",
        "dbreplication_config",
        "name",
        "code",
        "effective_start_date",
        "effective_end_date",
        "lockout_date",
        "lockout_reason",
        "enabled",
        "status",
        "product",
        "period_type",
        "seller",
        "buyer",
        "location",
        "quantity",
        "quantity_unit",
        "variance",
        "variance_unit",
        "warning",
        "warning_unit",
        "buffer",
        "buffer_unit",
        # "attachment",
    ]
    serializer_action_classes = {
        "list": PeriodicLimitListSerializer,
        "detail": PeriodicLimitSerializer,
        "choices": PeriodicLimitSelectSerializer,
    }
    table_name = "Limit"
    mapping_autocomplete_fields = {
        "status": dict(PeriodicLimit.Status.choices),
        "period_type": dict(PeriodicLimit.PeriodLength.choices),
    }

    def create(self, request, *args, **kwargs):
        """
        When creating a new limit, connect a limit-property
        based on the `created_by_server`:

        limit.display_unit = limit_properties.default_display_unit
        limit.properties = limit_properties.id
        """
        try:
            limit_properties = LimitProperties.objects.get(
                id=request.data.get("properties")
            )
        except ObjectDoesNotExist:
            return Response(status=status.HTTP_400_BAD_REQUEST)

        # -------------------------------- set display_unit & properties from limit_properties
        if not bool(request.data.get("display_unit")):
            request.data["display_unit"] = limit_properties.default_display_unit
        
        if not bool(request.data.get("carrier_set")):
            request.data["carrier_set"] = list()

        # --------------------------------

        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        self.perform_create(serializer)
        headers = self.get_success_headers(serializer.data)
        return Response(
            serializer.data, status=status.HTTP_201_CREATED, headers=headers
        )

    def filter_queryset(self, queryset):
        qs = super().filter_queryset(queryset)
        return qs

    def perform_create(self, serializer):
        company = self.get_terminalboss_company()
        serializer.save(dbreplication_client_company=company)

    @action(detail=False, methods=["get"])
    def count(self, request):
        qs = self.filter_queryset(self.get_queryset())
        return Response(qs.count())

    @action(detail=False, methods=["get"])
    def period_types(self, request):
        types = map(
            lambda l: {"value": l[0], "label": l[1]}, PeriodicLimit.PeriodLength.choices
        )
        return Response(types)

    @action(detail=False, methods=["get"])
    def status_choices(self, request):
        types = map(
            lambda l: {"value": l[0], "label": l[1]}, PeriodicLimit.Status.choices
        )
        return Response(types)

    @action(detail=True, methods=["post"])
    def roll_forward(self, request, pk=None):
        periodic_limit = self.get_object()

        periodic_limit.roll_forward()

        return Response(self.get_serializer(periodic_limit).data, status.HTTP_200_OK)

    @action(detail=True, methods=["post"])
    def upload_attachment(self, request, pk):
        limit = self.get_object()

        existing_file = limit.attachment
        #TODO if attachment were removed, should you delete the file
        # or keep it and just remove reference
        # limit.attachment = None
        if existing_file and not request.data:
            limit.attachment.delete()
            limit.save()
        else:
            # handle add / edit of file attachment
            for filename in request.data:
                limit.attachment = request.data[filename]
                limit.attachment.name = filename
                limit.save()
        serializer = self.get_serializer_class()(instance=limit)
        return Response(serializer.data)

class LimitPeriodFilter(FilterSet):
    limit_id = CharFilter(method="entity_id_filter")
    limit_id__in = CharFilter(method="entity_id_filter")
    limit_id__exclude = CharFilter(method="entity_id_exclude")

    def entity_id_filter(self, qs, field_name, value):
        query = Q()
        field_name = field_name.split("__in")[0]
        values = list(map(lambda v: str(uuid.UUID(v)), value.split(",")))

        if values:
            query = Q(**{f"{field_name}__in": values})
        return qs.filter(query)

    def entity_id_exclude(self, qs, field_name, value):
        query = Q()
        field_name = field_name.split("__exclude")[0]
        values = list(map(lambda v: str(uuid.UUID(v)), value.split(",")))

        if values:
            query = Q(**{f"{field_name}__in": values})
        return qs.exclude(query)
    
    class Meta:
        model = LimitPeriod
        fields = {
            **DBReplicationFilterMixin.Meta.fields,
            **TimeStampedFilterMixin.Meta.fields,
            **ActiveFilterMixin.Meta.fields,
            **LockoutFilterMixin.Meta.fields,
            "id": ["in", "exact"],
            "limit__name": ["in", "exact"],
        }


class TBossV3LimitPeriodViewset(TBossCompanyMixin, ModelViewSet):
    queryset = LimitPeriod.objects.all()
    serializer_class = LimitPeriodSerializer
    pagination_class = LimitOffsetPagination
    filterset_class = LimitPeriodFilter
    permission_classes = [IsAuthenticated]

    def filter_queryset(self, queryset):
        qs = super().filter_queryset(queryset)
        return qs

    @action(detail=False, methods=["get"])
    def count(self, request):
        qs = self.filter_queryset(self.get_queryset())
        return Response(qs.count())

    def perform_create(self, serializer):
        company = self.get_terminalboss_company()
        serializer.save(dbreplication_client_company=company)


class LimitPropertiesFilterSet(FilterSet):
    class Meta:
        model = LimitProperties
        fields = {
            **DBReplicationFilterMixin.Meta.fields,
            **TimeStampedFilterMixin.Meta.fields,
            "id": ["in", "exact"],
            "name": ["in", "exact"],
            "class_name": ["in", "exact"],
            "base_unit": ["in", "exact"],
            "default_display_unit": ["in", "exact"],
        }

class TBossV3LimitPropertiesViewSet(TBossCompanyMixin, ReadOnlyModelViewSet, ChoiceOptionsMixin):
    queryset = LimitProperties.objects.all()
    serializer_class = LimitPropertiesSerializer
    pagination_class = LimitOffsetPagination
    filterset_class = LimitPropertiesFilterSet
    permission_classes = [IsAuthenticated]

    def filter_queryset(self, queryset):
        qs = super().filter_queryset(queryset)
        return qs
