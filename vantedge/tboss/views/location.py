import uuid
from django.conf import settings
from django_filters import BaseIn<PERSON><PERSON><PERSON>, Char<PERSON>ilter
from django.db.models import Count, F, Q, Value
from decimal import Decimal

from rest_framework.response import Response
from rest_framework.decorators import action
from rest_framework.pagination import LimitOffsetPagination
from rest_framework.permissions import IsAuthenticated
from rest_framework.viewsets import ModelViewSet

from vantedge.autocomplete.views import AutoCompleteViewSetMixin
from .mixins.viewsets import (
    MultiSerializerViewSetMixin,
    TBossCompanyMixin,
    ChoiceOptionsMixin,
    FormConfigurationMixin,
)
from .mixins.filters import (
    DBReplicationFilterMixin,
    TimeStampedFilterMixin,
    ActiveFilterMixin,
    NameFilterMixin,
    LockoutFilterMixin,
)
from vantedge.tboss.serializers import (
    LocationSerializer,
    LocationSelectSerializer,
    LocationListSerializer,
)
from vantedge.tboss.models import Location, Reading
from vantedge.util.download.mixin import DownloadTableMixin
from util.mixins import SearchFilterMixin

import arrow
import pandas as pd
from zoneinfo import ZoneInfo


OPTIONS_MAP = {"location_type": Location.LocationType.choices}


class LocationFilter(
    SearchFilterMixin,
    NameFilterMixin,
    DBReplicationFilterMixin,
    TimeStampedFilterMixin,
    ActiveFilterMixin,
    LockoutFilterMixin,
):
    search_query = CharFilter(method="search_filter")
    name__pattern_match = CharFilter(field_name="name", lookup_expr="icontains")
    name = CharFilter(method="escape_comma_filter")
    name__in = CharFilter(method="escape_comma_filter")
    name__exclude = CharFilter(method="escape_comma_exclude")

    code__pattern_match = CharFilter(field_name="code", lookup_expr="icontains")
    code__exclude = BaseInFilter(field_name="code", exclude=True)

    location_type__pattern_match = CharFilter(
        field_name="location_type",
        method="pattern_match_choices_filter",
    )
    location_type = CharFilter(method="choice_filter")
    location_type__in = CharFilter(method="choice_filter")
    location_type__exclude = CharFilter(method="choice_exclude")

    products__name__pattern_match = CharFilter(
        field_name="products__product_name", lookup_expr="icontains"
    )
    products__name = CharFilter(method="m2m_name_filter")
    products__name__in = CharFilter(method="m2m_name_filter")
    products__name__exclude = CharFilter(method="m2m_name_exclude")

    location_server_associations__server__name__pattern_match = CharFilter(
        field_name="location_server_associations__server__name", lookup_expr="icontains"
    )
    location_server_associations__server__name = CharFilter(method="m2m_name_filter")
    location_server_associations__server__name__in = CharFilter(
        method="m2m_name_filter"
    )
    location_server_associations__server__name__exclude = CharFilter(
        method="m2m_name_exclude"
    )

    # JSON
    data__address__city__pattern_match = CharFilter(
        field_name="data__address__city", lookup_expr="icontains"
    )
    data__address__city = CharFilter(method="field_filter")
    data__address__city__in = CharFilter(method="field_filter")
    data__address__city__exclude = CharFilter(method="field_exclude")

    data__address__province__pattern_match = CharFilter(
        field_name="data__address__city", lookup_expr="icontains"
    )
    data__address__province = CharFilter(method="field_filter")
    data__address__province__in = CharFilter(method="field_filter")
    data__address__province__exclude = CharFilter(method="field_exclude")

    data__address__country__pattern_match = CharFilter(
        field_name="data__address__country", lookup_expr="icontains"
    )
    data__address__country = CharFilter(method="field_filter")
    data__address__country__in = CharFilter(method="field_filter")
    data__address__country__exclude = CharFilter(method="field_exclude")

    company__name__pattern_match = CharFilter(
        field_name="company__name", lookup_expr="icontains"
    )
    company__name__exclude = BaseInFilter(field_name="company__name", exclude=True)
    company__id__in = CharFilter(method="entity_id_filter")

    location__name__pattern_match = CharFilter(
        field_name="location__name", lookup_expr="icontains"
    )
    location__name__exclude = BaseInFilter(field_name="location__name", exclude=True)

    # Changed in TBOSS due to Destination/Source/Tank Parent
    parent__isnull = CharFilter(
        method="parent_classification_filter"
    )  # BooleanFilter(method="boolean_filter")

    parent__name__exclude = CharFilter(method="escape_comma_exclude")

    def parent_classification_filter(self, qs, field_name, value):
        if value in ["true", "True", True]:
            return qs.filter(
                Q(parent=None)
                | Q(code__iexact="source")
                | Q(code__iexact="destination")
                | Q(code__iexact="tank")
            )
        return qs.exclude(
            Q(parent=None)
            | Q(code__iexact="source")
            | Q(code__iexact="destination")
            | Q(code__iexact="tank")
        )

    def pattern_match_choices_filter(self, qs, field_name, value):
        query = Q()
        choices = {label: code for code, label in OPTIONS_MAP[field_name]}
        values = []

        for key in choices:
            if value in key:
                values.append(choices.get(key))

        if value:
            query = Q(**{f"{field_name}__in": values})

        return qs.filter(query)

    def field_filter(self, qs, field_name, value):
        query = Q()
        field_name = field_name.split("__in")[0]
        values = value.split(",")

        if values:
            query |= Q(**{f"{field_name}__in": values})
        return qs.filter(query)

    def field_exclude(self, qs, field_name, value):
        query = Q()
        field_name = field_name.split("__exclude")[0]
        values = value.split(",")

        if values:
            query |= Q(**{f"{field_name}__in": values})
        return qs.exclude(query)

    def choice_filter(self, qs, field_name, value):
        query = Q()
        field_name = field_name.split("__in")[0]
        choices = {label: code for code, label in OPTIONS_MAP[field_name]}
        values = [
            choices.get(v) for v in value.split(",") if choices.get(v) is not None
        ]

        if values:
            query = Q(**{f"{field_name}__in": values})
        return qs.filter(query)

    def choice_exclude(self, qs, field_name, value):
        query = Q()
        field_name = field_name.split("__exclude")[0]

        choices = {label: code for code, label in OPTIONS_MAP[field_name]}
        values = [
            choices.get(v) for v in value.split(",") if choices.get(v) is not None
        ]
        if values:
            query = Q(**{f"{field_name}__in": values})
        return qs.exclude(query)

    def entity_id_filter(self, qs, field_name, value):
        query = Q()
        field_name = field_name.split("__in")[0]
        values = list(map(lambda v: str(uuid.UUID(v)), value.split(",")))

        if values:
            query = Q(**{f"{field_name}__in": values})
        return qs.filter(query)

    class Meta:
        model = Location
        fields = {
            **DBReplicationFilterMixin.Meta.fields,
            **TimeStampedFilterMixin.Meta.fields,
            **ActiveFilterMixin.Meta.fields,
            **NameFilterMixin.Meta.fields,
            **LockoutFilterMixin.Meta.fields,
            "name": ["in", "exact"],
            "code": ["in", "exact"],
            "id": ["in", "exact"],
            # "company__name": ["in", "exact"],
            "parent__name": ["in", "exact"],
            "parent__code": ["in", "exact"],
        }


class TBossV3LocationViewset(
    FormConfigurationMixin,
    ChoiceOptionsMixin,
    TBossCompanyMixin,
    MultiSerializerViewSetMixin,
    DownloadTableMixin,
    ModelViewSet,
    AutoCompleteViewSetMixin,
):
    queryset = Location.objects.all()
    serializer_class = LocationSerializer
    pagination_class = LimitOffsetPagination
    permission_classes = [IsAuthenticated]
    filterset_class = LocationFilter
    ordering_fields = [
        "created",
        "modified",
        "id",
        "dbreplication_client_company",
        "parent",
        "dbreplication_state",
        "additional_data",
        "created_by_server__name",
        "modified_by_server__name",
        # "dbreplication_config",
        "name",
        "code",
        "effective_start_date",
        "effective_end_date",
        "lockout_date",
        "lockout_reason",
        "enabled",
        # "company",
        "location_type",
        "location_server_associations__server__name",
        # "data",
        # "data__capacity",
    ]
    serializer_action_classes = {
        "list": LocationListSerializer,
        "detail": LocationSerializer,
        "choices": LocationSelectSerializer,
    }
    table_name = "Locations"
    mapping_autocomplete_fields = {"location_type": dict(Location.LocationType.choices)}
    custom_ordering_fields = [
        ["company__name", Count("company")],
    ]

    def filter_queryset(self, queryset):
        qs = super().filter_queryset(queryset)
        return qs

    def perform_create(self, serializer):
        company = self.get_terminalboss_company()
        serializer.save(dbreplication_client_company=company)

    @action(detail=False, methods=["get"])
    def count(self, request):
        qs = self.filter_queryset(self.get_queryset())
        return Response(qs.count())

    @action(detail=False, methods=["get"])
    def types(self, request):
        types = map(
            lambda l: {"value": l[0], "label": l[1]}, Location.LocationType.choices
        )
        return Response(types)

    @action(detail=False, methods=["get"])
    def container_volumes(self, request):
        qs = self.filter_queryset(self.get_queryset())
        annotations = {
            "location_name": F("location__name"),
            "tank_height": F("quantity"),
            "tank_height_unit": Value("ft"),
        }

        now = arrow.now()

        from_dt = arrow.get(
            self.request.query_params.get("from", now.shift(minutes=-1440).isoformat())
        ).datetime
        to_dt = arrow.get(self.request.query_params.get("to", now.isoformat())).datetime

        measurement_qs = Reading.objects.filter(location__in=qs)

        # if measurement_qs.filter(created__range=[from_dt, to_dt]).exists():
        measurement_qs = measurement_qs.filter(created__range=[from_dt, to_dt])

        if not measurement_qs.exists():
            return Response({"measurements": {}, "kpis": []})
        else:
            measurement_qs = measurement_qs.annotate(**annotations)

        columns = list(annotations.keys()) + ["created", "modified"]

        measurements = measurement_qs.values(*columns)

        containers = (
            measurement_qs.order_by("location_name")
            .values_list("location_name", flat=True)
            .distinct()
        )

        kpis = []

        # Get the latest created timestamp for data
        for container in containers:
            try:
                latest = measurements.filter(location_name=container).latest("created")
            except Reading.DoesNotExist:
                latest = None
            if latest:
                tank_height = round(Decimal(latest.get("tank_height")), 3)
                tank_height_unit = latest.get("tank_height_unit")
                content = f"{tank_height} ({tank_height_unit})"
            else:
                content = "No Data"

            kpis.append({"title": container, "content": content})

        measurement_data = pd.DataFrame.from_records(measurements.order_by("created"))
        measurement_data.sort_values(by=["created"])
        tz = ZoneInfo(settings.TIME_ZONE)
        measurement_data["x"] = measurement_data["created"].apply(
            lambda x: x.astimezone(tz).isoformat()
        )
        measurement_data["y"] = measurement_data["tank_height"]

        info = ["x", "y"]
        measurement_data["info"] = measurement_data[info].apply(
            lambda row: row.to_dict(), axis=1
        )
        measurement_data = (
            measurement_data.groupby("location_name")["info"].apply(list).to_dict()
        )

        return Response({"measurements": measurement_data, "kpis": kpis})
