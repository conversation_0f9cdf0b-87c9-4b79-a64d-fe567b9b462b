import logging
import uuid

from django.apps import apps
from django.contrib.contenttypes.models import ContentType
from django.db import models
from django_filters import Char<PERSON>ilter
from rest_framework.decorators import action
from rest_framework.pagination import LimitOffsetPagination
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.viewsets import ReadOnlyModelViewSet
from vant_dbreplication.api.serializers import DBReplicationLogEntrySerializer
from vant_dbreplication.models import DBReplicationLogEntry

from vantedge.tboss.models import (
    LocationProduct, Location, PeriodicLimit, LimitPeriod,
    UserCertification, VehicleCertification, CompanyCertification,
    TBossUser, Vehicle, Company, Ticket, TicketEntry, ServiceContract, ServiceContractProduct
)
from vantedge.tboss.serializers import LogEntryListSerializer
from vantedge.tboss.views.mixins.filters import TimeStampedFilterMixin
from vantedge.tboss.views.mixins.viewsets import (
    MultiSerializerViewSetMixin,
    TBossCompanyMixin,
)

logger = logging.getLogger(__name__)


class TBossV3LogEntryFilter(TimeStampedFilterMixin):
    entity_id = CharFilter(method="entity_id_filter")
    entity_id__in = CharFilter(method="entity_id_filter")
    entity_id__exclude = CharFilter(method="entity_id_exclude")

    content_type_label = CharFilter(method="content_type_filter")

    origin_server = CharFilter(method="origin_server_filter")
    origin_server__in = CharFilter(method="origin_server_filter")
    origin_server__exclude = CharFilter(method="origin_server_exclude")

    def content_type_filter(self, qs, field_name, value):
        model = apps.get_model(value)
        content_type = ContentType.objects.get_for_model(model)
        return qs.filter(content_type=content_type)

    def entity_id_filter(self, qs, field_name, value):
        query = models.Q()
        field_name = field_name.split("__in")[0]
        values = list(map(lambda v: str(uuid.UUID(v)), value.split(",")))

        if values:
            query = models.Q(**{f"{field_name}__in": values})
        return qs.filter(query)

    def entity_id_exclude(self, qs, field_name, value):
        query = models.Q()
        field_name = field_name.split("__exclude")[0]
        values = list(map(lambda v: str(uuid.UUID(v)), value.split(",")))

        if values:
            query = models.Q(**{f"{field_name}__in": values})
        return qs.exclude(query)

    def origin_server_filter(self, qs, field_name, value):
        query = models.Q()
        field_name = field_name.split("__in")[0]
        values = list(map(lambda v: uuid.UUID(v), value.split(",")))

        if values:
            query = models.Q(**{f"{field_name}__in": values})
        return qs.filter(query)

    def origin_server_exclude(self, qs, field_name, value):
        query = models.Q()
        field_name = field_name.split("__in")[0]
        values = list(map(lambda v: uuid.UUID(v), value.split(",")))

        if values:
            query = models.Q(**{f"{field_name}__in": values})
        return qs.filter(query)

    class Meta:
        model = DBReplicationLogEntry
        fields = {
            **TimeStampedFilterMixin.Meta.fields,
            "dbreplication_client_company": ["in", "exact"]
        }


class TBossV3LogEntryViewset(
    TBossCompanyMixin, MultiSerializerViewSetMixin, ReadOnlyModelViewSet
):
    serializer_class = DBReplicationLogEntrySerializer
    pagination_class = LimitOffsetPagination
    permission_classes = [IsAuthenticated]
    filterset_class = TBossV3LogEntryFilter
    ordering_fields = ["created", "modified"]
    ordering = ["-modified"]
    serializer_action_classes = {
        "list": LogEntryListSerializer,
        "detail": DBReplicationLogEntrySerializer,
    }

    def get_queryset(self):
        company = self.get_terminalboss_company()
        if not company:
            return DBReplicationLogEntry.objects.none()

        qs = DBReplicationLogEntry.objects.filter(dbreplication_client_company=company)
        return qs

    def filter_queryset(self, queryset):
        qs = super().filter_queryset(queryset)

        company = self.get_terminalboss_company()
        entity_id = []
        single_object_search = False
        related_object_query = models.Q(dbreplication_client_company=company)
        log_entry_query = models.Q()
        # get the number of unique entity_ids
        entity_id_count = qs.values_list("entity_id", flat=True).distinct().count()

        if entity_id_count == 1:
            entity_id = list(set(qs.values_list("entity_id", flat=True)))
            single_object_search = True

        content_type_id = list(set(qs.values_list("content_type_id", flat=True)))

        if len(content_type_id) == 1:
            content_type = ContentType.objects.filter(pk__in=content_type_id).first()

            location_content_type = ContentType.objects.get_for_model(Location)
            limit_content_type = ContentType.objects.get_for_model(PeriodicLimit)
            user_content_type = ContentType.objects.get_for_model(TBossUser)
            vehicle_content_type = ContentType.objects.get_for_model(Vehicle)
            company_content_type = ContentType.objects.get_for_model(Company)
            ticket_content_type = ContentType.objects.get_for_model(Ticket)
            service_contract_content_type = ContentType.objects.get_for_model(ServiceContract)

            if content_type and content_type.model_class():
                if content_type == location_content_type:
                    location_product_content_type = ContentType.objects.get_for_model(
                        LocationProduct
                    )

                    log_entry_query &= models.Q(content_type=location_product_content_type)

                    # if single object search, lookup with entity_id
                    if single_object_search:
                        related_object_query &= models.Q(
                            location__pk__in=list(
                                map(lambda pk: uuid.UUID(pk), entity_id)
                            )
                        )

                    location_products = list(
                        map(
                            lambda pk: str(pk) if isinstance(pk, uuid.UUID) else pk,
                            LocationProduct.objects.filter(
                                related_object_query
                            ).values_list("pk", flat=True),
                        ),
                    )

                    # if single object search, lookup with entity_id
                    if single_object_search:
                        log_entry_query &= models.Q(
                            models.Q(entity_id__in=location_products)
                            | models.Q(entity_state__fields__location__in=entity_id),
                        )

                    # Include location product logs
                    qs |= queryset.filter(log_entry_query)

                if content_type == service_contract_content_type:
                    service_contract_product_content_type = ContentType.objects.get_for_model(
                        ServiceContractProduct
                    )

                    log_entry_query &= models.Q(content_type=service_contract_product_content_type)

                    if single_object_search:
                        related_object_query &= models.Q(
                            service_contract__pk__in=list(
                                map(lambda pk: uuid.UUID(pk), entity_id)
                            )
                        )

                    service_contract_products = list(
                        map(
                            lambda pk: str(pk) if isinstance(pk, uuid.UUID) else pk,
                            ServiceContractProduct.objects.filter(
                                related_object_query
                            ).values_list("pk", flat=True),
                        ),
                    )

                    if single_object_search:
                        log_entry_query &= models.Q(
                            models.Q(entity_id__in=service_contract_products)
                            | models.Q(entity_state__fields__service_contract__in=entity_id),
                        )

                    qs |= queryset.filter(log_entry_query)

                elif content_type == limit_content_type:
                    # Include limit period logs for limit history
                    limit_period_content_type = ContentType.objects.get_for_model(
                        LimitPeriod
                    )

                    log_entry_query &= models.Q(content_type=limit_period_content_type)

                    # if single object search, lookup with entity_id
                    if single_object_search:
                        related_object_query &= models.Q(
                            limit__pk__in=list(
                                map(lambda pk: uuid.UUID(pk), entity_id)
                            )
                        )

                    limit_periods = list(
                        map(
                            lambda pk: str(pk) if isinstance(pk, uuid.UUID) else pk,
                            LimitPeriod.objects.filter(
                                related_object_query
                            ).values_list("pk", flat=True),
                        ),
                    )

                    # if single object search, lookup with entity_id
                    if single_object_search:
                        log_entry_query &= models.Q(
                            models.Q(entity_id__in=limit_periods)
                            | models.Q(entity_state__fields__limit__in=entity_id),
                        )

                    # Include limit period logs
                    qs |= queryset.filter(log_entry_query)

                elif content_type == user_content_type:
                    # Include user certification logs for user history
                    user_certification_content_type = ContentType.objects.get_for_model(
                        UserCertification
                    )

                    log_entry_query &= models.Q(content_type=user_certification_content_type)

                    # if single object search, lookup with entity_id
                    if single_object_search:
                        related_object_query &= models.Q(
                            user__pk__in=list(
                                map(lambda pk: uuid.UUID(pk), entity_id)
                            )
                        )

                    user_certification = list(
                        map(
                            lambda pk: str(pk) if isinstance(pk, uuid.UUID) else pk,
                            UserCertification.objects.filter(
                                related_object_query
                            ).values_list("pk", flat=True),
                        ),
                    )

                    # if single object search, lookup with entity_id
                    if single_object_search:
                        log_entry_query &= models.Q(
                            models.Q(entity_id__in=user_certification)
                            | models.Q(entity_state__fields__user__in=entity_id),
                        )

                    # Include user certification logs
                    qs |= queryset.filter(log_entry_query)

                elif content_type == vehicle_content_type:
                    # Include user certification logs for user history
                    vehicle_certification_content_type = ContentType.objects.get_for_model(
                        VehicleCertification
                    )

                    log_entry_query &= models.Q(content_type=vehicle_certification_content_type)

                    # if single object search, lookup with entity_id
                    if single_object_search:
                        related_object_query &= models.Q(
                            vehicle__pk__in=list(
                                map(lambda pk: uuid.UUID(pk), entity_id)
                            )
                        )

                    vehicle_certification = list(
                        map(
                            lambda pk: str(pk) if isinstance(pk, uuid.UUID) else pk,
                            VehicleCertification.objects.filter(
                                related_object_query
                            ).values_list("pk", flat=True),
                        ),
                    )

                    # if single object search, lookup with entity_id
                    if single_object_search:
                        log_entry_query &= models.Q(
                            models.Q(entity_id__in=vehicle_certification)
                            | models.Q(entity_state__fields__vehicle__in=entity_id),
                        )

                    # Include vehicle certification logs
                    qs |= queryset.filter(log_entry_query)

                elif content_type == company_content_type:
                    # Include user certification logs for user history
                    company_certification_content_type = ContentType.objects.get_for_model(
                        CompanyCertification
                    )

                    log_entry_query &= models.Q(content_type=company_certification_content_type)

                    # if single object search, lookup with entity_id
                    if single_object_search:
                        related_object_query &= models.Q(
                            company__pk__in=list(
                                map(lambda pk: uuid.UUID(pk), entity_id)
                            )
                        )

                    company_certification = list(
                        map(
                            lambda pk: str(pk) if isinstance(pk, uuid.UUID) else pk,
                            CompanyCertification.objects.filter(
                                related_object_query
                            ).values_list("pk", flat=True),
                        ),
                    )

                    # if single object search, lookup with entity_id
                    if single_object_search:
                        log_entry_query &= models.Q(
                            models.Q(entity_id__in=company_certification)
                            | models.Q(entity_state__fields__company__in=entity_id),
                        )

                    # include company certification logs
                    qs |= queryset.filter(log_entry_query)

                elif content_type == ticket_content_type:
                    # Include ticket entry logs for ticket history
                    ticket_entry_content_type = ContentType.objects.get_for_model(
                        TicketEntry
                    )

                    log_entry_query &= models.Q(content_type=ticket_entry_content_type)

                    # if single object search, lookup with entity_id
                    if single_object_search:
                        related_object_query &= models.Q(
                            ticket__pk__in=list(
                                map(lambda pk: uuid.UUID(pk), entity_id)
                            )
                        )

                    ticket_entry = list(
                        map(
                            lambda pk: str(pk) if isinstance(pk, uuid.UUID) else pk,
                            TicketEntry.objects.filter(
                                related_object_query
                            ).values_list("pk", flat=True),
                        ),
                    )

                    # if single object search, lookup with entity_id
                    if single_object_search:
                        log_entry_query &= models.Q(
                            models.Q(entity_id__in=ticket_entry)
                            | models.Q(entity_state__fields__ticket__in=entity_id),
                        )

                    # Include ticket entry logs
                    qs |= queryset.filter(log_entry_query)

        return qs

    @action(detail=False, methods=["get"])
    def count(self, request):
        qs = self.filter_queryset(self.get_queryset())
        return Response(qs.count())

    @action(detail=True, methods=["get"])
    def prettify(self, request, pk):
        from vant_dbreplication.utils import ReplicationHandler

        instance = self.get_object()

        entity_state = instance.entity_state

        # If Calculated during record_log_entry(), just display it
        if (
            entity_state.state_update_prettified
            and len(entity_state.state_update_prettified.fields) != 0
        ):
            prettified = entity_state.state_update_prettified.dict()
        else:
            origin_server = instance.origin_server
            action_type = instance.action_type
            content_type = instance.content_type

            replication_handler = ReplicationHandler()

            try:
                prettified = replication_handler.prettify_state(
                    DBReplicationLogEntry,
                    instance,
                    entity_state.dict(),
                    origin_server,
                    action_type,
                    content_type,
                )
            except Exception as e:
                logging.warning(e)
                return Response(
                    [
                        {
                            "field": "Message",
                            "previous": "Could not retrieve the detailed changes.",
                            "value": "-",
                        }
                    ]
                )

        logs = [
            {
                "field": prettified["labels"].get(field),
                "value": prettified["values"].get(field),
                "previous": prettified["previous"].get(field),
            }
            for field in prettified["fields"]
        ]

        return Response(logs)
