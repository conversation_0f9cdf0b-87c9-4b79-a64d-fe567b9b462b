from django_filters import FilterSet, <PERSON><PERSON>n<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>
from django.db.models import Q

import arrow

class DBReplicationFilterMixin(FilterSet):
    created_by_server__name__exclude = BaseInFilter(
        field_name="created_by_server__name", exclude=True
    )
    modified_by_server__name__exclude = BaseInFilter(
        field_name="modified_by_server__name", exclude=True
    )
    dbreplication_state__exclude = BaseInFilter(
        field_name="dbreplication_state", exclude=True
    )
    # --
    created_by_server__name__pattern_match = Char<PERSON>ilter(
        field_name="created_by_server__name", lookup_expr="icontains"
    )
    modified_by_server__name__pattern_match = CharFilter(
        field_name="modified_by_server__name", lookup_expr="icontains"
    )
    dbreplication_state__pattern_match = CharFilter(
        field_name="dbreplication_state", lookup_expr="icontains"
    )

    def filter_queryset(self, queryset):
        queryset = super().filter_queryset(queryset)

        cleaned_data = self.form.cleaned_data

        # catch requests starting with a pattern 'data__' for the generic data filter
        if hasattr(self.request, "GET"):
            for field_name, value in self.request.GET.items():
                if field_name not in cleaned_data and (field_name.startswith("data__") | field_name.startswith("additional_data__")):
                    queryset = self.data_filter(queryset, field_name, value)
        return queryset

    def boolean_filter(self, queryset, name, value):
        query = Q()
        selection = value.split()
        if set(["false", False]).intersection(selection):
            query &= Q(**{f"{name}": False})
        elif set(["true", True]).intersection(selection):
            query &= Q(**{f"{name}": True})
        return queryset.filter(query)

    def m2m_name_filter(self, qs, field_name, value):
        query = Q()

        field_name = field_name.split("__in")[0]
        values = value.split(",")

        if values:
            query = Q(**{f"{field_name}__in": values})
        return qs.filter(query).distinct()

    def m2m_name_exclude(self, qs, field_name, value):
        query = Q()

        field_name = field_name.split("__exclude")[0]
        values = value.split(",")

        if values:
            query = Q(**{f"{field_name}__in": values})
        return qs.exclude(query)

    def data_filter(self, qs, field_name, value):
        if "," in value:
            value = value.split(",")

        if isinstance(value, list):
            value = [i.replace("%2C", ",") for i in value]
        else:
            value = value.replace("%2C", ",")

        if "__pattern_match" in field_name:
            f = field_name.replace("__pattern_match", "")
            return qs.filter(**{f"{f}__icontains": value})

        if "__exclude" in field_name:
            is_null = field_name.replace("__exclude", "__isnull")
            if isinstance(value, list):
                f = field_name.replace("__exclude", "__in")
            else:
                f = field_name.replace("__exclude", "")

            # Needed to add field_name__isnull=False since it is excluding null rows that do not match condition
            # https://code.djangoproject.com/ticket/31894
            return qs.exclude(**{f: value, is_null: False})

        return qs.filter(**{field_name: value})

    class Meta:
        fields = {
            "created_by_server__name": ["in", "exact"],
            "modified_by_server__name": ["in", "exact"],
            "dbreplication_state": ["in", "exact"],
        }


class TimeStampedFilterMixin(FilterSet):
    created = CharFilter(method="date_range_filter")
    created__in = CharFilter(method="date_range_filter__in")
    created__exclude = CharFilter(method="date_range_filter__exclude")

    modified = CharFilter(method="date_range_filter")
    modified__in = CharFilter(method="date_range_filter__in")
    modified__exclude = CharFilter(method="date_range_filter__exclude")

    def date_range_filter(self, qs, field_name, value):
        query = Q()

        date_range = value
        start, end = date_range.split("_")
        query |= Q(**{f"{field_name}__range": (start, end)})
        return qs.filter(query)

    def date_range_filter__in(self, qs, field_name, value):
        query = Q()
        # fix field_name
        field_name = field_name.split("__in")[0]

        # break the ranges down
        date_ranges = value.split(",")
        for date_range in date_ranges:
            start, end = date_range.split("_")
            # build query
            query |= Q(**{f"{field_name}__range": (start, end)})
        return qs.filter(query)

    def date_range_filter__exclude(self, qs, field_name, value):
        query = Q()
        # fix field_name
        field_name = field_name.split("__exclude")[0]

        # break the ranges down
        date_ranges = value.split(",")
        for date_range in date_ranges:
            start, end = date_range.split("_")
            # build query
            query |= Q(**{f"{field_name}__range": (start, end)})
        return qs.exclude(query)

    def date_range_filter_inclusive(self, qs, field_name, value):
        query = Q()

        date_range = value
        start, end = date_range.split("_")

        end = arrow.get(end).shift(days=1).strftime("%Y-%m-%d")
        query |= Q(**{f"{field_name}__range": (start, end)})
        return qs.filter(query)

    def date_range_filter_inclusive__in(self, qs, field_name, value):
        query = Q()
        # fix field_name
        field_name = field_name.split("__in")[0]

        # break the ranges down
        date_ranges = value.split(",")
        for date_range in date_ranges:
            start, end = date_range.split("_")
            end = arrow.get(end).shift(days=1).strftime("%Y-%m-%d")
            # build query
            query |= Q(**{f"{field_name}__range": (start, end)})
        return qs.filter(query)

    def date_range_filter_inclusive__exclude(self, qs, field_name, value):
        query = Q()
        # fix field_name
        field_name = field_name.split("__exclude")[0]

        # break the ranges down
        date_ranges = value.split(",")
        for date_range in date_ranges:
            start, end = date_range.split("_")
            end = arrow.get(end).shift(days=1).strftime("%Y-%m-%d")
            # build query
            query |= Q(**{f"{field_name}__range": (start, end)})
        return qs.exclude(query)

    class Meta:
        fields = {}

class ActiveFilterMixin(FilterSet):
    is_active = CharFilter(method="site_specific_active_filter")
    is_active__in = CharFilter(method="site_specific_active_filter__in")
    is_active__exclude = CharFilter(method="site_specific_active_filter__exclude")

    enabled = CharFilter(method="site_specific_enable_filter")
    enabled__in = CharFilter(method="site_specific_enable_filter__in")
    enabled__exclude = CharFilter(method="site_specific_enable_filter__exclude")

    effective_start_date = CharFilter(method="date_range_filter")
    effective_start_date__in = CharFilter(method="date_range_filter__in")
    effective_start_date__exclude = CharFilter(method="date_range_filter__exclude")

    effective_end_date = CharFilter(method="date_range_filter")
    effective_end_date__in = CharFilter(method="date_range_filter__in")
    effective_end_date__exclude = CharFilter(method="date_range_filter__exclude")

    def date_range_filter(self, qs, field_name, value):
        query = Q()

        date_range = value
        start, end = date_range.split("_")
        query |= Q(**{f"{field_name}__range": (start, end)})
        return qs.filter(query)

    def date_range_filter__in(self, qs, field_name, value):
        query = Q()
        # fix field_name
        field_name = field_name.split("__in")[0]

        # break the ranges down
        date_ranges = value.split(",")
        for date_range in date_ranges:
            start, end = date_range.split("_")
            # build query
            query |= Q(**{f"{field_name}__range": (start, end)})
        return qs.filter(query)

    def date_range_filter__exclude(self, qs, field_name, value):
        query = Q()
        # fix field_name
        field_name = field_name.split("__exclude")[0]

        # break the ranges down
        date_ranges = value.split(",")
        for date_range in date_ranges:
            start, end = date_range.split("_")
            # build query
            query |= Q(**{f"{field_name}__range": (start, end)})
        return qs.exclude(query)

    def site_specific_active_filter(self, queryset, name, value):
        query = Q()
        selection = value.split()
        if set(["Site", "Specific"]).intersection(selection):
            queryset = queryset.filter(additional_data__site_specific_config__is_active=True)
        elif set(["false", False, "Inactive"]).intersection(selection):
            query &= Q(**{f"{name}": False})
        elif set(["true", True, "Active"]).intersection(selection):
            query &= Q(**{f"{name}": True})
        return queryset.filter(query)

    def site_specific_active_filter__in(self, queryset, name, value):
        query = Q()
        selection = value.split(",")
        if "Site Specific" in selection:
            query |= Q(additional_data__site_specific_config__is_active=True)
        if "Active" in selection:
            query |= Q(**{"is_active": False})
        if "Inactive" in selection:
            query |= Q(**{"is_active": True})
        return queryset.filter(query)

    def site_specific_active_filter__exclude(self, queryset, name, value):
        query = Q()
        selection = value.split(",")
        if "Site Specific" in selection:
            ids = queryset.filter(additional_data__site_specific_config__is_active=True).values_list("id", flat=True)
            query &= ~Q(id__in=ids)
        if "Active" in selection:
            query &= ~Q(**{"is_active": False})
        if "Inactive" in selection:
            query &= ~Q(**{"is_active": True})

        return queryset.filter(query)

    def site_specific_enable_filter(self, queryset, name, value):
        query = Q()
        selection = value.split()
        if set(["Site", "Specific"]).intersection(selection):
            queryset = queryset.filter(additional_data__site_specific_config__enabled=True)
        elif set(["false", False, "Disabled"]).intersection(selection):
            query &= Q(**{f"{name}": False})
        elif set(["true", True, "Enabled"]).intersection(selection):
            query &= Q(**{f"{name}": True})

        return queryset.filter(query)

    def site_specific_enable_filter__in(self, queryset, name, value):
        query = Q()
        selection = value.split(",")
        if "Site Specific" in selection:
            query |= Q(additional_data__site_specific_config__enabled=True)
        if "Disabled" in selection:
            query |= Q(**{"enabled": False})
        if "Enabled" in selection:
            query |= Q(**{"enabled": True})
        return queryset.filter(query)

    def site_specific_enable_filter__exclude(self, queryset, name, value):
        query = Q()
        selection = value.split(",")
        if "Site Specific" in selection:
            ids = queryset.filter(additional_data__site_specific_config__enabled=True).values_list("id", flat=True)
            query &= ~Q(id__in=ids)
        if "Disabled" in selection:
            query &= ~Q(**{"enabled": False})
        if "Enabled" in selection:
            query &= ~Q(**{"enabled": True})

        return queryset.filter(query)

    class Meta:
        fields = {}


class NameFilterMixin(FilterSet):
    name = CharFilter(method="escape_comma_filter")
    name__in = CharFilter(method="escape_comma_filter")
    name__exclude = CharFilter(method="escape_comma_exclude")

    code = CharFilter(method="escape_comma_filter")
    code__in = CharFilter(method="escape_comma_filter")
    code__exclude = CharFilter(method="escape_comma_exclude")
    # --
    code__pattern_match = CharFilter(field_name="code", lookup_expr="icontains")
    name__pattern_match = CharFilter(field_name="name", lookup_expr="icontains")

    class Meta:
        fields = {}

    def escape_comma_filter(self, qs, field_name, value):
        query = Q()

        field_name = field_name.split("__in")[0]
        values = value.split(",")

        for value in values:
            query |= Q(**{f"{field_name}": value.replace("%2C", ",")})
        return qs.filter(query)

    def escape_comma_exclude(self, qs, field_name, value):
        query = Q()

        field_name = field_name.split("__exclude")[0]
        values = value.split(",")

        for value in values:
            query |= Q(**{f"{field_name}": value.replace("%2C", ",")})
        return qs.exclude(query)


class LockoutFilterMixin(FilterSet):
    lockout_date = CharFilter(method="date_range_filter")
    lockout_date__in = CharFilter(method="date_range_filter__in")
    lockout_date__exclude = CharFilter(method="date_range_filter__exclude")

    class Meta:
        fields = {}

    def date_range_filter(self, qs, field_name, value):
        query = Q()

        date_range = value
        start, end = date_range.split("_")
        query |= Q(**{f"{field_name}__range": (start, end)})
        return qs.filter(query)

    def date_range_filter__in(self, qs, field_name, value):
        query = Q()
        # fix field_name
        field_name = field_name.split("__in")[0]

        # break the ranges down
        date_ranges = value.split(",")
        for date_range in date_ranges:
            start, end = date_range.split("_")
            # build query
            query |= Q(**{f"{field_name}__range": (start, end)})
        return qs.filter(query)

    def date_range_filter__exclude(self, qs, field_name, value):
        query = Q()
        # fix field_name
        field_name = field_name.split("__exclude")[0]

        # break the ranges down
        date_ranges = value.split(",")
        for date_range in date_ranges:
            start, end = date_range.split("_")
            # build query
            query |= Q(**{f"{field_name}__range": (start, end)})
        return qs.exclude(query)
