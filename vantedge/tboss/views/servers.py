from django.apps import apps
from django.contrib.contenttypes.models import ContentType
from django.core.exceptions import ObjectDoesNotExist
from django.http import QueryDict
from django.db.models import Q
from django_auto_prefetching import AutoPrefetchViewSetMixin
from vant_dbreplication.models import DBReplicationConfig, DBReplicationServer, DBReplicationWorker
from django_filters import FilterSet, Char<PERSON><PERSON>er
from rest_framework import status
from rest_framework.decorators import action
from rest_framework.pagination import LimitOffsetPagination
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.viewsets import ModelViewSet, ReadOnlyModelViewSet
from util.mixins import SearchFilterMixin
from vantedge.autocomplete.views import AutoCompleteViewSetMixin

from .mixins.viewsets import TBossCompanyMixin, FormConfigurationMixin
from vantedge.tboss.views.mixins.filters import DBReplicationFilterMixin, NameFilterMixin
from vantedge.tboss.serializers import (
    DBReplicationServerSerializer,
    DBReplicationWorkerSerializer,
)
from uuid import UUID
import json

def is_valid_uuid(uuid_to_test, version=4):
    try:
        uuid_obj = UUID(uuid_to_test, version=version)
    except ValueError:
        return False
    return True

class TBossV3ServerFilter(SearchFilterMixin, DBReplicationFilterMixin):
    search_query = CharFilter(method="search_filter")

    name = CharFilter(method="escape_comma_filter")
    name__in = CharFilter(method="escape_comma_filter")
    name__exclude = CharFilter(method="escape_comma_exclude")

    timezone = CharFilter(method="escape_comma_filter")
    timezone__in = CharFilter(method="escape_comma_filter")
    timezone__exclude = CharFilter(method="escape_comma_exclude")

    class Meta:
        model = DBReplicationServer
        fields = {
            "name": ["in", "exact"],
            "timezone": ["in", "exact"],
        }
        exclude = ["additional_data"]

    def escape_comma_filter(self, qs, field_name, value):
        query = Q()

        field_name = field_name.split("__in")[0]
        values = value.split(",")

        for value in values:
            query |= Q(**{f"{field_name}": value.replace("%2C", ",")})
        return qs.filter(query)

    def escape_comma_exclude(self, qs, field_name, value):
        query = Q()

        field_name = field_name.split("__exclude")[0]
        values = value.split(",")

        for value in values:
            query |= Q(**{f"{field_name}": value.replace("%2C", ",")})
        return qs.exclude(query)

class TBossV3ServerViewSet(TBossCompanyMixin, FormConfigurationMixin, AutoPrefetchViewSetMixin, AutoCompleteViewSetMixin, ModelViewSet):
    filterset_class = TBossV3ServerFilter
    serializer_class = DBReplicationServerSerializer
    pagination_class = LimitOffsetPagination
    permission_classes = [IsAuthenticated]

    ordering_fields = ["name"]

    # Only allow get and patch requests
    http_method_names = ['get', 'patch', 'post']

    class Meta:
        model = DBReplicationServer
        fields = "__all__"


    def get_queryset(self):
        company = self.get_terminalboss_company()
        if not company:
            return DBReplicationServer.objects.none()

        qs = DBReplicationServer.objects.filter(dbreplication_client_company=company)
        return qs


    @action(detail=False, methods=["GET", "POST"])
    def choices(self, request):
        user = request.user

        # Putting everything on URL query params does not work if there are alot of selected ids
        # Add support for POST requests to have it on the body
        if request.method == 'POST':
            body_params = json.loads(request.body.decode())
            query_dict = QueryDict('', mutable=True)
            query_dict.update(body_params)
        else:
            query_dict = request.query_params

        user_input = query_dict.get("input", "")
        server_ids = query_dict.getlist("servers[]") or query_dict.get("servers")
        model = query_dict.get("model")
        exclude = query_dict.get("exclude")
        include = query_dict.get("include")
        limit = int(query_dict.get("limit", 50))

        if server_ids is not None and not isinstance(server_ids, list):
            raise Response( {"servers": "The servers query parameters was expecting a list of server ids"}, status=status.HTTP_400_BAD_REQUEST)

        try:
            replication_servers = get_replication_servers_from_model(user, model, server_ids)

            extra_filters = {}
            exclude_filters = {}

            if include:
                extra_filters["name__in"] = include.split(",")

            if exclude:
                exclude_filters["name__in"] = exclude.split(",")

            if user_input and isinstance(user_input, str) and not is_valid_uuid(user_input):
                extra_filters["name__icontains"] = user_input

            replication_servers = replication_servers.filter(
                **extra_filters
            ).exclude(**exclude_filters)

            if limit:
                replication_servers = replication_servers[:limit]

            serializer = DBReplicationServerSerializer(
                replication_servers, many=True, **{'context': { 'request': request }}
            )
            count = replication_servers.count()

        except ObjectDoesNotExist:
            count = 0
            serializer = DBReplicationServerSerializer(
                DBReplicationServer.objects.none(), many=True, **{'context': { 'request': request }}
            )

        return Response({"suggestions": serializer.data, "count": count})


    @action(detail=False, methods=["get"], permission_classes=[IsAuthenticated])
    def count(self, request):
        qs = self.filter_queryset(self.get_queryset())
        return Response(qs.count())


class TBossV3WorkerViewSet(TBossCompanyMixin, ReadOnlyModelViewSet):
    queryset = DBReplicationWorker.objects.all()
    serializer_class = DBReplicationWorkerSerializer
    permission_classes = [IsAuthenticated]


    @action(detail=False, methods=["get"])
    def last_sync(self, request, pk=None):
        server_id = request.query_params.get("server")

        worker = DBReplicationWorker.objects.get(server__id=server_id)
        serializer = DBReplicationWorkerSerializer(instance=worker)
        return Response(serializer.data)


def get_replication_servers(user):
    # User -> Qs[DBReplicationServer]
    """
    Return dbreplication-servers based on `user` access-level.
    """
    tboss_company = user.company.terminalboss
    replication_servers = DBReplicationServer.objects.filter(
        dbreplication_client_company=tboss_company
    )
    bypass_assigned_tboss_sites = user.company.applicationconfiguration.bypass_assigned_tboss_sites
    if not (user.is_staff or user.is_superuser or bypass_assigned_tboss_sites):
        user_assigned_sites = user.assigned_tboss_sites.all()
        replication_servers = replication_servers & user_assigned_sites

    return replication_servers


def get_replication_configs(user, model, server_ids=None):
    # User str str -> Qs[DBReplicationConfig]
    """
    Get dbreplication-configs based on `user` and `model`
    [, and `server_id`]
    """
    tboss_company = user.company.terminalboss
    content_type = ContentType.objects.get_for_model(
        apps.get_model(model)
    )

    # Fetch all the defined replication configs for the tboss_company
    # for a content_type that is bidirectional (supports 2-way sync)
    replication_configs = DBReplicationConfig.objects.filter(
        content_type=content_type,
        dbreplication_client_company=tboss_company,
    )

    # didnt find the use case but for keeping consistency with old
    # code keep bidirectional filter in case there is no server_ids
    if not server_ids:
        replication_configs = replication_configs.filter(bidirectional=True)

    if server_ids is not None:

        if not isinstance(server_ids, list):
            raise Exception("Server Ids must be a list")

        # But when a server_id is provided, use that id to
        # match on replication configs, this will then be used to
        replication_configs = replication_configs.filter(
            servers__in=server_ids
        )

    return replication_configs


def get_replication_servers_from_model(user, model, server_ids):
    # str str User -> Qs[DBReplicationServer]
    """
    Get dbreplication-servers based on `user` and `model`
    [, and `server_id`]
    """
    tboss_company = user.company.terminalboss

    replication_configs = get_replication_configs(user, model, server_ids)

    replication_servers = DBReplicationServer.objects.filter(
        dbreplication_client_company=tboss_company,
        replication_configs__in=replication_configs,
    )

    # Logic is moved to q-select option to be disabled instead of excluding it completely as it leads to weird behavior
    # in serializers.py, search for get_selectable() function under DBReplicationServerSerializer

    # if not (user.is_staff or user.is_superuser):
    #     user_assigned_sites = user.assigned_tboss_sites.all()
    #     replication_servers = replication_servers & user_assigned_sites

    return replication_servers
