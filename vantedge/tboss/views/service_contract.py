from datetime import datetime, time
from uuid import UUID

from django.db.models import Q, Prefetch
from django.db.models.functions import Coalesce
from django.utils import timezone
from django_filters import Char<PERSON>ilter
from rest_framework.decorators import action
from rest_framework.pagination import LimitOffsetPagination
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.viewsets import ModelViewSet

from vantedge.autocomplete.views import AutoCompleteViewSetMixin
from vantedge.tboss.models import ServiceContract
from vantedge.tboss.models import ServiceContractProduct
from vantedge.tboss.serializers import (
    ServiceContractSerializer,
    ServiceContractSelectSerializer,
    ServiceContractListSerializer,
)
from vantedge.util.mixins import MetadataViewSetMixin
from .mixins.filters import (
    DBReplicationFilterMixin,
    TimeStampedFilterMixin,
    ActiveFilterMixin,
    NameFilterMixin,
)
from .mixins.viewsets import (
    MultiSerializerViewSetMixin,
    TBossCompanyMixin,
    ChoiceOptionsMixin,
    FormConfigurationMixin,
)
from util.mixins import SearchFilterMixin


class ServiceContractFilter(
    SearchFilterMixin,
    DBReplicationFilterMixin,
    TimeStampedFilterMixin,
    ActiveFilterMixin,
    NameFilterMixin,
):
    search_query = CharFilter(method="search_filter")
    status = CharFilter(method="status_filter")
    status__in = CharFilter(method="status_filter")
    ticket_type = CharFilter(method="ticket_type_filter")

    class Meta:
        model = ServiceContract
        fields = {
            **DBReplicationFilterMixin.Meta.fields,
            **TimeStampedFilterMixin.Meta.fields,
            **ActiveFilterMixin.Meta.fields,
            **NameFilterMixin.Meta.fields,
            "code": ["in", "exact"],
            "name": ["in", "exact"],
            "id": ["in", "exact"],
            "customer__name": ["in", "exact"]
        }

    def filter_queryset(self, queryset):
        queryset = super().filter_queryset(queryset)
        cleaned_data = self.form.cleaned_data

        # generic data filter
        if hasattr(self.request, "GET"):
            for field_name, value in self.request.GET.items():
                if field_name not in cleaned_data and field_name.startswith("data__"):
                    # print('generic' ,field_name, value)
                    queryset = self.data_filter(queryset, field_name, value)
        return queryset

    def data_filter(self, qs, field_name, value):
        if "," in value:
            value = value.split(",")

        if isinstance(value, list):
            value = [i.replace("%2C", ",") for i in value]
        else:
            value = value.replace("%2C", ",")

        if "__pattern_match" in field_name:
            f = field_name.replace("__pattern_match", "")
            return qs.filter(**{f"{f}__icontains": value})

        if "__exclude" in field_name:
            is_null = field_name.replace("__exclude", "__isnull")
            if isinstance(value, list):
                f = field_name.replace("__exclude", "__in")
            else:
                f = field_name.replace("__exclude", "")

            # Needed to add field_name__isnull=False since it is excluding null rows that do not match condition
            # https://code.djangoproject.com/ticket/31894
            return qs.exclude(**{f: value, is_null: False})

        return qs.filter(**{field_name: value})

    def status_filter(self, qs, name, values):
        now = timezone.now()
        end_of_today = datetime.combine(now.date(), time.max)
        beginning_of_today = datetime.combine(now.date(), time.min)

        values = values.split(',')
        if len(values) == 0:
            return qs

        # Exclude drafts with both dates null to simplify Active and Expired filters
        if ServiceContract.ServiceContractStatus.DRAFT.label not in values:
            qs = qs.exclude(effective_start_date__isnull=True, effective_end_date__isnull=True)

        qs = qs.annotate(
            start_date=Coalesce('effective_start_date', now),
            end_date=Coalesce('effective_end_date', now)
        )

        queries = Q()
        if ServiceContract.ServiceContractStatus.ACTIVE.label in values:
            queries |= Q(start_date__lte=end_of_today, end_date__gte=beginning_of_today) | Q(
                effective_end_date__isnull=True)

        if ServiceContract.ServiceContractStatus.EXPIRED.label in values:
            queries |= Q(end_date__lt=beginning_of_today)

        if ServiceContract.ServiceContractStatus.DRAFT.label in values:
            queries |= (
                Q(effective_start_date__gt=end_of_today) | Q(effective_start_date__isnull=True,
                                                             effective_end_date__isnull=True)
            )
        return qs.filter(queries)

    def ticket_type_filter(self, qs, name, value):
        if value:
            return qs.filter(data__ticket_type=value)
        return None


class TBossV3ServiceContractViewset(
    FormConfigurationMixin,
    TBossCompanyMixin,
    ChoiceOptionsMixin,
    MultiSerializerViewSetMixin,
    AutoCompleteViewSetMixin,
    MetadataViewSetMixin,
    ModelViewSet
):
    queryset = (
        ServiceContract.objects
        .select_related(
            'customer',
            'dbreplication_client_company',
            'created_by_server',
            'modified_by_server',
            'dbreplication_config',
            'producer'
        )
        .prefetch_related(
            Prefetch(
                'servicecontractproduct_set',
                queryset=ServiceContractProduct.objects.select_related('product')
            ),
            'product_set',
            'location_set'
        ).all()
    )
    pagination_class = LimitOffsetPagination
    serializer_class = ServiceContractSerializer
    permission_classes = [IsAuthenticated]
    filterset_class = ServiceContractFilter
    ordering_fields = [
        "created",
        "modified",
        "id",
        "code",
        "created_by_server",
        "name",
        "effective_start_date",
        "effective_end_date",
        "enabled",
        "description",
        "customer__name",
        "created_by_server__name"
    ]
    serializer_action_classes = {
        "list": ServiceContractListSerializer,
        "detail": ServiceContractSerializer,
        "choices": ServiceContractSelectSerializer,
    }

    mapping_autocomplete_fields = {
        "status": dict(ServiceContract.ServiceContractStatus.choices)
    }

    @action(detail=False, methods=["get"], permission_classes=[IsAuthenticated])
    def autocomplete(self, request, *args, **kwargs):
        field_name = self.request.query_params.get("field")

        if field_name == 'status':
            qs = self.filter_queryset(self.get_queryset())
            statuses_in_query = {sc.get_status_display() for sc in qs}
            all_statuses = set(ServiceContract.ServiceContractStatus.labels)
            suggestions = statuses_in_query.intersection(all_statuses)
            return Response({"results": suggestions, "count": len(suggestions)})

        return super().autocomplete(request, *args, **kwargs)

    def perform_create(self, serializer):
        company = self.get_terminalboss_company()
        serializer.save(dbreplication_client_company=company)

    @action(detail=True, methods=["post"])
    def clone(self, request, pk=None):
        object = self.get_object()
        object.clone()

        serializer = ServiceContractSerializer(object)
        return Response(serializer.data)

    @action(detail=True, methods=["post"])
    def bulk_update_prices(self, request, pk=None):
        import time
        update_list = request.data.get('update_list', [])
        contracts = {}
        for item in update_list:
            try:
                contract_id = UUID(item['contract_id'])
                product_id = UUID(item['product_id'])
            except ValueError as e:
                return Response({"error: Invalid UUID format"}, status=400)

            if contract_id not in contracts:
                contracts[contract_id] = {}
            contracts[contract_id][product_id] = item['new_price']
        for contract_id, product_prices in contracts.items():
            try:
                service_contract = ServiceContract.objects.get(id=contract_id)
                service_contract.update_prices(new_product_prices=product_prices)
            except Exception as e:
                return Response({"error": f"An error occurred while updating contract"}, status=500)
        end = time.time()
        return Response({"status": "success"}, status=200)

    @action(detail=False, methods=["get"])
    def count(self, request):
        qs = self.filter_queryset(self.get_queryset())
        return Response(qs.count())
