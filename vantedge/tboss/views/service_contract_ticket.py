from datetime import datetime, time
from django.apps import apps
from django.db.models import Q
from django.db.models.functions import Coalesce
from django.utils import timezone

from rest_framework.response import Response
from rest_framework.decorators import action
from rest_framework.pagination import LimitOffsetPagination
from rest_framework.permissions import IsAuthenticated
from rest_framework.viewsets import ModelViewSet
from django_filters import Char<PERSON>ilter

from .mixins.viewsets import (
    MultiSerializerViewSetMixin,
    TBossCompanyMixin,
    ChoiceOptionsMixin,
    FormConfigurationMixin,
)
from .mixins.filters import (
    DBReplicationFilterMixin,
    TimeStampedFilterMixin,
    ActiveFilterMixin,
    NameFilterMixin,
)
from vantedge.autocomplete.views import AutoCompleteViewSetMixin
from vantedge.tboss.serializers import (
    ServiceContractSerializer,
    ServiceContractSelectSerializer,
    ServiceContractTicketSelectSerializer,
    ServiceContractListSerializer,
    ServiceContractTicketSerializer,
)
from vant_tboss.models import Company
from vantedge.tboss.models import ServiceContract, ServiceContractTicket


class ServiceContractTicketFilter(DBReplicationFilterMixin, TimeStampedFilterMixin, ActiveFilterMixin, NameFilterMixin):
    class Meta:
        model = ServiceContractTicket
        fields = {
            **DBReplicationFilterMixin.Meta.fields,
            **TimeStampedFilterMixin.Meta.fields,
            **ActiveFilterMixin.Meta.fields,
            **NameFilterMixin.Meta.fields,
            "id": ["in", "exact"],
            "service_contract__id": ["in", "exact"],
        }

    invoice_isnull = CharFilter(method="invoice_isnull_filter")
    ticket_state = CharFilter(method="ticket_state_filter")

    def invoice_isnull_filter(self, qs, name, value):
        if value == "true":
            return qs.filter(ticket_invoice__isnull=True)
        else:
            return qs.filter(ticket_invoice__isnull=False)

    def ticket_state_filter(self, qs, name, value):
        return qs.filter(ticket__data__additional_fields__state = value)

class TBossV3ServiceContractTicketViewset(
        TBossCompanyMixin,
        ChoiceOptionsMixin,
        MultiSerializerViewSetMixin,
        AutoCompleteViewSetMixin,
        ModelViewSet):
    queryset = ServiceContractTicket.objects.all()
    serializer_class = ServiceContractTicketSerializer
    permission_classes = [IsAuthenticated]
    pagination_class = LimitOffsetPagination
    filterset_class = ServiceContractTicketFilter
    ordering_fields = [
        "created",
        "modified",
        "id",
        "created_by_server",
        "name",
        "enabled",
        "description"
    ]
    serializer_action_classes = {
        "list": ServiceContractListSerializer,
        "detail": ServiceContractSerializer,
        "choices": ServiceContractTicketSelectSerializer,
    }
    def perform_create(self, serializer):
        company = self.get_terminalboss_company()
        serializer.save(dbreplication_client_company=company)

    @action(detail=False, methods=["get"])
    def count(self, request):
        qs = self.filter_queryset(self.get_queryset())
        return Response(qs.count())