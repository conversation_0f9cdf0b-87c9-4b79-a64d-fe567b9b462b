from django.db.models import Q

from django_filters import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, CharFilter

from ..mixins.filters import (
    DBReplicationFilterMixin,
    TimeStampedFilterMixin,
    ActiveFilterMixin,
    NameFilterMixin,
)
from util.mixins import SearchFilterMixin
from ...models import TBossUser


class TBossUserFilter(
    SearchFilterMixin,
    DBReplicationFilterMixin,
    TimeStampedFilterMixin,
    ActiveFilterMixin,
    NameFilterMixin,
):
    search_query = CharFilter(method="search_filter")
    is_staff = CharFilter(method="boolean_filter")

    username__pattern_match = CharFilter(field_name="username", lookup_expr="icontains")
    username__exclude = BaseInFilter(field_name="username", exclude=True)

    telephone__pattern_match = CharFilter(
        field_name="telephone", lookup_expr="icontains"
    )
    telephone__exclude = BaseInFilter(field_name="telephone", exclude=True)

    groups__name__pattern_match = Char<PERSON>ilter(
        field_name="groups__name", lookup_expr="icontains"
    )
    groups__name = CharFilter(method="m2m_name_filter")
    groups__name__in = CharFilter(method="m2m_name_filter")
    groups__name__exclude = CharFilter(method="m2m_name_exclude")

    certification__name__pattern_match = CharFilter(
        field_name="certification__name", lookup_expr="icontains"
    )
    certification__name = CharFilter(method="m2m_name_filter")
    certification__name__in = CharFilter(method="m2m_name_filter")
    certification__name__exclude = CharFilter(method="m2m_name_exclude")

    user_server_associations__server__name__pattern_match = CharFilter(
        field_name="user_server_associations__server__name", lookup_expr="icontains"
    )
    user_server_associations__server__name = CharFilter(method="m2m_name_filter")
    user_server_associations__server__name__in = CharFilter(method="m2m_name_filter")
    user_server_associations__server__name__exclude = CharFilter(method="m2m_name_exclude")

    user_permissions__name__pattern_match = CharFilter(
        field_name="user_permissions__name", lookup_expr="icontains"
    )
    user_permissions__name = CharFilter(method="m2m_name_filter")
    user_permissions__name__in = CharFilter(method="m2m_name_filter")
    user_permissions__name__exclude = CharFilter(method="m2m_name_exclude")



    class Meta:
        model = TBossUser
        fields = {
            **DBReplicationFilterMixin.Meta.fields,
            **TimeStampedFilterMixin.Meta.fields,
            **ActiveFilterMixin.Meta.fields,
            **NameFilterMixin.Meta.fields,
            "username": ["in", "exact"],
            "email": ["in", "exact"],
            "telephone": ["in", "exact"],
            "groups__name": ["in", "exact"],
            "name": ["in", "exact"]
        }


class TBossStaffFilter(TBossUserFilter):
    class Meta:
        model = TBossUser
        fields = {
            **DBReplicationFilterMixin.Meta.fields,
            **TimeStampedFilterMixin.Meta.fields,
            **ActiveFilterMixin.Meta.fields,
            **NameFilterMixin.Meta.fields,
            "username": ["in", "exact"],
            "email": ["in", "exact"],
            "telephone": ["in", "exact"],
            "is_active": ["in", "exact"],
            "groups__name": ["in", "exact"],
            "name": ["in", "exact"]
        }


class TBossDriverFilter(TBossUserFilter):
    class Meta:
        model = TBossUser
        fields = {
            **DBReplicationFilterMixin.Meta.fields,
            **TimeStampedFilterMixin.Meta.fields,
            **ActiveFilterMixin.Meta.fields,
            **NameFilterMixin.Meta.fields,
            "username": ["in", "exact"],
            "email": ["in", "exact"],
            "telephone": ["in", "exact"],
            "groups__name": ["in", "exact"],
            "name": ["in", "exact"]
        }
