from django.contrib.auth.password_validation import validate_password
from rest_framework.response import Response
from rest_framework.decorators import action
from rest_framework.pagination import LimitOffsetPagination
from rest_framework.permissions import IsAuthenticated
from rest_framework.viewsets import ModelViewSet
from rest_framework import status

from vantedge.autocomplete.views import AutoCompleteViewSetMixin
from vantedge.util.download.mixin import DownloadTableMixin
from ..mixins.viewsets import (
    MultiSerializerViewSetMixin,
    TBossCompanyMixin,
    ChoiceOptionsMixin,
    FormConfigurationMixin
)

from vantedge.tboss.serializers import (
    TBossStaffSerializer,
    TBossStaffSelectSerializer,
    TBossStaffListSerializer,
)
from vantedge.tboss.models import TBossUser
from .filter import TBossStaffFilter
from .mixins import TBossV3UserMixin


class TBossV3StaffViewset(
    FormConfigurationMixin,
    TBossCompanyMixin,
    ChoiceOptionsMixin,
    MultiSerializerViewSetMixin,
    AutoCompleteViewSetMixin,
    TBossV3UserMixin,
    DownloadTableMixin,
    ModelViewSet,
):
    queryset = TBossUser.objects.get_staff_members()
    serializer_class = TBossStaffSerializer
    pagination_class = LimitOffsetPagination
    permission_classes = [IsAuthenticated]
    filterset_class = TBossStaffFilter
    ordering_fields = [
        "dbreplication_state",
        "created_by_server",
        "modified_by_server",
        "last_login",
        "email",
        "is_active",
        "date_joined",
        "created",
        "modified",
        "id",
        "username",
        "name",
        "telephone",
        "code",
        "effective_start_date",
        "effective_end_date",
        "user_server_associations__server__name",
        "groups__name",
        "certification__name"
    ]
    serializer_action_classes = {
        "list": TBossStaffListSerializer,
        "detail": TBossStaffSerializer,
        "choices": TBossStaffSelectSerializer,
    }
    table_name = "Staff"

    def create(self, request, *args, **kwargs):

        # --------------------------------
        # --- is_staff
        request.data["is_staff"] = True

        if not bool(request.data.get("groups")):
            request.data["groups"] = list()

        if not bool(request.data.get("user_permissions")):
            request.data["user_permissions"] = list()
        
        if not bool(request.data.get("company_set")):
            request.data["company_set"] = list()

        # --------------------------------
        invalid_password = None
        password = request.data.get('password')
        try:
            validate_password(password)
        except Exception as e:
            invalid_password = e

        if invalid_password:
            return Response(invalid_password, status.HTTP_400_BAD_REQUEST)

        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        self.perform_create(serializer)
        headers = self.get_success_headers(serializer.data)
        return Response(
            serializer.data, status=status.HTTP_201_CREATED, headers=headers
        )

    def filter_queryset(self, queryset):
        qs = super().filter_queryset(queryset)
        return qs

    def perform_create(self, serializer):
        company = self.get_terminalboss_company()
        serializer.save(dbreplication_client_company=company)

    @action(detail=False, methods=["get"])
    def count(self, request):
        qs = self.filter_queryset(self.get_queryset())
        return Response(qs.count())
