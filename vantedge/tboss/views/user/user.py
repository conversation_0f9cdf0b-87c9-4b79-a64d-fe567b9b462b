from django.contrib.contenttypes.models import ContentType
from django.contrib.auth.password_validation import validate_password
from django.db.models import Q, Value, Case, When
from django.http import QueryDict
from rest_framework.response import Response
from rest_framework.decorators import action
from rest_framework.pagination import LimitOffsetPagination
from rest_framework.permissions import IsAuthenticated
from rest_framework.viewsets import ModelViewSet
from rest_framework import status

from vantedge.autocomplete.views import AutoCompleteViewSetMixin
from vantedge.util.download.mixin import DownloadTableMixin

from ..mixins.viewsets import (
    MultiSerializerViewSetMixin,
    TBossCompanyMixin,
    ChoiceOptionsMixin,
    FormConfigurationMixin,
    is_valid_uuid
)
from .mixins import TBossV3UserMixin

from vantedge.tboss.serializers import (
    TBossUserSerializer,
    TBossUserSelectSerializer,
    TBossUserListSerializer,
)
from vantedge.tboss.models import TBossUser
from .filter import TBossUserFilter
from vant_dbreplication.models import DBReplicationServer, DBReplicationConfig
from vant_dbreplication.utils import ReplicationHandler
import json

replication_handler = ReplicationHandler()

def get_company_wheelhouse_server(company):

    if hasattr(company, 'terminalboss'):
        client_company = company.terminalboss

        return (
            DBReplicationServer.objects.filter(dbreplication_client_company=client_company)
            .filter(name__contains='Wheelhouse').first()
        )



class TBossV3UserViewset(
    FormConfigurationMixin,
    TBossCompanyMixin,
    ChoiceOptionsMixin,
    MultiSerializerViewSetMixin,
    AutoCompleteViewSetMixin,
    TBossV3UserMixin,
    DownloadTableMixin,
    ModelViewSet,
):
    queryset = TBossUser.objects.exclude(Q(Q(groups__name="Driver")&Q(is_staff=False, is_superuser=False)))
    serializer_class = TBossUserSerializer
    pagination_class = LimitOffsetPagination
    permission_classes = [IsAuthenticated]
    filterset_class = TBossUserFilter
    ordering_fields = [
        "dbreplication_state",
        "created_by_server",
        "modified_by_server",
        "last_login",
        "email",
        "is_active",
        "date_joined",
        "created",
        "modified",
        "id",
        "username",
        "name",
        "telephone",
        "code",
        "effective_start_date",
        "effective_end_date",
        "user_server_associations__server__name",
        "groups__name",
        "certification__name"
    ]
    serializer_action_classes = {
        "list": TBossUserListSerializer,
        "detail": TBossUserSerializer,
        "choices": TBossUserSelectSerializer,
    }
    table_name = "User"

    def create(self, request, *args, **kwargs):
        # --------------------------------
        if not bool(request.data.get("groups")):
            request.data["groups"] = list()

        if not bool(request.data.get("user_permissions")):
            request.data["user_permissions"] = list()

        if not bool(request.data.get("company_set")):
            request.data["company_set"] = list()

        # --------------------------------
        invalid_password = None
        password = request.data.get("password")
        try:
            validate_password(password)
        except Exception as e:
            invalid_password = e

        if invalid_password:
            return Response(invalid_password, status.HTTP_400_BAD_REQUEST)

        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        self.perform_create(serializer)
        user = serializer.instance
        user.set_password(password)
        headers = self.get_success_headers(serializer.data)
        return Response(
            serializer.data, status=status.HTTP_201_CREATED, headers=headers
        )

    def filter_queryset(self, queryset):
        qs = super().filter_queryset(queryset)
        return qs

    def perform_create(self, serializer):
        company = self.get_terminalboss_company()
        serializer.save(dbreplication_client_company=company)

    @action(detail=False, methods=["get"])
    def count(self, request):
        qs = self.filter_queryset(self.get_queryset())
        return Response(qs.count())

    @action(detail=False, methods=["get", "post"])
    def choices(self, request):
        suggestions = []

        # Putting everything on URL query params does not work if there are alot of selected ids
        # Add support for POST requests to have it on the body
        if request.method == 'POST':
            body_params = json.loads(request.body.decode())
            query_dict = QueryDict('', mutable=True)
            query_dict.update(body_params)
        else:
            query_dict = request.query_params

        selected_ids = query_dict.getlist("selected_ids[]") or query_dict.get("selected_ids")
        user_input = query_dict.get("input")
        server_ids = query_dict.getlist("servers[]") or query_dict.get("servers")
        limit = int(query_dict.get("limit", 15))
        offset = int(query_dict.get("offset", 0))
        company = request.user.company

        qs = TBossUser.objects.all()
        model = qs.model

        if server_ids:
            if isinstance(server_ids, list):
                qs = (
                    qs
                    .filter(dbreplication_config__servers__id__in=server_ids)
                )
            else:
                qs = (
                    qs
                    .filter(dbreplication_config__servers__id=server_ids)
                )

        model_association = replication_handler.get_server_association_model(model)
        CLIENT_COMPANY_WHEELHOUSE_SERVER = get_company_wheelhouse_server(company)

        # add for GIP/AltaGas
        replication_configs = DBReplicationConfig.objects.filter(
            content_type=ContentType.objects.get_for_model(model),
            dbreplication_client_company=request.user.company.terminalboss
        )
        uses_server_associations = list(filter(lambda config: config.replicate_changes_using_server_associations, replication_configs))

        # filter suggestions based on association to the given site(s)/server(s):
        if model_association and uses_server_associations:
            assoc_qs = None
            associated_entity_ids = list()
            is_client_company_wheelhouse_server = False

            if server_ids:

                if isinstance(server_ids, list):
                    assoc_qs = model_association.objects.filter(server_id__in=server_ids)
                    is_client_company_wheelhouse_server = (
                        CLIENT_COMPANY_WHEELHOUSE_SERVER and
                        server_ids == [str(CLIENT_COMPANY_WHEELHOUSE_SERVER.id)]
                    )
                else:
                    assoc_qs = model_association.objects.filter(server_id=server_ids)
                    is_client_company_wheelhouse_server = (
                        CLIENT_COMPANY_WHEELHOUSE_SERVER and
                        server_ids == CLIENT_COMPANY_WHEELHOUSE_SERVER.id
                    )

                associated_entity_ids = list(assoc_qs.values_list('entity_id', flat=True))
                # entity_ids that are "active" for the given site(s)/server(s)
                # no need to check for association if server_ids = CLIENT_COMPANY_WHEELHOUSE_SERVER
                if not is_client_company_wheelhouse_server:
                    qs = qs.filter(id__in=associated_entity_ids)

            elif CLIENT_COMPANY_WHEELHOUSE_SERVER:
                from_wheelhouse_entity_ids = list()
                from_wheelhouse_entity_ids = list(
                    self.filter_queryset(self.get_queryset())
                        .filter(created_by_server=CLIENT_COMPANY_WHEELHOUSE_SERVER)
                        .values_list('id', flat=True)
                )
                qs = qs.filter(id__in=from_wheelhouse_entity_ids)

            else:
                qs = model.objects.none()

        # ----- autocomplete suggestions based on user-input
        if user_input and isinstance(user_input, str) and len(user_input) and not is_valid_uuid(user_input):
            query = Q()

            if hasattr(model, "name"):
                query |= Q(name__icontains=user_input)

            if hasattr(model, "code"):
                query |= Q(code__icontains=user_input)

            qs = qs.filter(query)

            count = qs.distinct().count()
            suggestions = qs.distinct()[offset:offset+limit]
            serializer = self.get_serializer_class()(suggestions, many=True)

            return Response({"suggestions": serializer.data, "count": count, "limit": limit, "offset": offset})

        # ----- suggestions are composed of selected_options and suggestions (in order)
        else:
            if selected_ids:
                # selected_ids are always above non-selected options
                # so that map-options is successful on the front-end
                # duplicates are removed:
                #   selections is removed from qs before `union(...)`
                if not isinstance(selected_ids, list):
                    selected_ids = [selected_ids]

                selection = model.objects.filter(id__in=selected_ids)
                limit += selection.count()
                preserved = Case(*[When(pk=pk, then=pos) for pos, pk in enumerate(selected_ids)])
                qs = (qs | selection).order_by(preserved)

            count = qs.distinct().count()
            suggestions = qs.distinct()[offset:offset+limit]
            serializer = self.get_serializer_class()(suggestions, many=True)

            return Response({"suggestions": serializer.data, "count": count, "limit": limit, "offset": offset})
