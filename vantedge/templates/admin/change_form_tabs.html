{% extends "admin/change_form.html" %}
{% load i18n admin_modify admin_urls tabbed_admin_tags %}
{% load add_preserved_filters from admin_urls %}

{% block object-tools-items %}
  {% for tool in objectactions %}
    <li class="objectaction-item" data-tool-name="{{ tool.name }}">
      {% url tools_view_name pk=object_id tool=tool.name as action_url %}
      <a href="{% add_preserved_filters action_url %}" title="{{ tool.standard_attrs.title }}"
         {% for k, v in tool.custom_attrs.items %}
           {{ k }}="{{ v }}"
         {% endfor %}
         class="{{ tool.standard_attrs.class }}">
      {{ tool.label|capfirst }}
      </a>
    </li>
  {% endfor %}
  {{ block.super }}
{% endblock %}


{% block content %}
  <div id="content-main">
    {% block object-tools %}{{ block.super }}{% endblock %}
    <form {% if has_file_field %}enctype="multipart/form-data" {% endif %}action="{{ form_url }}" method="post"
          id="{{ opts.model_name }}_form" novalidate>{% csrf_token %}{% block form_top %}{% endblock %}
      <div>
        {% if is_popup %}<input type="hidden" name="{{ is_popup_var }}" value="1"/>{% endif %}
        {% if to_field %}<input type="hidden" name="{{ to_field_var }}" value="{{ to_field }}"/>{% endif %}
        {% if save_on_top %}{% block submit_buttons_top %}{% submit_row %}{% endblock %}{% endif %}
        {% if errors %}
          <p class="errornote">
            {% if errors|length == 1 %}{% trans "Please correct the error below." %}{% else %}
              {% trans "Please correct the errors below." %}{% endif %}
          </p>
          {{ adminform.form.non_field_errors }}
        {% endif %}

        <!-- start admin_tabs stuff -->
        <div id="tabs">
          <ul>
            {% for tab in tabs.fields %}
              <li><a href="#tabs-{{ forloop.counter }}" id="for_tabs-{{ forloop.counter }}">{{ tab.name }}</a></li>
            {% endfor %}
          </ul>
          {% for tab in tabs.fields %}
            <div id="tabs-{{ forloop.counter }}" class="{{ tab.name }}">
              <div class="flex-panel">
                {% for entry in tab.entries %}
                  {% render_tab_fieldsets_inlines entry %}
                {% endfor %}
              </div>
            </div>
          {% endfor %}
        </div>
        <script type="text/javascript">
          (function ($) {
            $(window).scrollTop()
            $('#tabs').tabs({
              {% if add %}
                // when adding, don't select a tab by default, we'll do it ourselves
                // by finding the first available tab.
                selected: -1
              {% endif %}
            });

            // disable tabs marked as such in page_config
            var enabled_tabs = [];
            var disabled_tabs = [];

            {% for tab in page_config %}
              {% if tab.enabled %}
                enabled_tabs.push({{ forloop.counter0 }});
              {% else %}
                disabled_tabs.push({{ forloop.counter0 }});
              {% endif %}
            {% endfor %}

            for (var i = 0; i < disabled_tabs.length; i++) {
              $('#tabs').tabs("disable", disabled_tabs[i]);
            }
            // enable the first non-disabled tab in add view
            {% if add %}
              $('#tabs').tabs("option", "active", enabled_tabs[0]);
            {% endif %}

            // Hightlight tabs with errors inside
            $('#tabs > div').each(function () {
              if ($(this).find('.errorlist').length) {
                $('#tabs #for_' + this.id).addClass("errortab");
              }
            });

            $("#tabs").on('tabsactivate', function (event, ui) {
              var scrollPos = $(window).scrollTop();
              var hash = ui.newTab.children("li a").attr("href");
              window.location.hash = hash;
              $(window).scrollTop(scrollPos);
            });

            if ($('.errornote').length) {
              $('.errornote').addClass('tabbed-errornote');
            }


          })(django.jQuery);
        </script>
        <!-- end admin_tabs stuff -->

        {% block after_field_sets %}{% endblock %}

        {% block after_related_objects %}{% endblock %}

        {% block submit_buttons_bottom %}{% submit_row %}{% endblock %}

        {% if adminform and add and adminform.first_field and adminform.first_field.id_for_label %}
          <script type="text/javascript">document.getElementById("{{ adminform.first_field.id_for_label }}").focus();</script>
        {% endif %}

        {% prepopulated_fields_js %}

      </div>
    </form>
  </div>
{% endblock %}
