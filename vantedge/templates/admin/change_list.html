{% extends "admin/change_list.html" %}
{% load add_preserved_filters from admin_urls %}

{# Original template renders object-tools only when has_add_permission is True. #}
{# This hack allows sub templates to add to object-tools #}
{% block object-tools %}
<ul class="object-tools">
    {% block object-tools-items %}
    {% for tool in objectactions %}
    <li class="objectaction-item" data-tool-name="{{ tool.name }}">
      {% url tools_view_name tool=tool.name as action_url %}
      <a href="{% add_preserved_filters action_url %}" title="{{ tool.standard_attrs.title }}"
         {% for k, v in tool.custom_attrs.items %}
           {{ k }}="{{ v }}"
         {% endfor %}
         class="{{ tool.standard_attrs.class }}">
      {{ tool.label|capfirst }}
      </a>
    </li>
  {% endfor %}
    {% if has_add_permission %}
    {{ block.super }}
    {% endif %}
    {% endblock %}
</ul>
{% endblock %}