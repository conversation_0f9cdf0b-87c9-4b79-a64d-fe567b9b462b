.admin-interface {
    overflow-x: hidden;
}

/* fix login */
.admin-interface.login #container {
    width: 100%;
    max-width: 360px;
    margin: 15px auto;
    box-sizing: border-box;
    -moz-box-sizing: border-box;
    -webkit-box-sizing: border-box;
}

.admin-interface.login #content {
    padding: 15px 30px 30px 30px;
}

@media (min-width:768px){
    .admin-interface.login #container {
        margin: 90px auto;
    }
}

.admin-interface.login #header {
    min-height: auto;
    padding: 10px 30px;
    line-height: 30px;
    align-items: center;
    justify-content: flex-start;
}

.admin-interface.login #header #branding h1 {
    margin-right:0;
}

.admin-interface.login #header #branding h1 img.logo {
    margin-right: 0;
}

.admin-interface.login #header #branding h1 img.logo+span {
    display: block;
}

.admin-interface.login #login-form {
    display: flex;
    flex-direction: column;
}

.admin-interface.login .submit-row {
    float: left;
    width: 100%;
    margin-top: 20px;
    padding-top: 0;
    padding-left: 0;
    text-align: right;
}

.admin-interface.login .submit-row label {
    display: none;
}

.admin-interface.login .submit-row input[type="submit"] {
    width: 100%;
    text-transform: uppercase;
}

.admin-interface.login #footer {
    display: none;
}
/* end login fix*/

.admin-interface #header {
    height: auto;
    min-height: 55px;
    box-sizing: border-box;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

@media (max-width:1024px) {
    .admin-interface #header {
        align-items: start;
    }
}

.admin-interface #branding h1 img.logo {
    margin-top:10px;
    margin-bottom:10px;
    margin-right:15px;
    display:inline-block !important; /* override inline display:none; */
}

.admin-interface #branding h1 span {
    display: inline-block;
}

.admin-interface #branding h1 img.logo+span {
    white-space:nowrap;
}

.admin-interface #user-tools {
    margin-top: 10px;
    margin-bottom: 10px;
    white-space: nowrap;
    align-self: flex-start;
}

.admin-interface #user-tools br {
    display: none;
}
@media (max-width: 768px) {
    .admin-interface #user-tools br {
        display: block;
    }
}

.admin-interface fieldset.collapse {
    border: 1px solid transparent;
}

.admin-interface fieldset.collapse.collapsed a.collapse-toggle,
.admin-interface fieldset.collapse a.collapse-toggle,
.admin-interface .inline-group .inline-related fieldset.module a.collapse-toggle,
.admin-interface .inline-group .inline-related fieldset.module.collapsed a.collapse-toggle {
    font-weight: normal;
    text-transform: lowercase;
    font-size: 12px;
    text-decoration: underline;
    padding: 0 1px;
}

@media (min-width: 1024px) {
    .admin-interface #changelist .actions .button,
    .admin-interface #changelist .actions .action-counter {
        margin-left: 8px;
    }
}

.admin-interface #changelist .paginator {
    margin-top:-1px; /* merge 2 borders into 1 */
    line-height:42px;
}

.admin-interface .paginator a,
.admin-interface .paginator a:link,
.admin-interface .paginator a:visited,
.admin-interface .paginator .this-page {
    padding:7px 12px;
}

.admin-interface .paginator a,
.admin-interface .paginator .this-page {
    margin-left:0px;
}

.admin-interface .paginator .this-page,
.admin-interface .paginator a.end {
    margin-right:25px;
}

.admin-interface .paginator .this-page + a:not(.showall)  {
    margin-left:-25px;
}

body.admin-interface .paginator a.showall,
body.admin-interface .paginator a.showall:link,
body.admin-interface .paginator a.showall:visited {
    margin-left:20px;
}

/* fix help text icon on newline */
.admin-interface .inline-group thead th {
    white-space:nowrap;
}

.admin-interface .inline-group thead th img {
    vertical-align: -2px;
    margin-left: 5px;
}

.admin-interface .inline-group .inlinechangelink {
    background-size: contain;
    padding-left: 15px;
    margin-left: 10px;
}

.admin-interface .file-thumbnail > a {
    display: inline-block;
}

.admin-interface .aligned p.file-upload {
    display:table;
}

.admin-interface form .form-row p.file-upload > a {
    margin-right:20px;
}

.admin-interface form .form-row p.file-upload .clearable-file-input {
    display:inline-block;
}

.admin-interface form .form-row p.file-upload .clearable-file-input label {
    padding-bottom:0px;
    margin-left:2px;
}

.admin-interface form .form-row p.file-upload > input[type="file"] {
    margin-top: 0px;
}

@media (max-width:767px){

    .admin-interface form .form-row p.file-upload {
        width: 100%;
    }

    .admin-interface form .form-row p.file-upload > a {
        margin-right:0px;
        display: block;
        white-space: pre-wrap;
        word-break: break-word;
    }

    .admin-interface form .form-row p.file-upload .clearable-file-input {
        display: block;
        margin-top: 10px;
        margin-left: 0;
        margin-bottom: -10px;
    }

    .admin-interface form .form-row p.file-upload > input[type="file"] {
        display: block;
        width: auto;
        padding: 0px;
    }

    /* fix inline horizontal scroll caused by checkbox-row */
    .admin-interface form .form-row > div.checkbox-row {
        width: 100%;
    }
}


/* FIX WIDE FIELDSET HELPS / ERROR MESSAGES */
.admin-interface form .wide p.help,
.admin-interface form .wide div.help {
    padding-left: 50px;
}

.admin-interface form .wide input + p.help,
.admin-interface form .wide input + div.help {
    margin-left: 160px;
}

@media (max-width:767px){
    .admin-interface form .form-row div.help {
        display: block;
        width: 100%;
    }
}

.admin-interface form .wide ul.errorlist {
    margin-left: 200px;
}

/* LIST FILTER */
.admin-interface .module.filtered h2 {
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
}

.admin-interface .module.filtered #changelist-filter {
    min-width: 240px;
}

@media (max-width: 1024px) {
    .admin-interface .module.filtered #changelist-filter {
        min-width: 200px;
    }
}

.admin-interface .module.filtered #changelist-filter h2 {
    font-size: 11px;
    padding: 10px 15px;
}

.admin-interface .module.filtered #changelist-filter h2 + h3 {
    margin-top: 0px;
}

.admin-interface .module.filtered #changelist-filter h3 {
    margin-top: 12px;
    margin-bottom: 12px;
}

.admin-interface #changelist-form .results {
    scrollbar-width: thin;
}

/* begin fix issue #13 - Datetime widget broken in long inlines */
.admin-interface p.datetime {
    white-space:nowrap;
}
/* end fix */

/* begin fix lateral padding to align text with field labels */
.admin-interface .module h2,
.admin-interface.dashboard .module caption,
.admin-interface.dashboard .module th,
.admin-interface .module.filtered h2,
.admin-interface .inline-group h2,
.admin-interface #nav-sidebar .module caption,
.admin-interface #nav-sidebar .module th {
    padding-left: 10px;
    padding-right: 10px;
}
/* end fix */

/* begin fix restrict tabular-inline horizontal-scroll to inline-group instead of whole page */
.admin-interface .inline-group[data-inline-type="tabular"] {
    overflow-x:auto;
}
/* end fix */

/* begin fix stacked-inline margin-bottom in responsive small viewport */
.admin-interface .inline-group[data-inline-type="stacked"] .module {
    margin-bottom:0px;
}
/* end fix */

/* begin fix tabular inlines horizontal scroll */
.admin-interface .inline-related.tabular {
    overflow-x: scroll;
    overflow-y: hidden;
}
.admin-interface .inline-related.tabular fieldset.module {
    display: contents;
    width: 100%;
    white-space: nowrap;
    position: relative;
}
.admin-interface .inline-related.tabular fieldset.module h2 {
    position: sticky;
    left: 0;
}
.admin-interface .inline-related.tabular fieldset.module table {
    scrollbar-width: thin;
}
.admin-interface .inline-related.tabular fieldset.module table tbody tr {
    position: relative;
}
/* end fix */

.admin-interface .inline-related h3 {
    padding:6px 10px;
}

/* begin fix issue #12 - Inlines bad delete buttons alignement */
.admin-interface .inline-group .tabular thead th:last-child:not([class]):not([style]) {
    text-align:right;
}

.admin-interface .inline-group .tabular tr td {
    vertical-align: top;
}

.admin-interface .inline-group .tabular tr td.delete {
    text-align:right;
    padding-right:15px;
    vertical-align: top;
}

.admin-interface .inline-group .tabular tr td input[type="checkbox"] {
    margin: 7px 0px;
}

.admin-interface .inline-group .tabular tr td.delete a.inline-deletelink {
    margin-top:4px;
    overflow:hidden;
    text-indent:9999px;
}
/* end fix */

/* top-right buttons color on hover -> just a lighten grey */
.admin-interface .object-tools a {
    color:#FFFFFF;
}
.admin-interface .object-tools a:focus,
.admin-interface .object-tools a:hover,
.admin-interface .object-tools li:focus a,
.admin-interface .object-tools li:hover a {
    background-color:#AAAAAA;
}

/* improve responsive selector */

/* fix [stacked, not-stacked] equalize horizontal and vertical select padding for selector */
.admin-interface .selector .selector-available select,
.admin-interface .selector .selector-chosen select {
    padding: 7px 10px;
    display: block;
}

/* fix [stacked, not-stacked] select options text overflow */
.admin-interface .selector .selector-available select option,
.admin-interface .selector .selector-chosen select option {
    overflow: hidden;
    text-overflow: ellipsis;
}

/* fix [not-stacked] equalize selectors height by adding the height of the .selector-available filter-bar */
.admin-interface .selector:not(.stacked) .selector-chosen select {
    height: calc(46px + 17.2em) !important;
}

/* fix nav-sidebar (added in django 3.1.0) */
.admin-interface #toggle-nav-sidebar {
    top: 10px;
    left: 0;
    z-index: 20;
    flex: 0 0 30px;
    width: 30px;
    height: 45px;
    margin-top: 10px;
    margin-right: -30px;
    background-color: #FFFFFF;
    font-size: 16px;
    border: 1px solid #eaeaea;
    border-left: none;
    outline: none;
    -webkit-box-shadow: 4px 4px 8px -4px #DBDBDB;
    -moz-box-shadow: 4px 4px 8px -4px #DBDBDB;
    box-shadow: 4px 4px 8px -4px #DBDBDB;
    /*transition: left .3s;*/
}

.admin-interface .toggle-nav-sidebar::before {
    margin-top: -2px;
}

.admin-interface .main > #nav-sidebar + .content,
.admin-interface .main.shifted > #nav-sidebar + .content {
    max-width: 100%;
}

/* hide nav-sidebar below 1280px to prevent horizontal overflow issues */
@media (max-width:1279px) {
    .admin-interface #nav-sidebar,
    .admin-interface #toggle-nav-sidebar {
        display: none;
    }
}

.admin-interface #nav-sidebar {
    flex: 0 0 360px;
    left: -360px;
    margin-left: -360px;
    box-sizing: border-box;
    -moz-box-sizing: border-box;
    -webkit-box-sizing: border-box;
    padding: 40px 40px 40px 40px;
    border-top: none;
    border-bottom: none;
    border-left: none;
    scrollbar-width: thin;
    /*transition: left .3s, margin-left .3s;*/
}

.admin-interface #nav-filter {
    background-color: transparent;
    border-radius: 4px;
    height: 30px;
    margin: 0 0 30px 0;
    padding: 5px 6px;
    outline-width: initial;
}

@media (min-width:1280px) {
    .admin-interface #main.shifted > #toggle-nav-sidebar {
        left: 359px;
    }
    .admin-interface #main.shifted > #nav-sidebar {
        left: 0px;
        margin-left: 0;
    }
    .admin-interface #main:not(.shifted) > .content {
        max-width: 100%;
    }
    .admin-interface.change-list:not(.popup) #main.shifted > #nav-sidebar + .content,
    .admin-interface.change-form:not(.popup) #main.shifted > #nav-sidebar + .content {
        max-width: calc(100% - 360px);
    }
}

/* fixed related widget and select2 */
/* begin fix issue #10 - Related widget broken in long tabular inline */
.admin-interface .related-widget-wrapper {
    white-space: nowrap;
}
/* end fix */

.admin-interface .related-widget-wrapper select + .related-widget-wrapper-link,
.admin-interface .related-widget-wrapper .select2-container + .related-widget-wrapper-link {
    margin-left: 12px !important;
}

@media (min-width: 768px) {
    .admin-interface.change-form select {
        min-width: 150px;
    }
}

@media (min-width: 1024px) {
    .admin-interface.change-form select {
        min-width: 200px;
    }
}

.admin-interface.change-form .inline-related.tabular select {
    min-width: auto !important;
}

/* fixed time widget header border radius */
.admin-interface .clockbox.module h2 {
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
}

/* fix searchbar overriden padding */
.admin-interface #changelist #changelist-search #searchbar {
    padding: 2px 5px 3px 5px;
}

@media (min-width: 1024px) {
    .admin-interface #changelist #changelist-search #searchbar,
    .admin-interface #changelist #changelist-search input[type="submit"],
    .admin-interface #changelist #changelist-search .quiet {
        margin-left: 8px;
    }
    .admin-interface #changelist #changelist-search label img {
        vertical-align: text-top;
        margin-right: 0px;
    }
}

@media (max-width: 1024px) {
    .admin-interface #changelist #toolbar {
        border-top: 1px solid #eee;
        border-bottom: 1px solid #eee;
    }
    /* fixed changelist search size when there are search results and .quiet is visible */
    .admin-interface #changelist-search label img {
        margin-top: 2px;
    }
    .admin-interface #changelist-search .quiet {
        margin: 0 0 0 10px;
        align-self: center;
        flex-basis: content;
    }
}

@media (max-width: 767px) {
    /* fixed responsive widgets */
    .admin-interface .aligned.collapsed .form-row {
        display: none;
    }

    .admin-interface .aligned .form-row > div {
        display: flex;
        max-width: 100vw;
        flex-direction: column;
        align-items: flex-start;
    }

    .admin-interface .aligned .form-row .help {
        margin-left: 0;
    }

    .admin-interface .aligned .form-row .checkbox-row label {
        margin: 10px 0 0 0;
        padding: 0;
    }

    .admin-interface .aligned .form-row input[type="file"],
    .admin-interface .aligned .form-row input[type="text"],
    .admin-interface .aligned .form-row input[type="email"] {
        width: 100%;
    }

    /* fix textarea horizontal scroll on Firefox */
    .admin-interface .aligned .form-row textarea {
        width: 100% !important;
        flex: 0 1 auto;
    }

    .admin-interface .aligned .form-row .datetime input[type="text"] {
        width: 50%;
    }

    .admin-interface .aligned .form-row span + .file-upload {
        margin-top: 10px;
    }

    .admin-interface .aligned .form-row .file-upload input[type="file"] {
        margin-top: 5px;
    }
}
