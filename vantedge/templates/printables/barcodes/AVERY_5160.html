{% load barcode_tags %}
<!DOCTYPE html>
<html lang="en">

<html>
<head>
<style>
@media print {
	@page {
		size: portrait;
		margin: 0.42in 0.22in 0.42in 0.22in ;
	}
}

.cell {
	/* center text, add small padding and margin, background color light green */
	text-align: center;
	margin: 5px;
	/* Make the cell 1 inch tall and 2-5/8 wide  */
	height: 1.06in;
	/* Make a 1/6 inch margin around each cell */
	margin-top: 0.2in;
}

.cellMiddle{
	margin-left: 0.3in;
	margin-right: 0.3in;
}

</style>
</head>
<body>
	{% for i in "x"|ljust:10 %}
	<div class="columns">
		<div class="column cell">
			<img src="data:image/png;base64,{{ barcodes|column_1:forloop.counter0}}" alt="barcode">
			<p style="text-align: center; font-size: 12px;">{{ barcodes_content|column_1:forloop.counter0}}</p>
		</div>
		<div class="column cell cellMiddle">
			<img src="data:image/png;base64,{{ barcodes|column_2:forloop.counter0}}" alt="barcode">
			<p style="text-align: center; font-size: 12px;">{{ barcodes_content|column_2:forloop.counter0}}</p>
		</div>
		<div class="column cell">
			<img src="data:image/png;base64,{{ barcodes|column_3:forloop.counter0}}" alt="barcode">
			<p style="text-align: center; font-size: 12px;">{{ barcodes_content|column_3:forloop.counter0}}</p>
		</div>
	</div>
	{% endfor %}
</body>
</html>