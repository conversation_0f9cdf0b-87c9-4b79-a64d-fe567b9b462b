{% load humanize %}
{% load units %}

<div class="text-bold">{{ forloop.counter }}. {{ car.car_mark }} {{ car.car_number }}</div>
  {% if car.car_type == "tank car" %}
  <div>Load Date: {{ car.load_date }} - Seal: {{ car.seal_numbers|join:", " }} - Product USG/L Conv. Fact: {{ car.gal_to_liter_conversion_factor }}</div>
  <div>

    <table>
      <tr>
        <th>Units</th>
        <th>Capacity</th>
        <th>Outage Volume</th>
        <th>Gross Volume</th>
        <th>Loaded Temp.</th>
        <th>Corr. Factor</th>
        <th>Net Volume</th>
        <th>Billing Density</th>
      </tr>
      <tr>
        <td>Metric</td>
        <td>{{ car.shell_capacity | quantity }} L&nbsp;&nbsp;</td>
        <td>{{ car.volume_outage | quantity }} L&nbsp;&nbsp;</td>
        <td>{{ car.volume_gross | quantity }} L&nbsp;&nbsp;</td>
        <td>{{ car.load_temperature | quantity }} &deg;C</td>
        <td>{{ car.temperature_correction_factor | quantity:2 }}</td>
        <td>{{ car.calculated_net_volume_at_15 | quantity }} L&nbsp;&nbsp;</td>
        <td>{{ car.density | quantity:2 }} kg/L&nbsp;&nbsp;</td>
      </tr>
      <tr>
        <td>Imperial</td>
        <td>{{ car.shell_capacity | convert:'L->gal' | quantity }} USG</td>
        <td>{{ car.volume_outage | convert:'L->gal' | quantity }} USG</td>
        <td>{% convert_custom_factor car.volume_gross car.liter_to_gal_conversion_factor %} USG</td>
        <td>{{ car.load_temperature | convert:'degC->degF' | quantity }} &deg;F</td>
        <td>{{ car.temperature_correction_factor|quantity:2 }}</td>
        <td>{{ car.volume_usg | quantity }} USG</td>
        <td>{{ car.density | convert:'kg/L->lbs/gal' | quantity:2 }} lb/USG</td>
      </tr>
      <tr>
        <td></td>
        <td>{{ car.shell_capacity | convert:'L->oil_barrel' | quantity:1 }} Barrel</td>
        <td>{{ car.volume_outage | convert:'L->oil_barrel' | quantity:1 }} Barrel</td>
        <td>{{ car.volume_gross | convert:'L->oil_barrel' | quantity:1 }} Barrel</td>
        <td></td>
        <td></td>
        <td>{{ car.volume | convert:'L->oil_barrel' | quantity:1 }} Barrel</td>
        <td></td>
      </tr>

      <tr style="border-top: 1px dotted;">
        <td colspan="4" style="text-align: left; margin-left: 4px;"> <span style="font-weight: bold;">Weight(KG) </span> {{car.weight | quantity }} </td>
        <td colspan="4" style="text-align: left; margin-left: 4px;"> <span style="font-weight: bold;">Weight(US Pound) </span> {{car.weight | convert:'kg->lbs' | quantity }} </td>
      </tr>
    </table>
    {% endif %}

    {% if car.car_type == "hopper car" %}
    <div>Load Date: {{ car.load_date }} - Car Type: {{ car.car_type}}</div>
    <div>

  <table>
    <tr>
      <th>Load Date</th>
      <th>Seal Number</th>
      <th>Weight (KG)</th>
      <th>Weight (US Pound)</th>
    </tr>
    <tr>
        <td>{{ car.load_date }}</td>
        <td>{{ car.seal_numbers|join:", "}}</td>
        <td>{{ car.weight | quantity:0 }}</td>
        <td>{{ car.weight | convert:'kg->lbs' | quantity:0 }}</td>
    </tr>

  </table>
  {% endif %}
</div>
