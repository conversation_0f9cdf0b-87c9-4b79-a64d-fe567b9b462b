{%  extends 'printables/printable-base.html' %}
{% load static tboss_tags humanize %}
{% load tz %}

{% block content  %}
<div class="container">
    <section class=" pb-2">
        <div class="grid-container is-vcentered">
            <div class="grid-100 has-text-left pl-6">
                <span class="title is-2"> Shipping Document </span>
            </div>
        </div>
    </section>

    <section class="">
        <div class="grid-container">
            <div class="grid-30">
                <div class="bold">Product:</div>
                <div>
                    {% if object.location.is_american %}
                        <div>
                        {{ object.product.usa_regulatory_message|linebreaks }}
                        {% if object.data.trailer1 and not object.data.trailer2 %}
                            1 Cargo Tank
                        {% elif object.data.trailer1 and object.data.trailer2%}
                            2 Cargo Tanks
                        {% endif %}
                        </div>
                    {% else %}
                        <div>{{ object.product.canada_regulatory_message|linebreaks }}</div>
                    {% endif %}
                </div>
            </div>
            <div class="grid-30">
                <table>
                    <tbody>
                        <tr>
                            <td class="has-text-weight-bold">Date Shipped:</td>
                            <td class="pl-5">{{ object.data.end_time|localtime|to_arrow|arrow_format:"MMM D YYYY" }}</td>
                        </tr>
                        <tr>
                            <td class="has-text-weight-bold">Time In:</td>
                            <td class="pl-5">{{ object.data.start_time|localtime|to_arrow|arrow_format:"h:mm A" }}</td>
                        </tr>
                        <tr>
                            <td class="has-text-weight-bold">Time Out:</td>
                            <td class="pl-5">{{ object.data.end_time|localtime|to_arrow|arrow_format:"h:mm A" }}</td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <div class="grid-30">
                <div class="has-text-weight-bold">Document Number:</div>
                <div>{{ object.code }}</div>
                {% if object.created != object.modified %}
                    <div class="has-text-weight-bold">Modified:</div>
                    <div>{{ object.modified|localtime|to_arrow|arrow_format:"MMM D YYYY h:mm A"  }}</div>
                {% endif %}
            </div>
        </div>

        <div class="grid-container ba">
            <div class="grid-50">
                <div class="has-text-weight-bold">Destination:</div>
                <div class="has-text-weight-bold">Point of Origin:</div>
                <div class="has-text-weight-bold">Consignor:</div>
                <div class="has-text-weight-bold">Consignor Address:</div>
                <div class="has-text-weight-bold">Consignor 24 Hour Number:</div>
            </div>
            <div class="grid-50">
                <div class="">{{ object.data.destination }}</div>
                <div class="">{{ object.data.source}}</div>
                <div class="">Harmattan Gas Processing L.P.</div>
                <div class="">P.O. Box 2280 Didsbury, AB T0M 0W0</div>
                <div class="">(403) 335-3321</div>
            </div>
        </div>

        <div class="grid-container border-ltr">
            <div class="grid-100 is-3">
                <div class="has-text-weight-bold">Account:</div>
                <div class="has-text-weight-bold">Consignee:</div>
            </div>
            <div class="grid-100">
                <div class="">{{ object.data.shipper }}</div>
                <div class="">{{ object.data.receiver }}</div>
            </div>
        </div>

        <div class="grid-container">
            <div class="grid-100">
                <div class="has-text-weight-bold">24 Hour Emergency Contacts:</div>
            </div>
        </div>
        <div class="grid-container">
            <div class="grid-20">
                <div class="has-text-weight-bold">(Canada) LPGERC:</div>
                <div class="has-text-weight-bold">(US) CHEMTREC:</div>
            </div>
            <div class="grid-30">
                <div class="">1-800-265-0212</div>
                <div class="">1-800-424-9300</div>
            </div>
            <div class="grid-20">
                <div class="has-text-weight-bold">ERAP:</div>
                <div class="has-text-weight-bold">CCN:</div>
            </div>
            <div class="grid-30 is-2">
                <div class=""># 2-0010-159</div>
                <div class="">223612</div>
            </div>
        </div>

        <div class="grid-container border-ltr">
            <table class="m-3">
                <tr>
                    <td class="has-text-weight-bold">Carrier:</td>
                    <td class="pl-5">{{ object.data.carrier }}</td>
                    <td class="pl-5 has-text-weight-bold">Carrier BOL:</td>
                    <td class="pl-5">{{ object.data.carrier_bol }}</td>
                </tr>
                <tr>
                    <td class="has-text-weight-bold">Unit Number:</td>
                    <td class="pl-5">{{ object.data.tractor }} </td>
                    <td class="pl-5 has-text-weight-bold">Trailer #1:</td>
                    <td class="pl-5"><div class="">{{ object.data.trailer1 }}</div> </td>
                    <td class="pl-5 has-text-weight-bold">Trailer #2:</td>
                    <td class="pl-5">{{ object.data.trailer2 }}</td>
                </tr>
                <tr>
                    <td class="has-text-weight-bold">Riser:</td>
                    <td class="pl-5">{{ object.data.device }}</td>
                </tr>
            </table>
        </div>

        <div class="grid-container border-lr">
            <div class="grid-100 is-3">
                <div class="has-text-weight-bold">Gross Volume:</div>
                <div class="has-text-weight-bold">Net Volume:</div>
                <div class="has-text-weight-bold">Avg. Pressure:</div>
                <div class="has-text-weight-bold">Avg. Temperature:</div>
                <div class="has-text-weight-bold">Density:</div>
                <div class="has-text-weight-bold">Odorant added:</div>
            </div>
            <div class="grid-100">
                <div class="">{{ object.volume.total_volume.gross_volume|stringformat:".0f" }} L</div>
                <div class="">{{ object.volume.total_volume.net_volume|stringformat:".0f" }} L <em>(Corrected to vapour pressure at 15°C)</em></div>
                <div class="">{{ object.volume.total_volume.average_pressure|stringformat:".1f" }} kPa</div>
                <div class="">{{ object.volume.total_volume.average_temperature|stringformat:".1f" }} °C</div>
                <div class="">{{ object.volume.total_volume.average_density|stringformat:".0f" }} kg/m&sup3;</div>
                <div class="">{{ object.volume.total_volume.additive|stringformat:".2f" }} kg</div>
            </div>
        </div>

        <div class="grid-container border-ltr">
            <div class="grid-100">
                <div>
                    I hereby declare that the contents of this consignment are fully and accurately described above by the proper shipping name, are properly
                    classified and packaged, have dangerous goods safety marks properly affixed or displayed on them, and are in all respects in proper
                    condition for transport according to the Transportaion of Dangerous Goods Regulations.
                </div>
                <div class="mt-5">
                    <span class="pr-6">Harmattan Gas Processing L.P. representative:</span>
                    <span class="signature">Chris Frankemolle</span>
                </div>
                <div>


                </div>
            </div>
        </div>

        <div class="grid-container border-ltr">
            <div class="grid-100 is-2">
                <div class="has-text-weight-bold">Driver Name:</div>
            </div>
            <div class="grid-100 is-3">
                <div class="">{{ object.data.driver }}</div>
            </div>
            <div class="grid-100 is-3">
                <div class="has-text-weight-bold" >Driver Signiture:</div>
            </div>
        </div>

        <div class="grid-container border-lr ">
            <div class="has-text-weight-bold pt-3 m-3">Special Instructions:</div>
        </div>

        <div class="grid-container border-lrb" style="padding-top: 60px">
            <div class="grid-100 is-2">
                <span class="underline"></span>
            </div>
            <div class="grid-100">
                <div class="">Residue Last Contained (Liquefied Petroleum Gas)</div>
            </div>
        </div>
    </section>
{%  endblock %}
