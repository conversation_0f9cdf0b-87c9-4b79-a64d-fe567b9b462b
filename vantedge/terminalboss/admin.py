from django.contrib import admin
from django.contrib.contenttypes.models import ContentType
from django.urls import reverse
from django.utils.safestring import mark_safe
from django_object_actions import DjangoObjectActions
from vantedge.util.mixins import AppPermissionsMixin
from .models import (
    SYNC_MODELS,
    TerminalBossCompany,
    SiteUser,
    Token,
    ConsoleCommand,
    UpdateTablePointer,
    TicketMain,
    TicketView,
    ObjectSyncTable,
    SiteSyncPreference,
    PersonTable,
    CompanyType,
    CompanyTable,
    CompanyTypeTable,
    PersonCompany,
    MeasurementUnit,
    ProductTable,
    AllocationTable,
    TransferLimit,
    AllocationTransferLimit,
    CompanyAllocation,
    AllocationTransactionType,
    CompanyHierarchy,
    CompanyHierarchyCompany,
    Country,
    Address,
    CompanyAddress,
    PersonAddress,
    ContactType,
    Contact,
    CompanyContact,
    LocationTable,
    CompanyLocation,
    ProductLocation,
    DeletedObject,
    History,
)


class SyncTableAdmin(admin.ModelAdmin):
    def save_model(self, request, obj, form, change):
        super().save_model(request, obj, form, change)

    def get_readonly_fields(self, request, obj):
        readonly_fields = list(super().get_readonly_fields(request, obj))
        readonly_fields.append("history")
        return readonly_fields

    def history(self, obj):
        content_type = ContentType.objects.get_for_model(obj)
        history = History.objects.filter(
            content_type=content_type, object_id=obj.id
        ).first()
        if history:
            url = reverse("admin:terminalboss_history_history", args=[history.id])
            return mark_safe('<a href="%s">Link to object history</a>' % (url,))
        else:
            return "Nothing found"


@admin.register(TerminalBossCompany)
class TerminalBossCompanyAdmin(AppPermissionsMixin):
    list_display = ("name", "company")


@admin.register(SiteUser)
class SiteUserAdmin(admin.ModelAdmin):
    list_display = list(field.name for field in SiteUser._meta.fields) + [
        "missing_connection",
        "remaining_pull_objects",
    ]

    def missing_connection(self, obj):
        if obj.is_sync_missing():
            return "( ! )"
        else:
            return " "

    def remaining_pull_objects(self, obj):
        return obj.remaining_pull_objects()


@admin.register(Token)
class TokenAdmin(admin.ModelAdmin):
    list_display = list(field.name for field in Token._meta.fields)


@admin.register(MeasurementUnit)
class MeasurementUnitAdmin(SyncTableAdmin):
    list_display = ("company", "unit_code", "unit_name", "enabled")


@admin.register(ProductTable)
class ProductTableAdmin(SyncTableAdmin):
    list_display = ("company", "product_code", "product_name", "enabled")


@admin.register(AllocationTable)
class AllocationTableAdmin(SyncTableAdmin):
    list_display = ("company", "allocation_code", "product_key", "effective_date")


@admin.register(TransferLimit)
class TransferLimitAdmin(SyncTableAdmin):
    list_display = (
        "company",
        "transfer_limit_start_date",
        "period_type",
        "period_start_date",
    )


@admin.register(AllocationTransferLimit)
class AllocationTransferLimitAdmin(SyncTableAdmin):
    list_diplay = ("company", "allocation", "transfer_limit")


@admin.register(PersonCompany)
class PersonCompanyTableAdmin(SyncTableAdmin):
    list_display = ("company", "site_user", "created")
    readonly_fields = ("created", "modified")
    list_filter = ("site_user",)


@admin.register(CompanyTypeTable)
class CompanyTypeTableAdmin(SyncTableAdmin):
    list_display = ("company", "site_user", "created")
    readonly_fields = ("created", "modified")
    list_filter = ("site_user",)


@admin.register(CompanyTable)
class CompanyTableAdmin(SyncTableAdmin):
    list_display = ("company", "name", "site_user", "company_code", "created")
    readonly_fields = ("created", "modified")
    list_filter = ("site_user",)


@admin.register(CompanyType)
class CompanyTypeAdmin(SyncTableAdmin):
    list_display = ("company", "site_user", "company_type_name", "created")
    readonly_fields = ("created", "modified")
    list_filter = ("site_user",)


@admin.register(TicketMain)
class TicketMainAdmin(SyncTableAdmin):
    list_display = ("company", "site_user", "ticket_key", "created")
    readonly_fields = ("created", "modified")
    list_filter = ("site_user",)


@admin.register(TicketView)
class TicketViewAdmin(SyncTableAdmin):
    list_display = ("company", "site_user", "ticket_key", "created")
    readonly_fields = ("created", "modified")
    list_filter = ("site_user",)


@admin.register(PersonTable)
class PersonTableAdmin(SyncTableAdmin):
    list_display = (
        "company",
        "site_user",
        "first_name",
        "last_name",
        "password",
        "created",
    )
    readonly_fields = ("created", "modified")
    search_fields = ["first_name", "last_name"]


@admin.register(UpdateTablePointer)
class UpdateTablePointerAdmin(admin.ModelAdmin):
    list_display = ("table_name", "site_user", "is_applied")


@admin.register(ObjectSyncTable)
class ObjectSyncTableAdmin(admin.ModelAdmin):
    list_display = ("content_type", "object_id")
    readonly_fields = ("created", "modified")
    list_filter = ("sites", "content_type")
    readonly_fields = ["link"]

    def link(self, obj):
        url = reverse(
            "admin:%s_%s_change" % (obj.content_type.app_label, obj.content_type.model),
            args=[obj.object_id],
        )
        return mark_safe("<a href='%s'>Link to %s</a>" % (url, obj.content_object))


@admin.register(SiteSyncPreference)
class SiteSyncPreferenceAdmin(admin.ModelAdmin):
    list_display = ("content_type", "sites")
    readonly_fields = ("created", "modified")

    def sites(self, obj):
        return [str(site) for site in obj.site_users.all()]

    def formfield_for_foreignkey(self, db_field, request, **kwargs):

        if db_field.name == "content_type":
            kwargs["queryset"] = ContentType.objects.filter(
                id__in=[
                    value.id
                    for value in ContentType.objects.get_for_models(
                        *SYNC_MODELS
                    ).values()
                ]
            )
        return super().formfield_for_foreignkey(db_field, request, **kwargs)


@admin.register(ConsoleCommand)
class ConsoleCommandAdmin(admin.ModelAdmin):
    list_display = ("site_user", "command", "is_read")
    readonly_fields = ("created", "modified")


@admin.register(CompanyAllocation)
class CompanyAllocationAdmin(SyncTableAdmin):
    list_display = ("company", "site_user", "company_key", "allocation_key", "created")
    readonly_fields = ("created", "modified")


@admin.register(AllocationTransactionType)
class AllocationTransactionTypeAdmin(SyncTableAdmin):
    list_display = ("company", "site_user", "allocation", "transaction_code", "created")
    readonly_fields = ("created", "modified")


@admin.register(CompanyHierarchy)
class CompanyHierarchyAdmin(SyncTableAdmin):
    list_display = ("id", "company", "site_user", "company_type", "parent_key")
    readonly_fields = ("created", "modified")


@admin.register(CompanyHierarchyCompany)
class CompanyHierarchyAdmin(SyncTableAdmin):
    list_display = ("id", "company", "site_user", "company_type", "company_hierarchy")
    readonly_fields = ("created", "modified")


@admin.register(Country)
class CountryAdmin(SyncTableAdmin):
    list_display = ("id", "company", "site_user", "country_code")
    readonly_fields = ("created", "modified")


@admin.register(Address)
class AddressAdmin(SyncTableAdmin):
    list_display = ("id", "company", "site_user", "address1")
    readonly_fields = ("created", "modified")


@admin.register(CompanyAddress)
class CompanyAddressAdmin(SyncTableAdmin):
    list_display = ("id", "company", "site_user", "company_key", "address")
    readonly_fields = ("created", "modified")


@admin.register(PersonAddress)
class PersonAddressAdmin(SyncTableAdmin):
    list_display = ("id", "company", "site_user", "person", "address")
    readonly_fields = ("created", "modified")


@admin.register(ContactType)
class ContactTypeAdmin(SyncTableAdmin):
    list_display = ("id", "company", "site_user", "contact_type_code", "description")
    readonly_fields = ("created", "modified")


@admin.register(Contact)
class ContactAdmin(SyncTableAdmin):
    list_display = ("id", "company", "site_user", "contact_type", "contact_person")
    readonly_fields = ("created", "modified")


@admin.register(CompanyContact)
class CompanyContactAdmin(SyncTableAdmin):
    list_display = ("id", "company", "site_user", "company_key", "contact")
    readonly_fields = ("created", "modified")


@admin.register(LocationTable)
class LocationTableAdmin(SyncTableAdmin):
    list_display = ("id", "company", "site_user", "name", "code")
    readonly_fields = ("created", "modified")


@admin.register(CompanyLocation)
class CompanyLocationAdmin(SyncTableAdmin):
    list_display = ("id", "company", "site_user", "company_key", "location")
    readonly_fields = ("created", "modified")


@admin.register(ProductLocation)
class ProductLocationAdmin(SyncTableAdmin):
    list_display = ("id", "company", "site_user", "product", "location")
    readonly_fields = ("created", "modified")


@admin.register(DeletedObject)
class DeletedObjectAdmin(admin.ModelAdmin):
    list_display = ("site_user", "content_type", "is_applied")
    readonly_fields = ("created", "modified")


@admin.register(History)
class HistoryAdmin(admin.ModelAdmin):
    list_display = ("site_user", "content_type", "object_id", "is_deleted")
    readonly_fields = ("created", "modified")
    list_filter = ("content_type",)
    search_fields = ["object_id"]
    object_history_template = "admin/terminalboss/mssql_object_history.html"

    def history_view(self, request, object_id, extra_context={}):
        obj = self.get_object(request, object_id)
        if obj:
            extra_context["history"] = obj.get_object_history()
        else:
            extra_context["history"] = {}
        return super().history_view(request, object_id, extra_context)
