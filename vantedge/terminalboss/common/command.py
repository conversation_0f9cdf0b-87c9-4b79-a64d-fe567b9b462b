import json
from ..common.func import MAP_MODEL_SERIALIZER
from .normalize import MssqlCompatibleEncoder
from datetime import datetime


class JsonCommand(object):
    def __init__(self, *args, **kwargs):
        # type is sql, shell, ...
        # sub_type = insert, update, mkdir,...

        self.site_user = kwargs.pop("site_user", None)

        self.command = {
            "site_user": self.site_user,
            "command": {"type": None, "sub_type": None},
            "read": False,
            "acknowledged": False,
        }
        self.set_type()

    def set_type(self):
        self.set_command({"type": getattr(self, "type", None)})

    def set_command(self, data):
        self.command["command"].update(data)


class ConsoleCommand(JsonCommand):
    def __init__(self, command, is_shell, *args, **kwargs):
        self.pk = kwargs.pop("pk", None)
        self.type = "console"
        super().__init__(*args, **kwargs)
        self.set_command({"command_text": command, "is_shell": is_shell, "pk": self.pk})


class SqlCommand(JsonCommand):
    def __init__(self, table, data, *args, **kwargs):
        self.data = data
        self.table = table
        self.type = "sql"
        super().__init__(*args, **kwargs)
        self.set_command({"table": self.table})

    def populate_insert(self):
        command = {"sub_type": "insert", "values": self.data}
        self.set_command(command)
        return self.command

    def populate_upsert(self, key_col, sub_type="upsert"):
        values = self.data
        self.set_command({"key_col": key_col})
        command = {"sub_type": sub_type, "values": values}
        self.set_command(command)
        return self.command

    def populate_delete(self, key_col):
        self.set_command({"key_col": key_col})
        command = {"sub_type": "delete", "values": dict()}
        self.set_command(command)
        return self.command


def create_whid_upsert(instances, created, site_user, sub_type="update"):
    statements = []
    for instance in instances:
        data = {}
        if created:
            data = dict(WHID=instance.id)
            key_col = dict(
                [key, getattr(instance, key)]
                for key in instance.Additionals.match_filter_keys
            )

        else:
            key_col = dict(WHID=instance.id)

        if instance.Additionals.on_update_whid:
            data.update(instance.Additionals.on_update_whid)

        sql_command = SqlCommand(
            table=instance.Additionals.tboss_table,
            data=data,
            site_user=site_user.auth_token.key,
        )

        for key, value in key_col.items():
            if isinstance(value, datetime):
                key_col[key] = value.astimezone(site_user.site_timezone)

        command = sql_command.populate_upsert(
            json.loads(json.dumps(key_col, cls=MssqlCompatibleEncoder)),
            sub_type=sub_type,
        )
        statements.append(command)
    return statements


def create_upsert(instances, site_user, sub_type="upsert"):
    statements = []
    for instance in instances:
        mssql_model_serializer_class = MAP_MODEL_SERIALIZER[instance._meta.model]
        data = mssql_model_serializer_class(instance, tz=site_user.site_timezone).data
        if instance.Additionals.on_update_whid:
            data.update(instance.Additionals.on_update_whid)
        sql_command = SqlCommand(
            table=instance.Additionals.tboss_table,
            data=data,
            site_user=site_user.auth_token.key,
        )
        key_col = dict(WHID=instance.id)
        command = sql_command.populate_upsert(key_col, sub_type)
        statements.append(command)
    return statements


def create_console_command(instances):
    statements = []
    for instance in instances:
        console_command = ConsoleCommand(
            instance.command,
            instance.is_shell,
            site_user=instance.site_user.auth_token.key,
            pk=instance.pk,
        )
        statements.append(console_command.command)
    return statements


def create_delete(instances, site_user):
    statements = []
    for instance in instances:
        target_model = instance.content_type.model_class()
        sql_command = SqlCommand(
            table=target_model.Additionals.tboss_table,
            data=dict(),
            site_user=site_user.auth_token.key,
        )
        key_col = dict(WHID=instance.object_id)
        command = sql_command.populate_delete(key_col)
        statements.append(command)
    return statements
