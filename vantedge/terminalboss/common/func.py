from .normalized_models import MODEL_NORMALIZERS
from vantedge.terminalboss.serializer import SERIALIZERS
from vantedge.terminalboss.models import SYNC_MODELS


MAP_TABLE_NAME_TO_MODEL = {model._meta.model_name: model for model in SYNC_MODELS}

# MAP_MODEL_NORMALIZER = {item.django_model: item for item in MODEL_NORMALIZERS}

MAP_TABLE_NAME_TO_NORMALIZER = {
    item.django_model._meta.model_name: item for item in MODEL_NORMALIZERS
}

MAP_MODEL_SERIALIZER = {item.Meta.model: item for item in SERIALIZERS}
MAP_TABLENAME_SERIALIZER = {
    item.Meta.model._meta.model_name: item for item in SERIALIZERS
}

