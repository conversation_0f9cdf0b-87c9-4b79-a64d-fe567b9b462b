from re import finditer
import json
from dateutil.parser import parse
from datetime import datetime

from django.db.models.fields import DateT<PERSON><PERSON><PERSON>


def get_normalized_name(key):
    # key comming from mssql
    matches = finditer(".+?(?:(?<=[a-z])(?=[A-Z])|(?<=[A-Z])(?=[A-Z][a-z])|$)", key)
    return "_".join([m.group(0).lower() for m in matches])


def reverse_normalized_name(key):
    # keys comming from django model
    return "".join([item.capitalize() for item in key.split("_")])


class MssqlCompatibleEncoder(json.JSONEncoder):
    def default(self, o):
        if isinstance(o, datetime):
            return o.replace(tzinfo=None).strftime(
                f"%Y-%m-%d %H:%M:%S:{o.microsecond // 1000}"
            )


def normalize_for_site(value, **kwargs):
    if isinstance(value, datetime):
        tz = kwargs.pop("tz")
        if tz:
            value = value.astimezone(tz)

    return value


class RowDictDataNormalizer(object):
    def __init__(
        self,
        main_fields,
        date_time_fields,
        map_fields,
        field_name_additional_data,
        ignore_fields,
        tz,
        **kwargs,
    ):
        self.main_fields = main_fields
        self.date_time_fields = date_time_fields
        self.map_fields = map_fields
        self.field_name_additional_data = field_name_additional_data
        self.ignore_fields = ignore_fields
        self.tz = tz
        self.data = dict()
        self.data[self.field_name_additional_data] = dict()

        for key, value in kwargs.items():
            if key in self.ignore_fields:
                continue

            normalized_name = self.get_normalized_name(key)

            # if value return from func then it will not fall into other calculators
            if hasattr(self, normalized_name):
                value = getattr(self, normalized_name)(value)

            elif key in self.date_time_fields:
                value = self.to_datetime(value)

            if key in self.main_fields:
                self.data[key] = value

            else:
                self.data[self.field_name_additional_data][key] = value

    def to_datetime(self, value):
        if value is None:
            return value

        return parse(value).replace(tzinfo=self.tz)

    def get_normalized_name(self, key):
        if self.map_fields.get(key):
            return self.map_fields[key]
        else:
            return get_normalized_name(key)

    def normalize_field_names(self):
        # create a dict of {field_name: value ..., additional_data:{field_name:value ...}}
        result = dict()
        result[self.field_name_additional_data] = dict()

        for key, value in self.data.items():
            if key == self.field_name_additional_data:
                for key_additional, value_additional in self.data[key].items():
                    result[self.field_name_additional_data][
                        self.get_normalized_name(key_additional)
                    ] = value_additional
            else:
                result[self.get_normalized_name(key)] = value

        return result


class ModelFieldNormalizer(object):
    django_model = lambda x: x
    map_fields = {"WHID": "id", "id": "tboss_id"}
    reserved_fields = ["pk", "site_user_id", "company_id", "created", "modified"]
    field_name_additional_data = "additional_data"
    ignore_fields = []
    mssql_to_model_normalizer_class = RowDictDataNormalizer

    def __init__(self, model_qs=None, mssql_data=None, tz=None):
        self.main_fields = []
        self.date_time_fields = []
        self.model_qs = model_qs
        self.mssql_data = mssql_data
        self.reverse_name_mapping = {v: k for k, v in self.map_fields.items()}
        self.tz = tz
        self.populate_data_structure_from_model()

    def get_normalized_name(self, key):
        return self.mssql_to_model_normalizer_class(
            self.main_fields,
            self.date_time_fields,
            self.map_fields,
            self.field_name_additional_data,
            self.ignore_fields,
            self.tz,
        ).get_normalized_name(key)

    def get_reserved_fields(self):
        return self.reserved_fields + [self.field_name_additional_data]

    def get_reverse_name(self, field):
        if field in self.reverse_name_mapping:
            return self.reverse_name_mapping[field]

        return reverse_normalized_name(field)

    def populate_data_structure_from_model(self):
        model_fields = list(self.django_model._meta.fields)

        for field in model_fields:
            if field.attname in self.get_reserved_fields():
                continue

            elif isinstance(field, DateTimeField):
                self.date_time_fields.append(self.get_reverse_name(field.attname))

            self.main_fields.append(self.get_reverse_name(field.attname))

    def populate_model_json(self):
        result = []
        for row in self.mssql_data:
            normalizer = self.mssql_to_model_normalizer_class(
                self.main_fields,
                self.date_time_fields,
                self.map_fields,
                self.field_name_additional_data,
                self.ignore_fields,
                self.tz,
                **row,
            )
            normalized_row = normalizer.normalize_field_names()
            result.append(normalized_row)
        return result

    def populate_model_instances(self, site_user):
        result = []
        json_data = self.populate_model_json()
        for row in json_data:
            result.append(
                self.django_model(**row, site_user=site_user, company=site_user.company)
            )

        return result

    def write(self, site_user):
        self.django_model.objects.bulk_create(self.populate_model_instances(site_user))

    def populate_mssql_json(self):
        result = []
        for row in self.model_qs:
            data = {}
            for field in self.django_model._meta.fields:
                attname = field.attname
                if type(row) is dict:
                    value = row.get(attname)
                else:
                    value = getattr(row, attname)

                if attname in self.reserved_fields:
                    continue

                if attname == self.field_name_additional_data:
                    json_data = value
                    for key in json_data:
                        data[self.get_reverse_name(key)] = normalize_for_site(
                            json_data[key], tz=self.tz
                        )
                else:
                    data[self.get_reverse_name(attname)] = normalize_for_site(
                        value, tz=self.tz
                    )

            result.append(data)

        return json.loads(json.dumps(result, cls=MssqlCompatibleEncoder))
