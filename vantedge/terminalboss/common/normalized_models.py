from .normalize import ModelFieldNormalizer

from vantedge.terminalboss.models.tables import (
    TicketMain,
    TicketView,
    PersonTable,
    CompanyType,
    CompanyTable,
    CompanyTypeTable,
    PersonCompany,
    MeasurementUnit,
    ProductTable,
    AllocationTable,
    TransferLimit,
    AllocationTransferLimit,
    CompanyAllocation,
    AllocationTransactionType,
    CompanyHierarchy,
    CompanyHierarchyCompany,
    Country,
    Address,
    CompanyAddress,
    PersonAddress,
    ContactType,
    Contact,
    CompanyContact,
    LocationTable,
    CompanyLocation,
    ProductLocation,
)


class ProductLocationModelNormalizer(ModelFieldNormalizer):
    django_model = ProductLocation
    map_fields = {"WHID": "id", "Product": "product_id", "Location": "location_id"}


class CompanyLocationModelNormalizer(ModelFieldNormalizer):
    django_model = CompanyLocation
    map_fields = {
        "WHID": "id",
        "CompanyKey": "company_key_id",
        "Location": "location_id",
    }


class LocationModelNormalizer(ModelFieldNormalizer):
    django_model = LocationTable
    map_fields = {
        "WHID": "id",
        "LSD": "lsd",
        "DLSWellIdentifier": "dls_well_identifier",
        "DLSEventIdentifier": "dls_event_identifier",
        "NTSWellIdentifier": "nts_well_identifier",
        "NTSEventIdentifier": "nts_event_identifier",
    }


class CompanyContactModelNormalizer(ModelFieldNormalizer):
    django_model = CompanyContact
    map_fields = {
        "WHID": "id",
        "CompanyKey": "company_key_id",
        "ContactKey": "contact_id",
    }


class ContactModelNormalizer(ModelFieldNormalizer):
    django_model = Contact
    map_fields = {"WHID": "id", "ContactTypeKey": "contact_type_id"}


class ContactTypeModelNormalizer(ModelFieldNormalizer):
    django_model = ContactType


class PersonAddressModelNormalizer(ModelFieldNormalizer):
    django_model = PersonAddress
    map_fields = {"WHID": "id", "Person": "person_id", "Address": "address_id"}


class CompanyAddressModelNormalizer(ModelFieldNormalizer):
    django_model = CompanyAddress
    map_fields = {"WHID": "id", "CompanyKey": "company_key_id", "Address": "address_id"}


class AddressModelNormalizer(ModelFieldNormalizer):
    django_model = Address
    map_fields = {
        "WHID": "id",
        "Address1": "address1",
        "Address2": "address2",
        "Country": "country_id",
    }


class CountryModelNormalizer(ModelFieldNormalizer):
    django_model = Country


class CompanyHierarchyCompanyModelNormalizer(ModelFieldNormalizer):
    django_model = CompanyHierarchyCompany
    map_fields = {
        "WHID": "id",
        "CompanyType": "company_type_id",
        "CompanyHierarchy": "company_hierarchy_id",
    }


class CompanyHierarchyModelNormalizer(ModelFieldNormalizer):
    django_model = CompanyHierarchy
    map_fields = {
        "WHID": "id",
        "CompanyType": "company_type_id",
        "ParentKey": "parent_key_id",
    }


class AllocationTransactionTypeModelNormalizer(ModelFieldNormalizer):
    django_model = AllocationTransactionType
    map_fields = {"WHID": "id", "Allocation": "allocation_id"}


class CompanyAllocationModelNormalizer(ModelFieldNormalizer):
    django_model = CompanyAllocation
    map_fields = {
        "WHID": "id",
        "CompanyKey": "company_key_id",
        "CompanyTypeKey": "company_type_key_id",
        "AllocationKey": "allocation_key_id",
    }


class AllocationTransferLimitModelNormalizer(ModelFieldNormalizer):
    django_model = AllocationTransferLimit
    map_fields = {
        "WHID": "id",
        "Allocation": "allocation_id",
        "TransferLimit": "transfer_limit_id",
    }


class TransferLimitModelNormalizer(ModelFieldNormalizer):
    django_model = TransferLimit


class AllocationTableModelNormalizer(ModelFieldNormalizer):
    django_model = AllocationTable
    map_fields = {"WHID": "id", "ProductKey": "product_key_id"}


class ProductTableModelNormalizer(ModelFieldNormalizer):
    django_model = ProductTable
    map_fields = {
        "WHID": "id",
        "ProductUnit": "product_unit_id",
        "TemperatureConversionFactor15C60F": "temperature_conversion_factor",
    }


class MeasurementUnitModelNormalizer(ModelFieldNormalizer):
    django_model = MeasurementUnit


class PersonCompanyModelNormalizer(ModelFieldNormalizer):
    django_model = PersonCompany
    map_fields = {
        "WHID": "id",
        "PersonKey": "person_key_id",
        "CompanyKey": "company_key_id",
    }


class CompanyTypeTableModelNormalizer(ModelFieldNormalizer):
    django_model = CompanyTypeTable
    map_fields = {
        "WHID": "id",
        "CompanyTable": "company_table_id",
        "CompanyType": "company_type_id",
    }


class CompanyTableModelNormalizer(ModelFieldNormalizer):
    django_model = CompanyTable
    map_fields = {"WHID": "id", "IRSNumber": "irs_number", "BNNumber": "bn_number"}


class CompanyTypeModelNormalizer(ModelFieldNormalizer):
    django_model = CompanyType


class TicketModelNormalizer(ModelFieldNormalizer):
    django_model = TicketMain
    map_fields = {"WHID": "id", "TicketID": "ticket_id", "id": "tboss_id"}


class TicketViewModelNormalizer(ModelFieldNormalizer):
    django_model = TicketView
    map_fields = {
        "WHID": "id",
        "Trailer1Name": "trailer1_name",
        "Trailer2Name": "trailer2_name",
        "BOL": "bol",
        "CTPL": "ctpl",
        "CTL": "ctl",
        "CPL": "cpl",
        "DriverID": "driver_id",
        "PercentH2O": "percent_h2o",
        "TerminalID": "terminal_id",
        "TicketID": "ticket_id",
    }


class PersonTableModelNormalizer(ModelFieldNormalizer):
    django_model = PersonTable
    map_fields = {
        "WHID": "id",
        "UserName": "username",
        "LockoutDate": "lockout_date",
        "LockoutReason": "lockout_reason",
    }
    ignore_fields = ["LastModifiedTime", "UserName"]  # pointer should be excluded


MODEL_NORMALIZERS = ModelFieldNormalizer.__subclasses__()
