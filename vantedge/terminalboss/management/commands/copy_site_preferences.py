from django.core.management.base import BaseCommand
from vantedge.terminalboss.models import SiteUser


class Command(BaseCommand):
    help = "This will add default preferences objects for given site"

    def add_arguments(self, parser):
        parser.add_argument("--copy-to-sitename", required=True, type=str)
        parser.add_argument("--from-sitename", required=True, type=str)

    def handle(self, *args, **options):
        from_sitename = SiteUser.objects.get(username=options["from_sitename"])
        copy_to_sitename = SiteUser.objects.get(username=options["copy_to_sitename"])
        
        print(f"started to create {from_sitename.sitesyncpreference_set.count()} items")
        for preference in from_sitename.sitesyncpreference_set.all():
            preference.site_users.add(copy_to_sitename)
        print("finished!")
