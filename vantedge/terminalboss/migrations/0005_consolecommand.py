# Generated by Django 3.2.5 on 2021-12-21 16:14

from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import model_utils.fields


class Migration(migrations.Migration):

    dependencies = [
        ('terminalboss', '0004_rename_trailer_fields'),
    ]

    operations = [
        migrations.CreateModel(
            name='ConsoleCommand',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', model_utils.fields.AutoCreatedField(default=django.utils.timezone.now, editable=False, verbose_name='created')),
                ('modified', model_utils.fields.AutoLastModifiedField(default=django.utils.timezone.now, editable=False, verbose_name='modified')),
                ('command', models.TextField(help_text="ex: sqlcmd -S {db_address} -U {db_user} -P {db_password} -d {db} -Q'select * form TBL'")),
                ('is_shell', models.BooleanField(default=False)),
                ('is_read', models.BooleanField(default=False)),
                ('is_enable', models.BooleanField(default=True)),
                ('site_user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='terminalboss.siteuser')),
            ],
            options={
                'abstract': False,
            },
        ),
    ]
