# Generated by Django 3.2.5 on 2022-01-11 20:03

import django.core.serializers.json
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import model_utils.fields
import vantedge.autocomplete.models


class Migration(migrations.Migration):

    dependencies = [
        ('terminalboss', '0008_deletedobject_is_applied'),
    ]

    operations = [
        migrations.AlterField(
            model_name='updatetablepointer',
            name='table_name',
            field=models.CharField(choices=[('ticketmain', 'ticket main'), ('ticketview', 'ticket view'), ('persontable', 'person table'), ('companytype', 'company type'), ('companytable', 'company table'), ('companytypetable', 'company type table'), ('personcompany', 'person company'), ('measurementunit', 'measurement unit'), ('producttable', 'product table'), ('allocationtable', 'allocation table'), ('transferlimit', 'transfer limit'), ('allocationtransferlimit', 'allocation transfer limit'), ('companyallocation', 'company allocation'), ('allocationtransactiontype', 'allocation transaction type')], max_length=50),
        ),
        migrations.CreateModel(
            name='AllocationTransactionType',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', model_utils.fields.AutoCreatedField(default=django.utils.timezone.now, editable=False, verbose_name='created')),
                ('modified', model_utils.fields.AutoLastModifiedField(default=django.utils.timezone.now, editable=False, verbose_name='modified')),
                ('additional_data', models.JSONField(blank=True, encoder=django.core.serializers.json.DjangoJSONEncoder)),
                ('transaction_code', models.SmallIntegerField()),
                ('allocation', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='terminalboss.allocationtable')),
                ('company', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='terminalboss.terminalbosscompany')),
                ('site_user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='terminalboss.siteuser')),
            ],
            options={
                'abstract': False,
            },
            bases=(models.Model, vantedge.autocomplete.models.AutocompleteMixin),
        ),
    ]
