# Generated by Django 3.2.5 on 2022-01-12 19:09

import django.core.serializers.json
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import model_utils.fields
import vantedge.autocomplete.models


class Migration(migrations.Migration):

    dependencies = [
        ('terminalboss', '0009_auto_20220111_1303'),
    ]

    operations = [
        migrations.CreateModel(
            name='CompanyHierarchy',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', model_utils.fields.AutoCreatedField(default=django.utils.timezone.now, editable=False, verbose_name='created')),
                ('modified', model_utils.fields.AutoLastModifiedField(default=django.utils.timezone.now, editable=False, verbose_name='modified')),
                ('additional_data', models.JSONField(blank=True, encoder=django.core.serializers.json.DjangoJSONEncoder)),
                ('company', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='terminalboss.terminalbosscompany')),
                ('company_type', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='terminalboss.companytypetable')),
                ('parent_key', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to='terminalboss.companyhierarchy')),
                ('site_user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='terminalboss.siteuser')),
            ],
            options={
                'abstract': False,
            },
            bases=(models.Model, vantedge.autocomplete.models.AutocompleteMixin),
        ),
    ]
