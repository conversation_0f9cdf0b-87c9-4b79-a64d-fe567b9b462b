# Generated by Django 3.2.7 on 2022-01-13 22:43

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("terminalboss", "0011_auto_20220113_0944"),
    ]

    operations = [
        migrations.AlterField(
            model_name="allocationtable",
            name="product_direction",
            field=models.IntegerField(
                choices=[[None, "Allocation"], [1, "Nomination"]], null=True
            ),
        ),
        migrations.AlterField(
            model_name="allocationtransactiontype",
            name="transaction_code",
            field=models.SmallIntegerField(
                choices=[[3, "Accuload Loading"], [4, "Accuload Unloading"]]
            ),
        ),
        migrations.AlterField(
            model_name="companytable",
            name="bn_number",
            field=models.Char<PERSON>ield(blank=True, max_length=50, null=True),
        ),
        migrations.AlterField(
            model_name="companytable",
            name="company_code",
            field=models.CharField(blank=True, max_length=20, null=True),
        ),
        migrations.<PERSON><PERSON><PERSON><PERSON>(
            model_name="companytable",
            name="customs_brokerage_account_number",
            field=models.Char<PERSON><PERSON>(blank=True, max_length=50, null=True),
        ),
        migrations.<PERSON><PERSON><PERSON>ield(
            model_name="companytable",
            name="irs_number",
            field=models.CharField(blank=True, max_length=50, null=True),
        ),
        migrations.AlterField(
            model_name="companytable",
            name="lockout_reason",
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name="companytable",
            name="name",
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AlterField(
            model_name="companytype",
            name="company_type_code",
            field=models.CharField(blank=True, max_length=20, null=True),
        ),
        migrations.AlterField(
            model_name="companytype",
            name="description",
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AlterField(
            model_name="producttable",
            name="chemical_abstracts_service_number",
            field=models.CharField(blank=True, max_length=50, null=True),
        ),
        migrations.AlterField(
            model_name="producttable",
            name="commercial_description",
            field=models.TextField(blank=True, max_length=500, null=True),
        ),
        migrations.AlterField(
            model_name="producttable",
            name="description",
            field=models.TextField(blank=True, max_length=500, null=True),
        ),
        migrations.AlterField(
            model_name="producttable",
            name="harmonized_system_tariff_number",
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name="producttable",
            name="harmonized_tariff_unit_of_measure",
            field=models.CharField(blank=True, max_length=50, null=True),
        ),
        migrations.AlterField(
            model_name="producttable",
            name="measurement_unit",
            field=models.CharField(blank=True, max_length=50, null=True),
        ),
        migrations.AlterField(
            model_name="producttable",
            name="part_number",
            field=models.CharField(blank=True, max_length=50, null=True),
        ),
        migrations.AlterField(
            model_name="producttable",
            name="product_name",
            field=models.CharField(blank=True, max_length=50, null=True),
        ),
        migrations.AlterField(
            model_name="producttable",
            name="product_type",
            field=models.CharField(blank=True, max_length=20, null=True),
        ),
        migrations.AlterField(
            model_name="transferlimit",
            name="period_type",
            field=models.IntegerField(
                choices=[
                    [1, "Yearly"],
                    [2, "Monthly"],
                    [3, "Daily"],
                    [4, "Periodically"],
                ]
            ),
        ),
    ]
