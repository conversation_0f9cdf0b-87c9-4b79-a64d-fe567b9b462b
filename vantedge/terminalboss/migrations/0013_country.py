# Generated by Django 3.2.5 on 2022-01-17 18:20

import django.core.serializers.json
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import model_utils.fields
import vantedge.autocomplete.models


class Migration(migrations.Migration):

    dependencies = [
        ('terminalboss', '0012_auto_20220113_1543'),
    ]

    operations = [
        migrations.CreateModel(
            name='Country',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', model_utils.fields.AutoCreatedField(default=django.utils.timezone.now, editable=False, verbose_name='created')),
                ('modified', model_utils.fields.AutoLastModifiedField(default=django.utils.timezone.now, editable=False, verbose_name='modified')),
                ('additional_data', models.JSONField(blank=True, encoder=django.core.serializers.json.DjangoJSONEncoder)),
                ('country_code', models.CharField(max_length=20)),
                ('country_name', models.Char<PERSON>ield(max_length=20, null=True)),
                ('short_name', models.CharField(max_length=20, null=True)),
                ('company', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='terminalboss.terminalbosscompany')),
                ('site_user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='terminalboss.siteuser')),
            ],
            options={
                'verbose_name_plural': 'Countries',
            },
            bases=(models.Model, vantedge.autocomplete.models.AutocompleteMixin),
        ),
    ]
