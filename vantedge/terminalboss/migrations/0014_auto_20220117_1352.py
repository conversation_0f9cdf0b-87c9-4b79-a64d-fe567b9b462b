# Generated by Django 3.2.5 on 2022-01-17 20:52

import django.core.serializers.json
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import model_utils.fields
import vantedge.autocomplete.models


class Migration(migrations.Migration):

    dependencies = [
        ('terminalboss', '0013_country'),
    ]

    operations = [
        migrations.AlterField(
            model_name='updatetablepointer',
            name='table_name',
            field=models.CharField(choices=[('ticketmain', 'ticket main'), ('ticketview', 'ticket view'), ('persontable', 'person table'), ('companytype', 'company type'), ('companytable', 'company table'), ('companytypetable', 'company type table'), ('personcompany', 'person company'), ('measurementunit', 'measurement unit'), ('producttable', 'product table'), ('allocationtable', 'allocation table'), ('transferlimit', 'transfer limit'), ('allocationtransferlimit', 'allocation transfer limit'), ('companyallocation', 'company allocation'), ('allocationtransactiontype', 'allocation transaction type'), ('companyhierarchy', 'company hierarchy'), ('companyhierarchycompany', 'company hierarchy company'), ('country', 'country'), ('address', 'address')], max_length=50),
        ),
        migrations.CreateModel(
            name='Address',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', model_utils.fields.AutoCreatedField(default=django.utils.timezone.now, editable=False, verbose_name='created')),
                ('modified', model_utils.fields.AutoLastModifiedField(default=django.utils.timezone.now, editable=False, verbose_name='modified')),
                ('additional_data', models.JSONField(blank=True, encoder=django.core.serializers.json.DjangoJSONEncoder)),
                ('address1', models.CharField(max_length=50, null=True)),
                ('address2', models.CharField(max_length=50, null=True)),
                ('city', models.CharField(max_length=30, null=True)),
                ('province', models.CharField(max_length=20, null=True)),
                ('postal_code', models.CharField(max_length=10, null=True)),
                ('contact_name', models.CharField(max_length=30, null=True)),
                ('phone_number', models.CharField(max_length=20, null=True)),
                ('name', models.CharField(max_length=100, null=True)),
                ('company', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='terminalboss.terminalbosscompany')),
                ('country', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='terminalboss.country')),
                ('site_user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='terminalboss.siteuser')),
            ],
            options={
                'verbose_name_plural': 'Addresses',
            },
            bases=(models.Model, vantedge.autocomplete.models.AutocompleteMixin),
        ),
    ]
