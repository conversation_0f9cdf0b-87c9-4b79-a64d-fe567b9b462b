# Generated by Django 3.2.5 on 2022-01-26 18:17

import django.core.serializers.json
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import model_utils.fields
import vantedge.autocomplete.models


class Migration(migrations.Migration):

    dependencies = [
        ('contenttypes', '0002_remove_content_type_name'),
        ('terminalboss', '0019_auto_20220121_1234'),
    ]

    operations = [
        migrations.AlterField(
            model_name='updatetablepointer',
            name='table_name',
            field=models.CharField(choices=[('ticketmain', 'ticket main'), ('ticketview', 'ticket view'), ('persontable', 'person table'), ('companytype', 'company type'), ('companytable', 'company table'), ('companytypetable', 'company type table'), ('personcompany', 'person company'), ('measurementunit', 'measurement unit'), ('producttable', 'product table'), ('allocationtable', 'allocation table'), ('transferlimit', 'transfer limit'), ('allocationtransferlimit', 'allocation transfer limit'), ('companyallocation', 'company allocation'), ('allocationtransactiontype', 'allocation transaction type'), ('companyhierarchy', 'company hierarchy'), ('companyhierarchycompany', 'company hierarchy company'), ('country', 'country'), ('address', 'address'), ('companyaddress', 'company address'), ('personaddress', 'person address'), ('contacttype', 'contact type'), ('contact', 'contact'), ('companycontact', 'company contact')], max_length=50),
        ),
        migrations.CreateModel(
            name='ContactType',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', model_utils.fields.AutoCreatedField(default=django.utils.timezone.now, editable=False, verbose_name='created')),
                ('modified', model_utils.fields.AutoLastModifiedField(default=django.utils.timezone.now, editable=False, verbose_name='modified')),
                ('additional_data', models.JSONField(blank=True, encoder=django.core.serializers.json.DjangoJSONEncoder)),
                ('contact_type_code', models.CharField(max_length=20)),
                ('description', models.CharField(max_length=100)),
                ('enabled', models.BooleanField(default=True)),
                ('company', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='terminalboss.terminalbosscompany')),
                ('site_user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='terminalboss.siteuser')),
            ],
            options={
                'abstract': False,
            },
            bases=(models.Model, vantedge.autocomplete.models.AutocompleteMixin),
        ),
        migrations.CreateModel(
            name='Contact',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', model_utils.fields.AutoCreatedField(default=django.utils.timezone.now, editable=False, verbose_name='created')),
                ('modified', model_utils.fields.AutoLastModifiedField(default=django.utils.timezone.now, editable=False, verbose_name='modified')),
                ('additional_data', models.JSONField(blank=True, encoder=django.core.serializers.json.DjangoJSONEncoder)),
                ('contact_person', models.CharField(max_length=50)),
                ('contact_information', models.CharField(max_length=100)),
                ('company', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='terminalboss.terminalbosscompany')),
                ('contact_type', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='contenttypes.contenttype')),
                ('site_user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='terminalboss.siteuser')),
            ],
            options={
                'abstract': False,
            },
            bases=(models.Model, vantedge.autocomplete.models.AutocompleteMixin),
        ),
        migrations.CreateModel(
            name='CompanyContact',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', model_utils.fields.AutoCreatedField(default=django.utils.timezone.now, editable=False, verbose_name='created')),
                ('modified', model_utils.fields.AutoLastModifiedField(default=django.utils.timezone.now, editable=False, verbose_name='modified')),
                ('additional_data', models.JSONField(blank=True, encoder=django.core.serializers.json.DjangoJSONEncoder)),
                ('company', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='terminalboss.terminalbosscompany')),
                ('company_key', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='terminalboss.companytable')),
                ('contact', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='terminalboss.contact')),
                ('site_user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='terminalboss.siteuser')),
            ],
            options={
                'abstract': False,
            },
            bases=(models.Model, vantedge.autocomplete.models.AutocompleteMixin),
        ),
    ]
