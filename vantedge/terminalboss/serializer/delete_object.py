from rest_framework import serializers

from django.contrib.contenttypes.models import ContentType

from ..models import DeletedObject


class DeletedObjectSerializer(serializers.ModelSerializer):
    class Meta:
        model = DeletedObject
        fields = ["content_type", "object_id"]

    def to_internal_value(self, data):
        from ..common.func import MAP_TABLE_NAME_TO_MODEL

        model_name = data.get("model_name")
        object_id = data.get("obj_whid")

        model = MAP_TABLE_NAME_TO_MODEL.get(model_name)
        content_type = ContentType.objects.get_for_model(model)

        to_internal = dict(content_type=content_type.id, object_id=object_id)
        return super().to_internal_value(to_internal)

    def create(self, validated_data):
        instance = DeletedObject(**validated_data)
        if not instance.content_object:
            return

        if instance.company != instance.content_object.company:
            raise serializers.ValidationError("deleted object owner mismatch!!!")

        instance.from_site = True
        instance.save()
        instance.content_object.delete(from_site=True)
        return instance
