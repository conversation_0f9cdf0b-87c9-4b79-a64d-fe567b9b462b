import logging
from rest_framework import serializers

from ..models import (
    SiteSyncPreference,
    UpdateTablePointer,
    TicketMain,
    TicketView,
    PersonTable,
    CompanyType,
    CompanyTable,
    CompanyTypeTable,
    PersonCompany,
    MeasurementUnit,
    ProductTable,
    AllocationTable,
    TransferLimit,
    AllocationTransferLimit,
    CompanyAllocation,
    AllocationTransactionType,
    CompanyHierarchy,
    CompanyHierarchyCompany,
    Country,
    Address,
    CompanyAddress,
    PersonAddress,
    ContactType,
    Contact,
    CompanyContact,
    LocationTable,
    CompanyLocation,
    ProductLocation,
)

from ..common.normalized_models import (
    TicketModelNormalizer,
    TicketViewModelNormalizer,
    PersonTableModelNormalizer,
    CompanyTypeModelNormalizer,
    CompanyTableModelNormalizer,
    CompanyTypeTableModelNormalizer,
    PersonCompanyModelNormalizer,
    MeasurementUnitModelNormalizer,
    ProductTableModelNormalizer,
    AllocationTableModelNormalizer,
    TransferLimitModelNormalizer,
    AllocationTransferLimitModelNormalizer,
    CompanyAllocationModelNormalizer,
    AllocationTransactionTypeModelNormalizer,
    CompanyHierarchyModelNormalizer,
    CompanyHierarchyCompanyModelNormalizer,
    CountryModelNormalizer,
    AddressModelNormalizer,
    CompanyAddressModelNormalizer,
    PersonAddressModelNormalizer,
    ContactTypeModelNormalizer,
    ContactModelNormalizer,
    CompanyContactModelNormalizer,
    LocationModelNormalizer,
    CompanyLocationModelNormalizer,
    ProductLocationModelNormalizer,
)

logger = logging.getLogger(__name__)


class UpdateTablePointerSerializer(serializers.ModelSerializer):
    class Meta:
        model = UpdateTablePointer
        fields = ["table_name", "pointer_value"]


class CustomListSerializer(serializers.ListSerializer):
    def get_update_fields(self):
        model_normalizer = self.child.Meta.mssql_model_normalizer()
        mssql_fields = model_normalizer.main_fields
        fields = [model_normalizer.get_normalized_name(field) for field in mssql_fields]
        fields.append(model_normalizer.field_name_additional_data)
        if "id" in fields:
            fields.remove("id")

        return fields

    def create(self, all_validated_data):
        ModelClass = self.child.Meta.model

        all_instances = []

        self.child.with_whid = []
        self.child.without_whid = []

        self.child.created_instances = []
        self.child.updated_instances = []

        for validated_data in all_validated_data:
            if ModelClass.Additionals.update_whid and validated_data.get("id"):
                instance = ModelClass.objects.get(id=validated_data.get("id"))

                changed = False
                for key, value in validated_data.items():
                    if ModelClass.Additionals.sync_between_sites and key == "site_user":
                        continue
                    if getattr(instance, key) != value:
                        changed = True
                        setattr(instance, key, value)
                if changed:
                    self.child.updated_instances.append(instance)

                self.child.with_whid.append(instance)

            else:
                validated_data.pop("id", None)  # to prevent create new item
                already_inserted = (
                    ModelClass.Additionals.match_filter_keys
                    and ModelClass.objects.filter(
                        ModelClass.Additionals.match_filter(validated_data)
                    )
                )
                if already_inserted:
                    changed = False
                    if len(already_inserted) > 1:
                        logger.error(
                            f"{ModelClass} finds duplicate obj with given match filter in wheelhouse. data:{validated_data}"
                        )
                    instance = already_inserted.first()
                    for key, value in validated_data.items():
                        if (
                            ModelClass.Additionals.sync_between_sites
                            and key == "site_user"
                        ):
                            continue
                        if getattr(instance, key) != value:
                            changed = True
                            setattr(instance, key, value)
                    if changed:
                        self.child.updated_instances.append(instance)

                    self.child.without_whid.append(instance)

                else:
                    instance = ModelClass(**validated_data)
                    self.child.created_instances.append(instance)

        self.child.created_instances = ModelClass.objects.bulk_create(
            self.child.created_instances
        )

        self.child.without_whid.extend(self.child.created_instances)

        ModelClass.objects.bulk_update(
            self.child.updated_instances, fields=self.get_update_fields()
        )

        all_instances.extend(self.child.without_whid)
        all_instances.extend(self.child.with_whid)

        if ModelClass.Additionals.sync_between_sites:
            for instance in self.child.updated_instances:
                SiteSyncPreference.update_object_sync_table(instance)

            for instance in self.child.created_instances:
                SiteSyncPreference.update_object_sync_table(instance)

        return all_instances


class MSSQLModelSerializer(serializers.ModelSerializer):
    class Meta:
        fields = None
        model = None
        mssql_model_normalizer = None
        abstract = True

    def __init__(self, *args, **kwargs):
        self.created_instances = None
        self.updated_instances = None
        self.with_whid = None
        self.without_whid = None
        self.tz = kwargs.pop("tz", None)
        super().__init__(*args, **kwargs)

    def get_mssql_model_normalizer(self):
        return self.__class__.Meta.mssql_model_normalizer

    def to_internal_value(self, data):
        mssql_model_instance = self.get_mssql_model_normalizer()(
            mssql_data=[data], tz=self.tz
        )
        data = mssql_model_instance.populate_model_json()
        return data[0]

    def to_representation(self, instance):
        mssql_model_instance = self.get_mssql_model_normalizer()(
            model_qs=[instance], tz=self.tz
        )
        data = mssql_model_instance.populate_mssql_json()
        return data[0]

    def create(self, validated_data):
        ModelClass = self.Meta.model

        if not self.updated_instances:
            self.updated_instances = []
        if not self.created_instances:
            self.created_instances = []
        if not self.without_whid:
            self.without_whid = []
        if not self.with_whid:
            self.with_whid = []

        if ModelClass.Additionals.update_whid and validated_data.get("id"):
            instance = ModelClass.objects.get(id=validated_data.get("id"))

            changed = False
            for key, value in validated_data.items():
                if ModelClass.Additionals.sync_between_sites and key == "site_user":
                    continue
                if getattr(instance, key) != value:
                    changed = True
                    break

            if changed:
                instance.from_site = True
                instance = self.update(instance, validated_data)
                self.updated_instances.append(instance)

            self.with_whid.append(instance)

        else:
            validated_data.pop("id", None)  # to prevent create new item
            already_inserted = (
                ModelClass.Additionals.match_filter_keys
                and ModelClass.objects.filter(
                    ModelClass.Additionals.match_filter(validated_data)
                )
            )
            if already_inserted:
                changed = False
                if len(already_inserted) > 1:
                    logger.error(
                        f"{ModelClass} finds duplicate obj with given match filter in wheelhouse. data:{validated_data}"
                    )
                instance = already_inserted.first()

                for key, value in validated_data.items():
                    if ModelClass.Additionals.sync_between_sites and key == "site_user":
                        continue
                    if getattr(instance, key) != value:
                        changed = True
                        break

                if changed:
                    instance.from_site = True
                    instance = self.update(instance, validated_data)
                    self.updated_instances.append(instance)

                self.without_whid.append(instance)

            else:
                instance = ModelClass(**validated_data)
                instance.from_site = True
                instance.save()
                self.created_instances.append(instance)
                self.without_whid.append(instance)

        return instance


class TicketMainMSSQLModelSerializer(MSSQLModelSerializer):
    class Meta:
        model = TicketMain
        mssql_model_normalizer = TicketModelNormalizer
        fields = "__all__"
        list_serializer_class = CustomListSerializer


class TicketViewMSSQLModelSerializer(MSSQLModelSerializer):
    class Meta:
        model = TicketView
        mssql_model_normalizer = TicketViewModelNormalizer
        fields = "__all__"
        list_serializer_class = CustomListSerializer


class PersonTableMSSQLModelSerializer(MSSQLModelSerializer):
    class Meta:
        model = PersonTable
        mssql_model_normalizer = PersonTableModelNormalizer
        fields = "__all__"


class CompanyTypeMSSQLModelSerializer(MSSQLModelSerializer):
    class Meta:
        model = CompanyType
        mssql_model_normalizer = CompanyTypeModelNormalizer
        fields = "__all__"


class CompanyTableMSSQLModelSerializer(MSSQLModelSerializer):
    class Meta:
        model = CompanyTable
        mssql_model_normalizer = CompanyTableModelNormalizer
        fields = "__all__"


class CompanyTypeTableMSSQLModelSerializer(MSSQLModelSerializer):
    class Meta:
        model = CompanyTypeTable
        mssql_model_normalizer = CompanyTypeTableModelNormalizer
        fields = "__all__"


class PersonCompanyMSSQLModelSerializer(MSSQLModelSerializer):
    class Meta:
        model = PersonCompany
        mssql_model_normalizer = PersonCompanyModelNormalizer
        fields = "__all__"


class MeasurementUnitMSSQLModelSerializer(MSSQLModelSerializer):
    class Meta:
        model = MeasurementUnit
        mssql_model_normalizer = MeasurementUnitModelNormalizer
        fields = "__all__"


class ProductTableMSSQLModelSerializer(MSSQLModelSerializer):
    class Meta:
        model = ProductTable
        mssql_model_normalizer = ProductTableModelNormalizer
        fields = "__all__"


class AllocationTableMSSQLModelSerializer(MSSQLModelSerializer):
    class Meta:
        model = AllocationTable
        mssql_model_normalizer = AllocationTableModelNormalizer
        fields = "__all__"


class TransferLimitMSSQLModelSerializer(MSSQLModelSerializer):
    class Meta:
        model = TransferLimit
        mssql_model_normalizer = TransferLimitModelNormalizer
        fields = "__all__"


class AllocationTransferLimitMSSQLModelSerializer(MSSQLModelSerializer):
    class Meta:
        model = AllocationTransferLimit
        mssql_model_normalizer = AllocationTransferLimitModelNormalizer
        fields = "__all__"


class CompanyAllocationMSSQLModelSerializer(MSSQLModelSerializer):
    class Meta:
        model = CompanyAllocation
        mssql_model_normalizer = CompanyAllocationModelNormalizer
        fields = "__all__"


class AllocationTransactionTypeMSSQLModelSerializer(MSSQLModelSerializer):
    class Meta:
        model = AllocationTransactionType
        mssql_model_normalizer = AllocationTransactionTypeModelNormalizer
        fields = "__all__"


class CompanyHierarchyMSSQLModelSerializer(MSSQLModelSerializer):
    class Meta:
        model = CompanyHierarchy
        mssql_model_normalizer = CompanyHierarchyModelNormalizer
        fields = "__all__"


class CompanyHierarchyCompanyMSSQLModelSerializer(MSSQLModelSerializer):
    class Meta:
        model = CompanyHierarchyCompany
        mssql_model_normalizer = CompanyHierarchyCompanyModelNormalizer
        fields = "__all__"


class CountryMSSQLModelSerializer(MSSQLModelSerializer):
    class Meta:
        model = Country
        mssql_model_normalizer = CountryModelNormalizer
        fields = "__all__"


class AddressMSSQLModelSerializer(MSSQLModelSerializer):
    class Meta:
        model = Address
        mssql_model_normalizer = AddressModelNormalizer
        fields = "__all__"


class CompanyAddressMSSQLModelSerializer(MSSQLModelSerializer):
    class Meta:
        model = CompanyAddress
        mssql_model_normalizer = CompanyAddressModelNormalizer
        fields = "__all__"


class PersonAddressMSSQLModelSerializer(MSSQLModelSerializer):
    class Meta:
        model = PersonAddress
        mssql_model_normalizer = PersonAddressModelNormalizer
        fields = "__all__"


class ContactTypeMSSQLModelSerializer(MSSQLModelSerializer):
    class Meta:
        model = ContactType
        mssql_model_normalizer = ContactTypeModelNormalizer
        fields = "__all__"


class ContactMSSQLModelSerializer(MSSQLModelSerializer):
    class Meta:
        model = Contact
        mssql_model_normalizer = ContactModelNormalizer
        fields = "__all__"


class CompanyContactMSSQLModelSerializer(MSSQLModelSerializer):
    class Meta:
        model = CompanyContact
        mssql_model_normalizer = CompanyContactModelNormalizer
        fields = "__all__"


class LocationMSSQLModelSerializer(MSSQLModelSerializer):
    class Meta:
        model = LocationTable
        mssql_model_normalizer = LocationModelNormalizer
        fields = "__all__"


class CompanyLocationMSSQLModelSerializer(MSSQLModelSerializer):
    class Meta:
        model = CompanyLocation
        mssql_model_normalizer = CompanyLocationModelNormalizer
        fields = "__all__"


class ProductLocationMSSQLModelSerializer(MSSQLModelSerializer):
    class Meta:
        model = ProductLocation
        mssql_model_normalizer = ProductLocationModelNormalizer
        fields = "__all__"


SERIALIZERS = MSSQLModelSerializer.__subclasses__()

