import logging
from rest_framework.viewsets import ViewSet
from rest_framework.decorators import action
from rest_framework.response import Response

from django.db.models import Date<PERSON><PERSON><PERSON>ield

from .models import (
    UpdateTablePointer,
    SiteSyncPreference,
    ConsoleCommand,
    ObjectSyncTable,
)
from .authenticate import Terminal<PERSON><PERSON><PERSON><PERSON>, IsTerminalBossUser
from .serializer import UpdateTablePointerSerializer, DeletedObjectSerializer

from .common.func import (
    MAP_TABLE_NAME_TO_NORMALIZER,
    MAP_TABLE_NAME_TO_MODEL,
    MAP_TABLENAME_SERIALIZER,
)
from .common.command import create_whid_upsert, create_upsert, create_delete
from vantedge.util.json.render import Vantedge<PERSON>amel<PERSON><PERSON><PERSON>enderer
from djangorestframework_camel_case.parser import CamelCaseJSONParser

logger = logging.getLogger(__name__)


class SyncDataViewSet(ViewSet):
    authentication_classes = [TerminalBossAuth]
    permission_classes = [IsTerminalBossUser]
    renderer_classes = [VantedgeCamelJ<PERSON><PERSON>enderer]
    parser_classes = [CamelCaseJSONParser]
    lookup_field = "table"

    @action(detail=True, methods=["get"])
    def status(self, request, table):
        table_name = table
        pointer_field = request.query_params.get("pointer")

        new_pointer = UpdateTablePointer.check_update_pointer(
            site_user=request.user, table_name=table_name
        )
        response = UpdateTablePointerSerializer(new_pointer, many=False).data
        if new_pointer:
            new_pointer.is_applied = True
            new_pointer.save()

        else:
            model_normalizer = MAP_TABLE_NAME_TO_NORMALIZER.get(table_name)
            normalized_pointer_field = model_normalizer().get_normalized_name(
                pointer_field
            )
            last_object = (
                model_normalizer.django_model.objects.filter(site_user=request.user)
                .values(normalized_pointer_field)
                .order_by(normalized_pointer_field)
                .last()
            )

            if isinstance(
                (
                    getattr(model_normalizer.django_model, normalized_pointer_field)
                ).field,
                DateTimeField,
            ):
                if last_object:
                    pointer_value = last_object[normalized_pointer_field]
                else:
                    pointer_value = "2000-01-01"
            else:
                if last_object:
                    pointer_value = last_object[normalized_pointer_field]
                else:
                    pointer_value = 0

            response = {"table_name": table_name, "pointer_value": pointer_value}

        return Response(response)

    @action(detail=True, methods=["post"])
    def sync(self, request, table):
        statements = []
        user = request.user
        data = request.data
        rows = data["rows"]
        columns = data["columns"]

        data = [dict(zip(columns, row)) for row in rows]

        mssql_model_serializer_class = MAP_TABLENAME_SERIALIZER[table]
        mssql_model_serializer = mssql_model_serializer_class(
            data=data, tz=user.site_timezone, many=True
        )
        django_model = mssql_model_serializer_class.Meta.model
        mssql_model_serializer.is_valid()
        incoming_instances = mssql_model_serializer.save(
            site_user=user, company=user.company
        )

        with_whid = mssql_model_serializer.child.with_whid
        without_whid = mssql_model_serializer.child.without_whid

        logger.info(
            f"sync info, {user} total: {len(incoming_instances)}, updated instances: {len(with_whid)}, created instances: {len(without_whid)}"
        )

        if django_model.Additionals.sync_between_sites:

            statements.extend(
                create_whid_upsert(
                    with_whid,
                    created=False,
                    site_user=user,
                    sub_type=django_model.Additionals.on_update_sub_type,
                )
            )
            statements.extend(
                create_whid_upsert(
                    without_whid,
                    created=True,
                    site_user=user,
                    sub_type=django_model.Additionals.on_update_sub_type,
                )
            )

        elif django_model.Additionals.update_whid:
            statements.extend(
                create_whid_upsert(
                    without_whid,
                    created=True,
                    site_user=user,
                    sub_type=django_model.Additionals.on_update_sub_type,
                )
            )

        return Response(statements)

    @action(detail=True, methods=["post"])
    def pull_updates(self, request, table):
        user = request.user

        commands = []

        model = MAP_TABLE_NAME_TO_MODEL.get(table)
        pending_objects = SiteSyncPreference.get_pending_objects(model, user)
        if pending_objects:
            command_sub_type = model.Additionals.on_upsert_sub_type or "upsert"
            commands = create_upsert(pending_objects, user, command_sub_type)

        return Response(commands)

    @action(detail=False, methods=["post"])
    def pull_commands(self, request):
        user = request.user

        commands = ConsoleCommand.get_site_commands(user)

        return Response(commands)

    @action(detail=True, methods=["post"])
    def push_deletes(self, request, table):
        """
            to get deleted objects from tboss side
        """
        user = request.user
        data = request.data
        serializer = DeletedObjectSerializer(data=data, many=True)
        serializer.is_valid()
        serializer.save(site_user=user, company=user.company)

        return Response()

    @action(detail=True, methods=["post"])
    def pull_deletes(self, request, table):
        """
            send deleted objects to tboss site
        """
        commands = []
        user = request.user
        model = MAP_TABLE_NAME_TO_MODEL.get(table)
        pending_objects = ObjectSyncTable.get_deleted_pending_objects(model, user)

        if pending_objects:
            commands = create_delete(pending_objects, user)

        return Response(commands)

    @action(detail=True, methods=["post"])
    def match_whids(self, request, table):
        """
            recieve WHID values of table and register missing ones
        """
        user = request.user
        data = request.data
        site_whids = data["whids"]

        model = MAP_TABLE_NAME_TO_MODEL.get(table)

        all_whids = model.get_whids(user)

        missing_whids = set(all_whids) - set(site_whids)

        instances = model.objects.filter(id__in=missing_whids).order_by("id")

        logger.info(
            f"sync info, {user}:{table} was {len(instances)} behind, registering...."
        )

        for instance in instances:
            SiteSyncPreference.register_into_object_sync(instance, user)

        return Response()

