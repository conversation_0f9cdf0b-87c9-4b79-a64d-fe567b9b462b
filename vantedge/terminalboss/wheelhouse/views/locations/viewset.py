from django_auto_prefetching import AutoPrefetchViewSetMixin
from django.db.models import Cha<PERSON><PERSON>ield, F, Value
from django.db.models.functions import Concat
from rest_framework.response import Response
from vantedge.printables.views import PrintableViewset
from vantedge.terminalboss.wheelhouse import serializers
from vantedge.terminalboss.models import LocationTable
from rest_framework.decorators import action
from rest_framework.viewsets import ModelViewSet
from rest_framework.pagination import LimitOffsetPagination
from rest_framework.permissions import IsAuthenticated
import string


class LocationTableViewSet(AutoPrefetchViewSetMixin, PrintableViewset):
    serializer_class = serializers.LocationTableSerializer
    pagination_class = LimitOffsetPagination

    def get_queryset(self):
        user = self.request.user
        try:
            company = user.company.terminalbosscompany
        except:
            return LocationTable.objects.none()

        qs = LocationTable.objects.filter(company=company).annotate(
            site=F("site_user__username")
        )
        return qs

    @action(detail=False, methods=["get"], permission_classes=[IsAuthenticated])
    def choices(self, request):

        uwiSiteLocation = list(
            dict({"id": code, "label": label})
            for code, label in LocationTable.LOCATION_FORMATS
        )
        quarterUnits = [unit for unit in string.ascii_lowercase[:4]]
        blocks = [block for block in string.ascii_uppercase[:12]]
        mapAreas = [mapArea for mapArea in string.ascii_uppercase[:16]]
        mapSheet = [mapSheet for mapSheet in range(1, 17)]

        return Response(
            {
                "uwiSiteLocation": uwiSiteLocation,
                "quarterUnits": quarterUnits,
                "blocks": blocks,
                "mapAreas": mapAreas,
                "mapSheet": mapSheet,
            }
        )
