from django.db.models import F, Value
from django.db.models.fields import <PERSON><PERSON><PERSON><PERSON>
from django.db.models.functions import Concat
from django_auto_prefetching import AutoPrefetchViewSetMixin
from rest_framework.decorators import action
from rest_framework.pagination import LimitOffsetPagination
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from vantedge.printables.views import PrintableViewset
from vantedge.terminalboss.wheelhouse import serializers
from vantedge.terminalboss.models import PersonTable, PersonCompany


class PersonTableViewSet(AutoPrefetchViewSetMixin, PrintableViewset):
    serializer_class = serializers.PersonTableSerializer
    pagination_class = LimitOffsetPagination

    def get_queryset(self):
        user = self.request.user
        try:
            company = user.company.terminalbosscompany
        except:
            return PersonTable.objects.none()

        qs = (
            PersonTable.objects.prefetch_related("personcompany_set")
            .filter(company=company)
            .annotate(
                site=F("site_user__username"),
                name=Concat(
                    F("first_name"),
                    Value(" "),
                    F("last_name"),
                    output_field=CharField(),
                ),
            )
        )
        return qs

    @action(detail=False, methods=["get"], permission_classes=[IsAuthenticated])
    def choices(self, request):
        qs = self.get_queryset().values("id", "name")
        return Response(qs)


class PersonCompanyViewset(AutoPrefetchViewSetMixin, PrintableViewset):
    serializer_class = serializers.PersonCompanySerializer
    pagination_class = LimitOffsetPagination

    def get_queryset(self):
        user = self.request.user
        try:
            company = user.company.terminalbosscompany
        except:
            return PersonCompany.objects.none()

        qs = PersonCompany.objects.filter(company=company).annotate(
            site=F("site_user__username")
        )
        return qs
