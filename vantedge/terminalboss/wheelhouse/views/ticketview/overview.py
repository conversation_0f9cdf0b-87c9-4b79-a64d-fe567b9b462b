from datetime import datetime
from django.contrib.postgres.aggregates.general import ArrayAgg
from django.db.models import (
    Case,
    When,
    Min,
    Max,
    Avg,
    Sum,
    F,
    Count,
    Q,
    Value,
    FloatField,
    IntegerField,
    CharField,
    DateField,
    OuterRef,
    Subquery,
)
from django.db.models.functions import Cast, Coalesce
from vantedge.terminalboss.models import SiteUser
from vantedge.terminalboss.models.tables import (
    AllocationTable,
    CompanyAllocation,
    ProductTable,
    TicketView,
    TransferLimit,
)

from rest_framework.decorators import action
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.viewsets import ViewSet
import arrow, calendar, pandas as pd, numpy as np
from zoneinfo import ZoneInfo


class OverviewViewset(ViewSet):
    @action(detail=False, methods=["get"], permission_classes=[IsAuthenticated])
    def allocation_performance(self, request):
        user = self.request.user
        site = self.request.query_params.get("site")
        product = self.request.query_params.get("product")
        groupby = self.request.query_params.get("groupby", "company_name")

        try:
            company = user.company.terminalbosscompany
            product = ProductTable.objects.get(id=product)
            site = SiteUser.objects.get(id=site, company=company)
            tz = ZoneInfo(site.timezone)
            day_start = site.day_start
        except:
            return Response([])

        now = datetime.now(tz)
        dateStr = self.request.query_params.get("date", now.strftime("%Y-%m"))
        format = "%Y-%m"
        try:
            date = datetime.strptime(dateStr, format).replace(tzinfo=tz)
        except:
            raise ValueError("Invalid date parameter")

        start_time = date.replace(
            hour=day_start.hour,
            minute=day_start.minute,
            second=day_start.second,
            microsecond=day_start.microsecond,
        )
        end_time = arrow.get(start_time).shift(months=1).datetime

        companyAnnotate = CompanyAllocation.objects.exclude(
            company_type_key__company_type_code="Stockholder"
        ).filter(company=company, allocation_key=OuterRef("id"))
        stockholderAnnotate = CompanyAllocation.objects.filter(
            company=company,
            allocation_key=OuterRef("id"),
            company_type_key__company_type_code="Stockholder",
        )

        allocations = AllocationTable.objects.prefetch_related(
            "allocationtransferlimit_set__transfer_limit"
        ).filter(
            Q(company=company, site_user=site, product_key=product)
            & Q(effective_date__lt=end_time)
            & ~Q(expiration_date__lt=start_time)
        )
        groupby_fields = []
        if groupby == "allocation_code":
            groupby_fields.append(groupby)
        elif groupby == "company_name":
            allocations = allocations.annotate(
                company_name=companyAnnotate.values("company_key__name")[:1]
            )
            groupby_fields.append(groupby)
        elif groupby == "stockholder_name":
            allocations = allocations.annotate(
                stockholder_name=stockholderAnnotate.values("company_key__name")[:1]
            )
            groupby_fields.append(groupby)
        elif groupby == "company_stockholder":
            allocations = allocations.annotate(
                company_name=companyAnnotate.values("company_key__name")[:1],
                stockholder_name=stockholderAnnotate.values("company_key__name")[:1],
            )
            groupby_fields.extend(list(map(lambda f: f"{f}_name", groupby.split("_"))))
        else:
            raise ValueError("Invalid groupby parameter")

        allocations = (
            allocations.values(*groupby_fields).distinct().order_by(*groupby_fields)
        )

        if groupby == "allocation_code":
            tickets = allocations.annotate(
                transferred=Coalesce(
                    Sum(
                        Case(
                            When(
                                Q(
                                    allocationtransferlimit__transfer_limit__transfer_limit_start_date__lt=end_time
                                )
                                & ~Q(
                                    allocationtransferlimit__transfer_limit__transfer_limit_end_date__lt=start_time
                                ),
                                then=F(
                                    "allocationtransferlimit__transfer_limit__transferred_net_volume"
                                ),
                            )
                        )
                    ),
                    0,
                    output_field=FloatField(),
                ),
                load_count=Value("N/A"),
            )
        else:
            # get the transferred net volumes from TicketView
            tickets = (
                TicketView.reporting.exclude_parents()
                .filter(
                    company=company,
                    site_user=site,
                    product_name=product.product_name,
                    ticket_open_time__range=(start_time, end_time),
                )
                .exclude(ticket_status=TicketView.VOID)
                .annotate(
                    company_name=Case(
                        When(producer_name__isnull=False, then=F("producer_name")),
                        When(customer_name__isnull=False, then=F("customer_name")),
                        default=Value(""),
                        output=CharField(),
                    )
                )
                .values(*groupby_fields)
                .distinct()
                .annotate(
                    transferred=Coalesce(
                        Sum("transferred_net_volume"), 0, output_field=FloatField()
                    ),
                    load_count=Count("pk"),
                )
                .order_by(*groupby_fields)
            )

        allocations = allocations.annotate(
            allocation_codes=ArrayAgg("allocation_code", distinct=True, default=[]),
            limit=Coalesce(
                Sum(
                    Case(
                        When(
                            Q(
                                allocationtransferlimit__transfer_limit__period_type=TransferLimit.MONTHLY
                            )
                            & Q(
                                allocationtransferlimit__transfer_limit__transfer_limit_start_date__lt=end_time
                            )
                            & ~Q(
                                allocationtransferlimit__transfer_limit__transfer_limit_end_date__lt=start_time
                            ),
                            then=F("allocationtransferlimit__transfer_limit__limit"),
                        )
                    )
                ),
                0,
                output_field=FloatField(),
            ),
        )

        if allocations:
            allocations_df = pd.DataFrame(allocations)
        else:
            allocations_df = pd.DataFrame(
                columns=["allocation_codes", "limit"] + groupby_fields
            )

        if tickets:
            tickets_df = pd.DataFrame(tickets)
        else:
            tickets_df = pd.DataFrame(
                columns=["transferred", "load_count"] + groupby_fields
            )

        df = pd.merge(allocations_df, tickets_df, on=groupby_fields, how="outer")
        df[["limit", "transferred", "load_count"]] = (
            df[["limit", "transferred", "load_count"]].fillna(0).round(0)
        )
        df["allocation_codes"] = df["allocation_codes"].apply(
            lambda x: x if x is not np.nan else []
        )
        numOfDaysInMonth = calendar.monthrange(date.year, date.month)[1]
        monthPercentageCompleted = 1

        if date > now:
            df["performance_indicator"] = 0
        else:
            if now.strftime(format) == date.strftime(format):
                monthPercentageCompleted = now.day / numOfDaysInMonth
            df["performance_indicator"] = np.trunc(
                df["transferred"]
                .div(df["limit"] * monthPercentageCompleted)
                .replace([np.inf, -np.inf, np.nan], 0)
                .multiply(100)
            )
        df["remaining"] = (df["limit"] - df["transferred"]).clip(0).round(0)
        if groupby == "company_name":
            df["name"] = df["company_name"]
        elif groupby == "stockholder_name":
            df["name"] = df["stockholder_name"]
        elif groupby == "company_stockholder":
            df["name"] = df["company_name"] + "/" + df["stockholder_name"]
        elif groupby == "allocation_code":
            df["allocation_code"] = "Code: " + df["allocation_code"]
            df["name"] = df["allocation_code"]
        df["info"] = df[
            [
                "limit",
                "transferred",
                "load_count",
                "remaining",
                "performance_indicator",
                "allocation_codes",
            ]
            + groupby_fields
        ].apply(lambda row: row.to_dict(), axis=1)
        df = df.groupby("name")["info"].apply(list)

        return Response(df.to_dict())

    @action(detail=False, methods=["POST"], permission_classes=[IsAuthenticated])
    def allocation_performance_aggregate_sites(self, request):
        user = self.request.user
        sites = self.request.data.get("sites")
        product = self.request.data.get("product")
        groupby = self.request.data.get("groupby", "product")

        if groupby == "product":
            groupby = "company_name"
        elif groupby == "product_c_st":
            groupby = "company_stockholder"

        try:
            company = user.company.terminalbosscompany
            product = ProductTable.objects.get(id=product)
            sites = SiteUser.objects.filter(id__in=sites, company=company)
        except Exception as e:
            return Response([])

        dateStr = self.request.data.get("date")
        format = "%Y-%m"

        allocation_time_filters = Q()
        allocation_transferlimit_time = Q()
        ticket_time_filters = Q()

        for site in sites:
            site_timezone = site.site_timezone
            day_start = site.day_start

            try:
                date = datetime.strptime(dateStr, format).replace(tzinfo=site_timezone)
            except:
                raise ValueError("Invalid date parameter")

            start_time = date.replace(
                hour=day_start.hour,
                minute=day_start.minute,
                second=day_start.second,
                microsecond=day_start.microsecond,
            )
            end_time = arrow.get(start_time).shift(months=1).datetime
            allocation_time_filters |= (
                Q(site_user=site)
                & Q(effective_date__lt=end_time)
                & ~Q(expiration_date__lt=start_time)
            )
            allocation_transferlimit_time |= (
                Q(
                    site_user=site,
                    allocationtransferlimit__transfer_limit__period_type=TransferLimit.MONTHLY,
                )
                & Q(
                    allocationtransferlimit__transfer_limit__transfer_limit_start_date__lt=end_time
                )
                & ~Q(
                    allocationtransferlimit__transfer_limit__transfer_limit_end_date__lt=start_time
                )
            )
            ticket_time_filters |= Q(site_user=site) & Q(
                ticket_open_time__range=(start_time, end_time)
            )

        now = datetime.now(site_timezone)
        companyAnnotate = CompanyAllocation.objects.exclude(
            company_type_key__company_type_code="Stockholder"
        ).filter(company=company, allocation_key=OuterRef("id"))
        stockholderAnnotate = CompanyAllocation.objects.filter(
            company=company,
            allocation_key=OuterRef("id"),
            company_type_key__company_type_code="Stockholder",
        )

        allocations = (
            AllocationTable.objects.prefetch_related(
                "allocationtransferlimit_set__transfer_limit"
            )
            .filter(allocation_time_filters)
            .filter(product_key=product)
        )

        groupby_fields = []
        if groupby == "allocation_code":
            groupby_fields.append(groupby)
        elif groupby == "company_name":
            allocations = allocations.annotate(
                company_name=companyAnnotate.values("company_key__name")[:1]
            )
            groupby_fields.append(groupby)
        elif groupby == "stockholder_name":
            allocations = allocations.annotate(
                stockholder_name=stockholderAnnotate.values("company_key__name")[:1]
            )
            groupby_fields.append(groupby)
        elif groupby == "company_stockholder":
            allocations = allocations.annotate(
                company_name=companyAnnotate.values("company_key__name")[:1],
                stockholder_name=stockholderAnnotate.values("company_key__name")[:1],
            )
            groupby_fields.extend(list(map(lambda f: f"{f}_name", groupby.split("_"))))
        else:
            raise ValueError("Invalid groupby parameter")

        allocations = allocations.annotate(
            direction=Cast("product_direction", CharField())
        )
        groupby_fields.append("direction")
        allocations = (
            allocations.values(*groupby_fields).distinct().order_by(*groupby_fields)
        )

        allocations = allocations.annotate(
            allocation_codes=ArrayAgg("allocation_code", distinct=True, default=[]),
            limit=Coalesce(
                Sum(
                    Case(
                        When(
                            allocation_transferlimit_time,
                            then=F("allocationtransferlimit__transfer_limit__limit"),
                        )
                    )
                ),
                0,
                output_field=FloatField(),
            ),
        )

        tickets = (
            TicketView.reporting.exclude_parents()
            .filter(
                company=company,
                site_user__in=sites,
                product_name=product.product_name,
            )
            .filter(ticket_time_filters)
            .exclude(ticket_status=TicketView.VOID)
            .annotate(
                company_name=Case(
                    When(producer_name__isnull=False, then=F("producer_name")),
                    When(customer_name__isnull=False, then=F("customer_name")),
                    default=Value(""),
                    output=CharField(),
                ),
                direction=Case(
                    When(producer_name__isnull=False, then=Value("1")),
                    default=None,
                    output=IntegerField(),
                ),
            )
            .values(*groupby_fields)
            .distinct()
            .annotate(
                transferred=Coalesce(
                    Sum("transferred_net_volume"), 0, output_field=FloatField()
                ),
                load_count=Count("pk"),
            )
            .order_by(*groupby_fields)
        )

        if allocations:
            allocations_df = pd.DataFrame(allocations)
        else:
            allocations_df = pd.DataFrame(
                columns=["allocation_codes", "limit"] + groupby_fields
            )

        if tickets:
            tickets_df = pd.DataFrame(tickets)
        else:
            tickets_df = pd.DataFrame(
                columns=["transferred", "load_count"] + groupby_fields
            )

        df = pd.merge(allocations_df, tickets_df, on=groupby_fields, how="outer")
        df[["limit", "transferred", "load_count"]] = (
            df[["limit", "transferred", "load_count"]].fillna(0).round(0)
        )
        df["allocation_codes"] = df["allocation_codes"].apply(
            lambda x: x if x is not np.nan else []
        )

        numOfDaysInMonth = calendar.monthrange(date.year, date.month)[1]
        monthPercentageCompleted = 1

        if date > now:
            df["performance_indicator"] = 0
        else:
            if now.strftime(format) == date.strftime(format):
                monthPercentageCompleted = now.day / numOfDaysInMonth
            df["performance_indicator"] = np.trunc(
                df["transferred"]
                .div(df["limit"] * monthPercentageCompleted)
                .replace([np.inf, -np.inf, np.nan], 0)
                .multiply(100)
            )
        df["remaining"] = (df["limit"] - df["transferred"]).clip(0).round(0)

        if groupby == "company_name":
            df["name"] = df["company_name"]
        elif groupby == "stockholder_name":
            df["name"] = df["stockholder_name"]
        elif groupby == "company_stockholder":
            df["name"] = df["company_name"] + "/" + df["stockholder_name"]
        elif groupby == "allocation_code":
            df["allocation_code"] = "Code: " + df["allocation_code"]
            df["name"] = df["allocation_code"]

        if not df.empty:
            df["name"] = df.apply(
                lambda row: row["name"] + " (Nomination)"
                if row["direction"] is not None
                else row["name"],
                axis=1,
            )

            df["company_name"] = df.apply(
                lambda row: row["company_name"] + " (Nomination)"
                if row["direction"] is not None
                else row["company_name"],
                axis=1,
            )

        df["info"] = df[
            [
                "limit",
                "transferred",
                "load_count",
                "remaining",
                "performance_indicator",
                "allocation_codes",
            ]
            + groupby_fields
        ].apply(lambda row: row.to_dict(), axis=1)
        df = df.groupby("name")["info"].apply(list)

        return Response(df.to_dict())

    @action(detail=False, methods=["get"], permission_classes=[IsAuthenticated])
    def product_transferred(self, request):
        user = self.request.user
        site = self.request.query_params.get("site")
        groupby = self.request.query_params.get("groupby", "company_name")
        try:
            company = user.company.terminalbosscompany
            site = SiteUser.objects.get(id=site)
            tz = ZoneInfo(site.timezone)
            day_start = site.day_start
        except:
            return Response([])

        now = datetime.now(tz)
        dateStr = self.request.query_params.get("date", now.strftime("%Y-%m"))
        format = "%Y-%m"
        try:
            date = datetime.strptime(dateStr, format).replace(tzinfo=tz)
        except:
            raise ValueError("Invalid date parameter")

        start_time = date.replace(
            hour=day_start.hour,
            minute=day_start.minute,
            second=day_start.second,
            microsecond=day_start.microsecond,
        )

        end_time = arrow.get(start_time).shift(months=1).datetime

        groupby_fields = ["product_name"]

        tickets = (
            TicketView.reporting.exclude_parents()
            .filter(
                ticket_open_time__range=(start_time, end_time),
                site_user=site,
                company=company,
            )
            .exclude(ticket_status=TicketView.VOID)
            .annotate(
                company_name=Case(
                    When(producer_name__isnull=False, then=F("producer_name")),
                    When(customer_name__isnull=False, then=F("customer_name")),
                    default=Value(""),
                    output=CharField(),
                )
            )
        )

        if groupby == "company_name":
            groupby_fields.append(groupby)
        elif groupby == "stockholder_name":
            groupby_fields.append(groupby)
        elif groupby == "company_stockholder":
            groupby_fields.extend(list(map(lambda f: f"{f}_name", groupby.split("_"))))

        productTransferred = (
            tickets.values(*groupby_fields)
            .distinct()
            .order_by(*groupby_fields)
            .annotate(volume=Sum("transferred_net_volume"))
        )
        # productTransferred = tickets.values("product_name").distinct().order_by("product_name").annotate(volume=Sum("transferred_net_volume"))
        # productTransferred = (
        #     TicketView.reporting.exclude_parents()
        #     .filter(
        #         ticket_open_time__range=(start_time, end_time),
        #         site_user=site,
        #         company=company,
        #     )
        #     .exclude(ticket_status=TicketView.VOID)
        #     .values("product_name")
        #     .distinct()
        #     .order_by("product_name")
        #     .annotate(volume=Sum("transferred_net_volume"))
        # )
        return Response(productTransferred)

    @action(detail=False, methods=["get"], permission_classes=[IsAuthenticated])
    def loads_widget(self, request):
        format = "%Y-%m-%d"
        now = datetime.now()

        dateStr = self.request.query_params.get("date", now.strftime(format))
        site = self.request.query_params.get("site")
        product = self.request.query_params.get("product")

        try:
            date = datetime.strptime(dateStr, format)
        except:
            raise ValueError("Invalid date parameter")

        monthStart = date.replace(day=1)
        dateRange = pd.date_range(start=monthStart.date(), end=date.date())

        try:
            product = ProductTable.objects.get(id=product)
        except ProductTable.DoesNotExist:
            raise ValueError("Product does not exist")

        loads = (
            TicketView.reporting.exclude_parents()
            .filter(
                site_user=site,
                product_name=product.product_name,
                transaction_type_name="Accuload Loading",
                ticket_open_time__date__in=dateRange,
            )
            .exclude(ticket_status=TicketView.VOID)
            .annotate(day=Cast("ticket_open_time", DateField()))
            .values("day", "customer_name")
            .distinct()
            .annotate(volume=Sum("transferred_net_volume"), count=Count("pk"))
        )

        if not loads:
            return Response([])

        loadsDf = pd.DataFrame(loads)
        monthToDate = (
            loadsDf.groupby("customer_name")[["volume", "count"]].sum().reset_index()
        )
        monthToDate["avgVolume"] = monthToDate["volume"].div(monthToDate["count"])

        customers = loadsDf["customer_name"]
        presetData = pd.DataFrame(
            list(map(lambda x: [x] + [0.0] * 2 + [0], dateRange)),
            columns=["day", "volume", "avgVolume", "count"],
        )
        preset = pd.merge(customers, presetData, how="cross")

        preset["day"] = preset["day"].astype(str)
        loadsDf["day"] = loadsDf["day"].astype(str)

        trend = (
            pd.concat([preset, loadsDf])
            .groupby(["customer_name", "day"])[["volume", "count"]]
            .sum()
            .reset_index()
        )

        trend["volume_cumsum"] = trend.groupby(["customer_name"])["volume"].transform(
            pd.Series.cumsum
        )
        trend["count_cumsum"] = trend.groupby(["customer_name"])["count"].transform(
            pd.Series.cumsum
        )

        trend["trend"] = trend[
            ["customer_name", "day", "volume_cumsum", "count_cumsum"]
        ].apply(lambda row: row.to_dict(), axis=1)
        trend = trend.groupby("customer_name")["trend"].apply(list)
        df = pd.merge(monthToDate, trend, on=["customer_name"]).round(0)
        return Response(df.to_dict("records"))
