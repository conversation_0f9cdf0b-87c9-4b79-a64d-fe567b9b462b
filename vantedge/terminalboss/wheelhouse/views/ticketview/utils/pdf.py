import html

from django.template.loader import get_template
from django.template import Template, Context
from django.core.mail import EmailMultiAlternatives
from django.conf import settings

from vantedge.printables.utils import (
    generate_zip,
    BASIC_STYLESHEETS,
)
from vantedge.printables.views import PDFKitGenerator


def get_printable(company, code):
    """
    Get the printable object from the user's company.
    """
    return company.printable_templates.filter(code=code).first()


def generate_pdf(company, code, ticket):
    # Company str Ticket -> File
    """
    Generate the PDF for print-action

    PDF is generated based on the following:\n
    - base_template\n
    - context\n
    - printable\n
    """
    base_template = get_template("printables/base.html")
    printable = get_printable(company, code)

    context = dict(object=ticket)

    if printable:
        base_template = get_template(printable.base)
        attachments = {a.attachment_type: a for a in printable.attachments.all()}
        context.update(attachments=attachments, template=printable)
        sections = {
            section: Template(
                "{% load static %} {% spaceless %}"
                + html.unescape(getattr(printable, section))
                + "{% endspaceless %}"
            ).render(Context(context))
            for section in ["header", "content", "footer", "css"]
        }
        context.update(sections)

    return PDFKitGenerator(
        main_html=base_template.render(context),
        css=BASIC_STYLESHEETS,
    ).render_pdf()


def generate_bols_from_tickets(company, code, tickets):
    # Company str Qs[TicketView] -> Dict{ str: File }
    """
    Returns a dictionary of partner BOLs from a list of Tickets.
    """
    pdf_dict = {}

    for ticket in tickets:
        pdf = generate_pdf(company, code, ticket)
        pdf_dict[f"{ticket.bol}.pdf"] = pdf

    return pdf_dict


def generate_bols_and_zip(company, code, tickets):
    # Company str Qs[TicketView] -> File
    """
    Generates or fetches BOLs from a list of TBossV2Tickets,
    and returns them zipped.
    """
    pdf_dict = generate_bols_from_tickets(company, code, tickets)
    zipped_pdfs = generate_zip(pdf_dict, "tbossv2ticket-bols")

    return zipped_pdfs


def generate_bols_and_mail(company, code, tickets, recipients):
    # Company str Qs[TicketView] -> None
    """
    Generates or fetches BOLs from a list of TBossV2Tickets,
    and mails them to recipients.
    """
    zipped_pdfs = generate_bols_and_zip(company, code, tickets)

    from_email = settings.DEFAULT_FROM_EMAIL

    subject = "Ticket BOLs"
    text_content = "BOLs are attached to this email in a zipped document."

    msg = EmailMultiAlternatives(subject, text_content, from_email, recipients)
    msg.attach("bols.zip", zipped_pdfs, "application/zip")
    msg.send()
