import datetime
import logging
from django.db.models import <PERSON>, <PERSON>, <PERSON>, <PERSON>, Count
from django.db.utils import IntegrityError
from django.utils import timezone
from progressbar import progressbar

from .models import CarEvent, RailCar, TrackAndTraceCompany, Trip, RailStationDistance
from .sanitizer import sanitize

logger = logging.getLogger(__name__)

def detention(tracktrace_company_id=None, start_date=None):
    now = timezone.now()
    tz = timezone.get_current_timezone()
    company = None

    incurring_filters = Q(
        last_car_event__event_timestamp__gte=now-datetime.timedelta(days=368),
        detention_start_event__isnull=False, detention_stop_event__isnull=True
    )
    if start_date:
        incurring_filters &= Q(
            last_car_event__event_timestamp__gte=datetime.datetime.combine(
                start_date, datetime.datetime.min.time()
            ).replace(tzinfo=tz)
        )
    if tracktrace_company_id:
        company = TrackAndTraceCompany.objects.get(id=tracktrace_company_id)
        incurring_filters &= Q(company=company)

    # Calculate detention for incurring trips
    incurring_detention_trips = Trip.objects.filter(incurring_filters)
    if incurring_detention_trips.exists():
        logger.info(f"{incurring_detention_trips.count()} incurring trips for {company}")
        for trip in progressbar(list(incurring_detention_trips)):
            trip.calc_detention()
        Trip.objects.bulk_update(
            incurring_detention_trips,
            ["detention_time", "detention_cost", "detention_data"],
            batch_size=1000,
        )
        logger.info("Calculated incurring detention.")


INACTIVE_EVENTS = ["W", "X"]

def clean_car_events():
    groups = (
        CarEvent.objects.values("car_id", "cycle_id")
        .annotate(count=Count("id"))
        .filter(count__gt=1)
    )
    logger.info(groups)


def clean_trips():
    # delete empty trips
    logger.info("Deleting empty trips")
    result_empty_delete = Trip.objects.filter(last_car_event__isnull=True).delete()
    logger.info(f"Empty trip delete result: {result_empty_delete}")

    # delete trips if car has more than one active trip
    car_ids = (
        Trip.objects.filter(is_active=True)
        .values("car")
        .distinct()
        .order_by("car")
        .annotate(count=Count("id"))
        .filter(count__gt=1)
        .values_list("car", flat=True)
    )

    logger.info(f"Deleting trips for {len(car_ids)} cars with more than one active trip")
    result_duplicate_active = Trip.objects.filter(car_id__in=car_ids, is_active=True).delete()
    logger.info(f"Duplicate active trip delete result: {result_duplicate_active}")

    logger.info("Fetching trips with out of order events")
    incoming_carevents_min = list(
        CarEvent.objects.filter(is_invalid=False, trip__isnull=True)
        .values("car")
        .distinct()
        .order_by("car")
        .annotate(earliest=Min("event_timestamp"))
        .annotate(company=F("company"))
    )
    latest_carevents = list(
        CarEvent.objects.filter(is_invalid=False, trip__isnull=False)
        .values("car")
        .distinct()
        .order_by("car")
        .annotate(latest=Max("event_timestamp"))
    )
    recalc_cars = []
    while latest_carevents and incoming_carevents_min:
        l = latest_carevents[0] # noqa E741
        e = incoming_carevents_min[0]
        if (l["car"]) == (e["car"]):
            if l["latest"] > e["earliest"]:
                recalc_cars.append(e)
            latest_carevents.pop(0)
            incoming_carevents_min.pop(0)
        elif l["car"] < e["car"]:
            latest_carevents.pop(0)
        else:
            incoming_carevents_min.pop(0)

    logger.info(
        f"{len(recalc_cars)} cars with incoming events that are out of order"
    )

    for car_event in recalc_cars:
        trips_to_delete = Trip.objects.filter(
            car_id=car_event["car"],
            last_car_event__event_timestamp__gte=car_event["earliest"],
            company_id=car_event["company"]
        )

        # check existing rail_shipments with these trips data
        trips_with_shipments = trips_to_delete.filter(
            arrival_timestamp__isnull=False,
            waybills__shipment_car__isnull=False)

        for trips_with_shipment in trips_with_shipments:
            waybill = trips_with_shipment.waybills.filter(shipment_car__isnull=False).first()
            if waybill:
                rail_shipment = waybill.shipment_car.rail_shipment
                logger.info(f"Removing arrival date shipment {rail_shipment}")
                rail_shipment.arrive_date = None
                rail_shipment.save(update_fields=["arrive_date"])

        delete_trip_result = trips_to_delete.delete()
        logger.info(f"car event {car_event} trips {delete_trip_result} deleted!")


def chunks(data, batch_size):
    return [data[i : i + batch_size] for i in range(0, len(data), batch_size)]


def trips(delete_trips=False, company_id=None, sanitize_data=True):
    clean_trips()

    for company in (
        TrackAndTraceCompany.objects.filter(id=company_id)
        if company_id
        else TrackAndTraceCompany.objects.exclude(id=24)
    ).iterator():

        if sanitize_data:
            sanitize(check_duplicate=True, company_id=company.id)

        logger.info("start trip calculation company=%d (%s) deleteing=%s", company_id, company, delete_trips)

        if delete_trips:
            # company.trip_set.all().delete()
            logger.info("Setting trip=None for car events")
            company.carevent_set.filter(is_invalid=False, trip__isnull=False).update(
                trip=None
            )

            trips = company.trip_set.all()
            trip_ids = trips.values_list("id", flat=True)
            batch_ids = chunks(trip_ids, 1000)

            logger.info(f"{len(trip_ids)} total trips to delete.")

            for batch in batch_ids:
                delete_batch = company.trip_set.filter(id__in=batch)
                delete_batch.delete()
                logger.info(f"Deleted {len(batch)} trips.")

            logger.info(f"Remaining trips after delete: {company.trip_set.count()}")

        # get the cars of car events with no trip
        car_ids = list(
            company.carevent_set.filter(is_invalid=False, trip__isnull=True)
            .values_list("car_id", flat=True)
            .distinct()
        )

        car_qs = company.railcar_set.filter(id__in=car_ids)
        for car in car_qs.iterator():
            try:
                trip = car.trip_set.latest()
            except Trip.DoesNotExist:
                trip = None

            event_count = 0
            for car_event in (
                car.carevent_set.select_related("location")
                .filter(is_invalid=False, trip__isnull=True)
                .order_by("event_timestamp", "event_code")
                .iterator()
            ):
                event_count += 1
                # if hasattr(car_event, "detention_start_trip"):
                #     car_event.trip = car_event.detention_start_trip
                #     car_event.save()
                #     continue
                # if hasattr(car_event, "detention_stop_trip"):
                #     car_event.trip = car_event.detention_stop_trip
                #     car_event.save()
                #     continue
                previous_trip = None

                if (
                    trip
                    and car_event.event_code_id == "1"
                    and car_event.loaded == trip.last_car_event.loaded
                ):  # this condition covers if release event received not as starting event
                    car_event.cycle_id = trip.last_car_event.cycle_id

                elif (
                    trip
                    and trip.last_car_event.event_code_id
                    != "1"  # only break the trip if last car event is not a waybill event
                    and (
                        car_event.cycle_id != trip.last_car_event.cycle_id
                        or car_event.loaded != trip.last_car_event.loaded
                    )
                ):
                    trip.is_active = False
                    trip.detention_stop_event = car_event
                    if not trip.detention_start_event:
                        trip.detention_start_event = trip.events.filter(
                            is_invalid=False, event_code_id="D"
                        ).first()
                    trip.calc_detention()

                    try:
                        trip.save()
                    except IntegrityError:
                        logger.info(
                            f"Event {car_event.id} on trip {trip.id} is already a detention stop event for trip {trip.id}"
                        )

                    previous_trip = trip
                    trip = None

                if not trip:
                    trip = Trip.objects.create(
                        car=car,
                        company=company,
                        is_active=False,
                        departure_timestamp=car_event.event_timestamp,
                        last_car_event=car_event,
                        previous_trip=previous_trip,
                    )

                # calc time and distance between events
                if trip.last_car_event:
                    last_event = trip.last_car_event
                    elapsed_time = (
                        car_event.event_timestamp - last_event.event_timestamp
                    )

                    distance = RailStationDistance.get_distance(
                        last_event.location, car_event.location
                    )

                    car_event.distance_since_last_event = distance
                    car_event.time_since_last_event = elapsed_time
                    trip.actual_distance += distance


                car_event.trip = trip
                car_event.save()

                trip.last_car_event = car_event

                # match trip with waybill
                if car_event.waybill:
                    trip.waybills.add(car_event.waybill)
                    shipment_car = (
                        car_event.waybill.shipment_car
                        if hasattr(car_event.waybill, "shipment_car")
                        else None
                    )
                    if shipment_car:
                        schedule = shipment_car.rail_shipment.schedule
                        trip.pattern = schedule
                        if schedule:
                            contract = schedule.contract
                            trip.buyer_contract = (
                                schedule
                                if contract and contract.buyer_number
                                else None
                            )
                            trip.seller_contract = (
                                schedule
                                if contract and contract.seller_number
                                else None
                            )

                if (
                    car_event.loaded
                    or car.fleet_status == RailCar.FLEET_STATUS.permanent
                ):
                    trip.is_active = True

                if car_event.event_code_id == "Z":
                    trip.ap_timestamp = car_event.event_timestamp
                if car_event.event_code_id == "Y":
                    trip.cp_timestamp = car_event.event_timestamp
                if car_event.event_code_id == "W":
                    trip.release_timestamp = car_event.event_timestamp
                trip.eta_timestamp = car_event.delivery_timestamp or trip.eta_timestamp
                trip.bol_timestamp = car_event.bol_timestamp or trip.bol_timestamp
                trip.original_eta_timestamp = (
                    trip.original_eta_timestamp or trip.eta_timestamp
                )

                trip.destination_id = car_event.destination_id or trip.destination_id
                trip.stcc = car_event.stcc or trip.stcc
                trip.careof = car_event.care_of_name or trip.careof
                trip.consignee = car_event.consignee_name or trip.consignee
                trip.shipper = car_event.shipper_name or trip.shipper
                trip.bol_number = car_event.bol or trip.bol_number
                trip.origin_id = car_event.origin_id or trip.origin_id
                trip.volume = car_event.volume or trip.volume

                # calculate detention on the the latest arrival event
                if (
                    car_event.event_code_id in Trip.ARRIVAL_EVENTS
                    and not trip.arrival_timestamp
                ):
                    trip.detention_start_event = car_event
                    trip.arrival_timestamp = car_event.event_timestamp
                    trip.calc_detention()

            logger.info("company=%d car=%d event_count=%d", company.id, car.id, event_count)
            if trip:
                trip.save()

                if trip.arrival_timestamp:
                    waybill = trip.waybills.filter(shipment_car__isnull=False).first()
                    if waybill:
                        shipment = waybill.shipment_car.rail_shipment
                        shipment.arrive_date = (
                            trip.arrival_timestamp
                        )
                        shipment.save(update_fields=["arrive_date"])

                        if shipment.master_billing and not shipment.master_billing.arrive_date:
                            shipment.master_billing.arrive_date = trip.arrival_timestamp
                            shipment.master_billing.save(update_fields=["arrive_date"])
