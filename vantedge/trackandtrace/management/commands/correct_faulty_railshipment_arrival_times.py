import logging

from django.core.management.base import BaseCommand
from django.utils import timezone

from vantedge.wheelhouse.models import RailShipment

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = """This command designed to correct rail shipment arrival times that set
    wrong due to faulty event data
    """

    def add_arguments(self, parser):
        parser.add_argument(
            "--dry-run",
            action="store_true",
            help="just print, no writing",
        )

    def handle(self, *args, **options):
        dry_run = options["dry_run"]

        for shipment in RailShipment.objects.filter(arrive_date__isnull=False):
            car = shipment.cars.filter(waybill__isnull=False).first()
            if car:
                waybill = car.waybill
                if waybill.trip is None:
                    logger.info('condition: no trip: shipment="%s" car="%s"', shipment, car)
                    if not dry_run:
                        shipment.arrive_date = None
                        shipment.save(update_fields=["arrive_date"])
                    continue

                if not waybill.trip.arrival_timestamp:
                    logger.info('condition: no arrival timestamp shipment="%s" car="%s"', shipment, car)
                    if not dry_run:
                        shipment.arrive_date = None
                        shipment.save(update_fields=["arrive_date"])
                    continue

                trip_arrival_date = timezone.localtime(waybill.trip.arrival_timestamp).date()
                if trip_arrival_date != shipment.arrive_date:
                    logger.info(
                        'condition: arrival timestamp mismatch: shipment="%s" car="%s" ship_date=%s  current=%s  correct=%s',
                        shipment,
                        car,
                        shipment.ship_date,
                        shipment.arrive_date,
                        trip_arrival_date,
                    )
                    if not dry_run:
                        shipment.arrive_date = waybill.trip.arrival_timestamp
                        shipment.save(update_fields=["arrive_date"])

