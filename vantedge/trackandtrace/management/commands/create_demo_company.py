from faker import Faker
import string
import random
from datetime import timedelta

from django.core.management.base import BaseCommand
from django.utils import timezone
from django.conf import settings

from vantedge.trackandtrace.models import *
from vantedge.users.models import Company
from vantedge.edi.models import EdiCompany
from vantedge.schedule.models import ScheduleCompany, Slot, Booking
from vantedge.wheelhouse.models import (
    WheelhouseCompany,
    RailShipment,
    Party,
    Facility,
    LoadedRailCar,
    Contract,
    RailShippingSchedule,
    Product,
    Riser,
)
from vantedge.terminalboss.models import (
    TerminalBossCompany,
    SiteUser,
    ProductTable,
    PersonTable,
    TicketView,
    CompanyTable,
    AllocationTable,
)

RIVERLAND_COMPANY_ID = 21
STEELREEF_COMPANY_ID = 20
TIGER_TANKS_COMPANY_ID = 27


class FakeAttr:
    def __init__(self):
        self.faker = Faker()
        self.locations = dict()

    def fake_attr(self, attr, type_of_item="company"):
        if not attr:
            return attr

        attr = attr.replace(" ", "").replace("-", "_").upper()

        if self.__dict__.get(attr, None) is None:

            if type_of_item == "company":
                value = (
                    self.faker.company()
                    + "-"
                    + "".join(self.faker.random_letters()[0:2])
                )
            elif type_of_item == "number":
                value = self.faker.random_number()
            elif type_of_item == "phone_number":
                value = self.faker.phone_number().replace(".", "-")
            elif type_of_item == "location":
                value = self.faker.city()
            elif type_of_item == "car_mark":
                value = "".join(
                    self.faker.random_choices([n for n in string.ascii_uppercase], 4)
                )
            elif type_of_item == "car_number":
                value = self.faker.random_number(5, fix_len=True)
            elif type_of_item == "name":
                value = self.faker.name()
            elif type_of_item == "code":
                value = "".join(self.faker.random_letters()[0:5])
            elif type_of_item == "fleet":
                value = "".join(self.faker.city())

            if value in self.__dict__.values():
                value += "".join(self.faker.random_letters()[0:3])

            setattr(self, attr, value)

        return self.__dict__.get(attr)

    def fake_location(self, splc):
        created = False
        if self.locations.get(splc, None) is None:
            created = True
            self.locations[splc] = self.faker.city()

        return self.locations.get(splc), created

    def get_facility(self, name):
        try:
            index = list(self.locations.values()).index(name.upper())
            splc = list(self.locations.keys())[index]
        except ValueError:
            return self.fake_attr(name, type_of_item="location")

        railstation = RailStation.objects.filter(splc=splc).exclude(name=name).last()
        if railstation:
            return railstation.name
        else:
            return self.fake_attr(name, type_of_item="location")


faker = FakeAttr()


def fake_trip(trip, car, target_company):
    attrs_to_fake = [
        ("shipper", "company"),
        ("careof", "company"),
        ("freight_payer", "company"),
        ("consignee", "company"),
    ]
    trip.pk = None
    trip.company = target_company
    trip.car = car
    trip.buyer_contract = None
    trip.seller_contract = None
    trip.pattern = None

    trip.origin = fake_railstaion(trip.origin)
    trip.destination = fake_railstaion(trip.destination)

    last_car_event = trip.last_car_event
    trip.last_car_event = None

    detention_start_event = trip.detention_start_event
    trip.detention_start_event = None

    detention_stop_event = trip.detention_stop_event
    trip.detention_stop_event = None

    for attr in attrs_to_fake:
        setattr(trip, attr[0], faker.fake_attr(getattr(trip, attr[0], attr[1])))

    trip.save()
    return trip


def fake_event(event, car, trip, target_company):
    attrs_to_fake = [
        ("carrier_number", "company"),
        ("customer_number", "phone_number"),
        ("shipper_number", "phone_number"),
        ("consignee_name", "company"),
        ("shipper_name", "company"),
        ("care_of_name", "company"),
        ("freightpayer_name", "company"),
    ]
    event.pk = None
    event.trip = trip
    event.car = car
    event.company = target_company
    event.location = fake_railstaion(event.location)
    event.origin = fake_railstaion(event.origin)
    event.destination = fake_railstaion(event.destination)
    for attr in attrs_to_fake:
        setattr(event, attr[0], faker.fake_attr(getattr(event, attr[0], attr[1])))

    event.save()
    event.refresh_from_db()
    return event


def fake_car(car, target_company):
    car.company = target_company
    car.mark = faker.fake_attr(car.mark, "car_mark")
    if not RailCar.objects.filter(company=target_company, mark=car.mark).exists():
        car.pk = None
        car.save()
    else:
        car = RailCar.objects.filter(company=target_company, mark=car.mark).first()
    return car


def fake_railstaion(railstation):
    if railstation is None:
        return None
    name, created = faker.fake_location(railstation.splc)
    if created:
        railstation.name = name
        railstation.pk = None
        railstation.save()
    else:
        railstation = RailStation.objects.filter(
            name=name, splc=railstation.splc
        ).first()
    return railstation


def fake_whole_trip(trip, company):
    events = list(trip.events.order_by("event_timestamp"))

    car = fake_car(trip.car, company)
    faked_trip = fake_trip(trip, car, company)

    detention_start_event = trip.detention_start_event
    detention_stop_event = trip.detention_stop_event

    for event in events:
        if detention_start_event == event:
            detention_start_event = event
        if detention_stop_event == event:
            detention_stop_event == event

        faked_event = fake_event(event, car, faked_trip, company)

        if detention_start_event == fake_event:
            faked_trip.detention_start_event = faked_event
        if detention_stop_event == fake_event:
            faked_trip.detention_start_event = faked_event

    if random.choice((True, False)):
        faked_event.event_timestamp = timezone.now()
        faked_event.save()
    faked_trip.last_car_event = faked_event
    faked_trip.save()

    return faked_trip


def get_trips():
    trips = Trip.objects.filter(
        is_active=True,
        company__company=RIVERLAND_COMPANY_ID,
        origin__isnull=False,
        destination__isnull=False,
    ).order_by("-id")[0:100]
    return trips


def delete_trips(company_id):
    Trip.objects.filter(company=company_id).delete()
    CarEvent.objects.filter(company=company_id).delete()


# main function
def create_fake_trips(company_id):
    company = get_company(company_id)
    track_and_trace_company = company.track_trace
    delete_trips(track_and_trace_company)
    trips = get_trips()
    print("maximum trips to replicate:", len(trips))
    for i, trip in enumerate(list(trips)):
        print(f"replicating trip {i}", end="\r")
        fake_whole_trip(trip, track_and_trace_company)


def create_fake_waybills(company_id):
    demo_company = get_company(company_id)
    print("Delete old waybills...")
    Waybill.objects.filter(company__company=demo_company).delete()
    waybills = Waybill.objects.filter(company__company=RIVERLAND_COMPANY_ID).order_by(
        "-id"
    )[0:150]
    for i, waybill in enumerate(waybills):
        print(f"replicating waybill {i}", end="\r")
        waybill.pk = None
        waybill.car = fake_car(waybill.car, demo_company.track_trace)
        waybill.company = demo_company.track_trace
        waybill.edi = None
        waybill.trip = None

        if waybill.carrier_number:
            waybill.carrier_number = faker.fake_attr(waybill.carrier_number, "code")
        if waybill.customer_number:
            waybill.customer_number = faker.fake_attr(waybill.customer_number, "code")
        if waybill.shipper_number:
            waybill.shipper_number = faker.fake_attr(waybill.shipper_number, "code")

        if waybill.care_of_name:
            waybill.care_of_name = faker.fake_attr(waybill.care_of_name)
        if waybill.consignee_name:
            waybill.consignee_name = faker.fake_attr(waybill.consignee_name)
        if waybill.shipper_name:
            waybill.shipper_name = faker.fake_attr(waybill.shipper_name)
        if waybill.freight_payer_name:
            waybill.freight_payer_name = faker.fake_attr(waybill.freight_payer_name)

        waybill.origin = fake_railstaion(waybill.origin)
        waybill.destination = fake_railstaion(waybill.destination)

        waybill.source_data = None
        waybill.save()
    print("")


def create_fake_fleet(company_id):
    company = get_company(company_id)
    track_and_trace_company = company.track_trace

    RailCar.objects.filter(
        id__in=Trip.objects.filter(company=track_and_trace_company).values_list(
            "car", flat=True
        )
    ).delete()
    railcars = list(
        RailCar.objects.filter(lease_to_off_date__isnull=False)
        .exclude(fleet="", fleet__isnull=True)
        .order_by("?")[0:100]
    )
    for i, railcar in enumerate(railcars):
        print(f"replicating fleet {i}", end="\r")
        railcar.pk = None
        if railcar.home:
            railcar.home = faker.get_facility(railcar.home)
        railcar.company = track_and_trace_company
        railcar.mark = faker.fake_attr(railcar.mark, "car_mark")
        if not RailCar.objects.filter(
            mark=railcar.mark, company=track_and_trace_company
        ).exists():
            railcar.save()


def create_fake_contracts(company_id):
    company = get_company(company_id)
    wheelhouse_company = company.wheelhouse

    old_contracts = Contract.objects.filter(
        Q(buyer__company=wheelhouse_company) | Q(seller__company=wheelhouse_company)
    )
    RailShippingSchedule.objects.filter(
        Q(purchase_contract__in=old_contracts) | Q(sale_contract__in=old_contracts)
    ).delete()
    old_contracts.delete()

    contracts = Contract.objects.filter(
        Q(buyer__company__company=RIVERLAND_COMPANY_ID)
        | Q(seller__company__company=RIVERLAND_COMPANY_ID)
    ).filter(start_date__gt=timezone.now() - timedelta(days=32))

    parties = [
        field.name
        for field in RailShippingSchedule._meta.fields
        if field.related_model is Party
    ]

    for contract in contracts:
        patterns = list(
            RailShippingSchedule.objects.filter(
                Q(purchase_contract=contract) | Q(sale_contract=contract)
            )
        )

        contract.id = None
        if contract.product:
            contract.product = Product.objects.get(
                name=contract.product.name, company=wheelhouse_company
            )
        if contract.buyer:
            contract.buyer = Party.objects.get(
                company=wheelhouse_company, name=faker.fake_attr(contract.buyer.name)
            )
        if contract.seller:
            contract.seller = Party.objects.get(
                company=wheelhouse_company, name=faker.fake_attr(contract.seller.name)
            )
        fleets = contract.fleet
        f = []
        for name in fleets:
            f.append(faker.fake_attr(name, "fleet"))
        contract.fleet = f
        contract.save()
        contract.refresh_from_db()

        for pattern in patterns:
            pattern.pk = None

            if pattern.purchase_contract:
                pattern.purchase_contract = contract
            if pattern.sale_contract:
                pattern.sale_contract = contract

            if pattern.origin:
                pattern.origin = Facility.objects.get(
                    company=wheelhouse_company,
                    name=faker.get_facility(pattern.origin.name),
                )
            if pattern.destination:
                pattern.destination = Facility.objects.get(
                    company=wheelhouse_company,
                    name=faker.get_facility(pattern.destination.name),
                )
            for party in parties:
                if getattr(pattern, party):
                    setattr(
                        pattern,
                        party,
                        Party.objects.get(
                            company=wheelhouse_company,
                            name=faker.fake_attr(getattr(pattern, party).name),
                        ),
                    )
            pattern.name = " ".join(faker.faker.words())
            pattern.save()


def create_fake_railshipments(company_id):
    company = get_company(company_id)
    shipment_parties = [
        field.name
        for field in RailShipment._meta.fields
        if field.related_model is Party
    ]
    shipments = RailShipment.objects.filter(
        origin__company__company=RIVERLAND_COMPANY_ID
    ).order_by("-id")[0:50]
    for shipment in shipments:
        railcars = list(shipment.cars.all())
        shipment.pk = None
        shipment.product = Product.objects.get(
            name=shipment.product.name, company__company=company
        )
        for party in shipment_parties:
            if getattr(shipment, party):
                setattr(
                    shipment,
                    party,
                    Party.objects.get(
                        company__company=company,
                        name=faker.fake_attr(getattr(shipment, party).name),
                    ),
                )
        shipment.origin = Facility.objects.get(
            company__company=company, name=faker.get_facility(shipment.origin.name)
        )
        shipment.destination = Facility.objects.get(
            company__company=company, name=faker.get_facility(shipment.destination.name)
        )
        shipment.save()
        shipment.refresh_from_db()
        for car in railcars:
            car.id = None
            car.rail_shipment = shipment
            car.product = shipment.product
            car.current_location = shipment.origin
            car.car_mark = faker.fake_attr(car.car_mark, "car_mark")
            car.waybill = None
            car.save()


def create_entities(company_id):
    company = get_company(company_id)
    wheelhouse_company = company.wheelhouse
    print("wheelhouse_company", wheelhouse_company)
    old_contracts = Contract.objects.filter(
        Q(buyer__company=wheelhouse_company) | Q(seller__company=wheelhouse_company)
    )

    RailShippingSchedule.objects.filter(origin__company=wheelhouse_company).delete()
    old_contracts.delete()

    LoadedRailCar.objects.filter(current_location__company=wheelhouse_company).delete()
    RailShipment.objects.filter(origin__company=wheelhouse_company).delete()
    Party.objects.filter(company=wheelhouse_company).delete()
    Slot.objects.filter(
        location__in=Riser.objects.filter(company=wheelhouse_company)
    ).delete()
    Riser.objects.filter(company=wheelhouse_company).delete()
    Facility.objects.filter(company=wheelhouse_company).delete()
    Product.objects.filter(company=wheelhouse_company).delete()

    for party in Party.objects.filter(company__company=RIVERLAND_COMPANY_ID):
        i = party.id
        party.pk = None
        party.company = wheelhouse_company
        party.name = faker.fake_attr(party.name)
        party.save()
        if Party.objects.filter(name=party.name).count() > 1:
            party.name = party.name + str(i)
            party.save()

    for facility in Facility.objects.filter(company__company=RIVERLAND_COMPANY_ID):
        f = Facility(company=wheelhouse_company, name=faker.get_facility(facility.name))
        f.save()
        for railroad in facility.railroads.all():
            f.railroads.add(railroad)
        f.name = faker.get_facility(facility.name)

    for product in Product.objects.filter(company__company=RIVERLAND_COMPANY_ID):
        product.id = None
        product.company = wheelhouse_company
        product.save()


def create_fake_tboss_v2(company_id):
    demo_company = get_company(None)
    TerminalBossCompany.objects.filter(company=STEELREEF_COMPANY_ID).update(
        company=demo_company, name=faker.faker.name()
    )

    for site in SiteUser.objects.filter(company__company=demo_company).all():
        site.username = faker.faker.city()
        site.save()

    for company in CompanyTable.objects.filter(company__company=demo_company):
        company.company_code = faker.fake_attr(company.company_code)
        company.name = faker.fake_attr(company.name)
        company.save()

    for person in PersonTable.objects.filter(company__company=demo_company):
        person.first_name = faker.faker.first_name()
        person.last_name = faker.faker.last_name()
        person.password = faker.faker.random_number(4, fix_len=True)
        person.save()

    print("starting delete stale tboss v2 tickets....")
    old_tickets = TicketView.objects.filter(
        ticket_open_time__lt=timezone.now() - timedelta(days=45),
        company__company=demo_company,
    )
    print(f"counting....{old_tickets.count()}")
    old_tickets.delete()

    for i, ticket in enumerate(
        TicketView.objects.filter(company__company=demo_company)
    ):
        print(f"updating tboss ticket {i}", end="\r")
        if ticket.carrier_name:
            ticket.carrier_name = faker.fake_attr(ticket.carrier_name)
        if ticket.stockholder_name:
            ticket.stockholder_name = faker.fake_attr(ticket.stockholder_name)
        if ticket.customer_name:
            ticket.customer_name = faker.fake_attr(ticket.customer_name)
        if ticket.account_name:
            ticket.account_name = faker.fake_attr(ticket.account_name)
        if ticket.producer_name:
            ticket.producer_name = faker.fake_attr(ticket.producer_name)
        if ticket.plant_name:
            ticket.plant_name = faker.fake_attr(ticket.plant_name, "location")
        if ticket.location_code:
            ticket.location_code = faker.fake_attr(ticket.location_code, "code")
        if ticket.location_name:
            ticket.location_name = faker.fake_attr(ticket.location_name, "location")

        ticket.save()
    print("")


def create_fake_scheduler(company_id):
    demo_company = get_company(None)
    facility = Facility.objects.filter(company=demo_company.wheelhouse).first()
    party = Party.objects.filter(company=demo_company.wheelhouse).first()
    riser, created = Riser.objects.get_or_create(
        company=demo_company.wheelhouse, name="Riser 1", parent=facility
    )
    carrier_company = Company.objects.get(id=TIGER_TANKS_COMPANY_ID)
    for user in carrier_company.user_set.all():
        user.username = faker.faker.email()
        user.name = faker.faker.name()
        user.email = user.username
        user.telephone = faker.faker.phone_number()
        user.save()

    carrier_name = faker.fake_attr(carrier_company.name)
    print("Tiget tanks company renamed to: ", carrier_name)
    carrier_company.name = carrier_name
    carrier_company.save()
    party.wheelhouse_company = carrier_company.wheelhouse
    party.save()
    party.location_set.add(riser)
    Slot.create_slots(
        riser,
        timezone.now(),
        timezone.now() + timedelta(days=2),
        period=timedelta(minutes=45),
    )
    slots = Slot.objects.filter(location=riser, start_date__gt=timezone.now()).order_by(
        "?"
    )[0:10]
    for slot in slots:
        driver = (
            carrier_company.user_set.filter(roles__contains=[User.Role.DRIVER])
            .order_by("?")
            .first()
        )
        Booking.create_booking(
            slot,
            user=driver,
            company=carrier_company.schedule,
            truck=[faker.faker.color_name()],
            truck_volume=None,
            description="fake booking",
            created_by=driver,
        )


def get_company(company_id):
    if company_id is None:
        company, created = Company.objects.get_or_create(name="Demo Company")
        if created:
            print(f"company {company.name} with id {company.id} created!")
        else:
            print(f"company {company.name} with id {company.id} taken!")

    else:
        company = Company.objects.get(id=company_id)

    TrackAndTraceCompany.objects.get_or_create(company=company)

    EdiCompany.objects.get_or_create(
        company=company, defaults={"railinc_fleet": "somethingelse"}
    )
    WheelhouseCompany.objects.get_or_create(company=company)
    ScheduleCompany.objects.get_or_create(company=company)

    return company


class Command(BaseCommand):
    help = "Create Trips for demo"

    def handle(self, *args, **options):
        if settings.DEBUG is False:
            print("Is this production?!")
            return

        create_fake_fleet(None)
        create_fake_trips(None)
        create_fake_waybills(None)
        create_entities(None)
        create_fake_railshipments(None)
        create_fake_contracts(None)
        create_fake_tboss_v2(None)
        create_fake_scheduler(None)
