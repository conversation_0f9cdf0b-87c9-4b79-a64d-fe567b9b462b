from faker import Faker
import string

from django.core.management.base import BaseCommand
from django.utils import timezone

from vantedge.trackandtrace.models import *
from vantedge.users.models import Company
from vantedge.edi.models import EdiCompany
from vantedge.wheelhouse.models import WheelhouseCompany


class FakeAttr:

    ATTR_TO_FAKE = {
        "wb": {"type_of": "number", "alias": ["bol"]},
        "carrier_number": {"type_of": "phone_number", "alias": []},
        "customer_number": {"type_of": "phone_number", "alias": []},
        "shipper_number": {"type_of": "phone_number", "alias": []},
        "volume": {"type_of": "number", "alias": ["weight"]},
        "shipper_name": {"type_of": "company", "alias": ["shipper"]},
        "care_of_name": {"type_of": "company", "alias": ["careof"]},
        "freightpayer_name": {"type_of": "company", "alias": ["freight_payer"]},
        "consignee_name": {"type_of": "company", "alias": ["consignee"]},
        "car_mark": {"type_of": "car_mark", "alias": []},
    }

    def __init__(self):
        self.faker = Faker()
        self.locations = dict()

    def get_attr(self, attr):
        if self.ATTR_TO_FAKE.get(attr):
            return attr
        else:
            for key, value in self.ATTR_TO_FAKE.items():
                if attr in value["alias"]:
                    return key

        return attr

    def get_type_of(self, attr):
        if self.ATTR_TO_FAKE.get(attr):
            return self.ATTR_TO_FAKE[attr]["type_of"]
        return "company"

    def __getattr__(self, attr: str):
        attr = self.get_attr(attr)
        if self.__dict__.get(attr, None) is None:
            type_of_item = self.get_type_of(attr)

            if type_of_item == "company":
                value = self.faker.company()
            elif type_of_item == "number":
                value = self.faker.random_number()
            elif type_of_item == "phone_number":
                value = self.faker.phone_number().replace(".", "-")
            elif type_of_item == "location":
                value = self.faker.city()
            elif type_of_item == "car_mark":
                value = "".join(
                    self.faker.random_choices([n for n in string.ascii_uppercase], 4)
                )

            setattr(self, attr, value)
        return self.__dict__.get(attr)

    def fake_location(self, splc):
        created = False
        if self.locations.get(splc, None) is None:
            created = True
            self.locations[splc] = self.faker.city()

        return self.locations.get(splc), created


def fake_trip(trip, car, target_company, faker):
    attrs_to_fake = ["shipper", "careof", "freight_payer", "consignee", "wb"]

    trip.pk = None
    trip.company = target_company
    trip.car = car
    trip.buyer_contract = None
    trip.seller_contract = None
    trip.pattern = None

    trip.origin = fake_railstaion(trip.origin, faker)
    trip.destination = fake_railstaion(trip.destination, faker)

    last_car_event = trip.last_car_event
    trip.last_car_event = None

    detention_start_event = trip.detention_start_event
    trip.detention_start_event = None

    detention_stop_event = trip.detention_stop_event
    trip.detention_stop_event = None

    for attr in attrs_to_fake:
        if getattr(trip, attr, None):
            setattr(trip, attr, getattr(faker, attr))

    trip.save()
    return trip


def fake_event(event, car, trip, target_company, faker):
    attrs_to_fake = [
        "wb",
        "bol",
        "carrier_number",
        "customer_number",
        "shipper_number",
        "consignee_name",
        "shipper_name",
        "care_of_name",
        "freightpayer_name",
        "weight",
        "volume",
    ]
    event.pk = None
    event.trip = trip
    event.car = car
    event.company = target_company
    event.location = fake_railstaion(event.location, faker)
    for attr in attrs_to_fake:
        if getattr(event, attr, None):
            setattr(event, attr, getattr(faker, attr))
    event.save()
    return event


def fake_car(car, target_company, faker):
    car.pk = None
    car.company = target_company
    car.mark = faker.car_mark

    car.save()
    return car


def fake_railstaion(railstation, faker):
    if railstation is None:
        return None
    railstation.name, created = faker.fake_location(railstation.splc)
    if created:
        railstation.pk = None
    railstation.save()
    return railstation


def fake_whole_trip(trip, company):
    faker = FakeAttr()
    events = list(trip.events.order_by("event_timestamp"))

    car = fake_car(trip.car, company, faker)
    faked_trip = fake_trip(trip, car, company, faker)

    for event in events:
        faked_event = fake_event(event, car, faked_trip, company, faker)

    faked_trip.last_car_event = faked_event
    faked_trip.save()

    return faked_trip


def get_random_trip():
    for i in range(1000):
        trip = Trip.objects.filter(is_active=True).order_by("?").first()
        if not trip.events.first().loaded:
            continue
        if (timezone.now() - trip.events.first().event_timestamp).days > 3:
            continue
        if trip.events.count() > 7:
            return trip


# main function
def create_fake_trips(count, company_id=None):
    track_and_trace_company = get_company(company_id)
    for i in range(count):
        trip = get_random_trip()
        print(f"making a copy of trip id {trip.id} ....")
        fake_whole_trip(trip, track_and_trace_company)


def get_company(company_id):
    if company_id is None:
        company = Company(name=Faker().company())
        company.save()
        print(f"company {company.name} with id {company.id} created!")
    else:
        company = Company.objects.get(id=company_id)

    track_and_trace_company, created = TrackAndTraceCompany.objects.get_or_create(
        company=company
    )
    EdiCompany.objects.get_or_create(
        company=company, defaults={"railinc_fleet": "somethingelse"}
    )
    WheelhouseCompany.objects.get_or_create(company=company)
    return track_and_trace_company


class Command(BaseCommand):
    help = "Create Trips for demo"

    def add_arguments(self, parser):
        parser.add_argument("count", nargs="?", type=int)
        parser.add_argument("company", nargs="?", type=int)

    def handle(self, *args, **options):
        create_fake_trips(options["count"], company_id=options.get("company"))
