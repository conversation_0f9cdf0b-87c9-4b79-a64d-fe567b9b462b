from datetime import timedelta
from django.conf import settings
from django.core.mail import EmailMessage
from django.core.management.base import BaseCommand
from django_q.tasks import async_task
from django.template.loader import render_to_string
from django.utils import timezone
from vantedge.trackandtrace.models import TrackAndTraceCompany, Trip
import logging, os

logger = logging.getLogger(__name__)


def send_daily_activity_report(company_id, today):
    yesterday = today - timedelta(days=1)
    company = TrackAndTraceCompany.objects.get(id=company_id)
    try:
        trips = Trip.objects.filter(
            departure_timestamp__gte=yesterday,
            departure_timestamp__lt=today,
            company=company,
        )

        receipients = company.activity_report_emails
        trip_list = [trip for trip in trips]

        template = "email_templates/activity.html"
        html = render_to_string(
            template,
            {
                "company": company.company.name,
                "trips": trip_list,
                "title": "Daily Activity Report",
                "date": timezone.localdate().strftime("%b. %d, %Y"),
                "base_media_url": settings.BASE_MEDIA_URL,
            },
        )

        email = EmailMessage(
            subject="Wheelhouse Daily Activity Report",
            body=html,
            to=receipients,
        )
        email.content_subtype = "html"

        for trip in trips:
            for attachment in trip.attachments.filter(deleted=False):
                # attachments have no mime_types, making an assumption that it is a pdf
                try:
                    email.attach(
                        os.path.basename(attachment.file.name),
                        attachment.file.read(),
                        "application/pdf",
                    )
                except FileNotFoundError:
                    print(f"Failed to attach {attachment.file.name}")

        email.send()
        print(f"Sent Daily Activity Report to - [{company}]")

    except Exception as e:
        logger.exception(f"Generate Daily Activity Report Failure - [{company}]: [{e}]")


class Command(BaseCommand):
    help = "Sends out daily emails for Shipped Cars."

    def handle(self, *args, **options):
        self.generate_emails()

    def generate_emails(self):
        print("Processing daily activity reports")
        # get trips between
        # yesterday midnight <= departure_timestamp < today midnight
        today = timezone.localtime()
        today = today.replace(hour=0, minute=0, second=0, microsecond=0)

        for company in TrackAndTraceCompany.objects.select_related("company").exclude(
            activity_report_emails__len=0
        ):
            async_task(
                send_daily_activity_report,
                company.id,
                today,
                group="Daily Shipped Cars",
                task_name=f"{company} Daily Shipped Cars",
            )
