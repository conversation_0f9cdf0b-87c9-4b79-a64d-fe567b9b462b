from django.core.management.base import BaseCommand, CommandError
from vantedge.users.models import User
from vantedge.trackandtrace.calc import detention
from datetime import datetime


class Command(BaseCommand):
    help = "Calculate Detention"

    def add_arguments(self, parser):
        parser.add_argument("tracktrace_company_id", nargs="?", type=int)
        parser.add_argument("start_date", nargs="?")

    def handle(self, *args, **options):
        try:
            start_date = datetime.strptime(options.get("start_date") or "", "%Y-%m-%d")
        except ValueError:
            start_date = None
        detention(
            tracktrace_company_id=options.get("tracktrace_company_id", None),
            start_date=start_date,
        )
