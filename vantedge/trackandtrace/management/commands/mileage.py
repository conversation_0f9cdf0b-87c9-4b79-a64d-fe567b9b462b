from django.core.management.base import BaseCommand
from vantedge.trackandtrace.models.mileage import RailStationDistance
from vantedge.trackandtrace.models import Trip, CarEvent
from more_itertools import windowed
from progressbar import progressbar


class Command(BaseCommand):
    help = "Calculate Distance and Time Since Last Event for Existing Events"

    def add_arguments(self, parser):
        parser.add_argument("company_id", nargs="?", type=int)
        parser.add_argument(
            "-overwrite",
            type=bool,
            nargs="?",
            help="Over write previouse values",
            default=False,
        )

    def handle(self, *args, **options):
        trips = Trip.objects.all()
        car_event_updates = []
        if options.get("company_id"):
            trips = trips.filter(company=options.get("company_id"))
        for trip in progressbar(list(trips.iterator())):
            for car_events in windowed(
                trip.events.select_related("location")
                .filter(location__isnull=False)
                .order_by("event_timestamp"),
                2,
            ):
                if car_events[1] is None or (
                    car_events[1].distance_since_last_event
                    and not options.get("overwrite")
                ):
                    continue

                car_events[1].time_since_last_event = (
                    car_events[1].event_timestamp - car_events[0].event_timestamp
                )
                car_events[
                    1
                ].distance_since_last_event = RailStationDistance.get_distance(
                    car_events[0].location, car_events[1].location
                )
                if car_events[1]:
                    car_event_updates.append(car_events[1])

        CarEvent.objects.bulk_update(
            car_event_updates,
            ["distance_since_last_event", "time_since_last_event"],
            batch_size=500,
        )
