from django.core.management.base import BaseCommand, CommandError
from vantedge.users.models import User
from vantedge.trackandtrace.models import RailCar
from random import choice, randint
import arrow

class Command(BaseCommand):
    help = 'Seed Cars'

    # def add_arguments(self, parser):
    #     parser.add_argument('poll_ids', nargs='+', type=int)

    def handle(self, *args, **options):
        cars = list(RailCar.objects.all())
        for car in cars:
            dt = arrow.utcnow().shift(months=-20)
            car.car_type = choice(['DOT-105', 'DOT-111', 'DOT-112', 'DOT-211', 'DOT-117'])
            car.contract_number = 'TS12345'
            car.year_built = randint(1990, 2019)
            car.outage_table = 'T11098'
            car.tare = 79100
            car.shell_capacity = 25520
            car.home = choice(['SARNIA', 'Edmonton', 'Conway', 'Mont Belvieu', 'Bakerfields', 'Bath'])
            car.fleet = 'Pressure'
            car.division = choice(['Butane', 'Propane', 'Oil'])
            car.pool = 'CN1234'
            car.monthly_cost = 950
            car.lessor = choice(['Trinity', 'GATX'])
            car.on_lease = dt.date()
            car.off_lease = dt.shift(months=120).date()
            car.term = 84
        # RailCar.objects.bulk_update(cars, ['car_type', 'contract_number', 'year_built', 'outage_table', 'tare', 'shell_capacity' 'home','fleet','division', 'pool', 'monthly_cost'])
        RailCar.objects.bulk_update(cars, [field.name for field in RailCar._meta.get_fields() if field.name != 'id' and field.concrete])
