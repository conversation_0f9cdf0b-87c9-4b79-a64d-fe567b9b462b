# Generated by Django 3.1.4 on 2021-01-25 23:18

import computedfields.resolver
import datetime
import dirtyfields.dirtyfields
import django.contrib.gis.db.models.fields
import django.contrib.postgres.fields
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='CarEvent',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('event_timestamp', models.DateTimeField(blank=True, null=True)),
                ('delivery_timestamp', models.DateTimeField(blank=True, null=True)),
                ('arrival_timestamp', models.DateTimeField(blank=True, null=True)),
                ('schedule_timestamp', models.DateTimeField(blank=True, null=True)),
                ('time_since_last_event', models.DurationField(default=datetime.timedelta)),
                ('wb', models.Char<PERSON><PERSON>(blank=True, default='', max_length=40, null=True)),
                ('bol', models.CharField(blank=True, default='', max_length=40, null=True)),
                ('carrier_number', models.CharField(blank=True, default='', max_length=40, null=True)),
                ('customer_number', models.CharField(blank=True, default='', max_length=40, null=True)),
                ('shipper_number', models.CharField(blank=True, default='', max_length=40, null=True)),
                ('consignee_name', models.CharField(blank=True, default='', max_length=50, null=True)),
                ('shipper_name', models.CharField(blank=True, default='', max_length=50, null=True)),
                ('careof_name', models.CharField(blank=True, default='', max_length=50, null=True)),
                ('loaded', models.BooleanField(blank=True)),
                ('weight', models.IntegerField(blank=True, default=0, null=True)),
                ('cycle_id', models.IntegerField(blank=True, default=0, null=True)),
                ('trip_seq', models.IntegerField(blank=True, default=0, null=True)),
                ('source_data', models.JSONField(blank=True, null=True)),
                ('train_id', models.CharField(blank=True, default='', max_length=10, null=True)),
                ('reporting_railroad', models.CharField(blank=True, default='', max_length=4, null=True)),
                ('origin_railroad', models.CharField(blank=True, default='', max_length=6, null=True)),
                ('destination_railroad', models.CharField(blank=True, default='', max_length=6, null=True)),
                ('group_key', models.CharField(blank=True, editable=False, max_length=50, null=True)),
            ],
            options={
                'ordering': ['-event_timestamp'],
                'get_latest_by': 'event_timestamp',
            },
            bases=(computedfields.resolver._ComputedFieldsModelBase, models.Model),
        ),
        migrations.CreateModel(
            name='ClmEventCode',
            fields=[
                ('code', models.CharField(blank=True, default='', max_length=1, primary_key=True, serialize=False)),
                ('label', models.CharField(blank=True, default='', max_length=50)),
                ('description', models.CharField(blank=True, default='', max_length=255)),
                ('active', models.BooleanField(default=False)),
            ],
        ),
        migrations.CreateModel(
            name='RailCar',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('mark', models.CharField(blank=True, default='', max_length=4)),
                ('number', models.CharField(blank=True, default='', max_length=6)),
                ('car_type', models.CharField(blank=True, default='', max_length=15, null=True)),
                ('year_built', models.IntegerField(blank=True, null=True)),
                ('outage_table', models.CharField(blank=True, default='', max_length=15, null=True)),
                ('tare', models.IntegerField(blank=True, null=True)),
                ('shell_capacity', models.IntegerField(blank=True, null=True)),
                ('home', models.CharField(blank=True, default='', max_length=15, null=True)),
                ('fleet', models.CharField(blank=True, default='', max_length=15, null=True)),
                ('subfleet', models.CharField(blank=True, default='', max_length=15, null=True)),
                ('division', models.CharField(blank=True, default='', max_length=15, null=True)),
                ('pool', models.CharField(blank=True, default='', max_length=15, null=True)),
                ('lease_from_company', models.CharField(blank=True, default='', max_length=50, null=True)),
                ('lease_from_contract_number', models.CharField(blank=True, default='', max_length=20, null=True)),
                ('lease_from_rider_number', models.CharField(blank=True, default='', max_length=20, null=True)),
                ('lease_from_on_date', models.DateField(blank=True, null=True)),
                ('lease_from_off_date', models.DateField(blank=True, null=True)),
                ('lease_from_monthly_cost', models.IntegerField(blank=True, null=True)),
                ('lease_from_term', models.IntegerField(blank=True, null=True)),
                ('lease_to_company', models.CharField(blank=True, default='', max_length=50, null=True)),
                ('lease_to_contract_number', models.CharField(blank=True, default='', max_length=20, null=True)),
                ('lease_to_rider_number', models.CharField(blank=True, default='', max_length=20, null=True)),
                ('lease_to_on_date', models.DateField(blank=True, null=True)),
                ('lease_to_off_date', models.DateField(blank=True, null=True)),
                ('lease_to_monthly_cost', models.IntegerField(blank=True, null=True)),
                ('lease_to_term', models.IntegerField(blank=True, null=True)),
            ],
        ),
        migrations.CreateModel(
            name='RailCarLease',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('lessor', models.CharField(blank=True, default='', max_length=50, null=True)),
                ('lessee', models.CharField(blank=True, default='', max_length=50, null=True)),
                ('contract_number', models.CharField(blank=True, default='', max_length=20, null=True)),
                ('rider_number', models.CharField(blank=True, default='', max_length=20, null=True)),
                ('on_date', models.DateField(blank=True, null=True)),
                ('off_date', models.DateField(blank=True, null=True)),
                ('monthly_cost', models.IntegerField(blank=True, null=True)),
                ('term', models.IntegerField(blank=True, null=True)),
            ],
        ),
        migrations.CreateModel(
            name='RailStation',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(blank=True, default='', max_length=50, null=True)),
                ('splc', models.CharField(blank=True, default='', max_length=10, null=True)),
                ('fsac', models.CharField(blank=True, default='', max_length=10, null=True)),
                ('erpc', models.CharField(blank=True, default='', max_length=10, null=True)),
                ('r260', models.CharField(blank=True, default='', max_length=10, null=True)),
                ('station_state', models.CharField(blank=True, default='', max_length=40, null=True)),
                ('railroad', models.CharField(blank=True, default='', max_length=10, null=True)),
                ('lonlat', django.contrib.gis.db.models.fields.PointField(srid=4326)),
                ('city', models.CharField(blank=True, default='', max_length=50, null=True)),
                ('state', models.CharField(blank=True, default='', max_length=30, null=True)),
                ('aliases', django.contrib.postgres.fields.ArrayField(base_field=models.CharField(blank=True, max_length=30), default=list, size=None)),
            ],
        ),
        migrations.CreateModel(
            name='Stcc',
            fields=[
                ('stcc', models.CharField(max_length=10, primary_key=True, serialize=False)),
                ('name', models.CharField(blank=True, default='', max_length=50, null=True)),
            ],
        ),
        migrations.CreateModel(
            name='TrackAndTraceCompany',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
            ],
            options={
                'verbose_name_plural': 'Companies',
            },
        ),
        migrations.CreateModel(
            name='Trip',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('shipper', models.CharField(blank=True, default=None, max_length=50, null=True)),
                ('is_active', models.BooleanField(blank=True, default=False)),
                ('departure_timestamp', models.DateTimeField()),
                ('arrival_timestamp', models.DateTimeField(blank=True, null=True)),
                ('eta_timestamp', models.DateTimeField(blank=True, null=True)),
                ('detention_time', models.DurationField(blank=True, default=datetime.timedelta, null=True)),
                ('group_key', models.CharField(blank=True, editable=False, max_length=50, null=True)),
                ('car', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='trips', to='trackandtrace.railcar')),
                ('company', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='trackandtrace.trackandtracecompany')),
                ('destination', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='+', to='trackandtrace.railstation')),
                ('detention_start_event', models.OneToOneField(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to='trackandtrace.carevent')),
                ('detention_stop_event', models.OneToOneField(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to='trackandtrace.carevent')),
                ('last_car_event', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to='trackandtrace.carevent')),
                ('origin', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='+', to='trackandtrace.railstation')),
                ('stcc', models.ForeignKey(blank=True, default=None, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to='trackandtrace.stcc')),
            ],
            bases=(dirtyfields.dirtyfields.DirtyFieldsMixin, computedfields.resolver._ComputedFieldsModelBase, models.Model),
        ),
        migrations.CreateModel(
            name='Waybill',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('timestamp', models.DateTimeField(blank=True, null=True)),
                ('document_timestamp', models.DateTimeField(blank=True, null=True)),
                ('waybill', models.CharField(blank=True, default='', max_length=40, null=True)),
                ('bol', models.CharField(blank=True, default='', max_length=40, null=True)),
                ('carrier_number', models.CharField(blank=True, default='', max_length=40, null=True)),
                ('customer_number', models.CharField(blank=True, default='', max_length=40, null=True)),
                ('shipper_number', models.CharField(blank=True, default='', max_length=40, null=True)),
                ('consignee_name', models.CharField(blank=True, default='', max_length=50, null=True)),
                ('shipper_name', models.CharField(blank=True, default='', max_length=50, null=True)),
                ('source_data', models.JSONField(blank=True, null=True)),
                ('reporting_railroad', models.CharField(blank=True, default='', max_length=6, null=True)),
                ('origin_railroad', models.CharField(blank=True, default='', max_length=6, null=True)),
                ('destination_railroad', models.CharField(blank=True, default='', max_length=6, null=True)),
                ('car', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='waybills', to='trackandtrace.railcar')),
                ('company', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='trackandtrace.trackandtracecompany')),
                ('destination', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='+', to='trackandtrace.railstation')),
                ('origin', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='+', to='trackandtrace.railstation')),
                ('stcc', models.ForeignKey(blank=True, default=None, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to='trackandtrace.stcc')),
                ('trip', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='waybills', to='trackandtrace.trip')),
            ],
            options={
                'get_latest_by': 'timestamp',
            },
            bases=(computedfields.resolver._ComputedFieldsModelBase, models.Model),
        ),
    ]
