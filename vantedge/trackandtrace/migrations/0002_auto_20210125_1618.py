# Generated by Django 3.1.4 on 2021-01-25 23:18

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('trackandtrace', '0001_initial'),
        ('users', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='trackandtracecompany',
            name='company',
            field=models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='track_trace', to='users.company'),
        ),
        migrations.AddField(
            model_name='railcarlease',
            name='car',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='trackandtrace.railcar'),
        ),
        migrations.AddField(
            model_name='carevent',
            name='car',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='events', to='trackandtrace.railcar'),
        ),
        migrations.AddField(
            model_name='carevent',
            name='company',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='trackandtrace.trackandtracecompany'),
        ),
        migrations.AddField(
            model_name='carevent',
            name='destination',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='+', to='trackandtrace.railstation'),
        ),
        migrations.AddField(
            model_name='carevent',
            name='event_code',
            field=models.ForeignKey(on_delete=django.db.models.deletion.DO_NOTHING, to='trackandtrace.clmeventcode'),
        ),
        migrations.AddField(
            model_name='carevent',
            name='location',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='+', to='trackandtrace.railstation'),
        ),
        migrations.AddField(
            model_name='carevent',
            name='origin',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='+', to='trackandtrace.railstation'),
        ),
        migrations.AddField(
            model_name='carevent',
            name='stcc',
            field=models.ForeignKey(blank=True, default=None, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to='trackandtrace.stcc'),
        ),
        migrations.AddField(
            model_name='carevent',
            name='trip',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='events', to='trackandtrace.trip'),
        ),
        migrations.AddIndex(
            model_name='trip',
            index=models.Index(fields=['is_active'], name='trackandtra_is_acti_a27b11_idx'),
        ),
    ]
