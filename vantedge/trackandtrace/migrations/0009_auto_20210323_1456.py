# Generated by Django 3.1.6 on 2021-03-23 20:56

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('trackandtrace', '0008_railcar_project'),
    ]

    operations = [
        migrations.AddField(
            model_name='railcar',
            name='last_contents',
            field=models.CharField(blank=True, default='', max_length=50, null=True),
        ),
        migrations.AlterField(
            model_name='railcar',
            name='division',
            field=models.CharField(blank=True, default='', max_length=50, null=True),
        ),
        migrations.AlterField(
            model_name='railcar',
            name='fleet',
            field=models.CharField(blank=True, default='', max_length=50, null=True),
        ),
        migrations.AlterField(
            model_name='railcar',
            name='home',
            field=models.CharField(blank=True, default='', max_length=50, null=True),
        ),
        migrations.AlterField(
            model_name='railcar',
            name='pool',
            field=models.Char<PERSON>ield(blank=True, default='', max_length=50, null=True),
        ),
        migrations.Alter<PERSON>ield(
            model_name='railcar',
            name='project',
            field=models.Char<PERSON>ield(blank=True, default='', max_length=50, null=True),
        ),
        migrations.AlterField(
            model_name='railcar',
            name='subfleet',
            field=models.CharField(blank=True, default='', max_length=50, null=True),
        ),
    ]
