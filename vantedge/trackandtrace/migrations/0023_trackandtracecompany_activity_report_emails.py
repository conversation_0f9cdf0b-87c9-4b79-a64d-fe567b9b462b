# Generated by Django 3.2.5 on 2021-11-08 23:22

import django.contrib.postgres.fields
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('trackandtrace', '0022_auto_20211107_0112'),
    ]

    operations = [
        migrations.AddField(
            model_name='trackandtracecompany',
            name='activity_report_emails',
            field=django.contrib.postgres.fields.ArrayField(base_field=models.EmailField(max_length=254), blank=True, default=list, help_text='emails subscribed to the daily activity report', null=True, size=None),
        ),
    ]
