# Generated by Django 3.2.5 on 2021-11-18 15:55

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('trackandtrace', '0023_trackandtracecompany_activity_report_emails'),
    ]

    operations = [
        migrations.AlterField(
            model_name='trip',
            name='detention_start_event',
            field=models.OneToOneField(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='detention_start_trip', to='trackandtrace.carevent'),
        ),
        migrations.AlterField(
            model_name='trip',
            name='detention_stop_event',
            field=models.OneToOneField(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='detention_stop_trip', to='trackandtrace.carevent'),
        ),
    ]
