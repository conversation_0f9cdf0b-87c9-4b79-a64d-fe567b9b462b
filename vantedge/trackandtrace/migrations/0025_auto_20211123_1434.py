# Generated by Django 3.2.5 on 2021-11-23 21:34

from django.db import migrations, models
import functools
import schema_field.fields
import vantedge.trackandtrace.detention
import vantedge.trackandtrace.models.other


class Migration(migrations.Migration):

    dependencies = [("trackandtrace", "0024_auto_20211118_0855")]

    operations = [
        migrations.AddField(
            model_name="trackandtracecompany",
            name="data",
            field=schema_field.fields.JSONSchemedField(
                default=vantedge.trackandtrace.models.other.TrackAndTraceCompanyData,
                encoder=functools.partial(
                    schema_field.fields.JSONSchemedEncoder,
                    *(),
                    **{
                        "schema": (
                            vantedge.trackandtrace.models.other.TrackAndTraceCompanyData,
                        )
                    }
                ),
                schema=(vantedge.trackandtrace.models.other.TrackAndTraceCompanyData,),
            ),
        ),
        migrations.AddField(
            model_name="trip", name="detention_cost", field=models.FloatField(default=0)
        ),
        migrations.AddField(
            model_name="trip",
            name="detention_data",
            field=schema_field.fields.JSONSchemedField(
                default=vantedge.trackandtrace.detention.DetentionData,
                encoder=functools.partial(
                    schema_field.fields.JSONSchemedEncoder,
                    *(),
                    **{"schema": (vantedge.trackandtrace.detention.DetentionData,)}
                ),
                schema=(vantedge.trackandtrace.detention.DetentionData,),
            ),
        ),
    ]
