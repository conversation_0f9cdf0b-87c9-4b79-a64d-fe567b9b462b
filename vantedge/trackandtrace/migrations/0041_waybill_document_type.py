# Generated by Django 3.2.7 on 2023-02-16 15:35

from django.db import migrations, models


def add_waybill_cancel_code(apps, schema_editor):
    ClmEventCode = apps.get_model("trackandtrace", "ClmEventCode")
    ClmEventCode.objects.get_or_create(
        code="2",
        label="Waybill Canceled",
        defaults={"description": "Waybill has been canceled by the carrier company."},
    )


class Migration(migrations.Migration):

    dependencies = [("trackandtrace", "0040_waybill_care_of_name")]

    operations = [
        migrations.RunPython(add_waybill_cancel_code),
        migrations.AddField(
            model_name="waybill",
            name="document_type",
            field=models.CharField(
                choices=[("OR", "Original"), ("CO", "Correction"), ("CA", "Cancel")],
                default="OR",
                max_length=20,
            ),
        ),
    ]
