# Generated by Django 4.2 on 2024-02-09 19:32

from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import functools
import model_utils.fields
import schema_field.fields


class Migration(migrations.Migration):

    dependencies = [
        ('trackandtrace', '0047_carevent_railroad_eta_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='RailMovementPrice',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', model_utils.fields.AutoCreatedField(default=django.utils.timezone.now, editable=False, verbose_name='created')),
                ('modified', model_utils.fields.AutoLastModifiedField(default=django.utils.timezone.now, editable=False, verbose_name='modified')),
                ('charge_per_car', models.DecimalField(decimal_places=1, max_digits=6)),
                ('currency', models.CharField(choices=[('USD', 'Usd'), ('CAD', 'Cad')], max_length=3)),
                ('is_loaded', models.BooleanField(default=True)),
                ('is_active', models.BooleanField()),
                ('company', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='trackandtrace.trackandtracecompany')),
                ('destination', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='destinated_price_list', to='trackandtrace.railstation')),
                ('origin', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='originated_price_list', to='trackandtrace.railstation')),
                ('products', models.ManyToManyField(blank=True, to='trackandtrace.stcc')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.AddField(
            model_name='trip',
            name='movement_cost',
            field=schema_field.fields.JSONSchemedField(default=dict, encoder=functools.partial(schema_field.fields.JSONSchemedEncoder, *(), **{'schema': dict}), schema=dict),
        ),
        migrations.CreateModel(
            name='TripPricing',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', model_utils.fields.AutoCreatedField(default=django.utils.timezone.now, editable=False, verbose_name='created')),
                ('modified', model_utils.fields.AutoLastModifiedField(default=django.utils.timezone.now, editable=False, verbose_name='modified')),
                ('charge', models.DecimalField(decimal_places=1, max_digits=6)),
                ('currency', models.CharField(choices=[('USD', 'Usd'), ('CAD', 'Cad')], max_length=3)),
                ('destination', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='destinated_price_items', to='trackandtrace.railstation')),
                ('origin', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='originated_priced_items', to='trackandtrace.railstation')),
                ('price', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='trackandtrace.railmovementprice')),
                ('trip', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='trackandtrace.trip')),
            ],
            options={
                'abstract': False,
            },
        ),
    ]
