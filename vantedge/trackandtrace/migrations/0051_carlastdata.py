# Generated by Django 4.2 on 2024-12-16 16:54

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ("trackandtrace", "0050_rename_railmovementprice_railrate_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="CarLastData",
            fields=[
                (
                    "car",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        primary_key=True,
                        serialize=False,
                        to="trackandtrace.railcar",
                    ),
                ),
            ],
            options={
                "managed": False,
            },
        ),
        migrations.RunSQL(
            """
                CREATE materialized VIEW trackandtrace_carlastdata AS
                (
                    select 
                        DISTINCT ON (trackandtrace_railcar.id)
                        trackandtrace_railcar.id as car_id,
                        trackandtrace_railcar.company_id,
                        last_event_data.last_trip_id,
                        last_event_data.last_car_event_id

                    from trackandtrace_railcar left join
                    (select 
                        trackandtrace_carevent.car_id,
                        trackandtrace_carevent.id as last_car_event_id,
                        trackandtrace_carevent.trip_id as last_trip_id,
                        trackandtrace_carevent.company_id 
                        from
                        trackandtrace_carevent right join
                            (
                            select 
                            trackandtrace_carevent.car_id,
                            MAX(trackandtrace_carevent.event_timestamp) as last_event_timestamp,
                            is_invalid
                            from trackandtrace_carevent
                            group by trackandtrace_carevent.car_id, is_invalid
                            having is_invalid = false
                            )  max_event_table
                        on trackandtrace_carevent.car_id = max_event_table.car_id and max_event_table.last_event_timestamp = trackandtrace_carevent.event_timestamp
                    ) last_event_data
                    on trackandtrace_railcar.id=last_event_data.car_id
                );
                CREATE UNIQUE INDEX unique_car_index ON trackandtrace_carlastdata("car_id");
            """,
            """DROP materialized view trackandtrace_carlastdata;"""
        )
    ]
