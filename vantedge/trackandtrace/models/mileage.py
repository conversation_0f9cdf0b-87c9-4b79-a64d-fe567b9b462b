from django.contrib.gis.db import models
from vantedge.trackandtrace.trimble import Trimble, TrimbleStation


class RailStationDistance(models.Model):
    station1 = models.ForeignKey(
        "trackandtrace.RailStation", on_delete=models.CASCADE, related_name="+"
    )
    station2 = models.ForeignKey(
        "trackandtrace.RailStation", on_delete=models.CASCADE, related_name="+"
    )
    distance = models.FloatField(default=0)

    @classmethod
    def get_distance(cls, station1, station2):
        _station1 = station1
        _station2 = station2
        if station1 is None or station2 is None or station1.id == station2.id:
            return 0
        elif station1.id > station2.id:
            _station1 = station2
            _station2 = station1

        result, created = cls.objects.get_or_create(
            station1_id=_station1.id, station2_id=_station2.id
        )
        if created:
            result.distance = Trimble.mileage_stations(
                [
                    TrimbleStation(
                        Format="SPLC", Name=_station1.splc, Railroad=_station1.railroad
                    ),
                    TrimbleStation(
                        Format="SPLC", Name=_station2.splc, Railroad=_station2.railroad
                    ),
                ]
            )
            result.save()

        return result.distance

    @classmethod
    def test1(cls):
        return Trimble.mileage_stations(
            [
                TrimbleStation(Format="SPLC", Name="191808000", Railroad="CSXT"),
                TrimbleStation(Format="SPLC", Name="047600000", Railroad="CSXT"),
            ]
        )

    @classmethod
    def test2(cls):
        from vantedge.trackandtrace.models import RailStation

        s1 = RailStation.objects.filter(splc="191808000").first()
        s2 = RailStation.objects.filter(splc="047600000").first()
        return cls.get_distance(s1, s2)

