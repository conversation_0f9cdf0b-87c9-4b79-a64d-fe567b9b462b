from decimal import Decimal
from collections.abc import Collection
from django.db import models
from django.db import IntegrityError, transaction
from django.core.exceptions import ValidationError

from model_utils.models import TimeStampedModel

from vantedge.util.mixins import EasyAuditModelConfigMixin
from .other import TrackAndTraceCompany, Stcc, RailStation
from .trip import Trip
from .mileage import RailStationDistance
from .schemas import CostDetail

class CURRENCY_CHOICES(models.TextChoices):
    USD = "USD"
    CAD = "CAD"


class RailRate(EasyAuditModelConfigMixin, TimeStampedModel):
    class FreightChargeChoices(models.TextChoices):
        Prepaid = "Prepaid", "Prepaid"
        Rule11 = "Rule11", "Rule11"

    company = models.ForeignKey(TrackAndTraceCompany, on_delete=models.CASCADE)
    products = models.ManyToManyField(Stcc, blank=True)
    origin = models.ForeignKey(RailStation, related_name="originated_price_list", on_delete=models.PROTECT)
    destination = models.ForeignKey(RailStation, related_name="destinated_price_list", on_delete=models.PROTECT)
    route = models.CharField(max_length=50, blank=True, null=True) #TODO: after going to prod this should become manadatory
    freight_charges = models.CharField(
        choices=FreightChargeChoices.choices, max_length=20, default=FreightChargeChoices.Prepaid)
    charge_per_car = models.DecimalField(max_digits=7, decimal_places=2)
    charge_rule_11 = models.DecimalField(max_digits=7, decimal_places=2, null=True, blank=True)
    surcharge_fuel = models.DecimalField(max_digits=5, decimal_places=3, blank=True, null=True)
    mileage = models.FloatField(blank=True, null=True)
    currency = models.CharField(max_length=3, choices=CURRENCY_CHOICES.choices)
    is_loaded = models.BooleanField(default=True)
    is_active = models.BooleanField()

    def get_related_trips(self):
        return Trip.objects.filter(
            company=self.company,
            is_movement_cost_calculated=True,
            movement_cost__rate_snapshot__id=self.id
        )

    def delete(self, using = ..., keep_parents = ...):
        if self.get_related_trips().exists():
            return
        return super().delete(using, keep_parents)

    def calculate_mileage_origin_destination(self):
        return RailStationDistance.get_distance(
            self.origin, self.destination
        )

    @staticmethod
    def find_rate(trip):
        rates = RailRate.objects.filter(
            is_active=True,
            company=trip.company,
            origin=trip.origin,
            destination=trip.destination,
            is_loaded=trip.last_car_event.loaded,
        )

        rate = rates.filter(products=trip.stcc).first() or rates.filter(products=None).first()
        if rate:
            if RailRate.match_route(rate.route, trip.past_railroads()):
                return rate

        return None

    @staticmethod
    def match_route(route, railroads):
        route_railroads = route.split("/")[::2]
        return route_railroads == railroads

    def rate_snapshot(self):
        data = self.__dict__.copy()
        data.pop("_state")
        return data

    @staticmethod
    def calculate_cost(price_snapshot, **kwargs):

        # it is using price_snapshot in case you need to recaclulate later
        surcharge_fuel_cost = None
        base_charge = price_snapshot["charge_per_car"]
        surcharge_fuel = price_snapshot["surcharge_fuel"]
        charge_rule_11 = price_snapshot["charge_rule_11"]
        mileage = price_snapshot["mileage"]

        if price_snapshot.get("surcharge_fuel"):
            surcharge_fuel_cost = Decimal(mileage) * surcharge_fuel

        total_cost = (
            (surcharge_fuel_cost or 0) +
            base_charge +
            (charge_rule_11 or 0)
        )

        return CostDetail(**{
            "base_charge": dict(amount=base_charge),
            "currency": price_snapshot["currency"],
            "charge_rule_11": dict(amount=charge_rule_11) if charge_rule_11 else None,
            "surcharge_fuel_cost": dict(amount=surcharge_fuel_cost) if surcharge_fuel_cost else None,
            "total_cost": dict(amount=total_cost),
            "mileage": mileage
        })

    def validate_unique(self, exclude: Collection[str] | None = ...) -> None:
        if self.is_active:
            existing_active_pricing = RailRate.objects.filter(
                company=self.company,
                origin=self.origin,
                destination=self.destination,
                is_loaded=self.is_loaded,
                route=self.route,
                is_active=True
            )
            if self.id:
                existing_active_pricing = existing_active_pricing.exclude(id=self.id)

                intersection = set(self.products.all().values_list("stcc", flat=True)).intersection(
                    existing_active_pricing.values_list("products", flat=True)
                )
                if intersection:
                    raise ValidationError(f"These products already have active pricing for same origin destination; {intersection}")

        return super().validate_unique(exclude)

class TripCost(TimeStampedModel):
    trip = models.ForeignKey(Trip, on_delete=models.CASCADE)
    price = models.ForeignKey(RailRate, on_delete=models.SET_NULL, null=True, blank=True)
    origin = models.ForeignKey(RailStation, related_name="originated_priced_items", on_delete=models.PROTECT)
    destination = models.ForeignKey(RailStation, related_name="destinated_price_items", on_delete=models.PROTECT)
    charge = models.DecimalField(max_digits=6, decimal_places=1)
    currency = models.CharField(max_length=3, choices=CURRENCY_CHOICES.choices)


class RailRateMapper:
    """
    m = RailRateMapper()
    if you are using inside calc.py, which is faster:
        m.active_trip_matching_price(trip)
    if you want to calc for trip:
        m.trip_matching_price(trip)
    """

    def __init__(self, company=None):
        self.selected_company = company
        self.companies = []
        self.pricing = dict()
        self.populate_pricing()

    def get_active_prices(self):
        qs = RailRate.objects.filter(
            is_active=True
        )
        if self.selected_company:
            qs.filter(company=self.selected_company)

        return qs.order_by("created")

    def get_key(self, company_id, origin_id, destination_id, is_loaded, route, product_id):
        return (company_id, origin_id, destination_id, is_loaded, route, product_id)

    def populate_price_item(self, movement_price):
        if movement_price.products.all():
            for product in movement_price.products.all():
                key = self.get_key(
                    movement_price.company.id,
                    movement_price.origin.id,
                    movement_price.destination.id,
                    movement_price.is_loaded,
                    movement_price.route,
                    product.stcc)
                self.pricing[key] = movement_price.rate_snapshot()
        else:
            key = self.get_key(
                movement_price.company.id,
                movement_price.origin.id,
                movement_price.destination.id,
                movement_price.is_loaded,
                movement_price.route,
                None)
            self.pricing[key] = movement_price.rate_snapshot()


    def populate_pricing(self):
        prices = self.get_active_prices()

        for movement_price in prices:
            try:
                self.companies.index(movement_price.company.id)
            except ValueError:
                self.companies.append(movement_price.company.id)

            self.populate_price_item(movement_price)

    def company_has_pricing(self, company_id):
        return company_id in self.companies

    def find_price_item(self, company_id, origin_id, destination_id, is_loaded, route, product_id):
        key = self.get_key(company_id, origin_id, destination_id, is_loaded, route, product_id)
        alternate_key = self.get_key(company_id, origin_id, destination_id, is_loaded, route, None)
        return self.pricing.get(key) or self.pricing.get(alternate_key)

    def get_trip_segments(self, trip):
        """
        It should return ((origin, destination, railroad), (origin, destination, railroad),...)
        """
        segments = []
        # [(8463, 'BNSF'), (8491, 'BNSF'), (102, 'BNSF')]
        locations = trip.events.values_list("location", "reporting_railroad").order_by("event_timestamp")

        # at least there should be a movement between 2 locations
        if len(locations) < 2:
            return segments

        origin = locations[0]
        destination = None

        for index, destination in enumerate(locations[1:]):
            # check railroads are different
            if destination[1] != origin[1]:
                segments.append((origin[0], locations[index][0], origin[1]))
                origin = destination

        if destination:
            segments.append((origin[0], destination[0], origin[1]))
        return segments

    def add_trip_pricing(self, trip, match_price):
        # print(f"Assinging movement cost {match_price.charge_per_car} {match_price.currency} from {match_price.origin_id}->{match_price.destination_id} to trip {trip.id}")
        TripCost(
            trip=trip,
            price=match_price,
            destination_id=match_price.destination.id,
            origin_id=match_price.origin.id,
            charge=match_price.charge_per_car,
            currency=match_price.currency
        ).save()
        trip.add_movement_cost(match_price.charge_per_car, match_price.currency)


    def update_trip_cost(self, trip):
        """
            will update trip movement_cost and is_movement_cost_calculated
            returns True if trip has any matching rate price, else False
        """
        match_prices = []
        company = trip.company
        product = trip.stcc

        if not self.company_has_pricing(company.id) or not trip.last_car_event:
            trip.is_movement_cost_calculated = True
            return False

        trip_segments = self.get_trip_segments(trip)

        for trip_segment in trip_segments:
            destination_id = trip_segment[1]
            past_facility_id = trip_segment[0]

            match_price = self.find_price_item(company.id, past_facility_id, destination_id, trip.last_car_event.loaded, product.stcc if product else None)
            if match_price:
                match_prices.append(match_price)

        # delete past trip costs
        trip.movement_cost = {}
        trip.tripcost_set.all().delete()

        try:
            with transaction.atomic():
                for match_price in set(match_prices):
                    self.add_trip_pricing(trip, match_price)

                trip.is_movement_cost_calculated = True
                trip.save(update_fields=["is_movement_cost_calculated", "movement_cost"])
        except IntegrityError:
            print(f"Trip {trip.id} cost calculation failed!")
            return None

        if match_prices:
            return True
        else:
            return False
