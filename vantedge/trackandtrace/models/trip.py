# from django.db import models
from datetime import timedelta
import re

import pydash
from computedfields.models import ComputedFieldsModel
from dateutil.parser import parse, Parser<PERSON>rror
from dirtyfields import DirtyFieldsMixin
from django.contrib.contenttypes.fields import GenericRelation
from django.contrib.gis.db import models
from django.db.models import IntegerField, F
from django.db.models.functions import Cast
from django.utils import timezone


# from vantedge.attachment.models import Attachment
from vantedge.edi.comm.dataclasses import RailWaybillDataclass
from vantedge.wheelhouse.models.schedule import RailShippingSchedule
from schema_field.fields import J<PERSON>NSchemedField
from vantedge.trackandtrace.detention import DetentionData, DetentionCost
from .schemas import MovementCostSchema

from .other import TrackAndTraceCompany, RailCar, Stcc, RailStation
from vantedge.autocomplete.models import AutocompleteMixin
from zoneinfo import ZoneInfo

class Trip(AutocompleteMixin, DirtyFieldsMixin, ComputedFieldsModel):
    class Meta:
        indexes = [models.Index(fields=["is_active"])]
        get_latest_by = "last_car_event__event_timestamp"
        ordering = ["-last_car_event__event_timestamp"]

    company = models.ForeignKey(TrackAndTraceCompany, on_delete=models.CASCADE)
    car = models.ForeignKey(RailCar, on_delete=models.CASCADE)

    origin = models.ForeignKey(
        RailStation, null=True, blank=True, on_delete=models.CASCADE, related_name="+"
    )
    destination = models.ForeignKey(
        RailStation, null=True, blank=True, on_delete=models.CASCADE, related_name="+"
    )
    last_car_event = models.ForeignKey(
        "CarEvent", on_delete=models.SET_NULL, null=True, blank=True, related_name="+"
    )
    previous_trip = models.ForeignKey(
        "self", null=True, blank=True, on_delete=models.SET_NULL
    )
    pattern = models.ForeignKey(
        RailShippingSchedule, on_delete=models.SET_NULL, null=True, blank=True
    )
    # stcc = models.CharField(max_length=10, default=None, blank=True, null=True)
    stcc = models.ForeignKey(
        Stcc, default=None, blank=True, null=True, on_delete=models.SET_NULL
    )
    volume = models.IntegerField(default=0, blank=True, null=True)

    shipper = models.CharField(max_length=1000, default=None, blank=True, null=True)
    careof = models.CharField(max_length=1000, default=None, blank=True, null=True)
    freight_payer = models.CharField(
        max_length=1000, default=None, blank=True, null=True
    )
    consignee = models.CharField(max_length=1000, default=None, blank=True, null=True)

    is_active = models.BooleanField(default=False, blank=True)
    departure_timestamp = models.DateTimeField()
    release_timestamp = models.DateTimeField(blank=True, null=True)
    cp_timestamp = models.DateTimeField(blank=True, null=True)
    ap_timestamp = models.DateTimeField(blank=True, null=True)
    arrival_timestamp = models.DateTimeField(blank=True, null=True)
    original_eta_timestamp = models.DateTimeField(blank=True, null=True)
    eta_timestamp = models.DateTimeField(blank=True, null=True)
    bol_timestamp = models.DateTimeField(blank=True, null=True)

    bol_number = models.CharField(max_length=50, default=None, blank=True, null=True)
    wb = models.CharField(max_length=50, default=None, blank=True, null=True)
    actual_distance = models.FloatField(default=0)

    ARRIVAL_EVENTS = ["Y", "Z"]

    # Detention/Demurrage
    detention_time = models.DurationField(default=timedelta, blank=True, null=True)
    detention_start_event = models.OneToOneField(
        "CarEvent",
        blank=True,
        null=True,
        related_name="detention_start_trip",
        on_delete=models.SET_NULL,
    )
    detention_stop_event = models.OneToOneField(
        "CarEvent",
        blank=True,
        null=True,
        related_name="detention_stop_trip",
        on_delete=models.SET_NULL,
    )
    detention_cost = models.FloatField(default=0)
    detention_data = JSONSchemedField(schema=DetentionData, default=DetentionData)

    # Contracts
    buyer_contract = models.ForeignKey(
        RailShippingSchedule,
        blank=True,
        null=True,
        related_name="buyer_contract",
        on_delete=models.SET_NULL,
    )
    seller_contract = models.ForeignKey(
        RailShippingSchedule,
        blank=True,
        null=True,
        related_name="seller_contract",
        on_delete=models.SET_NULL,
    )
    note = models.TextField(blank=True, null=True)
    movement_cost = JSONSchemedField(schema=MovementCostSchema, default=None, null=True)
    is_movement_cost_calculated = models.BooleanField(null=True, blank=True)
    # attachments = GenericRelation("attachment.Attachment")

    def past_railroads(self):
        railroads = []
        reporting_railroads = self.events.filter(is_invalid=False).values_list("reporting_railroad", flat=True).order_by("id")
        for railroad in reporting_railroads:
            if railroad not in railroads:
                railroads.append(railroad.upper())
        return railroads

    def populate_movement_cost(self):
        from vantedge.trackandtrace.models import RailRate
        railrate = RailRate.find_rate(self)
        if not railrate:
            self.is_movement_cost_calculated = True
            self.save(update_fields=["is_movement_cost_calculated"])
            return

        # initial_snapshot
        snapshot = railrate.rate_snapshot()
        cost_detail = railrate.calculate_cost(snapshot)
        self.movement_cost = MovementCostSchema(rate_snapshot=snapshot, cost_detail=cost_detail)

        self.is_movement_cost_calculated = True
        self.save(update_fields=["movement_cost", "is_movement_cost_calculated"])

    def calc_detention(self):
        self.calc_detention_time()
        self.calc_detention_cost()

    def calc_detention_time(self):
        if self.detention_start_event:
            if self.detention_stop_event:
                self.detention_time = (
                    self.detention_stop_event.event_timestamp
                    - self.detention_start_event.event_timestamp
                )
            else:
                self.detention_time = (
                    timezone.now() - self.detention_start_event.event_timestamp
                )
        else:
            self.detention_time = None

    def calc_detention_cost(self):
        self.detention_data = None

        if self.buyer_contract and self.buyer_contract.purchase_contract:
            self.detention_data = (
                self.buyer_contract.purchase_contract.detention_data.copy()
            )
        elif self.seller_contract and self.seller_contract.sale_contract:
            self.detention_data = (
                self.seller_contract.sale_contract.detention_data.copy()
            )
        elif self.company.data and self.company.data.detention_data:
            self.detention_data = self.company.data.detention_data.copy()
        else:
            self.detention_data = DetentionData()

        if not self.detention_time or self.detention_time.days < 1:
            self.detention_cost = 0
            return

        self.detention_data.detention_scheme = pydash.sort_by(
            self.detention_data.detention_scheme, "days"
        )

        days = int(self.detention_time.days)
        cost = 0
        self.detention_data.detention_cost = []
        for tier in self.detention_data.detention_scheme:
            tier_data = DetentionCost(
                days=tier.days, rate=tier.rate, lay=max(0, days - int(tier.days))
            )
            tier_data.cost = tier_data.lay * tier_data.rate
            cost += tier_data.cost
            days = min(days, int(tier.days))
            self.detention_data.detention_cost.insert(-1, tier_data)
        self.detention_cost = int(cost)

    def __str__(self):
        return f"{self.origin} -> {self.destination}"

    @staticmethod
    def activate_trips(company_id=None):
        trip_qs = Trip.objects.all()
        if company_id:
            trip_qs = trip_qs.filter(company__id=company_id)
        trip_qs.update(is_active=False)

        car_qs = RailCar.objects.exclude(fleet_status=RailCar.FLEET_STATUS.inactive)
        if company_id:
            car_qs = car_qs.filter(company__id=company_id)

        updated_trips = []
        for car in car_qs:
            try:
                print("", end=".")
                trip = car.trip_set.latest()
                trip.is_active = True
                updated_trips.append(trip)
            except Trip.DoesNotExist:
                pass
        Trip.objects.bulk_update(updated_trips, ["is_active"])

        return len(updated_trips)


class Waybill(ComputedFieldsModel):
    class Meta:
        get_latest_by = "document_timestamp"

    class DocumntType(models.TextChoices):
        ORIGINAL = "OR", "Original"
        CORRECTION = "CO", "Correction"
        CANCEL = "CA", "Cancel"

    class EquipmentCode(models.TextChoices):
        BOGIE = "BG", "Bogie"
        CABOOSE = "CA", "Caboose"
        CONTAINER_ON_CHASSIS = "CC", "Container on a Chassis"
        CHASSIS = "CH", "Chassis"
        CONTAINER_OPEN_SIDED = "CM", "Container, Open-Sided"
        CONTAINER = "CN", "Container"
        CONTAINER_TANK = "CX", "Container, Tank"
        REFRIGERATED_CONTAINER = "CZ", "Refrigerated Container"
        GENERATOR_SET = "GS", "Generator Set"
        IDLER_CAR = "ID", "Idler Car"
        LOCOMOTIVE = "LO", "Locomotive"
        HALF_HEIGHT_FLAT_RACK = "LS", "Half Height Flat Rack"
        OPEN_TOP_FLATBED_TRAILER = "OT", "Open-top/flatbed trailer"
        CONTAINER_PLATFORM = "PL", "Container, Platform"
        PROTECTED_TRAILER = "PT", "Protected Trailer"
        RAIL_CAR = "RR", "Rail Car"
        TEMPERATURE_TRAILER = "RT", "Temperature Trailer (Reefer)"
        STACK_CAR = "SK", "Stack Car"
        TRAILER = "TL", "Trailer"
        TANK_CAR = "TN", "Tank Car"

    TANK_CARS = [EquipmentCode.CONTAINER_TANK, EquipmentCode.TANK_CAR]

    company = models.ForeignKey(TrackAndTraceCompany, on_delete=models.CASCADE)
    edi = models.ForeignKey(
        "edi.EdiDocument",
        null=True,
        on_delete=models.SET_NULL,
        related_name="created_waybills",
    )

    trip = models.ForeignKey(
        Trip, on_delete=models.SET_NULL, related_name="waybills", null=True
    )

    # Equipment
    car = models.ForeignKey(RailCar, on_delete=models.CASCADE, related_name="waybills")
    equipment_code = models.CharField(max_length=2, null=True, blank=True)
    weight = models.IntegerField(default=0, blank=True, null=True)

    # Times
    timestamp = models.DateTimeField(blank=True, null=True)
    document_timestamp = models.DateTimeField(blank=True, null=True)

    # Reference Numbers
    waybill = models.CharField(max_length=40, default="", blank=True, null=True)
    bol = models.CharField(max_length=40, default="", blank=True, null=True)
    carrier_number = models.CharField(max_length=40, default="", blank=True, null=True)
    customer_number = models.CharField(max_length=40, default="", blank=True, null=True)
    shipper_number = models.CharField(max_length=40, default="", blank=True, null=True)

    # Names
    care_of_name = models.CharField(max_length=50, default="", blank=True, null=True)
    consignee_name = models.CharField(max_length=50, default="", blank=True, null=True)
    shipper_name = models.CharField(max_length=50, default="", blank=True, null=True)
    freight_payer_name = models.CharField(
        max_length=50, default="", blank=True, null=True
    )

    # Contents
    # stcc = models.CharField(max_length=15, default='', blank=True, null=True)
    stcc = models.ForeignKey(
        Stcc,
        default=None,
        blank=True,
        null=True,
        on_delete=models.SET_NULL,
        related_name="+",
    )

    # Locations
    origin = models.ForeignKey(
        RailStation, null=True, blank=True, on_delete=models.CASCADE, related_name="+"
    )
    destination = models.ForeignKey(
        RailStation, null=True, blank=True, on_delete=models.CASCADE, related_name="+"
    )
    source_data = models.JSONField(blank=True, null=True)

    reporting_railroad = models.CharField(
        max_length=6, default="", blank=True, null=True
    )
    origin_railroad = models.CharField(max_length=6, default="", blank=True, null=True)
    destination_railroad = models.CharField(
        max_length=6, default="", blank=True, null=True
    )
    document_type = models.CharField(
        max_length=20, choices=DocumntType.choices, default=DocumntType.ORIGINAL
    )

    def __str__(self):
        tz = timezone.get_current_timezone()
        return f"{self.car} BOL:{self.bol} {self.origin} -> {self.destination} Issued At: {self.document_timestamp.astimezone(tz):%m-%d %H:%M %Z}"

    def from_dataclass(self, waybill: RailWaybillDataclass):
        if waybill.weight and not waybill.weight.isnumeric():
            waybill.weight = 0
        elif not waybill.weight:
            waybill.weight = 0

        for field_name in map(
            lambda f: f.name if f.name != "event_code" else "event_code_id",
            self._meta.get_fields(),
        ):
            if field_name == "stcc":
                self.stcc, created = Stcc.objects.get_or_create(
                    stcc=waybill.stcc or "None",
                    defaults={
                        "name": waybill.commodity_description or "N/A",
                        "unit": waybill.weight_unit_code or "N/A",
                    },
                )

            else:
                if hasattr(waybill, field_name):
                    setattr(self, field_name, getattr(waybill, field_name))

        try:
            self.car, created = RailCar.objects.get_or_create(
                mark=waybill.car_mark, number=waybill.car_number, company=self.company
            )
        except RailCar.MultipleObjectsReturned:
            raise RailCar.MultipleObjectsReturned(
                f"Duplicate Car {waybill.car_mark} {waybill.car_number}"
            )
        if waybill.origin_splc:
            self.origin = RailStation.get_station_by_splc(
                waybill.origin_splc, waybill.reporting_railroad
            )
        elif waybill.origin_city and waybill.origin_state:
            self.origin = RailStation.get_station_by_city_state(
                waybill.origin_city, waybill.origin_state, waybill.reporting_railroad
            )

        if waybill.destination_splc:
            self.destination = RailStation.get_station_by_splc(
                waybill.destination_splc, waybill.reporting_railroad
            )
        elif waybill.destination_city and waybill.destination_state:
            self.destination = RailStation.get_station_by_city_state(
                waybill.destination_city,
                waybill.destination_state,
                waybill.reporting_railroad,
            )

        # attach timezone to bol_timestamp
        if waybill.document_timestamp:
            # default to server timezone
            tz = timezone.get_current_timezone()

            # get timezone from origin railstation
            if self.origin and self.origin.timezone:
                tz = ZoneInfo(self.origin.timezone)
            try:
                self.document_timestamp = parse(waybill.document_timestamp).replace(
                    tzinfo=tz
                )
            except ParserError as e:
                print(
                    f"Failed to parse document_timestamp with format {waybill.document_timestamp}"
                )
                raise e

    def get_equipment_code(self, update=False):
        if self.equipment_code:
            return self.equipment_code

        if isinstance(self.source_data, dict):
            from vantedge.edi.models import GeoTrip
            self.equipment_code = GeoTrip.get_equipment_code(self.source_data)

        elif self.source_data:
            match = re.search(r"\nN7(.*)", self.source_data)
            n7 = (match.group(1)).split("*")
            if len(n7) >= 11:
                self.equipment_code = n7[11]

        if update:
            if self.equipment_code:
                self.save(update_fields=["equipment_code"])
        return self.equipment_code


    def find_shipment_car(self):
        from vantedge.wheelhouse.models.railcar import LoadedRailCar

        return (
            LoadedRailCar.objects.annotate(
                car_number_int=Cast("car_number", IntegerField())
            )
            .select_related("current_location__company__company")
            .filter(
                waybill=None,
                current_location__company__company=self.company.company,
                rail_shipment__reference_number=self.bol,
                car_mark=self.car.mark,
                car_number_int=int(self.car.number),
            )
            .first()
        )

    def assign_to_shipment_car(self):
        car = self.find_shipment_car()
        if car:
            car.waybill = self
            car.save(update_fields=["waybill"])
