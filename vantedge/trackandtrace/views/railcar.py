import re

from datetime import datetime
from django.apps import apps
from django.db.models import <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>
from django.db.models.functions import Concat
from django_auto_prefetching import AutoPrefetchViewSetMixin
from django_filters import (
    Char<PERSON>ilter,
    FilterSet,
    BaseInFilter,
)
from rest_framework.pagination import LimitOffsetPagination
from rest_framework.decorators import action
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.viewsets import ModelViewSet

from vantedge.util.mixins import BaseViewsetThrottleMixin
from vantedge.edi.comm import RailincEDI
from vantedge.autocomplete.views import AutoCompleteViewSetMixin
from ..models import RailCar, CarLastData
from ..serializers import RailCarSerializer, CarLastDataSerializer


class RailCarFilter(FilterSet):
    name = CharFilter(method="name_filter")
    car = CharFilter(method="car_filter")
    car__in = Char<PERSON>ilter(method="car_filter_in")
    id = CharFilter(method="car_list_filter")
    lease_term = CharFilter(method="lease_term_filter")
    fleet_status__exclude = BaseInFilter(field_name="fleet_status", exclude=True)
    lease_from_company__exclude = BaseInFilter(
        field_name="lease_from_company", exclude=True
    )
    home__exclude = BaseInFilter(field_name="home", exclude=True)
    division__exclude = BaseInFilter(field_name="division", exclude=True)
    fleet__exclude = BaseInFilter(field_name="fleet", exclude=True)
    pool__exclude = BaseInFilter(field_name="pool", exclude=True)
    group__exclude = BaseInFilter(field_name="group", exclude=True)
    subfleet__exclude = BaseInFilter(field_name="subfleet", exclude=True)
    project__exclude = BaseInFilter(field_name="project", exclude=True)
    catr_type__exclude = BaseInFilter(field_name="car_type", exclude=True)
    lease_from_monthly_cost__exclude = BaseInFilter(
        field_name="lease_from_monthly_cost", exclude=True
    )
    last_contents__exclude = BaseInFilter(field_name="last_contents", exclude=True)
    lease_from_on_date = CharFilter(method="date_range_filter")
    lease_from_off_date = CharFilter(method="date_range_filter")
    lease_from_on_date__exclude = CharFilter(method="date_range_filter__exclude")
    lease_from_off_date__exclude = CharFilter(method="date_range_filter__exclude")

    class Meta:
        model = RailCar
        fields = {
            "id": {"exact"},
            "fleet_status": {"in", "exact"},
            "lease_from_company": {"in", "exact"},
            "home": {"in", "exact"},
            "division": {"in", "exact"},
            "fleet": {"in", "exact"},
            "pool": {"in", "exact"},
            "group": {"in", "exact"},
            "subfleet": {"in", "exact"},
            "project": {"in", "exact"},
            "car_type": {"in", "exact"},
            "lease_from_monthly_cost": {"in", "exact"},
            "last_contents": {"in", "exact"},
        }

    def car_list_filter(self, queryset, name, value):
        return queryset.filter(**{name + "__in": value.split(",")})

    def name_filter(self, queryset, name, value):
        queryset = queryset.annotate(car_name=Concat(F("mark"), F("number")))
        return self.car_list_filter(queryset, "car_name", value)

    def lease_term_filter(self, queryset, name, value):
        if value:
            start, end = value.split("_")
            start = datetime.strptime(start, "%Y-%m-%d")
            end = datetime.strptime(end, "%Y-%m-%d")

            queryset = queryset.filter(
                lease_from_off_date__gte=start, lease_from_on_date__lte=end
            )
            #       [- start<lease_off ->     <- end>lease_on -]
            # *-1-*       *---2---*                   *------3-----*

            # Returns *---2---* and *------3-----*
            # Why? Most railcars have lease terms spanning years.
            # The month filter on the front checks by month or by year.
            # qs contains rail cars with lease terms overlapping with the given range

        return queryset

    def car_filter(self, queryset, name, value):
        car_matches = re.compile("([a-zA-Z]*)\s*(\d*)").match(value)
        mark = car_matches.group(1).upper()
        number = car_matches.group(2)
        if mark:
            queryset = queryset.filter(mark__contains=mark)
        if number:
            queryset = queryset.filter(number__contains=number)
        return queryset

    def car_filter_in(self, queryset, name, value):
        values = value.split(",")
        qs = RailCar.objects.none()
        for value in values:
            qs |= self.car_filter(queryset, name, value)
        return qs

    def date_range_filter(self, qs, field_name, value):
        query = Q()
        # single date range
        date_range = value
        start, end = date_range.split("_")

        query |= Q(**{f"{field_name}__range": (start, end)})
        return qs.filter(query)

    def date_range_filter__in(self, qs, field_name, value):
        query = Q()
        # fix field_name
        field_name = field_name.split("__in")[0]

        # break the ranges down
        date_ranges = value.split(",")
        for date_range in date_ranges:
            start, end = date_range.split("_")
            # build query
            query |= Q(**{f"{field_name}__range": (start, end)})
        return qs.filter(query)

    def date_range_filter__exclude(self, qs, field_name, value):
        query = Q()
        # fix field_name
        field_name = field_name.split("__exclude")[0]

        # break the ranges down
        date_ranges = value.split(",")
        for date_range in date_ranges:
            start, end = date_range.split("_")
            # build query
            query |= Q(**{f"{field_name}__range": (start, end)})
        return qs.exclude(query)


class RailCarViewSet(BaseViewsetThrottleMixin, AutoCompleteViewSetMixin, AutoPrefetchViewSetMixin, ModelViewSet):
    serializer_class = RailCarSerializer
    pagination_class = LimitOffsetPagination
    queryset = RailCar.objects.all()
    filterset_fields = {"id": ["exact"]}
    filterset_class = RailCarFilter
    ordering_fields = "__all__"

    throttle_rates = {
        "list": "20/minute",
        "retrieve": "20/minute",
        "create": "10/minute",
        "update": "10/minute",
        "destroy": "10/minute",
        "count": "20/minute",
        "bulk_update": "5/minute",
        "bulk_create": "5/minute",
        "autocomplete": "30/minute",
    }

    def get_queryset(self):
        return (
            super()
            .get_queryset()
            .filter(company=self.request.user.company.track_trace)
        )

    def list(self, request, *args, **kwargs):
        queryset = self.paginate_queryset(self.filter_queryset(self.get_queryset()))

        if not queryset:
            return Response(CarLastDataSerializer(None, many=True).data)

        car_ids = [i.id for i in queryset]
        query_set = CarLastData.objects.filter(car_id__in=car_ids).select_related(
            "car",
            "last_car_event",
            "last_car_event__location",
            "last_car_event__event_code",
            "last_car_event__stcc"
        )
        return Response(CarLastDataSerializer(query_set, many=True).data)

    @action(detail=False, methods=["get"], permission_classes=[IsAuthenticated])
    def count(self, request):
        qs = self.filter_queryset(self.get_queryset())
        return Response(qs.count())

    def perform_update(self, serializer):
        serializer.save()
        if self.request.data.get("fleet_status", "N/A") != "N/A":
            with RailincEDI() as edi:
                edi.update_permanant_fleet(self.request.user.company.edi.railinc_fleet)

    @action(detail=False, methods=["put"], permission_classes=[IsAuthenticated])
    def bulk_update(self, request):
        cars = self.get_queryset().filter(id__in=request.data["cars"])

        # convert empty strings to None
        for key in request.data["updates"].keys():
            request.data["updates"][key] = (
                request.data["updates"][key]
                if request.data["updates"][key] != ""
                else None
            )

        cars.update(**request.data["updates"])
        if (
            self.request.data.get("updates")
            and self.request.data["updates"].get("fleet_status", "N/A") != "N/A"
        ):
            with RailincEDI() as edi:
                edi.update_permanant_fleet(self.request.user.company.edi.railinc_fleet)
        return Response(self.get_serializer_class()(cars, many=True).data)

    @action(detail=False, methods=["post"], permission_classes=[IsAuthenticated])
    def bulk_create(self, request):
        cars = request.data["cars"]
        status = (
            request.data["status"]
            if request.data["status"] in dict(RailCar.FLEET_STATUS).keys()
            else "P"
        )
        new_cars = {}
        update_cars = {}
        for car in cars:
            mark = re.findall(r"\D+", car)
            if mark:
                mark = mark[0]
            else:
                mark = ""
            number = re.findall(r"\d+", car)
            if number:
                number = str(number[0]).rjust(6, "0")
            else:
                number = ""
            car_instance = RailCar.objects.filter(
                company=request.user.company.track_trace, mark=mark, number=number
            ).first()
            if not car_instance:
                new_cars[mark + number] = RailCar(
                    company=request.user.company.track_trace,
                    mark=mark,
                    number=number,
                    fleet_status=status,
                )
            elif car_instance.fleet_status != status:
                car_instance.fleet_status = status
                update_cars[mark + number] = car_instance
            # try:
            #     RailCar.objects.get(
            #         company=request.user.company.track_trace, mark=mark, number=number,
            #     )
            # except RailCar.DoesNotExist:
            #     new_cars[mark + number] = RailCar(
            #         company=request.user.company.track_trace, mark=mark, number=number
            #     )
        new_cars = list(new_cars.values())
        update_cars = list(update_cars.values())
        if new_cars or update_cars:
            new_cars = self.get_queryset().bulk_create(new_cars)
            update_cars_count = self.get_queryset().bulk_update(
                update_cars, ["fleet_status"]
            )
            cars = new_cars + update_cars

            # update permanent fleet if cars imported
            with RailincEDI() as edi:
                edi.update_permanant_fleet(self.request.user.company.edi.railinc_fleet)
        else:
            cars = []
        return Response(self.get_serializer_class()(cars, many=True).data)

    @action(detail=False, methods=["GET"], permission_classes=[IsAuthenticated])
    def autocomplete(self, request):
        suggestions = []
        field_name = self.request.query_params.get("field")
        user_input = self.request.query_params.get("input")
        limit = int(self.request.query_params.get("limit", 15))

        # get list of values for field_name, sorted by most frequent entry first
        if field_name == "car":
            qs = self.filter_queryset(self.get_queryset())

            if user_input and len(user_input):
                car_matches = re.compile("([a-zA-Z]*)\s*(\d*)").match(user_input)
                mark = car_matches.group(1).upper()
                number = car_matches.group(2)
                if mark:
                    qs = qs.filter(mark__contains=mark)
                if number:
                    qs = qs.filter(number__contains=number)

            qs = (
                qs.values_list("mark", "number")
                .annotate(
                    car_reference=Concat(
                        F("mark"), F("number"), output_field=CharField()
                    )
                )
                .annotate(field_count=Count("car_reference"))
                .order_by("-field_count")
                .values_list("car_reference", flat=True)
            )

            count = qs.distinct().count()
            suggestions = qs.distinct()[:limit]

            return Response({"results": suggestions, "count": count})

        else:
            return super().autocomplete(request)
