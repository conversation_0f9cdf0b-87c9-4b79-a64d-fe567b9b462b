from django.db.models import Count

from rest_framework.decorators import action
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.viewsets import ReadOnlyModelViewSet

from ..models import RailStation
from ..serializers import RailStationSerializer

class RailstationViewSet(ReadOnlyModelViewSet):
    queryset = RailStation.objects.all().order_by("name")
    serializer_class = RailStationSerializer
    permission_classes = [IsAuthenticated]

    @action(detail=False, methods=["GET"])
    def railroads(self, request):
        railroads = RailStation.objects.values("railroad").annotate(c=Count("railroad")).order_by("-c")
        return Response([i["railroad"] for i in railroads])

    def list(self, request):
        railroad = request.GET.get("railroad")
        station = request.GET.get("station")

        qs = self.get_queryset().filter(railroad=railroad)
        if station:
            qs = qs.filter(name__icontains=station)
        return Response(RailStationSerializer(qs, many=True).data)