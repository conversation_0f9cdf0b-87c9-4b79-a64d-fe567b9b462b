from django_filters import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, FilterSet
from django.apps import apps
from django.db.models import <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Field, F
from django.db.models.functions import Concat
from django.core.exceptions import ObjectDoesNotExist
from django.shortcuts import get_object_or_404
from rest_framework import viewsets
from rest_framework import mixins, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.pagination import LimitOffsetPagination
from rest_framework.permissions import IsAuthenticated


from ..models import Waybill
from ..serializers import WaybillSerializer, WaybillListSerializer
from vantedge.wheelhouse.models import LoadedRailCar, RailShipmentNote
from vantedge.autocomplete.views import AutoCompleteViewSetMixin

import re


class WaybillFilter(FilterSet):
    car = CharFilter(method="car_filter")
    car__in = CharFilter(method="car_filter_in")

    stcc__stcc__exclude = BaseInFilter(field_name="stcc__stcc", exclude=True)
    bol__exclude = BaseInFilter(field_name="bol", exclude=True)

    stcc__name = CharFilter(method="escape_comma_filter")
    stcc__name__in = CharFilter(method="escape_comma_filter")
    stcc__name__exclude = CharFilter(method="escape_comma_exclude")

    loaded = CharFilter(method="loaded_filter")
    origin__name__exclude = BaseInFilter(field_name="origin__name", exclude=True)
    destination__name__exclude = BaseInFilter(
        field_name="destination__name", exclude=True
    )
    shipper_name__exclude = BaseInFilter(field_name="shipper_name", exclude=True)
    consignee_name__exclude = BaseInFilter(field_name="consignee_name", exclude=True)
    freight_payer_name__exclude = BaseInFilter(
        field_name="freight_payer_name", exclude=True
    )

    # Entity Numbers
    carrier_number__exclude = BaseInFilter(field_name="carrier_number", exclude=True)
    customer_number__exclude = BaseInFilter(field_name="customer_number", exclude=True)
    shipper_number__exclude = BaseInFilter(field_name="shipper_number", exclude=True)

    # Railroads
    reporting_railroad__exclude = BaseInFilter(
        field_name="reporting_railroad", exclude=True
    )
    origin_railroad__exclude = BaseInFilter(field_name="origin_railroad", exclude=True)
    destination_railroad__exclude = BaseInFilter(
        field_name="destination_railroad", exclude=True
    )

    # Dates
    document_timestamp = CharFilter(method="date_range_filter")
    document_timestamp__in = CharFilter(method="date_range_filter__in")
    document_timestamp__exclude = CharFilter(method="date_range_filter__exclude")

    class Meta:
        model = Waybill
        fields = {
            "stcc__stcc": ["in", "exact"],
            "stcc__name": ["in", "exact"],
            "origin__name": ["in", "exact"],
            "destination__name": ["in", "exact"],
            "shipper_name": ["in", "exact"],
            "consignee_name": ["in", "exact"],
            "freight_payer_name": ["in", "exact"],
            "carrier_number": ["in", "exact"],
            "customer_number": ["in", "exact"],
            "shipper_number": ["in", "exact"],
            "reporting_railroad": ["in", "exact"],
            "origin_railroad": ["in", "exact"],
            "destination_railroad": ["in", "exact"],
            "document_type": ["in", "exact"],
            "bol": ["in", "exact"],
        }

    def escape_comma_filter(self, qs, field_name, value):
        query = Q()

        field_name = field_name.split("__in")[0]
        values = value.split(",")

        for value in values:
            query |= Q(**{f"{field_name}": value.replace("%2C", ",")})
        return qs.filter(query)

    def escape_comma_exclude(self, qs, field_name, value):
        query = Q()

        field_name = field_name.split("__exclude")[0]
        values = value.split(",")

        for value in values:
            query |= Q(**{f"{field_name}": value.replace("%2C", ",")})
        return qs.exclude(query)

    def car_filter(self, queryset, name, value):
        car_matches = re.compile("([a-zA-Z]*)\s*(\d*)").match(value)
        mark = car_matches.group(1).upper()
        number = car_matches.group(2).zfill(6)
        car = self.request.user.company.track_trace.railcar_set.filter(
            mark=mark, number=number
        ).first()
        qs = queryset.filter(car=car)
        return qs

    def car_filter_in(self, queryset, name, value):
        values = value.split(",")
        qs = Waybill.objects.none()
        for value in values:
            qs |= self.car_filter(queryset, name, value)
        return qs

    def loaded_filter(self, queryset, name, value):
        query = Q()
        selection = value.split()
        if set(["false", False]).intersection(selection):
            query &= Q(loaded=False)
        elif set(["true", True]).intersection(selection):
            query &= Q(loaded=True)
        return queryset.filter(query)

    def date_range_filter(self, qs, field_name, value):
        query = Q()
        # singe date range
        date_range = value
        start, end = date_range.split("_")
        query |= Q(**{f"{field_name}__range": (start, end)})
        return qs.filter(query)

    def date_range_filter__in(self, qs, field_name, value):
        query = Q()
        # fix field_name
        field_name = field_name.split("__in")[0]

        # break the ranges down
        date_ranges = value.split(",")
        for date_range in date_ranges:
            start, end = date_range.split("_")
            # build query
            query |= Q(**{f"{field_name}__range": (start, end)})
        return qs.filter(query)

    def date_range_filter__exclude(self, qs, field_name, value):
        query = Q()
        # fix field_name
        field_name = field_name.split("__exclude")[0]

        # break the ranges down
        date_ranges = value.split(",")
        for date_range in date_ranges:
            start, end = date_range.split("_")
            # build query
            query |= Q(**{f"{field_name}__range": (start, end)})
        return qs.exclude(query)


class WaybillViewSet(
    AutoCompleteViewSetMixin,
    mixins.ListModelMixin,
    mixins.RetrieveModelMixin,
    viewsets.GenericViewSet,
):
    serializer_class = WaybillSerializer
    filterset_class = WaybillFilter
    pagination_class = LimitOffsetPagination
    permission_classes = [IsAuthenticated]
    ordering_fields = [
        "stcc__stcc",
        "stcc__name",
        "document_timestamp",
        # "car",
        # "loaded",
        "origin__name",
        "destination__name",
        "weight",
        # "volume",
        "shipper_number",
        "consignee_name",
        "shipper_name",
        "freight_payer_name",
        "carrier_number",
        "customer_number",
        "reporting_railroad",
        "origin_railroad",
        "destination_railroad",
    ]

    def get_queryset(self):
        if hasattr(self.request.user.company, "track_trace"):
            company = self.request.user.company.track_trace
            return (
                Waybill.objects.filter(company=company)
                .select_related(
                    "company", "trip", "car", "stcc", "origin", "destination"
                )
                .annotate(
                    car_reference=Concat(
                        F("car__mark"), F("car__number"), output_field=CharField()
                    )
                )
            )
        return Waybill.objects.none()

    def get_serializer_class(self):
        if self.action == "list":
            return WaybillListSerializer
        return super().get_serializer_class()

    @action(detail=True, methods=["POST"])
    def assign_loaded_rail_car(self, request, pk):
        """
        Assigns the given loaded rail car to this waybill.\n
        Checks if the car has been assigned to another waybill,
        disconnects that link and reassigns to this waybill.
        """
        carID = request.data.get("car_id")
        loaded_rail_car = get_object_or_404(LoadedRailCar, pk=carID)

        if loaded_rail_car.current_location.company != request.user.company.wheelhouse:
            return Response(
                "Not permitted to assign this rail car",
                status=status.HTTP_403_FORBIDDEN,
            )

        # if requested car already has waybill
        try:
            loaded_rail_car.waybill = None
        except ObjectDoesNotExist:
            pass

        waybill = self.get_object()

        # if this waybill already has a car, disconnect that car
        try:
            loaded_rail_car_on_waybill = get_object_or_404(
                LoadedRailCar, pk=waybill.shipment_car.id
            )
            loaded_rail_car_on_waybill.waybill = None
            loaded_rail_car_on_waybill.save()
        except ObjectDoesNotExist:
            # waybill.shipment_car might be none
            pass

        # assign waybill to requested car, then save
        loaded_rail_car.waybill = waybill
        loaded_rail_car.save()

        # update shipment arrival date
        if (
            loaded_rail_car.waybill.trip and
            loaded_rail_car.waybill.trip.arrival_timestamp
        ):
            shipment = loaded_rail_car.rail_shipment
            arrival_timestamp = loaded_rail_car.waybill.trip.arrival_timestamp
            shipment.arrive_date = arrival_timestamp
            shipment.save(update_fields=['arrive_date'])

            if shipment.master_billing and not shipment.master_billing.arrive_date:
                shipment.master_billing.arrive_date = arrival_timestamp
                shipment.master_billing.save(update_fields=['arrive_date'])

        RailShipmentNote(
            rail_shipment=loaded_rail_car.rail_shipment,
            note=f"Assigned Loaded Rail Car: {loaded_rail_car} to Waybill: {waybill}; by: {request.user}",
            is_write_protect=True,
        ).save()

        return Response(self.get_serializer(waybill).data, status.HTTP_200_OK)

    @action(detail=False, methods=["GET"])
    def count(self, request):
        return Response(self.filter_queryset(self.get_queryset()).count())

    @action(detail=False, methods=["GET"], permission_classes=[IsAuthenticated])
    def autocomplete(self, request):
        suggestions = []
        field_name = self.request.query_params.get("field")
        user_input = self.request.query_params.get("input")
        limit = int(self.request.query_params.get("limit", 15))
        # get list of values for field_name, sorted by most frequent entry first
        if field_name == "car":
            qs = self.filter_queryset(self.get_queryset())

            if user_input and len(user_input):
                car_matches = re.compile("([a-zA-Z]*)\s*(\d*)").match(user_input)
                mark = car_matches.group(1).upper()
                number = car_matches.group(2)
                if mark:
                    qs = qs.filter(car__mark__contains=mark)
                if number:
                    qs = qs.filter(car__number__contains=number)

            qs = (
                qs.values_list("car__mark", "car__number")
                .annotate(
                    car_reference=Concat(
                        F("car__mark"), F("car__number"), output_field=CharField()
                    )
                )
                .annotate(field_count=Count("car_reference"))
                .order_by("-field_count")
                .values_list("car_reference", flat=True)
            )

            count = qs.distinct().count()
            suggestions = qs.distinct()[:limit]

            return Response({"results": suggestions, "count": count})

        else:
            return super().autocomplete(request)
