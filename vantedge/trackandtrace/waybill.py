from django.db.models import <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, When, Subquery, OuterRef
from django.db.utils import IntegrityError
from progressbar import progressbar

from .models import Waybill, TrackAndTraceCompany, Trip, CarEvent
from vantedge.edi.models import EdiDocument


def waybills(company_id=None):
    """
    Creates car event from waybill information
    """

    for company in (
        TrackAndTraceCompany.objects.filter(id=company_id)
        if company_id
        else TrackAndTraceCompany.objects.exclude(id=24)
    ).iterator():

        car_events = []
        # create events from waybills with no events associated
        incoming_waybills = company.waybill_set.filter(
            event__isnull=True, document_type="OR"
        )

        print(f"Company ID {company.id} {company} CREATING EVENTS FROM WAYBILLS")

        try:
            for wb in progressbar(list(incoming_waybills.iterator())):
                try:
                    event = CarEvent(company=company)
                    event.from_waybill(wb)
                    car_events.append(event)
                except Exception as e:
                    # skip processing if from_waybill throws exception
                    # print(e)
                    pass

            CarEvent.objects.bulk_create(car_events)

            if incoming_waybills:
                print(f"{len(car_events)}/{len(incoming_waybills)} waybills processed.")
            else:
                print(f"All waybills processed.")

        except Exception as e:
            print(f"Failed to create events from waybills for {company}", str(e))
