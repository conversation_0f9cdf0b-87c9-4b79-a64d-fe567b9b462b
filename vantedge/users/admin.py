from django.contrib import admin
from django.contrib.auth import admin as auth_admin
from django.contrib.auth import get_user_model
from django.db import models
from django_json_widget.widgets import JSONEditorWidget
from django.contrib.admin.widgets import FilteredSelectMultiple
from vant_dbreplication.admin.utils import JSONFieldAdmin
from vantedge.users.forms import UserChangeForm, UserCreationForm
from vantedge.users.models import Company, ApplicationConfiguration, FormConfiguration, UserDevice, UserPopup, UserPopupHistory, UserNote

User = get_user_model()


@admin.register(Company)
class CompanyAdmin(admin.ModelAdmin):
    pass


@admin.register(UserDevice)
class UserDeviceAdmin(admin.ModelAdmin):
    list_display = ["user", "device_id", "created"]
    search_fields = ["user__username", "device_id"]


@admin.register(ApplicationConfiguration)
class ApplicationConfigurationAdmin(JSONFieldAdmin, admin.ModelAdmin):
    filter_horizontal = ('available_permissions',)


@admin.register(UserPopup)
class UserPopupAdmin(admin.ModelAdmin):
    list_display = ['name', 'start_ts', 'end_ts', 'show_to_all']
    search_fields = ['name']
    list_filter = ['show_to_all']
    formfield_overrides = {
        models.ManyToManyField: {
            'widget': FilteredSelectMultiple('Users', is_stacked=False)
        }
    }


@admin.register(UserPopupHistory)
class UserPopupHistoryAdmin(admin.ModelAdmin):
    list_display = ['user', 'user_popup', 'seen_at']

@admin.register(UserNote)
class UserNoteAdmin(admin.ModelAdmin):
    list_display = ('created_by', 'created_at', 'company', 'fk_model')


@admin.register(FormConfiguration)
class FormConfigurationAdmin(JSONFieldAdmin, admin.ModelAdmin):
    formfield_overrides = {
        models.JSONField: {"widget": JSONEditorWidget(options={"mode": "code"})}
    }


@admin.register(User)
class UserAdmin(auth_admin.UserAdmin):
    form = UserChangeForm
    formfield_overrides = {
        models.JSONField: {"widget": JSONEditorWidget(options={"mode": "code"})}
    }
    add_form = UserCreationForm
    fieldsets = (
        (
            "User",
            {"fields": ("name", "company", "assigned_companies", "assigned_tboss_sites", "telephone", "score", "ui_config", "app_config")},
        ),
    ) + auth_admin.UserAdmin.fieldsets
    list_display = ["username", "name", "company", "is_superuser"]
    search_fields = ["name", "email"]
    save_on_top = True
