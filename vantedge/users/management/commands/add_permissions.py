from django.contrib.auth import get_user_model
from django.contrib.auth.models import Permission, Group
from django.apps import apps
from django.contrib.contenttypes.models import ContentType
from django.core.management.base import BaseCommand, CommandError

from vantedge.users.models import User, Company

import inquirer


class Command(BaseCommand):
    help = """
        Add given permission to all selected companies,
        ex: manage.py add_permissions --permission-codename add_report change_report
    """

    def handle(self, *args, **options):
        app_model = [
            inquirer.Text(
                "app_model",
                message="what is the target app.model name? ex, reports.report",
            )
        ]

        app_model_answer = inquirer.prompt(app_model)
        app_name, model_name = app_model_answer["app_model"].split(".")
        model_class = apps.get_model(app_name, model_name)
        content_type = ContentType.objects.get_for_model(model_class)

        if not content_type:
            print("content type not found ")
            return
        else:
            print("given content type:", content_type)

        permissions = [
            inquirer.Text(
                "codenames",
                message="what are the code names? comma seperated, ex: add_report, change_report",
            )
        ]
        permissions_answer = inquirer.prompt(permissions)
        codenames = permissions_answer["codenames"].replace(" ", "").split(",")
        if not codenames:
            print("codenames are empty! ")
            return
        else:
            permissions_list = Permission.objects.filter(content_type=content_type, codename__in=codenames)
            print("given codenames:", codenames)

        company_list = [
            inquirer.Checkbox(
                'companies',
                message="please select which companies will have this permission",
                choices=list(Company.objects.values_list("name", flat=True).order_by('name'))
            )
        ]

        selected_companies = inquirer.prompt(company_list)
        companies = selected_companies["companies"]

        print(f"Setting available permissions for {len(companies)} companies")
        for company_name in companies:
            company = Company.objects.get(name=company_name)
            for permission in permissions_list:
                company.applicationconfiguration.available_permissions.add(permission)
        print("Done!")

        target_users_text = [
            inquirer.Text(
                "target_users",
                message="Do you want to give those permissions to specific user? please give usernames comma seperated",
            )
        ]

        target_users_answer = inquirer.prompt(target_users_text)
        if target_users_answer["target_users"]:
            target_users = target_users_answer["target_users"].replace(" ", "").split(",")
            print(f"giving permissions to {len(target_users)} user")
            breakpoint()
            for target_user in target_users:
                user = User.objects.filter(username=target_user)
                if not user:
                    print(f"{target_user} not found!")
                    continue
                else:
                    user = user[0]

                for permission in permissions_list:
                    user.user_permissions.add(permission)
            print("Done!")
