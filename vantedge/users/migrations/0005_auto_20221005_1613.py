# Generated by Django 3.2.7 on 2022-10-05 22:13

import django.contrib.postgres.fields
from django.db import migrations, models
import functools
import schema_field.fields
import vantedge.users.models.schema


class Migration(migrations.Migration):

    dependencies = [("users", "0004_user_ui_config")]

    operations = [
        migrations.AddField(
            model_name="user",
            name="app_config",
            field=schema_field.fields.JSONSchemedField(
                default=vantedge.users.models.schema.AppConfigData,
                encoder=functools.partial(
                    schema_field.fields.JSONSchemedEncoder,
                    *(),
                    **{"schema": (vantedge.users.models.schema.AppConfigData,)}
                ),
                schema=(vantedge.users.models.schema.AppConfigData,),
            ),
        ),
        migrations.AddField(
            model_name="user",
            name="role",
            field=django.contrib.postgres.fields.ArrayField(
                base_field=models.CharField(
                    choices=[
                        ("Commercial", "Commercial"),
                        ("Site Administration", "Site Administration"),
                        ("Carrier/Dispatch", "Carrier/Dispatch"),
                        ("Driver", "Driver"),
                    ],
                    max_length=50,
                ),
                default=list,
                size=None,
            ),
        ),
    ]
