# Generated by Django 3.2.7 on 2023-01-25 21:00

import django.contrib.postgres.fields
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('users', '0008_applicationconfiguration_track_trace_show_cars_with_waybill'),
    ]

    operations = [
        migrations.AddField(
            model_name='applicationconfiguration',
            name='available_groups',
            field=django.contrib.postgres.fields.ArrayField(base_field=models.CharField(max_length=150, unique=True), blank=True, default=list, size=None),
        ),
    ]
