# Generated by Django 3.2.7 on 2023-01-25 21:00

from django.db import migrations


def populate_app_config_available_groups(apps, schema_editor):
    ApplicationConfiguration = apps.get_model("users", "ApplicationConfiguration")
    Group = apps.get_model("auth", "Group")

    groups = list(Group.objects.values_list("name", flat=True))

    for app_config in ApplicationConfiguration.objects.all():
        # put all group options (just names) into available_groups
        app_config.available_groups = groups
        app_config.save()


def reset_app_config_available_groups(apps, schema_editor):
    ApplicationConfiguration = apps.get_model("users", "ApplicationConfiguration")
    for app_config in ApplicationConfiguration.objects.all():
        # clear available_groups
        app_config.available_groups = []
        app_config.save()


class Migration(migrations.Migration):

    dependencies = [("users", "0009_applicationconfiguration_available_groups")]

    operations = [
        migrations.RunPython(
            populate_app_config_available_groups, reset_app_config_available_groups
        )
    ]
