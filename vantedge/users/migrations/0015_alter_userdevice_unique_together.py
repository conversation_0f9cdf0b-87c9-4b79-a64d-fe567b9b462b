# Generated by Django 4.2 on 2023-08-01 15:05

from django.db import migrations
from django.db.models import Count

def remove_duplicate_rows(apps, schema_editor):
    db_alias = schema_editor.connection.alias

    UserDevice = apps.get_model("users", "UserDevice")
    rows = UserDevice.objects.values("user", "device_id").annotate(c=Count("user")).order_by("user").filter(c__gt=1)
    for row in rows:
        UserDevice.objects.filter(user=row["user"], device_id=row["device_id"]).delete()


class Migration(migrations.Migration):

    dependencies = [
        ("users", "0014_applicationconfiguration_inform_parties_of_void_billing"),
    ]

    operations = [
        migrations.RunPython(remove_duplicate_rows),

        migrations.AlterUniqueTogether(
            name="userdevice",
            unique_together={("user", "device_id")},
        ),
    ]
