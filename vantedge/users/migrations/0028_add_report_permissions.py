from django.contrib.auth.models import Permission
from django.contrib.contenttypes.models import ContentType
from django.core.management.base import BaseCommand

from vantedge.reports.models import Report
from vantedge.users.models import User, Company

from django.db import migrations, models

def add_report_permissions(apps, schema_editor):
    report_content_type = ContentType.objects.get_for_model(Report)
    report_menu_permission = Permission.objects.filter(
        content_type=report_content_type, codename="access_menu__reports"
        ).first()
    if not report_menu_permission:
        print("report_menu_permission not found, exiting....")
        return

    add_report_permission, created = Permission.objects.get_or_create(content_type=report_content_type, codename="add_report")
    if created:
        print("add_report permission created!")
    change_report_permission, created = Permission.objects.get_or_create(content_type=report_content_type, codename="change_report")
    if created:
        print("change_report permission created!")

    print("assigning permissions to companies...")
    for company in Company.objects.filter(applicationconfiguration__available_permissions=report_menu_permission):
        company.applicationconfiguration.available_permissions.add(add_report_permission)
        company.applicationconfiguration.available_permissions.add(change_report_permission)

    print("assigning user permissions")
    target_users = User.objects.filter(user_permissions=report_menu_permission)
    for target_user in target_users:
        target_user.user_permissions.add(add_report_permission)
        target_user.user_permissions.add(change_report_permission)

class Migration(migrations.Migration):

    dependencies = [
        ("users", "0027_user_assigned_railroad_facilities"),
    ]

    operations = [
        migrations.RunPython(add_report_permissions)
    ]
