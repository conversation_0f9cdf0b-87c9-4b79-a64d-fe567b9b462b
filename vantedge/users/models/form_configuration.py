from django.contrib.contenttypes.models import ContentType
from django.db import models
from django.utils.translation import gettext_lazy as _
from schema_field.fields import J<PERSON>NSchemedField
from pydantic import BaseModel
from typing import Optional, List
from vantedge.util.mixins import EasyAuditModelConfigMixin
from .application_configuration import ApplicationConfiguration

# class TableColumn(BaseModel):
#     # To be used for quasar tables
#     # https://quasar.dev/vue-components/table/
#     name: Optional[str] = None
#     label: Optional[str] = None
#     type: Optional[str] = None # dateRange,
#     align: Optional[str] = None
#     sortable: Optional[bool] = None
#     nonFilterable: Optional[bool] = None
#     autocompleteField: Optional[str] = None

class AdditionalConfig(BaseModel):
    allow_multi_address: bool = False


class FormFeatureData(BaseModel):
    # General
    form_uses_server_associations: bool = False
    form_server_specific_fields_initial: Optional[dict] = dict()
    form_uses_certifications: bool = False
    form_uses_jsonforms: bool = False
    form_detailed_component_route: str = ''
    choices_queryset_return_empty_on_no_association: bool = False


class FormConfigurationData(BaseModel):
    table_columns: Optional[List[dict]] = list()
    table_feature_flags: Optional[List[str]] = list()

     # To be used for JSON Forms
     # https://jsonforms.io/docs/
    form_schema: Optional[dict] = dict()
    form_ui_schema: Optional[dict] = dict()
    form_inital_data: Optional[dict] = dict()
    form_features: Optional[FormFeatureData] = FormFeatureData()

    # anything that is not generic should be added here
    additional_config: Optional[AdditionalConfig] = AdditionalConfig()


class FormConfiguration(EasyAuditModelConfigMixin, models.Model):
    """ Used to configure table and form view of Wheelhouse objects specific to a client's application configuration """
    class Meta:
        verbose_name = _("Form Configuration")
        verbose_name_plural = _("Form Configurations")

    application_configuration = models.ForeignKey(ApplicationConfiguration, on_delete=models.CASCADE)
    content_type = models.ForeignKey(ContentType, on_delete=models.CASCADE)
    data = JSONSchemedField(schema=FormConfigurationData, default=FormConfigurationData, blank=True, null=True)

    def __str__(self):
        return f"{self.application_configuration} form - {self.content_type}"
