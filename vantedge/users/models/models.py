from django.contrib.auth.models import AbstractUser
from django.contrib.postgres.fields import <PERSON><PERSON>y<PERSON>ield
from django.contrib.contenttypes.models import ContentType
from django.conf import settings
from django.db.models import signals, Q

from django.db import models
from django.db.models.functions import Lower
from django.dispatch import receiver
from django.urls import reverse
from django.utils.translation import gettext_lazy as _
from django.utils import timezone
from allauth.account.signals import user_signed_up

from rest_framework.authtoken.models import Token
from phonenumber_field.modelfields import <PERSON>NumberField
from uuid import uuid4
import re

from schema_field.fields import J<PERSON>NSchemedField
from .schema import AppConfigData

from vantedge.util.notifications.email import send_user_expiry_notification
from vantedge.util.mixins import EasyAuditModelConfigMixin

import logging

logger = logging.getLogger(__name__)


def default_user_data():
    return {"address": {}}


class Company(EasyAuditModelConfigMixin, models.Model):
    name = models.CharField(max_length=100)
    logo_url_bol = models.URLField(blank=True, null=True)

    class Meta:
        verbose_name_plural = "Companies"
        constraints = [
            models.UniqueConstraint(
                Lower('name'),
                name='unique_company_name',
            ),
        ]

    def __str__(self):
        return self.name

    def get_dispatchers(self):
        return self.user_set.filter(
            is_active=True, roles__contains=[User.Role.CARRIER_DISPATCH]
        )

    def get_site_administrators(self):
        return self.user_set.filter(
            is_active=True, roles__contains=[User.Role.SITE_ADMINISTRATION]
        )

    def get_drivers(self):
        return self.user_set.filter(is_active=True, roles__contains=[User.Role.DRIVER])

    def get_template(self, template_id):
        from django.template import Template
        return Template("""{% extends 'email/email_base_color.mjml' %}""")

class User(EasyAuditModelConfigMixin, AbstractUser):
    class Role(models.TextChoices):
        COMMERCIAL = "Commercial", "Commercial"
        SITE_ADMINISTRATION = "Site Administration", "Site Administration"
        CARRIER_DISPATCH = "Carrier/Dispatch", "Carrier/Dispatch"
        DRIVER = "Driver", "Driver"
        OPERATOR = "Operator", "Operator"

    name = models.CharField(_("Name of User"), blank=True, max_length=255)
    telephone = PhoneNumberField(blank=True)
    data = models.JSONField(null=True, default=default_user_data)
    company = models.ForeignKey(
        Company, null=True, on_delete=models.SET_NULL, verbose_name="Selected Company"
    )
    assigned_companies = models.ManyToManyField(
        Company, blank=True, related_name="assigned_users"
    )
    assigned_tboss_sites = models.ManyToManyField(
        "vant_dbreplication.DBReplicationServer", blank=True, related_name="assigned_users"
    )
    assigned_railroad_facilities = models.ManyToManyField(
        "wheelhouse.Facility", blank=True, related_name="assigned_users"
    )

    ui_config = models.JSONField(null=True, blank=True, default=dict)
    app_config = JSONSchemedField(schema=AppConfigData, default=AppConfigData)

    roles = ArrayField(
        models.CharField(choices=Role.choices, max_length=50), default=list, blank=True
    )
    score = models.SmallIntegerField(blank=True, default=0)
    password_set_date = models.DateTimeField(null=True)

    class Meta:
        ordering = ["username"]

    def get_absolute_url(self):
        return reverse("users:detail", kwargs={"username": self.username})

    @property
    def certificates(self):
        from vantedge.wheelhouse.models import Certificate

        return Certificate.objects.filter(
            content_type=ContentType.objects.get_for_model(self), object_id=self.id
        )

    @property
    def is_driver(self):
        return self.Role.DRIVER in self.roles

    @property
    def is_site_administrator(self):
        return self.Role.SITE_ADMINISTRATION in self.roles

    @property
    def is_carrier_dispatch(self):
        return self.Role.CARRIER_DISPATCH in self.roles

    @property
    def is_commercial(self):
        return self.Role.COMMERCIAL in self.roles

    @property
    def is_operator(self):
        return self.Role.OPERATOR in self.roles

    def notification_locations_company(self, company):
        result = list(
            filter(
                lambda notification_location: notification_location.company
                == company.id,
                self.app_config.notification_locations,
            )
        )

        if result:
            return result[0].locations
        return []

    def regenerate_token(self):
        Token.objects.filter(user=self).delete()
        Token.objects.create(user=self)
        return self.auth_token.key

    def check_user_expiry(self, notify_days=None):
        now = timezone.now()
        password_maximum_age = (
            self.company.applicationconfiguration.password_maximum_age
        )
        if password_maximum_age is None or self.password_set_date is None:
            return

        diff_created = now - self.password_set_date
        if diff_created.days > password_maximum_age:
            self.is_active = False
            self.save(update_fields=["is_active"])
            return

        days_remaining = password_maximum_age - diff_created.days
        if notify_days and days_remaining < notify_days:
            print(f"sending user {self.username} password expiry notification...")
            send_user_expiry_notification(self, days_remaining)

    def add_score(self, value):
        self.score = models.F("score") + value
        self.save(update_fields=["score"])

    def reset_score(self):
        self.score = 0
        self.save(update_fields=["score"])


@receiver(user_signed_up)
def user_signed_up_signal(request, user, **kwargs):
    user.is_active = False
    if user.name == "" and (user.first_name or user.last_name):
        user.name = f"{user.first_name} {user.last_name}".strip()
    if user.email and user.username and not re.match(r".+@.+", user.username):
        user.username = user.email
    if not user.has_usable_password():
        user.set_password(uuid4().hex)
    user.save()

def sync_wheelhouse_user_changes_to_tboss_user(sender, instance, **kwargs):
    """_summary_
    Post Save receiver function to ensure that any changes to a Wheelhouse User
    gets reflected to a record on the TerminalBOSS User Database

    Flag for application configration - wheelhouse_user_changes_syncs_to_tboss_user
    Also needs to have a terminalboss v3 subscription and the user to be assigned to tboss sites

    Args:
        sender (_type_): Sender class
        instance (_type_): User instance
    """

    m2m_signal_action = kwargs.get('action')
    if m2m_signal_action and m2m_signal_action in ["pre_add", "pre_remove", "pre_clear"]:
        # Do a noop for m2m pre execute signals
        return

    wheelhouse_user = instance
    if hasattr(wheelhouse_user, 'company'):
        company = wheelhouse_user.company
        if hasattr(company, 'applicationconfiguration'):
            application_configuration = company.applicationconfiguration
            terminalboss_subscription = hasattr(company, 'terminalboss')
            if (
                terminalboss_subscription and
                application_configuration.wheelhouse_user_changes_syncs_to_tboss_user
            ):
                from vantedge.tboss.models import TBossUser

                content_type = ContentType.objects.get_for_model(TBossUser, for_concrete_model=True)

                terminalboss_company = getattr(company, 'terminalboss')
                assigned_tboss_sites = wheelhouse_user.assigned_tboss_sites.all()
                assigned_tboss_site_ids = assigned_tboss_sites.values_list('id', flat=True)

                # if bypass_assigned_tboss_sites is set to True, do not filter by assigned_tboss_sites
                bypass_assigned_tboss_sites = application_configuration.bypass_assigned_tboss_sites

                query = Q(content_type=content_type)

                if not bypass_assigned_tboss_sites:
                    query &= Q(servers__in=assigned_tboss_sites)

                replication_configurations = terminalboss_company.dbreplicationconfig_set.filter(query)

                synced_fields = ['name', 'username', 'email', 'is_active', 'telephone']
                tboss_user_fields = [f.name for f in TBossUser._meta.get_fields()]
                update_field_values = {
                    field: getattr(wheelhouse_user, field) for field in synced_fields if hasattr(wheelhouse_user, field) and field in tboss_user_fields
                }

                if not update_field_values:
                    logger.warning("No Fields between Wheelhouse User model and TerminalBOSS User model could be updated")
                    return

                if settings.DEBUG:
                    logger.info(f"Sender: {sender}")

                for replication_configuration in replication_configurations:
                    # if bypass_assigned_tboss_sites is set to True, do not filter by assigned_tboss_sites
                    query = Q()
                    if not bypass_assigned_tboss_sites:
                        query &= Q(pk__in=assigned_tboss_site_ids)

                    servers = replication_configuration.servers.filter(query)
                    server = servers.filter(name='Wheelhouse').first()
                    if not server:
                        server = servers.first()

                    # lookup by username first
                    tboss_user = replication_configuration.user_set.filter(Q(username=wheelhouse_user.username)|Q(code=f"Wheelhouse-{wheelhouse_user.pk}")).first()

                    if not tboss_user:
                        tboss_user = TBossUser.objects.create(**{
                            **update_field_values,
                            "code": f"Wheelhouse-{wheelhouse_user.pk}",
                            "created_by_server": server,
                            "modified_by_server": server,
                            "dbreplication_config": replication_configuration,
                            "dbreplication_client_company": terminalboss_company,
                            "password": TBossUser.objects.make_random_password()
                        })
                        if settings.DEBUG:
                            logger.info(f"Wheelhouse User {wheelhouse_user} triggered a creation of its TerminalBOSS user equivalent for site {server}")
                    else:
                        for field, value in update_field_values.items():
                            setattr(tboss_user, field, value)
                        tboss_user.save(update_fields=update_field_values)
                        if settings.DEBUG:
                            logger.info(f"Wheelhouse User {wheelhouse_user} triggered an update to its TerminalBOSS user equivalent for site {server}")

signals.post_save.connect(sync_wheelhouse_user_changes_to_tboss_user, sender=User, dispatch_uid="sync_wheelhouse_user_changes_to_tboss_user_signal")
signals.m2m_changed.connect(sync_wheelhouse_user_changes_to_tboss_user, sender=User.assigned_tboss_sites.through, dispatch_uid="sync_wheelhouse_user_changes_to_tboss_user_m2m_signal")
