from django.db import models
from django.contrib.contenttypes.models import ContentType
from django.contrib.contenttypes.fields import GenericForeignKey
from .models import User, Company


class UserNote(models.Model):
    company = models.ForeignKey(Company, on_delete=models.CASCADE, related_name='user_notes')
    content = models.TextField(max_length=255 ,help_text="Content to be shown in the note.")
    created_by = models.ForeignKey(User, related_name='notes_created', on_delete=models.SET_NULL, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    content_type = models.ForeignKey(ContentType, on_delete=models.CASCADE, help_text="Content type the note is associated with (eg. railcar)")
    object_id = models.CharField(max_length=40, help_text="Object Id the note is associated with. Pairs with content_type.")
    fk_model = GenericForeignKey("content_type", "object_id")

    def __str__(self):
        return f"Note {self.id} - Created by {self.created_by} - Created at {self.created_at}"

    class Meta:
        verbose_name = "User Note"
        verbose_name_plural = "User Notes"
