from allauth.socialaccount.models import SocialAccount
from django.contrib.auth import get_user_model
from django.contrib.auth.models import Permission, Group
from django.contrib.contenttypes.models import ContentType
from rest_framework import serializers

from vantedge.wheelhouse.models.location import Facility
from vantedge.users.models import Company, ApplicationConfiguration, FormConfiguration, UserDevice, UserPopup
from vantedge.util.notifications.email import (
    send_user_register_notification,
    send_user_register_sso_notification
)
from vantedge.wheelhouse.views.serializers.certificate import CertificateSerializer
from vantedge.wheelhouse.views.serializers.utility import ModelNameIdSerializer

User = get_user_model()


def get_subscription_company(company, related_name):
    return getattr(company, related_name) if hasattr(company, related_name) else None


class UserSocialAccountSerializer(serializers.ModelSerializer):
    class Meta:
        model = SocialAccount
        fields = ["provider"]


class ContentTypeSerializer(serializers.ModelSerializer):
    class Meta:
        model = ContentType
        fields = "__all__"


class PermissionSerializer(serializers.ModelSerializer):
    content_type = ContentTypeSerializer(read_only=True)

    def to_internal_value(self, data):
        internal_value = super().to_internal_value(data)
        permission_id = data.get("id")
        content_type = data.get("content_type")

        if permission_id:
            internal_value["id"] = data.get("id")

        if content_type:
            internal_value["content_type"] = data.get("content_type")
        return internal_value

    class Meta:
        model = Permission
        fields = "__all__"


class GroupSerializer(serializers.ModelSerializer):
    permissions = PermissionSerializer(read_only=True, many=True)

    class Meta:
        model = Group
        fields = "__all__"
        extra_kwargs = {"name": {"validators": []}}


class FormConfigurationSerializer(serializers.ModelSerializer):
    content_type = ContentTypeSerializer(read_only=True)

    class Meta:
        model = FormConfiguration
        fields = ["content_type", "data"]


class ApplicationConfigurationSerializer(serializers.ModelSerializer):
    available_permissions = PermissionSerializer(many=True, read_only=True)
    form_configurations = FormConfigurationSerializer(many=True, read_only=True, source='formconfiguration_set')
    app_labels = serializers.SerializerMethodField()

    class Meta:
        model = ApplicationConfiguration
        exclude = ["track_trace_show_cars_with_waybill"]
        read_only_fields = [
            "schedule_access_mode",
            "available_groups",
            "available_roles",
            "available_permissions",
        ]

    @staticmethod
    def get_app_labels(obj):
        modules = {}
        company = obj.company
        # check if use has terminalboss v3 app
        if hasattr(company, "terminalboss"):
            terminalboss = company.terminalboss
            modules["tboss"] = terminalboss.data
        return modules


class RelatedCompanyApplicationConfigurationSerializer(serializers.ModelSerializer):
    """
    if application configuration not belong to company and came from company parties,
    then only specific fields should be seenable for company which owned the party,
    """

    company = serializers.CharField(source="company.name", read_only=True)

    class Meta:
        model = ApplicationConfiguration
        fields = [
            "id",
            "company",
            "schedule_access_mode",
            "schedule_driver_book_slot",
            "schedule_driver_cancel_booking",
            "schedule_dispatchers_set_booking_activity",
            "dispatcher_can_alter_booking"
        ]


class CompanyNameIdPermissionSerializer(serializers.ModelSerializer):
    companies = serializers.SerializerMethodField()
    application_configuration = ApplicationConfigurationSerializer(source='applicationconfiguration', read_only=True)

    @staticmethod
    def get_companies(obj):
        """
        return {
            "dashboard": True,
            "fleet": hasattr(obj, "track_trace"),
            "operations": user.is_superuser,
            "trackAndTrace": hasattr(obj, "track_trace"),
            "reports": hasattr(obj, "reporting"),
            "contracts": hasattr(obj, "wheelhouse"),
        }
        """
        main_subscriptions = dict(
            (
                ("edi", get_subscription_company(obj, "edi")),
                ("reports", get_subscription_company(obj, "reporting")),
                ("wheelhouse", get_subscription_company(obj, "wheelhouse")),
                ("operations", get_subscription_company(obj, "wheelhouse")),
                ("invoice", get_subscription_company(obj, "invoice")),
                ("track_and_trace", get_subscription_company(obj, "track_trace")),
                ("yard_management", get_subscription_company(obj, "yard_management")),
                ("terminalboss", get_subscription_company(obj, "terminalbosscompany")),
                ("terminalbossv3", get_subscription_company(obj, "terminalboss")),
                ("document_reading", get_subscription_company(obj, "document_reading")),
                ("schedule", get_subscription_company(obj, "schedule")),
                ("customs", get_subscription_company(obj, "customs")),
                ("invitation", get_subscription_company(obj, "invitation")),
                ("forms", get_subscription_company(obj, "forms"))
            )
        )

        subscriptions = {
            subscription_prefix: {
                "id": subscription_company.id if subscription_company else None,
                "subscribed": subscription_company is not None,
            }
            for subscription_prefix, subscription_company in main_subscriptions.items()
        }

        return subscriptions

    class Meta:
        model = Company
        fields = ["id", "name", "companies", "application_configuration"]
        read_only_fields = fields


class UserListSerializer(serializers.ModelSerializer):
    groups = serializers.SerializerMethodField()
    last_activity = serializers.DateTimeField(read_only=True)

    def get_groups(self, obj):
        return [group.name for group in obj.groups.all()]

    class Meta:
        model = User
        fields = [
            "id",
            "email",
            "username",
            "name",
            "company",
            "assigned_tboss_sites",
            "assigned_railroad_facilities",
            "telephone",
            "is_active",
            "groups",
            "last_activity",
            "roles",
            "date_joined",
            "last_login"
        ]
        read_only_fields = fields

class UserSerializer(serializers.ModelSerializer):
    company = CompanyNameIdPermissionSerializer(read_only=True)
    assigned_companies = serializers.SerializerMethodField()
    socialaccount_set = UserSocialAccountSerializer(read_only=True, many=True)
    certificates = CertificateSerializer(read_only=True, many=True)
    groups = GroupSerializer(many=True)
    user_permissions = PermissionSerializer(many=True)
    user_access = serializers.SerializerMethodField()
    last_activity = serializers.DateTimeField(read_only=True)

    @staticmethod
    def get_assigned_companies(user):
        if user.is_superuser:
            return ModelNameIdSerializer(Company)(Company.objects.all(), many=True).data
        return ModelNameIdSerializer(Company)(
            user.assigned_companies.all(), many=True
        ).data

    @staticmethod
    def get_user_access(user):
        """
        Retrieves all the permissions the user has and returns them in the format below
        user_access: [
            {
                app: 'wheelhouse',
                models: [
                    {
                        model: 'contract',
                        codenames: ['add_contract', 'edit_contract', 'delete_contract']
                    }
                ]
            }
        ]
        """
        user_access = []
        if not user.is_active or user.is_superuser:
            return user_access
        else:
            # retrieve the user and group permissions
            permissions = set(user.user_permissions.all()).union(
                *(group.permissions.all() for group in user.groups.all())
            )

        for permission in permissions:
            app_label = permission.content_type.app_label.lower()
            model_name = permission.content_type.model.lower()
            codename = permission.codename.lower()

            app_access = next(
                filter(lambda access: access.get("app") == app_label, user_access), None
            )
            if not app_access:
                app_access = {"app": app_label, "models": []}
                user_access.append(app_access)

            app_access_index = user_access.index(app_access)

            model_access = next(
                filter(
                    lambda model: model.get("model") == model_name,
                    user_access[app_access_index]["models"],
                ),
                None,
            )
            if not model_access:
                model_access = {"model": model_name, "codenames": [], "ids": []}
                user_access[app_access_index]["models"].append(model_access)

            model_access_index = user_access[app_access_index]["models"].index(
                model_access
            )

            if (
                codename
                not in user_access[app_access_index]["models"][model_access_index][
                    "codenames"
                ]
            ):
                user_access[app_access_index]["models"][model_access_index][
                    "codenames"
                ].append(codename)
                user_access[app_access_index]["models"][model_access_index][
                    "ids"
                ].append(permission.id)

        return user_access

    def to_internal_value(self, data):
        additionals = {}
        additional_fields = ["company", "password"]
        for field in additional_fields:
            if field in data:
                additionals[field] = data.pop(field)

        internal_value = super().to_internal_value(data)
        for field, val in additionals.items():
            if field == "company":
                internal_value["company_id"] = val.get("id")
            else:
                internal_value[field] = val
        return internal_value

    def create(self, validated_data):

        groups = validated_data.pop("groups", [])
        user_permissions = validated_data.pop("user_permissions", [])
        password = validated_data.pop("password", "")
        company = validated_data.get("company_id")

        if validated_data["email"] != validated_data["username"]:
            raise serializers.ValidationError("email and username should be same")

        instance = super().create(validated_data)

        instance.user_permissions.set(user_permissions)
        instance.groups.set(groups)

        instance.assigned_companies.add(company)
        instance.set_password(password)
        instance.save()

        email_context = instance
        email_context.password = password
        email_context.wheelhouse_url = f"{self.context['request'].scheme}://{self.context['request'].get_host()}"
        if instance.company.applicationconfiguration.force_sso_login:
            send_user_register_sso_notification(email_context)
        else:
            send_user_register_notification(email_context)

        return instance

    def update(self, instance, validated_data):
        if validated_data.get("email", None):
            if validated_data["email"] != instance.username:
                raise serializers.ValidationError("email and username should be same")

        groups = validated_data.pop("groups", None)
        user_permissions = validated_data.pop("user_permissions", None)

        instance = super().update(instance, validated_data)

        if user_permissions is not None:
            instance.user_permissions.set(user_permissions, clear=True)

        if groups is not None:
            instance.groups.set(groups, clear=True)

        return instance

    def validate(self, attrs):
        super().validate(attrs)

        if self.instance:
            attrs.pop("email", None)
            attrs.pop("username", None)

        if email := attrs.get("email", None):
            if User.objects.filter(email__iexact=email).exists():
                raise serializers.ValidationError("User already exists")

        if username := attrs.get("username", None):
            if User.objects.filter(username__iexact=username).exists():
                raise serializers.ValidationError("User already exists")

        if self.instance:
            app_config = getattr(
                self.instance.company, "applicationconfiguration", None
            )
            if attrs.get("company_id", None):
                attrs["company_id"] = self.instance.company.id
        else:
            app_config = getattr(
                self.context["request"].user.company, "applicationconfiguration", None
            )
            if attrs.get("company_id", None):
                attrs["company_id"] = self.context["request"].user.company.id

        if attrs.get("groups", None):
            current_groups = []
            available_groups = app_config.available_groups.all() if app_config else Group.objects.none()

            for group in attrs["groups"]:
                if x := available_groups.filter(name=group["name"]).first():
                    current_groups.append(x)

            user_groups = self.instance.groups.all() if self.instance else Group.objects.none()
            user_groups = user_groups.exclude(id__in=available_groups.values_list("id", flat=True))

            attrs["groups"] = current_groups + list(user_groups)

        if attrs.get("user_permissions", None):
            available_permissions = (
                app_config.available_permissions.values_list("pk", flat=True)
                if app_config
                else []
            )
            attrs["user_permissions"] = [
                permission.get("id")
                for permission in attrs["user_permissions"]
                if permission.get("id") in available_permissions
            ]

            user_permissions = self.instance.user_permissions.exclude(
                id__in=available_permissions
            ) if self.instance else Permission.objects.none()

            attrs["user_permissions"] += list(user_permissions.values_list("id", flat=True))

        if attrs.get("roles", None):
            current_roles = self.instance.roles if self.instance else []
            available_roles = app_config.available_roles if app_config else []
            available_role_class = [User.Role[item] for item in available_roles] + current_roles
            acceptable_roles = []
            for role in attrs["roles"]:
                if role in available_role_class:
                    acceptable_roles.append(role)
            attrs["roles"] = acceptable_roles

        if attrs.get("assigned_tboss_sites"):
            tboss_company = getattr(self.context["request"].user.company, "terminalboss", None)
            available_tboss_sites = tboss_company.dbreplicationserver_set.all() if tboss_company else []
            acceptable_tboss_sites = []
            for site in attrs["assigned_tboss_sites"]:
                if site in available_tboss_sites:
                    acceptable_tboss_sites.append(site)
            attrs["assigned_tboss_sites"] = acceptable_tboss_sites

        if attrs.get("assigned_railroad_facilities"):
            company = getattr(self.context["request"].user.company, "wheelhouse", None)

            available_railroad_facilities = Facility.objects.filter(
                company=company,
                railroads__isnull=False
            ).distinct()

            acceptable_railroad_facilities = []
            for facility in attrs["assigned_railroad_facilities"]:
                if facility in available_railroad_facilities:
                    acceptable_railroad_facilities.append(facility)

            attrs["assigned_railroad_facilities"] = acceptable_railroad_facilities

        return attrs

    class Meta:
        model = User

        fields = [
            "id",
            "email",
            "username",
            "name",
            "company",
            "assigned_companies",
            "assigned_tboss_sites",
            "assigned_railroad_facilities",
            "telephone",
            "socialaccount_set",
            "ui_config",
            "is_active",
            "is_superuser",
            "is_staff",
            "date_joined",
            "groups",
            "user_permissions",
            "user_access",
            "last_login",
            "last_activity",
            "app_config",
            "roles",
            "certificates",
        ]

        read_only_fields = [
            "company",
            "assigned_companies",
            "is_superuser",
            "is_staff",
            "date_joined",
            "last_login"
        ]

class UserSimplifiedSerializer(serializers.ModelSerializer):
    class Meta:
        model = User

        fields = [
            "id",
            "email",
            "username",
            "name",
            "company",
            "is_active",
        ]

class UserDeviceSerializer(serializers.ModelSerializer):
    class Meta:
        model = UserDevice
        fields = "__all__"

    def validate(self, attrs):
        attrs["user"] = self.context["request"].user
        return attrs

    def create(self, validated_data):
        user_device, created = UserDevice.objects.get_or_create(**validated_data)
        return user_device
    

class UserPopupSerializer(serializers.ModelSerializer):
    class Meta:
        model = UserPopup
        fields = "__all__"
