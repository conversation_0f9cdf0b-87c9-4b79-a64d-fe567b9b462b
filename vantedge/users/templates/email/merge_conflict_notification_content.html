{% extends 'email/email_base_color.mjml'%}
{% block content %}
<mj-text align="center">
  <h2>
    Merge Conflict Affecting Wheelhouse Sync ({{ context.server.name }})<br/><br/>
  </h2>
</mj-text>

<mj-text>
  <p>
    Hello, <br/><br/>

    {%if resolved %}
    This is an urgent email to inform you that there is a <b>merge conflict affecting the TBOSS-WH Sync</b> has now been resolved. Any data records not synced before will now be available across both apps. <br/><br/>
    {% else %}
    This is an email to inform you that there is a <b>merge conflict affecting the TBOSS-WH Sync.</b>
    This means that any data entered or created during this time will not be synced across the two apps.<br/><br/>
    Our team is working to resolve it as soon as possible. We will provide an update when it is rectified.<br/><br/>

    {% endif %}

  Please contact us at <span style="color:#0066ff"><EMAIL></span> if you have any other questions or concerns.<br/><br/>
  Thanks,<br/>
  Wheelhouse & TerminalBOSS team

  </p>
</mj-text>


<mj-text color="#ffffff">
  Instance: <br/>
  Object: {{context.dbreplication_logentry.entity_str_repr}} <br/>
  Origin Server: {{ context.dbreplication_logentry.origin_server}}   <br/>
  Apply to Server: {{ context.server}}   <br/>
  Last Status: {{context.dbreplication_logentry.get_status_display}} <br/>
  Action Type: {{context.dbreplication_logentry.get_action_type_display}} <br/>
  Entry Type: {{context.dbreplication_logentry.get_entry_type_display}} <br/>
  Response: {{context.server_response}}<br/><br/>
</mj-text>
{% endblock %}
{% block footer %}{% endblock %}
