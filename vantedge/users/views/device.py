from rest_framework.viewsets import GenericViewSet
from rest_framework.mixins import CreateModelMixin
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response

from ..models import UserDevice
from ..serializers import UserDeviceSerializer


class UserDeviceView(GenericViewSet, CreateModelMixin):
    permission_classes = [IsAuthenticated]
    queryset = UserDevice.objects.all()
    serializer_class = UserDeviceSerializer

