from django.contrib.auth.models import Permission, Group
from rest_framework.viewsets import ModelViewSet
from rest_framework.pagination import LimitOffsetPagination
from vantedge.users.serializers import PermissionSerializer, GroupSerializer


class GroupViewSet(ModelViewSet):
    serializer_class = GroupSerializer
    queryset = Group.objects.prefetch_related('permissions', 'permissions__content_type').all()

    def get_queryset(self):
        qs = super().get_queryset()
        user = self.request.user

        app_config = getattr(user.company, "applicationconfiguration", None)
        available_groups = app_config.available_groups.all() if app_config else Group.objects.none()

        return qs & available_groups


class PermissionsViewSet(ModelViewSet):
    serializer_class = PermissionSerializer
    pagination_class = LimitOffsetPagination

    def get_queryset(self):
        return Permission.objects.filter(company=self.request.user.company)
