import requests
from allauth.socialaccount.providers.google.views import GoogleOAuth2Adapter
from allauth.socialaccount.providers.microsoft.views import MicrosoftGraphOAuth2Adapter
from dj_rest_auth.registration.views import SocialLoginView, SocialConnectView
from django.conf import settings
from django.contrib import messages
from django.contrib.auth import get_user_model
from django.contrib.auth.mixins import LoginRequiredMixin
from django.contrib.auth.models import Permission, Group

from django.db.models import Prefetch, Subquery, OuterRef
from django.urls import reverse
from django.utils import timezone
from django.views.generic import DetailView, RedirectView, UpdateView
from django_filters import FilterSet, CharFilter
from rest_framework import status
from rest_framework.authentication import TokenAuthentication, exceptions
from rest_framework.authtoken.models import Token
from rest_framework.decorators import action
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.viewsets import ModelViewSet

from vantedge.easyaudit.models import CR<PERSON>DEvent
from vantedge.users.models import Company
from vantedge.users.serializers import UserSerializer, UserListSerializer

User = get_user_model()


class CustomTokenAuthentication(TokenAuthentication):
    def authenticate_credentials(self, key):
        model = self.get_model()
        try:
            token = model.objects.select_related("user").get(key=key)
        except model.DoesNotExist:
            raise exceptions.AuthenticationFailed("Invalid token.")

        if not token.user.is_active:
            raise exceptions.AuthenticationFailed(
                "User inactive. If you have just created this account please wait 24 hours <NAME_EMAIL>."
            )

        return (token.user, token)


class UserFilter(FilterSet):
    email = CharFilter(field_name='email', lookup_expr='icontains')

    class Meta:
        model = User
        fields = [
            "name",
            "email",
            "username",
            "telephone",
            "company_id",
            "company__name",
            "is_active",
            "is_staff",
            "is_superuser",
        ]


class UserViewSet(ModelViewSet):
    serializer_class = UserSerializer
    queryset = User.objects.all()
    filterset_class = UserFilter
    permission_classes = [IsAuthenticated]
    authentication_classes = [CustomTokenAuthentication]

    def get_serializer_class(self):
        if self.action == 'list':
            return UserListSerializer
        return super().get_serializer_class()

    def get_queryset(self):
        qs = super().get_queryset().filter(company=self.request.user.company)
        if self.action == 'list':

            qs= qs.prefetch_related(
                'groups').annotate(
                    last_activity=Subquery(
                        CRUDEvent.objects.filter(user=OuterRef('pk'))
                        .order_by('-datetime')
                        .values('datetime')[:1]
                    ))
        else:
            qs = (
                qs
                .select_related('company', 'company__applicationconfiguration')
                .prefetch_related(
                    'assigned_companies',
                    'assigned_tboss_sites',
                    'assigned_railroad_facilities',
                    'socialaccount_set',
                    'company__edi',
                    'company__reporting',
                    'company__wheelhouse',
                    'company__invoice',
                    'company__track_trace',
                    'company__yard_management',
                    'company__terminalbosscompany',
                    'company__terminalboss',
                    'company__document_reading',
                    'company__schedule',
                    'company__customs',
                    'company__forms',
                    'company__applicationconfiguration',
                    'company__applicationconfiguration__available_permissions',
                    'company__applicationconfiguration__available_groups',
                    'company__applicationconfiguration__available_permissions__content_type',
                    'company__applicationconfiguration__formconfiguration_set',
                    'company__applicationconfiguration__formconfiguration_set__content_type',
                    'company__applicationconfiguration__available_permissions',
                    'company__applicationconfiguration__available_permissions__content_type',
                    Prefetch(
                        'groups',
                        queryset=Group.objects.prefetch_related(
                            Prefetch('permissions', queryset=Permission.objects.select_related('content_type'))
                        )
                    ),
                    Prefetch(
                        'user_permissions',
                        queryset=Permission.objects.select_related('content_type')
                    ),
                )
            )

        if self.request.user.is_superuser:
            return qs
        elif self.request.user.has_perms(["users.add_user", "users.change_user"]):
            return (
                qs.exclude(is_staff=True).exclude(is_superuser=True)
                .exclude(email__icontains="@vantedge.com")
                .exclude(email__icontains="@vantedgelgx.com")
            )
        return qs.filter(id=self.request.user.id)

    def destroy(self, request, pk):
        return Response(status=403)

    @action(detail=False, methods=["get"], permission_classes=[IsAuthenticated])
    def me(self, request):
        serializer = self.get_serializer(request.user)
        return Response(serializer.data)

    @action(detail=False, methods=["get"], permission_classes=[IsAuthenticated])
    def backend_version(self, request):
        backend_version = settings.BACKEND_VERSION
        return Response({"backend_version": backend_version})

    @action(detail=False, methods=['post'], permission_classes=[IsAuthenticated])
    def user_tokens(self, request):
        if (
            (request.user.is_staff and request.user.groups.filter(name='support_admin').exists()) or
            request.user.is_superuser
        ):
            limit = 15
            email = request.data.get('email', None)
            user_tokens = []

            if email:
                qs = Token.objects.select_related('user').filter(user__email__icontains=email)
            else:
                qs = Token.objects.select_related('user').all()

            for token in qs[:limit]:
                user_token = {
                    'email': token.user.email,
                    'token': token.key,
                    'is_active': token.user.is_active,
                }

                user_tokens.append(user_token)

            return Response(user_tokens)

        else:
            return Response(status=status.HTTP_403_FORBIDDEN)

    @action(detail=False, methods=["post"], permission_classes=[IsAuthenticated])
    def change_company(self, request):
        if request.user.is_superuser or request.data["company"] in [
            c["id"] for c in request.user.assigned_companies.all().values("id")
        ]:
            request.user.company = Company.objects.get(id=request.data["company"])
            request.user.save()
            serializer = self.get_serializer(request.user)
            return Response(serializer.data)
        return Response(status=401)

    @action(detail=True, methods=["post"], permission_classes=[IsAuthenticated])
    def set_password(self, request, pk):
        password = request.data.get("params").get("password")
        user = self.get_object()
        company_configuration = user.company.applicationconfiguration
        is_valid, error = company_configuration.meeting_password_setting(password)
        if not is_valid:
            return Response([error], status=status.HTTP_400_BAD_REQUEST)

        user.set_password(password)
        user.password_set_date = timezone.now()
        user.save()
        serializer = self.get_serializer(user)
        return Response(serializer.data)

    @action(detail=False, methods=["post"], permission_classes=[IsAuthenticated])
    def cors_anywhere(self, request):
        return Response(requests.get(request.data["url"], timeout=10).text)

    @action(detail=True, methods=["post"], permission_classes=[IsAuthenticated])
    def regenerate_token(self, request, pk):
        if not (
            self.request.user.is_superuser
            or self.request.user.is_staff
            or self.request.user.has_perms(["users.regenerate_token"])
        ):
            return Response(["Access Denied!"], status=status.HTTP_400_BAD_REQUEST)

        obj = self.get_object()
        new_token = obj.regenerate_token()
        return Response(new_token)


class MicrosoftLogin(SocialLoginView):
    adapter_class = MicrosoftGraphOAuth2Adapter


class MicrosoftConnect(SocialConnectView):
    adapter_class = MicrosoftGraphOAuth2Adapter


class GoogleLogin(SocialLoginView):
    adapter_class = GoogleOAuth2Adapter


class GoogleConnect(SocialConnectView):
    adapter_class = GoogleOAuth2Adapter


# All stuff for password reseting, all copy pasted


def empty_view(request):
    return Response("")


class UserDetailView(LoginRequiredMixin, DetailView):
    model = User
    slug_field = "username"
    slug_url_kwarg = "username"


user_detail_view = UserDetailView.as_view()


class UserUpdateView(LoginRequiredMixin, UpdateView):
    model = User
    fields = ["name"]

    def get_success_url(self):
        return reverse("users:detail", kwargs={"username": self.request.user.username})

    def get_object(self):
        return User.objects.get(username=self.request.user.username)

    def form_valid(self, form):
        messages.add_message(
            self.request, messages.INFO, _("Infos successfully updated")
        )
        return super().form_valid(form)


user_update_view = UserUpdateView.as_view()


class UserRedirectView(LoginRequiredMixin, RedirectView):
    permanent = False

    def get_redirect_url(self):
        return reverse("users:detail", kwargs={"username": self.request.user.username})


user_redirect_view = UserRedirectView.as_view()
