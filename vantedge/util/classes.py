import itertools
from django.db import models


def get_field_by_id(cls_or_qs, pk=None, lookup_field="name", default=""):
    if not pk:
        return default
    if isinstance(cls_or_qs, models.QuerySet):
        qs = cls_or_qs
    elif isinstance(cls_or_qs, models.base.ModelBase):
        qs = cls_or_qs.objects.all()
    else:
        raise TypeError("cls_or_qs must be a QuerySet or a Model")
    try:
        instance = qs.get(pk=pk)
        return getattr(instance, lookup_field)
    except qs.model.DoesNotExist:
        return default


def get_or_default(cls_or_qs, pk=None, default=None):
    if not pk:
        return default
    if isinstance(cls_or_qs, models.QuerySet):
        qs = cls_or_qs
    elif isinstance(cls_or_qs, models.Model):
        qs = cls_or_qs.objects.all()
    else:
        raise TypeError("cls_or_qs must be a QuerySet or a Model")
    try:
        instance = qs.get(pk=pk)
        return instance
    except qs.model.DoesNotExist:
        return default


class ProxyToolsMixin:
    is_leaf = False

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        #NOTE activate this print method for debugging
        # print("Casting to proxy")
        self.cast_to_proxy()
        # print(type(self), self.data_schema)

    @classmethod
    def can_handle(cls, instance):
        return False

    @classmethod
    def leaf_subclasses(cls):
        subclasses = cls.__subclasses__()
        if not subclasses:
            return [cls]
        result = itertools.chain.from_iterable(
            [subclass.leaf_subclasses() for subclass in subclasses]
            + [subclass for subclass in subclasses if subclass.is_leaf]
        )
        return result

    @staticmethod
    def find_proxy_for_instance(instance):
        subclasses = instance.__class__.leaf_subclasses()
        for subcls in subclasses:
            if (
                subcls._meta.proxy
                and hasattr(subcls, "can_handle")
                and subcls.can_handle(instance)
            ):
                return subcls
        return instance.__class__

    def cast_to_proxy(self, to_cls=None):
        if not to_cls:
            to_cls = ProxyToolsMixin.find_proxy_for_instance(self)
        self.__class__ = to_cls

        return self

class ProxyCompanyMixin(ProxyToolsMixin):
    can_handle_company_names = None

    @classmethod
    def can_handle_company(cls, instance):
        return (
            instance.dbreplication_client_company.name in cls.can_handle_company_names
        )

    @classmethod
    def can_handle(cls, instance):
        return cls.can_handle_company(instance)

    @classmethod
    def wh_company(cls):
        if len(cls.can_handle_company_names) != 1:
            raise ValueError(
                "wh_company must have exactly one name in can_handle_companies."
            )
        from vantedge.users.models import Company as WheelhouseCompany

        cls._wh_company = WheelhouseCompany.objects.get(
            name=cls.can_handle_company_names[0]
        )
        return cls._wh_company.terminalboss


class ProxyTicketMixin(ProxyCompanyMixin):
    can_handle_ticket_types = None

    @classmethod
    def can_handle_ticket_type(cls, instance):
        return instance.ticket_type in cls.can_handle_ticket_types

    @classmethod
    def can_handle(cls, instance):
        return cls.can_handle_company(instance) and cls.can_handle_ticket_type(instance)
