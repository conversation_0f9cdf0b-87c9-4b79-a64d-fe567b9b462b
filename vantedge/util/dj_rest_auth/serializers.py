from dj_rest_auth.serializers import LoginSerializer
from django.contrib.auth import get_user_model
from django.utils.translation import gettext_lazy as _
from django.conf import settings
from rest_framework import exceptions

UserModel = get_user_model()

class CustomLoginSerializer(LoginSerializer):
    def validate(self, attrs):
        username = attrs.get("username")
        email = attrs.get("email")
        password = attrs.get("password")

        user = UserModel.objects.filter(username=email).first()
        if user and user.company and user.company.applicationconfiguration.force_sso_login:
            msg = _("Please login through SSO")
            raise exceptions.ValidationError(msg)

        user = self.get_auth_user(username, email, password)

        if not user:
            msg = _("Unable to log in with provided credentials.")
            raise exceptions.ValidationError(msg)

        # Did we get back an active user?
        self.validate_auth_user_status(user)

        # If required, is the email verified?
        if "dj_rest_auth.registration" in settings.INSTALLED_APPS:
            self.validate_email_verification_status(user, email=email)

        attrs["user"] = user
        return attrs
