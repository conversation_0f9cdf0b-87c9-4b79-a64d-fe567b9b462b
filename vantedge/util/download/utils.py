from django.core.files.storage import default_storage
from django.core.files.base import ContentFile
from io import BytesIO
from uuid import uuid4

def get_path(name, ext):
    return f"downloads/{name}.{ext}"

def save_as_uuid_download(f, ext, root=None):
    # get BytesIO as file and save it in storage and return the path
    name = str(uuid4())
    if root:
        path = f"{root}/{name}.{ext}"
    else:
        path = get_path(name, ext)
    f.seek(0)
    file = ContentFile(f.read())
    file_name = default_storage.save(path, file)
    return default_storage.url(file_name)
