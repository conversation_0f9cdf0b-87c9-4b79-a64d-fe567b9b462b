import json

import orj<PERSON>
from rest_framework.renderers import <PERSON><PERSON><PERSON><PERSON>

from .encoder import <PERSON><PERSON><PERSON><PERSON>nco<PERSON>
from .util import camelize


class VantedgeJSONRenderer(BaseRenderer):
    # def render(self, data, *args, **kwargs):

    media_type = "application/json"
    format = "json"
    options = 0

    default = VantedgeEncoder.static_default

    def render(self, data, media_type=None, renderer_context=None):
        """
        Serializes Python objects to JSON.
        :param data: The response data, as set by the Response() instantiation.
        :param media_type: If provided, this is the accepted media type, of the
                `Accept` HTTP header.
        :param renderer_context: If provided, this is a dictionary of contextual
                information provided by the view. By default this will include
                the following keys: view, request, response, args, kwargs
        :return: bytes() representation of the data encoded to UTF-8
        """
        # preprocessed_data = parse(data)  # Remove the parse, which does nothing.
        preprocessed_data = data
        renderer_context = renderer_context or {}

        if "default_function" not in renderer_context:
            default = self.default
        else:
            default = renderer_context["default_function"]

        # If `indent` is provided in the context, then pretty print the result.
        # E.g. If we're being called by RestFramework's BrowsableAPIRenderer.
        indent = renderer_context.get("indent")
        if False and (indent is None or "application/json" in media_type):
            serialized = orjson.dumps(
                preprocessed_data, default=default, option=self.options
            )
        else:
            encoder_class = renderer_context.get(
                "django_encoder_class", VantedgeEncoder
            )
            serialized = json.dumps(preprocessed_data, indent=indent, cls=encoder_class)
        return serialized


class VantedgeCamelJSONRenderer(BaseRenderer):
    # def render(self, data, *args, **kwargs):

    media_type = "application/json"
    format = "json"
    options = 0

    default = VantedgeEncoder.static_default

    def render(self, data, media_type=None, renderer_context=None):
        """
        Serializes Python objects to JSON.
        :param data: The response data, as set by the Response() instantiation.
        :param media_type: If provided, this is the accepted media type, of the
                `Accept` HTTP header.
        :param renderer_context: If provided, this is a dictionary of contextual
                information provided by the view. By default this will include
                the following keys: view, request, response, args, kwargs
        :return: bytes() representation of the data encoded to UTF-8
        """
        preprocessed_data = camelize(data)
        renderer_context = renderer_context or {}

        if "default_function" not in renderer_context:
            default = self.default
        else:
            default = renderer_context["default_function"]

        # If `indent` is provided in the context, then pretty print the result.
        # E.g. If we're being called by RestFramework's BrowsableAPIRenderer.
        indent = renderer_context.get("indent")
        if False and (indent is None or "application/json" in media_type):
            serialized = orjson.dumps(
                preprocessed_data, default=default, option=self.options
            )
        else:
            encoder_class = renderer_context.get(
                "django_encoder_class", VantedgeEncoder
            )
            serialized = json.dumps(preprocessed_data, indent=indent, cls=encoder_class)
        return serialized


# class VantedgeBrowseableAPIRenderer(BrowsableAPIRenderer):
#     def get_default_renderer(self, view):
#         return VantedgeJSONRenderer()
