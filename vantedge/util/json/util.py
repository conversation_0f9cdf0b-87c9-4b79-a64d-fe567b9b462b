import re
from collections import OrderedDict

from django.core.files import File
from django.http import QueryDict
from django.utils.datastructures import MultiValueDict
from django.utils.encoding import force_str
from django.utils.functional import Promise

from rest_framework.utils.serializer_helpers import ReturnDict
from pandas import DataFrame
from typing import Any, Dict, Type, Optional, Union
from pydantic import BaseModel, ValidationError as PydanticValidationError
from django.core.exceptions import ValidationError


camelize_re = re.compile(r"[a-z0-9]?_[a-z0-9]")


def underscore_to_camel(match):
    group = match.group()
    if len(group) == 3:
        return group[0] + group[2].upper()
    else:
        return group[1].upper()


def camelize(data, **options):
    # Handle lazy translated strings.
    ignore_fields = options.get("ignore_fields") or ()
    if isinstance(data, Promise):
        data = force_str(data)
    if isinstance(data, BaseModel):
        data = data.dict()

    if isinstance(data, dict):
        if isinstance(data, ReturnDict):
            new_dict = ReturnDict(serializer=data.serializer)
        else:
            new_dict = OrderedDict()
        for key, value in data.items():
            if isinstance(key, Promise):
                key = force_str(key)
            if isinstance(key, str) and "_" in key:
                new_key = re.sub(camelize_re, underscore_to_camel, key)
            else:
                new_key = key
            if key not in ignore_fields and new_key not in ignore_fields:
                new_dict[new_key] = camelize(value, **options)
            else:
                new_dict[new_key] = value
        return new_dict
    if is_iterable(data) and not isinstance(data, str):
        return [camelize(item, **options) for item in data]
    return data


def parse(data, **options):
    # Handle lazy translated strings.
    ignore_fields = options.get("ignore_fields") or ()
    if isinstance(data, Promise):
        data = force_str(data)
    if isinstance(data, BaseModel):
        data = data.dict()

    if isinstance(data, dict):
        if isinstance(data, ReturnDict):
            new_dict = ReturnDict(serializer=data.serializer)
        else:
            new_dict = OrderedDict()
        for key, value in data.items():
            if isinstance(key, Promise):
                key = force_str(key)

            new_key = key
            if key not in ignore_fields and new_key not in ignore_fields:
                new_dict[new_key] = parse(value, **options)
            else:
                new_dict[new_key] = value
        return new_dict
    if is_iterable(data) and not isinstance(data, str):
        return [parse(item, **options) for item in data]
    return data


def get_underscoreize_re(options):
    if options.get("no_underscore_before_number"):
        pattern = r"([a-z0-9]|[A-Z]?(?=[A-Z](?=[a-z])))([A-Z])"
    else:
        pattern = r"([a-z0-9]|[A-Z]?(?=[A-Z0-9](?=[a-z0-9]|$)))([A-Z]|(?<=[a-z])[0-9](?=[0-9A-Z]|$)|(?<=[A-Z])[0-9](?=[0-9]|$))"
    return re.compile(pattern)


def camel_to_underscore(name, **options):
    underscoreize_re = get_underscoreize_re(options)
    return underscoreize_re.sub(r"\1_\2", name).lower()


def _get_iterable(data):
    if isinstance(data, QueryDict):
        return data.lists()
    else:
        return data.items()


def underscoreize(data, **options):
    ignore_fields = options.get("ignore_fields") or ()
    if isinstance(data, dict):
        new_dict = {}
        if type(data) == MultiValueDict:
            new_data = MultiValueDict()
            for key, value in data.items():
                new_data.setlist(camel_to_underscore(key, **options), data.getlist(key))
            return new_data
        for key, value in _get_iterable(data):
            if isinstance(key, str):
                new_key = camel_to_underscore(key, **options)
            else:
                new_key = key

            if key not in ignore_fields and new_key not in ignore_fields:
                new_dict[new_key] = underscoreize(value, **options)
            else:
                new_dict[new_key] = value

        if isinstance(data, QueryDict):
            new_query = QueryDict(mutable=True)
            for key, value in new_dict.items():
                new_query.setlist(key, value)
            return new_query
        return new_dict
    if is_iterable(data) and not isinstance(data, (str, File)):
        return [underscoreize(item, **options) for item in data]

    return data


def is_iterable(obj):
    try:
        iter(obj)
    except TypeError:
        return False
    else:
        return True


def camelize_str(data: str) -> str:
    return re.sub(camelize_re, underscore_to_camel, data)


# ============  DATAFRAME CAMELIZE  =========
def camelize_df(df: DataFrame) -> DataFrame:
    result = df.rename(columns=dict([(c, camelize_str(c)) for c in df.columns]))
    return result


# Validate JSON vs Pydantic and convert to Django-style validation errors.
def clean_json_field(
    data: Dict[str, Any],
    schema: Optional[Type[BaseModel]],
    return_dict: bool = True
) -> Union[Dict[str, Any], BaseModel]:
    """Clean and validate JSON data using a Pydantic model.

    Args:
        data: The JSON data to validate
        schema: The Pydantic model to use for validation
        return_dict: Whether to return a dict (True) or Pydantic model (False)

    Returns:
        The validated and possibly transformed data, either as a dict or Pydantic model

    Raises:
        ValidationError: If the data is invalid according to the Pydantic model
    """
    # Skip validation if schema is not a Pydantic model
    if schema is None or schema == dict or not hasattr(schema, "__fields__"):
        return data

    try:
        # Validate and transform data using the Pydantic model
        validated_data = schema(**data)
        return validated_data.dict() if return_dict else validated_data
    except PydanticValidationError as e:
        # Convert Pydantic validation errors to Django validation errors
        errors = {}
        for error in e.errors():
            field = error["loc"][0]
            message = error["msg"]
            if field not in errors:
                errors[field] = []
            errors[field].append(message)

        if errors:
            raise ValidationError(errors)
