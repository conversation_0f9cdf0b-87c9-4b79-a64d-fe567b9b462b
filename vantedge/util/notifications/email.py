from django_q.tasks import async_task
from vant_email.email import send_email as vant_send_email


def send_email(to, subject, template_name, context, cc=None):
    vant_send_email(
        to if isinstance(to, list) else [to],
        subject,
        template_name=template_name,
        context=context,
        cc=cc
    )


def async_send_email(to, subject, template_name, context, cc=None):
    async_task(
        send_email,
        to,
        subject,
        template_name,
        context,
        cc=cc,
        task_name=f"notification email {to}"[:100],
    )


def send_user_expiry_notification(user, days_remaining):
    async_send_email(
        user.username,
        "Password Expiry Notification",
        "user_expiry_notification",
        {"user": user, "days_remaining": days_remaining},
    )


def send_user_register_notification(context):
    async_send_email(
        context.email,
        "Welcome To Wheelhouse",
        "user_register_notification",
        {"context": context}
    )


def send_user_register_sso_notification(context):
    async_send_email(
        context.email,
        "Wheelhouse Sign In Using SSO",
        "user_register_sso_notification",
        {"context": context}
    )


def send_merge_conflict_notification(users, context, resolved):
    async_send_email(users, "Merge Conflict", "merge_conflict_notification", {"context": context, "resolved": resolved})


def send_invoice_approval(emails, context):
    async_send_email(
        emails,
        "Invoice Approval Request",
        "ticket_invoice_approval",
        {"context": context}
    )


def send_invoice_approval_responded(user, context):
    async_send_email(
        [user],
        "Invoice Approval Response",
        "ticket_invoice_approval_response",
        {"context": context}
    )


def send_invoice_payment(emails, context, cc):
    async_send_email(
        emails,
        "Invoice Payment Request",
        "ticket_invoice_payment",
        {"context": context},
        cc=cc
    )


def send_customs_ticket_status_update(emails, context, cc):
    async_send_email(
        emails,
        f"Ticket {context['ticket'].code} has new status",
        "customs_status_change",
        context,
        cc)
