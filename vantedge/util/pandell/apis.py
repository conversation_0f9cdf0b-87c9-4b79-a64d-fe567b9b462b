from django.conf import settings

import base64
from requests import request
from urllib.parse import urljoin

TIMEOUT = 60

class Pandell(object):
    API_LIST = {
        "Invoices": {
            "Attachment": {
                "url": "/api/v1/invoices/file",
                "method": "POST"
            },
            "Create": {
                "url": "/api/v1/invoices",
                "method": "POST",
                "headers": { "Content-Type": "application/xml" }
            }
        }
    }

    def __init__(self, client_company):
        self.client_company = client_company

    def get_base_url(self):
        base_url = settings.BASE_URL
        # Debug is false on gip servers
        if settings.DEBUG or ("gip" in base_url) or ("plain" in base_url):
            return "https://test-jobutrax.pandell.com/"
        else:
            return "https://jobutrax.pandell.com/"

    def get_token(self):
        return self.client_company.data.pandel_token

    def create_url(self, url):
        base_url = self.get_base_url()
        return urljoin(base_url, url)

    def create_invoice(self, pidx_data):
        api_dict = self.API_LIST["Invoices"]["Create"]
        return self.send_api(api_dict, body=pidx_data)

    def send_invoice_attachment(self, pidx_id, name, attachment_file):
        api_dict = self.API_LIST["Invoices"]["Attachment"]
        image = base64.b64encode(attachment_file.read())

        body = {
            "pidx_id": pidx_id,
            "name": name,
            "image": image
        }

        return self.send_api(api_dict, body)

    def send_api(self, api_dict, body=None, params=None):
        headers = {
            "Authorization": f"Bearer {self.get_token()}",
            "accept": "application/json"
        }

        if api_dict.get("headers"):
            headers.update(api_dict["headers"])

        return request(
            url=self.create_url(api_dict["url"]),
            method=api_dict["method"],
            data=body,
            headers=headers
            )

def test():
    from vantedge.util.pidx.main import PIDX
    from vantedge.util.pandell.apis import Pandell
    from vantedge.util.pidx.schemas.data import InvoiceItem, InvoiceProperties
    invoice = InvoiceProperties(
        InvoiceNumber= "1",
        InvoiceDate= "2024-07-11",
        SellerDUNS= "*********",
        BuyerDUNS= "*********",
        JobLocationIdentifier= "well\location",
        FileName= "sample.csv",
        FileType= "application/csv"
    )

    sample_line_number = InvoiceItem(
        LineItemNumber= "1",
        Quantity= "2",
        UnitOfMeasureCode= "Item",
        LineItemDescription= "haha ha ha",
        FieldTicketNumber= "1",
        FieldTicketDate= "2024-04-01",
        MonetaryAmount= "12",
        TaxRate= "0",
        TaxMonetaryAmount= "0",
        AFENumber="1234",
        CostCenterNumber="",
        Major="123",
        Minor="456"
    )

    pidx = PIDX(invoice, [sample_line_number])
    pidx.generate()
    pandel = Pandell("ASfd")
    result = pandel.create_invoice(pidx.to_string())
    print(result.text)

