from lxml import objectify, etree
from .schemas.data import InvoiceProperties, InvoiceItem

def remove_objectify_ns(el):
    for elem in el.iter():
        if isinstance(elem, objectify.ObjectifiedElement):
            # Remove the objectify-specific attributes
            elem.attrib.pop('{http://codespeak.net/lxml/objectify/pytype}pytype', None)
            objectify.deannotate(elem, cleanup_namespaces=True)

class PIDX(object):
    def __init__(self, invoice, invoice_items, template_path="vantedge/util/pidx/xmls/pidx_v1_62.xml"):
        self.template_path = template_path
        self.invoice = invoice
        self.invoice_items = invoice_items
        self.xml = None
        self.line_item_file_io = None

    def start_xml(self):
        with open("vantedge/util/pidx/xmls/invoice_line_item.xml") as f:
            self.line_item_file_io = f.read()

        with open(self.template_path) as f:
            self.xml = objectify.fromstring(f.read())

    def create_line_item(self, line_item_data):
        xml_line_item = objectify.fromstring(self.line_item_file_io)
        xml_line_item.LineItemNumber = line_item_data.LineItemNumber
        xml_line_item.InvoiceQuantity.Quantity = line_item_data.Quantity
        xml_line_item.InvoiceQuantity.UnitOfMeasureCode = line_item_data.UnitOfMeasureCode
        xml_line_item.LineItemInformation.LineItemDescription = line_item_data.LineItemDescription
        xml_line_item.FieldTicketInformation.FieldTicketNumber = line_item_data.FieldTicketNumber
        xml_line_item.FieldTicketInformation.FieldTicketDate = line_item_data.FieldTicketDate
        xml_line_item.Pricing.UnitPrice.MonetaryAmount = line_item_data.MonetaryAmount
        xml_line_item.Tax.TaxRate = line_item_data.TaxRate
        xml_line_item.Tax.TaxAmount.MonetaryAmount = line_item_data.MonetaryAmount

        if line_item_data.CostCenterNumber:
            xml_line_item.ReferenceInformation[1].ReferenceNumber.set("referenceInformationIndicator", "CostCenter")
            xml_line_item.ReferenceInformation[1].ReferenceNumber = line_item_data.CostCenterNumber
            del xml_line_item.ReferenceInformation[0]
        else:
            xml_line_item.ReferenceInformation[0].ReferenceNumber.set("referenceInformationIndicator", "AFENumber")
            xml_line_item.ReferenceInformation[0].ReferenceNumber = line_item_data.AFENumber
            del xml_line_item.ReferenceInformation[1]

        if line_item_data.Major and line_item_data.Minor:
            xml_line_item.ReferenceInformation[1].ReferenceNumber.set("referenceInformationIndicator", "OperatorGeneralLedgerCode")
            xml_line_item.ReferenceInformation[1].ReferenceNumber = f"{line_item_data.Major}.{line_item_data.Minor}"

        return xml_line_item


    def generate(self):
        self.start_xml()

        self.xml.InvoiceProperties.InvoiceNumber = self.invoice.InvoiceNumber
        self.xml.InvoiceProperties.InvoiceDate = self.invoice.InvoiceDate
        self.xml.InvoiceProperties.PartnerInformation[0].PartnerIdentifier = self.invoice.SellerDUNS
        self.xml.InvoiceProperties.PartnerInformation[0].PartnerIdentifier.set("partnerIdentifierIndicator", "DUNSNumber")
        self.xml.InvoiceProperties.PartnerInformation[1].PartnerIdentifier = self.invoice.BuyerDUNS
        self.xml.InvoiceProperties.PartnerInformation[1].PartnerIdentifier.set("partnerIdentifierIndicator", "DUNSNumber")
        self.xml.InvoiceProperties.JobLocationInformation.JobLocationIdentifier = self.invoice.JobLocationIdentifier
        self.xml.InvoiceProperties.Attachment.FileName = self.invoice.FileName
        self.xml.InvoiceProperties.Attachment.FileType = self.invoice.FileType

        for line_item in self.invoice_items:
            self.xml.InvoiceDetails.append(self.create_line_item(line_item))

    def to_string(self):
        # Return the XML as a string
        remove_objectify_ns(self.xml)
        return etree.tostring(self.xml, pretty_print=True, xml_declaration=True, encoding='UTF-8').decode('UTF-8')

    def save_to_file(self, file_path):
        # Save the XML to a file
        tree = etree.ElementTree(self.xml)
        tree.write(file_path, xml_declaration=False, default_namespace=False, encoding='UTF-8')


def test():
    invoice = InvoiceProperties(
        InvoiceNumber= "1",
        InvoiceDate= "2024-07-11",
        SellerDUNS= "*********",
        BuyerDUNS= "*********",
        JobLocationIdentifier= "well\location",
        FileName= "sample.csv",
        FileType= "application/csv"
    )

    sample_line_number = InvoiceItem(
        LineItemNumber= "1",
        Quantity= "2",
        UnitOfMeasureCode= "Item",
        LineItemDescription= "haha ha ha",
        FieldTicketNumber= "1",
        FieldTicketDate= "2024-04-01",
        MonetaryAmount= "12",
        TaxRate= "0",
        TaxMonetaryAmount= "0",
        AFENumber="1234",
        CostCenterNumber="",
        Major="123",
        Minor="456"
    )

    pidx = PIDX(invoice, [sample_line_number])
    pidx.generate()
    print(pidx.to_string())


