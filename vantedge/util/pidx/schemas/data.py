from dataclasses import dataclass


@dataclass
class InvoiceProperties:
    InvoiceNumber: str
    InvoiceDate: str
    SellerDUNS: str
    BuyerDUNS: str
    JobLocationIdentifier: str
    FileName: str
    FileType: str


@dataclass
class InvoiceItem:
    LineItemNumber: int
    Quantity: int
    UnitOfMeasureCode: str
    LineItemDescription: str
    FieldTicketNumber: str
    FieldTicketDate: str
    MonetaryAmount: str
    TaxRate: str
    TaxMonetaryAmount: str
    AFENumber: str
    CostCenterNumber: str
    Major: str
    Minor: str
