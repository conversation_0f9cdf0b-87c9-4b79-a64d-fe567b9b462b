from django.apps import AppConfig
from vant_ticket.signals import ticket_saved

def link_ticket_to_railcar(sender, **kwargs):
    from django_q.tasks import async_task
    from vantedge.wheelhouse.models.transactions import TbossWheelhouseRailCar

    if (sender.created_by_server and
        sender.created_by_server.additional_data.get("wheelhouse_facility_id") and
        isinstance(sender.data, dict)):

        async_task(
            TbossWheelhouseRailCar.populate_from,
            sender,
            task_name=f"populate tboss wheelhouse railcar {sender.id}"
        )


class WheelhouseConfig(AppConfig):
    name = "vantedge.wheelhouse"

    def ready(self) -> None:
        ticket_saved.connect(link_ticket_to_railcar)
        return super().ready()
