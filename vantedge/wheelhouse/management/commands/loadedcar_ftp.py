from django.conf import settings
from django.core.mail import EmailMessage

from io import StringIO
import csv
from dateutil.parser import parse
from pint import UnitRegistry
import logging

from vantedge.edi.comm.edi import EDI, SFTPMixin
from vantedge.edi.models import EdiDocument
from vantedge.users.models import Company

from vantedge.wheelhouse.models import LoadedRailCar, Product, Facility

environment = "debug" if settings.DEBUG else "production"

ureg = UnitRegistry()

logger = logging.getLogger(__name__)
# units are pound/kilogram/tonne US_liquid_gallon/barrel/litre fahrenheit/celsius
# if item need to fill with default remove it from column_mapping

COMPANY_DATA = {
    "Independent Energy Corp": {
        "KUTT": {
            "config_name": "KUTT",
            "source": "FTP Plain(KUTT)",
            "notify_address": ["<EMAIL>"],
            "credentials": {
                "production": {
                    "server": "ftp.plainsmidstream.com",
                    "username": "VantEdge",
                    "password": "l0NRKO4V7ss8H9ELyO2m",
                    "connection_type": "ftp",
                    "remote_locations": {"car_data": "/", "pattern": "*.csv"},
                },
                "debug": {
                    "server": "ftp.plainsmidstream.com",
                    "username": "VantEdge-Test",
                    "password": "xWYWcc@Q'obT&Og2022!",
                    "connection_type": "ftp",
                    "remote_locations": {"car_data": "/", "pattern": "*.csv"},
                },
            },
        }
    }
}

LOADED_CARS_CONFIG = [
    {
        "name": "KUTT",
        "map_header": True,
        "product_map": {"VGO": "Marine Fuel (VGO)"},
        "column_mapping": {
            "product": {"column": 4, "header": "Product Code"},
            "car_mark": {"column": 4, "header": "Car Initials", "unit": None},
            "car_number": {"column": 4, "header": "Car Number", "unit": None},
            "seal_number_1": {"column": 4, "header": "Seal No.", "unit": None},
            "load_date": {"column": 4, "header": "Load Date", "unit": None},
            "outage": {
                "column": 4,
                "header": "Estimated Gauge Inches",
                "unit": "inch",
                "target_unit": "inch",
                "ndigits": 2,
            },
            "density": {
                "column": 4,
                "header": "gravity",
                "unit": "kg/litre",
                "target_unit": "kg/litre",  # to convert it from str to value
            },
            "volume": {
                "column": 4,
                "header": "Net Quantity",
                "unit": "US_liquid_gallon",
                "target_unit": "litre",
                "ndigits": 0,
            },
            "volume_outage": {
                "column": 4,
                "header": "Outage Gal",
                "unit": "US_liquid_gallon",
                "target_unit": "litre",
                "ndigits": 0,
            },
            "load_temperature": {
                "column": 4,
                "header": "Load Temperature",
                "unit": "fahrenheit",
                "target_unit": "celsius",
                "ndigits": 1,
            },
            "shell_capacity": {
                "column": 4,
                "header": "Total Liq. Capacity",
                "unit": "US_liquid_gallon",
                "target_unit": "litre",
                "ndigits": 0,
            },
        },
    }
]


class FTPConnection(EDI):
    def __init__(self, credentials):
        self._connection = None
        self._config = credentials[environment]
        self._config_type = environment


class FTPSConnection(SFTPMixin, EDI):
    def __init__(self, credentials):
        self._connection = None
        self._config = credentials
        self._config_type = environment


def parse_unit(unit_txt):
    if len(unit_txt.split("/")) == 2:
        a, b = unit_txt.split("/")
        return getattr(ureg, a) / getattr(ureg, b)
    else:
        return getattr(ureg, unit_txt)


def read_line_data(config, map_header, line_data):
    fields = [
        "car_mark",
        "car_number",
        "seal_number_1",
        "load_date",
        "density",
        "volume",
        "volume_outage",
        "outage",
        "load_temperature",
        "temperature_correction_factor",
        "shell_capacity",
    ]

    column_mapping = config["column_mapping"]

    data = {}

    field_mapping_product = column_mapping["product"]
    product_name = (
        line_data[field_mapping_product["header"]]
        if map_header
        else line_data[field_mapping_product["column"]]
    )
    product = Product.objects.get(name=config["product_map"].get(product_name))
    data["product"] = product

    for field in fields:
        field_mapping = column_mapping.get(field, None)
        if field_mapping is None:

            if field == "temperature_correction_factor":
                data["temperature_correction_factor"] = 1

            elif field == "volume_outage":
                data["volume_outage"] = 0

            elif field == "shell_capacity":
                data["shell_capacity"] = data["volume"] + data["volume_outage"]

        else:
            if map_header:
                field_value = line_data[field_mapping["header"]]
            else:
                field_value = line_data[field_mapping["column"]]

            if field_mapping.get("unit", None):

                unit = parse_unit(field_mapping["unit"])
                target_unit = parse_unit(field_mapping["target_unit"])

                field_value = ureg.Quantity(float(field_value), unit)
                field_value.ito(target_unit)
                field_value = field_value.magnitude

            # if no ndigit do not round it, mainly for density
            if (
                isinstance(field_value, float)
                and field_mapping.get("ndigits", None) is not None
            ):
                field_value = round(field_value, ndigits=field_mapping.get("ndigits"))

            if field == "load_date":
                field_value = parse(field_value).date()

            if field == "seal_number_1":
                field_value = field_value.replace(" ", "")
                seal_numbers = field_value.split(",")
                for i in range(len(seal_numbers)):
                    data[f"seal_number_{i+1}"] = seal_numbers[i]
            else:
                data[field] = field_value

    return data


def fetch_data_facility(config):
    print(f"connecting {config['source']} through {environment} environment")
    files = []
    credentials = config["credentials"]
    if credentials[environment]["connection_type"] == "ftp":
        Connection = FTPConnection(credentials)
    else:
        Connection = FTPSConnection(credentials)

    with Connection as edi:
        for filename, data in edi.fetch_folder(
            transaction="car_data",
            delete=True,
            pattern=credentials[environment]["remote_locations"]["pattern"],
        ):
            files.append((filename, data))

    return files


def create_cars(cars):
    result = {"created": [], "updated": [], "skipped": []}
    for car in cars:
        matched_cars = LoadedRailCar.objects.filter(
            load_date=car.load_date,
            car_mark=car.car_mark,
            car_number=car.car_number,
            current_location=car.current_location,
        )

        if matched_cars:
            for matched_car in matched_cars:
                if (
                    matched_car.rail_shipment
                    and matched_car.rail_shipment.status != "open"
                ):
                    result["skipped"].append(matched_car)
                else:
                    changes = []
                    fields = [
                        "shell_capacity",
                        "seal_number_1",
                        "volume_outage",
                        "load_temperature",
                        "temperature_correction_factor",
                        "density",
                        "volume",
                        "outage",
                    ]

                    for field in fields:
                        if getattr(matched_car, field) != getattr(car, field):
                            changes.append(
                                {
                                    field: (
                                        getattr(matched_car, field),
                                        getattr(car, field),
                                    )
                                }
                            )
                            setattr(matched_car, field, getattr(car, field))

                    if changes:
                        matched_car.save()

                    result["updated"].append(dict(car=matched_car, changes=changes))
        else:
            car.save()
            result["created"].append(car)

    return result


def parse_data(company, facility, data):
    cars = []
    company_data = COMPANY_DATA[company.name]
    facility_data = company_data[facility.name]

    config = next(
        (
            item
            for item in LOADED_CARS_CONFIG
            if item["name"] == facility_data["config_name"]
        )
    )

    f = StringIO(data)

    if config["map_header"]:
        lines = csv.DictReader(f)
    else:
        lines = csv.reader(f)

    for line_data in lines:
        try:
            car_data = read_line_data(config, config["map_header"], line_data)
        except Exception as e:
            raise Exception(
                f"line data: {line_data} failed to parse with error:{str(e)}"
            )

        car_data["current_location"] = facility
        cars.append(LoadedRailCar(**car_data))

    result = create_cars(cars)
    return result


def send_notification(
    filename, data, notify_address, company, facility, created_cars, error
):
    if error:
        notify_address.extend(["<EMAIL>"])

        subject = f"Process file '{filename}' for facility '{facility.name}' failed!"
    else:
        total_cars = (
            len(created_cars["created"])
            + len(created_cars["updated"])
            + len(created_cars["skipped"])
        )
        subject = f"{filename} processed!"

    body = f"""
    Hello, <br/><br/>

    <b>Filename:</b> {filename}<br/>
    <b>Company:</b> {company.name}<br/>
    <b>Facility:</b> {facility.name}<br/>
    <b>Error:</b> {error}<br/>
    <b>Cars:</b>
    <ul>
        <li><b>Create by data: </b>{len(created_cars.get("created"))} cars(s) </li>
    """
    body += "<ul>"
    for car in created_cars.get("created"):
        body += f"<li>{car.car_mark}{car.car_number} (SystemID: {car.id})</li>"
    body += "</ul>"

    body += f"""<li><b>Updated by data: </b>{len(created_cars.get("updated"))} cars(s)</li>"""
    body += "<ul>"
    for car in created_cars.get("updated"):
        # changes = ""
        # changes += f"{car['changes']['field']}: {old}->{new}" for car["changes"]
        body += f"<li>{car['car'].car_mark}{car['car'].car_number} changes:{car['changes']}</li>"
    body += "</ul>"

    body += f"""<li style="color:Tomato;"><b>Skipped importing (already billed):</b>{len(created_cars.get("skipped"))} cars(s) </li>"""
    body += """<ul style="color:Tomato;">"""
    for car in created_cars.get("skipped"):
        body += f"<li>{car.car_mark}{car.car_number} (SystemID: {car.id})</li>"
    body += "</ul>"

    body += """
        </ul>
        Thank you.<br/>

        This is automated email.<br/>
        """

    to = notify_address

    email = EmailMessage(
        subject=subject, body=body, to=to
    )
    email.content_subtype = "html"
    email.attach(filename=f"{filename}", content=data, mimetype="text/csv")

    try:
        email.send(fail_silently=False)
    except:
        logger.exception(
            f"Could not send the report at this time. task is import car data for {facility.name}."
        )


def save_to_edi(edi_company, source, data, filename):
    return EdiDocument.objects.create(
        company=edi_company,
        source=source,
        doc_type="Car Load",
        data=data,
        name=filename,
    )


def iterate_companies():
    for company_name in COMPANY_DATA:
        company = Company.objects.get(name=company_name)
        wheelhouse_company = company.wheelhouse

        for facility_name in COMPANY_DATA[company_name]:
            facility = Facility.objects.get(
                company=wheelhouse_company, name=facility_name
            )
            config = COMPANY_DATA[company_name][facility_name]
            files = fetch_data_facility(config)

            for filename, data in files:
                print(f"importing loaded car data from {filename}")
                processed = False
                error = None
                created_cars = {"created": [], "updated": [], "skipped": []}

                edi_doc = save_to_edi(company.edi, config["source"], data, filename)
                try:
                    created_cars = parse_data(company, facility, data)
                except Exception as e:
                    edi_doc.processed = False
                    error = str(e)
                else:
                    processed = True
                    edi_doc.processed = True

                edi_doc.save(update_fields=["processed"])

                send_notification(
                    filename,
                    data,
                    config["notify_address"],
                    company,
                    facility,
                    created_cars,
                    error,
                )

