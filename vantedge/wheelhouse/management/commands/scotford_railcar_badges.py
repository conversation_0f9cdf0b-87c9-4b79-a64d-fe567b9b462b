
from django.core.management import BaseCommand
from django.db.models import Count

from vantedge.wheelhouse.models import LoadedRailCar


class Command(BaseCommand):
    help = "Updates Contact Preferences"

    def handle(self, *args, **options):
        lrcs = LoadedRailCar.objects.filter(
            current_location__name="Scotford",
        )
        count = 0
        for loaded_car in lrcs:
            if loaded_car.carposition_set.count() > 1:
                loaded_car.save()
                count = count + 1
                print("save", loaded_car)
        print("ticket save count: ", count)
