from django.core.management.base import BaseCommand, CommandError
from vantedge.wheelhouse.models import Facility, Riser, WheelhouseCompany, ERP, Railroad, Product, Contract, Route, \
    Address, Codes,LoadedRailCar, RailShipment, RailCar
from vantedge.users.models import User, Company
from vantedge.trackandtrace.models import TrackAndTraceCompany
from vantedge.edi.models import EdiCompany
from mimesis import Person
from random import randint


class Command(BaseCommand):
    help = 'Seeds Database'

    # def add_arguments(self, parser):
    #     parser.add_argument('poll_ids', nargs='+', type=int)

    def handle_railroadss(self, clear=False):
        if clear:
            Railroad.objects.all().delete()

        cn, created = Railroad.objects.get_or_create(name='CN')
        cpr, created = Railroad.objects.get_or_create(name='CPRS')
        bn, created = Railroad.objects.get_or_create(name='BNSF')

    def handle_companies(self, clear=False):
        if clear:
            Company.objects.all().delete()
        company, created = Company.objects.get_or_create(name='Keyera Partnership')
        if created:
            company.wheelhouse_company = WheelhouseCompany.objects.create(company=company,
                data={'address': Address(name='Keyera Partnership',
                                         address1='55239 RR 220',
                                         city='Fort Saskatchewan', state='AB',
                                         country='Canada', postal_code='T2P 2V7'),
                      'erp': {
                          '2.1': ERP(erap='2-0010-059', erap_telephone='************',
                                     ccn='17168',
                                     ccn_telephone='************')}
                      })
        company.save()

        company, created = Company.objects.get_or_create(name='Pieridae')
        if created:
            company.wheelhouse_company = WheelhouseCompany.objects.create(company=company,
                data={'address': Address(name='Pieridae AB Production Ltd',
                                         address1='SW 1 2 SEC 24 TWP 25 RG 5 W5',
                                         city='Cochrane', state='AB',
                                         country='Canada', postal_code='T2P 2V7'),
                      'erp': {
                          '2.1': ERP(erap='2-0010-059', erap_telephone='************',
                                     ccn='17168',
                                     ccn_telephone='************')}
                      })
        company.save()


        company, created = Company.objects.get_or_create(name='Colley West Shipping')
        if created:
            company.wheelhouse_company = WheelhouseCompany.objects.create(company=company,
                data={'address': Address(name='Colley West Shipping',
                                         address1='Suite 3900 - 205 5 Ave SW',
                                         city='Calgary', state='AB',
                                         country='Canada', postal_code='T2P 2V7'),
                      'erp': {
                          '2.1': ERP(erap='2-0010-059', erap_telephone='************', ccn='17168',
                                     ccn_telephone='************')}
                      })
        company.save()

        company, created = Company.objects.get_or_create(name='Petrogas Inc')
        if created:
            company.wheelhouse_company = WheelhouseCompany.objects.create(company=company, data={
                'address': Address(name='Petrogas Inc.', address1='Suite 3900 - 205 5 Ave SW',
                                   city='Calgary', state='AB',
                                   country='Canada',
                                   postal_code='T2P 2V7'),
                'erp': {
                    '2.1': ERP(erap='2-0010-059', erap_telephone='************', ccn='17168',
                               ccn_telephone='************')}
            })
        company.save()

    def handle_products(self, clear=False):
        if clear:
            Product.objects.all().delete()
        propane_data = {'codes': Codes(stcc='4905421',
                                       harmonized_code='2711-12-0000', hazmat_class='2.1',
                                       un_code='UN1075', cas='74-98-6')}

        butane_data = {'codes': Codes(stcc='4905423',
                                      harmonized_code='2711-13-0000', hazmat_class='2.1',
                                      un_code='UN1011', cas='106-97-8')}

        propane_odorized, created = Product.objects.get_or_create(name='(Odorized) HD5 Propane', data=propane_data)
        propane_non_odorized, created = Product.objects.get_or_create(name='(Non-Odorized) HD5 Propane',
                                                                      data=propane_data)
        hd10_odor, created = Product.objects.get_or_create(name='(Odorized) HD10 Propane', data=propane_data)
        hd10_no_odor, created = Product.objects.get_or_create(name='(Odorized) Off Spec Propane', data=propane_data)
        Product.objects.get_or_create(name='Butane (C4)', data=butane_data)
        Product.objects.get_or_create(name='Purity Butane (NC4)', data=butane_data)
        fc4, created = Product.objects.get_or_create(name='Field Grade Butane (FC4)', data=butane_data)
        Product.objects.get_or_create(name='Iso Butane (IC4)', data=butane_data)
        Product.objects.get_or_create(name='Refinery Grade Butane (RGB)', data=butane_data)

        sulphur, created = Product.objects.get_or_create(name='Sulphur')
        wheat, created = Product.objects.get_or_create(name='Wheat')
        canola, created = Product.objects.get_or_create(name='Canola')

    def handle_facilities(self, clear=False):
        if clear:
            Facility.objects.all().delete()

        if Facility.objects.count():
            return

        ft_sask = Facility.objects.create(name='Fort Saskatchewan', legal_description='04-24-055-22W4',
                                          operator=Company.objects.get(name='Colley West Shipping'),
                                          emergency_phone='+17802784828',
                                          data={'address': Address(name='Petrogas Fort Saskatchewan',
                                                                   address1='12011 125th St',
                                                                   city='Fort Saskatchewan', state='AB',
                                                                   postal_code='T8L 4G2',
                                                                   country='Canada')})

        ft_sask.products.add(Product.objects.get(name='(Odorized) HD5 Propane'),
                             Product.objects.get(name='(Non-Odorized) HD5 Propane'))
        ft_sask.railroads.add(Railroad.objects.get(name='CN'), Railroad.objects.get(name='BNSF'))

        shantz = Facility.objects.create(name='Shantz', legal_description='04-24-055-22W4',
                                         operator=Company.objects.get(name='Colley West Shipping'),
                                         emergency_phone='+17802784828',
                                         data={'address': Address(name='Shantz AB',
                                                                  address1='12011 125th St',
                                                                  city='Fort Saskatchewan', state='AB',
                                                                  postal_code='T8L 4G2',
                                                                  country='Canada')})

        shantz.products.add(Product.objects.get(name='Sulphur'))
        shantz.railroads.add(Railroad.objects.get(name='CN'), Railroad.objects.get(name='CPRS'))

        vancouver = Facility.objects.create(name='Vancouver', legal_description='04-24-055-22W4',
                                            operator=Company.objects.get(name='Colley West Shipping'),
                                            emergency_phone='+17802784828',
                                            data={'address': Address(name='vancouver AB',
                                                                     address1='12011 125th St',
                                                                     city='Fort Saskatchewan', state='BC',
                                                                     postal_code='T8L 4G2',
                                                                     country='Canada')})

        vancouver.products.add(Product.objects.get(name='Sulphur'))
        vancouver.railroads.add(Railroad.objects.get(name='CN'), Railroad.objects.get(name='CPRS'))

        cochrane = Facility.objects.create(name='Bassano', operator=Company.objects.get(name='Pieridae'),
                                           emergency_phone='+13603841701',
                                           data={
                                               'address': Address(name='Cochrane Pieridae', address1='Cochrane',
                                                                  city='Cochrane',
                                                                  state='AB',
                                                                  postal_code='', country='CA')})

        keyera_ft_sask = Facility.objects.create(name='FT SASK', operator=Company.objects.get(name='Keyera Partnership'),
                                                 emergency_phone='+13603841701',
                                                 data={
                                                     'address': Address(name='Keyera Ft Sask',
                                                                        address1='12011 125th St',
                                                                        city='Fort Saskatchewan', state='AB',
                                                                        postal_code='T8L 4G2',
                                                                        country='Canada')})

        ferndale = Facility.objects.create(name='Ferndale', operator=Company.objects.get(name='Petrogas Inc'),
                                           emergency_phone='+13603841701',
                                           data={
                                               'address': Address(name='Petrogas Ferndale', address1='4100 Unick Road',
                                                                  city='Ferndale',
                                                                  state='Washington',
                                                                  postal_code='98248', country='USA')})

        bakerfield = Facility.objects.create(name='Bakersfield', operator=Company.objects.get(name='Petrogas Inc'),
                                             emergency_phone='+13603841701',
                                             data={'address': Address(name='Petrogas Bakefield',
                                                                      address1='4100 Unick Road', city='Bakersfield',
                                                                      state='California',
                                                                      postal_code='98248', country='USA')})

        shafter = Facility.objects.create(name='Shafter', operator=Company.objects.get(name='Petrogas Inc'),
                                          emergency_phone='+13603841701',
                                          data={'address': Address(name='Petrogas Shafter', address1='4100 Unick Road',
                                                                   city='Shafter',
                                                                   state='California',
                                                                   postal_code='98248', country='USA')})

        ferndale.products.add(Product.objects.get(name='(Odorized) HD5 Propane'),
                              Product.objects.get(name='(Non-Odorized) HD5 Propane'))
        ferndale.railroads.add(Railroad.objects.get(name='BNSF'))

    def handle_shipments(self, clear=False):
        from datetime import date
        import calendar
        import arrow

        if clear:
            LoadedRailCar.objects.all().delete()
            RailShipment.objects.all().delete()

        for i in range(1, 10):
            # m = randint(5, 6)
            m = i

        seller = Company.objects.get(name='Pieridae')
        buyer = Company.objects.get(name='Keyera Partnership')
        product = Product.objects.get(name='Sulphur')
        origin = Facility.objects.get(name='Shantz')
        destination = Facility.objects.get(name='Vancouver')

        contract = Contract.objects.create(seller=seller, buyer=buyer, buyer_number=f'KP{i:03}',
                                           seller_number=f'PS{i:03}',
                                           volume_type=Contract.VOLUME_TYPE.Fixed, total_volume=randint(30, 31),
                                           unit=Contract.UNIT.Car, product=product,
                                           fob=Contract.FOB.Origin,
                                           start=date(2020, m, 1),
                                           end=date(2020, m, calendar.monthrange(2020, m)[-1]))

        route = Route.objects.create(contract=contract, origin=origin, destination=destination, routing='CP',
                                     cars=contract.total_volume)
        rail_shipment = RailShipment.objects.create(route=route)
        rail_shipment.reset_defaults()

        arw = arrow.utcnow()

        if m < 10:
            rail_shipment.status = \
                ['open', 'bol-sent', 'bol-acknowledged', 'bol-rejected', 'bol-accepted', 'received', 'closed'][m % 7]
        rail_shipment.ship_date = arw.shift(days=-m).date()
        rail_shipment.arrive_date = arw.shift(days=-m + 8).date()
        rail_shipment.save()
        for i in range(randint(2, 10)):
            car = LoadedRailCar.objects.create(rail_shipment=rail_shipment, car_mark='GATX',
                                               car_number=randint(100000, 300000),
                                               shell_capacity=randint(120000, 130000),
                                               volume_outage=randint(225, 275),
                                               volume=125000 - 250 + randint(0, 500),
                                               temperature_correction_factor=randint(98, 110) / 100.0,
                                               load_temperature=randint(150, 250) / 10.0)

    def handle_users(self, clear=False):
        if clear:
            User.objects.all().delete()

        meridian, c = Company.objects.get_or_create(name='Meridian')
        EdiCompany.objects.get_or_create(company=meridian, railinc_fleet='MERIDIAN')
        TrackAndTraceCompany.objects.get_or_create(company=meridian)

        if not User.objects.filter(username='<EMAIL>').count():
            User.objects.create_superuser('<EMAIL>', name='Glenn Moore', email='<EMAIL>', password='v')
        glenn = User.objects.get(username='<EMAIL>')
        glenn.company = Company.objects.get(name='Meridian')
        glenn.save()

        if not User.objects.filter(username='<EMAIL>').count():
            User.objects.create_superuser('<EMAIL>', name='Alex Gierus', email='<EMAIL>', password='v')
        alex = User.objects.get(username='<EMAIL>')
        alex.company = Company.objects.get(name='Meridian')
        alex.save()
        from rest_framework.authtoken.models import Token
        Token.objects.get_or_create(user=alex, key='01fdd517e2ff350ad4a93d820976dc48545bfc79')

        usoil, cr = Company.objects.get_or_create(name='US Oil')
        EdiCompany.objects.get_or_create(company=usoil, railinc_fleet='USOIL')
        TrackAndTraceCompany.objects.get_or_create(company=usoil)

        if not User.objects.filter(username='<EMAIL>').count():
            User.objects.create_superuser('<EMAIL>', name='Abigail Nicewander',
                                               email='<EMAIL>', password='wheelhouse')
        an = User.objects.get(username='<EMAIL>')
        an.company = usoil
        an.save()

        TrackAndTraceCompany.objects.get_or_create(company=usoil)

        if not User.objects.filter(username='<EMAIL>').count():
            User.objects.create_superuser('<EMAIL>', name='Abigail Nicewander',
                                               email='<EMAIL>', password='wheelhouse')
        an = User.objects.get(username='<EMAIL>')
        an.company = usoil
        an.save()

    def handle(self, *args, **options):
        # address = Address('en')

        self.handle_companies()
        self.handle_users()
        self.handle_products()
        self.handle_railroadss()
        self.handle_facilities()
        self.handle_shipments()
