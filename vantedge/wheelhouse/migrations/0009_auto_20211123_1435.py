# Generated by Django 3.2.5 on 2021-11-23 21:35

from django.db import migrations
import functools
import schema_field.fields
import vantedge.trackandtrace.detention


class Migration(migrations.Migration):

    dependencies = [("wheelhouse", "0008_auto_20210928_1111")]

    operations = [
        migrations.RemoveField(model_name="contract", name="detention"),
        migrations.AddField(
            model_name="contract",
            name="detention_data",
            field=schema_field.fields.JSONSchemedField(
                default=vantedge.trackandtrace.detention.DetentionData,
                encoder=functools.partial(
                    schema_field.fields.JSONSchemedEncoder,
                    *(),
                    **{"schema": (vantedge.trackandtrace.detention.DetentionData,)}
                ),
                schema=(vantedge.trackandtrace.detention.DetentionData,),
            ),
        ),
    ]
