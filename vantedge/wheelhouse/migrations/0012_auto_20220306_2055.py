# Generated by Django 3.2.7 on 2022-03-07 03:55

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('wheelhouse', '0011_auto_20220306_2055'),
        ('trackandtrace', '0029_auto_20220306_2055'),
    ]

    operations = [
        migrations.DeleteModel(
            name='RailShippingPattern',
        ),
        migrations.DeleteModel(
            name='ShippingPattern',
        ),
        migrations.AddField(
            model_name='railshippingschedule',
            name='consignee',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='consignee_schedule_set', to='wheelhouse.party'),
        ),
        migrations.AddField(
            model_name='railshippingschedule',
            name='customs',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='customs_schedule_set', to='wheelhouse.party'),
        ),
        migrations.AddField(
            model_name='railshippingschedule',
            name='destination',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='in_schedule_set', to='wheelhouse.facility'),
        ),
        migrations.AddField(
            model_name='railshippingschedule',
            name='freight_payer',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='freight_payer_schedule_set', to='wheelhouse.party'),
        ),
        migrations.AddField(
            model_name='railshippingschedule',
            name='origin',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='out_schedule_set', to='wheelhouse.facility'),
        ),
        migrations.AddField(
            model_name='railshippingschedule',
            name='purchase_contract',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='purchase_schedules', to='wheelhouse.contract'),
        ),
        migrations.AddField(
            model_name='railshippingschedule',
            name='sale_contract',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='sale_schedules', to='wheelhouse.contract'),
        ),
        migrations.AddField(
            model_name='railshippingschedule',
            name='shipper',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='shipper_schedule_set', to='wheelhouse.party'),
        ),
    ]
