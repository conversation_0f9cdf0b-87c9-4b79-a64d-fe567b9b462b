# Generated by Django 3.2.7 on 2022-03-09 17:23

from django.db import migrations, models
import django.db.models.deletion
import functools
import schema_field.fields
import vantedge.wheelhouse.models.pricing


class Migration(migrations.Migration):

    dependencies = [("wheelhouse", "0012_auto_20220306_2055")]

    operations = [
        migrations.CreateModel(
            name="ProductType",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=50)),
                ("description", models.CharField(max_length=100)),
                (
                    "configuration",
                    schema_field.fields.JSONSchemedField(
                        default=vantedge.wheelhouse.models.pricing.ProductTypeConfiguration,
                        encoder=functools.partial(
                            schema_field.fields.JSONSchemedEncoder,
                            *(),
                            **{
                                "schema": (
                                    vantedge.wheelhouse.models.pricing.ProductTypeConfiguration,
                                )
                            }
                        ),
                        schema=(
                            vantedge.wheelhouse.models.pricing.ProductTypeConfiguration,
                        ),
                    ),
                ),
            ],
        ),
        migrations.AddField(
            model_name="product",
            name="product_type",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                to="wheelhouse.producttype",
            ),
        ),
    ]
