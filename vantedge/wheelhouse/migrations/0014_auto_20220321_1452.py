# Generated by Django 3.2.11 on 2022-03-21 20:52

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('wheelhouse', '0013_product_type'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='railshipment',
            name='route',
        ),
        migrations.RemoveField(
            model_name='route',
            name='contract',
        ),
        migrations.AddField(
            model_name='railshipment',
            name='custom_broker',
            field=models.ForeignKey(default=1, on_delete=django.db.models.deletion.CASCADE, related_name='ship_rail_custom_broker', to='wheelhouse.party'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='railshipment',
            name='reference_number',
            field=models.CharField(default=1, max_length=30),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='railshipment',
            name='routing',
            field=models.CharField(default='', max_length=50),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='railshipment',
            name='schedule',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='rail_shipments', to='wheelhouse.railshippingschedule'),
        ),
        migrations.AlterField(
            model_name='loadedrailcar',
            name='car_mark',
            field=models.CharField(max_length=4),
        ),
        migrations.AlterField(
            model_name='loadedrailcar',
            name='car_number',
            field=models.CharField(max_length=8),
        ),
        migrations.AlterField(
            model_name='loadedrailcar',
            name='product',
            field=models.ForeignKey(default=13, on_delete=django.db.models.deletion.CASCADE, related_name='movements', to='wheelhouse.product'),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name='loadedrailcar',
            name='unload_date',
            field=models.DateField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='railshipment',
            name='arrive_date',
            field=models.DateField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='railshipment',
            name='consignee',
            field=models.ForeignKey(default=1, on_delete=django.db.models.deletion.CASCADE, related_name='ship_rail_consignee', to='wheelhouse.party'),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name='railshipment',
            name='freight_charges',
            field=models.CharField(blank=True, choices=[('Collect', 'Collect'), ('Prepaid', 'Prepaid'), ('Rule11', 'Rule11')], default='Prepaid', max_length=10),
        ),
        migrations.AlterField(
            model_name='railshipment',
            name='freight_payer',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='ship_rail_freight_payer', to='wheelhouse.party'),
        ),
        migrations.AlterField(
            model_name='railshipment',
            name='shipper',
            field=models.ForeignKey(default=1, on_delete=django.db.models.deletion.CASCADE, related_name='ship_rail_shipper', to='wheelhouse.party'),
            preserve_default=False,
        ),
    ]
