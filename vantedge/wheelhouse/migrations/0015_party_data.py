# Generated by Django 3.2.11 on 2022-03-24 15:25

from django.db import migrations
import functools
import schema_field.fields
import vantedge.wheelhouse.models.company


class Migration(migrations.Migration):

    dependencies = [("wheelhouse", "0014_auto_20220321_1452")]

    operations = [
        migrations.AddField(
            model_name="party",
            name="data",
            field=schema_field.fields.JSONSchemedField(
                default=vantedge.wheelhouse.models.company.PartyData,
                encoder=functools.partial(
                    schema_field.fields.JSONSchemedEncoder,
                    *(),
                    **{"schema": (vantedge.wheelhouse.models.company.PartyData,)}
                ),
                schema=(vantedge.wheelhouse.models.company.PartyData,),
            ),
        )
    ]
