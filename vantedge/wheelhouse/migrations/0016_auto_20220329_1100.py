# Generated by Django 3.2.11 on 2022-03-29 17:00

from django.db import migrations, models
import django.db.models.deletion
import functools
import schema_field.fields
import vantedge.wheelhouse.models.location
import vantedge.wheelhouse.models.product


class Migration(migrations.Migration):

    dependencies = [("wheelhouse", "0015_party_data")]

    operations = [
        migrations.AlterField(
            model_name="loadedrailcar",
            name="current_location",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="cars",
                to="wheelhouse.facility",
            ),
        ),
        migrations.AlterField(
            model_name="loadedrailcar",
            name="product",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.PROTECT,
                related_name="movements",
                to="wheelhouse.product",
            ),
        ),
        migrations.AlterField(
            model_name="loadedrailcar",
            name="rail_shipment",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="cars",
                to="wheelhouse.railshipment",
            ),
        ),
        migrations.AlterField(
            model_name="location",
            name="data",
            field=schema_field.fields.JSONSchemedField(
                default=vantedge.wheelhouse.models.location.LocationData,
                encoder=functools.partial(
                    schema_field.fields.JSONSchemedEncoder,
                    *(),
                    **{"schema": (vantedge.wheelhouse.models.location.LocationData,)}
                ),
                schema=(vantedge.wheelhouse.models.location.LocationData,),
            ),
        ),
        migrations.AlterField(
            model_name="product",
            name="data",
            field=schema_field.fields.JSONSchemedField(
                default=vantedge.wheelhouse.models.product.ProductData,
                encoder=functools.partial(
                    schema_field.fields.JSONSchemedEncoder,
                    *(),
                    **{"schema": (vantedge.wheelhouse.models.product.ProductData,)}
                ),
                schema=(vantedge.wheelhouse.models.product.ProductData,),
            ),
        ),
        migrations.AlterField(
            model_name="product",
            name="product_type",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                to="wheelhouse.producttype",
            ),
        ),
        migrations.AlterField(
            model_name="railshipment",
            name="consignee",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.PROTECT,
                related_name="ship_rail_consignee",
                to="wheelhouse.party",
            ),
        ),
        migrations.AlterField(
            model_name="railshipment",
            name="custom_broker",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="ship_rail_custom_broker",
                to="wheelhouse.party",
            ),
        ),
        migrations.AlterField(
            model_name="railshipment",
            name="destination",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="ship_rail_in",
                to="wheelhouse.facility",
            ),
        ),
        migrations.AlterField(
            model_name="railshipment",
            name="freight_payer",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="ship_rail_freight_payer",
                to="wheelhouse.party",
            ),
        ),
        migrations.AlterField(
            model_name="railshipment",
            name="origin",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="ship_rail_out",
                to="wheelhouse.facility",
            ),
        ),
        migrations.AlterField(
            model_name="railshipment",
            name="product",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                to="wheelhouse.product",
            ),
        ),
        migrations.AlterField(
            model_name="railshipment",
            name="shipper",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.PROTECT,
                related_name="ship_rail_shipper",
                to="wheelhouse.party",
            ),
        ),
    ]
