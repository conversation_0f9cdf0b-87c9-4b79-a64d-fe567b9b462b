# Generated by Django 3.2.11 on 2022-04-01 19:21

from django.db import migrations, models
import django.db.models.deletion
import functools
import schema_field.fields
import vantedge.wheelhouse.models.schema


class Migration(migrations.Migration):

    dependencies = [("wheelhouse", "0017_auto_20220330_1752")]

    operations = [
        migrations.RemoveField(model_name="railshipment", name="edi"),
        migrations.AddField(
            model_name="railshipment",
            name="care_party",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="shiprail_care_party_set",
                to="wheelhouse.party",
            ),
        ),
        migrations.AddField(
            model_name="railshipment",
            name="custom_broker_us",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="shiprail_custom_broker_us_set",
                to="wheelhouse.party",
            ),
        ),
        migrations.AddField(
            model_name="railshipment",
            name="notify_party",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="shiprail_notify_party_set",
                to="wheelhouse.party",
            ),
        ),
        migrations.AddField(
            model_name="railshipment",
            name="pickup_party",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="shiprail_pickup_party_set",
                to="wheelhouse.party",
            ),
        ),
        migrations.AddField(
            model_name="railshipment",
            name="ultimate_consignee",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="shiprail_ultimate_consignee_set",
                to="wheelhouse.party",
            ),
        ),
        migrations.AlterField(
            model_name="railshipment",
            name="consignee",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.PROTECT,
                related_name="shiprail_consignee_set",
                to="wheelhouse.party",
            ),
        ),
        migrations.AlterField(
            model_name="railshipment",
            name="custom_broker",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="shiprail_custom_broker_set",
                to="wheelhouse.party",
            ),
        ),
        migrations.AlterField(
            model_name="railshipment",
            name="data",
            field=schema_field.fields.JSONSchemedField(
                default=vantedge.wheelhouse.models.schema.RailShipmentDataSchema,
                encoder=functools.partial(
                    schema_field.fields.JSONSchemedEncoder,
                    *(),
                    **{
                        "schema": (
                            vantedge.wheelhouse.models.schema.RailShipmentDataSchema,
                        )
                    }
                ),
                schema=(vantedge.wheelhouse.models.schema.RailShipmentDataSchema,),
            ),
        ),
        migrations.AlterField(
            model_name="railshipment",
            name="freight_payer",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="shiprail_freight_payer_set",
                to="wheelhouse.party",
            ),
        ),
        migrations.AlterField(
            model_name="railshipment",
            name="shipper",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.PROTECT,
                related_name="shiprail_shipper_set",
                to="wheelhouse.party",
            ),
        ),
    ]
