# Generated by Django 3.2.11 on 2022-04-05 17:23

import django.contrib.postgres.fields
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('wheelhouse', '0020_auto_20220404_1153'),
    ]

    operations = [
        migrations.AddField(
            model_name='railshipment',
            name='exporter',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.PROTECT, related_name='shiprail_exporter_set', to='wheelhouse.party'),
        ),
        migrations.AddField(
            model_name='railshipment',
            name='importer',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.PROTECT, related_name='shiprail_importer_set', to='wheelhouse.party'),
        ),
        migrations.AddField(
            model_name='railshippingschedule',
            name='exporter',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='exporter_schedule_set', to='wheelhouse.party'),
        ),
        migrations.AddField(
            model_name='railshippingschedule',
            name='importer',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='importer_schedule_set', to='wheelhouse.party'),
        ),
        migrations.AlterField(
            model_name='party',
            name='party_types',
            field=django.contrib.postgres.fields.ArrayField(base_field=models.CharField(choices=[('shipper', 'Shipper'), ('consignee', 'Consignee'), ('freight_payer', 'Freight Payer'), ('customs_broker', 'Customs Broker'), ('care_party', 'Care Party'), ('pickup_party', 'Pickup Party'), ('notify_party', 'Notify Party'), ('importer_exporter', 'Importer/Exporter')], max_length=50), blank=True, default=list, null=True, size=None),
        ),
        migrations.AlterField(
            model_name='railshipment',
            name='care_party',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.PROTECT, related_name='shiprail_care_party_set', to='wheelhouse.party'),
        ),
        migrations.AlterField(
            model_name='railshipment',
            name='custom_broker_us',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.PROTECT, related_name='shiprail_custom_broker_us_set', to='wheelhouse.party'),
        ),
        migrations.AlterField(
            model_name='railshipment',
            name='notify_party',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.PROTECT, related_name='shiprail_notify_party_set', to='wheelhouse.party'),
        ),
        migrations.AlterField(
            model_name='railshipment',
            name='pickup_party',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.PROTECT, related_name='shiprail_pickup_party_set', to='wheelhouse.party'),
        ),
        migrations.AlterField(
            model_name='railshipment',
            name='ultimate_consignee',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.PROTECT, related_name='shiprail_ultimate_consignee_set', to='wheelhouse.party'),
        ),
    ]
