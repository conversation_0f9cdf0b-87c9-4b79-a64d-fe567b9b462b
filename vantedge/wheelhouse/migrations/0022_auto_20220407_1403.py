# Generated by Django 3.2.11 on 2022-04-07 20:03

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('wheelhouse', '0021_auto_20220405_1123'),
    ]

    operations = [
        migrations.AlterField(
            model_name='railshipment',
            name='care_party',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='shiprail_care_party_set', to='wheelhouse.party'),
        ),
        migrations.AlterField(
            model_name='railshipment',
            name='custom_broker_us',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='shiprail_custom_broker_us_set', to='wheelhouse.party'),
        ),
        migrations.AlterField(
            model_name='railshipment',
            name='exporter',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='shiprail_exporter_set', to='wheelhouse.party'),
        ),
        migrations.AlterField(
            model_name='railshipment',
            name='importer',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='shiprail_importer_set', to='wheelhouse.party'),
        ),
        migrations.AlterField(
            model_name='railshipment',
            name='notify_party',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='shiprail_notify_party_set', to='wheelhouse.party'),
        ),
        migrations.AlterField(
            model_name='railshipment',
            name='pickup_party',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='shiprail_pickup_party_set', to='wheelhouse.party'),
        ),
        migrations.AlterField(
            model_name='railshipment',
            name='ultimate_consignee',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='shiprail_ultimate_consignee_set', to='wheelhouse.party'),
        ),
    ]
