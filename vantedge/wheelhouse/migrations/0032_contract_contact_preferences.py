# Generated by Django 3.2.11 on 2022-06-08 19:06

from django.db import migrations
import functools
import schema_field.fields
import vantedge.wheelhouse.models.schema


class Migration(migrations.Migration):

    dependencies = [("wheelhouse", "0031_auto_20220531_1351")]

    operations = [
        migrations.AddField(
            model_name="contract",
            name="contact_preferences",
            field=schema_field.fields.JSONSchemedField(
                default=vantedge.wheelhouse.models.schema.RailShipmentContactPreference,
                encoder=functools.partial(
                    schema_field.fields.JSONSchemedEncoder,
                    *(),
                    **{
                        "schema": (
                            vantedge.wheelhouse.models.schema.RailShipmentContactPreference,
                        )
                    }
                ),
                schema=(
                    vantedge.wheelhouse.models.schema.RailShipmentContactPreference,
                ),
            ),
        )
    ]
