# Generated by Django 3.2.13 on 2022-08-25 17:33

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('wheelhouse', '0035_auto_20220822_1356'),
    ]

    operations = [
        migrations.AddField(
            model_name='railshippingschedule',
            name='shipment_method_of_payment',
            field=models.CharField(blank=True, choices=[('11', 'Rule 11 Shipment'), ('BP', 'Paid by Buyer'), ('CA', 'Advance Collect'), ('CC', 'Collect'), ('CD', 'Collect on Delivery'), ('CF', 'Collect, Freight Credited Back to Customer'), ('DE', 'Per Contract'), ('DF', 'Defined by Buyer and Seller'), ('FO', 'FOB Port of Call'), ('HP', 'Half Prepaid'), ('MX', 'Mixed'), ('NC', 'Service Freight, No Charge'), ('NR', 'Non Revenue'), ('PA', 'Advance Prepaid'), ('PB', 'Customer Pick-up/Backhaul'), ('PC', 'Prepaid but Charged to Customer'), ('PD', 'Prepaid by Processor'), ('PE', 'Prepaid and Summary Bill'), ('PL', 'Prepaid Local, Collect Outstate'), ('PO', 'Prepaid Only'), ('PP', 'Prepaid (by Seller)'), ('PS', 'Paid by Seller'), ('PU', 'Pickup'), ('RC', 'Return Container Freight Paid by Customer'), ('RF', 'Return Container Freight Free'), ('RS', 'Return Container Freight Paid by Supplier'), ('TP', 'Third Party Pay'), ('WC', 'Weight Condition')], max_length=2, null=True),
        ),
    ]
