# Generated by Django 3.2.7 on 2022-09-12 19:52

import django.contrib.postgres.fields
from django.db import migrations, models
import django.db.models.deletion
import functools
import schema_field.fields
import vantedge.wheelhouse.models.company
import vantedge.wheelhouse.models.location
import vantedge.wheelhouse.models.product
import vantedge.wheelhouse.models.validations


class Migration(migrations.Migration):

    dependencies = [("wheelhouse", "0038_alter_loadedrailcar_waybill")]

    operations = [
        migrations.AddField(
            model_name="railshipment",
            name="loading_party",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="shiprail_loading_party_set",
                to="wheelhouse.party",
            ),
        ),
        migrations.AddField(
            model_name="railshippingschedule",
            name="loading_party",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="loading_party_schedule_set",
                to="wheelhouse.party",
            ),
        ),
        migrations.AlterField(
            model_name="location",
            name="data",
            field=schema_field.fields.JSONSchemedField(
                default=vantedge.wheelhouse.models.location.LocationData,
                encoder=functools.partial(
                    schema_field.fields.JSONSchemedEncoder,
                    *(),
                    **{"schema": (vantedge.wheelhouse.models.location.LocationData,)}
                ),
                schema=(vantedge.wheelhouse.models.location.LocationData,),
                validators=[
                    vantedge.wheelhouse.models.validations.validate_facility_data
                ],
            ),
        ),
        migrations.AlterField(
            model_name="party",
            name="data",
            field=schema_field.fields.JSONSchemedField(
                default=vantedge.wheelhouse.models.company.PartyData,
                encoder=functools.partial(
                    schema_field.fields.JSONSchemedEncoder,
                    *(),
                    **{"schema": (vantedge.wheelhouse.models.company.PartyData,)}
                ),
                schema=(vantedge.wheelhouse.models.company.PartyData,),
                validators=[vantedge.wheelhouse.models.validations.validate_party_data],
            ),
        ),
        migrations.AlterField(
            model_name="party",
            name="party_types",
            field=django.contrib.postgres.fields.ArrayField(
                base_field=models.CharField(
                    choices=[
                        ("shipper", "Shipper"),
                        ("consignee", "Consignee"),
                        ("freight_payer", "Freight Payer"),
                        ("customs_broker", "Customs Broker"),
                        ("care_party", "Care Party"),
                        ("pickup_party", "Pickup Party"),
                        ("notify_party", "Notify Party"),
                        ("loading_party", "Loading Party"),
                        ("importer_exporter", "Importer/Exporter"),
                    ],
                    max_length=50,
                ),
                blank=True,
                default=list,
                null=True,
                size=None,
            ),
        ),
        migrations.AlterField(
            model_name="product",
            name="data",
            field=schema_field.fields.JSONSchemedField(
                default=vantedge.wheelhouse.models.product.ProductData,
                encoder=functools.partial(
                    schema_field.fields.JSONSchemedEncoder,
                    *(),
                    **{"schema": (vantedge.wheelhouse.models.product.ProductData,)}
                ),
                schema=(vantedge.wheelhouse.models.product.ProductData,),
                validators=[
                    vantedge.wheelhouse.models.validations.validate_product_data
                ],
            ),
        ),
    ]
