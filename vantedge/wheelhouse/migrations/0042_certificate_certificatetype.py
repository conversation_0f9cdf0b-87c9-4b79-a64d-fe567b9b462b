# Generated by Django 3.2.7 on 2022-10-03 21:34

import django.contrib.postgres.fields
from django.db import migrations, models
import django.db.models.deletion
import functools
import schema_field.fields
import vantedge.wheelhouse.models.schema


class Migration(migrations.Migration):

    dependencies = [
        ("contenttypes", "0002_remove_content_type_name"),
        ("wheelhouse", "0041_auto_20220920_1504"),
    ]

    operations = [
        migrations.CreateModel(
            name="CertificateType",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=100)),
                ("enabled", models.BooleanField(default=False)),
                (
                    "data",
                    schema_field.fields.JSONSchemedField(
                        default=vantedge.wheelhouse.models.schema.CertificateData,
                        encoder=functools.partial(
                            schema_field.fields.JSONSchemedEncoder,
                            *(),
                            **{
                                "schema": (
                                    vantedge.wheelhouse.models.schema.CertificateData,
                                )
                            }
                        ),
                        schema=(vantedge.wheelhouse.models.schema.CertificateData,),
                    ),
                ),
                (
                    "scopes",
                    django.contrib.postgres.fields.ArrayField(
                        base_field=models.CharField(
                            blank=True,
                            choices=[("party", "Party")],
                            max_length=50,
                            null=True,
                        ),
                        blank=True,
                        default=list,
                        size=None,
                    ),
                ),
                ("code", models.CharField(blank=True, max_length=50, null=True)),
                (
                    "effective_date",
                    models.DateTimeField(blank=True, null=True, verbose_name="start"),
                ),
                (
                    "expiry_date",
                    models.DateTimeField(blank=True, null=True, verbose_name="end"),
                ),
                (
                    "company",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="certificate_types",
                        to="wheelhouse.wheelhousecompany",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="Certificate",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "start_date",
                    models.DateTimeField(blank=True, null=True, verbose_name="start"),
                ),
                (
                    "end_date",
                    models.DateTimeField(blank=True, null=True, verbose_name="end"),
                ),
                ("object_id", models.PositiveIntegerField(blank=True, null=True)),
                (
                    "certificate_type",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="certificates",
                        to="wheelhouse.certificatetype",
                    ),
                ),
                (
                    "content_type",
                    models.ForeignKey(
                        limit_choices_to={
                            "app_label__in": ["wheelhouse"],
                            "model__in": ["party"],
                        },
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="content_type_certificates",
                        to="contenttypes.contenttype",
                    ),
                ),
            ],
        ),
    ]
