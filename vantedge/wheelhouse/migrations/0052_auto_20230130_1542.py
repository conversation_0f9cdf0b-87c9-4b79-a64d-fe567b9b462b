# Generated by Django 3.2.7 on 2023-01-30 22:42

import django.contrib.postgres.fields
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('wheelhouse', '0051_auto_20230123_1348'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='railshipment',
            name='loading_party',
        ),
        migrations.RemoveField(
            model_name='railshippingschedule',
            name='loading_party',
        ),
        migrations.AddField(
            model_name='railshipment',
            name='beneficial_owner',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='shiprail_beneficial_owner_set', to='wheelhouse.party'),
        ),
        migrations.AddField(
            model_name='railshipment',
            name='notify_party_2',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='shiprail_notify_2_party_set', to='wheelhouse.party'),
        ),
        migrations.AddField(
            model_name='railshippingschedule',
            name='beneficial_owner',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='beneficial_owner_schedule_set', to='wheelhouse.party'),
        ),
        migrations.AddField(
            model_name='railshippingschedule',
            name='notify_party_2',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.PROTECT, related_name='notify_party_2_schedule_set', to='wheelhouse.party'),
        ),
        migrations.AlterField(
            model_name='party',
            name='party_types',
            field=django.contrib.postgres.fields.ArrayField(base_field=models.CharField(choices=[('monitoring', 'Monitoring'), ('shipper', 'Shipper'), ('consignee', 'Consignee'), ('freight_payer', 'Freight Payer'), ('customs_broker', 'Customs Broker'), ('care_party', 'Care Party'), ('pickup_party', 'Pickup Party'), ('notify_party', 'Notify Party'), ('ship_from', 'Ship From'), ('importer_exporter', 'Importer/Exporter'), ('carrier_company', 'Carrier Company'), ('forwarder', 'Forwarder'), ('beneficial_owner', 'Beneficial Owner')], max_length=50), blank=True, default=list, null=True, size=None),
        ),
    ]
