from django.db import migrations


def alter_erap_field(apps, schema_editor):
    Party = apps.get_model("wheelhouse", "party")
    for party in Party.objects.all():
        party.data["erp"] = [party.data["erp"]] if party.data["erp"].get("erap") else []
        party.save(update_fields=["data"])


class Migration(migrations.Migration):

    dependencies = [("wheelhouse", "0057_alter_location_workflows")]

    operations = [migrations.RunPython(alter_erap_field)]
