# Generated by Django 4.2 on 2024-01-03 21:11

from django.db import migrations, models
import vantedge.wheelhouse.models.location


class Migration(migrations.Migration):

    dependencies = [
        ('wheelhouse', '0077_location_minimum_bookings_time_gap'),
    ]

    operations = [
        migrations.AddField(
            model_name='location',
            name='booking_not_showedup_score',
            field=models.PositiveSmallIntegerField(default=0),
        ),
        migrations.AddField(
            model_name='location',
            name='minutes_per_score',
            field=models.PositiveSmallIntegerField(default=15),
        ),
        migrations.AddField(
            model_name='location',
            name='slots_ahead_hours',
            field=models.PositiveSmallIntegerField(default=0, help_text='hours ahead that slots could booked, 0 is disabled'),
        ),
        migrations.AddField(
            model_name='location',
            name='without_penalty_cancellation_hours',
            field=models.FloatField(default=0, validators=[vantedge.wheelhouse.models.location.validate_without_penalty_cancellation_hour]),
        ),
    ]
