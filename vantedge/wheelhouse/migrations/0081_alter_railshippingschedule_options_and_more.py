# Generated by Django 4.2 on 2024-02-17 00:43

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    dependencies = [
        ("wheelhouse", "0080_railshippingschedule_allow_shipment"),
    ]

    operations = [
        migrations.AlterModelOptions(
            name="railshippingschedule",
            options={"ordering": ["-created"]},
        ),
        migrations.AddField(
            model_name="facility",
            name="is_paired",
            field=models.BooleanField(
                default=False, help_text="Toggle to pair/unpair to partner facility"
            ),
        ),
        migrations.AddField(
            model_name="facility",
            name="partner",
            field=models.OneToOneField(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                to="wheelhouse.facility",
            ),
        ),
        migrations.AddField(
            model_name="party",
            name="is_paired",
            field=models.BooleanField(
                default=False, help_text="Toggle to pair/unpair to partner party"
            ),
        ),
        migrations.AddField(
            model_name="party",
            name="partner",
            field=models.OneToOneField(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                to="wheelhouse.party",
            ),
        ),
    ]
