# Generated by Django 4.2 on 2024-04-05 18:37

from django.db import migrations, models
import django.db.models.deletion
from django.contrib.auth.models import Permission
from django.contrib.contenttypes.models import ContentType

def create_permission(apps, schema_editor):
    RailShipment = apps.get_model("wheelhouse", "RailShipment")

    permission, created = Permission.objects.get_or_create(
        codename='assign_contract',
        name='Can assign shipment to constract',
        content_type=ContentType.objects.get_for_model(RailShipment)
    )    

class Migration(migrations.Migration):

    dependencies = [
        ('wheelhouse', '0086_separate_billing'),
    ]

    operations = [
        migrations.RunPython(create_permission),
        migrations.AlterField(
            model_name='railshipment',
            name='freight_charges',
            field=models.CharField(blank=True, choices=[('Collect', 'Collect'), ('Prepaid', 'Prepaid'), ('Rule11', 'Rule11'), ('Non-Revenue', 'Non-Revenue')], default='Prepaid', max_length=20),
        ),
        migrations.AlterField(
            model_name='railshippingschedule',
            name='freight_charges',
            field=models.CharField(blank=True, choices=[('Collect', 'Collect'), ('Prepaid', 'Prepaid'), ('Rule11', 'Rule11')], default='Prepaid', max_length=20),
        ),
        migrations.AlterField(
            model_name='railshipment',
            name='export_declaration',
            field=models.CharField(blank=True, default='', max_length=100, null=True),
        ),
        migrations.AlterField(
            model_name='railshipment',
            name='export_permit',
            field=models.CharField(blank=True, default='', max_length=100, null=True),
        ),
        migrations.AlterField(
            model_name='railshipment',
            name='product',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, to='wheelhouse.product'),
        ),
        migrations.AddField(
            model_name='railshippingschedule',
            name='is_for_empty_shipment',
            field=models.BooleanField(default=False),
        ),
        migrations.AlterField(
            model_name='railshippingschedule',
            name='freight_charges',
            field=models.CharField(blank=True, choices=[('Collect', 'Collect'), ('Prepaid', 'Prepaid'), ('Rule11', 'Rule11'), ('Non-Revenue', 'Non-Revenue')], default='Prepaid', max_length=20),
        ),
    ]
