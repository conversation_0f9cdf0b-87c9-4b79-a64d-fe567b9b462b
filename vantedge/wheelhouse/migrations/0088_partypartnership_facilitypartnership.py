# Generated by Django 4.2 on 2024-04-17 14:13

from django.db import migrations, models
import django.db.models.deletion
from django.db.models import Q


def update_shared_schedules_and_shipments_then_delete_entities(apps, schema_editor):
    """
    Update schedules & shipments using old-partner-design by replacing partner-party/facility
    with new-partner-design.
    """
    Party = apps.get_model("wheelhouse", "Party")
    Facility = apps.get_model("wheelhouse", "Facility")
    RailShippingSchedule = apps.get_model("wheelhouse", "RailShippingSchedule")
    RailShipment = apps.get_model("wheelhouse", "RailShipment")
    PartyPartnership = apps.get_model("wheelhouse", "PartyPartnership")
    FacilityPartnership = apps.get_model("wheelhouse", "FacilityPartnership")

    schedule_party_fields = get_entity_fields(RailShippingSchedule, Party)
    schedule_facility_fields = get_entity_fields(RailShippingSchedule, Facility)

    shipment_party_fields = get_entity_fields(RailShipment, Party)
    shipment_facility_fields = get_entity_fields(RailShipment, Facility)

    # remove all partner: one-to-one in Party and Facility
    parties_to_delete = Party.objects.filter(partner__isnull=False)
    facilities_to_delete = Facility.objects.filter(partner__isnull=False)

    for party in parties_to_delete:
        scrub_party_in_shipments_and_schedules(
            RailShippingSchedule,
            RailShipment,
            PartyPartnership,
            party,
            schedule_party_fields,
            shipment_party_fields,
        )

    for facility in facilities_to_delete:
        scrub_facility_in_shipments_and_schedules(
            RailShippingSchedule,
            RailShipment,
            FacilityPartnership,
            facility,
            schedule_facility_fields,
            shipment_facility_fields,
        )

    parties_to_delete.delete()
    facilities_to_delete.delete()


def scrub_party_in_shipments_and_schedules(
    schedule_model,
    shipment_model,
    party_partnership_model,
    party,
    schedule_party_fields,
    shipment_party_fields,
):
    """
    Update all shipments and schedules using obsolete partner-party.
    """
    inviter = party.company.company
    invitee = party.partner.company.company
    default_fields = get_defaults_by_partnership(inviter, invitee)

    relevant_schedule_fields = [
        f for f in schedule_party_fields
    ]

    relevant_shipment_fields = [
        f for f in shipment_party_fields
    ]

    party_partnership, created = party_partnership_model.objects.get_or_create(
        owner_company=invitee,
        partner_company=inviter,
        party=party.partner,
    )
    if created:
        if party_partnership.party.company.company != party_partnership.owner_company:
            print("-"*50, f"check this party: {party_partnership.party}")

    partnership_party = party_partnership.party

    for field in relevant_schedule_fields:
        schedule_model.objects.filter(Q(**{f"{field}": party})).update(
            **{f"{field}": partnership_party}
        )

    for field in relevant_shipment_fields:
        shipment_model.objects.filter(Q(**{f"{field}": party})).update(
            **{f"{field}": partnership_party}
        )


def scrub_facility_in_shipments_and_schedules(
    schedule_model,
    shipment_model,
    facility_partnership_model,
    facility,
    schedule_facility_fields,
    shipment_facility_fields,
):
    """
    Update all shipments and schedules using obsolete partner-facility.
    """
    inviter = facility.company.company
    invitee = facility.partner.company.company
    default_fields = get_defaults_by_partnership(inviter, invitee)

    relevant_schedule_fields = [
        f for f in schedule_facility_fields
    ]

    relevant_shipment_fields = [
        f for f in shipment_facility_fields
    ]

    facility_partnership, created = facility_partnership_model.objects.get_or_create(
        owner_company=invitee,
        partner_company=inviter,
        facility=facility.partner,
    )
    partnership_facility = facility_partnership.facility

    for field in relevant_schedule_fields:
        schedule_model.objects.filter(Q(**{f"{field}": facility})).update(
            **{f"{field}": partnership_facility}
        )

    for field in relevant_shipment_fields:
        shipment_model.objects.filter(Q(**{f"{field}": facility})).update(
            **{f"{field}": partnership_facility}
        )


# ----- utils:

def get_defaults_by_partnership(inviter, invitee) -> list:

    maybe_invite = invitee.invites.filter(status="Accepted").filter(
        data__resource="rail_contract"
    ).filter(company__company=inviter)

    if maybe_invite:
        invite = maybe_invite.first()
        defaults = invite.data.defaults
        default_fields = list()

        for entity_data in defaults:
            default_fields.append(entity_data.field)

        return list(default_fields)

    return list()


def get_entity_fields(a, b):
    fields = [
        f.name
        for f in a._meta.fields
        if (f.related_model is b)
    ]

    return fields


class Migration(migrations.Migration):

    dependencies = [
        ("users", "0024_merge_20240305_1338"),
        ("wheelhouse", "0087_alter_railshipment_freight_charges"),
    ]

    operations = [
        # create link-table for party&facility-partnerships
        migrations.CreateModel(
            name="PartyPartnership",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "owner_company",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="users.company",
                    ),
                ),
                (
                    "partner_company",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="party_partnerships",
                        to="users.company",
                    ),
                ),
                (
                    "party",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="wheelhouse.party",
                    ),
                ),
            ],
            options={
                "verbose_name": "party partnership",
                "verbose_name_plural": "party partnerships",
                "ordering": ["party"],
            },
        ),
        migrations.CreateModel(
            name="FacilityPartnership",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "facility",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="wheelhouse.facility",
                    ),
                ),
                (
                    "owner_company",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="users.company",
                    ),
                ),
                (
                    "partner_company",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="facility_partnerships",
                        to="users.company",
                    ),
                ),
            ],
            options={
                "verbose_name": "facility partnership",
                "verbose_name_plural": "facility partnerships",
                "ordering": ["facility"],
            },
        ),
        # replace partner one-to-one data accross all relevant schedules and shipments
        # (nominations already carry the correct value that will be shared in link-table)
        migrations.RunPython(
            update_shared_schedules_and_shipments_then_delete_entities
        ),
    ]
