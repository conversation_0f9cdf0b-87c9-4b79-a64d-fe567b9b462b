from django.db import migrations
from django.contrib.auth.models import Permission
from django.contrib.contenttypes.models import ContentType

from vantedge.users.models import User


def create_permissions(apps, schema_editor):
    content_type = ContentType.objects.get_or_create(app_label="tboss", model="certification")[0]
    permission_list = [Permission.objects.get_or_create(
        codename='can_view_certification_history',
        name='Can View Certification History',
        content_type=content_type
    )[0]]

    content_type = ContentType.objects.get_or_create(app_label="tboss", model="company")[0]
    permission_list.append(Permission.objects.get_or_create(
        codename='can_view_company_history',
        name='Can View Company History',
        content_type=content_type
    )[0])

    content_type = ContentType.objects.get_or_create(app_label="tboss", model="tbossuser")[0]
    permission_list.append(Permission.objects.get_or_create(
        codename='can_view_driver_history',
        name='Can View Driver History',
        content_type=content_type
    )[0])

    content_type = ContentType.objects.get_or_create(app_label="tboss", model="gcanalysis")[0]
    permission_list.append(Permission.objects.get_or_create(
        codename='can_view_gcanalysis_history',
        name='Can View GC Analysis History',
        content_type=content_type
    )[0])

    content_type = ContentType.objects.get_or_create(app_label="tboss", model="periodiclimit")[0]
    permission_list.append(Permission.objects.get_or_create(
        codename='can_view_limit_history',
        name='Can View Limit History',
        content_type=content_type
    )[0])

    content_type = ContentType.objects.get_or_create(app_label="tboss", model="location")[0]
    permission_list.append(Permission.objects.get_or_create(
        codename='can_view_location_object_history',
        name='Can View Location Object History',
        content_type=content_type
    )[0])
    permission_list.append(Permission.objects.get_or_create(
        codename='can_view_location_api_history',
        name='Can View Location API History',
        content_type=content_type
    )[0])

    content_type = ContentType.objects.get_or_create(app_label="tboss", model="product")[0]
    permission_list.append(Permission.objects.get_or_create(
        codename='can_view_product_history',
        name='Can View Product History',
        content_type=content_type
    )[0])

    content_type = ContentType.objects.get_or_create(app_label="tboss", model="route")[0]
    permission_list.append(Permission.objects.get_or_create(
        codename='can_view_route_history',
        name='Can View Route History',
        content_type=content_type
    )[0])

    content_type = ContentType.objects.get_or_create(app_label="tboss", model="servicecontract")[0]
    permission_list.append(Permission.objects.get_or_create(
        codename='can_view_service_contract_history',
        name='Can View Contract History',
        content_type=content_type
    )[0])

    content_type = ContentType.objects.get_or_create(app_label="tboss", model="tbossuser")[0]
    permission_list.append(Permission.objects.get_or_create(
        codename='can_view_staff_history',
        name='Can View Staff History',
        content_type=content_type
    )[0])

    content_type = ContentType.objects.get_or_create(app_label="tboss", model="ticket")[0]
    permission_list.append(Permission.objects.get_or_create(
        codename='can_view_ticket_object_history',
        name='Can View Ticket Object History',
        content_type=content_type
    )[0])
    permission_list.append(Permission.objects.get_or_create(
        codename='can_view_ticket_api_history',
        name='Can View Ticket API History',
        content_type=content_type
    )[0])

    content_type = ContentType.objects.get_or_create(app_label="tboss", model="tbossuseraccess")[0]
    permission_list.append(Permission.objects.get_or_create(
        codename='can_view_user_access_history',
        name='Can View User Access History',
        content_type=content_type
    )[0])

    content_type = ContentType.objects.get_or_create(app_label="tboss", model="tbossuser")[0]
    permission_list.append(Permission.objects.get_or_create(
        codename='can_view_user_history',
        name='Can View User History',
        content_type=content_type
    )[0])

    content_type = ContentType.objects.get_or_create(app_label="tboss", model="vehicle")[0]
    permission_list.append(Permission.objects.get_or_create(
        codename='can_view_vehicle_history',
        name='Can View Vehicle History',
        content_type=content_type
    )[0])

    content_type = ContentType.objects.get_or_create(app_label="tboss", model="codegenerator")[0]
    permission_list.append(Permission.objects.get_or_create(
        codename='can_view_codegenerator_history',
        name='Can View Code Generator History',
        content_type=content_type
    )[0])

    all_users = User.objects.all()

    for user in all_users:
        user.user_permissions.add(*permission_list)


class Migration(migrations.Migration):
    dependencies = [
        ('wheelhouse', '0094_create_history_permissions'),
    ]

    operations = [
        migrations.RunPython(create_permissions)
    ]
