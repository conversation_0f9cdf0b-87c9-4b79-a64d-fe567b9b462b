# Generated by Django 4.2 on 2024-08-15 17:53

from django.db import migrations, models
import django.db.models.deletion
import vantedge.util.json.encoder


class Migration(migrations.Migration):

    dependencies = [
        ('schedule', '0009_rename_description_booking_description_internal_and_more'),
        ('wheelhouse', '0096_railshippingschedule_product'),
    ]

    operations = [
        migrations.AddField(
            model_name='location',
            name='ahead_days',
            field=models.PositiveIntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='location',
            name='apply_schedule',
            field=models.JSONField(blank=True, default=list, encoder=vantedge.util.json.encoder.VantedgeEncoder, null=True),
        ),
        migrations.AddField(
            model_name='location',
            name='slot_template',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='schedule.slottemplate'),
        ),
    ]
