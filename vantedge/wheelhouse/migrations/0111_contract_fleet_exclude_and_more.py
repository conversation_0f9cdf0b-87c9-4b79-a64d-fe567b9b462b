# Generated by Django 4.2 on 2025-02-18 21:05

import django.contrib.postgres.fields
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("wheelhouse", "0110_alter_hoppercar_weight"),
    ]

    operations = [
        migrations.AddField(
            model_name="contract",
            name="fleet_exclude",
            field=django.contrib.postgres.fields.ArrayField(
                base_field=models.CharField(
                    blank=True, default="", max_length=50, null=True
                ),
                blank=True,
                default=list,
                size=10,
            ),
        ),
        migrations.AddField(
            model_name="railshippingschedule",
            name="fleets_exclude",
            field=django.contrib.postgres.fields.ArrayField(
                base_field=models.CharField(
                    blank=True, default="", max_length=50, null=True
                ),
                blank=True,
                default=list,
                size=10,
            ),
        ),
    ]
