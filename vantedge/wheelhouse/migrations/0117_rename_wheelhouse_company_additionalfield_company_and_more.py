# Generated by Django 4.2 on 2025-04-16 04:31

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ("contenttypes", "0002_remove_content_type_name"),
        ("users", "0030_userpopup_userpopuphistory"),
        (
            "wheelhouse",
            "0116_defaultvaluegenerator_contract_additional_fields_and_more",
        ),
    ]

    operations = [
        migrations.RenameField(
            model_name="additionalfield",
            old_name="wheelhouse_company",
            new_name="company",
        ),
        migrations.AlterUniqueTogether(
            name="additionalfield",
            unique_together={("code", "content_type", "company")},
        ),
    ]
