from django.contrib.contenttypes.fields import GenericF<PERSON><PERSON><PERSON><PERSON>
from django.contrib.contenttypes.models import ContentType
from django.db import models

from django.contrib.postgres.fields import ArrayField

from schema_field.fields import J<PERSON><PERSON>chemed<PERSON>ield
from .schema import CertificateData
from vantedge.util.mixins import EasyAuditModelConfigMixin

class CertificateScope(models.TextChoices):
    PARTY = "party", "Party"
    USER = "user", "User"


class CertificateType(EasyAuditModelConfigMixin, models.Model):

    company = models.ForeignKey(
        "WheelhouseCompany", related_name="certificate_types", on_delete=models.CASCADE
    )

    name = models.CharField(max_length=100)
    enabled = models.BooleanField(default=False)
    data = JSONSchemedField(schema=CertificateData, default=CertificateData)
    scopes = ArrayField(
        models.CharField(
            choices=CertificateScope.choices, max_length=50, blank=True, null=True
        ),
        default=list,
        blank=True,
    )

    code = models.CharField(max_length=50, blank=True, null=True)
    effective_date = models.DateTimeField("start", null=True, blank=True)
    expiry_date = models.DateTimeField("end", null=True, blank=True)

    def __str__(self):
        return str(self.name)


class Certificate(EasyAuditModelConfigMixin, models.Model):
    certificate_type = models.ForeignKey(
        "CertificateType",
        related_name="certificates",
        on_delete=models.CASCADE,
        null=True,
    )
    start_date = models.DateTimeField("start", null=True, blank=True)
    end_date = models.DateTimeField("end", null=True, blank=True)

    content_type = models.ForeignKey(
        ContentType,
        on_delete=models.CASCADE,
        related_name="content_type_certificates",
        limit_choices_to={
            "app_label__in": ["wheelhouse"],
            "model__in": list(CertificateScope.values),
        },
        null=True,
    )
    object_id = models.PositiveIntegerField(null=True, blank=True)
    content_object = GenericForeignKey("content_type", "object_id")

    def __str__(self):
        return str(self.certificate_type)
