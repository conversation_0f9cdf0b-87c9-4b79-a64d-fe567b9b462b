from django.db import models
from datetime import datetime
from django.utils import timezone
from django.contrib.postgres.fields import <PERSON><PERSON><PERSON><PERSON><PERSON>
from model_utils import Choices
from model_utils.models import TimeStampedModel
from vantedge.autocomplete.models import AutocompleteMixin
from vantedge.wheelhouse.models.company import Party
from vantedge.util.mixins import EasyAuditModelConfigMixin
from schema_field.fields import J<PERSON><PERSON><PERSON>medField
from vantedge.trackandtrace.detention import DetentionData
from .schema import RailShipmentContactPreference
import arrow
import copy

from django.contrib.contenttypes.models import ContentType

from vantedge.wheelhouse.models.additional_fields import AdditionalFieldMixin


class Contract(AdditionalFieldMixin, EasyAuditModelConfigMixin, AutocompleteMixin, TimeStampedModel):
    buyer = models.ForeignKey(
        Party, on_delete=models.CASCADE, related_name="purchase_contract_set", null=True
    )
    seller = models.ForeignKey(
        Party, on_delete=models.CASCADE, related_name="sale_contract_set", null=True
    )
    buyer_number = models.CharField(max_length=20, blank=True)
    seller_number = models.CharField(max_length=20, blank=True)

    FOB = Choices("Origin", "Destination")
    fob = models.CharField(choices=FOB, max_length=20, default="", blank=True)
    fleet = ArrayField(
        models.CharField(max_length=50, default="", blank=True, null=True),
        size=10,
        default=list,
        blank=True,
    )
    fleet_exclude = ArrayField(
        models.CharField(max_length=50, default="", blank=True, null=True),
        size=10,
        default=list,
        blank=True,
    )

    min_volume = models.IntegerField(default=0)
    max_volume = models.IntegerField(default=1000000)
    total_volume = models.IntegerField(default=0, null=True, blank=True)

    VOLUME_TYPE = Choices("Fixed", "Variable")
    volume_type = models.CharField(
        choices=VOLUME_TYPE, max_length=20, default="", blank=True
    )

    UNIT = Choices("Car", "USG")
    unit = models.CharField(choices=UNIT, max_length=10, default="", blank=True)

    product = models.ForeignKey(
        "Product", on_delete=models.CASCADE, null=True, blank=True
    )

    start_date = models.DateField("start", null=True, blank=True)
    end_date = models.DateField("end", null=True, blank=True)

    PRICING_TYPES = Choices("Index", "Fixed", "Posting")
    pricing_type = models.CharField(
        choices=PRICING_TYPES, max_length=10, default="", blank=True
    )
    pricing_basis = models.CharField(max_length=20, blank=True)
    pricing_diff = models.CharField(max_length=20, blank=True)

    VOLUME_UNIT_TYPES = Choices("LITRE", "USG", "BARREL", "M3", "SHORT TON")
    volume_unit = models.CharField(max_length=15, choices=VOLUME_UNIT_TYPES)
    volume_unit_price = models.DecimalField(max_digits=8, decimal_places=4, null=True)
    discount = models.DecimalField(max_digits=8, decimal_places=4, null=True)
    CURRENCY_CHOICES = Choices("CAD", "USD", "MXN")
    currency = models.CharField(max_length=3, choices=CURRENCY_CHOICES)

    detention_data = JSONSchemedField(schema=DetentionData, default=DetentionData)
    contact_preferences = JSONSchemedField(
        schema=RailShipmentContactPreference, default=RailShipmentContactPreference
    )

    def __str__(self):
        return f"{self.seller} -> {self.buyer}: {self.product} ({self.volume_type} {self.total_volume})"

    def clone(self, roll_over=False, period="month"):
        # Contract dict -> Contract
        """
        Clone or roll-over a contract.
        Any associated schedules are also cloned/copied over.

        Roll-over operation is by month or year.
        """
        cloned = copy.deepcopy(self)

        cloned.pk = None

        if roll_over:
            if cloned.start_date and cloned.end_date:
                start_date = arrow.get(cloned.start_date)
                end_date = arrow.get(cloned.end_date)

                match period:
                    case "month":
                        start_date = start_date.shift(months=1).date()
                        ceil_date = end_date.ceil("month").date()

                        if end_date.date() == ceil_date:
                            end_date = end_date.shift(months=1).ceil("month").date()
                        else:
                            end_date = end_date.shift(months=1).date()

                    case "year":
                        start_date = start_date.shift(years=1).date()
                        ceil_date = end_date.ceil("month").date()

                        if end_date.date() == ceil_date:
                            end_date = end_date.shift(years=1).ceil("month").date()
                        else:
                            end_date = end_date.shift(years=1).date()

                cloned.start_date = start_date
                cloned.end_date = end_date

        cloned.save()

        original = self
        tz = timezone.get_current_timezone()
        now = timezone.now()
        cloned_start_time = max(
            timezone.make_aware(
                datetime.combine(cloned.start_date or now.date(), datetime.min.time()),
                tz,
                is_dst=False,
            ),
            now,
        )

        # clone the shipping schedules associated with contract
        schedules = list(original.purchase_schedules.all().iterator()) + list(
            original.sale_schedules.all().iterator()
        )

        for schedule in set(schedules):
            if roll_over:
                schedule.cars_quantity = 0
                schedule.schedule = []
                schedule.allow_shipment = False
                schedule.auto_schedule_batch = None
                schedule.auto_schedule_periodic_days = None
                schedule.auto_schedule_shipment_count = None
                schedule.created = cloned_start_time
                schedule.modified = cloned_start_time

            if schedule.purchase_contract == original:
                schedule.purchase_contract = cloned
            if schedule.sale_contract == original:
                schedule.sale_contract = cloned

            schedule.pk = None
            schedule.save()

        return cloned

