import re

from django.contrib.postgres.fields import <PERSON><PERSON>y<PERSON>ield
from django.db import models


class DefaultValueGenerator(models.Model):
    """
        Generates default values for additional fields based on a specified format.
        This model allows dynamic field value generation by evaluating predefined functions

        Example:
            name: shipment_po_number
            separator: "-"
            format: ["po", "separator", "APPEND_SHIPMENT_COUNT(buyer_id=842)"]

        Format:
            - A simple list of strings:
            Example: ['foo', 'separator', 'bar'] → 'foo-bar'

            - A function call with parameters:
            Example: ['APPEND_SHIPMENT_COUNT(buyer_id=842)']
                - `APPEND_SHIPMENT_COUNT` is the function name.
                - `buyer_id=842` is a keyword argument.
            Split kwargs by `|` and pass them to the function.

        (see `DefaultValFunctions`) and applying separators when necessary.
    """
    name = models.CharField(max_length=50, blank=False)
    separator = models.CharField(max_length=1, blank=True, default="-")
    format = ArrayField(
        models.CharField(max_length=200, blank=True, null=True),
        size=20,
        default=list,
        blank=True,
    )

    def __str__(self):
        return  str(self.name)

    def generate_value(self, instance, company):
        """Generates a default value based on the instance and defined format.
           See DefaultValFunctions for available functions & how to use them.
        """
        from vantedge.wheelhouse.utils.default_value_utils import DefaultValFunctions
        result = []
        extractor = DefaultValFunctions()
        for field in self.format:
            if field == "separator":
                result.append(self.separator)
            else:
                pattern = r"(\w+)\s*\((.*?)\)"
                match = re.match(pattern, field)
                if match:
                    field_name, param_string = match.groups()
                    param_pairs = param_string.split("|")
                    kwargs = {}
                    for param in param_pairs:
                        if "=" in param:
                            k, v = param.split("=")
                            kwargs[k.strip()] = v.strip()
                    value = extractor.calculate(company, instance, field_name, **kwargs)
                else:
                    value = extractor.calculate(company, instance, field)
                result.append(str(value) if value is not None else "")
        return "".join(result)

