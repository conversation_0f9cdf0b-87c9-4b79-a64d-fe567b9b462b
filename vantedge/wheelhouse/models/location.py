from django.db import models
from django.utils import timezone
from django.core.exceptions import ValidationError
from django.contrib.postgres.fields import <PERSON><PERSON>y<PERSON>ield
from phonenumber_field.modelfields import PhoneNumberField

from .schema import AddressSchema, HazardousCertificate
from .validations import validate_facility_data
from schema_field.fields import JSONSchemedField
from pydantic import BaseModel
from vantedge.users.models import Company
from vantedge.workflow.models import Workflow
from vantedge.util.json.encoder import VantedgeEncoder
from vantedge.util.mixins import EasyAuditModelConfigMixin

import arrow
from datetime import timedelta
from dirtyfields import DirtyFieldsMixin


def validate_without_penalty_cancellation_hour(value):
    if value < 0:
        raise ValidationError("Without penalty cancellation hour in not positive")


def default_rail_car_types():
    return [
        Facility.RailCarTypeChoices.HOPPER_CAR,
        Facility.RailCarTypeChoices.TANK_CAR
    ]


def set_default_required_fields():
    fields = list()
    fields.append("truck")
    return fields


class LocationData(BaseModel):
    address: AddressSchema = {}
    cert: HazardousCertificate = {}


class Location(EasyAuditModelConfigMixin, DirtyFieldsMixin, models.Model):
    class Status(models.TextChoices):
        ACTIVE = "Active", "Active"
        INACTIVE = "Inactive", "Inactive"
        WARNING = "Warning", "Warning"

    class ActionChoices(models.TextChoices):
        LOAD = "Load", "Load"
        UNLOAD = "Unload", "Unload"

    class BookingDataChoices(models.TextChoices):
        Truck = "truck", "truck"
        TruckVolume = "truck_volume", "truck_volume"
        Density = "density", "density"
        LSD = "lsd", "lsd"
        Producer = "producer", "producer"

    status = models.CharField(
        choices=Status.choices, max_length=100, default=Status.ACTIVE
    )
    company = models.ForeignKey("WheelhouseCompany", on_delete=models.CASCADE)
    announcement = models.CharField(max_length=255, blank=True, null=True)
    parent = models.ForeignKey("self", on_delete=models.CASCADE, null=True, blank=True)
    name = models.CharField(max_length=100)
    data = JSONSchemedField(
        schema=LocationData, default=LocationData, validators=[validate_facility_data]
    )
    workflows = models.ManyToManyField(
        Workflow, related_name="location_workflows", blank=True
    )
    generate_token = models.BooleanField(default=False)
    location_key = models.CharField(max_length=20, blank=True, null=True)

    slots_ahead_hours = models.PositiveSmallIntegerField(
        default=0, help_text="hours ahead that slots could booked, 0 is disabled"
    )
    force_booking_data_time_window = models.PositiveSmallIntegerField(
        default=0
    )  # diff minutes check-in and booking
    minimum_bookings_time_gap = models.PositiveSmallIntegerField(
        default=12
    )  # hours to next booking
    minutes_per_score = models.PositiveSmallIntegerField(default=15)
    booking_not_showedup_score = models.PositiveSmallIntegerField(default=0)
    without_penalty_cancellation_hours = models.FloatField(
        default=0, validators=[validate_without_penalty_cancellation_hour]
    )
    grace_period = models.PositiveSmallIntegerField(default=10)

    booking_required_fields = ArrayField(
        models.CharField(max_length=30, choices=BookingDataChoices.choices),
        blank=True, default=set_default_required_fields
    )
    available_products = models.ManyToManyField("wheelhouse.product", blank=True)
    available_actions = ArrayField(
        models.CharField(max_length=30, choices=ActionChoices.choices),
        blank=True, default=list
    )
    apply_schedule = models.JSONField(encoder=VantedgeEncoder, default=list, null=True, blank=True)
    ahead_days = models.PositiveIntegerField(blank=True, null=True)
    slot_template = models.ForeignKey("schedule.SlotTemplate", null=True, blank=True, on_delete=models.CASCADE)

    node_order_by = ["name"]

    def __str__(self):
        rep = []
        rep.insert(0, self.name)
        parent = self.parent
        while parent:
            rep.insert(0, parent.name)
            parent = parent.parent
        return " ---> ".join(rep)

    def save(self, *args, **kwargs):
        if self.pk and self.parent and self.parent.id == self.id:
            # prevent infinite loop
            raise Exception("can not set parent and child same, creates infinite loop")
        if "apply_schedule" in self.get_dirty_fields() and self.apply_schedule:
            self.calc_next_runs()
        return super().save(*args, **kwargs)

    @classmethod
    def company_locations(cls, company):
        return cls.objects.filter(company=company)

    @classmethod
    def company_shared_schedule_locations(cls, company):
        from .company import Party

        locations_id = []
        owned_locations = Party.objects.filter(
            company=company, wheelhouse_company__company__schedule__isnull=False
        ).values_list("location_set", flat=True)
        locations_id.extend(owned_locations)

        if hasattr(company.company, "schedule"):
            host_locations = Party.objects.filter(
                wheelhouse_company__company__schedule=company.company.schedule
            ).values_list("location_set", flat=True)
            locations_id.extend(host_locations)

        return cls.objects.filter(id__in=locations_id)

    def allowed_companies(self):
        # return list of base companies,
        # which directly or indirectly
        # could see the location
        from vantedge.users.models import Company

        companies = []
        companies.append(self.company.company.id)
        related_companies = self.wheelhouse_party_set.values_list(
            "wheelhouse_company__company", flat=True
        )
        companies.extend(related_companies)
        return Company.objects.filter(id__in=companies)

    def allowed_wheelhouse_companies(self):
        pass

    @property
    def time_delta_per_score(self):
        return timedelta(minutes=self.minutes_per_score)

    @property
    def allowed_slot_ahead_time(self):
        return timezone.now() + timedelta(hours=self.slots_ahead_hours)

    @property
    def without_penalty_cancellation_delta(self):
        return timedelta(hours=self.without_penalty_cancellation_hours)

    @property
    def grace_period_delta(self):
        return timedelta(minutes=self.grace_period)

    def score_to_time_delta(self, score):
        if score >= 0:
            return 0 * self.time_delta_per_score

        return abs(score) * self.time_delta_per_score

    def score_to_time(self, last_cancellation, score):
        return (last_cancellation + self.score_to_time_delta(score)).astimezone(
            timezone.get_current_timezone()
        )

    def calc_next_runs(self, now=None):
        if not now:
            now = arrow.now()

        weekdays = [
            "Monday",
            "Tuesday",
            "Wednesday",
            "Thursday",
            "Friday",
            "Saturday",
            "Sunday",
        ]

        day = now.format("DD")
        month = now.format("MM")
        year = now.format("YYYY")
        schedule_tz = timezone.get_current_timezone()

        if self.apply_schedule:
            for schedule in self.apply_schedule:
                next_run_timestamp = None
                if schedule["interval"] == "Daily":
                    next_run_timestamp = arrow.get(
                        f'{year}-{month}-{day} {schedule["time"]}', "YYYY-MM-DD HH:mm"
                    ).replace(tzinfo=schedule_tz)
                    while next_run_timestamp <= now:
                        next_run_timestamp = next_run_timestamp.shift(days=+1)
                elif schedule["interval"] == "Weekly":
                    next_run_timestamp = arrow.get(
                        f'{year}-{month}-{day} {schedule["time"]}', "YYYY-MM-DD HH:mm"
                    ).replace(tzinfo=schedule_tz)
                    next_run_timestamp = next_run_timestamp.shift(days=+7)
                elif schedule["interval"] == "Monthly":
                    schedule_day = (
                        schedule["date"]
                        if len(str(schedule["date"])) == 2
                        else f'0{schedule["date"]}'
                    )
                    next_run_timestamp = arrow.get(
                        f'{year}-{schedule_day} {schedule["time"]}', "YYYY-DD HH:mm"
                    ).replace(tzinfo=schedule_tz)

                    while next_run_timestamp <= now:
                        next_run_timestamp = next_run_timestamp.shift(months=+1)

                schedule["next_run"] = next_run_timestamp.datetime


class Facility(Location):
    class RailCarInitialStatusChoices(models.TextChoices):
        ARRIVED = "Arrived", "Arrived"
        UNDER_INSPECTION = "Under Inspection", "Under Inspection"
        READY_TO_LOAD = "Ready To Load", "Ready To Load"

    class RailCarTypeChoices(models.TextChoices):
        HOPPER_CAR = "hopper car", "Hopper Car"
        TANK_CAR = "tank car", "Tank Car"

    class RailCarStatusWhenLoadedChoices(models.TextChoices):
        LOADED = "Loaded", "Loaded"
        READY_TO_SHIP = "Ready To Ship", "Ready To Ship"

    class DisabledFieldsChoices(models.TextChoices):
        SAFETY_FACTOR_PERCENT = "Safety Factor Percent", "Safety Factor Percent"
        SAFETY_FACTOR_INCH = "Safety Factor Inch", "Safety Factor Inch"

    class MeasurementSystemChoices(models.TextChoices):
        METRIC = "Metric", "Metric"
        IMPERIAL = "Imperial", "Imperial"

    disabled_fields = ArrayField(
        models.CharField(choices=DisabledFieldsChoices.choices),
        max_length=30,
        blank=True,
        default=list
    )
    default_density_unit = models.BooleanField(default=False)
    default_safety_factor_inch = models.FloatField(default=0)
    default_safety_factor_percent = models.FloatField(default=1)
    default_measurement_system = models.CharField(
        choices=MeasurementSystemChoices.choices,
        default=MeasurementSystemChoices.METRIC
    )

    alias = models.CharField(max_length=20, blank=True, default="")
    emergency_phone = PhoneNumberField(blank=True)
    firms_code = models.CharField(max_length=6, blank=True, default="")
    legal_description = models.CharField(max_length=20, blank=True, default="")
    products = models.ManyToManyField("Product", blank=True)
    railroads = models.ManyToManyField("Railroad", blank=True)
    rail_car_types = ArrayField(
        models.CharField(choices=RailCarTypeChoices.choices),
        max_length=30,
        blank=True,
        default=default_rail_car_types
    )
    rail_car_initial_status = models.CharField(
        max_length=30,
        choices=RailCarInitialStatusChoices.choices,
        default=RailCarInitialStatusChoices.READY_TO_LOAD
    )
    rail_car_status_when_loaded = models.CharField(
        max_length=30,
        choices=RailCarStatusWhenLoadedChoices.choices,
        default=RailCarStatusWhenLoadedChoices.READY_TO_SHIP
    )

    railstation = models.ManyToManyField(to="trackandtrace.RailStation")

    embargo_number = models.CharField(max_length=30, blank=True, null=True, unique=True)
    embargo_permit_numbers = ArrayField(
        models.CharField(max_length=30, blank=False),
        blank=True,
        default=list
    )
    enforce_empty_car_seal_numbers = models.BooleanField(default=True)
    enforce_loaded_car_seal_numbers = models.BooleanField(default=True)

    class Meta:
        ordering = ["name"]
        verbose_name_plural = "Facilities"

    def delete(self, *args, **kwargs):
        from vantedge.invitation.models.validation import relied_on_by_partner_entities

        if relied_on_by_partner_entities(self):
            raise models.ProtectedError(
                f"There are objects that rely on this {self._meta.model_name.capitalize()}.",
                self
            )

        return super(Facility, self).delete(*args, **kwargs)


class Riser(Location):
    pass


class FacilityPartnership(EasyAuditModelConfigMixin, models.Model):
    owner_company = models.ForeignKey(Company, null=True, blank=True, on_delete=models.CASCADE)
    partner_company = models.ForeignKey(Company,
                                        null=True,
                                        blank=True,
                                        related_name="facility_partnerships",
                                        on_delete=models.CASCADE)
    facility = models.ForeignKey(Facility, null=True, blank=True, on_delete=models.CASCADE)

    class Meta:
        ordering = ["facility"]
        verbose_name = "facility partnership"
        verbose_name_plural = "facility partnerships"

    def __str__(self) -> str:
        return f"{self.facility} owned by {self.owner_company} available to {self.partner_company}"

    @classmethod
    def create(cls, facility: Facility, partner: Company):
        instance = cls(company=facility.company.company,
                       partner_company=partner,
                       facility=facility)
        instance.save()
        return instance

    @classmethod
    def get_facility_partnerships(cls, owner: Company, partner: Company):
        return cls.objects.filter(owner_company=owner,
                                  partner_company=partner)
