from email.policy import default
from django.db import models
from django.contrib.postgres.fields import <PERSON><PERSON>yField
from pydantic import BaseModel
from .schema import CodesSchema
from .validations import validate_product_data
from .company import WheelhouseCompany
from schema_field.fields import J<PERSON><PERSON><PERSON>med<PERSON>ield
from vantedge.wheelhouse.models.pricing import ProductTypeConfiguration
from vantedge.util.mixins import EasyAuditModelConfigMixin
from pydantic import BaseModel


def default_product_data():
    return {"codes": CodesSchema()}


class YardManagementConfigSchema(BaseModel):
    display_color: str = "#AAAAAA"


class ProductData(BaseModel):
    codes: CodesSchema = CodesSchema()
    yard_management: YardManagementConfigSchema = YardManagementConfigSchema()


class Product(EasyAuditModelConfigMixin, models.Model):
    company = models.ForeignKey(WheelhouseCompany, on_delete=models.CASCADE)

    name = models.CharField(max_length=30)
    data = JSONSchemedField(
        schema=ProductData, default=ProductData, validators=[validate_product_data]
    )
    density = models.DecimalField(max_digits=10, decimal_places=5, default=1)
    gal_to_liter_conversion_factor = models.DecimalField(
        max_digits=10, decimal_places=5, default=3.78541
    )
    default_stcc = models.ForeignKey(
        "trackandtrace.Stcc", on_delete=models.SET_NULL, null=True, blank=True
    )
    stcc_aliases = ArrayField(
        models.CharField(max_length=7, blank=False), default=list, blank=True, null=True
    )
    product_type = models.ForeignKey(
        "ProductType", on_delete=models.CASCADE, null=True, blank=True
    )

    def display_color(self):
        return self.data.yard_management.display_color

    def __str__(self):
        return self.name

    @property
    def is_dangerous_goods(self):
        return bool(self.data.codes.hazmat_class)

    @property
    def liter_to_gal_conversion_factor(self):
        return 1 / self.gal_to_liter_conversion_factor


class ProductType(EasyAuditModelConfigMixin, models.Model):
    name = models.CharField(max_length=50)
    description = models.CharField(max_length=100)
    configuration = JSONSchemedField(
        schema=ProductTypeConfiguration, default=ProductTypeConfiguration
    )

    def __str__(self):
        return self.name


class Unit(EasyAuditModelConfigMixin, models.Model):
    name = models.CharField(max_length=30)

    def __str__(self):
        return self.name


def CalculateWeight(density, volume):
    return density * volume


def CalculateNetVolume(gross_volume, temperature_correction_factor):
    return gross_volume * temperature_correction_factor
