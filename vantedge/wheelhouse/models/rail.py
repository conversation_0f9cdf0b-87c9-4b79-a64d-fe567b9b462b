from datetime import date, timedelta

from django.contrib.contenttypes.models import ContentType
from django.contrib.postgres.fields import <PERSON><PERSON>y<PERSON>ield
from django.core.mail import EmailMessage, EmailMultiAlternatives
from django.core.validators import Min<PERSON>engthValidator
from django.db import models, transaction
from django.db.models.functions import Concat
from django.template.loader import render_to_string
from django.utils import timezone
from django_q.tasks import async_task
from model_utils import Choices
from model_utils.fields import StatusField
from model_utils.models import TimeStampedModel
from schema_field.fields import J<PERSON>NSchemedField
from vant_email.base import PriorityEmailMessage

from vantedge.util.mixins import EasyAuditModelConfigMixin
from vantedge.edi.comm.edi_404 import EDI824, edi404_data_from_shipment
from vantedge.edi.models import EdiDocument
from vantedge.wheelhouse.models import AdditionalFieldMixin
from vantedge.yard_management.models.rail_yard import RailYard

from .bol import BolNumber
from .company import Party
from .location import Facility
from .product import Product
from .railroad import Railroad
from .schedule import RailShippingSchedule
from .schema import (
    EDI404PackageSchema,
    EDIDataSchema,
    RailShipmentContactPreference,
    RailShipmentDataSchema,
)
from .utility import html_to_text


def tomorrow():
    return date.today() + timedelta(days=1)


class RoutingError(Exception):
    def __init__(self, route, message=""):
        self.route = route
        self.message = message


class Route(EasyAuditModelConfigMixin, models.Model):
    origin = models.ForeignKey(
        "Facility", on_delete=models.CASCADE, related_name="schedule_out"
    )
    destination = models.ForeignKey(
        "Facility", on_delete=models.CASCADE, related_name="schedule_in"
    )
    routing = models.CharField(max_length=50, blank=True)
    cars = models.IntegerField(default=0)
    schedule = ArrayField(models.IntegerField(), default=list)

    def save(self, *args, **kwargs):
        # days = (self.contract.end_date - self.contract.start_date).days + 1
        # if len(self.schedule) < days:
        #     self.schedule += [0] * (days - len(self.schedule))
        # self.schedule = self.schedule[:days]
        super().save(*args, **kwargs)


class RailShipment(AdditionalFieldMixin, EasyAuditModelConfigMixin, TimeStampedModel):
    STATUS = Choices(
        "open",
        "bol-sent",
        "bol-syntax-rejected",
        "bol-acknowledged",
        "bol-rejected",
        "bol-accepted",
        "bol-accepted-with-error",
        "received",
        "closed",
        "voided",
        "correction-open",
        "correction-sent",
        "correction-syntax-rejected",
        "correction-acknowledged",
        "correction-rejected",
        "correction-accepted",
        "correction-accepted-with-error",
        "cancellation-open",
        "cancellation-sent",
        "cancellation-acknowledged",
        "cancellation-rejected",
        "cancellation-accepted",
        "master-billing"
    )

    class SubmissionType(models.TextChoices):
        Original = "Original"
        Correction = "Correction"
        Cancellation = "Cancellation"

    BILLING_TYPES = Choices("Single", "Multiple")

    CLOSED_STATUSES = [
        "bol-accepted",
        "bol-accepted-with-error",
        "closed",
        "correction-accepted",
        "correction-accepted-with-error",
    ]
    VALID_STATUSES_FOR_CORRECTION = CLOSED_STATUSES + [
        "correction-rejected",
        "cancellation-rejected",
    ]
    VALID_STATUSES_FOR_CANCELLATION = CLOSED_STATUSES + [
        "bol-sent",
        "bol-acknowledged",
        "correction-sent",
        "correction-syntax-rejected",
        "correction-rejected",
        "cancellation-rejected",
        "correction-open",
        "correction-acknowledged",
    ]

    SINGLE_BILLING_FIELDS = [
        "embargo_number",
        "embargo_permit_number",
        "customs_barcode_number"
    ]

    master_billing = models.ForeignKey("self", related_name="sub_billings", null=True, blank=True,  on_delete=models.CASCADE)
    status = StatusField()
    reference_number = models.CharField(max_length=80)

    product = models.ForeignKey(Product, on_delete=models.PROTECT, null=True, blank=True)
    schedule = models.ForeignKey(
        RailShippingSchedule,
        related_name="rail_shipments",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
    )
    railroad = models.ForeignKey(Railroad, on_delete=models.PROTECT)
    routing = models.CharField(max_length=50, validators=[MinLengthValidator(2)])
    consignee = models.ForeignKey(
        Party, related_name="shiprail_consignee_set", on_delete=models.PROTECT
    )
    beneficial_owner = models.ForeignKey(
        Party,
        related_name="shiprail_beneficial_owner_set",
        null=True,
        blank=True,
        on_delete=models.PROTECT,
    )
    account_of = models.ForeignKey(
        Party,
        related_name="shiprail_account_of_set",
        null=True,
        blank=True,
        on_delete=models.PROTECT,
    )
    account_of_origin = models.ForeignKey(
        Party,
        related_name="shiprail_account_of_origin_set",
        null=True,
        blank=True,
        on_delete=models.PROTECT,
    )
    account_of_destination = models.ForeignKey(
        Party,
        related_name="shiprail_account_of_destination_set",
        null=True,
        blank=True,
        on_delete=models.PROTECT,
    )
    shipper = models.ForeignKey(
        Party, related_name="shiprail_shipper_set", on_delete=models.PROTECT
    )
    ship_from = models.ForeignKey(
        Party,
        related_name="shiprail_ship_from_set",
        on_delete=models.PROTECT,
        null=True,
        blank=True,
    )
    forwarder = models.ForeignKey(
        Party,
        related_name="shiprail_forwarder_set",
        on_delete=models.PROTECT,
        null=True,
        blank=True,
    )
    accounting_rule_11 = models.ForeignKey(
        Party,
        related_name="shiprail_accounting_rule_11_set",
        null=True,
        blank=True,
        on_delete=models.PROTECT,
    )
    freight_payer = models.ForeignKey(
        Party,
        related_name="shiprail_freight_payer_set",
        null=True,
        blank=True,
        on_delete=models.PROTECT,
    )
    custom_broker = models.ForeignKey(
        Party,
        related_name="shiprail_custom_broker_set",
        on_delete=models.PROTECT,
        null=True,
        blank=True,
    )
    notify_party = models.ForeignKey(
        Party,
        on_delete=models.PROTECT,
        related_name="shiprail_notify_party_set",
        null=True,
        blank=True,
    )
    notify_party_2 = models.ForeignKey(
        Party,
        on_delete=models.PROTECT,
        related_name="shiprail_notify_2_party_set",
        null=True,
        blank=True,
    )
    care_party = models.ForeignKey(
        Party,
        on_delete=models.PROTECT,
        related_name="shiprail_care_party_set",
        null=True,
        blank=True,
    )
    monitoring_party = models.ForeignKey(
        Party,
        on_delete=models.PROTECT,
        related_name="shiprail_monitoring_set",
        null=True,
        blank=True,
    )
    pickup_party = models.ForeignKey(
        Party,
        on_delete=models.PROTECT,
        related_name="shiprail_pickup_party_set",
        null=True,
        blank=True,
    )
    ultimate_consignee = models.ForeignKey(
        Party,
        on_delete=models.PROTECT,
        related_name="shiprail_ultimate_consignee_set",
        null=True,
        blank=True,
    )
    custom_broker_us = models.ForeignKey(
        Party,
        on_delete=models.PROTECT,
        related_name="shiprail_custom_broker_us_set",
        null=True,
        blank=True,
    )
    custom_broker_mx = models.ForeignKey(
        Party,
        on_delete=models.PROTECT,
        related_name="shiprail_custom_broker_mx_set",
        null=True,
        blank=True,
    )
    importer = models.ForeignKey(
        Party,
        on_delete=models.PROTECT,
        related_name="shiprail_importer_set",
        null=True,
        blank=True,
    )
    exporter = models.ForeignKey(
        Party,
        on_delete=models.PROTECT,
        related_name="shiprail_exporter_set",
        null=True,
        blank=True,
    )

    origin = models.ForeignKey(
        Facility, related_name="ship_rail_out", on_delete=models.PROTECT
    )
    destination = models.ForeignKey(
        Facility, related_name="ship_rail_in", on_delete=models.PROTECT
    )
    FREIGHT_CHARGE_TYPE = Choices("Collect", "Prepaid", "Rule11", "Non-Revenue")
    freight_charges = models.CharField(
        choices=FREIGHT_CHARGE_TYPE, max_length=20, blank=True, default="Prepaid"
    )

    billing_type = models.CharField(
        choices=BILLING_TYPES, max_length=20, default="Single"
    )
    ship_date = models.DateField(default=date.today)
    arrive_date = models.DateField(null=True, blank=True)

    export_permit = models.CharField(max_length=100, blank=True, null=True, default="")
    export_declaration = models.CharField(max_length=100, blank=True, null=True, default="")

    contact_preferences = JSONSchemedField(
        schema=RailShipmentContactPreference, default=RailShipmentContactPreference
    )

    data = JSONSchemedField(
        schema=RailShipmentDataSchema, default=RailShipmentDataSchema
    )

    hazardous_certificate_person = models.CharField(
        max_length=35, null=True, blank=True
    )

    class Meta:
        ordering = ("-id",)

    def __str__(self):
        if self.id:
            return f"{self.id} {self.product}: {self.origin}->{self.destination} - {self.cars.count()} cars"
        else:
            return f"{self.id} {self.product}: {self.origin}->{self.destination}"

    # https://github.com/Koed00/django-q/issues/613
    def __name__(self):
        return "RailShipment"

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.__original_status = self.status

    def delete(self, *args, **kwargs):
        if self.status == "master-billing" and self.sub_billings.count() == self.sub_billings.filter(status="open").count():
            for billing in self.sub_billings.filter(status="open"):
                billing.delete()
        elif self.status != "open":
            raise Exception("Shipment has history")

        self.cars.update(rail_shipment=None)
        super().delete(*args, **kwargs)

    def save(self, *args, **kwargs):
        from .railcar import LoadedRailCar
        if self.status == "closed" and self.__original_status != "closed":
            self.generate_BOL_archive(control_number=None)

        super().save(*args, **kwargs)
        origin = self.origin
        rail_yard_id = None
        if hasattr(origin, "railyard") and origin.railyard is not None:
            rail_yard_id = origin.railyard.id
        if self.status != self.__original_status and self.status in ["bol-sent", "closed"]:
            self.cars.update(status=LoadedRailCar.LoadedRailCarStatus.SHIPPED)
            if rail_yard_id:
                self.origin.railyard.remove_shipped(None, self.cars.all())
                RailYard.set_last_update(rail_yard_id)
                return

        if rail_yard_id:
            RailYard.set_last_update_details(rail_yard_id)

    def is_master_billing(self):
        return (self.sub_billings.count() > 0 or self.status == 'master-billing')

    @property
    def is_empty_shipment(self):
        return not bool(self.weight)

    @property
    def wheelhouse_company(self):
        return self.consignee.company

    @property
    def contract(self):
        if self.schedule:
            return self.schedule.purchase_contract or self.schedule.sale_contract
        return None

    @property
    def weight(self):
        return round(sum([car.weight for car in self.cars.all()]), 3)

    @property
    def volume(self):
        from .railcar import TankCar

        return round(sum([car.volume for car in self.cars.instance_of(TankCar)]), 3)

    def is_export_mexican(self):
        return self.destination.data.address.country == 'MX'

    def bol_notes(self):
        return self.notes.filter(show_on_bol=True)

    def bol_additional_fields(self):
        """ Used to display additional_fields on the BOL """
        from vantedge.wheelhouse.models.additional_fields import AdditionalField
        field_ids = self.additional_fields.fields.keys()
        field_qs = AdditionalField.objects.filter(id__in=field_ids)
        return [(field.label, self.additional_fields.fields[field.id]) for field in field_qs]

    def update_bolnumber(self):
        if BolNumber.objects.filter(company=self.origin.company).first():
            self.reference_number = BolNumber.generate_bol(self.origin.company, self)
            self.save(update_fields=["reference_number"])

    @staticmethod
    def populate_shipment_from_schedule(schedule_id):
        rail_shipment_schedule = RailShippingSchedule.objects.get(id=schedule_id)

        contract = (
            rail_shipment_schedule.purchase_contract
            or rail_shipment_schedule.sale_contract
        )

        if (
            rail_shipment_schedule.origin.railroads.count() > 1
            and rail_shipment_schedule.route
        ):
            railroad = rail_shipment_schedule.origin.railroads.filter(
                name__istartswith=rail_shipment_schedule.route.split("/")[0][:3]
            ).first()
        else:
            railroad = rail_shipment_schedule.origin.railroads.first()

        product = None
        if rail_shipment_schedule.product:
            product = rail_shipment_schedule.product
        elif contract and contract.product:
            product = contract.product

        rail_shipment = RailShipment(
            reference_number=BolNumber.generate_reference_number(),
            product=product,
            railroad=railroad,
            schedule=rail_shipment_schedule,
            consignee=rail_shipment_schedule.consignee,
            ultimate_consignee=rail_shipment_schedule.ultimate_consignee,
            beneficial_owner=rail_shipment_schedule.beneficial_owner,
            account_of=rail_shipment_schedule.account_of,
            account_of_origin=rail_shipment_schedule.account_of_origin,
            account_of_destination=rail_shipment_schedule.account_of_destination,
            shipper=rail_shipment_schedule.shipper,
            ship_from=rail_shipment_schedule.ship_from,
            accounting_rule_11=rail_shipment_schedule.accounting_rule_11,
            freight_payer=rail_shipment_schedule.freight_payer,
            forwarder=rail_shipment_schedule.forwarder,
            custom_broker=rail_shipment_schedule.customs,
            custom_broker_us=rail_shipment_schedule.custom_broker_us,
            custom_broker_mx=rail_shipment_schedule.custom_broker_mx,
            notify_party=rail_shipment_schedule.notify_party,
            notify_party_2=rail_shipment_schedule.notify_party_2,
            monitoring_party=rail_shipment_schedule.monitoring_party,
            care_party=rail_shipment_schedule.care_party,
            pickup_party=rail_shipment_schedule.pickup_party,
            exporter=rail_shipment_schedule.exporter,
            importer=rail_shipment_schedule.importer,
            origin=rail_shipment_schedule.origin,
            destination=rail_shipment_schedule.destination,
            freight_charges=rail_shipment_schedule.freight_charges,
            routing=rail_shipment_schedule.route,
            contact_preferences=contract.contact_preferences if contract else RailShipmentContactPreference(),
        )
        rail_shipment.save()
        rail_shipment.update_bolnumber()
        return rail_shipment

    def get_gs_code(self):
        return self.railroad.get_gs_code(self.product.company.company)

    @property
    def is_readonly(self):
        return (
            False
            if self.status
            in [
                "open",
                "bol-syntax-rejected",
                "bol-rejected",
                "correction-open",
                "correction-syntax-rejected",
                "correction-rejected",
            ]
            else True
        )

    @property
    def not_valid_bol_status(self):
        return [
        "open",
        "bol-sent",
        "bol-syntax-rejected",
        "bol-acknowledged",
        "bol-rejected",
        "received",
        "voided",
        "correction-open",
        "correction-sent",
        "correction-syntax-rejected",
        "correction-acknowledged",
        "correction-rejected",
        "cancellation-open",
        "cancellation-sent",
        "cancellation-acknowledged",
        "cancellation-rejected",
        "cancellation-accepted",
        ]

    @property
    def doc_number(self):
        return self.id

    def edi_submission_type(self):
        if self.status in ["open", "bol-syntax-rejected", "bol-rejected"]:
            return RailShipment.SubmissionType.Original
        elif "correction" in self.status:
            return RailShipment.SubmissionType.Correction
        elif self.status in ["cancellation-open"]:
            return RailShipment.SubmissionType.Cancellation
        else:
            return None

    @classmethod
    def find_control_number(cls, control_number):
        try:
            return RailShipment.objects.get(
                data__edi__contains=[{"control_number": control_number}]
            )
        except RailShipment.DoesNotExist:
            return None

    def find_shipment_submission_type(self, control_number):
        try:
            return list(
                filter(lambda x: x.control_number == control_number, self.data.edi)
            )[0].submission_type
        except IndexError:
            return None

    def _create_sub_billing(self):
        skip_fields = ["id", "status", "master_biling", "data"]
        rail_shipment = RailShipment()

        for field_name in [field.name for field in self._meta.fields if field.name not in skip_fields]:
            value = getattr(self, field_name)
            setattr(rail_shipment, field_name, value)

        rail_shipment.master_billing = self
        rail_shipment.status = "open"
        rail_shipment.save()
        return rail_shipment

    def create_sub_billings(self):
        assert self.status == "open", "Can not create sub billing when billing has history"
        assert self.sub_billings.count() == 0, "Can not create sub billing more than once"
        assert self.master_billing is None, "Can not create master billing more than once"

        for car in self.cars.all():
            sub_rail_shipment = self._create_sub_billing()
            car.rail_shipment = sub_rail_shipment
            car.save(update_fields=["rail_shipment"])

        self.status = "master-billing"
        self.save(update_fields=["status"])

    def create_sub_billing(self):
        assert self.is_master_billing(), "This is not master billing"
        assert self.cars.count() == 0, "There is car on master billing"
        return self._create_sub_billing()

    def set_for_cancellation(self, user, reason=""):
        assert (
            self.status in self.VALID_STATUSES_FOR_CANCELLATION
        ), "Only accepted shipments could be canceled"
        self.status = "cancellation-open"
        self.save(update_fields=["status"])
        RailShipmentNote(
            rail_shipment=self,
            note=f"Shipment cancellation requested by {user} on {timezone.localtime()}. Reason for cancellation: {reason}",
            is_write_protect=True,
        ).save()

    def set_for_correction(self, user):
        if self.master_billing is None:
            assert (
                self.status in self.VALID_STATUSES_FOR_CORRECTION
            ), "Shipment status is not valid for correction"
        if self.master_billing:
            assert (
                self.status in self.VALID_STATUSES_FOR_CORRECTION + ["open"]
            ), "Shipment status is not valid for correction"

        # master billing open correction should have no foot print
        if self.status != "open":
            self.status = "correction-open"
            self.save(update_fields=["status"])
            RailShipmentNote(
                rail_shipment=self,
                note=f"Shipment opened for correction by {user} on {timezone.localtime()}",
                is_write_protect=True,
            ).save()

    def check_single_car_billing_mandate(self):
        query = self.cars.all()
        for field in self.SINGLE_BILLING_FIELDS:
            query = query.exclude(**{f"{field}__isnull": True}).exclude(**{f"{field}__exact": ""})

        return query.exists()

    def ship(self, user, hazardous_certificate_person=""):
        if self.cars.count() > 1 and self.check_single_car_billing_mandate():
            raise Exception(
                "When cars in shipment has additional data, you can't bill them all in one billing"
            )
        if not self.get_gs_code():
            raise Exception(
                "You don't have required credentials to ship with this rail road"
            )

        if self.is_master_billing():
            for sub_billing in self.sub_billings.filter(status__in=["open", "correction-open"]):
                sub_billing.send_edi404(user)
            return

        self.hazardous_certificate_person = hazardous_certificate_person
        self.save(update_fields=["hazardous_certificate_person"])
        self.send_edi404(user)

    def cancel_shipment(self, user, reason=""):
        if not self.get_gs_code():
            raise Exception(
                "You don't have required credentials to ship with this rail road"
            )
        self.set_for_cancellation(user, reason)
        self.send_edi404(user)

    def send_edi404(self, user):
        edi_class = self.railroad.get_edi_class(404)
        data = edi404_data_from_shipment(self.id)
        edi = edi_class(data)
        filename, control_number, out = edi.send_to_ftp()
        submission_type = self.edi_submission_type()

        EdiDocument.objects.create(
            company=self.railroad.get_edi_company(self.get_gs_code()),
            source=self.railroad.name.lower(),
            doc_type="404",
            data=out,
            name=filename,
            processed=True,
        )

        edi_404_package = EDI404PackageSchema(
            control_number=control_number,
            user=user.email,
            user_id=user.id,
            submission_type=submission_type,
        )
        edi_404_package.edi_404 = EDIDataSchema(
            filename=filename, text=out, result="Sent", date=timezone.now()
        )

        self.data.edi.append(edi_404_package)

        if submission_type == RailShipment.SubmissionType.Original:
            self.status = "bol-sent"
        elif submission_type == RailShipment.SubmissionType.Correction:
            self.status = "correction-sent"
        elif submission_type == RailShipment.SubmissionType.Cancellation:
            self.status = "cancellation-sent"
        self.save(update_fields=["data", "status"])

    @classmethod
    def fetch_997(cls, railroad):
        edi_class = railroad.get_edi_class(997)
        result = edi_class.fetch_from_ftp()
        for edi in result:
            edi_document = EdiDocument.objects.create(
                company=railroad.get_edi_company(edi.company),
                source=railroad.name.lower(),
                doc_type="997",
                data=edi.edi_data,
                name=edi.filename,
            )
            control_number = edi.control_number
            shipment = cls.find_control_number(control_number)

            if shipment:
                if edi.is_validate():
                    if shipment.status == "bol-sent":
                        shipment.status = "bol-acknowledged"
                    elif shipment.status == "correction-sent":
                        shipment.status = "correction-acknowledged"
                    elif shipment.status == "cancellation-sent":
                        shipment.status = "cancellation-acknowledged"
                    else:
                        print(
                            f"997-Could not determine the shipment {shipment.id} status"
                        )

                else:
                    if shipment.status == "bol-sent":
                        shipment.status = "bol-syntax-rejected"
                    elif shipment.status == "correction-sent":
                        shipment.status = "correction-syntax-rejected"
                    elif shipment.status == "cancellation-sent":
                        shipment.status = "cancellation-syntax-rejected"
                    else:
                        print(
                            f"997-Could not determine the shipment {shipment.id} status"
                        )

                edi_package = shipment.data.get_edi(control_number)
                if edi_package.edi_997:
                    print(f"expecting empty edi_997 on control number {control_number}")
                    print(edi.edi_data)
                else:
                    edi_package.edi_997 = EDIDataSchema(
                        filename=edi.filename,
                        text=edi.edi_data,
                        result=edi.code,
                        date=timezone.now(),
                    )

                shipment.save(update_fields=["data", "status"])

                edi_document.processed = True
                edi_document.save(update_fields=["processed"])

                async_task(
                    shipment.send_email_status,
                    [edi_package.user],
                    group="Billing status",
                    task_name=f"Billing status {shipment.id}",
                )

            else:
                edi_document.processed = False
                edi_document.save(update_fields=["processed"])
                print(f"shipment {control_number} not found")

    @classmethod
    def fetch_824(cls, railroad):
        edi_class = railroad.get_edi_class(824)
        result = edi_class.fetch_from_ftp()
        for edi in result:
            edi_document = EdiDocument.objects.create(
                company=railroad.get_edi_company(edi.company),
                source=railroad.name.lower(),
                doc_type="824",
                data=edi.edi_data,
                name=edi.filename,
            )
            control_number = edi.control_number
            shipment = cls.find_control_number(control_number)

            # CP reply back to 404 no matter who is the sender!!
            if railroad.name == "CPR" and shipment is None:
                edi_document.processed = False
                edi_document.save(update_fields=["processed"])
                continue

            edi_submission_type = shipment.find_shipment_submission_type(control_number)

            if shipment:
                edi_status = edi.is_validate()

                if edi_submission_type == RailShipment.SubmissionType.Cancellation:
                    if edi_status == EDI824.ACCEPTED:
                        shipment.status = "cancellation-accepted"
                    elif edi_status == EDI824.ACCEPTED_WITH_ERROR:
                        shipment.status = "cancellation-accepted-with-error"
                    elif edi_status == EDI824.REJECTED:
                        shipment.status = "cancellation-rejected"

                elif edi_submission_type == RailShipment.SubmissionType.Correction:
                    if edi_status == EDI824.ACCEPTED:
                        shipment.status = "correction-accepted"
                    elif edi_status == EDI824.ACCEPTED_WITH_ERROR:
                        shipment.status = "correction-accepted-with-error"
                    elif edi_status == EDI824.REJECTED:
                        shipment.status = "correction-rejected"

                elif edi_submission_type == RailShipment.SubmissionType.Original:
                    if edi_status == EDI824.ACCEPTED:
                        shipment.status = "bol-accepted"
                    elif edi_status == EDI824.ACCEPTED_WITH_ERROR:
                        shipment.status = "bol-accepted-with-error"
                    elif edi_status == EDI824.REJECTED:
                        shipment.status = "bol-rejected"

                edi_package = shipment.data.get_edi(control_number)
                if edi_package.edi_824:
                    print(f"expecting empty edi_824 on control number {control_number}")
                    print(edi.edi_data)
                else:
                    edi_package.edi_824 = EDIDataSchema(
                        filename=edi.filename,
                        text=edi.edi_data,
                        result=str(edi.is_validate()),
                        date=timezone.now(),
                    )

                shipment.save(update_fields=["data", "status"])

                edi_document.processed = True
                edi_document.save(update_fields=["processed"])

                async_task(
                    shipment.send_email_status,
                    [edi_package.user],
                    group="Billing status",
                    task_name=f"Billing status {shipment.id}",
                )

                if edi_status == EDI824.ACCEPTED:
                    if edi_submission_type == RailShipment.SubmissionType.Cancellation:
                        shipment.void_shipment(cancellation_sent=True, user="SYSTEM")

                if edi_status in [EDI824.ACCEPTED, EDI824.ACCEPTED_WITH_ERROR]:
                    if edi_submission_type != RailShipment.SubmissionType.Cancellation:
                        shipment.send_email_bol_to_contacts(
                            to=[edi_package.user], bcc=[], realtime=False
                        )
                        async_task(
                            shipment.generate_BOL_archive,
                            control_number,
                            group="BOL Archive",
                            task_name=f"BOL Archive {shipment.id}",
                        )

            else:
                edi_document.processed = False
                edi_document.save(update_fields=["processed"])
                print(f"shipment {control_number} not found, edi 824")

    def send_email_status(self, to, bcc=[], additional_notes=""):
        body = f"""
        Hello,

        This is automated email.
        Your shipment from {self.origin.name} to {self.destination.name} with reference number {self.reference_number} has new status: {self.status}
        {additional_notes if additional_notes else ''}
        You can check the latest status in https://wheelhouse.vantedgelgx.com/

        Thank you.
        """

        email = PriorityEmailMessage(
            subject=f"Shipment Status {self.reference_number}",
            body=body,
            to=to,
            bcc=bcc,
            priority=1
        )

        email.send()

    def send_email_bol_to_contacts(self, to, bcc, realtime=True, archive_id=None):
        parties = self.contact_preferences.parties
        to.extend(self.contact_preferences.to)

        for party_name in parties:
            party = getattr(self, f"{party_name}", None)
            if party is not None:
                bcc.extend(party.data.get_contacts_email(["BOL"]))

        if realtime is False:
            async_task(
                self.send_bol,
                to,
                bcc,
                archive_id,
                group="BOL to contacts",
                task_name=f"BOL Email, Shipment {self.id}",
            )
        else:
            self.send_bol(to, bcc, archive_id)

    def send_bol(self, to, bcc, archive_id):
        from vantedge.wheelhouse.views.utils.pdf import generate_pdf

        email_context = {"shipment": self}
        subject = f"Shipment BOL {self.reference_number}"
        html_content = render_to_string(
            "email_templates/bol_email_content.html", context=email_context
        )
        text_content = html_to_text(html_content)
        email = PriorityEmailMessage(
            subject, text_content, to=to, bcc=bcc, priority=1
        )
        email.attach_alternative(html_content, "text/html")

        if not archive_id:
            attachment_content = generate_pdf(
                self.origin.company.company, "rail-bol-custom", self
            )
        else:
            attachment_content = self.bol_archives.get(id=archive_id).data_pdf

        if attachment_content:
            email.attach(
                filename=f"BOL_{self.reference_number}.pdf", content=attachment_content
            )
            email.send()

    def update_contact_preferences(self, preferences):
        assert set(preferences.keys()) == set(
            RailShipmentContactPreference.__fields__
        ), "railshipment contact preferences must match schema"
        self.contact_preferences = RailShipmentContactPreference(**preferences)
        self.save()

    def void_shipment(self, cancellation_sent, user, reason=""):
        from .railcar import LoadedRailCar
        request_type = "canceled" if cancellation_sent else "voided"
        cars = list(
            self.cars.annotate(car=Concat("car_mark", "car_number")).values_list(
                "car", flat=True
            )
        )

        for car in self.cars.all():
            if car.waybill:
                if car.waybill.trip:
                    car.waybill.trip.pattern = None
                    car.waybill.trip.save(update_fields=["pattern"])
                car.waybill = None
            car.rail_shipment = None
            car.status = LoadedRailCar.LoadedRailCarStatus.READY_TO_SHIP
            car.save(update_fields=["waybill", "rail_shipment", "status"])

        # status set during 824
        if cancellation_sent is False:
            self.status = "voided"
            self.save(update_fields=["status"])

        RailShipmentNote(
            rail_shipment=self,
            note=f"Shipment {request_type} on {timezone.localtime()} by {user}, cars: {cars}. Reason for voiding: {reason}",
            is_write_protect=True,
        ).save()

        inform_parties_of_void_billing = (
            self.origin.company.company.applicationconfiguration.inform_parties_of_void_billing
        )
        if self.data.has_accepted_billing and inform_parties_of_void_billing:
            to = []
            bcc = []
            if user.email:
                to.append(user.email)

            parties = self.contact_preferences.parties
            bcc.extend(self.contact_preferences.to)

            for party_name in parties:
                party = getattr(self, f"{party_name}", None)
                if party is not None:
                    bcc.extend(party.data.get_contacts_email(["BOL"]))

            async_task(
                self.send_email_status,
                to=to,
                bcc=bcc,
                additional_notes=reason,
                group="Billing status",
                task_name=f"Billing status {self.id}"
            )

    def generate_BOL_archive(self, control_number):
        from vantedge.wheelhouse.views.utils.pdf import generate_pdf

        data = generate_pdf(self.origin.company.company, "rail-bol-custom", self)
        bol = BOLArchive.objects.create(shipment=self, data_pdf=data)

        if control_number is not None:
            with transaction.atomic():
                shipment = RailShipment.objects.select_for_update(of=("self",)).get(
                    id=self.id
                )
                try:
                    edi_package = shipment.data.get_edi(control_number)
                    edi_package.bol_archive = bol.id
                    shipment.save(update_fields=["data"])
                except Exception as e:
                    print(f"error accured during generating bol archive; {e}")


class RailShipmentNote(EasyAuditModelConfigMixin, TimeStampedModel):
    rail_shipment = models.ForeignKey(
        RailShipment, related_name="notes", on_delete=models.CASCADE
    )
    note = models.TextField()
    show_on_bol = models.BooleanField(default=False)
    is_write_protect = models.BooleanField(default=False)


class BOLArchive(EasyAuditModelConfigMixin, TimeStampedModel):
    shipment = models.ForeignKey(
        RailShipment, on_delete=models.CASCADE, related_name="bol_archives"
    )
    data_pdf = models.BinaryField()

    class Meta:
        ordering = ("-id",)

    def __str__(self) -> str:
        return f"BOL shipment {self.shipment.id}"
