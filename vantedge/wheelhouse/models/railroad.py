from django.db import models
from vantedge.edi.models import EdiCompany
from vantedge.edi.comm.bn import EDI404BN, EDI997BN, EDI824BN
from vantedge.edi.comm.cp import EDI404CP, EDI997CP, EDI824CP
from vantedge.edi.comm.cn import EDI404C<PERSON>, EDI997CN, EDI824C<PERSON>
from vantedge.edi.comm.csxt import EDI404CSXT, EDI997CSXT, EDI824CSXT
from vantedge.util.mixins import EasyAuditModelConfigMixin

class Railroad(EasyAuditModelConfigMixin, models.Model):
    EDI_MAPPING = {
        "BNSF": {"EDI_404": EDI404BN, "EDI_997": EDI997BN, "EDI_824": EDI824BN},
        "CN": {"EDI_404": EDI404CN, "EDI_997": EDI997C<PERSON>, "EDI_824": EDI824CN},
        "CPR": {"EDI_404": EDI404CP, "EDI_997": EDI997<PERSON>, "EDI_824": EDI824CP},
        "CSXT": {"EDI_404": EDI404CSXT, "EDI_997": EDI997CSXT, "EDI_824": EDI824CSXT},
    }

    CSXT = "CSXT", "CSXT"
    BNSF = "BNSF", "BNSF"
    CPR = "CPR", "CPR"
    CN = "CN", "CN"

    RAILROAD_CHOICES = (CSXT, BNSF, CPR, CN)

    name = models.CharField(max_length=30, choices=RAILROAD_CHOICES, unique=True)

    def __str__(self):
        return self.name

    def get_edi_class(self, edi_number):
        return self.EDI_MAPPING[self.name].get(f"EDI_{edi_number}")

    def gs_code_attr(self):
        return f"gs_code_{self.name.lower()}"

    def get_edi_company(self, gs_code):
        """ find edi company based on gs code value in edi """
        return EdiCompany.objects.filter(
            **{f"{self.gs_code_attr()}__iexact": gs_code}
        ).first()

    def get_gs_code(self, company):
        edi_company = EdiCompany.objects.get(company=company)
        return getattr(edi_company, self.gs_code_attr())
