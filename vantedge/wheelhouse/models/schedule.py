from datetime import date

import arrow
from django.contrib.postgres.fields import <PERSON><PERSON><PERSON><PERSON>ield
from django.db import models
from django.db.models import Q
from model_utils import Choices
from model_utils.models import TimeStampedModel

from vantedge.wheelhouse.models import AdditionalFieldMixin
from vantedge.wheelhouse.models.company import Party, WheelhouseCompany
from vantedge.util.mixins import EasyAuditModelConfigMixin
from vantedge.wheelhouse.models.company import Party



class ShippingSchedule(AdditionalFieldMixin, EasyAuditModelConfigMixin, TimeStampedModel):
    is_for_empty_shipment = models.BooleanField(default=False)
    origin = models.ForeignKey(
        "Facility",
        on_delete=models.CASCADE,
        related_name="out_schedule_set",
        null=True,
        blank=True,
    )
    destination = models.ForeignKey(
        "Facility",
        on_delete=models.CASCADE,
        related_name="in_schedule_set",
        null=True,
        blank=True,
    )
    purchase_contract = models.ForeignKey(
        "Contract",
        on_delete=models.CASCADE,
        related_name="purchase_schedules",
        null=True,
    )
    sale_contract = models.ForeignKey(
        "Contract", on_delete=models.CASCADE, related_name="sale_schedules", null=True
    )
    cars_quantity = models.IntegerField(default=None, null=True, blank=True)
    schedule = ArrayField(models.JSONField(), default=list, blank=True)
    auto_schedule_batch = models.IntegerField(default=None, null=True, blank=True)
    auto_schedule_periodic_days = models.IntegerField(
        default=None, null=True, blank=True
    )
    auto_schedule_shipment_count = models.IntegerField(
        default=None, null=True, blank=True
    )
    allow_shipment = models.BooleanField(default=True)
    is_ratable = models.BooleanField(default=False)

    class Meta:
        ordering = ["id"]
        abstract = True

    @property
    def contract(self):
        return self.purchase_contract if self.purchase_contract else self.sale_contract

    def auto_schedule(self, *args, **kwargs):
        cars_quantity = kwargs.get("cars_quantity")
        auto_schedule_batch = kwargs.get("auto_schedule_batch")
        auto_schedule_periodic_days = kwargs.get("auto_schedule_periodic_days")
        contract = self.contract

        assert bool(
            cars_quantity
        ), "Ambiguous parameters - Missing `cars_quantity`."

        assert bool(
            contract
        ), "Failed to automate scheduling. Missing connected to a purchase/sale contract."

        if contract:
            from .calcs.schedule import auto_schedule

            _auto_schedule = list(
                auto_schedule(
                    date.today(),
                    contract.end_date,
                    cars_quantity,
                    auto_schedule_batch,
                    auto_schedule_periodic_days,
                )
            )

            auto_scheduled_info = {
                "cars_quantity": cars_quantity,
                "auto_schedule_batch": auto_schedule_batch,
                "auto_schedule_periodic_days": auto_schedule_periodic_days,
                "shipping_schedule": _auto_schedule,
                "is_ratable": False
            }

            return auto_scheduled_info

class RailShippingSchedule(ShippingSchedule):
    class Meta:
        ordering = ["-created"]

    US_CUSTOM_CLEARANCE_OFFICE_CHOICES = (
        ("0102", "Bangor ME"),
        ("0104", "Jackman ME"),
        ("0106", "Houlton ME"),
        ("0108", "Van Buren ME"),
        ("0109", "Madewaska, ME"),
        ("0115", "Calais ME"),
        ("0203", "Richford VT"),
        ("0206", "Pittsburgh NH"),
        ("0207", "Burlington VT"),
        ("0209", "Derby Line VT"),
        ("0211", "Norton VT"),
        ("0212", "Highgate VT"),
        ("0401", "Boston MA"),
        ("0410", "E Granby-Bridgeport CT"),
        ("0411", "E Granby-Bradley Int'l Arpt"),
        ("0412", "E Granby-New Haven CT"),
        ("0701", "Ogdensburg NY"),
        ("0704", "Massena NY"),
        ("0708", "Alexandria Bay NY"),
        ("0712", "Champlain NY"),
        ("0901", "Buffalo Buffalo NY"),
        ("1703", "Atlanta-Savannah GA"),
        ("1704", "Atlanta GA"),
        ("2704", "Los Angeles CA"),
        ("3004", "Blaine WA"),
        ("3302", "Eastport ID"),
        ("3310", "Sweetgrass MT"),
        ("3316", "Piegan MT"),
        ("3318", "Rooseville MT"),
        ("3401", "Pembina ND"),
        ("3403", "Portal ND"),
        ("3422", "Dunseith ND"),
        ("3601", "Duluth MN"),
        ("3604", "Int'l Falls MN"),
        ("3801", "Detroit MI"),
        ("3802", "Port Huron MI"),
        ("3803", "Sault Ste Marie MI"),
        ("3806", "Grand Rapids MI"),
        ("3807", "Romulus MI"),
        ("3901", "Chicago IL"),
        ("4102", "Cincinnati OH"),
        ("4601", "JFK-Newark NJ"),
        ("4701", "JFK Arpt NY"),
        ("5201", "Miami FL (OCN)"),
        ("5204", "Miami-West Palm Beach FL"),
        ("5206", "Miami Intl Arpt FL"),
        ("5301", "Houston TX"),
    )

    SHIPMENT_METHOD_OF_PAYMENT_CHOICES = (
        ("11", "Rule 11 Shipment"),
        ("BP", "Paid by Buyer"),
        ("CA", "Advance Collect"),
        ("CC", "Collect"),
        ("CD", "Collect on Delivery"),
        ("CF", "Collect, Freight Credited Back to Customer"),
        ("DE", "Per Contract"),
        ("DF", "Defined by Buyer and Seller"),
        ("FO", "FOB Port of Call"),
        ("HP", "Half Prepaid"),
        ("MX", "Mixed"),
        ("NC", "Service Freight, No Charge"),
        ("NR", "Non Revenue"),
        ("PA", "Advance Prepaid"),
        ("PB", "Customer Pick-up/Backhaul"),
        ("PC", "Prepaid but Charged to Customer"),
        ("PD", "Prepaid by Processor"),
        ("PE", "Prepaid and Summary Bill"),
        ("PL", "Prepaid Local, Collect Outstate"),
        ("PO", "Prepaid Only"),
        ("PP", "Prepaid (by Seller)"),
        ("PS", "Paid by Seller"),
        ("PU", "Pickup"),
        ("RC", "Return Container Freight Paid by Customer"),
        ("RF", "Return Container Freight Free"),
        ("RS", "Return Container Freight Paid by Supplier"),
        ("TP", "Third Party Pay"),
        ("WC", "Weight Condition"),
    )

    name = models.CharField(max_length=80, blank=True, default="Shipping Schedule")
    FREIGHT_CHARGE_TYPE = Choices("Collect", "Prepaid", "Rule11", "Non-Revenue")
    freight_charges = models.CharField(
        choices=FREIGHT_CHARGE_TYPE, max_length=20, blank=True, default="Prepaid"
    )
    fleets = ArrayField(
        models.CharField(max_length=50, default="", blank=True, null=True),
        size=10,
        default=list,
        blank=True,
    )
    fleets_exclude = ArrayField(
        models.CharField(max_length=50, default="", blank=True, null=True),
        size=10,
        default=list,
        blank=True,
    )
    route = models.CharField(max_length=50, blank=True)
    product = models.ForeignKey(
        "Product", on_delete=models.PROTECT, null=True, blank=True
    )
    consignee = models.ForeignKey(
        Party,
        on_delete=models.PROTECT,
        related_name="consignee_schedule_set",
        null=True,
    )
    accounting_rule_11 = models.ForeignKey(
        Party,
        related_name="accounting_rule_11_schedule_set",
        null=True,
        blank=True,
        on_delete=models.PROTECT,
    )
    freight_payer = models.ForeignKey(
        Party,
        on_delete=models.PROTECT,
        related_name="freight_payer_schedule_set",
        null=True,
    )
    beneficial_owner = models.ForeignKey(
        Party,
        on_delete=models.PROTECT,
        related_name="beneficial_owner_schedule_set",
        null=True,
        blank=True,
    )
    account_of = models.ForeignKey(
        Party,
        on_delete=models.PROTECT,
        related_name="account_of_schedule_set",
        null=True,
    )
    account_of_origin = models.ForeignKey(
        Party,
        related_name="account_of_origin_schedule_set",
        null=True,
        blank=True,
        on_delete=models.PROTECT,
    )
    account_of_destination = models.ForeignKey(
        Party,
        related_name="account_of_destination_schedule_set",
        null=True,
        blank=True,
        on_delete=models.PROTECT,
    )
    shipper = models.ForeignKey(
        Party, on_delete=models.PROTECT, related_name="shipper_schedule_set", null=True
    )
    ship_from = models.ForeignKey(
        Party,
        related_name="ship_from_schedule_set",
        on_delete=models.PROTECT,
        null=True,
        blank=True,
    )
    forwarder = models.ForeignKey(
        Party,
        related_name="forwarder_schedule_set",
        on_delete=models.PROTECT,
        null=True,
    )
    customs = models.ForeignKey(
        Party, on_delete=models.PROTECT, related_name="customs_schedule_set", null=True
    )
    notify_party = models.ForeignKey(
        Party,
        on_delete=models.PROTECT,
        related_name="notify_party_schedule_set",
        null=True,
    )
    notify_party_2 = models.ForeignKey(
        Party,
        on_delete=models.PROTECT,
        related_name="notify_party_2_schedule_set",
        null=True,
    )
    care_party = models.ForeignKey(
        Party,
        on_delete=models.PROTECT,
        related_name="care_party_schedule_set",
        null=True,
    )
    monitoring_party = models.ForeignKey(
        Party,
        on_delete=models.PROTECT,
        related_name="monitoring_schedule_set",
        null=True,
        blank=True,
    )
    pickup_party = models.ForeignKey(
        Party,
        on_delete=models.PROTECT,
        related_name="pickup_party_schedule_set",
        null=True,
    )
    ultimate_consignee = models.ForeignKey(
        Party,
        on_delete=models.PROTECT,
        related_name="ultimate_consignee_schedule_set",
        null=True,
    )
    custom_broker_us = models.ForeignKey(
        Party,
        on_delete=models.PROTECT,
        related_name="custom_broker_us_schedule_set",
        null=True,
    )
    custom_broker_mx = models.ForeignKey(
        Party,
        on_delete=models.PROTECT,
        related_name="custom_broker_mx_schedule_set",
        null=True,
    )
    importer = models.ForeignKey(
        Party, on_delete=models.PROTECT, related_name="importer_schedule_set", null=True
    )
    exporter = models.ForeignKey(
        Party, on_delete=models.PROTECT, related_name="exporter_schedule_set", null=True
    )

    us_custom_office = models.CharField(
        max_length=4, choices=US_CUSTOM_CLEARANCE_OFFICE_CHOICES, blank=True, null=True
    )

    basis_of_price = models.CharField(
        max_length=2,
        choices=(("CT", "Contract"), ("PE", "Price Per Each")),
        blank=True,
        null=True,
    )

    estimate_freight_charge = models.PositiveIntegerField(
        blank=True, null=True, help_text="Cost To Border"
    )
    estimated_days_to_border = models.PositiveIntegerField(blank=True, null=True)
    freight_cost = models.DecimalField(
        max_digits=8, decimal_places=4, null=True, help_text="Per Barrel"
    )
    is_parties_related = models.BooleanField(default=False)
    shipment_method_of_payment = models.CharField(
        max_length=2, choices=SHIPMENT_METHOD_OF_PAYMENT_CHOICES, blank=True, null=True
    )

    # Transborder Shipment Data
    customs_entry_type = models.CharField(
        max_length=10,
        choices=(
            ("61", "Immediate Transportation"),
            ("62", "Transportation Exportation (T&E) bond"),
        ),
        blank=True,
        null=True,
    )

    export_declaration_type = models.CharField(
        max_length=40,
        choices=(
            ("CAED", "Canadian Automated Export Declaration"),
            ("G7", "G7 EDI Export Reporting"),
            ("Summary Reporting", "Summary Reporting ID"),
            ("B13A", "B13A Export Declaration"),
            ("NDR", "No Declaration Required"),
        ),
        blank=True,
        null=True,
    )

    export_reference_number = models.CharField(max_length=40, blank=True, null=True)

    def volume_scheduled(self, date_range=None):
        schedules = [sch for sch in self.schedule]
        if date_range:
            dateRangeFrom = date_range.get("dateRangeFrom")
            dateRangeTo = date_range.get("dateRangeTo")

            if dateRangeFrom:
                filterFrom = lambda x: arrow.get(x["date"]) >= arrow.get(dateRangeFrom)
                schedules = list(filter(filterFrom, schedules))

            if dateRangeTo:
                filterTo = lambda x: arrow.get(x["date"]) <= arrow.get(dateRangeTo)
                schedules = list(filter(filterTo, schedules))

        return sum(int(sch["quantity"] or 0) for sch in schedules)

    def volume_shipped(self, date_range=None):
        from ...trackandtrace.models import Trip

        shipped_filters = Q(seller_contract=self) | Q(buyer_contract=self)
        if date_range:
            dateRangeFrom = date_range.get("dateRangeFrom")
            dateRangeTo = date_range.get("dateRangeTo")

            if dateRangeFrom:
                shipped_filters &= Q(
                    departure_timestamp__date__gte=arrow.get(dateRangeFrom).date()
                )
            if dateRangeTo:
                shipped_filters &= Q(
                    departure_timestamp__date__lte=arrow.get(dateRangeTo).date()
                )

        shipped = Trip.objects.filter(shipped_filters)
        return shipped.count()

    def shipments_car_count(self, date_range=None):
        from vantedge.wheelhouse.models import LoadedRailCar, RailShipment

        shipped_filters = Q(rail_shipment__status__in=RailShipment.CLOSED_STATUSES)
        if date_range:
            dateRangeFrom = date_range.get("dateRangeFrom")
            dateRangeTo = date_range.get("dateRangeTo")

            if dateRangeFrom:
                shipped_filters &= Q(
                    rail_shipment__ship_date__gte=arrow.get(dateRangeFrom).date()
                )
            if dateRangeTo:
                shipped_filters &= Q(
                    rail_shipment__ship_date__lte=arrow.get(dateRangeTo).date()
                )

        cars = LoadedRailCar.objects.filter(rail_shipment__schedule=self).filter(shipped_filters)
        return cars.count()

    def __str__(self):
        return self.name
