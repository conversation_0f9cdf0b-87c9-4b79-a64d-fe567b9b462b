from django.core.validators import validate_email
from django.core.exceptions import ValidationError

import string
import re

from pydantic import BaseModel

CHAR_SET = set(string.ascii_letters + string.digits)
CHAR_SET_WITH_DASH = CHAR_SET.union("-")
CHAR_SET_ADDRESS = CHAR_SET.union("-", " ", "#", ",", ".")
CHAR_SET_CITY = CHAR_SET.union(" ", ".", ",")
CHAR_SET_ROUTE = CHAR_SET.union(" ", "/")

US_STATES = [
    "AK",
    "AL",
    "AR",
    "AZ",
    "CA",
    "CO",
    "CT",
    "DC",
    "DE",
    "FL",
    "GA",
    "HI",
    "IA",
    "ID",
    "IL",
    "IN",
    "KS",
    "KY",
    "LA",
    "MA",
    "MD",
    "ME",
    "MI",
    "MN",
    "MO",
    "MS",
    "MT",
    "NC",
    "ND",
    "NE",
    "NH",
    "NJ",
    "NM",
    "NV",
    "NY",
    "OH",
    "OK",
    "OR",
    "PA",
    "RI",
    "SC",
    "SD",
    "TN",
    "TX",
    "UT",
    "VA",
    "VT",
    "WA",
    "WI",
    "WV",
    "WY",
]

CA_STATES = ["AB", "BC", "CN", "MB", "NB", "NF", "NS", "ON", "PE", "PQ", "SK", "YT"]

MX_STATES = [
    "AG",
    "BJ",
    "BS",
    "CH",
    "CI",
    "CL",
    "CP",
    "CU",
    "DF",
    "DG",
    "EM",
    "GJ",
    "GR",
    "HG",
    "JA",
    "MH",
    "MR",
    "MX",
    "NA",
    "NL",
    "OA",
    "PU",
    "QA",
    "QR",
    "SL",
    "SO",
    "SI",
    "TA",
    "TL",
    "TM",
    "VL",
    "YC",
    "ZT",
]

ALL_STATES = US_STATES + CA_STATES + MX_STATES

CA_POSTALCODE_PATTERN = re.compile("^[A-Za-z]\d[A-Za-z] ?\d[A-Za-z]\d$")
US_POSTALCODE_PATTERN = re.compile("[0-9]{5}(?:-[0-9]{4})?$")
MX_POSTALCODE_PATTERN = re.compile("^[0-9]{5}$")
PHONE_PATTERN = re.compile("^[0-9]{3}-?[0-9]{3}-?[0-9]{4}$")
IRS_PATTERN = re.compile("[0-9]{2}-[0-9]{7}")
MID_PATTERN = re.compile("^[A-Za-z]{2}[a-zA-Z0-9]{11,13}")
HTS_PATTERN = re.compile("^[0-9]{4}\.[0-9]{2}\.[0-9]{4}$")

PART_LIST = [
    "ARO - AROMATIC HYDROCARGON MIXTURE",
    "DIS - INSTOW DISTILLATE",
    "DIS - PETROLEUM DISTILLATE 25API OR HIGHER",
    "KERR - KERROBERT DISTILLATE",
    "MOTOR - MOTOR FUEL",
    "RES - RESIDUAL FUEL OIL",
    "ULSD - ULTRA LOW SULPHUR DIESEL",
    "VGO - VGO MARINE FUEL",
]


def validate_party_data(party_data):
    if isinstance(party_data, BaseModel):
        party_data = party_data.dict()

    for key, value in party_data.items():
        if key == "address":
            validate_data_address_fields(value)

        elif key == "erp":
            validate_data_erp_fields(value)

        elif key == "extra_data":
            validate_data_extra_data_fields(value)

        elif key == "contact":
            validate_data_contact_fields(value)


def validate_data_address_fields(address_data):
    for key, value in address_data.items():
        if key == "state":
            if value not in ALL_STATES:
                raise ValidationError("State is not valid")

        elif key == "city":
            if len(value) < 3 or not set(value).issubset(CHAR_SET_CITY):
                raise ValidationError("City is not valid")

        elif key == "country":
            if value not in ["US", "CA", "MX"]:
                raise ValidationError("Country is not valid")
            if value == "CA" and address_data.get("state") not in CA_STATES:
                raise ValidationError("Canada State is not valid")
            if value == "US" and address_data.get("state") not in US_STATES:
                raise ValidationError("US State is not valid")
            if value == "MX" and address_data.get("state") not in MX_STATES:
                raise ValidationError("Mexico State is not valid")

        elif key == "name":
            if len(value) < 3:
                raise ValidationError("Name is missing in address section")

        elif key == "address_1" or key == "address1":
            if len(value) < 3 or not set(value).issubset(CHAR_SET_ADDRESS):
                raise ValidationError("Primary Address is not valid")

        elif key == "address_2" or key == "address2":
            if not set(value).issubset(CHAR_SET_ADDRESS):
                raise ValidationError("Secondary Address is not valid")

        elif key == "postal_code":
            if address_data.get("country", "") == "CA":
                if not re.match(CA_POSTALCODE_PATTERN, value):
                    raise ValidationError("Postal Code is not valid")
            if address_data.get("country", "") == "US":
                if not re.match(US_POSTALCODE_PATTERN, value):
                    raise ValidationError("Postal Code is not valid")
            if address_data.get("country", "") == "MX":
                if not re.match(MX_POSTALCODE_PATTERN, value):
                    raise ValidationError("Postal Code is not valid")


def validate_data_erp_fields(erp_data):
    if (
        erp_data.get("ccn")
        or erp_data.get("erap")
        or erp_data.get("ccn_telephone")
        or erp_data.get("erap_telephone")
    ):
        if not (
            erp_data.get("ccn")
            and erp_data.get("erap")
            and erp_data.get("ccn_telephone")
            and erp_data.get("erap_telephone")
        ):
            raise ValidationError("Emergency response plan data is not complete")

    for key, value in erp_data.items():
        if key in ["erap_telephone", "ccn_telephone", "canutec_telephone"]:
            if value and not re.match(PHONE_PATTERN, value):
                raise ValidationError(f'Emergency phone number "{value}" are not valid')

        elif key in ["ccn", "canutec"]:
            if value and not set(value).issubset(CHAR_SET_WITH_DASH):
                raise ValidationError(f'Customer Number value "{value}" is not valid')

        elif key == "erap":
            if value and not set(value).issubset(CHAR_SET_WITH_DASH):
                raise ValidationError(f'ERAP value "{value}" is not valid')


def validate_data_extra_data_fields(extra_data):
    for key, value in extra_data.items():
        if key == "irs":
            if value and not re.match(IRS_PATTERN, value):
                raise ValidationError("IRS format is ##-#######")

        elif key == "manufacture_code":
            if value and not re.match(MID_PATTERN, value):
                raise ValidationError("MID value is not valid")

        elif key == "export_status_code":
            if extra_data.get("export_license_number") and not value:
                raise ValidationError("Export Status Code should be selected")

def validate_data_contact_fields(contact_data):
    for contact in contact_data:
        for key, value in contact.items():
            if key == "email":
                try:
                    validate_email(value)
                except:
                    raise ValidationError(f"Email {value} is not valid")


def validate_facility_data(facility_data):
    if isinstance(facility_data, BaseModel):
        facility_data = facility_data.dict()
    for key, value in facility_data.items():
        if key == "address":
            validate_facility_address_fields(value)


def validate_facility_address_fields(address_data):
    for key, value in address_data.items():
        if key == "state":
            if value not in ALL_STATES:
                raise ValidationError("State is not valid")

        elif key == "city":
            if len(value) < 3 or not set(value).issubset(CHAR_SET_CITY):
                raise ValidationError("City is not valid")

        elif key == "country":
            if value not in ["US", "CA", "MX"]:
                raise ValidationError("Country is not valid")
            if value == "CA" and address_data.get("state") not in CA_STATES:
                raise ValidationError("Canada State is not valid")
            if value == "US" and address_data.get("state") not in US_STATES:
                raise ValidationError("US State is not valid")
            if value == "MX" and address_data.get("state") not in MX_STATES:
                raise ValidationError("Mexico State is not valid")


def validate_product_data(product_data):
    if isinstance(product_data, BaseModel):
        product_data = product_data.dict()

    for key, value in product_data.items():
        if key == "codes":
            validate_product_codes_fields(value)


def validate_product_codes_fields(codes_data):
    for key, value in codes_data.items():
        if key == "part_description":
            if value and value not in PART_LIST:
                raise ValidationError("Part Description is not valid")

        elif key == "hts":
            if value and not re.match(HTS_PATTERN, value):
                raise ValidationError("HTS pattern is ####.##.####")

        else:
            if value:
                try:
                    value.encode("ascii")
                except:
                    raise ValidationError(f"{key} has non ascii value")

def validate_route(route):
    route = "/".join([x.strip() for x in route.split("/")])

    if not route or not set(route).issubset(CHAR_SET_ROUTE):
        return False, "Route is not valid. Use / to split the route, Example RR/INTCHG/CR"

    parsed_route = route.split("/")
    if len(parsed_route) % 2 != 1:
        return False, "Invalid route. Need odd number of connections, ex. RR/INTCHG/RR"

    if not all(map(lambda x: x != "", route.split("/"))):
        return False, "Invalid route. Missing connection."

    return True, route
