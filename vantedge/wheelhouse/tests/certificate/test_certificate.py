from rest_framework import status

from vantedge.wheelhouse.models import Certificate
from vantedge.wheelhouse.tests.base.base_api_test_case import BaseTestCase
from vantedge.wheelhouse.tests.factories.certificate import CertificateTypeFactory, CertificateDataFactory, \
    CertificateFactory
from vantedge.wheelhouse.tests.factories.company import WheelhouseCompanyFactory


class TestCertificate(BaseTestCase):
    certificate_type_endpoint = 'certificatetype/'
    certificate_endpoint = 'certificate/'

    def test_get__certificate_type_exists__success(self):
        certificate_type_data = CertificateDataFactory(description='Some description')
        certificate_type = CertificateTypeFactory(
            company=self.wheelhouse_company,
            name='Fake Cert',
            data=certificate_type_data
        )

        response = self.get(self.certificate_type_endpoint)

        self.assertEqual(status.HTTP_200_OK, response.status_code)
        self.assertEqual(certificate_type.id, response.data[0]['id'])
        self.assertEqual(certificate_type.name, response.data[0]['name'])
        self.assertEqual(certificate_type_data.description, response.data[0]['data'].description)

    def test_get__different_company__return_none(self):
        certificate_type_data = CertificateDataFactory(description='Some description')
        different_wheelhouse_company = WheelhouseCompanyFactory()
        CertificateTypeFactory(
            company=different_wheelhouse_company,
            name='Fake Cert',
            data=certificate_type_data
        )

        response = self.get(self.certificate_type_endpoint)

        self.assertEqual(status.HTTP_200_OK, response.status_code)
        self.assertEqual(0, len(response.data))

    def test_post_bulk_update_date__success(self):
        certificate_type_data = CertificateDataFactory(description='Some description')
        certificate_type = CertificateTypeFactory(
            company=self.wheelhouse_company,
            name='Fake Cert',
            data=certificate_type_data
        )
        certificate = CertificateFactory(
            certificate_type=certificate_type
        )

        data = [
            {
                'id': certificate.id,
                'certificate_type': certificate_type.id
            }
        ]

        response = self.post(self.certificate_endpoint + 'bulk_update_date/', data)

        self.assertEqual(status.HTTP_200_OK, response.status_code)

    def test_post_bulk_update_date__certificate_type_does_not_match__return_no_content(self):
        certificate_type_data = CertificateDataFactory(description='Some description')
        certificate_type = CertificateTypeFactory(
            company=self.wheelhouse_company,
            name='Fake Cert',
            data=certificate_type_data
        )
        certificate = CertificateFactory(
            certificate_type=certificate_type
        )

        data = [
            {
                'id': certificate.id
            }
        ]

        response = self.post(self.certificate_endpoint + 'bulk_update_date/', data)

        self.assertEqual(status.HTTP_204_NO_CONTENT, response.status_code)

    def test_post_bulk_update_date__certificate_does_not_exist__certificate_created(self):
        certificate_type_data = CertificateDataFactory(description='Some description')
        certificate_type = CertificateTypeFactory(
            company=self.wheelhouse_company,
            name='Fake Cert',
            data=certificate_type_data
        )
        data = [
            {
                'id': 12345,
                'certificate_type': certificate_type.id
            }
        ]

        response = self.post(self.certificate_endpoint + 'bulk_update_date/', data)

        self.assertEqual(status.HTTP_201_CREATED, response.status_code)
        self.assertEqual(1, Certificate.objects.count())
