from django.contrib.contenttypes.models import ContentType
from rest_framework import status

from vantedge.schedule.models import ScheduleCompany
from vantedge.users.models import Company, ApplicationConfiguration
from vantedge.wheelhouse.models import WheelhouseCompany
from vantedge.wheelhouse.tests.base.base_api_test_case import BaseTestCase
from vantedge.wheelhouse.tests.factories.group import GroupFactory
from vantedge.wheelhouse.tests.factories.permission import PermissionFactory


class TestCompany(BaseTestCase):
    endpoint = 'company/'

    def test_post_create_related_carrier_company__creates_companies(self):
        PermissionFactory(
            codename='access_menu__reports',
            content_type_id=ContentType.objects.get(app_label='reports', model='report').id
        )
        PermissionFactory(
            codename='slot_read_only_access',
            content_type_id=ContentType.objects.get(app_label='schedule', model='slot').id
        )
        PermissionFactory(
            codename='configuration__user_administration',
            content_type_id=ContentType.objects.get(app_label='users', model='user').id
        )
        carrier_admin_group = GroupFactory(name='carrier_admin')
        company_name = 'Test Company'
        expected_config_roles = ['DRIVER', 'CARRIER_DISPATCH']
        expected_number_of_permissions = 7
        data = {
            'carrier_company_name': company_name,
        }

        response = self.post(self.endpoint + 'create_related_carrier_company/', data)

        self.assertEqual(status.HTTP_201_CREATED, response.status_code)

        user_company = Company.objects.get(name=company_name)
        self.assertEqual(company_name, user_company.name)

        schedule_company = ScheduleCompany.objects.get(company=user_company)
        self.assertEqual(user_company, schedule_company.company)
        self.assertEqual(schedule_company.company_id, user_company.id)
        self.assertEqual(schedule_company.related_companies.count(), 1)

        wheelhouse_company = WheelhouseCompany.objects.get(company=user_company)
        self.assertEqual(user_company, wheelhouse_company.company)
        self.assertEqual(wheelhouse_company.company_id, user_company.id)

        application_config = ApplicationConfiguration.objects.get(company_id=user_company.id)
        self.assertEqual(application_config.schedule_access_mode, ApplicationConfiguration.AccessMode.READONLY)
        self.assertEqual(application_config.available_roles, expected_config_roles)
        self.assertEqual(application_config.available_groups.count(), 1)
        self.assertEqual(application_config.available_groups.first(), carrier_admin_group)
        self.assertEqual(application_config.available_permissions.count(), expected_number_of_permissions)

    def test_post_create_related_carrier_company___user_is_not_super_user__returns_forbidden(self):
        self.user.is_superuser = False
        self.user.save()

        company_name = 'Test Company'
        data = {
            'carrier_company_name': company_name,
        }

        response = self.post(self.endpoint + 'create_related_carrier_company/', data)

        self.assertEqual(status.HTTP_403_FORBIDDEN, response.status_code)
