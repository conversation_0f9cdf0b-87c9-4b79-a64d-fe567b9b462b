from factory import SubFactory, Faker
from factory.django import DjangoModelFactory

from vantedge.users.models import Company
from vantedge.wheelhouse.models import WheelhouseCompany


class CompanyFactory(DjangoModelFactory):
    name = Faker('company')

    class Meta:
        model = Company


class WheelhouseCompanyFactory(DjangoModelFactory):
    company = SubFactory(CompanyFactory)

    class Meta:
        model = WheelhouseCompany
