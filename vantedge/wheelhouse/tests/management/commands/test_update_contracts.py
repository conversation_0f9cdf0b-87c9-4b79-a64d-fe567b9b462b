from django.core.management import call_command

from vantedge.wheelhouse.models import Contract
from vantedge.wheelhouse.tests.base.base_api_test_case import BaseTestCase
from vantedge.wheelhouse.tests.factories.contract import ContractFactory


class TestUpdateContracts(BaseTestCase):
    def test_update_contracts_command__to_is_none__success(self):
        contract_to_is_none = ContractFactory(contact_preferences={"to": None, "parties": []})

        self.assertIsNone(contract_to_is_none.contact_preferences.get('to'))

        call_command('update_contact_preferences')

        updated_contract = Contract.objects.get(id=contract_to_is_none.id)
        updated_contact_preferences = {"to": [], "parties": []}

        self.assertEqual(updated_contact_preferences, updated_contract.contact_preferences)

    def test_update_contracts_command__parties_exist__parties_unchanged(self):
        parties = ["party1", 'part2']
        original_contract = ContractFactory(contact_preferences={"to": None, "parties": parties})

        self.assertEqual(parties, original_contract.contact_preferences.get('parties'))

        call_command('update_contact_preferences')

        updated_contract = Contract.objects.get(id=original_contract.id)
        updated_contact_preferences = {"to": [], "parties": parties}

        self.assertEqual(updated_contact_preferences, updated_contract.contact_preferences)
