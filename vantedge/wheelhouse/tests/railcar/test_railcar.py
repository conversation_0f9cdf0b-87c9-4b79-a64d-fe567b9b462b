from vantedge.wheelhouse.views.serializers.railcar import TankCarSerializer
from vantedge.wheelhouse.models import TankCar, Product
from vantedge.wheelhouse.models.location import Facility
from vantedge.wheelhouse.tests.base.base_api_test_case import BaseTestCase
from rest_framework.exceptions import ValidationError
from unittest.mock import MagicMock


class RailCarTestCase(BaseTestCase):

    def test_seal_number_validation_empty_car_passes(self):
        """Test that when facility enforce_empty_car_seal_numbers=False, validation passes if no seal number is provided for empty tank cars."""

        facility = Facility.objects.create(
            name="Test Facility", 
            company=self.wheelhouse_company, 
            enforce_empty_car_seal_numbers=False
        )
        product = Product.objects.create(
            name="product",
            company=self.wheelhouse_company
        )

        mock_request = MagicMock()
        mock_request.user = self.user

        tank_car = TankCar.objects.create(current_location=facility, status='Ready To Load')

        serializer = TankCarSerializer(
            instance=tank_car,
            data={"shell_capacity": 0, "outage": 0,"volume": 0, "product": product.id, "car_mark": 'abc', "car_number": '123', "current_location": facility},
            context={"request": mock_request}    
        )

        self.assertTrue(serializer.is_valid())

    def test_seal_number_validation_empty_car_raises_error(self):
        """Test that when facility enforce_empty_car_seal_numbers=True, a validation error is raised if no seal number is provided for empty tank cars."""

        facility = Facility.objects.create(
            name="Test Facility", 
            company=self.wheelhouse_company, 
            enforce_empty_car_seal_numbers=True
        )
        product = Product.objects.create(
            name="product",
            company=self.wheelhouse_company
        )

        mock_request = MagicMock()
        mock_request.user = self.user

        tank_car = TankCar.objects.create(current_location=facility, status='Ready To Load')

        serializer = TankCarSerializer(
            instance=tank_car,
            data={"shell_capacity": 0, "outage": 0,"volume": 0, "product": product.id, "car_mark": 'abc', "car_number": '123', "current_location": facility},
            context={"request": mock_request}    
        )

        with self.assertRaisesMessage(ValidationError, 'Facility requires a Seal Number for empty cars.'):
            serializer.is_valid(raise_exception=True)

    def test_seal_number_validation_loaded_car_passes(self):
        """Test that when facility enforce_empty_car_seal_numbers=False, validation passes if no seal number is provided for loaded tank cars."""

        facility = Facility.objects.create(
            name="Test Facility", 
            company=self.wheelhouse_company, 
            enforce_loaded_car_seal_numbers=False
        )
        product = Product.objects.create(
            name="product",
            company=self.wheelhouse_company
        )

        mock_request = MagicMock()
        mock_request.user = self.user

        tank_car = TankCar.objects.create(current_location=facility, status='Ready To Load')

        serializer = TankCarSerializer(
            instance=tank_car,
            data={"shell_capacity": 101, "outage": 1,"volume": 100, "product": product.id, "car_mark": 'abc', "car_number": '123', "current_location": facility},
            context={"request": mock_request}    
        )

        self.assertTrue(serializer.is_valid())

    def test_seal_number_validation_loaded_car_raises_error(self):
        """Test that when enforce_loaded_car_seal_numbers=True, a validation error is raised if no seal number is provided for loaded tank cars."""

        facility = Facility.objects.create(
            name="Test Facility", 
            company=self.wheelhouse_company, 
            enforce_loaded_car_seal_numbers=True
        )
        product = Product.objects.create(
            name="product",
            company=self.wheelhouse_company
        )

        mock_request = MagicMock()
        mock_request.user = self.user

        tank_car = TankCar.objects.create(current_location=facility, status='Ready To Load')

        serializer = TankCarSerializer(
            instance=tank_car,
            data={"shell_capacity": 101, "outage": 1,"volume": 100, "product": product.id, "car_mark": 'abc', "car_number": '123', "current_location": facility},
            context={"request": mock_request}    
        )

        with self.assertRaisesMessage(ValidationError, 'Facility requires a Seal Number for loaded cars.'):
            serializer.is_valid(raise_exception=True)
