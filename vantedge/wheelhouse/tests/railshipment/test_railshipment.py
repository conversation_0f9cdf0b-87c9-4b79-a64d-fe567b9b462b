
from vantedge.wheelhouse.models import Party, Railroad, RailShipment, WheelhouseCompany
from vantedge.wheelhouse.models.location import Facility
from vantedge.wheelhouse.tests.base.base_api_test_case import BaseTestCase
from vantedge.wheelhouse.tests.factories.bol import BolNumberFactory


class RailShipmentTestCase(BaseTestCase):

    def test_update_bolnumber(self):
        """ Test the happy path for RailShipment.update_bolnumber """
        user_company = self.user.company
        wheelhouse_company = WheelhouseCompany.objects.get(company_id=user_company.id)
        party = Party.objects.create(company=wheelhouse_company)
        origin = Facility.objects.create(name="Test Facility", company=wheelhouse_company)
        rail_road = Railroad.objects.create(name="AA")
        rail_shipment = RailShipment.objects.create(
            origin=origin,
            reference_number="",
            consignee=party,
            shipper=party,
            destination=origin,
            railroad=rail_road,
        )
        prefix = "TEST1-TEST2-"
        separator = "-"
        counter_start = 100
        BolNumberFactory(
            company=wheelhouse_company,
            counter=counter_start,
            separator=separator,
            format=["TEST1", "separator", "TEST2", "separator"],
            auto_generate=False,
        )
        expected_bol_number = str(origin.company.id) + prefix + str(counter_start + 1)
        rail_shipment.update_bolnumber()
        rail_shipment.refresh_from_db()
        self.assertEqual(rail_shipment.reference_number, expected_bol_number)  # noqa: PT009
