from rest_framework import status
from ..base.base_api_test_case import BaseTestCase
from vantedge.wheelhouse.tests.factories.schedule import ShippingScheduleFactory, RailShippingScheduleFactory


class TestRailShippingSchedule(BaseTestCase):
    endpoint = 'railschedule/'

    def test_get__response__success(self):
        rail_shipping_schedule = RailShippingScheduleFactory()
        response = self.get(self.endpoint)

        self.assertEqual(status.HTTP_200_OK, response.status_code)
        self.assertIsNotNone(rail_shipping_schedule)
