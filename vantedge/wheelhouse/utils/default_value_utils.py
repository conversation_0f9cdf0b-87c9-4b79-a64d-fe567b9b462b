from django.contrib.contenttypes.models import ContentType
from django_q.tasks import schedule
from vantedge.wheelhouse.models.rail import RailShipment
from vantedge.wheelhouse.models.schedule import RailShippingSchedule


class DefaultValFunctions:
    """
    A utility class responsible for extracting and calculating specific field values for additional_fields.

    This class provides static methods to fetch predefined field values dynamically
    and supports field mapping via the `FIELD_FUNCTIONS` dictionary.

    See wheelhouse/models/default_value_generator.py DefaultValueGenerator
    """
    @staticmethod
    def shipping_schedule_po_number(company, instance):
        from vantedge.wheelhouse.models.additional_fields import AdditionalField
        if isinstance(instance, RailShippingSchedule):
            contract = instance.contract
            field = AdditionalField.objects.filter(
                    company=company,
                    code="po_number",
                    content_type=ContentType.objects.get_for_model(contract),
            ).first()
            if field:
                field_id = field.id
            contract_po_number = contract.additional_fields.fields.get(field_id)
            return contract_po_number if contract_po_number else ""
        return ""

    @staticmethod
    def shipment_po_number(company, instance):
        from vantedge.wheelhouse.models.additional_fields import AdditionalField
        if isinstance(instance, RailShipment):
            schedule = instance.schedule
            field = AdditionalField.objects.filter(
                    company=company,
                    code="po_number",
                    content_type=ContentType.objects.get_for_model(schedule),
            ).first()
            if field:
                field_id = field.id
            schedule_po_number = schedule.additional_fields.fields.get(field_id)
            return schedule_po_number if schedule_po_number else ""
        return ""

    @staticmethod
    def append_schedule_car_count(_company, instance, **kwargs):
        buyer_id = str(kwargs.get("buyer_id"))
        if not buyer_id.isdigit():
            return ""
        if isinstance(instance, RailShipment) and int(buyer_id) == instance.contract.buyer.id:
            padding = 3
            shipment_count = instance.schedule.rail_shipments.count()
            count_str_padded = str(shipment_count).zfill(padding)
            return count_str_padded
        return ""


    FIELD_FUNCTIONS = {
        "SCHEDULE_PO_NUMBER": lambda company, instance: DefaultValFunctions.shipping_schedule_po_number(
            company,
            instance
        ),
        "SHIPMENT_PO_NUMBER": lambda company, instance: DefaultValFunctions.shipment_po_number(
            company,
            instance
        ),
        "APPEND_SHIPMENT_COUNT": lambda company, instance, **kwargs: DefaultValFunctions.append_schedule_car_count(
                company,
                instance,
                **kwargs
        )
    }

    def calculate(self, company, instance, field_name, **kwargs):
        func = self.FIELD_FUNCTIONS.get(field_name)
        return_val = func(company, instance, **kwargs) if func else field_name
        return return_val

