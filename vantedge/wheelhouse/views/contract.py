from datetime import *

import arrow
import django_filters as filters
from dateutil.relativedelta import *
from django.db.models import <PERSON><PERSON><PERSON><PERSON>, F, ProtectedError, Q
from django.db.models.functions import Concat
from django.utils import timezone
from model_utils import Choices, choices
from rest_framework import permissions, status
from rest_framework.decorators import action
from rest_framework.pagination import LimitOffsetPagination
from rest_framework.response import Response
from rest_framework.viewsets import ModelViewSet
from vant_tboss.models import company

from vantedge.trackandtrace.models import DetentionData, RailCar, Stcc, Trip
from vantedge.trackandtrace.serializers import TripContractSerializer
from vantedge.wheelhouse.models.company import PartyPartnership, PartyTypes, WheelhouseCompany

from ..models.contract import Contract, Party
from ..models.location import Facility
from ..models.product import Product
from ..models.rail import RailShipment, Route
from ..models.schedule import RailShippingSchedule
from .company import CompanyFilterMixin
from .rail import RailShipmentSerializer
from .serializers.contract import (
    ContractDashboardSerializer,
    ContractListSerializer,
    ContractSerializer,
    ContractShortListSerializer,
    PartySerializer,
    RailShipmentListSerializer,
    RailShippingScheduleSerializer,
    RouteSerializer,
)


class ContractFilter(filters.FilterSet):
    expired = filters.BooleanFilter(method="expired_filter", label="Expired")
    PARTY = Choices("all", "purchases", "sales")
    party = filters.ChoiceFilter(method="party_filter", label="Party", choices=PARTY)
    dateRange = filters.CharFilter(method="date_range", label="DateRange")

    def expired_filter(self, qs, field_name, value):
        if value:
            return qs.filter(end_date__lt=timezone.now().today())
        else:
            return qs.filter(
                Q(end_date__gte=timezone.now().today()) | Q(end_date__isnull=True)
            )

    def party_filter(self, qs, field_name, value):
        if value == "purchases":
            qs = qs.exclude(buyer_number="")
        if value == "sales":
            qs = qs.exclude(seller_number="")
        return qs

    def date_range(self, qs, field_name, value):
        if value:
            start, end = value.split("_")
            qs = qs.filter(Q(start_date__lte=end) & Q(end_date__gte=start))
        return qs

    class Meta:
        model = Contract
        fields = ["expired", "party"]


class ContractViewSet(ModelViewSet):
    permission_classes = [permissions.IsAuthenticated]
    serializer_class = ContractSerializer
    queryset = Contract.objects.select_related("buyer", "seller", "product").all()
    filterset_class = ContractFilter

    def get_queryset(self):
        qs = super().get_queryset()
        company = getattr(self.request.user.company, "wheelhouse", None)
        if company is None:
            return qs.none()
        return qs.filter(Q(buyer__company=company) | Q(seller__company=company))

    def get_serializer_class(self):
        if self.action == "list":
            return ContractListSerializer
        return super().get_serializer_class()

    def perform_create(self, serializer):
        company = self.request.user.company.wheelhouse
        if not company.default_sale_party:
            from rest_framework.exceptions import ValidationError

            raise ValidationError("Cannot create sale without a default sale party.")
        product = None
        if self.request.data.get("stcc", False):
            stcc = Stcc.objects.get(stcc=self.request.data["stcc"])
            product = Product.objects.filter(
                Q(company=company)
                & (Q(default_stcc=stcc) | Q(stcc_aliases__contains=[stcc]))
            )
            if product:
                product = product[0]
            else:
                product = Product(
                    company=company,
                    default_stcc=stcc,
                    name=self.request.data["stcc_name"],
                )
                product.save()
        serializer.save(seller=company.default_sale_party, product=product)

    @action(detail=True, methods=["post"])
    def create_sale(self, request, pk=None):
        company = self.request.user.company
        wheelhouse = company.wheelhouse
        detention_data = DetentionData()
        if hasattr(company, "track_trace"):
            detention_data = company.track_trace.data.detention_data
        if not wheelhouse.default_sale_party:
            from rest_framework.exceptions import ValidationError

            raise ValidationError("Cannot create sale without a default sale party.")
        contract = Contract.objects.create(
            seller=wheelhouse.default_sale_party, detention_data=detention_data
        )
        serializer = self.get_serializer(contract)
        return Response(serializer.data)

    @action(detail=True, methods=["post"])
    def create_purchase(self, request, pk=None):
        company = self.request.user.company
        wheelhouse = company.wheelhouse
        detention_data = DetentionData()
        if hasattr(company, "track_trace"):
            detention_data = company.track_trace.data.detention_data

        if not wheelhouse.default_purchase_party:
            from rest_framework.exceptions import ValidationError

            raise ValidationError(
                "Cannot create purchase without a default sale party."
            )
        contract = Contract.objects.create(
            buyer=wheelhouse.default_purchase_party, detention_data=detention_data
        )
        serializer = self.get_serializer(contract)
        return Response(serializer.data)

    @action(detail=True, methods=["post"])
    def clone(self, request, pk=None):
        """
        Clone or roll-over contract, if possible.
        """
        roll_over = request.data.get("roll_over")
        period = request.data.get("period")
        contract = self.get_object()

        cloned = contract.clone(roll_over, period)

        serializer = ContractSerializer(cloned)
        return Response(serializer.data)

    @action(detail=False, methods=["post"])
    def clone_multiple(self, request):
        """
        Clone or roll-over multiple contracts, if possible.
        """
        roll_over = request.data.get("roll_over")
        period = request.data.get("period")
        contract_ids = request.data.get("contract_ids")

        contracts = self.get_queryset().filter(id__in=contract_ids)

        cloned_contracts = {}

        for contract in contracts:
            cloned = contract.clone(roll_over, period)
            cloned_contracts[cloned.pk] = cloned

        return Response(cloned_contracts)

    @action(detail=True, methods=["get"])
    def get_cars(self, request, pk=None):
        contract = self.get_object()
        data = Trip.objects.filter(
            Q(buyer_contract__in=contract.purchase_schedules.all())
            | Q(seller_contract__in=contract.sale_schedules.all())
        ).select_related(
            "car",
            "last_car_event",
            "last_car_event__event_code",
            "last_car_event__location",
            "stcc",
            "destination",
        )
        if request.query_params.get("dateRange", False):
            dateRange = request.query_params.get("dateRange")
            dateRangeFrom, dateRangeTo = dateRange.split("_")
            data = data.filter(
                departure_timestamp__gte=arrow.get(dateRangeFrom).isoformat(),
                departure_timestamp__lte=arrow.get(dateRangeTo).isoformat(),
            )
        return Response(TripContractSerializer(data, many=True).data)

    @action(detail=True, methods=["get"])
    def get_shipments(self, request, pk=None):
        contract = self.get_object()
        shipments = RailShipment.objects.filter(
            Q(schedule__in=contract.purchase_schedules.all())
            | Q(schedule__in=contract.sale_schedules.all())
        )
        return Response(RailShipmentSerializer(shipments, many=True).data)

    @action(detail=True, methods=["get"])
    def get_shipments_count(self, request, pk=None):
        contract = self.get_object()
        shipments = RailShipment.objects.filter(
            Q(schedule__in=contract.purchase_schedules.all())
            | Q(schedule__in=contract.sale_schedules.all())
        )
        return Response(shipments.count())

    @action(detail=False, methods=["GET"])
    def get_short_contract_list(self, request):
        contracts = self.get_queryset()
        contracts = ContractFilter(data=dict(expired=False), queryset=contracts)
        return Response(ContractShortListSerializer(contracts.qs.all(), many=True).data)

    @action(detail=False, methods=["GET"])
    def get_dashboard_contract_list(self, request):
        contracts = self.get_queryset().select_related("seller", "buyer").order_by("-id")
        return Response(ContractDashboardSerializer(contracts, many=True).data)


class RouteViewSet(ModelViewSet):
    serializer_class = RouteSerializer
    queryset = Route.objects.all()


class PartyFilter(filters.FilterSet):
    name__pattern_match = filters.CharFilter(
        field_name="name", lookup_expr="icontains"
    )
    partner = filters.CharFilter(method="partner_filter", label="partner")

    def partner_filter(self, qs, field_name, value):
        request = self.request

        if request and value:
            include_ids = PartyPartnership.objects.filter(owner_company_id=value, partner_company=request.user.company).values_list("party", flat=True)
            return self.Meta.model.objects.filter(id__in=include_ids)

        return qs

    class Meta:
        model = Party
        fields = []


class PartyViewSet(CompanyFilterMixin, ModelViewSet):
    serializer_class = PartySerializer
    filterset_class = PartyFilter
    pagination_class = LimitOffsetPagination
    queryset = (
        Party.objects.all()
        .select_related(
            'wheelhouse_company__company__applicationconfiguration',
            'company__company__applicationconfiguration',
        )
        .prefetch_related('location_set', 'certificates')
        .order_by("name")
    )
    company_module = "wheelhouse"

    def destroy(self, request, *args, **kwargs):
        instance = self.get_object()

        try:
            self.perform_destroy(instance)
        except ProtectedError as e:
            return Response([str(e.args[0])], status=403)

        return Response({"message": "Deleted successfully"}, status=200)

    @action(
        detail=False, methods=["get"], permission_classes=[permissions.IsAuthenticated]
    )
    def choices(self, request):
        wheelhouseCompanyChoices = (
            WheelhouseCompany.objects.exclude(company=self.request.user.company)
            .annotate(value=F("id"), label=F("company__name"))
            .values("value", "label")
        )

        partyTypeChoices = [
            {"value": key, "label": val}
            for key, val in dict(PartyTypes.choices).items()
        ]

        return Response(
            {
                "wheelhouseCompanies": wheelhouseCompanyChoices,
                "partyTypes": partyTypeChoices,
            }
        )

    @action(detail=False, methods=["get"])
    def count(self, request):
        qs = self.filter_queryset(self.get_queryset())
        return Response(qs.count())


class RailShippingScheduleFilter(filters.FilterSet):
    contract_id = filters.NumberFilter(method="contract_id_filter")
    active = filters.CharFilter(method="get_active_shipping_schedules")

    def contract_id_filter(self, queryset, name, value):
        return queryset.filter(Q(purchase_contract=value) | Q(sale_contract=value))

    def get_active_shipping_schedules(self, queryset, name, value):
        now = timezone.localdate()
        return queryset.filter(
            Q(sale_contract__start_date__lte=now, sale_contract__end_date__gte=now)
            | Q(
                purchase_contract__start_date__lte=now,
                purchase_contract__end_date__gte=now,
            )
        )


class RailShippingScheduleViewSet(ModelViewSet):
    serializer_class = RailShippingScheduleSerializer
    queryset = RailShippingSchedule.objects.all()
    filterset_class = RailShippingScheduleFilter

    def get_queryset(self):
        qs = super().get_queryset()
        company = getattr(self.request.user.company, "wheelhouse", None)
        if company is None:
            return qs.none()
        return qs.filter(
            Q(purchase_contract__buyer__company=company)
            | Q(sale_contract__seller__company=company)
            | Q(origin__company=company)
        )

    # def perform_create(self, serializer):
    #     company = self.request.user.company.wheelhouse
    #     origin = None
    #     destination = None
    #     if self.request.data.get("origin_name", False):
    #         facility = Facility.objects.filter(name=self.request.data["origin_name"])
    #         if facility:
    #             origin = facility[0]
    #         else:
    #             origin = Facility(
    #                 company=company, name=self.request.data["origin_name"]
    #             )
    #             origin.save()
    #     if self.request.data.get("destination_name", False):
    #         facility = Facility.objects.filter(
    #             name=self.request.data["destination_name"]
    #         )
    #         if facility:
    #             destination = facility[0]
    #         else:
    #             destination = Facility(
    #                 company=company, name=self.request.data["destination_name"]
    #             )
    #             destination.save()
    #     serializer.save(origin=origin, destination=destination)

    @action(detail=True, methods=["post"])
    def auto_schedule(self, request, pk=None):
        pattern = self.get_object()
        schedule_params = request.data
        schedule_info = {}

        try:
            schedule_info = pattern.auto_schedule(**schedule_params)
        except Exception as e:
            return Response([str(e)], status=status.HTTP_400_BAD_REQUEST)

        return Response(schedule_info)

    @action(detail=True, methods=["get"])
    def get_shipments(self, request, pk=None):
        pattern = self.get_object()
        shipments = pattern.rail_shipments.all()
        return Response(RailShipmentListSerializer(shipments, many=True).data)

    @action(detail=True, methods=["get"])
    def get_cars(self, request, pk=None):
        pattern = self.get_object()
        data = Trip.objects.filter(
            Q(buyer_contract=pattern) | Q(seller_contract=pattern)
        ).select_related(
            "car",
            "last_car_event",
            "last_car_event__event_code",
            "last_car_event__location",
            "stcc",
            "destination",
        )
        if request.query_params.get("dateRange", False):
            dateRange = request.query_params.get("dateRange")
            dateRangeFrom, dateRangeTo = dateRange.split("_")
            data = data.filter(
                departure_timestamp__gte=arrow.get(dateRangeFrom).isoformat(),
                departure_timestamp__lte=arrow.get(dateRangeTo).isoformat(),
            )
        return Response(TripContractSerializer(data, many=True).data)

    @action(detail=True, methods=["post"])
    def bulk_add_cars(self, request, pk=None):
        pattern = self.get_object()
        cars = request.data["cars"]
        buyerSeller = request.data["buyer_seller"]
        Trip.objects.annotate(
            car_name=Concat("car__mark", "car__number", output_field=CharField())
        ).filter(
            car__company=request.user.company.track_trace, car_name__in=cars
        ).update(
            **{f"{buyerSeller}_contract": pattern}
        )

        return Response()

    @action(detail=True, methods=["post"])
    def create_shipment(self, request, pk):
        pattern = self.get_object()
        shipment = RailShipment.populate_shipment_from_schedule(pattern.id)
        print(shipment.id, shipment)
        return Response(dict(id=shipment.id))

    @action(detail=True, methods=["post"])
    def clone(self, request, pk=None):
        schedule = self.get_object()
        now = timezone.now()

        schedule.pk = None
        schedule.schedule = []
        schedule.auto_schedule_batch = None
        schedule.auto_schedule_periodic_days = None
        schedule.auto_schedule_shipment_count = None
        schedule.created = now
        schedule.modified = now

        schedule.save()
        schedule = RailShippingScheduleSerializer(schedule)
        return Response(schedule.data)
