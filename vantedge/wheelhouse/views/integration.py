from django.http import Http404
from django.utils import timezone

from rest_framework import mixins
from rest_framework import status
from rest_framework import viewsets
from rest_framework import versioning
from rest_framework import permissions

from rest_framework.response import Response
from rest_framework.pagination import LimitOffsetPagination

from vantedge.wheelhouse.models.bol import BolNumber


from ..models import RailShipment, RailShipmentNote

from .serializers.integration import RailShipmentSerializer


class ShipmentAPIPermission(permissions.BasePermission):
    def has_permission(self, request, view):
        if request.user.has_perm(
            "wheelhouse.ship_railshipment"
        ) or request.user.has_perm("wheelhouse.add_railshipment"):
            return True


class RailShipmentIntegrationViewSet(
    mixins.RetrieveModelMixin,
    mixins.CreateModelMixin,
    mixins.DestroyModelMixin,
    mixins.UpdateModelMixin,
    viewsets.GenericViewSet,
):
    serializer_class = RailShipmentSerializer
    pagination_class = LimitOffsetPagination
    versioning_class = versioning.QueryParameterVersioning
    permission_classes = [permissions.IsAuthenticated, ShipmentAPIPermission]
    queryset = RailShipment.objects.all()

    def get_queryset(self):
        qs = super().get_queryset()
        return qs.filter(origin__company__company=self.request.user.company)

    def get_object(self):
        pk = self.kwargs["pk"]
        pk_type = self.request.query_params.get("pk_type", "id")

        if pk_type == "id":
            try:
                instance = self.get_queryset().get(id=pk)
            except RailShipment.DoesNotExist:
                raise Http404("Object Not found")
            except ValueError:
                raise Http404("Object Not found")

        elif pk_type == "reference_number":
            try:
                instance = self.get_queryset().get(reference_number=pk)
            except RailShipment.DoesNotExist:
                raise Http404("Object not found")
            except RailShipment.MultipleObjectsReturned:
                raise Http404("Multiple objects found")

        elif pk_type == "id_geo":
            try:
                instance = self.get_queryset().get(data__id_geo=int(pk))
            except RailShipment.DoesNotExist:
                raise Http404("Object not found")
            except RailShipment.MultipleObjectsReturned:
                raise Http404("Multiple objects found")

        return instance

    def replace_seal_numbers(self, car_data):
        if car_data.get("seal_number_1"):
            pass
        elif car_data.get("seal_number", ""):
            for i, value in enumerate(car_data.get("seal_number").split(",")):
                if i + 1 > 4:
                    break
                car_data[f"seal_number_{i+1}"] = value.replace(" ", "")

    def create(self, request, *args, **kwargs):
        cars_data = request.data.get("cars", [])
        for car_data in cars_data:
            self.replace_seal_numbers(car_data)

        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        self.perform_create(serializer)
        headers = self.get_success_headers(serializer.data)
        return Response(
            serializer.data, status=status.HTTP_201_CREATED, headers=headers
        )

    def update(self, request, *args, **kwargs):
        cars_data = request.data.get("cars", [])
        for car_data in cars_data:
            self.replace_seal_numbers(car_data)

        partial = kwargs.pop("partial", False)
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=partial)
        serializer.is_valid(raise_exception=True)
        try:
            self.perform_update(serializer)
        except:
            return Response(
                "shipment is in readonly mode", status=status.HTTP_400_BAD_REQUEST
            )

        if getattr(instance, "_prefetched_objects_cache", None):
            # If 'prefetch_related' has been applied to a queryset, we need to
            # forcibly invalidate the prefetch cache on the instance.
            instance._prefetched_objects_cache = {}

        return Response(serializer.data)

    def destroy(self, request, *args, **kwargs):
        instance = self.get_object()

        if instance.status not in ["open", "closed", "voided"]:
            return Response(
                ["Shipment is write protect"], status=status.HTTP_406_NOT_ACCEPTABLE
            )

        for car in instance.cars.all():
            if car.invoice_items.all():
                return Response(
                    ["Shipment has invoiced car"], status=status.HTTP_406_NOT_ACCEPTABLE
                )

        old_reference_number = instance.reference_number
        try:
            instance.cars.all().delete()
            instance.reference_number = BolNumber.generate_reference_number()
            instance.status = "voided"
            instance.save(update_fields=["status", "reference_number"])
        except Exception as e:
            return Response(
                "Shipment is in readonly mode", status=status.HTTP_400_BAD_REQUEST
            )

        RailShipmentNote(
            rail_shipment=instance,
            note=f"Shipment voided by API; by '{request.user.username}' in {timezone.localtime()}, old reference_number: {old_reference_number}",
            is_write_protect=True,
        ).save()

        return Response(status=status.HTTP_204_NO_CONTENT)

