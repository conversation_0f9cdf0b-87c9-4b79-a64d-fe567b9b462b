from django.contrib.contenttypes.models import ContentType
from django.core.exceptions import ObjectDoesNotExist
from django.db.models import Value
from django.db.models.functions import Concat
from django.db.models import F, Q
from django.utils import timezone
from rest_framework.viewsets import ViewSet

from vant_dbreplication.models import DBReplicationServer
from vantedge.tboss.serializers import DBReplicationServerSerializer
from vant_calcs.uom import (
    VolumeChoices,
    PriceChoices,
    DensityChoices,
    QUANTITY_CHOICES,
    QUANTITY_PERCENT_CHOICES,
)
from vant_tboss.choices import (
    CountryChoices,
    ProvinceChoices,
    UsaStateChoices,
    NORTH_AMERICA_PROVINCE_CHOICES,
)
from vantedge.reports.units import UOM_LABELS_BY_QUANTITY
from vantedge.reports.format import PRECISION_OPTIONS, DATE_OPTIONS, DATETIME_OPTIONS
from vant_ledger.models import Transaction
from vant_tboss.models import DefaultPaymentTerms, DefaultTax
from vantedge.trackandtrace.models.other import RailCar
from vantedge.schedule.models import ScheduleCompany
from ..models.company import Company, Party, PartyTypes
from ..models.product import Product, ProductType
from ..models.contract import Contract
from ..models.schedule import RailShippingSchedule
from ..models.location import Facility
from ..models.location import Location
from ..models.rail import RailShipment
from ..models.railroad import Railroad
from ..models.certificate import CertificateScope
from ..models.schema import ContactRoleEnum
from .serializers.utility import ModelPickerSerializer, ModelFieldSerializer, ModelPartyPickerSerializer
from vantedge.reports.models import Report
from vantedge.users.models import User
from vantedge.trackandtrace.models import Stcc
from ..views.serializers.product import ProductTypeSerializer, ProductPickerSerializer
from vantedge.tboss.views.servers import (
    get_replication_servers,
    get_replication_servers_from_model,
)
from rest_framework.response import Response
from rest_framework.decorators import action
import pytz


from custom_apps.green_impact_partners.schemas import GIPTicketTypes


def get_parties():
    return Party.objects.all()


def get_facilities():
    return Facility.objects.all()


class PickerViewSet(ViewSet):
    @action(detail=False, methods=["get"])
    def company(self, request):
        serializer = ModelPickerSerializer(Company)(Company.objects.all(), many=True)
        return Response(serializer.data)

    @action(detail=False, methods=["get"])
    def buyer(self, request):
        serializer = ModelPickerSerializer(Party)(
            get_parties().filter(
                company__company=self.request.user.company,
            ),
            many=True,
        )
        return Response(serializer.data)

    @action(detail=False, methods=["get"])
    def seller(self, request):
        serializer = ModelPickerSerializer(Party)(
            get_parties().filter(
                company__company=self.request.user.company,
            ),
            many=True,
        )
        return Response(serializer.data)

    @action(detail=False, methods=["get"])
    def shipper(self, request):
        party_type = [PartyTypes.Shipper.value]
        serializer = ModelPartyPickerSerializer(Party)(
            get_parties().filter(
                company__company=self.request.user.company,
                party_types__contains=party_type,
            ),
            many=True,
        )
        return Response(serializer.data)

    @action(detail=False, methods=["get"])
    def consignee(self, request):
        party_type = [PartyTypes.Consignee.value]
        serializer = ModelPartyPickerSerializer(Party)(
            get_parties().filter(
                company__company=self.request.user.company,
                party_types__contains=party_type,
            ),
            many=True,
        )
        return Response(serializer.data)

    @action(detail=False, methods=["get"])
    def freight_payer(self, request):
        party_type = [PartyTypes.FreightPayer.value]
        serializer = ModelPartyPickerSerializer(Party)(
            get_parties().filter(
                company__company=self.request.user.company,
                party_types__contains=party_type,
            ),
            many=True,
        )
        return Response(serializer.data)

    @action(detail=False, methods=["get"])
    def customs(self, request):
        party_type = [PartyTypes.CustomsBroker.value]
        serializer = ModelPartyPickerSerializer(Party)(
            get_parties().filter(
                company__company=self.request.user.company,
                party_types__contains=party_type,
            ),
            many=True,
        )
        return Response(serializer.data)

    @action(detail=False, methods=["get"])
    def notify_party(self, request):
        party_type = [PartyTypes.NotifyParty.value]
        serializer = ModelPartyPickerSerializer(Party)(
            get_parties().filter(
                company__company=self.request.user.company,
                party_types__contains=party_type,
            ),
            many=True,
        )
        return Response(serializer.data)

    @action(detail=False, methods=["get"])
    def ship_from(self, request):
        party_type = [PartyTypes.ShipFrom.value]
        serializer = ModelPartyPickerSerializer(Party)(
            get_parties().filter(
                company__company=self.request.user.company,
                party_types__contains=party_type,
            ),
            many=True,
        )
        return Response(serializer.data)

    @action(detail=False, methods=["get"])
    def beneficial_owner(self, request):
        party_type = [PartyTypes.BeneficialOwner.value]
        serializer = ModelPartyPickerSerializer(Party)(
            get_parties().filter(
                company__company=self.request.user.company,
                party_types__contains=party_type,
            ),
            many=True,
        )
        return Response(serializer.data)

    @action(detail=False, methods=["get"])
    def care_party(self, request):
        party_type = [PartyTypes.CareParty.value]
        serializer = ModelPartyPickerSerializer(Party)(
            get_parties().filter(
                company__company=self.request.user.company,
                party_types__contains=party_type,
            ),
            many=True,
        )
        return Response(serializer.data)

    @action(detail=False, methods=["get"])
    def pickup_party(self, request):
        party_type = [PartyTypes.PickupParty.value]
        serializer = ModelPartyPickerSerializer(Party)(
            get_parties().filter(
                company__company=self.request.user.company,
                party_types__contains=party_type,
            ),
            many=True,
        )
        return Response(serializer.data)

    @action(detail=False, methods=["get"])
    def monitoring_party(self, request):
        party_type = [PartyTypes.Monitoring.value]
        serializer = ModelPartyPickerSerializer(Party)(
            get_parties().filter(
                company__company=self.request.user.company,
                party_types__contains=party_type,
            ),
            many=True,
        )
        return Response(serializer.data)

    @action(detail=False, methods=["get"])
    def forwarder(self, request):
        party_type = [PartyTypes.Forwarder.value]
        serializer = ModelPartyPickerSerializer(Party)(
            get_parties().filter(
                company__company=self.request.user.company,
                party_types__contains=party_type,
            ),
            many=True,
        )
        return Response(serializer.data)

    @action(detail=False, methods=["get"])
    def importer_exporter(self, request):
        party_type = [PartyTypes.ImporterExporter.value]
        serializer = ModelPartyPickerSerializer(Party)(
            get_parties().filter(
                company__company=self.request.user.company,
                party_types__contains=party_type,
            ),
            many=True,
        )
        return Response(serializer.data)

    @action(detail=False, methods=["get"])
    def sale_contracts(self, request):
        sale_contract_qs = (
            Contract.objects.filter(
                ~Q(seller_number="")
                & Q(seller__company=self.request.user.company.wheelhouse)
            )
            .exclude(seller=self.request.user.company.wheelhouse.default_purchase_party)
            .annotate(
                value=F("id"),
                label=Concat(
                    F("seller__name"), Value(" ("), F("seller_number"), Value(")")
                ),
            )
            .values("value", "label")
        )
        return Response(sale_contract_qs)

    @action(detail=False, methods=["get"])
    def purchase_contracts(self, request):
        purchase_contract_qs = (
            Contract.objects.filter(
                ~Q(buyer_number="")
                & Q(buyer__company=self.request.user.company.wheelhouse)
            )
            .exclude(buyer=self.request.user.company.wheelhouse.default_sale_party)
            .annotate(
                value=F("id"),
                label=Concat(
                    F("buyer__name"), Value(" ("), F("buyer_number"), Value(")")
                ),
            )
            .values("value", "label")
        )
        return Response(purchase_contract_qs)

    @action(detail=False, methods=["get"])
    def producer(self, request):
        party_type = [PartyTypes.Producer.value]
        serializer = ModelPickerSerializer(Party)(
            get_parties().filter(
                company__company=self.request.user.company,
                party_types__contains=party_type,
            ),
            many=True,
        )
        return Response(serializer.data)

    @action(detail=False, methods=["get"])
    def rail_shipping_schedule_buyer_number(self, request):
        schedules = (
            RailShippingSchedule.objects.filter(
                Q(
                    purchase_contract__isnull=False,
                    purchase_contract__end_date__gte=timezone.now().today(),
                )
                & (
                    Q(
                        purchase_contract__buyer__company=self.request.user.company.wheelhouse
                    )
                    | Q(
                        purchase_contract__seller__company=self.request.user.company.wheelhouse
                    )
                )
            )
            .annotate(buyer_number=F("purchase_contract__buyer_number"))
            .values("id", "name", "purchase_contract", "buyer_number")
        )
        return Response(schedules)

    @action(detail=False, methods=["get"])
    def rail_shipping_schedule_seller_number(self, request):
        schedules = (
            RailShippingSchedule.objects.filter(
                Q(
                    sale_contract__isnull=False,
                    sale_contract__end_date__gte=timezone.now().today(),
                )
                & (
                    Q(
                        sale_contract__buyer__company=self.request.user.company.wheelhouse
                    )
                    | Q(
                        sale_contract__seller__company=self.request.user.company.wheelhouse
                    )
                )
            )
            .annotate(seller_number=F("sale_contract__seller_number"))
            .values("id", "name", "sale_contract", "seller_number")
        )
        return Response(schedules)

    @action(detail=False, methods=["get"])
    def product(self, request):
        serializer = ModelPickerSerializer(Product)(
            Product.objects.filter(company=self.request.user.company.wheelhouse),
            many=True,
        )
        return Response(serializer.data)

    @action(detail=False, methods=["get"])
    def product_spec(self, request):
        serializer = ProductPickerSerializer(
            Product.objects.filter(company=self.request.user.company.wheelhouse),
            many=True,
        )
        return Response(serializer.data)

    @action(detail=False, methods=["get"])
    def stcc(self, request):
        serializer = ModelFieldSerializer(Stcc, ["stcc", "name"])(
            Stcc.objects.all(), many=True
        )
        return Response(serializer.data)

    @action(detail=False, methods=["get"])
    def origin(self, request):
        serializer = ModelPickerSerializer(Facility)(
            get_facilities().filter(
                company__company=self.request.user.company,
            ),
            many=True,
        )
        return Response(serializer.data)

    @action(detail=False, methods=["get"])
    def destination(self, request):
        serializer = ModelPickerSerializer(Facility)(
            get_facilities().filter(
                company__company=self.request.user.company,
            ),
            many=True,
        )
        return Response(serializer.data)

    @action(detail=False, methods=["get"])
    def unit(self, request):
        return Response(map(lambda l: {"value": l[0], "label": l[1]}, Contract.UNIT))

    @action(detail=False, methods=["get"])
    def railroad(self, requset):
        serializer = ModelPickerSerializer(Railroad)(Railroad.objects.all(), many=True)
        return Response(serializer.data)

    @action(detail=False, methods=["get"])
    def rail_freight_charge_type(self, request):
        return Response(
            map(
                lambda l: {"value": l[0], "label": l[1]},
                RailShipment.FREIGHT_CHARGE_TYPE,
            )
        )

    @action(detail=False, methods=["get"])
    def volume_type(self, request):
        return Response(
            map(lambda l: {"value": l[0], "label": l[1]}, Contract.VOLUME_TYPE)
        )

    @action(detail=False, methods=["get"])
    def fob(self, request):
        return Response(map(lambda l: {"value": l[0], "label": l[1]}, Contract.FOB))

    @action(detail=False, methods=["get"])
    def pricing_type(self, request):
        return Response(
            map(lambda l: {"value": l[0], "label": l[1]}, Contract.PRICING_TYPES)
        )

    @action(detail=False, methods=["get"])
    def product_types(self, request):
        serializer = ModelPickerSerializer(ProductType)(
            ProductType.objects.all(), many=True
        )
        return Response(serializer.data)

    @action(detail=False, methods=["get"])
    def fleet(self, request):
        serializer = ModelFieldSerializer(RailCar, ["fleet", "fleet"])(
            RailCar.objects.filter(company__company=request.user.company)
            .exclude(fleet="")
            .exclude(fleet__isnull=True)
            .values("fleet")
            .distinct(),
            many=True,
        )
        return Response(serializer.data)

    @action(detail=False, methods=["get"])
    def product_type_configuration(self, request):
        serializer = ProductTypeSerializer(ProductType.objects.all(), many=True)
        return Response(serializer.data)

    @action(detail=False, methods=["get"])
    def report_emails(self, request):
        reports = Report.objects.filter(company=request.user.company.reporting)
        emails = []
        for report in reports:
            emails += report.distribution
        return Response(list(set(emails)))

    @action(detail=False, methods=["get"])
    def certificate_scopes(self, request):
        choices = get_picker_from_text_choice(CertificateScope)
        return Response(choices)

    @action(detail=False, methods=["get"])
    def certificate_content_type(self, request):
        choices = []
        for model in dict(CertificateScope.choices):
            try:
                model_id = ContentType.objects.get(
                    app_label="wheelhouse", model=model
                ).id
            except Exception:
                model_id = ContentType.objects.get(app_label="users", model=model).id
            choices.append({"name": model, "id": model_id})
        return Response(choices)

    @action(detail=False, methods=["get"])
    def locations(self, request):
        serializer = ModelPickerSerializer(Location)(
            Location.company_locations(self.request.user.company.wheelhouse), many=True
        )
        return Response(serializer.data)

    @action(detail=False, methods=["get"])
    def contact_roles(self, request):
        choices = []
        for item in ContactRoleEnum:
            choices.append({"value": item.value, "label": item.value})
        return Response(choices)

    @action(detail=False, methods=["get"])
    def user_roles(self, request):
        user = request.user
        choices = []

        app_config = getattr(user.company, "applicationconfiguration", None)
        available_roles = app_config.available_roles if app_config else []

        choices = [
            {
                "value": User.Role[role.upper()].value,
                "label": User.Role[role.upper()].label,
            }
            for role in available_roles
            if getattr(User.Role, role.upper(), None)
        ]

        return Response(choices)

    @action(detail=False, methods=["get"])
    def schedule_companies(self, request):
        choices = []
        companies = (
            ScheduleCompany.user_accessible_companies(request.user)
            .values("company")
            .annotate(name=F("company__name"), id=F("company__wheelhouse__id"))
            .values("name", "id")
        )
        for item in companies:
            choices.append({"value": item["id"], "label": item["name"]})
        return Response(choices)

    @action(detail=False, methods=["get"])
    def db_replication_servers(self, request):
        user = request.user
        model = request.query_params.get("model")

        if model:
            server_id = request.query_params.get("server")
            servers = [server_id] if server_id else None

            try:
                servers = get_replication_servers_from_model(user, model, servers)
                serializer = DBReplicationServerSerializer(
                    servers, many=True, **{"context": {"request": request}}
                )
            except ObjectDoesNotExist:
                serializer = DBReplicationServerSerializer(
                    DBReplicationServer.objects.none(),
                    many=True,
                    **{"context": {"request": request}},
                )

            return Response(serializer.data)
        else:
            try:
                servers = get_replication_servers(user)
                serializer = DBReplicationServerSerializer(
                    servers, many=True, **{"context": {"request": request}}
                )
            except:
                serializer = DBReplicationServerSerializer(
                    DBReplicationServer.objects.none(),
                    many=True,
                    **{"context": {"request": request}},
                )

            return Response(serializer.data)

    @action(detail=False, methods=["get"])
    def tboss_v3_quantity_choices(self, request):
        choices = get_picker_from_list_choices(QUANTITY_CHOICES)
        return Response(choices)

    @action(detail=False, methods=["get"])
    def tboss_v3_quantity_percent_choices(self, request):
        choices = get_picker_from_list_choices(QUANTITY_PERCENT_CHOICES)
        return Response(choices)

    @action(detail=False, methods=["get"])
    def tboss_v3_transaction_status(self, request):
        choices = get_picker_from_text_choice(Transaction.TransactionStatus)
        return Response(choices)

    @action(detail=False, methods=["get"])
    def tboss_v3_density_choices(self, request):
        choices = get_picker_from_text_choice(DensityChoices)
        return Response(choices)

    @action(detail=False, methods=["get"])
    def tboss_v3_volume_choices(self, request):
        choices = get_picker_from_text_choice(VolumeChoices)
        return Response(choices)

    @action(detail=False, methods=["get"])
    def tboss_v3_price_choices(self, request):
        choices = get_picker_from_text_choice(PriceChoices)
        return Response(choices)

    @action(detail=False, methods=["get"])
    def tboss_country_choices(self, request):
        choices = get_picker_from_text_choice(CountryChoices)
        return Response(choices)

    @action(detail=False, methods=["get"])
    def tboss_cad_province_choices(self, request):
        choices = get_picker_from_text_choice(ProvinceChoices)
        return Response(choices)

    @action(detail=False, methods=["get"])
    def tboss_usa_state_choices(self, request):
        choices = get_picker_from_text_choice(UsaStateChoices)
        return Response(choices)

    @action(detail=False, methods=["get"])
    def tboss_north_american_province_choices(self, request):
        choices = get_picker_from_list_choices(NORTH_AMERICA_PROVINCE_CHOICES)
        return Response(choices)

    @action(detail=False, methods=["get"])
    def green_impact_partner_ticket_types(self, request):
        choices = []
        for item in GIPTicketTypes:
            choices.append({"value": item.value, "label": item.value})
        return Response(choices)

    @action(detail=False, methods=["get"])
    def tboss_company_default_taxes(self, request):
        choices = [(c, c) for c in DefaultTax]
        return Response(get_picker_from_list_choices(choices))

    @action(detail=False, methods=["get"])
    def tboss_company_default_payment_terms(self, request):
        choices = [(c, c) for c in DefaultPaymentTerms]
        return Response(get_picker_from_list_choices(choices))

    @action(detail=False, methods=["get"])
    def available_timezones(self, request):
        # zoneinfo.available_timezones
        north_america_timezones = sorted(
            pytz.country_timezones("CA") + pytz.country_timezones("US")
        )
        choices = [(t, t) for t in north_america_timezones]
        return Response(get_picker_from_list_choices(choices))

    @action(detail=False, methods=["get"])
    def report_quantity_choices(self, request):
        choicesDict = {}

        for measure in UOM_LABELS_BY_QUANTITY.keys():
            choices = get_picker_from_dict_choices(UOM_LABELS_BY_QUANTITY[measure])
            choicesDict[measure] = choices

        return Response(choicesDict)

    @action(detail=False, methods=["get"])
    def report_date_choices(self, request):
        choices = get_picker_from_dict_choices(DATE_OPTIONS)
        return Response(choices)

    @action(detail=False, methods=["get"])
    def report_datetime_choices(self, request):
        choices = get_picker_from_dict_choices(DATETIME_OPTIONS)
        return Response(choices)

    @action(detail=False, methods=["get"])
    def report_precision_choices(self, request):
        choices = get_picker_from_dict_choices(PRECISION_OPTIONS)
        return Response(choices)

    @action(detail=False, methods=["get"])
    def slot_templates(self, request):
        from vantedge.schedule.models import SlotTemplate
        serializer = ModelPickerSerializer(SlotTemplate)(
            SlotTemplate.objects.filter(
                company__company=self.request.user.company
            ),
            many=True,
        )
        return Response(serializer.data)

def get_picker_from_text_choice(text_choice):
    return [
        {"value": value, "label": label}
        for value, label in dict(text_choice.choices).items()
    ]


def get_picker_from_list_choices(list_choices):
    return [{"value": value, "label": label} for (value, label) in list_choices]


def get_picker_from_dict_choices(dict_choices):
    return [{"value": value, "label": label} for (value, label) in dict_choices.items()]
