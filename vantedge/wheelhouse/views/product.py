from rest_framework.decorators import action
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.viewsets import ModelViewSet

from vantedge.util.mixins import BaseViewsetThrottleMixin

from .company import CompanyFilterMixin
from ..models.product import Product
from .serializers.product import ProductSerializer, ProductColorSerializer

class ProductViewSet(BaseViewsetThrottleMixin, CompanyFilterMixin, ModelViewSet):
    serializer_class = ProductSerializer
    queryset = Product.objects.all().order_by("name")

    @action(detail=False, methods=["get"], permission_classes=[IsAuthenticated])
    def choices(self, request):
        qs = self.get_queryset().values("id", "name", "product_type")
        return Response(qs)

    @action(detail=False, methods=["get"], permission_classes=[IsAuthenticated])
    def product_colors(self, _request):
        serializer = ProductColorSerializer(self.get_queryset(), many=True)
        return Response(serializer.data)
