from django.db import transaction
from django.shortcuts import get_object_or_404
from django.views.generic import DetailView
from django.db.models import Count, Q, F
from django.db.models.functions import Concat
from django.utils import timezone

from django_weasyprint import WeasyTemplateResponseMixin
from django_filters import FilterSet, BaseInFilter, Char<PERSON>ilter
from django.conf import settings
from django.http.response import HttpResponse

from vantedge.wheelhouse.models.company import Party
from vantedge.wheelhouse.models.location import Facility
from vantedge.wheelhouse.models.product import Product
from vantedge.printables.views import PrintableViewset
from vantedge.autocomplete.views import AutoCompleteViewSetMixin
from vantedge.wheelhouse.models.railroad import Railroad
from vantedge.wheelhouse.models.schedule import RailShippingSchedule

from rest_framework.decorators import action
from rest_framework.pagination import LimitOffsetPagination
from rest_framework.permissions import IsAuthenticated
from rest_framework.exceptions import ValidationError
from rest_framework.response import Response
from rest_framework.viewsets import ModelViewSet
from rest_framework import status

from ..models.rail import RailShipment, RailShipmentNote, BOLArchive
from ..models.railcar import CAR_TYPE_MODEL
from ..models.bol import BolNumber

from .serializers.rail import (
    RailShipmentListSerializerShortened,
    RailShipmentSerializer,
    RailShipmentNoteSerializer,
)
from .serializers.railcar import LoadedRailCarSerializer
from .utils.pdf import get_bols_and_zip
from util.mixins import SearchFilterMixin


class RailShipmentFilter(SearchFilterMixin, FilterSet):
    # Search
    search_query = CharFilter(method="search_filter")

    # Parties
    facility = CharFilter(method="facility_filter")

    consignee__name = CharFilter(method="escape_comma_filter")
    consignee__name__in = CharFilter(method="escape_comma_filter")
    consignee__name__exclude = CharFilter(method="escape_comma_exclude")

    beneficial_owner__name = CharFilter(method="escape_comma_filter")
    beneficial_owner__name__in = CharFilter(method="escape_comma_filter")
    beneficial_owner__name__exclude = CharFilter(method="escape_comma_exclude")

    account_of__name = CharFilter(method="escape_comma_filter")
    account_of__name__in = CharFilter(method="escape_comma_filter")
    account_of__name__exclude = CharFilter(method="escape_comma_exclude")

    account_of_origin__name = CharFilter(method="escape_comma_filter")
    account_of_origin__name__in = CharFilter(method="escape_comma_filter")
    account_of_origin__name__exclude = CharFilter(method="escape_comma_exclude")

    account_of_destination__name = CharFilter(method="escape_comma_filter")
    account_of_destination__name__in = CharFilter(method="escape_comma_filter")
    account_of_destination__name__exclude = CharFilter(method="escape_comma_exclude")

    shipper__name = CharFilter(method="escape_comma_filter")
    shipper__name__in = CharFilter(method="escape_comma_filter")
    shipper__name__exclude = CharFilter(method="escape_comma_exclude")

    ship_from__name = CharFilter(method="escape_comma_filter")
    ship_from__name__in = CharFilter(method="escape_comma_filter")
    ship_from__name__exclude = CharFilter(method="escape_comma_exclude")

    forwarder__name = CharFilter(method="escape_comma_filter")
    forwarder__name__in = CharFilter(method="escape_comma_filter")
    forwarder__name__exclude = CharFilter(method="escape_comma_exclude")

    accounting_rule_11__name = CharFilter(method="escape_comma_filter")
    accounting_rule_11__name__in = CharFilter(method="escape_comma_filter")
    accounting_rule_11__name__exclude = CharFilter(method="escape_comma_exclude")

    freight_payer__name = CharFilter(method="escape_comma_filter")
    freight_payer__name__in = CharFilter(method="escape_comma_filter")
    freight_payer__name__exclude = CharFilter(method="escape_comma_exclude")

    custom_broker__name = CharFilter(method="escape_comma_filter")
    custom_broker__name__in = CharFilter(method="escape_comma_filter")
    custom_broker__name__exclude = CharFilter(method="escape_comma_exclude")

    notify_party__name = CharFilter(method="escape_comma_filter")
    notify_party__name__in = CharFilter(method="escape_comma_filter")
    notify_party__name__exclude = CharFilter(method="escape_comma_exclude")

    care_party__name = CharFilter(method="escape_comma_filter")
    care_party__name__in = CharFilter(method="escape_comma_filter")
    care_party__name__exclude = CharFilter(method="escape_comma_exclude")

    monitoring_party__name = CharFilter(method="escape_comma_filter")
    monitoring_party__name__in = CharFilter(method="escape_comma_filter")
    monitoring_party__name__exclude = CharFilter(method="escape_comma_exclude")

    pickup_party__name = CharFilter(method="escape_comma_filter")
    pickup_party__name__in = CharFilter(method="escape_comma_filter")
    pickup_party__name__exclude = CharFilter(method="escape_comma_exclude")

    ultimate_consignee__name = CharFilter(method="escape_comma_filter")
    ultimate_consignee__name__in = CharFilter(method="escape_comma_filter")
    ultimate_consignee__name__exclude = CharFilter(method="escape_comma_exclude")

    custom_broker_us__name = CharFilter(method="escape_comma_filter")
    custom_broker_us__name__in = CharFilter(method="escape_comma_filter")
    custom_broker_us__name__exclude = CharFilter(method="escape_comma_exclude")

    custom_broker_mx__name = CharFilter(method="escape_comma_filter")
    custom_broker_mx__name__in = CharFilter(method="escape_comma_filter")
    custom_broker_mx__name__exclude = CharFilter(method="escape_comma_exclude")

    importer__name = CharFilter(method="escape_comma_filter")
    importer__name__in = CharFilter(method="escape_comma_filter")
    importer__name__exclude = CharFilter(method="escape_comma_exclude")

    exporter__name = CharFilter(method="escape_comma_filter")
    exporter__name__in = CharFilter(method="escape_comma_filter")
    exporter__name__exclude = CharFilter(method="escape_comma_exclude")

    # Origin & Destination as Facilities
    origin__name__exclude = BaseInFilter(field_name="origin__name", exclude=True)
    destination__name__exclude = BaseInFilter(
        field_name="destination__name", exclude=True
    )

    # Dates
    ship_date = CharFilter(method="date_range_filter")
    ship_date__in = CharFilter(method="date_range_filter__in")
    ship_date__exclude = CharFilter(method="date_range_filter__exclude")

    arrive_date = CharFilter(method="date_range_filter")
    arrive_date__in = CharFilter(method="date_range_filter__in")
    arrive_date__exclude = CharFilter(method="date_range_filter__exclude")

    # Others
    status__exclude = BaseInFilter(field_name="status", exclude=True)
    product__name__exclude = BaseInFilter(field_name="product__name", exclude=True)
    railroad__name__exclude = BaseInFilter(field_name="railroad__name", exclude=True)
    reference_number__exclude = BaseInFilter(
        field_name="reference_number", exclude=True
    )

    def facility_filter(self, qs, field_name, value):
        if value:
            return qs.filter(origin__id=value)

    def escape_comma_filter(self, qs, field_name, value):
        query = Q()

        field_name = field_name.split("__in")[0]
        values = value.split(",")

        for value in values:
            query |= Q(**{f"{field_name}": value.replace("%2C", ",")})
        return qs.filter(query)

    def escape_comma_exclude(self, qs, field_name, value):
        query = Q()

        field_name = field_name.split("__exclude")[0]
        values = value.split(",")

        for value in values:
            query |= Q(**{f"{field_name}": value.replace("%2C", ",")})
        return qs.exclude(query)

    def date_range_filter(self, qs, field_name, value):
        query = Q()
        # singe date range
        date_range = value
        start, end = date_range.split("_")
        query |= Q(**{f"{field_name}__range": (start, end)})
        return qs.filter(query)

    def date_range_filter__in(self, qs, field_name, value):
        query = Q()
        # fix field_name
        field_name = field_name.split("__in")[0]

        # break the ranges down
        date_ranges = value.split(",")
        for date_range in date_ranges:
            start, end = date_range.split("_")
            # build query
            query |= Q(**{f"{field_name}__range": (start, end)})
        return qs.filter(query)

    def date_range_filter__exclude(self, qs, field_name, value):
        query = Q()
        # fix field_name
        field_name = field_name.split("__exclude")[0]

        # break the ranges down
        date_ranges = value.split(",")
        for date_range in date_ranges:
            start, end = date_range.split("_")
            # build query
            query |= Q(**{f"{field_name}__range": (start, end)})
        return qs.exclude(query)

    class Meta:
        model = RailShipment
        fields = {
            "status": ["in", "exact"],
            "product__name": ["in", "exact"],
            "railroad__name": ["in", "exact"],
            "origin__name": ["in", "exact"],
            "destination__name": ["in", "exact"],
            "reference_number": ["in", "exact"],
            "schedule__name": ["in", "exact"],
            "shipper__name": ["in", "exact"],
            "freight_payer__name": ["in", "exact"],
            "consignee__name": ["in", "exact"],
            "notify_party__name": ["in", "exact"],
            "care_party__name": ["in", "exact"],
            "pickup_party__name": ["in", "exact"],
            "importer__name": ["in", "exact"],
            "custom_broker__name": ["in", "exact"],
            "custom_broker_us__name": ["in", "exact"],
            "ultimate_consignee__name": ["in", "exact"],
            "ship_date": ["in", "exact"]
        }
        exclude = ["contact_preferences", "additional_data"]


class RailShipmentViewSet(AutoCompleteViewSetMixin, PrintableViewset):
    serializer_class = RailShipmentSerializer
    pagination_class = LimitOffsetPagination
    filterset_class = RailShipmentFilter
    queryset = RailShipment.objects.all().select_related().prefetch_related()
    ordering_fields = [
        "railroad__name",
        "status",
        "reference_number",
        "product__name",
        "consignee__name",
        "shipper__name",
        "freight_payer__name",
        "custom_broker__name",
        "notify_party__name",
        "care_party__name",
        "pickup_party__name",
        "ultimate_consignee__name",
        "custom_broker_us__name",
        "importer__name",
        "exporter__name",
        "origin__name",
        "destination__name",
        "car_count",
        "ship_date",
        "arrive_date",
    ]

    custom_ordering_fields = [["car_count", Count("cars")]]

    def get_queryset(self):
        qs = super().get_queryset().order_by("-id")
        return (
            qs.filter(origin__company__company=self.request.user.company)
            .select_related()
            .annotate(**dict(self.custom_ordering_fields))
        )

    def get_serializer_class(self):
        if self.action == "list":
            return RailShipmentListSerializerShortened
        return super().get_serializer_class()

    def get_context(self):
        from vantedge.wheelhouse.models import TankCar, HopperCar

        shipment=self.get_object()
        context = dict(object=shipment)
        if shipment.status in [shipment.not_valid_bol_status, "bol-accepted-with-error"]:
            status = shipment.status
            status = status.replace(
                "-", " "
                ).replace(
                    "-syntax", ""
                ).replace(
                    "sent", "pending"
                ).replace(
                    "open", "pending"
                ).replace(
                "acknowledged", "pending"
                ).upper()

            context.update(watermark={"text": f"{status}"})

        if shipment.is_master_billing():
            shipments_with_bol = shipment.sub_billings.exclude(status__in=shipment.not_valid_bol_status)
            billed_cars = TankCar.objects.filter(rail_shipment__in=shipments_with_bol)
            if not billed_cars:
                billed_cars = HopperCar.objects.filter(rail_shipment__in=shipments_with_bol)

        else:
            billed_cars = shipment.cars.all()

        context.update(billed_cars=billed_cars)

        return context

    # @action(detail=False, methods=["GET"], url_path="route/(?P<route_pk>[^/.]+)")
    # def route(self, request, route_pk=None):
    #     shipment, created = RailShipment.objects.get_or_create(
    #         route_id=route_pk, status=RailShipment.STATUS.open
    #     )
    #     if created:
    #         shipment.reset_defaults()
    #     serializer = self.get_serializer(shipment)
    #     return Response(serializer.data)

    @action(detail=True, methods=["GET"], url_path="add")
    def add(self, request, pk=None):
        shipment = self.get_object()
        serializer = self.get_serializer(shipment)
        return Response(serializer.data)

    @action(detail=True, methods=["GET"], url_path="cars")
    def cars(self, request, pk=None):
        shipment = self.get_object()
        serializer = LoadedRailCarSerializer(shipment.cars, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=["POST"], url_path="create_master_billing")
    def create_master_billing(self, request, pk=None):
        shipment = self.get_object()
        try:
            shipment.create_sub_billings()
        except Exception as e:
            return Response([str(e)], status=400)
        serializer = self.get_serializer(shipment)
        return Response(serializer.data)

    @action(detail=True, methods=["POST"], url_path="create_sub_billing")
    def create_sub_billing(self, request, pk=None):
        shipment = self.get_object()
        try:
            sub_shipment = shipment.create_sub_billing()
        except Exception as e:
            return Response([str(e)], status=400)
        serializer = self.get_serializer(sub_shipment)
        return Response(serializer.data)

    @action(detail=True, methods=["GET"], url_path="get_sub_billings")
    def get_sub_billings(self, request, pk=None):
        shipment = self.get_object()
        sub_billings = shipment.sub_billings.all()
        data = []
        for billing in sub_billings:
            data.append({
                "id": billing.id,
                "reference_number": billing.reference_number,
                "status": billing.status,
                "car": billing.cars.all().annotate(car=Concat(F("car_mark"), F("car_number"))).values_list("car", flat=True),
                "arrive_data": billing.arrive_date,
                "modified": billing.modified
            })
        return Response(data)

    @action(detail=True, methods=["POST"], url_path="ship")
    def ship(self, request, pk=None):
        shipment = self.get_object()
        hazardous_certificate_person=request.data.get("hazardous_certificate_person")
        hazardous_certificate_person = hazardous_certificate_person.lstrip().rstrip() if hazardous_certificate_person else None
        try:
            if (shipment.product.is_dangerous_goods and not hazardous_certificate_person):
                return Response(["Hazardous shipment certifier missing"], status=400)

            shipment.ship(self.request.user, hazardous_certificate_person)
        except Exception as e:
            return Response([str(e)], status=400)
        serializer = self.get_serializer(shipment)
        return Response(serializer.data)

    @action(detail=True, methods=["POST"], url_path="cancel_shipment")
    def cancel_shipment(self, request, pk=None):
        shipment = self.get_object()
        reason = request.data.get("reason")
        if shipment.is_master_billing():
            if shipment.sub_billings.filter(Q(arrive_date__isnull=False) | ~Q(status__in=RailShipment.VALID_STATUSES_FOR_CANCELLATION)):
                return Response(
                    ["Only billed shipment could be canceled"],
                    status=status.HTTP_400_BAD_REQUEST,
                )

            with transaction.atomic():
                for sub_billing in shipment.sub_billings.all():
                    sub_billing.cancel_shipment(self.request.user, reason=reason)

                RailShipmentNote(
                    rail_shipment=shipment,
                    note=f"Master Billing voided by {self.request.user} on {timezone.localtime()}. Reason for cencellation: {reason}",
                    is_write_protect=True,
                ).save()

            serializer = self.get_serializer(shipment)
            return Response(serializer.data)

        if (
            shipment.arrive_date
            or shipment.status not in RailShipment.VALID_STATUSES_FOR_CANCELLATION
        ):
            return Response(
                ["Only billed shipment could be canceled"],
                status=status.HTTP_400_BAD_REQUEST,
            )

        try:
            shipment.cancel_shipment(self.request.user,  reason = reason)
        except Exception as e:
            return Response([str(e)], status=400)
        serializer = self.get_serializer(shipment)
        return Response(serializer.data)

    @action(detail=True, methods=["POST"], url_path="void_shipment")
    def void_shipment(self, request, pk=None):
        reason = request.data.get("reason")
        shipment = self.get_object()
        if shipment.is_master_billing():
            if shipment.sub_billings.filter(Q(status__in=["open"]) | Q(arrive_date__isnull=False)):
                return Response(
                    ["Can't void this shipment"], status=status.HTTP_400_BAD_REQUEST
                )

            with transaction.atomic():
                for sub_billing in shipment.sub_billings.exclude(status__in=["open","voided"]):
                    sub_billing.void_shipment(cancellation_sent=False, user=self.request.user, reason=reason)

                RailShipmentNote(
                    rail_shipment=shipment,
                    note=f"Master Billing voided by {self.request.user} on {timezone.localtime()}. Reason for voiding: {reason}",
                    is_write_protect=True,
                ).save()

            serializer = self.get_serializer(shipment)
            return Response(serializer.data)

        if shipment.arrive_date or shipment.status in ["open", "voided"]:
            return Response(
                ["Can't void this shipment"], status=status.HTTP_400_BAD_REQUEST
            )

        try:
            shipment.void_shipment(cancellation_sent=False, user=self.request.user, reason=reason)
        except Exception as e:
            return Response([str(e)], status=400)
        serializer = self.get_serializer(shipment)
        return Response(serializer.data)

    @action(detail=True, methods=["POST"], url_path="open_for_correction")
    def open_for_correction(self, request, pk=None):
        shipment = self.get_object()
        if (
            shipment.arrive_date
            or shipment.status not in RailShipment.VALID_STATUSES_FOR_CORRECTION
        ):
            return Response(
                ["Only billed shipment could be corrected"],
                status=status.HTTP_400_BAD_REQUEST,
            )

        try:
            shipment.set_for_correction(self.request.user)
        except Exception as e:
            return Response([str(e)], status=400)
        serializer = self.get_serializer(shipment)
        return Response(serializer.data)

    def perform_update(self, serializer):
        # if it is master-billing replicate changes to sub-billings
        # if not master-billing just update the related billing
        skip_fields = ["id", "status", "master_billing", "data", "schedule", "ship_data", "arrive_data"]
        instance = serializer.save()

        if instance.is_master_billing():
            # all billings must have same entities and attribs
            if instance.sub_billings.filter(~Q(status__in=instance.VALID_STATUSES_FOR_CORRECTION + ["open", "correction-open"]) | Q(arrive_date__isnull=False)).exists():
                raise ValidationError("Not all sub billings are changable")

            with transaction.atomic():
                for sub_billing in instance.sub_billings.filter(status__in=instance.VALID_STATUSES_FOR_CORRECTION + ["open", "correction-open"], arrive_date__isnull=True):
                    for field_name in [field.name for field in instance._meta.fields if field.name not in skip_fields]:
                        value = getattr(instance, field_name)
                        setattr(sub_billing, field_name, value)
                        sub_billing.save()
                        if sub_billing.status != "correction-open":
                            sub_billing.set_for_correction(self.request.user)

                RailShipmentNote(
                    rail_shipment=instance,
                    note=f"Master Billing corrected by {self.request.user} on {timezone.localtime()}",
                    is_write_protect=True,
                ).save()
        return instance

    @action(detail=False, methods=["GET"], permission_classes=[IsAuthenticated])
    def count(self, request):
        qs = self.filter_queryset(self.get_queryset())
        return Response(qs.count())

    @action(detail=True, methods=["POST"], permission_classes=[IsAuthenticated])
    def update_contact_preferences(self, request, pk=None):
        shipment = self.get_object()

        preferences = request.data.get("preferences")
        if not preferences:
            return Response(
                ["No preferences given"], status=status.HTTP_400_BAD_REQUEST
            )

        try:
            shipment.update_contact_preferences(preferences)
        except Exception as e:
            return Response([str(e)], status=status.HTTP_400_BAD_REQUEST)

        serializer = self.get_serializer(shipment)
        return Response(serializer.data)

    @action(detail=True, methods=["POST"], url_path="send_bol")
    def send_bol(self, request, pk=None):
        shipment = self.get_object()
        target = request.data.get("target")
        archive_id = request.data.get("archive_id")

        if archive_id and not shipment.bol_archives.filter(id=archive_id):
            return Response(status=404)

        if target == "parties":
            shipment.send_email_bol_to_contacts(
                to=[], bcc=[], realtime=True, archive_id=archive_id
            )
        else:
            shipment.send_bol(
                to=[self.request.user.username], bcc=[], archive_id=archive_id
            )

        return Response()

    @action(detail=True, methods=["GET"], url_path="bol_archive")
    def bol_archive(self, request, pk):
        shipment = self.get_object()
        bol_archive_id = request.GET.get("bolArchiveId")

        bol_archive = get_object_or_404(
            BOLArchive, id=bol_archive_id, shipment=shipment
        )

        response = HttpResponse(bol_archive.data_pdf, content_type="application/pdf")
        response[
            "Content-Disposition"
        ] = f'attachment; filename="{shipment.id}_{bol_archive_id}.pdf"'
        return response

    @action(detail=False, methods=["POST"], url_path="request_shipment")
    def request_shipment(self, request):
        # get contract shcedule, or shipment data and create shipment for that
        schedule_id = request.data.get("schedule_id", None)
        cars = request.data.get("cars", [])

        shipper = request.data.get("shipper", None)
        consignee = request.data.get("consignee", None)
        product = request.data.get("product", None)
        origin = request.data.get("origin", None)
        destination = request.data.get("destination", None)
        railroad = request.data.get("railroad", None)
        routing = request.data.get("routing", None)
        freight_charges = request.data.get("freight_charges", "")
        reference_number = request.data.get("reference_number")
        billing_type = request.data.get("billing_type")

        if not request.user.has_perm("wheelhouse.ship_railshipment"):
            return Response(status=status.HTTP_403_FORBIDDEN)

        if schedule_id:
            try:
                schedule = RailShippingSchedule.objects.get(id=schedule_id)
            except RailShippingSchedule.DoesNotExist:
                return Response(status=404)

            shipment = RailShipment.populate_shipment_from_schedule(schedule_id)

        else:
            shipment_data = dict()

            shipment_data["product"] = Product.objects.filter(
                name__iexact=product, company__company=request.user.company
            ).first()
            if shipment_data["product"] is None:
                return Response(
                    {"message": "Product not found"}, status=status.HTTP_400_BAD_REQUEST
                )

            shipment_data["shipper"] = Party.objects.filter(
                name__iexact=shipper, company__company=request.user.company
            ).first()
            if shipment_data["shipper"] is None:
                return Response(
                    {"message": "Shipper not found"}, status=status.HTTP_400_BAD_REQUEST
                )

            shipment_data["consignee"] = Party.objects.filter(
                name__iexact=consignee, company__company=request.user.company
            ).first()
            if shipment_data["consignee"] is None:
                return Response(
                    {"message": "Consignee not found"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            shipment_data["origin"] = Facility.objects.filter(
                name__iexact=origin, company__company=request.user.company
            ).first()
            if shipment_data["origin"] is None:
                return Response(
                    {"message": "Origin not found"}, status=status.HTTP_400_BAD_REQUEST
                )

            shipment_data["destination"] = Facility.objects.filter(
                name__iexact=destination, company__company=request.user.company
            ).first()
            if shipment_data["destination"] is None:
                return Response(
                    {"message": "Destination not found"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            if not routing:
                return Response(
                    {"message": "Routing is not valid"},
                    status=status.HTTP_400_BAD_REQUEST,
                )
            shipment_data["routing"] = routing

            shipment_data["railroad"] = Railroad.objects.filter(
                name__iexact=railroad,
                id__in=shipment_data["origin"].railroads.values_list("id", flat=True),
            ).first()
            if shipment_data["railroad"] is None:
                return Response(
                    {"message": "Railroad not found"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            if freight_charges.capitalize() not in RailShipment.FREIGHT_CHARGE_TYPE:
                return Response(
                    {"message": "Freight charge is not valid"},
                    status=status.HTTP_400_BAD_REQUEST,
                )
            shipment_data["freight_charges"] = freight_charges.capitalize()

            shipment_data["reference_number"] = (
                reference_number
                if reference_number
                else BolNumber.generate_reference_number()
            )

            if billing_type.capitalize() not in RailShipment.BILLING_TYPES:
                return Response(
                    {"message": "Billing Type is not valid"},
                    status=status.HTTP_400_BAD_REQUEST,
                )
            shipment_data["billing_type"] = billing_type.capitalize()

            for party in [
                "freight_payer",
                "custom_broker",
                "custom_broker_us",
                "notify_party",
                "care_party",
                "pickup_party",
            ]:
                if request.data.get(party, None):
                    shipment_data[party] = Party.objects.filter(
                        name__iexact=request.data.get(party, None),
                        company__company=request.user.company,
                    ).first()
                    if shipment_data[party] is None:
                        return Response(
                            {"message": f"{party} not found"},
                            status=status.HTTP_400_BAD_REQUEST,
                        )

        shipment = RailShipment(**shipment_data)

        try:
            with transaction.atomic():
                shipment.save()
                if not reference_number:
                    shipment.update_bolnumber()
                try:
                    if len(cars) == 0:
                        raise Exception("No car data given")

                    for car in cars:
                        car_type = car.get("car_type", "tank car")
                        car_model = CAR_TYPE_MODEL.get(car_type)

                        car_obj = car_model(
                            car_mark=car["mark"],
                            car_number=car["number"],
                            current_location=shipment.origin,
                            rail_shipment=shipment,
                            product=shipment.product,
                        )

                        for attr in car.keys():
                            if attr in [
                                field.name
                                for field in car_model._meta.get_fields()
                                if field.name not in ["id"]
                            ]:
                                setattr(car_obj, attr, car.get(attr))

                        car_obj.save()

                except Exception as e:
                    raise Exception(str(e))

                shipment.send_edi404(request.user)

        except Exception as e:
            return Response({"message": str(e)}, status=status.HTTP_400_BAD_REQUEST)

        return Response(
            {
                "message": "Shipment sent",
                "id": shipment.id,
                "shipment": RailShipmentSerializer(shipment).data,
                "status": shipment.status,
            }
        )

    @action(detail=True, methods=["GET"], url_path="shipment_status")
    def shipment_status(self, request, pk):
        shipment = RailShipment.objects.filter(
            id=pk, origin__company__company=request.user.company
        ).first()

        if shipment is None:
            return Response(
                {"message": "Shipment not found"}, status=status.HTTP_400_BAD_REQUEST
            )

        return Response({"status": shipment.status})

    @action(detail=False, methods=["get"])
    def print_multiple(self, request):
        """
        Responds with a zip of pdfs tagged with the print-code.
        """
        shipment_ids = request.query_params.getlist("shipment_ids[]")
        shipment_ids = [int(id) for id in shipment_ids]
        shipments = RailShipment.objects.filter(id__in=shipment_ids)

        zipped_pdfs = get_bols_and_zip(shipments)

        response = HttpResponse(zipped_pdfs, content_type="application/zip")
        response["Content-Disposition"] = 'attachment; filename="bols.zip"'
        return response

    @action(detail=True, methods=["POST"])
    def assign_to_schedule(self, request, pk=None):
        schedule_id = request.data.get("schedule_id")
        shipment = self.get_object()
        schedule = RailShippingSchedule.objects.get(id=schedule_id)
        contract = schedule.purchase_contract or schedule.sale_contract
        if shipment.origin.company != (schedule.purchase_contract and schedule.purchase_contract.buyer.company or
                                       schedule.sale_contract and schedule.sale_contract.buyer.company) :
            return Response(status=status.HTTP_403_FORBIDDEN)

        if shipment.cars.filter(waybill__isnull=True):
            return Response(["There is a car without waybill in the shipemnt"], status=status.HTTP_400_BAD_REQUEST)

        for car in shipment.cars.all():
            if contract.buyer_number:
                car.waybill.trip.buyer_contract = schedule
            elif contract.seller_number:
                car.waybill.trip.seller_contract = schedule
            car.waybill.trip.save(update_fields=["seller_contract", "buyer_contract"])

        shipment.schedule = schedule
        shipment.save(update_fields=["schedule"])
        RailShipmentNote(
            rail_shipment=shipment,
            note=f"Assigned to schedule {schedule.id}: {schedule.name}, by {request.user.email} on {timezone.localtime()}",
            is_write_protect=True,
        ).save()
        return Response()

    @action(detail=True, methods=["POST"])
    def create_template(self, request, pk=None):
        shipment = self.get_object()
        name = request.data.get("name")
        schedule = RailShippingSchedule()
        for field_name in [field.name for field in shipment._meta.fields]:
            if hasattr(schedule, field_name):
                setattr(schedule, field_name, getattr(shipment, field_name))

        schedule.name = name or ""
        schedule.route = shipment.routing
        schedule.schedule = []
        schedule.save()

        shipment.schedule = schedule
        shipment.save(update_fields=["schedule"])
        serializer = self.get_serializer_class()
        return Response(serializer(shipment, many=False).data)

class BOLView(DetailView):
    # vanilla Django DetailView
    model = RailShipment
    template_name = "printables/bol.html"

    def get_queryset(self):
        qs = super().get_queryset()
        return qs.filter(origin__company__company=self.request.user.company)


class BOLPrintView(WeasyTemplateResponseMixin, BOLView):
    # output of MyModelView rendered as PDF with hardcoded CSS
    # pdf_stylesheets = [
    #     settings.STATIC_ROOT + 'css/app.css',
    # ]
    # show pdf in-line (default: True, show download dialog)
    pdf_attachment = False
    # suggested filename (is required for attachment!)
    pdf_filename = "foo.pdf"


class RailShipmentNoteViewSet(ModelViewSet):
    serializer_class = RailShipmentNoteSerializer
    queryset = RailShipmentNote.objects.all().order_by("id")

    def get_queryset(self):
        return (
            super()
            .get_queryset()
            .filter(rail_shipment__origin__company__company=self.request.user.company)
        )

    def list(self, request):
        shipment_id = self.request.query_params.get("rail_shipment", None)
        if shipment_id:
            qs = self.get_queryset().filter(rail_shipment=shipment_id)
            return Response(RailShipmentNoteSerializer(qs, many=True).data)
        return super().list(request)

    def destroy(self, request, *args, **kwargs):
        obj = self.get_object()
        if obj.is_write_protect:
            return Response(status=status.HTTP_403_FORBIDDEN)
        return super().destroy(request, *args, **kwargs)

    def update(self, request, *args, **kwargs):
        obj = self.get_object()
        if obj.is_write_protect:
            return Response(status=status.HTTP_403_FORBIDDEN)
        return super().update(request, *args, **kwargs)
