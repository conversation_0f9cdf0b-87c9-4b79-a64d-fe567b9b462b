from django.db.models import Q

from rest_framework import serializers
from rest_framework.exceptions import ValidationError

from vantedge.util.mixins import validate_schemafield
from ...models.contract import Contract
from ...models.company import Party
from ...models.schedule import RailShippingSchedule
from ...models.product import Product
from ...models.location import Facility
from .certificate import CertificateSerializer
from ...models.rail import Route
from ...models.validations import validate_route
from .rail import RailShipmentListSerializer
from vantedge.invitation.models.utils import (
    contract_is_shared, get_party_fields,
    get_facility_fields,
    get_entity_defaults_by_partnership
)
from vantedge.trackandtrace.models import Trip
from ..serializers.utility import (
    ModelNameIdSerializer
)

from datetime import date
import arrow

class RouteSerializer(serializers.ModelSerializer):
    schedule = serializers.ListField(child=serializers.IntegerField(), required=False)
    actual = serializers.SerializerMethodField()

    class Meta:
        model = Route
        exclude = ()

    def get_actual(self, route):
        days = (route.contract.end_date - route.contract.start_date).days + 1
        history_days = (
            min(route.contract.end_date, date.today()) - route.contract.start_date
        ).days
        result = [0] * history_days + [None] * (days - history_days)
        for rail_shipment in route.rail_shipments.all():
            try:
                result[(rail_shipment.ship_date - route.contract.start_date).days] = (
                    rail_shipment.cars.count()
                )
            except IndexError:
                print("Index error", rail_shipment.ship_date)
                pass
        return result

    def save(self, **kwargs):
        super().save(**kwargs)


class ContractShortListSerializer(serializers.ModelSerializer):
    contract_number = serializers.SerializerMethodField()
    schedules = serializers.SerializerMethodField()

    def get_contract_number(self, obj):
        return obj.buyer_number or obj.seller_number

    def get_schedules(self, obj):
        schedules = obj.purchase_schedules.all() | obj.sale_schedules.all()
        return [{"id": item.id, "name": item.name} for item in schedules]

    class Meta:
        model = Contract
        fields = ("id", "contract_number", "schedules")

class ContractDashboardSerializer(serializers.ModelSerializer):
    buyer_name = serializers.SerializerMethodField()
    seller_name = serializers.SerializerMethodField()
    volume_scheduled = serializers.SerializerMethodField()
    volume_shipped = serializers.SerializerMethodField()
    volume_scheduled_to_date = serializers.SerializerMethodField()

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self._contract_patterns_cache = {}

    def get_patterns(self, obj, key):
        if result := self._contract_patterns_cache.get(obj):
            return result[key]

        patterns_data = RailShippingSchedule.objects.filter(
            Q(purchase_contract=obj) | Q(sale_contract=obj)
            ).values("id", "schedule")

        self._contract_patterns_cache[obj] = {}
        self._contract_patterns_cache[obj]["pattern_ids"] = [pattern_data["id"] for pattern_data in patterns_data]
        self._contract_patterns_cache[obj]["schedules"] = [pattern_data["schedule"] for pattern_data in patterns_data]
        return self._contract_patterns_cache[obj][key]

    def get_volume_scheduled(self, obj):
        schedules = self.get_patterns(obj, "schedules")
        return sum(int(obj['quantity']) for items in schedules for obj in items if obj)

    def get_volume_scheduled_to_date(self, obj):
        today = date.today()
        schedules_items = self.get_patterns(obj, "schedules")

        sum_schedules = 0
        for schedules in schedules_items:
            for schedule in schedules:
                if scheduled_date := schedule.get("date", None):
                    if arrow.get(scheduled_date) < arrow.get(today):
                        sum_schedules += int(schedule["quantity"])

        return sum_schedules

    def get_volume_shipped(self, obj):
        shipped_filters = (
            Q(seller_contract__in=self.get_patterns(obj, "pattern_ids")) |
            Q(buyer_contract__in=self.get_patterns(obj, "pattern_ids"))
        )
        shipped = Trip.objects.filter(shipped_filters)
        return shipped.count()

    def get_buyer_name(self, obj):
        return obj.buyer.name if obj.buyer else ""

    def get_seller_name(self, obj):
        return obj.seller.name if obj.seller else ""

    class Meta:
        model = Contract
        fields = (
            "id",
            "buyer_name",
            "seller_name",
            "buyer_number",
            "seller_number",
            "total_volume",
            "start_date",
            "end_date",
            "volume_scheduled",
            "volume_shipped",
            "volume_scheduled_to_date"
        )
        read_only_fields = fields

class RailShippingScheduleCountSerializer(serializers.ModelSerializer):
    volume_shipped = serializers.SerializerMethodField(read_only=True)
    volume_shipped_to_date = serializers.SerializerMethodField(read_only=True)
    volume_scheduled = serializers.SerializerMethodField(read_only=True)
    volume_scheduled_to_date = serializers.SerializerMethodField(read_only=True)
    shipments_car_count = serializers.SerializerMethodField()
    origin = ModelNameIdSerializer(Facility)(read_only=True)
    destination = ModelNameIdSerializer(Facility)(read_only=True)
    fleets = serializers.ListField()

    def get_volume_shipped(self, obj):
        request = self.context.get("request", False)
        if request:
            dateRange = request.query_params.get("dateRange", False)
            if dateRange:
                dateRangeFrom, dateRangeTo = dateRange.split("_")
                return obj.volume_shipped(
                    {"dateRangeFrom": dateRangeFrom, "dateRangeTo": dateRangeTo}
                )

        return obj.volume_shipped()

    def get_volume_shipped_to_date(self, obj):
        params = {"dateRangeFrom": None, "dateRangeTo": date.today()}

        return obj.volume_shipped(params)

    def get_volume_scheduled(self, obj):
        request = self.context.get("request", False)
        if request:
            dateRange = request.query_params.get("dateRange", False)
            if dateRange:
                dateRangeFrom, dateRangeTo = dateRange.split("_")
                return obj.volume_scheduled(
                    {"dateRangeFrom": dateRangeFrom, "dateRangeTo": dateRangeTo}
                )
        return obj.volume_scheduled()

    def get_volume_scheduled_to_date(self, obj):
        params = {"dateRangeFrom": None, "dateRangeTo": date.today()}

        return obj.volume_scheduled(params)

    def get_shipments_car_count(self, obj):
        request = self.context.get("request", False)
        if request:
            dateRange = request.query_params.get("dateRange", False)
            if dateRange:
                dateRangeFrom, dateRangeTo = dateRange.split("_")
                return obj.shipments_car_count(
                    {"dateRangeFrom": dateRangeFrom, "dateRangeTo": dateRangeTo}
                )
        return obj.shipments_car_count()

    class Meta:
        model = RailShippingSchedule
        fields = [
            "id",
            "name",
            "route",
            "cars_quantity",
            "volume_shipped",
            "volume_shipped_to_date",
            "volume_scheduled",
            "volume_scheduled_to_date",
            "purchase_contract",
            "sale_contract",
            "shipments_car_count",
            "origin",
            "destination",
            "fleets",
        ]


class ContractListSerializer(serializers.ModelSerializer):
    buyer = ModelNameIdSerializer(Party)(read_only=True)
    seller = ModelNameIdSerializer(Party)(read_only=True)
    product = ModelNameIdSerializer(Product)(read_only=True)
    schedules = serializers.SerializerMethodField()
    is_shared = serializers.SerializerMethodField()
    order = serializers.PrimaryKeyRelatedField(many=False, read_only=True)
    pending_nominations_count = serializers.SerializerMethodField()

    def get_is_shared(self, obj):
        request = self.context.get("request", {})

        if hasattr(request, 'user'):
            company = request.user.company
            return contract_is_shared(obj, company)

        return False

    def get_pending_nominations_count(self, obj):
        if hasattr(obj, 'order') and obj.order:
            return obj.order.nomination_set.filter(status="Sent").count()

    def get_schedules(self, obj):
        request = self.context.get("request", {})
        purchase_schedules = RailShippingScheduleCountSerializer(
            obj.purchase_schedules.all(),
            read_only=True,
            many=True,
            context={"request": request},
        )
        sale_schedules = RailShippingScheduleCountSerializer(
            obj.sale_schedules.all(),
            read_only=True,
            many=True,
            context={"request": request},
        )
        schedules = purchase_schedules.data + sale_schedules.data
        # get unique rows based on id
        schedules = {sched["id"]: sched for sched in schedules}.values()
        return schedules

    class Meta:
        model = Contract
        exclude = ()


class ContractSerializer(serializers.ModelSerializer):
    routes = RouteSerializer(read_only=True, many=True)
    order = serializers.PrimaryKeyRelatedField(many=False, read_only=True)
    is_shared = serializers.SerializerMethodField()

    def get_is_shared(self, obj):
        request = self.context.get("request", {})

        if request:
            company = request.user.company
            return contract_is_shared(obj, company)

        return False

    def validate(self, attrs):
        contract = self.instance
        request = self.context.get("request", {})

        if request:
            company = request.user.company

            if contract_is_shared(contract, company):
                if contract.buyer != attrs.get('buyer'):
                    raise ValidationError({"buyer": "This field is readonly for shared contracts."})

                if contract.seller != attrs.get('seller'):
                    raise ValidationError({"seller": "This field is readonly for shared contracts."})

        return attrs

    class Meta:
        model = Contract
        exclude = ()


@validate_schemafield(["data"])
class PartySerializer(serializers.ModelSerializer):
    certificates = CertificateSerializer(many=True, read_only=True)
    application_configuration_id = serializers.SerializerMethodField()
    is_readonly = serializers.SerializerMethodField()
    company = serializers.StringRelatedField(many=False)

    def __init__(self, *args, **kwargs):
        # Get the user from the context
        user = kwargs.pop('context', {}).get('request', {}).user

        super().__init__(*args, **kwargs)

        # Conditionally set 'wheelhouse_company' as read-only based on user permissions
        if not user.is_superuser:
            self.fields['wheelhouse_company'].read_only = True

    def get_application_configuration_id(self, obj):
        if obj.wheelhouse_company:
            return obj.wheelhouse_company.company.applicationconfiguration.id
        return None

    def get_is_readonly(self, obj):
        request = self.context.get("request")
        _is_readonly = True

        if request:
            company = request.user.company
            _is_readonly = company != obj.company.company

        return _is_readonly

    class Meta:
        model = Party
        fields = [
            "id",
            "name",
            "party_types",
            "data",
            "certificates",
            "location_set",
            "wheelhouse_company",
            "application_configuration_id",
            "company",
            "is_readonly",
        ]


class EntityCharField(serializers.CharField):
    def get_attribute(self, instance):
        entity_name, field = self.source_attrs
        if entity := getattr(instance, entity_name):
            return getattr(entity, field)
        return None


class RailShippingScheduleListSerializer(serializers.ModelSerializer):
    consignee = EntityCharField(source="consignee.name")
    shipper = EntityCharField(source="shipper.name")
    freight_payer = EntityCharField(source="freight_payer.name")
    notify_party = EntityCharField(source="notify_party.name")
    care_party = EntityCharField(source="care_party.name")
    pickup_party = EntityCharField(source="pickup_party.name")
    ultimate_consignee = EntityCharField(source="ultimate_consignee.name")
    custom_broker_us = EntityCharField(source="custom_broker_us.name")
    custom_broker_mx = EntityCharField(source="custom_broker_mx.name")
    account_of = EntityCharField(source="account_of.name")
    account_of_origin = EntityCharField(source="account_of_origin.name")
    account_of_destination = EntityCharField(source="account_of_destination.name")
    accounting_rule_11 = EntityCharField(source="accounting_rule_11.name")
    importer = EntityCharField(source="importer.name")
    exporter = EntityCharField(source="exporter.name")
    origin = EntityCharField(source="origin.name")
    origin_id = serializers.IntegerField(source="origin.id")
    destination = EntityCharField(source="destination.name")
    product = serializers.SerializerMethodField()
    seller = serializers.SerializerMethodField()
    buyer = serializers.SerializerMethodField()
    contract = serializers.SerializerMethodField()
    shipments = RailShipmentListSerializer(
        source="rail_shipments", many=True, read_only=True
    )
    has_shipments = serializers.SerializerMethodField()

    def _get_contract(self, obj):
        return obj.sale_contract or obj.purchase_contract

    def get_contract(self, obj):
        contract = self._get_contract(obj)
        if contract:
            return ContractSerializer(contract).data

    def get_product(self, obj):
        if obj.product:
            return obj.product.name

        contract = self._get_contract(obj)
        if contract:
            return contract.product.name

    def get_buyer(self, obj):
        contract = self._get_contract(obj)
        if contract:
            return contract.buyer.name if contract.buyer else None

    def get_seller(self, obj):
        contract = self._get_contract(obj)
        if contract:
            return contract.seller.name if contract.seller else None

    def get_has_shipments(self, obj):
        return obj.rail_shipments.exists()

    class Meta:
        model = RailShippingSchedule
        exclude = ()


class RailShippingScheduleSerializer(serializers.ModelSerializer):
    shipments = serializers.SerializerMethodField()
    nomination = serializers.PrimaryKeyRelatedField(many=False, read_only=True)
    has_nomination = serializers.SerializerMethodField()

    def get_shipments(self, obj):
        result = []
        shipments = obj.rail_shipments.all()
        for shipment in shipments:
            result.append(dict(
                ship_date = shipment.ship_date,
                car_count = shipment.cars.all().count()
            ))
        return result

    def get_has_nomination(self, obj):
        from vantedge.invitation.models import Nomination

        has_nomination = False

        try:
            has_nomination = bool(obj.nomination.status == Nomination.Status.SENT)
        except Exception:
            pass

        return has_nomination

    class Meta:
        model = RailShippingSchedule
        exclude = ("additional_fields",)

    def validate(self, attrs):
        if "route" in attrs.keys():
            route_is_valid, route_msg = validate_route(attrs["route"])
            if route_is_valid:
                attrs["route"] = route_msg
            else:
                raise serializers.ValidationError(route_msg)

        schedule = self.instance

        # not less than shipped
        if schedule and "cars_quantity" in attrs.keys():
            quantity = get_int_or_zero(attrs.get("cars_quantity"))
            shipped_quantity = schedule.shipments_car_count()

            if quantity and (shipped_quantity > quantity):
                raise serializers.ValidationError(
                    {"quantity": f"cannot be less than the shipped quantity ({shipped_quantity} shipped)"}
                )

        # not more than contract_total or contract_aggregate
        if schedule and "cars_quantity" in attrs.keys():
            quantity = get_int_or_zero(attrs.get("cars_quantity"))
            contract = get_schedule_contract(schedule)

            if contract:
                # not more than contract_total
                contract_max = contract.total_volume

                if contract_max and (quantity > contract_max):
                    raise serializers.ValidationError(
                        {"quantity": f"total schedule quantity ({quantity}) exceeds total contract quantity ({contract_max})"}
                    )

                # not more than aggregate
                other_schedules = (
                    RailShippingSchedule.objects
                        .filter(Q(purchase_contract=contract.id) | Q(sale_contract=contract.id))
                        .exclude(id=schedule.id)
                )
                contract_max = contract.total_volume

                aggregate_from_assoc_schedules = 0
                for s in other_schedules:
                    aggregate_from_assoc_schedules += get_schedule_total(s)

                if contract_max and (aggregate_from_assoc_schedules + quantity > contract_max):
                    raise serializers.ValidationError(
                        {"quantity": f"total schedule quantities ({aggregate_from_assoc_schedules + quantity}) exceed total contract quantity ({contract_max})"}
                    )

        request = self.context.get("request", False)
        # ------------ partner-portal
        if schedule and request:
            contract = get_schedule_contract(schedule)
            company = request.user.company

            if contract and contract_is_shared(contract, company):
                # validate entity-fields based on inviter-company
                order = contract.order if hasattr(contract, 'order') else None

                if not order:
                    raise ValidationError("No order on this contract yet.")

                buyer = order.get_buyer()
                seller = order.get_seller()
                entity_defaults = get_entity_defaults_by_partnership(seller, buyer)

                for k, v in attrs.items():
                    if v and (k in get_party_fields() + get_facility_fields()):
                        # default-field -> only mine
                        if k in entity_defaults.keys():
                            mine = v.company.company == company

                            if not mine:
                                raise ValidationError({f"{k}": "Invalid choice for shipping schedule in shared contract."})

                        # non-default -> only theirs
                        else:
                            theirs = v.company.company == buyer

                            if not theirs:
                                raise ValidationError({f"{k}": "Invalid choice for shipping schedule in shared contract."})
            else:
                # validate all entities are for this railschedule-owner
                for k, v in attrs.items():
                    if v and (k in get_party_fields() + get_facility_fields()):
                        mine = v.company.company == company

                        if not mine:
                            raise ValidationError({f"{k}": "Entity not found for shipping schedule in independent contract."})

        if self.instance:
            missing_items = {}
            mandatory_fields = {
                "shipper": "Mandatory field, please select a party.",
                "consignee": "Mandatory field, please select a party.",
                "origin": "Mandatory field, please select a facility.",
                "destination": "Mandatory field, please select a facility.",
                "freight_charges": "Mandatory field, please select from options.",
                "route": "Mandatory field, please fill route."
            }

            for mandatory_field, message in mandatory_fields.items():
                if not attrs.get(mandatory_field) and not (self.instance and getattr(self.instance, mandatory_field)):
                    missing_items[mandatory_field] = message

            if missing_items:
                raise ValidationError(missing_items)

        return attrs


# ------------ utils

def get_schedule_contract(schedule):
    if hasattr(schedule, 'purchase_contract') or hasattr(schedule, 'sale_contract'):
        return schedule.purchase_contract or schedule.sale_contract

def get_schedule_total(schedule):
    # RailShippingSchedule -> int
    """
    Return the nominated quantity or the approved (schedule) plus any shipments
    """
    quantity = get_int_or_zero(schedule.cars_quantity)

    return max(quantity, schedule.shipments_car_count())

def get_int_or_zero(value):
    try:
        return int(value)
    except Exception:
        return 0
