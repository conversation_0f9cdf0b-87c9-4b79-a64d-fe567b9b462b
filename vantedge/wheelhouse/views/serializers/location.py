from rest_framework import serializers

from vantedge.yard_management.models import RailYard
from vantedge.util.mixins import validate_schemafield

from ...models.location import Location, Facility, Riser
from ...models.riser_schedule_company_config import RiserScheduleCompanyConfig

class LocationSerializer(serializers.ModelSerializer):
    structure = serializers.SerializerMethodField()

    class Meta:
        model = Location
        exclude = ["location_key"]

    def get_structure(self, obj):
        return str(obj)

class RailYardConfigSerializer(serializers.ModelSerializer):
    class Meta:
        model = RailYard
        exclude = [ 'facility' ]

@validate_schemafield(["data"])
class FacilitySerializer(serializers.ModelSerializer):
    company = serializers.StringRelatedField(many=False)
    is_readonly = serializers.SerializerMethodField()
    railyard = RailYardConfigSerializer(read_only=True)

    def get_is_readonly(self, obj):
        request = self.context.get("request")
        _is_readonly = True

        if request:
            company = request.user.company
            _is_readonly = company != obj.company.company

        return _is_readonly

    class Meta:
        model = Facility
        fields = [
            "id",
            "name",
            "alias",
            "emergency_phone",
            "firms_code",
            "legal_description",
            "data",
            "railroads",
            "booking_required_fields",
            "force_booking_data_time_window",
            "minimum_bookings_time_gap",
            "company",
            "rail_car_initial_status",
            "rail_car_status_when_loaded",
            "is_readonly",
            "rail_car_types",
            "default_safety_factor_inch",
            "default_safety_factor_percent",
            "default_measurement_system",
            "default_density_unit",
            "disabled_fields",
            "railyard",
            "embargo_number",
            "embargo_permit_numbers",
            "enforce_loaded_car_seal_numbers",
            "enforce_empty_car_seal_numbers"
        ]

@validate_schemafield(["data"])
class RiserSerializer(serializers.ModelSerializer):
    structure = serializers.SerializerMethodField()

    def get_structure(self, obj):
        return str(obj)

    class Meta:
        model = Riser
        exclude = ["company", "location_key"]


class RiserScheduleCompanyConfigSerializer(serializers.ModelSerializer):
    company_name = serializers.SerializerMethodField()

    class Meta:
        model = RiserScheduleCompanyConfig
        fields = ['id', 'riser', 'schedule_company', 'company_name', 'max_slots']

    def get_company_name(self, obj):
        return obj.schedule_company.company.name

    def validate(self, data):
        max_slots = data.get('max_slots')

        if max_slots and max_slots < 0:
            raise serializers.ValidationError("Max slots cannot be negative.")

        return data
