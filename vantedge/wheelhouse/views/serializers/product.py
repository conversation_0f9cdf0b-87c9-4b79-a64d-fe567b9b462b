from rest_framework import serializers

from vantedge.util.mixins import validate_schemafield
from ...models.product import Product, ProductType

from decimal import Decimal

@validate_schemafield(["data"])
class ProductSerializer(serializers.ModelSerializer):
    class Meta:
        model = Product
        fields = [
            "id",
            "name",
            "density",
            "gal_to_liter_conversion_factor",
            "default_stcc",
            "stcc_aliases",
            "product_type",
            "data",
        ]


class ProductTypeSerializer(serializers.ModelSerializer):
    product_set = ProductSerializer(many=True, read_only=True)

    class Meta:
        model = ProductType
        fields = "__all__"


class ProductColorSerializer(serializers.ModelSerializer):
    color = serializers.CharField(source="data.yard_management.display_color")
    class Meta:
        model = Product
        fields = [
            "id",
            "name",
            "color"
        ]

class ProductPickerSerializer(serializers.ModelSerializer):
    value = serializers.CharField(source="name")
    density = serializers.SerializerMethodField()
    gal_to_liter_conversion_factor = serializers.SerializerMethodField()

    def get_density(self, obj):
        try:
            return Decimal(obj.density)
        except:
            return 1

    def get_gal_to_liter_conversion_factor(self, obj):
        try:
            return Decimal(obj.gal_to_liter_conversion_factor)
        except:
            return 3.78541

    class Meta:
        model = Product
        fields = ["id", "value", "density", "gal_to_liter_conversion_factor"]

