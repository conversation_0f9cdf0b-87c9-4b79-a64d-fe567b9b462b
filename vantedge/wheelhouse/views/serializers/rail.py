from rest_framework import serializers

from vantedge.invitation.models.utils import (
    get_contract_default_parties_by_partnership,
    get_contract_default_facilities_by_partnership
)

from ...models.rail import RailShipment, Railroad, RailShipmentNote, BOLArchive
from ...models.railcar import LoadedRailCar
from ...models.location import Facility, FacilityPartnership
from ...models.product import Product
from ...models.company import Party, PartyPartnership
from ...models.validations import validate_route
from ..serializers.utility import ModelNameIdSerializer

class RailShipmentNoteSerializer(serializers.ModelSerializer):
    class Meta:
        model = RailShipmentNote
        read_only_fields = ["id", "modified", "created", "is_write_protect"]
        fields = "__all__"

    def validate(self, attrs):
        user = self.context["request"].user
        for attr in attrs:
            if (
                attr == "rail_shipment"
                and attrs[attr].origin.company.company != user.company
            ):
                raise serializers.ValidationError("Permisson mismatch!")

        return super().validate(attrs)


class BOLArchiveSerializer(serializers.ModelSerializer):
    class Meta:
        model = BOLArchive
        fields = ["id", "created"]

class RailShipmentSerializer(serializers.ModelSerializer):
    car_count = serializers.SerializerMethodField()
    volume = serializers.SerializerMethodField()
    is_readonly = serializers.SerializerMethodField()
    notes = RailShipmentNoteSerializer(many=True, read_only=True)
    contract = serializers.SerializerMethodField()
    bol_archives = BOLArchiveSerializer(many=True, read_only=True)
    is_master_billing = serializers.SerializerMethodField()
    is_for_empty_shipment = serializers.SerializerMethodField()

    class Meta:
        model = RailShipment
        read_only_fields = ["data", "status", "schedule", "arrive_date", "master_billing"]
        fields = "__all__"

    def get_is_readonly(self, obj):
        return obj.is_readonly

    def get_car_count(self, obj):
        return obj.cars.count()

    def get_volume(self, obj):
        return obj.volume

    def get_contract(self, obj):
        return obj.contract.id if obj.contract else None

    def get_is_master_billing(self, obj):
        return obj.is_master_billing()

    def get_is_for_empty_shipment(self, obj):
        return obj.schedule and obj.schedule.is_for_empty_shipment

    def get_partner_parties(self, instance):
        partnership_parties = []
        default_parties = {}
        contract = self.instance.contract
        if hasattr(contract, "order"):
            order = contract.order
            buyer_company = order.get_buyer()
            partnership_parties = PartyPartnership.get_party_partnerships(
                owner=buyer_company, partner=instance.origin.company.company)

            default_parties = get_contract_default_parties_by_partnership(
                instance.origin.company.company, buyer_company)

            return partnership_parties.values_list("party", flat=True), default_parties

        return partnership_parties, default_parties

    def get_partner_facilites(self, instance):
        partnership_facilities = []
        default_facilities = {}
        contract = self.instance.contract
        if hasattr(contract, "order"):
            order = contract.order
            buyer_company = order.get_buyer()

            partnership_facilities = FacilityPartnership.get_facility_partnerships(
                owner=buyer_company, partner=instance.origin.company.company)

            default_facilities = get_contract_default_facilities_by_partnership(
                instance.origin.company.company, buyer_company)

            return partnership_facilities.values_list("facility", flat=True), default_facilities

        return partnership_facilities, default_facilities

    def validate(self, attrs):
        partnership_parties, default_parties = [], {}
        partnership_facilities, default_facilites = [], {}

        user = self.context["request"].user

        if self.instance and self.instance.master_billing:
            raise serializers.ValidationError("Billing could only change from master billing")

        if self.instance and self.instance.arrive_date:
            raise serializers.ValidationError("Billing can not change after one of the cars arrival")

        if self.instance:
            partnership_parties, default_parties = self.get_partner_parties(self.instance)
            partnership_facilities, default_facilites = self.get_partner_facilites(self.instance)

        for attr in attrs:
            if hasattr(attrs[attr], "company"):
                if attrs[attr].company.company != user.company:
                    if issubclass(attrs[attr].__class__, Party):
                        if (attr in default_parties or attrs[attr].id not in partnership_parties):
                            raise serializers.ValidationError(f"Permisson mismatch, '{attr}' is not accepted!")

                    elif issubclass(attrs[attr].__class__, Facility):
                        if (attr in default_facilites or attrs[attrs].id not in partnership_facilities):
                            raise serializers.ValidationError(f"Permisson mismatch, '{attr}' is not accepted!")
                    else:
                        raise serializers.ValidationError("Permisson mismatch!")

            if attr == "routing":
                route_is_valid, route_msg = validate_route(attrs[attr])
                if route_is_valid:
                    attrs[attr] = route_msg
                else:
                    raise serializers.ValidationError(route_msg)

            if self.instance:
                if "correction" in self.instance.status:
                    if attr == "reference_number":
                        if attrs[attr] != getattr(self.instance, attr):
                            raise serializers.ValidationError(
                                "Can't change reference number"
                            )

                    if attr == "ship_date":
                        if attrs[attr] != getattr(self.instance, attr):
                            raise serializers.ValidationError("Can't change ship date")

        if self.instance and self.instance.is_readonly:
            if self.instance.status != "master-billing":
                raise serializers.ValidationError("Shipment is in readonly mode")

        return attrs

class RailShipmentListSerializer(serializers.ModelSerializer):
    railroad = ModelNameIdSerializer(Railroad)(read_only=True)
    consignee = ModelNameIdSerializer(Party)(read_only=True)
    shipper = ModelNameIdSerializer(Party)(read_only=True)
    freight_payer = ModelNameIdSerializer(Party)(read_only=True)
    custom_broker = ModelNameIdSerializer(Party)(read_only=True)
    notify_party = ModelNameIdSerializer(Party)(read_only=True)
    care_party = ModelNameIdSerializer(Party)(read_only=True)
    pickup_party = ModelNameIdSerializer(Party)(read_only=True)
    ultimate_consignee = ModelNameIdSerializer(Party)(read_only=True)
    custom_broker_us = ModelNameIdSerializer(Party)(read_only=True)
    importer = ModelNameIdSerializer(Party)(read_only=True)
    exporter = ModelNameIdSerializer(Party)(read_only=True)
    origin = ModelNameIdSerializer(Facility)(read_only=True)
    destination = ModelNameIdSerializer(Facility)(read_only=True)
    product = ModelNameIdSerializer(Product)(read_only=True)
    car_count = serializers.SerializerMethodField()
    is_master_billing = serializers.SerializerMethodField()

    class Meta:
        model = RailShipment
        exclude = ()

    def get_car_count(self, obj):
        return obj.cars.all().count()

    def get_is_master_billing(self, obj):
        return obj.is_master_billing()

class EntityCharField(serializers.CharField):
    def get_attribute(self, instance):
        entity_name, field = self.source_attrs
        if entity := getattr(instance, entity_name):
            return getattr(entity, field)
        return None


class RailShipmentListSerializerShortened(serializers.ModelSerializer):
    railroad = EntityCharField(source="railroad.name")
    consignee = EntityCharField(source="consignee.name")
    shipper = EntityCharField(source="shipper.name")
    freight_payer = EntityCharField(source="freight_payer.name")
    custom_broker = EntityCharField(source="custom_broker.name")
    notify_party = EntityCharField(source="notify_party.name")
    care_party = EntityCharField(source="care_party.name")
    pickup_party = EntityCharField(source="pickup_party.name")
    ultimate_consignee = EntityCharField(source="ultimate_consignee.name")
    custom_broker_us = EntityCharField(source="custom_broker_us.name")
    importer = EntityCharField(source="importer.name")
    exporter = EntityCharField(source="exporter.name")
    origin = EntityCharField(source="origin.name")
    origin_id = serializers.IntegerField(source="origin.id")
    destination = EntityCharField(source="destination.name")
    product = EntityCharField(source="product.name")
    car_count = serializers.SerializerMethodField()
    is_master_billing = serializers.SerializerMethodField()

    class Meta:
        model = RailShipment
        exclude = ()

    def get_car_count(self, obj):
        return obj.cars.all().count()

    def get_is_master_billing(self, obj):
        return obj.is_master_billing()

class RailCarBOLSerializer(serializers.ModelSerializer):
    bol_number = serializers.SerializerMethodField()
    order_number = serializers.SerializerMethodField()
    ship_date = serializers.SerializerMethodField()
    buyer_number = serializers.SerializerMethodField()
    seller_number = serializers.SerializerMethodField()
    buyer = serializers.SerializerMethodField()
    seller = serializers.SerializerMethodField()
    origin = serializers.SerializerMethodField()

    class Meta:
        model = LoadedRailCar
        exclude = ()

    def get_bol_number(self, obj):
        return obj.rail_shipment.id

    def get_order_number(self, obj):
        return obj.rail_shipment.id

    def get_ship_date(self, obj):
        return obj.rail_shipment.ship_date

    def get_buyer_number(self, obj):
        return obj.rail_shipment.route.contract.buyer_number

    def get_seller_number(self, obj):
        return obj.rail_shipment.route.contract.seller_number

    def get_buyer(self, obj):
        return obj.rail_shipment.route.contract.buyer.name

    def get_seller(self, obj):
        return obj.rail_shipment.route.contract.seller.name

    def get_origin(self, obj):
        return obj.rail_shipment.route.origin.name


class RailroadSerializer(serializers.ModelSerializer):
    class Meta:
        model = Railroad
        fields = "__all__"
