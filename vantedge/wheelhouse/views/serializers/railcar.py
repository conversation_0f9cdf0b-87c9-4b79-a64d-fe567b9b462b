from rest_framework import serializers
from django.db.models import Integer<PERSON>ield
from django.db.models.functions import Cast

from ...models.railcar import LoadedRailCar, TankCar, HopperCar
from ...models.location import Facility
from ...models.product import Product

from ..serializers.utility import ModelNameIdSerializer


class LoadedRailCarListSerializer(serializers.ModelSerializer):
    product = ModelNameIdSerializer(Product)(read_only=True)
    current_location = ModelNameIdSerializer(Facility)(read_only=True)
    reference_number = serializers.SerializerMethodField()
    consignee = serializers.SerializerMethodField()
    destination = serializers.SerializerMethodField()
    weight = serializers.SerializerMethodField()
    weight_unrounded = serializers.SerializerMethodField()
    seal_number_1 = serializers.SerializerMethodField()
    seal_number_2 = serializers.SerializerMethodField()
    seal_number_3 = serializers.SerializerMethodField()
    seal_number_4 = serializers.SerializerMethodField()
    seal_numbers = serializers.SerializerMethodField()
    is_readonly = serializers.SerializerMethodField()
    arrive_date = serializers.SerializerMethodField()
    invoices = serializers.SerializerMethodField()
    custom_invoices = serializers.SerializerMethodField()
    car_type = serializers.SerializerMethodField()
    gross_volume = serializers.SerializerMethodField()
    net_volume = serializers.SerializerMethodField()
    gal_to_liter_conversion_factor = serializers.SerializerMethodField()
    temperature_correction_factor_at_15c = serializers.SerializerMethodField()

    def get_custom_invoices(self, obj):
        return obj.custom_invoices_car.values("id", "status", "date_of_invoice")

    def get_invoices(self, obj):
        return obj.loadedrailcar_ptr.invoice_items.values(
            "invoice", "invoice__invoice_number"
        )

    def get_gross_volume(self, obj):
        return obj.gross_volume

    def get_net_volume(self, obj):
        return obj.net_volume

    def get_gal_to_liter_conversion_factor(self, obj):
        return getattr(obj, "gal_to_liter_conversion_factor", None)

    def get_temperature_correction_factor_at_15c(self, obj):
        return getattr(obj, "temperature_correction_factor_at_15c", None)

    def get_reference_number(self, obj):
        return obj.rail_shipment.reference_number if obj.rail_shipment else None

    def get_consignee(self, obj):
        return obj.rail_shipment.consignee.name if obj.rail_shipment else None

    def get_destination(self, obj):
        return obj.rail_shipment.destination.name if obj.rail_shipment else None

    def get_arrive_date(self, obj):
        return obj.arrive_date

    def get_is_readonly(self, obj):
        return obj.is_readonly

    def get_car_type(self, obj):
        return obj.car_type

    def get_weight(self, obj):
        return getattr(obj, "weight", None)

    def get_weight_unrounded(self, obj):
        return getattr(obj, "weight_unrounded", None)

    def get_seal_number_1(self, obj):
        return getattr(obj, "seal_number_1", None)

    def get_seal_number_2(self, obj):
        return getattr(obj, "seal_number_2", None)

    def get_seal_number_3(self, obj):
        return getattr(obj, "seal_number_3", None)

    def get_seal_number_4(self, obj):
        return getattr(obj, "seal_number_4", None)

    def get_seal_numbers(self, obj):
        return getattr(obj, "seal_numbers", None)

    class Meta:
        model = LoadedRailCar
        exclude = ()


class LoadedRailCarSerializer(serializers.ModelSerializer):
    product = serializers.PrimaryKeyRelatedField(queryset=Product.objects.all())
    is_readonly = serializers.SerializerMethodField()
    arrive_date = serializers.SerializerMethodField()
    weight = serializers.SerializerMethodField()
    volume = serializers.SerializerMethodField()
    car_type = serializers.SerializerMethodField()
    rail_shipment_status = serializers.SerializerMethodField()
    rail_shipment_master_billing = serializers.SerializerMethodField()

    def get_rail_shipment_status(self, obj):
        return obj.rail_shipment.status if obj.rail_shipment else None

    def get_rail_shipment_master_billing(self, obj):
        return obj.rail_shipment.master_billing.id if (
            obj.rail_shipment and
            obj.rail_shipment.master_billing) else None

    def get_is_readonly(self, obj):
        return obj.is_readonly

    def get_arrive_date(self, obj):
        return obj.arrive_date

    def get_weight(self, obj):
        return obj.weight

    def get_volume(self, obj):
        return getattr(obj, "volume", None)

    def get_car_type(self, obj):
        return obj.car_type

    def validate(self, attrs):
        user = self.context["request"].user
        # force attr set in gaugetools to force update all records during overwrite
        force = self.context["request"].data.get("force")

        if self.instance:
            facility = self.instance.current_location
        else:
            facility = attrs.get("current_location")

        if self.instance and self.instance.is_readonly:
            raise serializers.ValidationError("Car is in readonly mode")

        # prevent from changing the car facility
        if self.instance:
            attrs.pop("current_location", None)

        for attr in attrs:
            if hasattr(attrs[attr], "company"):
                if attrs[attr].company.company != user.company:
                    raise serializers.ValidationError("Permisson mismatch!")

            if attr == "rail_shipment" and attrs[attr]:
                if attrs[attr].origin.company.company != user.company:
                    raise serializers.ValidationError("Permisson mismatch!")

                if "correction" in attrs[attr].status and self.instance:
                    if self.instance.rail_shipment != attrs[attr]:
                        raise serializers.ValidationError(
                            "Can't add car during correction"
                        )

                if attrs[attr].master_billing:
                    if attrs[attr].cars.exclude(id=self.instance.id).exists():
                        raise serializers.ValidationError("Only one car can be added to sub billing")

            if (attr == "rail_shipment" and
                self.instance and
                self.instance.rail_shipment and
                attrs.get("rail_shipment") != self.instance.rail_shipment):
                if (self.instance.rail_shipment.status != "open" and
                    self.instance.rail_shipment.master_billing):
                    raise serializers.ValidationError("Can not hold the car in non open sub billing")

                if self.instance.rail_shipment.is_master_billing():
                    raise serializers.ValidationError("Can not attach car to master billing")

            if (attr == "rail_shipment"):
                if self.instance and self.instance.rail_shipment and not attrs.get("rail_shipment"):
                    self.instance.status = LoadedRailCar.LoadedRailCarStatus.READY_TO_SHIP

            if (
                self.instance
                and self.instance.car_type == "tank car"
                and self.instance.temperature_correction_factor_at_15c != 1
            ):
                if attr == "load_temperature":
                    if attrs[attr] != getattr(self.instance, attr) and not force:
                        raise serializers.ValidationError(
                            "You can't change the loading temperature on this car"
                        )

            if (
                self.instance
                and self.instance.rail_shipment
                and "correction" in self.instance.rail_shipment.status
            ):
                if attr == "car_mark":
                    if attrs[attr] != getattr(self.instance, attr):
                        raise serializers.ValidationError("Can't change car mark")

                if attr == "car_number":
                    if attrs[attr] != getattr(self.instance, attr):
                        raise serializers.ValidationError("Can't change car number")

                if attr == "load_date":
                    if attrs[attr] != getattr(self.instance, attr):
                        raise serializers.ValidationError("Can't change load date")

                if attr == "rail_shipment":
                    if attrs[attr] != getattr(self.instance, attr):
                        if self.instance.rail_shipment.railroad.name == 'CPR':
                            # CP said they accept removing car during correction
                            if self.instance.rail_shipment.cars.count() == 1:
                                raise serializers.ValidationError("Can't remove the only car from shipment")

                        else:
                            raise serializers.ValidationError(
                                "Can't remove the car during correction"
                            )

        if car_mark := attrs.get("car_mark"):
            attrs["car_mark"] = car_mark.replace(" ", "").upper()

            if not car_mark.isalpha():
                raise serializers.ValidationError("Car Mark is not valid")

        if car_number := attrs.get("car_number"):
            try:
                int(car_number)
            except ValueError:
                raise serializers.ValidationError("Car Number is not valid")

        if not self.instance:

            if facility.rail_car_initial_status != LoadedRailCar.LoadedRailCarStatus.READY_TO_LOAD:
                raise serializers.ValidationError("Inspection plan is in place")

            if LoadedRailCar.objects.annotate(
                    car_int=Cast("car_number", IntegerField())
                ).filter(
                    rail_shipment__isnull=True,
                    current_location=facility,
                    car_mark=car_mark,
                    car_int=int(car_number)
                ).exists():
                raise serializers.ValidationError("Car already in facility")

            # initial status
            attrs["status"] = facility.rail_car_initial_status
            status = attrs["status"]
        else:
            status = self.instance.status

        if attrs.get("product"):
            if self.instance and not self.instance.product and status != LoadedRailCar.LoadedRailCarStatus.READY_TO_LOAD:
                raise serializers.ValidationError(f"You can't load car with status '{self.instance.status}'")

            if status == LoadedRailCar.LoadedRailCarStatus.READY_TO_LOAD:
                attrs["status"] = facility.rail_car_status_when_loaded

        # checking volume value is correct
        volume = attrs.get("volume")
        if volume is not None:
            shell_capacity = attrs.get("shell_capacity")
            shell_capacity = shell_capacity if shell_capacity is not None else self.instance.shell_capacity

            volume_outage = attrs.get("volume_outage")
            volume_outage = volume_outage if volume_outage is not None else self.instance.volume_outage

            temperature_correction_factor = attrs.get("temperature_correction_factor")
            temperature_correction_factor = (temperature_correction_factor
                                             if temperature_correction_factor is not None
                                             else self.instance.temperature_correction_factor)

            temperature_correction_factor_at_15c = attrs.get("temperature_correction_factor_at_15c")
            temperature_correction_factor_at_15c = (temperature_correction_factor_at_15c
                                                    if temperature_correction_factor_at_15c is not None
                                                    else self.instance.temperature_correction_factor_at_15c)

            calculated_volume_net = ((shell_capacity - volume_outage) * temperature_correction_factor /
                temperature_correction_factor_at_15c)
            if abs(calculated_volume_net - volume) > 1:
                raise serializers.ValidationError(f"volume of {volume} is not matching the calculation {calculated_volume_net}")
            print(volume, calculated_volume_net)
        return attrs

    class Meta:
        model = LoadedRailCar
        read_only_fields = [
            "id", "status", "modified", "created", "polymorphic_ctype"
        ]
        exclude = ()


class TankCarSerializer(LoadedRailCarSerializer):
    volume = serializers.FloatField()
    seal_number_1 = serializers.CharField()

    class Meta:
        model = TankCar
        exclude = ()


class HopperCarSerializer(LoadedRailCarSerializer):
    weight = serializers.FloatField()

    class Meta:
        model = HopperCar
        exclude = ()


CAR_TYPE_SERIALIZER = {"tank car": TankCarSerializer, "hopper car": HopperCarSerializer}
