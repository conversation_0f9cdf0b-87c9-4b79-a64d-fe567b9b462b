from rest_framework import serializers
import re


class DynamicFieldsMixin(object):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.dynamic_include = []
        self.dynamic_exclude = []

    def set_fields(self, include=[], exclude=[]):
        self.dynamic_include = include
        self.dynamic_exclude = exclude
        return self

    def get_fields(self):
        fields = super().get_fields()
        remove_fields = (set(fields) - set(self.dynamic_include)).intersection(
            set(self.dynamic_exclude)
        )
        for field_name in remove_fields:
            self.fields.pop(field_name)
        return fields


class ModelQuickSerializerBase(serializers.ModelSerializer):
    id = serializers.SerializerMethodField()
    value = serializers.SerializerMethodField()
    label = serializers.SerializerMethodField()
    name = serializers.SerializerMethodField()
    address = serializers.SerializerMethodField()

    class Meta:
        pass

    def get_id(self, instance):
        return instance.id

    def get_value(self, instance):
        return self.get_id(instance)

    def get_label(self, instance):
        return str(instance)

    def get_code(self, instance):
        try:
            result = instance.code
            return result
        except NameError:
            return ""

    def get_name(self, instance):
        try:
            result = instance.name
            return result
        except NameError:
            return ""

    def get_address(self, instance):
        if hasattr(instance.data, "address"):
            return instance.data.address
        else:
            return None

    def __init__(self, *args, dyn_fields=[], **kwargs):
        for field in dyn_fields:
            self.fields[re.sub("__", "", field)] = serializers.CharField(
                read_only=True, source=re.sub("__", ".", field)
            )

        super(ModelQuickSerializerBase, self).__init__(*args, **kwargs)


def ModelPickerSerializer(model):
    return ModelQuickSerializer(model, "Picker", ["value", "label"])


def ModelPartyPickerSerializer(model):
    return ModelQuickSerializer(model, "Picker", ["value", "label", "party_types", "address", "company"])


def ModelNameIdSerializer(model):
    return ModelQuickSerializer(model, "NameId", ["id", "name"])


def ModelCodeIdSerializer(model):
    return ModelQuickSerializer(model, "CodeId", ["id", "code"])


def ModelFieldSerializer(model, fields):
    return ModelQuickSerializer(model, "Fields", fields)


def ModelQuickSerializer(model, name, fields=["value", "label"]):
    result = type(model.__class__.__name__ + name, (ModelQuickSerializerBase,), {})
    result.Meta = type("", (), {})
    result.Meta.model = model
    result.Meta.fields = fields
    result.Meta.read_only_fields = fields
    return result
