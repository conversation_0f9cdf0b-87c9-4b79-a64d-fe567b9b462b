# Generated by Django 3.2.7 on 2023-02-27 04:01

import django.contrib.postgres.fields
from django.db import migrations, models
import django.db.models.deletion
import functools
import schema_field.fields
import vantedge.workflow.models.constraint


class Migration(migrations.Migration):

    initial = True

    dependencies = [("users", "0010_populate_app_config_available_groups")]

    operations = [
        migrations.CreateModel(
            name="Workflow",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=100)),
                (
                    "constraint",
                    schema_field.fields.JSONSchemedField(
                        default=vantedge.workflow.models.constraint.ConstraintData,
                        encoder=functools.partial(
                            schema_field.fields.JSONSchemedEncoder,
                            *(),
                            **{
                                "schema": (
                                    vantedge.workflow.models.constraint.ConstraintData,
                                )
                            }
                        ),
                        schema=(vantedge.workflow.models.constraint.ConstraintData,),
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="WorkflowState",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=100)),
                ("end", models.BooleanField(default=False)),
            ],
        ),
        migrations.CreateModel(
            name="WorkflowCompany",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "company",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="workflow_company",
                        to="users.company",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="WorkflowAction",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=70)),
                ("code", models.CharField(max_length=70)),
                ("kwargs", models.JSONField(blank=True, default=dict)),
                (
                    "args",
                    django.contrib.postgres.fields.ArrayField(
                        base_field=models.CharField(max_length=30),
                        default=list,
                        size=20,
                    ),
                ),
                (
                    "constraint",
                    schema_field.fields.JSONSchemedField(
                        default=vantedge.workflow.models.constraint.ConstraintData,
                        encoder=functools.partial(
                            schema_field.fields.JSONSchemedEncoder,
                            *(),
                            **{
                                "schema": (
                                    vantedge.workflow.models.constraint.ConstraintData,
                                )
                            }
                        ),
                        schema=(vantedge.workflow.models.constraint.ConstraintData,),
                    ),
                ),
                (
                    "action_type",
                    models.CharField(
                        choices=[("RUN", "Run"), ("CHECK", "Check")],
                        default="RUN",
                        max_length=20,
                    ),
                ),
                ("is_reversible", models.BooleanField(default=False)),
                (
                    "from_state",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="to_action_set",
                        to="workflow.workflowstate",
                    ),
                ),
                (
                    "to_state",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="from_action_set",
                        to="workflow.workflowstate",
                    ),
                ),
                (
                    "workflow",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="actions",
                        to="workflow.workflow",
                    ),
                ),
            ],
        ),
        migrations.AddField(
            model_name="workflow",
            name="company",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                to="workflow.workflowcompany",
            ),
        ),
    ]
