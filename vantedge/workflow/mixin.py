from django.shortcuts import get_object_or_404

from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework import status

from vantedge.workflow.models.constraint import ConstraintException
from vantedge.workflow.models import WorkflowAction
from vantedge.workflow.serializers import WorkflowActionSerializer


class WorkflowViewMixin:
    @action(methods=["POST"], detail=True)
    def get_actions(self, request, pk):
        context = request.data.get("context", {})
        context["user"] = request.user
        context["company"] = None if request.user.is_anonymous else request.user.company
        context["role"] = [] if request.user.is_anonymous else request.user.roles
        obj = self.get_object()
        all_actions = obj.get_all_actions()
        # next_actions = obj.get_next_actions(context)
        all_actions_data = WorkflowActionSerializer(all_actions, many=True).data
        # next_actions_data = WorkflowActionSerializer(next_actions, many=True).data

        started = False

        if obj.workflow_completed():
            for action_data in all_actions_data:
                action_data["state"] = "past"

        elif obj.state is None:
            for action_data in all_actions_data:
                if action_data["from_state"] is None:
                    action_data["state"] = "pending"
                else:
                    action_data["state"] = "future"

        else:
            for action_data in all_actions_data:
                action_data["state"] = "future"
                if action_data["from_state"] == obj.state.id:
                    action_data["state"] = "pending"
                    started = True

            if started:
                for action_data in all_actions_data:
                    if action_data["state"] == "pending":
                        break
                    action_data["state"] = "past"
                    if (
                        action_data["is_reversible"]
                        and action_data["to_state"] == obj.state.id
                    ):
                        action_data["reverse_allowed"] = True

        return Response(all_actions_data)

    @action(methods=["POST"], detail=True)
    def run_action(self, request, pk):
        context = request.data
        action_id = request.data.get("action", "")
        reverse = request.data.get("reverse", False)
        serializer_class = self.get_serializer_class()

        if not action_id or not str(action_id).isdigit():
            return Response("Action missing", status=status.HTTP_400_BAD_REQUEST)

        context["user"] = request.user
        context["URL"] = request.get_full_path()
        context["HTTP_HOST"] = request.META["HTTP_HOST"]
        context["SCHEMA"] = request.scheme

        obj = self.get_object()
        workflow = obj.workflow

        workflow_action = get_object_or_404(
            WorkflowAction, pk=action_id, workflow=workflow
        )

        if reverse and not workflow_action.is_reversible:
            return Response("Action not reversible", status=status.HTTP_400_BAD_REQUEST)

        try:
            result = obj.execute_action(workflow_action, context)
        except ConstraintException as e:
            return Response(e.items, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            return Response(str(e), status=status.HTTP_400_BAD_REQUEST)

        response = serializer_class(result, many=False).data

        return Response(
            response,
            status=status.HTTP_200_OK if result else status.HTTP_400_BAD_REQUEST,
        )
