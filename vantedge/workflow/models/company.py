from django.contrib.auth.models import Permission
from django.db import models
from django.db.models.signals import m2m_changed

from vantedge.util.mixins import EasyAuditModelConfigMixin
from vantedge.users.models import Company
from vantedge.users.models.application_configuration import company_permissions_handler

class WorkflowCompany(EasyAuditModelConfigMixin, models.Model):
    company = models.OneToOneField(
        Company, related_name="workflow_company", on_delete=models.CASCADE
    )

    available_permissions = models.ManyToManyField(
        Permission, blank=True, limit_choices_to={"content_type__app_label": "workflow"}
    )

    def __str__(self) -> str:
        return f"{self.company}"


m2m_changed.connect(
    company_permissions_handler, sender=WorkflowCompany.available_permissions.through
)

