from django.db import models

from vantedge.util.mixins import EasyAuditModelConfigMixin
from vantedge.workflow.models import Workflow, WorkflowState
from vantedge.workflow.models.constraint import ConstraintException


class WorkflowMixin(EasyAuditModelConfigMixin, models.Model):
    workflow = models.ForeignKey(
        Workflow, null=True, blank=True, on_delete=models.SET_NULL
    )
    state = models.ForeignKey(
        WorkflowState, null=True, blank=True, on_delete=models.CASCADE
    )

    class Meta:
        abstract = True

    @staticmethod
    def get_available_workflow(context, raise_exception=False):
        workflows = Workflow.objects.none()

        location_id = context.get("location", None)
        if location_id:
            from vantedge.wheelhouse.models import Location

            location = Location.objects.filter(pk=location_id).first()
            if location:
                workflows = location.workflows.all()

        for workflow in workflows:
            success, error = workflow.check_constraint(context, raise_exception)
            if success:
                return workflow

    def update_context(self, context):
        return context

    def is_ok_static_constraint(self, context):
        """return (is_ok, error_message)"""
        return True, None

    def get_all_actions(self):
        if self.workflow:
            return self.workflow.ordered_actions()
        return None

    def get_available_actions(self, context):
        context = self.update_context(context)
        available_actions = []
        context = self.get_context(context)
        for action in self.get_all_actions():
            success, error = action.check_constraint(context)
            if success:
                available_actions.append(action)
        return available_actions

    def get_next_actions(self, context):
        context = self.update_context(context)
        next_actions = []
        if self.workflow:
            actions = self.workflow.actions.filter(from_state=self.state)
            for action in actions:
                success, error = action.check_constraint(context)
                if success:
                    next_actions.append(action)
        return next_actions

    def execute_action(self, action, context):
        context = self.update_context(context)
        context["action_name"] = action.name
        context["action_code"] = action.code
        context["action_kwargs"] = action.kwargs
        context["action_args"] = action.args

        reverse = context.get("reverse", False)

        is_ok, error = self.is_ok_static_constraint(context)
        if not is_ok:
            raise ConstraintException({"error": error})

        action.check_constraint(context, raise_exception=True)

        if reverse:
            if self.state != action.to_state:
                raise ConstraintException(
                    {"error": "Workflow state does not match for reverse"}
                )

        elif self.state != action.from_state:
            raise ConstraintException({"error": "Workflow state does not match"})

        code = action.code
        result = getattr(self, code)(context)
        # if result action is not None then it is accepted

        if result is not None:
            if reverse:
                self.state = action.from_state
            else:
                self.state = action.to_state

            self.save(update_fields=["state"])
            self.send_notification(action, context)

            return result

        return None

    def workflow_completed(self):
        return self.state.end if self.state else None

    def send_notification(self, action, context):
        from vantedge.notification.templates import send_note

        reverse = context.get("reverse", False)
        message = getattr(
            action.notification_message, "reverse" if reverse else "forward"
        )
        if not message:
            return

        roles = message.roles.copy()
        roles.extend(context.get("notification_roles", []))

        notification_context = context.get("notification_context", {})
        notification_additional_user_ids = context.get(
            "notification_additional_user_ids", []
        )
        notification_additional_user_ids.extend(message.additional_user_ids)

        send_note(
            location=context.get("notification_location", None),
            company=context.get("notification_company", None),
            roles=roles,
            users=context.get("notification_users", []),
            sender=context.get("user", None),
            title=self.populate_notification_text(message.title, notification_context),
            message=self.populate_notification_text(
                message.content, notification_context
            ),
            sms=message.sms,
            email=message.email,
            additional_user_ids=context.get("notification_additional_user_ids", []),
        )

    def populate_notification_text(self, text, context):
        return text.format(**context)
