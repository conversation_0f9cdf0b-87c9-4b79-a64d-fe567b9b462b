from django.db import models

from django.contrib.postgres.fields import <PERSON><PERSON><PERSON><PERSON><PERSON>
from schema_field.fields import <PERSON><PERSON><PERSON><PERSON>med<PERSON>ield

from vantedge.util.mixins import EasyAuditModelConfigMixin
from .company import WorkflowCompany
from .constraint import ConstraintData, ConstraintException
from .schema import NotificationMessage


class Workflow(EasyAuditModelConfigMixin, models.Model):
    company = models.ForeignKey(WorkflowCompany, on_delete=models.CASCADE)
    name = models.CharField(max_length=100, blank=False, null=False)
    constraint = JSONSchemedField(schema=ConstraintData, default=ConstraintData)

    def __str__(self) -> str:
        return f"{self.name}"

    def check_constraint(self, context, raise_exception=False):
        success, errors = self.constraint.check(context)
        if not success and raise_exception:
            raise ConstraintException(errors)

        return success, errors

    def ordered_actions(self):
        ordered_actions = []
        action = self.actions.filter(from_state=None)  # expecting just one
        while action:
            ordered_actions.extend(action)
            action = self.actions.filter(
                from_state__id__in=action.values_list("to_state__id", flat=True)
            )

        return ordered_actions


class WorkflowState(EasyAuditModelConfigMixin, models.Model):
    name = models.CharField(max_length=100, blank=False, null=False)
    end = models.BooleanField(default=False)

    def __str__(self) -> str:
        return f"{self.name}"


class WorkflowAction(EasyAuditModelConfigMixin, models.Model):
    ACTION_TYPES = (("RUN", "Run"), ("CHECK", "Check"))

    workflow = models.ForeignKey(
        Workflow, on_delete=models.CASCADE, related_name="actions"
    )
    name = models.CharField(max_length=70, blank=False, null=False)
    code = models.CharField(max_length=70, blank=False, null=False)
    kwargs = models.JSONField(default=dict, blank=True)
    args = ArrayField(
        models.CharField(max_length=30, blank=False), size=20, default=list
    )

    from_state = models.ForeignKey(
        WorkflowState,
        on_delete=models.SET_NULL,
        related_name="to_action_set",
        null=True,
        blank=True,
    )
    to_state = models.ForeignKey(
        WorkflowState,
        on_delete=models.CASCADE,
        blank=False,
        null=False,
        related_name="from_action_set",
    )

    constraint = JSONSchemedField(schema=ConstraintData, default=ConstraintData)
    action_type = models.CharField(max_length=20, choices=ACTION_TYPES, default="RUN")
    is_reversible = models.BooleanField(default=False)
    notification_message = JSONSchemedField(
        schema=NotificationMessage, default=NotificationMessage
    )

    # TODO: constraint one workflow one from_state as None

    def __str__(self) -> str:
        return f"{self.name}"

    def check_constraint(self, context, raise_exception=False):
        success, errors = self.constraint.check(context)
        if not success and raise_exception:
            raise ConstraintException(errors)

        return success, errors
