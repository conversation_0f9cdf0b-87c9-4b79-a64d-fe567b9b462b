from datetime import timedelta
import logging

from django.core.management.base import BaseCommand
from django.utils import timezone

from vantedge.yard_management.views.filters import get_incoming_cars
from vantedge.yard_management.models import RailYard
from vantedge.wheelhouse.models import TankCar, HopperCar

logger = logging.getLogger(__name__)

def do_add_incoming_cars(now, expectation_of_arrival_datetime):
    logger.info(f"Fetching cars. Arrival time: {expectation_of_arrival_datetime}")
    for yard in RailYard.objects.all():
        name = yard.facility.name
        logger.info(f"{name}: Start")
        if not yard.auto_add_incoming_cars:
            logger.info(f"{name}: SKIP: Auto Add disabled")
            continue
        car_type = yard.auto_incoming_car_type
        if car_type == "TANK":
            CarClass = TankCar
        elif car_type == "HOPPER":
            CarClass = HopperCar
        else:
            logger.info(f"{name}: SKIP: Unknown car type")
            continue
        logger.info(f"{name}: Fetching")
        trips = get_incoming_cars(yard.facility, now, expectation_of_arrival_datetime)
        logger.info(f"{name}: Adding cars: {len(trips)}")
        for trip in trips:
            logger.info(
                "%s: creating car: %s eta_timestamp=%s original_eta_timestamp=%s",
                name, trip, trip.eta_timestamp, trip.original_eta_timestamp
            )
            car = CarClass.objects.create(
                current_location=yard.facility,
                car_mark=trip.car.mark,
                car_number=trip.car.number,
                incoming_trip=trip,
                status=yard.facility.rail_car_initial_status,
            )
            yard.add_unassigned_car(car)
        logger.info(f"{name}: End")
    logger.info("Auto Import Complete")

class Command(BaseCommand):
    help = "Fetch incoming cars for a facility and add them to the yard"

    def add_arguments(self, parser):
        parser.add_argument('--hours', nargs=1, type=int)

    def handle(self, *args, **options):
        hours = options['hours'][0]

        now = timezone.now()
        expectation_of_arrival_datetime = now + timedelta(hours=hours)
        do_add_incoming_cars(now, expectation_of_arrival_datetime)
