# Generated by Django 3.2.7 on 2022-02-22 15:41

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ("feature", "0001_initial"),
        ("auth", "0012_alter_user_first_name_max_length"),
        ("yard_management", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="YardManagementFeature",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=100)),
                ("code", models.CharField(max_length=100, unique=True)),
                (
                    "feature",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="yard_management_feature",
                        to="feature.feature",
                    ),
                ),
                (
                    "permissions",
                    models.ManyToManyField(
                        blank=True,
                        limit_choices_to={"content_type__model": "feature"},
                        to="auth.Permission",
                    ),
                ),
            ],
            options={"abstract": False,},
        ),
        migrations.CreateModel(
            name="YardManagementFeatureSubscription",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("renewal_date", models.DateTimeField()),
                ("expiry_date", models.DateTimeField()),
                (
                    "is_active",
                    models.BooleanField(
                        default=True,
                        help_text="Toggle to enable/disable the feature subscription",
                    ),
                ),
                (
                    "check_user_permissions",
                    models.BooleanField(
                        default=True,
                        help_text="Include user permissions in calculation of feature access",
                    ),
                ),
                (
                    "company",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="subscriptions",
                        to="yard_management.yardmanagementcompany",
                    ),
                ),
                (
                    "feature",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="subscribers",
                        to="yard_management.yardmanagementfeature",
                    ),
                ),
            ],
        ),
        migrations.AddConstraint(
            model_name="yardmanagementfeaturesubscription",
            constraint=models.UniqueConstraint(
                fields=("feature", "company"), name="unique_yardmanagement_feature"
            ),
        ),
    ]
