# Generated by Django 4.2 on 2025-01-10 17:43

from django.db import migrations, models
import django.db.models.deletion


def remove_carpositions_with_no_scenario(apps, schema_editor):
    CarPosition = apps.get_model("yard_management", "CarPosition")
    db_alias = schema_editor.connection.alias
    CarPosition.objects.using(db_alias).filter(scenario__isnull=True).delete()

class Migration(migrations.Migration):

    dependencies = [
        (
            "yard_management",
            "0008_railcarinspectionplan_yard_management_badge_and_more",
        ),
    ]

    operations = [
        migrations.RunPython(remove_carpositions_with_no_scenario),
        migrations.AlterField(
            model_name="carposition",
            name="scenario",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                to="yard_management.scenario",
            ),
        ),
        migrations.AlterField(
            model_name="carposition",
            name="track",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                to="yard_management.track",
            ),
        ),
        migrations.Alter<PERSON>ield(
            model_name="carposition",
            name="track_position",
            field=models.IntegerField(null=True),
        ),
    ]
