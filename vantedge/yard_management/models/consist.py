# from django.db import models
from django.contrib.auth.models import Permission
from django.contrib.gis.db import models
from django.db.models.signals import m2m_changed
from vantedge.trackandtrace.models import RailStation
from vantedge.users.models import Company
from vantedge.users.models.application_configuration import company_permissions_handler
from vantedge.edi.comm.dataclasses import (
    RailSwitchListDataclass
)
from vantedge.util.mixins import EasyAuditModelConfigMixin

class YardManagementCompany(EasyAuditModelConfigMixin, models.Model):
    company = models.OneToOneField(
        Company, related_name="yard_management", on_delete=models.CASCADE
    )

    available_permissions = models.ManyToManyField(
        Permission,
        blank=True,
        limit_choices_to={"content_type__app_label": "yard_management"},
    )

    class Meta:
        verbose_name_plural = "Companies"

    def __str__(self):
        return self.company.name


m2m_changed.connect(
    company_permissions_handler,
    sender=YardManagementCompany.available_permissions.through,
)


class YardData(EasyAuditModelConfigMixin, models.Model):
    easyaudit_crud_retention = 180

    company = models.ForeignKey(
        YardManagementCompany, on_delete=models.CASCADE, related_name="yards"
    )
    rail_station = models.ForeignKey(
        RailStation, on_delete=models.CASCADE, related_name="yards"
    )

    data = models.JSONField(null=True, default=None, blank=True)

    def from_dataclass(self, switch_list: RailSwitchListDataclass):
        rail_station = RailStation.objects.filter(
            splc=switch_list.destination.splc
        ).first()
        if not rail_station:
            rail_station = RailStation.objects.filter(
                name=switch_list.destination.splc
            ).first()
        if not rail_station:
            rail_station = RailStation.objects.filter(
                city=switch_list.destination.splc
            ).first()

        self.rail_station = rail_station
        switch_list_dict = switch_list.to_dict()

        self.data = {
            "track": "1",
            "inbound_train": switch_list_dict["inbound_train"],
            "cars": switch_list_dict["cars"],
        }
        for position, car in enumerate(self.data["cars"]):
            car["position"] = position
        self.data = [self.data]

    def __str__(self) -> str:
        return f"{self.company} {self.rail_station}"
