from django.db import models
from vantedge.util.mixins import EasyAuditModelConfigMixin

class LoadedRailCarStatus(models.TextChoices):
    UNDER_INSPECTION = "Under Inspection", "Under Inspection"
    READY_TO_LOAD = "Ready To Load", "Ready To Load"
    READY_TO_SHIP = "Ready To Ship", "Ready To Ship"

class RailCarInspectionPlan(EasyAuditModelConfigMixin, models.Model):
    form = models.ForeignKey("forms.form", related_name="railcar_inspection_plans", on_delete=models.PROTECT)
    facility = models.ForeignKey("wheelhouse.facility", related_name="inspection_plans",  on_delete=models.PROTECT)
    pre_requirement = models.ForeignKey("self", blank=True, null=True, related_name="downstream_plans", on_delete=models.PROTECT)
    status = models.CharField(max_length=30, choices=LoadedRailCarStatus.choices)
    yard_management_badge = models.Char<PERSON>ield(max_length=5, null=True)

    class Meta:
        unique_together = ["form", "facility", "pre_requirement"]

    def __str__(self):
        return f"facility: {self.facility} form: {self.form.name}"

class RailCarInspectionForm(EasyAuditModelConfigMixin, models.Model):
    form_data = models.ForeignKey("forms.formdata", on_delete=models.PROTECT)
    railcar = models.ForeignKey("wheelhouse.loadedrailcar", related_name="inspection_forms", on_delete=models.PROTECT)

    def __str__(self):
        return f"form: {self.form_data} car: {self.railcar} facility: {self.railcar.current_location}"

    def save(self, *args, **kwargs):
        super().save(*args, **kwargs)
        # Save the railcar to update the badge
        self.railcar.save()
