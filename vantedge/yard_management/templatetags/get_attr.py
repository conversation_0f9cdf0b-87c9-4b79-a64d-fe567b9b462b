from django import template

register = template.Library()

def normalize_value(val):
    if isinstance(val, bool):
        if val == True:
            return 'Yes'
        else:
            return 'No'
    return val

def get_attr(obj, attr):
    name = attr.split("/")[-1]
    if isinstance(obj, dict):
        return normalize_value(obj.get(name, ""))
    return normalize_value(getattr(obj, name))

register.filter("get_attr", get_attr)
