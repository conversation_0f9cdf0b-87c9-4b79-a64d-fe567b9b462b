from django.test import TestCase
from django.contrib.gis.geos import Point

from vantedge.users.models import Company, User
from vantedge.yard_management.models import YardManagementCompany, YardData
from vantedge.trackandtrace.models import RailStation

from rest_framework.test import APIClient

from rest_framework.authtoken.models import Token


class YardDataViewTests(TestCase):
    @classmethod
    def setUpClass(cls):
        super(YardDataViewTests, cls).setUpClass()

        cls.apiclient = APIClient()

    @classmethod
    def setUpTestData(cls):
        """
        TODO: seed extra data to test working in a more full database
        """
        User.objects.create_superuser(
            "<EMAIL>",
            name="Test",
            email="<EMAIL>",
            password="1234",
        )
        tester = User.objects.get(username="<EMAIL>")
        Token.objects.create(user=tester, key="asdf1234")

        test_company = Company.objects.create(name="tester_company")
        yard_test_company = YardManagementCompany.objects.create(company=test_company)
        test_rail = RailStation.objects.create(
            name="tester_rail", city="Calgary", country="Canada", lonlat=Point(1, 1)
        )
        YardData.objects.create(company=yard_test_company, rail_station=test_rail)

        tester.assigned_companies.add(test_company)

    def setUp(self):
        self.apiclient.credentials(HTTP_AUTHORIZATION="Token asdf1234")

    def test_invalid_token(self):
        """
        Send get request for yard data info, with invalid authentication token
        """
        # Overwrite valid authorization token with invalid one
        self.apiclient.credentials(HTTP_AUTHORIZATION="Token wrongtoken")
        response = self.apiclient.get("/api/yarddata/")
        self.assertEqual(
            response.status_code, 401
        )  # status code for unauthorized client

    def test_access_data(self):
        """
        Send get request for yard data of tester user with user's valid authentication token
        """
        response = self.apiclient.get("/api/yarddata/")

        json_response = response.json()
        self.assertEqual(len(json_response), 1)
        company_id = json_response[0]["company"]
        response_company = YardManagementCompany.objects.get(id=company_id)
        self.assertEqual(str(response_company), "tester_company")

    def test_access_other_data(self):
        """
        Add another company's yard data, test that user only accesses data from their assigned company
        """
        test_other_company = Company.objects.create(name="not_tester_company")
        yard_test_other_company = YardManagementCompany.objects.create(
            company=test_other_company
        )
        test_other_rail = RailStation.objects.create(
            name="not_tester_rail", city="Calgary", country="Canada", lonlat=Point(1, 1)
        )
        YardData.objects.create(
            company=yard_test_other_company, rail_station=test_other_rail
        )

        response = self.apiclient.get("/api/yarddata/")

        json_response = response.json()
        self.assertEqual(len(json_response), 1)
        company_id = json_response[0]["company"]
        response_company = YardManagementCompany.objects.get(id=company_id)
        self.assertEqual(str(response_company), "tester_company")

