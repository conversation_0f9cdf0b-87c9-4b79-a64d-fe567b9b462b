import unittest
from unittest import mock
from django.test import TestCase

from vantedge.users.models import Company, User
from vantedge.yard_management.models import (
    CarPosition,
    RailYard,
    RailCarInspectionForm,
    Track,
    YardManagementCompany,
)
from vantedge.forms.models import Form, FormData, FormCompany

from vantedge.yard_management.models.rail_yard import MoveRequest
from django.contrib.auth.models import Permission

from vantedge.edi.models import EdiCompany
from vantedge.wheelhouse.models import (
    Facility,
    LoadedRailCar,
    Party,
    Product,
    Railroad,
    RailShipment,
    TankCar,
    WheelhouseCompany,
)
# For CarImportJobTests
from vantedge.yard_management.management.commands.add_incoming_cars import do_add_incoming_cars
from vantedge.trackandtrace.models import Trip, RailCar, RailStation, TrackAndTraceCompany
from django.utils import timezone
from datetime import timedelta
from rest_framework.test import APIClient

from rest_framework.authtoken.models import Token

#  This is the scenario we're setting up
#
#  Planned Layout:
#
#      Track1, Cap: 5       Track2, Cap: 7         Track3, Cap: 5
#          Start: 1           Start: 5               Start: 1
#
# ┌─────────────────────┐┌─────────────────────┐┌─────────────────────┐
# │   ┌────────────────┐││                     ││                     │
# │ 1 │  ABC 0001      │││ 5                   ││ 1                   │
# │   └────────────────┘││                     ││                     │
# │   ┌────────────────┐││  ┌────────────────┐ ││                     │
# │ 2 │  ABC 0002      │││ 6│  ABC 0004      │ ││ 2                   │
# │   └────────────────┘││  └────────────────┘ ││                     │
# │   ┌────────────────┐││                     ││                     │
# │ 3 │  ABC 0003      │││ 7                   ││ 3                   │
# │   └────────────────┘││                     ││                     │
# │                     ││                     ││                     │
# │ 4                   ││ 8                   ││ 4                   │
# │                     ││                     ││                     │
# │                     ││                     ││                     │
# │ 5                   ││ 9                   ││ 5                   │
# │                     ││                     ││                     │
# └─────────────────────┘│                     │└─────────────────────┘
#                        │10                   │
#                        │                     │
#                        │                     │
#                        │11                   │
#                        │                     │
#                        └─────────────────────┘
#
# ruff: noqa: PT009
class YardLayoutTests(TestCase):
    @classmethod
    def setUpClass(cls):
        super().setUpClass()
        cls.apiclient = APIClient()

    @classmethod
    def setUpTestData(cls):
        User.objects.create_superuser(
            "<EMAIL>",
            name="Test",
            email="<EMAIL>",
            password="1234",
        )
        test_user = User.objects.get(username="<EMAIL>")
        Token.objects.create(user=test_user, key="asdf1234")

        test_company = Company.objects.create(name="tester_company")
        cls.test_company = test_company
        cls.wheelhouse_company = WheelhouseCompany.objects.create(company=test_company)
        ymc = YardManagementCompany.objects.create(company=test_company)
        ymc.available_permissions.add(Permission.objects.get(codename='view_railyard'))
        ymc.save()
        test_user.assigned_companies.add(test_company)
        test_user.company = test_company
        test_user.save()
        cls.p1 = Party.objects.create(company=cls.wheelhouse_company)
        cls.railroad = Railroad.objects.create(name="CN")
        cls.edi_company = EdiCompany.objects.create(company=test_company, gs_code_cn="xxxx")
        cls.facility = Facility.objects.create(company=cls.wheelhouse_company)
        cls.yard = RailYard.objects.create(facility=cls.facility)
        cls.track1 = Track.objects.create(
            rail_yard=cls.yard,
            capacity=5,
            start_position=1,
            sort_order=1,
            facility=cls.facility,
        )
        cls.track2 = Track.objects.create(
            rail_yard=cls.yard,
            capacity=7,
            start_position=5,
            sort_order=2,
            facility=cls.facility,
        )
        cls.track3 = Track.objects.create(
            rail_yard=cls.yard,
            capacity=5,
            start_position=1,
            sort_order=3,
            facility=cls.facility,
        )
        cls.current = cls.yard.get_scenario_current()
        cls.plan = cls.yard.get_scenario_planned()

        # Set YardLayout Cache
        RailYard.set_last_update(cls.yard.id)

        def new_car(num, track, pos):
            # TODO(wnh): Run all these tests again with a HopperCar to ensure we work for everything
            lrc = TankCar.objects.create(car_mark='ABC', car_number=num, current_location=cls.facility)
            lrc.save()
            carpos = CarPosition.objects.create(rail_car=lrc, scenario=cls.plan, track=track, track_position=pos)
            CarPosition.objects.create(rail_car=lrc, scenario=cls.current, track=track, track_position=pos)
            carpos.save()
            return carpos
        cls.car1 = new_car('0001', cls.track1, 0)
        cls.car2 = new_car('0002', cls.track1, 1)
        cls.car3 = new_car('0003', cls.track1, 2)
        cls.car4 = new_car('0004', cls.track2, 1)

    def setUp(self):
        self.apiclient.credentials(HTTP_AUTHORIZATION="Token asdf1234")

def car_result_set(car_result):
    """turn a car result into a set of tuples of their locations"""
    return { (
        c["scenario_id"],
        c["track_id"],
        c["current_position"],
        c["rail_car_id"],
    ) for c in car_result }

class YardMovementTests(YardLayoutTests):
    def test_single_move_works(self):
        mm = MoveRequest(
            move_up=True,
            railcar_ids=[self.car1.rail_car_id],
            scenario_id=self.plan.id,
            track_id=self.track3.id,
            track_position=0,
            last_update=RailYard.get_last_update(self.yard.id),
        )
        res = self.apiclient.post(
            f"/api/yard_management/rail_yard/{self.yard.id}/move_cars/",
            mm.dict(),
            format="json"
        )
        self.assertEqual(200, res.status_code)
        # carjson = res.json()
        # result_cars = [
        #     x for x in carjson if
        #         x['rail_car_id'] == self.car1.rail_car_id
        #         and x['scenario_id'] == self.plan.id
        # ]
        # self.assertEqual(1, len(result_cars))
        # res_car = result_cars[0]
        # self.assertEqual(res_car['track_id'], self.track3.id)
        # self.assertEqual(res_car['current_position'], 0)
        self.assertTrue((self.plan.id, self.track3.id, 0, self.car1.rail_car_id) in car_result_set(res.json()))
        self.assertTrue((self.plan.id, self.track1.id, 0, self.car1.rail_car_id) not in car_result_set(res.json()))


    def test_non_autocomplete_doesnt_move_other_car(self):
        prev_track = self.car1.track_id
        prev_pos = self.car1.track_position
        mm = MoveRequest(
            move_up=True,
            railcar_ids=[self.car1.rail_car_id],
            scenario_id=self.plan.id,
            track_id=self.track3.id,
            track_position=4,
            last_update=RailYard.get_last_update(self.yard.id),
        )
        res = self.apiclient.post(
            f"/api/yard_management/rail_yard/{self.yard.id}/move_cars/",
            mm.dict(),
            format="json"
        )
        self.assertEqual(200, res.status_code)
        data = res.json()
        other_car = [x for x in data if x['rail_car_id'] == self.car1.rail_car_id and x['scenario_id'] == self.current.id][0]
        self.assertEqual(other_car['track_id'], prev_track)
        self.assertEqual(other_car['current_position'], prev_pos)


    def test_multi_move_works(self):
        mm = MoveRequest(
            move_up=True,
            railcar_ids=[self.car1.rail_car_id, self.car2.rail_car_id],
            scenario_id=self.plan.id,
            track_id=self.track3.id,
            track_position=3,
            last_update=RailYard.get_last_update(self.yard.id),
        )
        res = self.apiclient.post(
            f"/api/yard_management/rail_yard/{self.yard.id}/move_cars/",
            mm.dict(),
            format="json"
        )
        self.assertEqual(200, res.status_code)
        body = res.json()
        # result_cars = {
        #     (x['rail_car_id'], x['track_id'], x['current_position'])
        #     for x in body if
        #         x['rail_car_id'] in (self.car1.rail_car_id, self.car2.rail_car_id)
        #         and x['scenario_id'] == self.plan.id
        # }
        # self.assertEqual(result_cars, {
        #     (self.car1.rail_car_id, self.track3.id, 3),
        #     (self.car2.rail_car_id, self.track3.id, 4)
        result_cars = car_result_set(body)
        self.assertEqual(result_cars, {
            (self.current.id, self.track1.id, 0, self.car1.rail_car_id),
            (self.current.id, self.track1.id, 1, self.car2.rail_car_id),
            (self.current.id, self.track1.id, 2, self.car3.rail_car_id),
            (self.current.id, self.track2.id, 1, self.car4.rail_car_id),

            (self.plan.id,    self.track3.id, 3, self.car1.rail_car_id),
            (self.plan.id,    self.track3.id, 4, self.car2.rail_car_id),
            (self.plan.id,    self.track1.id, 2, self.car3.rail_car_id),
            (self.plan.id,    self.track2.id, 1, self.car4.rail_car_id),
        })

    def test_moving_block_of_cars_doesnt_conflict_with_itself(self):
        mm = MoveRequest(
            move_up=True,
            railcar_ids=[self.car1.rail_car_id, self.car2.rail_car_id, self.car3.rail_car_id],
            scenario_id=self.plan.id,
            track_id=self.track1.id,
            track_position=1,
            last_update=RailYard.get_last_update(self.yard.id),
        )
        res = self.apiclient.post(
            f"/api/yard_management/rail_yard/{self.yard.id}/move_cars/",
            mm.dict(),
            format="json"
        )
        self.assertEqual(200, res.status_code)
        j = res.json()
        self.assertEqual(car_result_set(j), {
            (self.current.id, self.track1.id, 0, self.car1.rail_car_id),
            (self.current.id, self.track1.id, 1, self.car2.rail_car_id),
            (self.current.id, self.track1.id, 2, self.car3.rail_car_id),
            (self.current.id, self.track2.id, 1, self.car4.rail_car_id),

            (self.plan.id,    self.track1.id, 1, self.car1.rail_car_id),
            (self.plan.id,    self.track1.id, 2, self.car2.rail_car_id),
            (self.plan.id,    self.track1.id, 3, self.car3.rail_car_id),
            (self.plan.id,    self.track2.id, 1, self.car4.rail_car_id),
        })

    def test_move_just_before(self):
        mm = MoveRequest(
            move_up=True,
            railcar_ids=[self.car1.rail_car_id],
            scenario_id=self.plan.id,
            track_id=self.track2.id,
            track_position=0,
            last_update=RailYard.get_last_update(self.yard.id),
        )
        res = self.apiclient.post(
            f"/api/yard_management/rail_yard/{self.yard.id}/move_cars/",
            mm.dict(),
            format="json"
        )
        self.assertEqual(200, res.status_code)

    def get_track_position_car_number_list(self, track, scenario_name):
        qs = CarPosition.objects.filter(
            track=track,
            scenario__name=scenario_name,
        ).order_by("track_position")
        return [(c.track_position, str(c.rail_car)) for c in qs]

    def move_with_checks(
        self,
        insert_cars, insert_track, insert_pos, expected_initial_track, expected_final_track,
        res_status=200, move_up=True
    ):
        mm = MoveRequest(
            move_up=move_up,
            railcar_ids=insert_cars,
            scenario_id=self.plan.id,
            track_id=insert_track.id,
            track_position=insert_pos,
            last_update=RailYard.get_last_update(self.yard.id),
        )
        initial_track = self.get_track_position_car_number_list(insert_track, "Planned")
        # Assert test is set up correctly
        self.assertEqual(initial_track, expected_initial_track, msg="Test set up failed")
        res = self.apiclient.post(
            f"/api/yard_management/rail_yard/{self.yard.id}/move_cars/",
            mm.dict(),
            format="json"
        )
        final_track = self.get_track_position_car_number_list(insert_track, "Planned")
        # Check the move was successful and cars where bumped up in the correct order
        self.assertEqual(res_status, res.status_code)
        self.assertEqual(final_track, expected_final_track)

    def add_new_car(self, num, track, pos):
        # TODO(wnh): Run all these tests again with a HopperCar to ensure we work for everything
        lrc = TankCar.objects.create(car_mark='ABC', car_number=num, current_location=self.facility)
        lrc.save()
        carpos = CarPosition.objects.create(rail_car=lrc, scenario=self.plan, track=track, track_position=pos)
        CarPosition.objects.create(rail_car=lrc, scenario=self.current, track=track, track_position=pos)
        carpos.save()
        return carpos

    def test_bump_up(self):
        insert_cars = [self.car4.rail_car.id]
        insert_track = self.track1
        insert_pos = 0
        expected_initial_track = [(0, "ABC0001"), (1, "ABC0002"), (2, "ABC0003")]
        expected_final_track = [(0, "ABC0004"), (1, "ABC0001"), (2, "ABC0002"), (3, "ABC0003")]
        self.move_with_checks(insert_cars, insert_track, insert_pos, expected_initial_track, expected_final_track)

    def test_bump_up_same_track(self):
        insert_cars = [self.car3.rail_car.id]
        insert_track = self.track1
        insert_pos = 0
        expected_initial_track = [(0, "ABC0001"), (1, "ABC0002"), (2, "ABC0003")]
        expected_final_track = [(0, "ABC0003"), (1, "ABC0001"), (2, "ABC0002")]
        self.move_with_checks(insert_cars, insert_track, insert_pos, expected_initial_track, expected_final_track)

    def test_bump_up_into_gap(self):
        _car5 = self.add_new_car("0005", self.track1, 4)
        insert_cars = [self.car4.rail_car.id]
        insert_track = self.track1
        insert_pos = 0
        expected_initial_track = [(0, "ABC0001"), (1, "ABC0002"), (2, "ABC0003"), (4, "ABC0005")]
        expected_final_track = [(0, "ABC0004"), (1, "ABC0001"), (2, "ABC0002"), (3, "ABC0003"), (4, "ABC0005")]
        self.move_with_checks(insert_cars, insert_track, insert_pos, expected_initial_track, expected_final_track)

    def test_bump_up_multiple_cars(self):
        car5 = self.add_new_car("0005", self.track1, 4)
        insert_cars = [car5.rail_car.id, self.car4.rail_car.id]
        insert_track = self.track1
        insert_pos = 0
        expected_initial_track = [(0, "ABC0001"), (1, "ABC0002"), (2, "ABC0003"), (4, "ABC0005")]
        expected_final_track = [(0, "ABC0005"), (1, "ABC0004"),
                                (2, "ABC0001"), (3, "ABC0002"), (4, "ABC0003")]
        self.move_with_checks(insert_cars, insert_track, insert_pos, expected_initial_track, expected_final_track)


    def test_bump_up_second_moved_car_causes_bump(self):
        _car5 = self.add_new_car("0005", self.track3, 3)
        insert_cars = [self.car1.rail_car.id, self.car2.rail_car.id]
        insert_track = self.track3
        insert_pos = 2
        expected_initial_track = [(3, "ABC0005")]
        expected_final_track = [(2, "ABC0001"), (3, "ABC0002"), (4, "ABC0005")]
        self.move_with_checks(insert_cars, insert_track, insert_pos, expected_initial_track, expected_final_track)

    def test_bump_up_gaps_above(self):
        _car6 = self.add_new_car("0006", self.track2, 5)
        _car5 = self.add_new_car("0005", self.track2, 0)
        insert_cars = [self.car1.rail_car.id]
        insert_track = self.track2
        insert_pos = 0
        # Test that car in position 5 does not move.
        expected_initial_track = [(0, "ABC0005"), (1, "ABC0004"), (5, "ABC0006")]
        expected_final_track = [(0, "ABC0001"), (1, "ABC0005"), (2, "ABC0004"), (5, "ABC0006")]
        self.move_with_checks(insert_cars, insert_track, insert_pos, expected_initial_track, expected_final_track)

    def test_bump_up_off_the_end_of_track(self):
        car6 = self.add_new_car("0006", self.track2, 5)
        car5 = self.add_new_car("0005", self.track2, 0)
        insert_cars = [self.car4.rail_car.id, car5.rail_car.id, car6.rail_car.id]
        insert_track = self.track1
        insert_pos = 0
        expected_initial_track = [(0, "ABC0001"), (1, "ABC0002"), (2, "ABC0003")]
        expected_final_track = [(0, "ABC0001"), (1, "ABC0002"), (2, "ABC0003")]
        self.move_with_checks(insert_cars, insert_track, insert_pos, expected_initial_track, expected_final_track, 400)

    def test_move_down(self):
        insert_cars = [self.car4.rail_car.id, self.car3.rail_car.id, self.car2.rail_car.id]
        insert_track = self.track3
        insert_pos = 4
        expected_initial_track = []
        expected_final_track = [(2,"ABC0002"), (3, "ABC0003"), (4, "ABC0004")]
        move_up=False
        self.move_with_checks(
            insert_cars, insert_track, insert_pos, expected_initial_track, expected_final_track, 200, move_up
        )

    def test_bump_down(self):
        insert_cars = [self.car1.rail_car.id]
        insert_track = self.track2
        insert_pos = 1
        expected_initial_track = [(1, "ABC0004")]
        expected_final_track = [(0, "ABC0004"), (1, "ABC0001")]
        move_up=False
        self.move_with_checks(
            insert_cars, insert_track, insert_pos, expected_initial_track, expected_final_track, 200, move_up
        )

    def test_move_down_with_gaps(self):
        _car5 = self.add_new_car("0005", self.track2, 4)
        _car6 = self.add_new_car("0006", self.track2, 5)
        insert_cars = [self.car1.rail_car.id]
        insert_track = self.track2
        insert_pos = 5
        expected_initial_track = [(1,"ABC0004"), (4, "ABC0005"), (5, "ABC0006")]
        expected_final_track = [(1,"ABC0004"), (3, "ABC0005"), (4, "ABC0006"), (5, "ABC0001")]
        move_up=False
        self.move_with_checks(
            insert_cars, insert_track, insert_pos, expected_initial_track, expected_final_track, 200, move_up
        )

    def test_move_down_off_start_of_track(self):
        insert_cars = [self.car4.rail_car.id, self.car3.rail_car.id, self.car2.rail_car.id]
        insert_track = self.track3
        insert_pos = 1
        expected_initial_track = []
        expected_final_track = []
        move_up=False
        self.move_with_checks(
            insert_cars, insert_track, insert_pos, expected_initial_track, expected_final_track, 400, move_up
        )

    def test_multi_move_checks_overlap(self):
        mm = MoveRequest(
            move_up=True,
            railcar_ids=[self.car1.rail_car_id, self.car2.rail_car_id],
            scenario_id=self.plan.id,
            track_id=self.track2.id,
            track_position=0,
            last_update=RailYard.get_last_update(self.yard.id),
        )
        res = self.apiclient.post(
            f"/api/yard_management/rail_yard/{self.yard.id}/move_cars/",
            mm.dict(),
            format="json"
        )
        self.assertEqual(200, res.status_code)

    def test_multi_move_checks_track_overrun(self):
        mm = MoveRequest(
            move_up=True,
            railcar_ids=[self.car1.rail_car_id, self.car2.rail_car_id],
            scenario_id=self.plan.id,
            track_id=self.track2.id,
            track_position=6,
            last_update=RailYard.get_last_update(self.yard.id),
        )
        res = self.apiclient.post(
            f"/api/yard_management/rail_yard/{self.yard.id}/move_cars/",
            mm.dict(),
            format="json"
        )
        self.assertEqual(400, res.status_code)

    def test_completing_moves_add_entry_to_history(self):
        from_track = self.car1.track_id
        from_pos = self.car1.track_position
        mm = MoveRequest(
            move_up=True,
            railcar_ids=[self.car1.rail_car_id],
            scenario_id=self.plan.id,
            track_id=self.track3.id,
            track_position=3,
            last_update=RailYard.get_last_update(self.yard.id),
        )
        res = self.apiclient.post(
            f"/api/yard_management/rail_yard/{self.yard.id}/move_cars/",
            mm.dict(),
            format="json"
        )

        # assert we have no history from that move
        self.assertEqual(200, res.status_code)
        res = self.apiclient.get(f"/api/yard_management/rail_yard/{self.yard.id}/move_history/")
        self.assertEqual(200, res.status_code)
        hist = res.json()
        self.assertEqual(0, len(hist['results']))

        self.assertEqual(200, res.status_code)
        res = self.apiclient.get(f"/api/yard_management/rail_yard/{self.yard.id}/move_history/")
        self.assertEqual(200, res.status_code)
        hist = res.json()
        self.assertEqual(0, len(hist['results']))

        res = self.apiclient.get(f"/api/yard_management/rail_yard/{self.yard.id}/switch_list/")
        self.assertEqual(200, res.status_code)
        switch_list = res.json()
        self.assertEqual(1, len(switch_list))

        # Send the single move back in
        res = self.apiclient.post(
            f"/api/yard_management/rail_yard/{self.yard.id}/execute_move/",
            switch_list[0],
            format="json",
        )
        self.assertEqual(200, res.status_code)

        # Assert that the move is recorded with all fields
        res = self.apiclient.get(f"/api/yard_management/rail_yard/{self.yard.id}/move_history/")
        self.assertEqual(200, res.status_code)
        hist = res.json()
        self.assertEqual(1, len(hist['results']))
        h =hist['results'][0]
        self.assertEqual(h['from_track']['id'], from_track)
        self.assertEqual(h['from_position'], from_pos)
        self.assertEqual(h['to_track']['id'], self.track3.id)
        self.assertEqual(h['car_mark'], self.car1.rail_car.car_mark)
        self.assertEqual(h['car_number'], self.car1.rail_car.car_number)


    def test_move_add_cars_in_order_specified_in_request(self):
        """It is the responsibility of the front end to specify the car order for a move,
        on the backend, we're just going to place the cars in the order that they are specified
        in the `rail_car_id` parameter in the move request"""
        all_ids = [
            self.car3.rail_car_id,
            self.car1.rail_car_id,
            self.car4.rail_car_id,
            self.car2.rail_car_id,
        ]
        # Just to make sure this is certain where going to test all 24
        # ways we can order these cars
        import itertools
        for id_tuple in itertools.permutations(all_ids):
            req_ids = list(id_tuple)
            mm = MoveRequest(
                move_up=True,
                railcar_ids=req_ids,
                scenario_id=self.plan.id,
                track_id=self.track2.id,
                track_position=0,
                last_update=RailYard.get_last_update(self.yard.id),
            )
            res = self.apiclient.post(
                f"/api/yard_management/rail_yard/{self.yard.id}/move_cars/",
                mm.dict(),
                format="json"
            )
            self.assertEqual(200, res.status_code)
            carjson = [c for c in res.json() if c['scenario_id'] == self.plan.id]
            sorted_cars = sorted(carjson, key=lambda c: c['current_position'])
            sorted_ids = [c['rail_car_id'] for c in sorted_cars]
            self.assertEqual( req_ids, sorted_ids )
            self.assertEqual( [0,1,2,3], [c['current_position'] for c in sorted_cars] )

    @unittest.skip("TODO")
    def test_reset_layout(self):
        """If there are pending moves in the Planned scenario, resetting the layout makes Planned the same as Current"""
        pass

class AutoCompleteTests(YardLayoutTests):
    def setUp(self):
        super().setUp()
        self.yard.use_autocomplete = True
        self.yard.save()

    def test_move_cars_outdated_cache(self):
        mm = MoveRequest(
            move_up=True,
            railcar_ids=[self.car1.rail_car_id],
            scenario_id=self.plan.id,
            track_id=self.track3.id,
            track_position=3,
            last_update="garbage",
        )
        res = self.apiclient.post(
            f"/api/yard_management/rail_yard/{self.yard.id}/move_cars/",
            mm.dict(),
            format="json"
        )
        self.assertEqual(400, res.status_code)
        self.assertEqual(res.content.decode(), "RAILYARD SYNC ERROR")

    def test_setting_autocomplete_works(self):
        for state in [True, False]:
            res = self.apiclient.put(
                f"/api/yard_management/rail_yard/{self.yard.id}/set_autocomplete/",
                dict(use_autocomplete=state),
                format="json"
            )
            self.assertEqual(200, res.status_code)
            res = self.apiclient.get(f"/api/yard_management/rail_yard/{self.yard.id}/")
            yard = res.json()
            self.assertEqual(state, yard["use_autocomplete"])

    @unittest.skip("TODO")
    def test_setting_autcomplete_clears_planned_scenario(self):
        """ If there are pending moves in the Planned scenario, setting the autocomplete flag should clear those moves and reset the layout to be the same as the Current scenario"""
        pass

    def test_auto_complete_moves_in_both_scenarios(self):
        mm = MoveRequest(
            move_up=True,
            railcar_ids=[self.car1.rail_car_id],
            scenario_id=self.plan.id,
            track_id=self.track3.id,
            track_position=3,
            last_update=RailYard.get_last_update(self.yard.id),
        )
        res = self.apiclient.post(
            f"/api/yard_management/rail_yard/{self.yard.id}/move_cars/",
            mm.dict(),
            format="json"
        )
        self.assertEqual(200, res.status_code)
        body = res.json()
        # all cars with the updated 'rail_car_id' should have the same track/pos
        result_cars = {(x['scenario_id'], x['track_id'], x['current_position'])  for x in body if x['rail_car_id'] == self.car1.rail_car_id}
        self.assertEqual(result_cars, {
            (self.plan.id, self.track3.id, 3),
            (self.current.id, self.track3.id, 3),
        })

    def test_auto_complete_adds_to_history(self):
        from_track = self.car1.track_id
        from_pos = self.car1.track_position
        mm = MoveRequest(
            move_up=True,
            railcar_ids=[self.car1.rail_car_id],
            scenario_id=self.plan.id,
            track_id=self.track3.id,
            track_position=3,
            last_update=RailYard.get_last_update(self.yard.id),
        )
        res = self.apiclient.post(
            f"/api/yard_management/rail_yard/{self.yard.id}/move_cars/",
            mm.dict(),
            format="json"
        )
        self.assertEqual(200, res.status_code)
        res = self.apiclient.get(f"/api/yard_management/rail_yard/{self.yard.id}/move_history/")
        self.assertEqual(200, res.status_code)
        hist = res.json()
        self.assertEqual(1, len(hist['results']))
        h =hist['results'][0]
        self.assertEqual(h['from_track']['id'], from_track)
        self.assertEqual(h['from_position'], from_pos)
        self.assertEqual(h['to_track']['id'], self.track3.id)
        self.assertEqual(h['to_position'], 3)
        self.assertEqual(h['car_mark'], self.car1.rail_car.car_mark)
        self.assertEqual(h['car_number'], self.car1.rail_car.car_number)


class YardLayoutDeleteTests(YardLayoutTests):
    def test_delete_with_unshipped_car(self):
        """ Removing a car from the yard is OK if: the attached LoadedRailCar is not yet shipped, has not attached RailShipment object
        """
        lrc = self.car1.rail_car
        lrc.status=LoadedRailCar.LoadedRailCarStatus.UNDER_INSPECTION
        lrc.save()

        res = self.apiclient.post(
            f"/api/yard_management/rail_yard/{self.yard.id}/remove/",
            {"car_ids": [self.car1.id], "last_update": RailYard.get_last_update(self.yard.id)},
            format="json"
        )
        # it should obviously succeede
        self.assertEqual(200, res.status_code)

        # the car position should be removed from the current_scenario
        res = self.apiclient.get(f"/api/yard_management/rail_yard/{self.yard.id}/cars/")
        cars = res.json()
        self.assertEqual(0, len([c for c in cars if c.get('id') == self.car1.id]))
        # The loaded rail car should not exist anymore
        self.assertEqual(0, len(LoadedRailCar.objects.filter(pk=lrc.id)), "The attached LoadedRailCar should also be removed")

    def test_delete_adds_a_history_record(self):
        lrc = self.car1.rail_car
        lrc.status=LoadedRailCar.LoadedRailCarStatus.UNDER_INSPECTION
        lrc.save()

        res = self.apiclient.post(
            f"/api/yard_management/rail_yard/{self.yard.id}/remove/",
            {"car_ids": [self.car1.id], "last_update": RailYard.get_last_update(self.yard.id)},
            format="json"
        )
        # it should obviously succeed
        self.assertEqual(200, res.status_code)

        res = self.apiclient.get(f"/api/yard_management/rail_yard/{self.yard.id}/move_history/")
        self.assertEqual(200, res.status_code)
        hist = res.json()
        print(hist)
        self.assertEqual(1, len(hist['results']))
        self.assertEqual("REMOVED", hist['results'][0]['event_type'])

    def test_delete_works_with_unassigned_cars(self):
        print("Starting test_delete_works_with_unassigned_cars")
        lrc = LoadedRailCar.objects.create(
            current_location=self.facility,
            car_mark="ABC",
            car_number="00005",
            status=LoadedRailCar.LoadedRailCarStatus.UNDER_INSPECTION,
            rail_shipment=None,
        )
        lrc_id = lrc.id
        carpos = CarPosition.objects.create(
            rail_car=lrc,
            scenario=self.current,
        )

        res = self.apiclient.post(
            f"/api/yard_management/rail_yard/{self.yard.id}/remove/",
            {"car_ids": [carpos.id], "last_update": RailYard.get_last_update(self.yard.id)},
            format="json"
        )
        # it should obviously succeed
        self.assertEqual(200, res.status_code)

        # the car position should be removed from the current_scenario
        res = self.apiclient.get(f"/api/yard_management/rail_yard/{self.yard.id}/unassigned_cars/")
        self.assertEqual(0, len([c for c in res.json() if c.get('id') == carpos.id]))
        # The loaded rail car should not exist anymore
        self.assertEqual(0, len(LoadedRailCar.objects.filter(pk=lrc_id)))


    def test_delete_removes_from_both_current_and_planned(self):
        lrc = self.car1.rail_car
        lrc.status=LoadedRailCar.LoadedRailCarStatus.UNDER_INSPECTION
        lrc.save()

        res = self.apiclient.post(
            f"/api/yard_management/rail_yard/{self.yard.id}/remove/",
            {"car_ids": [self.car1.id], "last_update": RailYard.get_last_update(self.yard.id)},
            format="json"
        )
        self.assertEqual(200, res.status_code)
        # The car should not be listed in the either scenario
        res = self.apiclient.get(f"/api/yard_management/rail_yard/{self.yard.id}/cars/")
        cars = res.json()
        self.assertEqual(0, len([c for c in cars if c['rail_car_id'] == lrc.id]))

    def test_delete_with_attached_shipment_fails(self):
        sh = RailShipment.objects.create(
            consignee=self.p1,
            shipper=self.p1,
            destination=self.facility,
            origin=self.facility,
            railroad=self.railroad
        )
        lrc = self.car1.rail_car
        lrc.status=LoadedRailCar.LoadedRailCarStatus.UNDER_INSPECTION
        lrc.rail_shipment=sh
        lrc.save()

        res = self.apiclient.post(
            f"/api/yard_management/rail_yard/{self.yard.id}/remove/",
            {"car_ids": [self.car1.id], "last_update": RailYard.get_last_update(self.yard.id)},
            format="json"
        )
        # The request should fail
        self.assertEqual(400, res.status_code)
        res = self.apiclient.get(f"/api/yard_management/rail_yard/{self.yard.id}/cars/")
        #The car should still be listed in the yard (1 for each senario)
        self.assertEqual(2, len([c for c in res.json() if c['rail_car_id'] == lrc.id]))

    def test_delete_with_attached_inspections_fails(self):
        form_company = FormCompany.objects.create(company=self.test_company)
        form = Form.objects.create(company=form_company)
        form_data = FormData.objects.create(form=form, data={})
        RailCarInspectionForm.objects.create(railcar=self.car1.rail_car, form_data=form_data)
        res = self.apiclient.post(
            f"/api/yard_management/rail_yard/{self.yard.id}/remove/",
            {"car_ids": [self.car1.id], "last_update": RailYard.get_last_update(self.yard.id)},
            format="json"
        )
        # The request should fail
        self.assertEqual(400, res.status_code)
        res = self.apiclient.get(f"/api/yard_management/rail_yard/{self.yard.id}/cars/")
        # The car should still be listed in the yard (1 for each senario)
        self.assertEqual(2, len([c for c in res.json() if c['rail_car_id'] == self.car1.rail_car_id]))

    def test_one_failure_keeps_all_cars(self):
        sh = RailShipment.objects.create(
            consignee=self.p1,
            shipper=self.p1,
            destination=self.facility,
            origin=self.facility,
            railroad=self.railroad
        )
        lrc = self.car1.rail_car
        lrc.status=LoadedRailCar.LoadedRailCarStatus.UNDER_INSPECTION
        lrc.rail_shipment=sh
        lrc.save()

        res = self.apiclient.post(
            f"/api/yard_management/rail_yard/{self.yard.id}/remove/",
            {"car_ids": [self.car1.id, self.car2.id, self.car3.id],
             "last_update": RailYard.get_last_update(self.yard.id)},
            format="json"
        )
        # The request should fail because of the attached shipment
        self.assertEqual(400, res.status_code)
        res = self.apiclient.get(f"/api/yard_management/rail_yard/{self.yard.id}/cars/")
        cars = res.json()
        # Each car that for which the delete was requested should still be in the yard
        # (one for each scenario)
        self.assertEqual(2, len([c for c in cars if c['rail_car_id'] == lrc.id]))
        self.assertEqual(2, len([c for c in cars if c['rail_car_id'] == self.car2.rail_car_id]))
        self.assertEqual(2, len([c for c in cars if c['rail_car_id'] == self.car3.rail_car_id]))

    def test_checking_only_doesnt_remove_cars(self):
        """ When checking a car that is valid to delete, it returns OK but doesn't remove the car """
        lrc = self.car1.rail_car
        lrc.status=LoadedRailCar.LoadedRailCarStatus.UNDER_INSPECTION
        lrc.save()

        res = self.apiclient.post(
            f"/api/yard_management/rail_yard/{self.yard.id}/remove/?checkOnly=true",
            {"car_ids": [self.car1.id], "last_update": RailYard.get_last_update(self.yard.id)},
            format="json"
        )
        # it should work
        self.assertEqual(200, res.status_code)

        res = self.apiclient.get(f"/api/yard_management/rail_yard/{self.yard.id}/cars/")
        # The car should still be listed in the yard (1 for each senario)
        self.assertEqual(2, len([c for c in res.json() if c['rail_car_id'] == lrc.id]))

    def test_checking_with_attached_inspections_fails_and_returns_conflicting_ids(self):
        form_company = FormCompany.objects.create(company=self.test_company)
        form = Form.objects.create(company=form_company)
        form_data = FormData.objects.create(form=form, data={})
        RailCarInspectionForm.objects.create(railcar=self.car1.rail_car, form_data=form_data)
        res = self.apiclient.post(
            f"/api/yard_management/rail_yard/{self.yard.id}/remove/?checkOnly=true",
            {"car_ids": [self.car1.id], "last_update": RailYard.get_last_update(self.yard.id)},
            format="json"
        )
        # The request should fail and report the ids of the cars that are blocking
        self.assertEqual(400, res.status_code)
        conflict_res = res.json()
        self.assertEqual([{'carposition_id': self.car1.id, 'rail_car_id': self.car1.rail_car_id}], conflict_res['blocking_cars'])
        res = self.apiclient.get(f"/api/yard_management/rail_yard/{self.yard.id}/cars/")
        #The car should still be listed in the yard (1 for each senario)
        self.assertEqual(2, len([c for c in res.json() if c['rail_car_id'] == self.car1.rail_car_id]))


    def test_shipping_a_shipment_removes_cars_from_the_yard(self):
        # This needs some major refactoring to:
        #  - More EDI data setup with addresses, etc. so the  pre-send checks validate correctly

        # When a car references a rail shipment, and that rail shipment is shipped
        # the railcar should be removed from the yard
        prod = Product.objects.create(company=self.wheelhouse_company, name="product")
        sh = RailShipment.objects.create(
            consignee=self.p1,
            shipper=self.p1,
            destination=self.facility,
            origin=self.facility,
            railroad=self.railroad,
            product=prod,
        )
        lrc = self.car1.rail_car
        lrc.status=LoadedRailCar.LoadedRailCarStatus.UNDER_INSPECTION
        lrc.rail_shipment=sh
        lrc.save()

        with mock.patch(
            'vantedge.edi.comm.cn.EDI404CN.send_to_ftp',
            return_value=("fake_filename", 12, "out_text")
        ):
            res = self.apiclient.post(
                f"/api/railshipment/{sh.id}/ship/",
                {"hazardous_certificate_person":"Some User"},
                format="json"
            )
        self.assertEqual(200, res.status_code)
        res = self.apiclient.get(f"/api/yard_management/rail_yard/{self.yard.id}/cars/")
        car_res = res.json()
        #The car should be removed from the yard
        self.assertEqual(0, len([c for c in car_res if c['rail_car_id'] == lrc.id]))
        # but the rest of the cars stick around (one in each scenario)
        for check in [self.car2, self.car3, self.car4]:
            self.assertEqual(2, len([c for c in car_res if c['rail_car_id'] == check.rail_car_id]))

        # ensure that we see a "SHIPPED" envent in the execution log
        res = self.apiclient.get(f"/api/yard_management/rail_yard/{self.yard.id}/move_history/")
        self.assertEqual(200, res.status_code)
        hist = res.json()
        self.assertEqual(1, len(hist['results']))
        self.assertEqual("SHIPPED", hist['results'][0]['event_type'])

    @unittest.skip("TODO: figure out how to 'ship' without talking to an FTP server")
    def test_shipping_a_shipment_then_voiding_returns_the_car(self):
        # Ship a shipment
        # Then you void that shipment
        # expect the cars associated with that shipment to come back
        #     into the yard in the unassigned cars column
        pass

class CarAdditionTests(YardLayoutTests):
    def test_adding_lowercase_car_mark_gets_uppercased(self):
        # The search boxes rely on having the car marks normalized to
        # uppercase. If we add cars through the yard layout page ensure they
        # are converted
        res = self.apiclient.post(
            f"/api/yard_management/rail_yard/{self.yard.id}/add_cars/",
            {"car_marks":["tank00000"],"car_type":"TANK_CAR","scenario":"planned"},
            format="json"
        )
        self.assertEqual(200, res.status_code)
        res = self.apiclient.get(f"/api/yard_management/rail_yard/{self.yard.id}/cars/")
        cars = res.json()
        self.assertEqual(0, len([c for c in cars if c['name'] == "tank00000"]))
        self.assertEqual(2, len([c for c in cars if c['name'] == "TANK00000"]))

    def test_adding_a_car_creates_an_add_record(self):
        print("adding_a_car_creates_an_add_record")
        # Adding a car should create an "ADDED" event
        res = self.apiclient.post(
            f"/api/yard_management/rail_yard/{self.yard.id}/add_cars/",
            {"car_marks":["tank00000"],"car_type":"TANK_CAR","scenario":"planned"},
            format="json"
        )
        self.assertEqual(200, res.status_code)
        res = self.apiclient.get(f"/api/yard_management/rail_yard/{self.yard.id}/move_history/")
        self.assertEqual(200, res.status_code)
        hist = res.json()
        self.assertEqual(1, len(hist['results']))
        self.assertEqual("ADDED", hist['results'][0]['event_type'])



# TODO(wnh): There is alot of duplication here that can easily be cleaned up
class CarImportJobTests(YardLayoutTests):

    def setUp(self):
        super().setUp()
        self.tnt_company = TrackAndTraceCompany.objects.create(company=self.test_company)
        self.railstation = RailStation.objects.create(lonlat="SRID=4326;POINT(-114.07 51.05)")
        self.yard.facility.railstation.add(self.railstation)
        self.yard.auto_incoming_car_type = "TANK"
        self.yard.auto_add_incoming_cars = True
        self.yard.save()

    def test_incoming_rail_cars_are_added_to_the_yard(self):
        rc = RailCar.objects.create(mark='ABC', number='9999')
        base_time = timezone.now()
        Trip.objects.create(
            car=rc,
            company=self.tnt_company,
            is_active=True, # Only searches for active trips
            destination=self.railstation,
            ##
            departure_timestamp=base_time + timedelta(days=2),
            eta_timestamp=base_time + timedelta(hours=3),
        )
        expectation_of_arrival_datetime = base_time + timedelta(hours=12)
        do_add_incoming_cars(base_time, expectation_of_arrival_datetime)

        res = self.apiclient.get(f"/api/yard_management/rail_yard/{self.yard.id}/cars/")
        self.assertEqual(200, res.status_code)
        car_res = res.json()
        self.assertEqual(2, len([x for x in car_res if x['name'] == "ABC9999"]))

    def test_incoming_cars_that_are_already_added_are_skipped(self):
        res = self.apiclient.get(f"/api/yard_management/rail_yard/{self.yard.id}/cars/")
        def _t(car):
            return (
                car['scenario_id'],
                car['track_id'],
                car['current_position'],
                car['rail_car_id'],
            )
        original_cars = {_t(c) for c in res.json()}

        # Car number that is already in the yard
        rc = RailCar.objects.create(mark='ABC', number='0001')
        base_time = timezone.now()
        Trip.objects.create(
            car=rc,
            company=self.tnt_company,
            is_active=True, # Only searches for active trips
            destination=self.railstation,
            ##
            departure_timestamp=base_time + timedelta(days=2),
            eta_timestamp=base_time + timedelta(hours=3),
        )
        expectation_of_arrival_datetime = base_time + timedelta(hours=12)
        do_add_incoming_cars(base_time, expectation_of_arrival_datetime)

        res = self.apiclient.get(f"/api/yard_management/rail_yard/{self.yard.id}/cars/")
        self.assertEqual(200, res.status_code)
        car_res = res.json()
        self.assertEqual(original_cars, {_t(c) for c in car_res})


    def test_flag_stops_import(self):
        res = self.apiclient.get(f"/api/yard_management/rail_yard/{self.yard.id}/cars/")
        def _t(car):
            return (
                car['scenario_id'],
                car['track_id'],
                car['current_position'],
                car['rail_car_id'],
            )
        original_cars = {_t(c) for c in res.json()}

        # Turn off the import flag
        self.yard.auto_add_incoming_cars = False
        self.yard.save()

        # Car number is OK for import
        rc = RailCar.objects.create(mark='ABC', number='9999')
        base_time = timezone.now()
        Trip.objects.create(
            car=rc,
            company=self.tnt_company,
            is_active=True, # Only searches for active trips
            destination=self.railstation,
            ##
            departure_timestamp=base_time + timedelta(days=2),
            eta_timestamp=base_time + timedelta(hours=3),
        )
        expectation_of_arrival_datetime = base_time + timedelta(hours=12)
        do_add_incoming_cars(base_time, expectation_of_arrival_datetime)

        # The original cars should also be the same as before
        res = self.apiclient.get(f"/api/yard_management/rail_yard/{self.yard.id}/cars/")
        self.assertEqual(200, res.status_code)
        car_res = res.json()
        self.assertEqual(original_cars, {_t(c) for c in car_res})

    def test_no_car_type_set_stops_import(self):
        res = self.apiclient.get(f"/api/yard_management/rail_yard/{self.yard.id}/cars/")
        def _t(car):
            return (
                car['scenario_id'],
                car['track_id'],
                car['current_position'],
                car['rail_car_id'],
            )
        original_cars = {_t(c) for c in res.json()}

        # Import flag is on, but no car type set
        self.yard.auto_incoming_car_type = None
        self.yard.auto_add_incoming_cars = True
        self.yard.save()

        # Car number is OK for import
        rc = RailCar.objects.create(mark='ABC', number='9999')
        base_time = timezone.now()
        Trip.objects.create(
            car=rc,
            company=self.tnt_company,
            is_active=True, # Only searches for active trips
            destination=self.railstation,
            ##
            departure_timestamp=base_time + timedelta(days=2),
            eta_timestamp=base_time + timedelta(hours=3),
        )
        expectation_of_arrival_datetime = base_time + timedelta(hours=12)
        do_add_incoming_cars(base_time, expectation_of_arrival_datetime)

        # The original cars should also be the same as before
        res = self.apiclient.get(f"/api/yard_management/rail_yard/{self.yard.id}/cars/")
        self.assertEqual(200, res.status_code)
        car_res = res.json()
        self.assertEqual(original_cars, {_t(c) for c in car_res})

    def test_the_correct_car_type_is_added(self):
        # When the car type is TANK
        self.yard.auto_incoming_car_type = "TANK"
        self.yard.save()

        # Car number is OK for import
        rc = RailCar.objects.create(mark='ABC', number='9999')
        base_time = timezone.now()
        Trip.objects.create(
            car=rc,
            company=self.tnt_company,
            is_active=True, # Only searches for active trips
            destination=self.railstation,
            ##
            departure_timestamp=base_time + timedelta(days=2),
            eta_timestamp=base_time + timedelta(hours=3),
        )
        expectation_of_arrival_datetime = base_time + timedelta(hours=12)
        do_add_incoming_cars(base_time, expectation_of_arrival_datetime)

        # The original cars should also be the same as before
        res = self.apiclient.get(f"/api/yard_management/rail_yard/{self.yard.id}/cars/")
        self.assertEqual(200, res.status_code)
        car_res = res.json()
        car_ids = {c['rail_car_id'] for c in car_res if c['name'] == 'ABC9999'}
        self.assertEqual(1, len(car_ids))
        lrc = LoadedRailCar.objects.get(pk=list(car_ids)[0])
        self.assertTrue(isinstance(lrc, TankCar))

