import copy
import time
from collections import defaultdict
from io import BytesIO

import pandas as pd
from django.template.loader import get_template
from openpyxl import styles as openpyxl_styles
from openpyxl.utils import get_column_letter

from vantedge.printables.pdf import PDFKitGenerator

from ..models import CarPosition, RailYard, Track


def time_it(func):
    def wrapper(*args, **kwargs):
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()
        print(f"Function '{func.__name__}' executed in {end_time - start_time:.6f} seconds")
        return result
    return wrapper

class ExportUtils:
    """
    This class houses the functions for exporting rail yards to csv & pdf.
    """
    @staticmethod
    def get_export_volume(car_pos):
        volume = car_pos.get_active_volume() if car_pos.use_trip_data() else car_pos.get_volume()
        if volume is None:
            volume = ""
        return volume

    @staticmethod
    def get_export_weight(car_pos):
        weight = car_pos.get_active_weight() if car_pos.use_trip_data() else car_pos.get_weight()
        if weight is None:
            weight = ""
        return weight

    @staticmethod
    def create_track_df(track: Track, car_positions: list[int, CarPosition]):  # noqa: FBT001, FBT002
        column_lookup = {
            "product_color": lambda cp: cp.product_display_color(),
            "POS": None,
            "CAR": lambda cp: cp.rail_car,  # RailCar.__str__()
            "PRODUCT": lambda cp: cp.trip_product_name() if cp.use_trip_data() else cp.product_name(),
            "CUSTOMER": lambda cp: cp.trip_customer_name() if cp.use_trip_data() else cp.customer_name(),
            "ORIGIN": lambda cp: cp.trip_origin_name() if cp.use_trip_data() else cp.origin_name(),
            "DESTINATION": lambda cp: cp.trip_destination_name() if cp.use_trip_data() else cp.destination_name(),
            "RECEIVED DATE": lambda cp: cp.trip_received_date() if cp.use_trip_data() else cp.received_date(),
            "WEIGHT": lambda cp: ExportUtils.get_export_weight(cp),
            "VOLUME": lambda cp: ExportUtils.get_export_volume(cp),
            "DENSITY": lambda cp: cp.get_density() if cp.get_density() is not None else "",
            "STATUS": lambda cp: cp.rail_car.status,
            "LOADED": lambda cp: "L" if cp.is_loaded() else "E",
        }

        columns = list(column_lookup.keys())
        car_position_dict = {cp.track_position: cp for cp in car_positions}
        data = [
            [(i + 1) if col == "POS" else column_lookup[col](car_position_dict[i]) if i in car_position_dict else ""
            for col in columns]
            for i in range(track.capacity)
        ]
        track_df = pd.DataFrame(data, columns=columns)
        if not track.car_position_ascending:
            track_df = track_df[::-1].reset_index(drop=True)
        return track_df

    @staticmethod
    def force_rounding(track_df):
        for col in ["WEIGHT", "VOLUME", "DENSITY"]:
                track_df[col] = track_df[col].apply(lambda x: f"{x:.3f}" if isinstance(x, (int, float)) else x)
        return track_df

    @staticmethod
    def apply_styles(track_df):
        product_colors = track_df["product_color"].copy()
        track_df = track_df.drop(columns=["product_color"])
        styled_df = (
            track_df.style
            .apply(lambda df: ExportUtils.highlight_product_col(df, product_colors), axis=None)
            .apply(ExportUtils.color_loaded_car, axis=None)
        )
        return styled_df

    @staticmethod
    def highlight_product_col(df, product_colors):
        styles = pd.DataFrame("", index=df.index, columns=df.columns)
        exclude_columns = {"POSITION", "STATUS", "LOADED"}
        for index, color in enumerate(product_colors):
            if not color:
                continue
            for column in df.columns:
                if column not in exclude_columns:
                    styles.loc[index, column] = f"background-color: {color}"

        return styles

    @staticmethod
    def color_loaded_car(df):
        styles = pd.DataFrame("", index=df.index, columns=df.columns)
        loaded_style = "background-color: green"
        not_loaded_style = "background-color: orange"
        styles["LOADED"] = [
            loaded_style if is_loaded == "L" else (not_loaded_style if is_loaded == "E" else "")
            for is_loaded in df["LOADED"]
        ]
        return styles

    @staticmethod
    def get_export_data(track_ids: str, rail_yard: RailYard) -> list:  # noqa: FBT001, FBT002
        track_ids_list = [int(track_id) for track_id in track_ids.split(",")]
        scenario = rail_yard.get_scenario_current()
        tracks = list(Track.objects.filter(id__in=track_ids_list).order_by("sort_order"))
        car_positions = scenario.get_carpositions_ordered().filter(
            track__in=track_ids_list
        ).prefetch_related("rail_car", "rail_car__incoming_trip", "rail_car__rail_shipment")

        car_positions_by_track = defaultdict(list)
        for cp in car_positions:
            car_positions_by_track[cp.track_id].append(cp)

        track_data = [
            (track, ExportUtils.create_track_df(track, car_positions_by_track[track.id]))
            for track in tracks
        ]
        return track_data

    @staticmethod
    def generate_pdf(track_ids: str, rail_yard: RailYard):
        track_data = ExportUtils.get_export_data(track_ids, rail_yard)  # noqa: FBT003
        tracks_html = []
        max_rows_per_page = 41

        def split_and_render(track_df, track_code):
            """
            Split the dataframe and return HTML with page breaks.
            This prevents a table from going across multiple pages.
            """
            result_html = []
            if len(track_df) <= max_rows_per_page: # base case
                track_df = ExportUtils.force_rounding(track_df)
                styled_df = ExportUtils.apply_styles(track_df)
                result_html.append((styled_df.hide(axis="index").to_html(), track_code))
                return result_html

            split_point = max_rows_per_page
            df_part = track_df.iloc[:split_point].reset_index(drop=True)
            df_part = ExportUtils.force_rounding(df_part)
            styled_df_part = ExportUtils.apply_styles(df_part)
            result_html.append((styled_df_part.hide(axis="index").to_html(), track_code ))
            result_html.append(('<div style="page-break-before: always;"></div>', ""))
            result_html.extend(split_and_render(track_df.iloc[split_point:].reset_index(drop=True), ""))
            return result_html

        for track, track_df in track_data:
            result_html = split_and_render(track_df, track.code)
            result_html.append(('<div style="page-break-before: always;"></div>', ""))
            tracks_html.extend(result_html)

        if tracks_html and tracks_html[-1][0] == '<div style="page-break-before: always;"></div>':
            tracks_html.pop()

        template = get_template("yard_management/export.html")
        html = template.render({
            "tracks_html": tracks_html,
        })
        pdf = PDFKitGenerator(
            main_html=html
        ).render_pdf()
        return pdf


    @staticmethod
    def generate_excel(track_ids: str, rail_yard: RailYard):
        track_data = ExportUtils.get_export_data(track_ids, rail_yard)
        center_align = openpyxl_styles.Alignment(horizontal="center", vertical="center")
        thick = openpyxl_styles.Side(border_style=openpyxl_styles.borders.BORDER_THICK)
        thin = openpyxl_styles.Side(border_style=openpyxl_styles.borders.BORDER_THIN)
        thin_border = openpyxl_styles.Border(
            left=thin, right=thin, top=thin, bottom=thin,
        )
        thick_border = openpyxl_styles.Border(
            left=thick, right=thick, top=thick, bottom=thick,
        )
        excel_output = BytesIO()
        col_offset = 0
        title_row = 1
        with pd.ExcelWriter(excel_output, engine="openpyxl") as writer:
            for track, track_df in track_data:
                styled_df = ExportUtils.apply_styles(track_df)
                styled_df.to_excel(
                    writer, index=False, startrow=title_row, startcol=col_offset, sheet_name="Yard Layout"
                )
                worksheet = writer.sheets["Yard Layout"]
                start_col = col_offset + 1
                col_offset += len(styled_df.data.columns)
                end_col = col_offset
                title = track.code
                worksheet.cell(
                    row=title_row,
                    column=start_col,
                    value=title
                )
                worksheet.merge_cells(
                    start_row=title_row,
                    start_column=start_col,
                    end_row=title_row,
                    end_column=end_col
                )
                worksheet.cell(row=title_row, column=start_col).font = openpyxl_styles.Font(bold=True)
                for col in range(start_col, end_col + 1):
                    col_title = str(worksheet.cell(row=title_row + 1, column=col).value)
                    max_length = len(col_title)
                    row_count = len(styled_df.data) + title_row + 1
                    for row in range(title_row + 2, row_count + 1):  # Start from first data row
                        cell_value = str(worksheet.cell(row=row, column=col).value or "")
                        max_length = max(max_length, len(cell_value))
                    padding = 1
                    worksheet.column_dimensions[get_column_letter(col)].width = max_length + padding
                    border = copy.deepcopy(thin_border)
                    border.left = thick if col == start_col else thin
                    border.right = thick if col == end_col else thin
                    for row in range(title_row, row_count + 1):
                        cell = worksheet.cell(row=row, column=col)
                        cell.alignment = center_align
                        cell.border = border if row != title_row else thick_border
                        if row == row_count:
                            bottom_border = copy.deepcopy(border)
                            bottom_border.bottom = thick
                            cell.border = bottom_border
        excel_output.seek(0)
        return excel_output
