import logging
import re

from django_filters import FilterSet, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from django.db.models.functions import <PERSON><PERSON>, Cast
from django.db.models import <PERSON>, <PERSON><PERSON><PERSON><PERSON>, IntegerField

from vantedge.trackandtrace.models import Trip
from vantedge.yard_management.models import CarPosition
from vantedge.wheelhouse.models import LoadedRailCar
from ..models import RailCarInspectionForm


logger = logging.getLogger(__name__)

class RailCarInspectionPlanFilter(FilterSet):
    facility = CharFilter(method="get_from_facility")

    def get_from_facility(self, queryset, name, value):
        if value:
            queryset = queryset.filter(facility__id=value)
        return queryset

class LoadedRailCarFilter(FilterSet):
    facility = CharFilter(method="get_from_facility")
    car = CharFilter(method="car_filter")
    car__in = CharFilter(method="car_filter_in")
    car__exclude = CharFilter(method="car_filter_exclude")
    rail_shipment__reference_number = Char<PERSON>ilt<PERSON>(method="shipment_filter")
    rail_shipment__reference_number__in = CharFilter(method="shipment_filter_in")
    rail_shipment__reference_number__exclude = CharFilter(method="shipment_filter_exclude")
    inspection = CharFilter(method="inspection_filter")
    show_historic_data = BooleanFilter(method="historic_data_filter")
    status = CharFilter(method="status_filter")
    status__in = CharFilter(method="status_filter_in")
    status__exclude = CharFilter(method="status_filter_exclude")

    def get_from_facility(self, queryset, name, value):
        return queryset.filter(current_location__id=value)

    def historic_data_filter(self, queryset, name, value):
        if value is False:
            return queryset.filter(rail_shipment__isnull=True)
        return queryset

    def car_filter(self, queryset, name, value):
        car_matches = re.compile("([a-zA-Z]*)\s*(\d*)").match(value)
        mark = car_matches.group(1).upper()
        number = car_matches.group(2)
        if mark:
            queryset = queryset.filter(car_mark__contains=mark)
        if number:
            queryset = queryset.filter(car_number__contains=number)
        return queryset

    def car_filter_in(self, queryset, name, value):
        values = value.split(",")
        qs = LoadedRailCar.objects.none()
        for value in values:
            qs |= self.car_filter(queryset, name, value)
        return qs

    def car_filter_exclude(self, queryset, name, value):
        cars_included = self.car_filter_in(queryset, name, value)
        return queryset.exclude(id__in=cars_included.values_list("id", flat=True))

    def shipment_filter(self, queryset, name, value):
        return queryset.filter(rail_shipment__reference_number=value)

    def shipment_filter_in(self, queryset, name, value):
        values = value.split(",")
        qs = LoadedRailCar.objects.none()
        for value in values:
            qs |= self.shipment_filter(queryset, name, value)
        return qs

    def shipment_filter_exclude(self, queryset, name, value):
        filter_in = self.shipment_filter_in(queryset, name, value)
        return queryset.exclude(id__in=filter_in.values_list("id", flat=True))

    def inspection_filter(self, queryset, name, value):
        form_name, filter_type = value.split("-")
        inspection_forms = RailCarInspectionForm.objects.filter(railcar__in=queryset)
        if filter_type == "Passed":
            inspection_forms = inspection_forms.filter(form_data__form__name=form_name, form_data__is_ok=True)
            queryset = queryset.filter(id__in=inspection_forms.values_list("railcar", flat=True))
        elif filter_type == "Not Passed":
            inspection_forms = inspection_forms.filter(form_data__form__name=form_name).filter(Q(form_data__is_ok=False) | Q(form_data__is_ok__isnull=True))
            queryset = queryset.filter(id__in=inspection_forms.values_list("railcar", flat=True))
        elif filter_type == "Empty":
            inspection_forms = inspection_forms.filter(form_data__form__name=form_name)
            queryset = queryset.exclude(id__in=inspection_forms.values_list("railcar", flat=True))
        else:
            return queryset

        return queryset

    def status_filter(self, queryset, name, value):
        return queryset.filter(status=value)

    def status_filter_in(self, queryset, name, value):
        values = value.split(",")
        return queryset.filter(status__in=values)

    def status_filter_exclude(self, queryset, name, value):
        values = value.split(",")
        return queryset.exclude(status__in=values)

def get_incoming_cars(facility, now, expectation_of_arrival_datetime):
    rail_stations = facility.railstation.all()
    log = logger.getChild("get_incoming_cars")
    log.info("start: facility=%s facility_id=%d from=%s to=%s", facility, facility.id, now, expectation_of_arrival_datetime)
    railyard_cars = (
        CarPosition.objects
        .filter(scenario__rail_yard__facility=facility)
        .annotate(car_number_int=Cast("rail_car__car_number", IntegerField()))
        .annotate(railroad_car=Concat("rail_car__car_mark", Cast("car_number_int", output_field=CharField())))
        .values_list("railroad_car", flat=True)
    )
    log.info("Existing cars: %s", railyard_cars)
    trips = (
        Trip.objects.filter(
            company__company= facility.company.company,
            is_active=True,
            destination__in=rail_stations
        )
        .filter(
            (Q(eta_timestamp__gt = now) |
            Q(original_eta_timestamp__gt = now)),
            Q(eta_timestamp__lt = expectation_of_arrival_datetime) |
            Q(original_eta_timestamp__lt = expectation_of_arrival_datetime)
        )
    )
    log.info("Incoming trips: %s", trips)
    trips = (
        trips
        .annotate(car_number_int=Cast("car__number", IntegerField()))
        .annotate(railroad_car=Concat("car__mark", Cast("car_number_int", output_field=CharField())))
        .exclude(
            railroad_car__in=railyard_cars
        )
    )
    log.info("trips after filtering: %s", trips)
    return trips
