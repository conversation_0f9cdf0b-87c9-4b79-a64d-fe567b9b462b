from django.db.models.functions import Concat
from django.db.models import <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, When
from django.db.models.deletion import ProtectedError
from django.db import transaction
from django.core.exceptions import MultipleObjectsReturned

from rest_framework.pagination import LimitOffsetPagination
from rest_framework.viewsets import GenericViewSet, ModelViewSet
from rest_framework.mixins import ListModelMixin, CreateModelMixin, RetrieveModelMixin, DestroyModelMixin, UpdateModelMixin
from rest_framework.permissions import IsAuthenticated
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework import status

import re

from vantedge.printables.views import PrintableViewset
from vantedge.wheelhouse.models import LoadedRailCar, Facility, HopperCar, TankCar
from ..models.inspection import RailCarInspectionPlan, RailCarInspectionForm
from vantedge.autocomplete.views import AutoCompleteViewSetMixin
from ..serializers import (
    RailCarInspectionPlanSerializer ,
    RailCarInspectionPlanCreateSerializer,
    RailCarInspectionFormSerializer,
    RailCarInspectionsSerializer,
    RailCarSerializer
    )

from .filters import LoadedRailCarFilter, RailCarInspectionPlanFilter

class RailCarInspectionPlanViewset(ModelViewSet):
    queryset = RailCarInspectionPlan.objects.all()
    serializer_class = RailCarInspectionPlanSerializer
    filterset_class = RailCarInspectionPlanFilter

    def get_queryset(self):
        qs = super().get_queryset()
        return qs.filter(facility__company__company=self.request.user.company)

    def get_serializer_class(self):
        if self.action in ["create", "update", "partial_update"]:
            return RailCarInspectionPlanCreateSerializer
        return super().get_serializer_class()

    def destroy(self, request, pk):
        try:
            return super().destroy(request, pk)
        except ProtectedError:
            return Response(["You cant delete this plan, there is dependencies"], status=status.HTTP_400_BAD_REQUEST)
        # obj = self.get_object()
        # if RailCarInspectionForm.objects.filter(form_data__form=obj.form).exists():
        #     return Response(["There is car inspections based on this plan"], status=status.HTTP_400_BAD_REQUEST)
        # super().destroy(request, pk)

class RailCarInspectionFormViewset(PrintableViewset):
    queryset = RailCarInspectionForm.objects.all().order_by("id")
    serializer_class = RailCarInspectionFormSerializer

    def get_queryset(self):
        qs = super().get_queryset().select_related()
        return qs.filter(
            form_data__form__company__company=self.request.user.company,
        )

    def list(self, request, *args, **kwargs):
        return Response()

    @transaction.atomic
    def destroy(self, request, pk=None):
        inspection = self.get_object()
        railcar = inspection.railcar
        form = inspection.form_data.form

        post_requirement = RailCarInspectionPlan.objects.filter(
            facility=railcar.current_location,
            pre_requirement__form=form).values_list("form", flat=True)

        if linked_inspections := railcar.inspection_forms.filter(form_data__form__in=post_requirement):
            form_names = list(linked_inspections.values_list("form_data__form__name", flat=True))
            return Response([f"You need to delete {', '.join(form_names)}"], status=status.HTTP_400_BAD_REQUEST)

        plan = RailCarInspectionPlan.objects.filter(
            facility=railcar.current_location,
            form=form,
        ).first()

        if plan.pre_requirement:
            railcar.status = plan.pre_requirement.status
            # departure form passed, status -> "ready to ship"
            # departure form deleted, previouse status is "Ready To Load" but the car already loaded
            if railcar.status == "Ready To Load" and railcar.product:
                railcar.status = railcar.current_location.rail_car_status_when_loaded
        else:
            if railcar.product:
                railcar.status = railcar.current_location.rail_car_status_when_loaded
            else:
                railcar.status = railcar.current_location.rail_car_initial_status

        railcar.save(update_fields=["status"])
        return super().destroy(request, pk)

    @action(methods=["GET"], detail=False)
    def car_data(self, request):
        car = LoadedRailCar.objects.filter(
            current_location__company__company= request.user.company,
            id=request.query_params.get("railcar_id", 0)
        ).first()

        if not car:
            return Response(status=status.HTTP_404_NOT_FOUND)

        return Response(RailCarSerializer(car).data)

    @action(methods=["POST"], detail=False)
    def create_railcars(self, request):
        car_instances = []
        cars_data = request.data.get("rail_cars", "")
        facility_id = request.data.get("facility_id", None)

        car_type = request.data.get("car_type", None)
        if car_type not in ["tank car", "hopper car"]:
            return Response(["Car type is not valid"], status=status.HTTP_400_BAD_REQUEST)

        if car_type == 'hopper car':
            car_model = HopperCar
        elif car_type == 'tank car':
            car_model = TankCar

        facility = Facility.objects.filter(
            company__company = request.user.company,
            id=facility_id
        ).first()

        if not facility:
            return Response(["facility not found"], status=status.HTTP_400_BAD_REQUEST)

        cleaned_cars = []
        cars = cars_data.replace(" ", "").split(",")

        for car in cars:
            car_matches = re.compile("([a-zA-Z]*)\s*(\d*)").match(car)
            mark = car_matches.group(1).upper()
            number = car_matches.group(2)

            if len(mark) < 2 or len(mark) > 4 or len(number) < 4 or len(number) > 6:
                return Response([f"Car not accepted {car}"], status=status.HTTP_400_BAD_REQUEST)

            if car_type == 'hopper car':
                cleaned_cars.append(
                    dict(
                        car_mark=mark,
                        car_number=number,
                        current_location=facility,
                        weight=0,
                        rail_shipment=None,
                        status=facility.rail_car_initial_status
                    )
                )
            else:
                cleaned_cars.append(
                    dict(
                        car_mark=mark,
                        car_number=number,
                        current_location=facility,
                        rail_shipment=None,
                        status=facility.rail_car_initial_status
                    )
                )

        for car in cleaned_cars:
            try:
                instance, created = car_model.objects.get_or_create(**car)
            except MultipleObjectsReturned:
                return Response([f"more than one instance of {car['car_mark']}{car['car_number']} in facility"], status=status.HTTP_400_BAD_REQUEST)
            car_instances.append(instance)

        return Response([car.id for car in car_instances])


class RailCarInspectionListViewset(AutoCompleteViewSetMixin, GenericViewSet, ListModelMixin):
    queryset = LoadedRailCar.objects.all()
    serializer_class = RailCarInspectionsSerializer
    filterset_class = LoadedRailCarFilter
    # pagination_class = LimitOffsetPagination


    def get_queryset(self):
        qs = LoadedRailCar.objects.prefetch_related(
                "inspection_forms"
            ).select_related(
                "rail_shipment"
            ).filter(current_location__company__company=self.request.user.company)

        return self.filter_queryset(
                qs
            ).annotate(relevance=Case(
                When(status="Arrived", then=1),
                When(status="Under Inspection", then=2),
                When(status="Ready To Load", then=3),
                When(status="Loaded", then=4),
                When(status="Ready To Ship", then=5),
                When(status="Shipped", then=6)
                )
            ).order_by("relevance", "-id").distinct("relevance", "id")

    @action(detail=False, methods=["GET"], permission_classes=[IsAuthenticated])
    def autocomplete(self, request):
        field_name = self.request.query_params.get("field")
        user_input = self.request.query_params.get("input")
        limit = int(self.request.query_params.get("limit", 15))

        if field_name == "car":
            qs = self.get_queryset()

            if user_input and len(user_input):
                car_matches = re.compile("([a-zA-Z]*)\s*(\d*)").match(user_input)
                mark = car_matches.group(1).upper()
                number = car_matches.group(2)
                if mark:
                    qs = qs.filter(car_mark__contains=mark)
                if number:
                    qs = qs.filter(car_number__contains=number)

            qs = (
                qs.values_list("car_mark", "car_number")
                .annotate(
                    car_reference=Concat(
                        F("car_mark"), F("car_number"), output_field=CharField()
                    )
                )
                .annotate(field_count=Count("car_reference"))
                .order_by("-field_count")
                .values_list("car_reference", flat=True)
            ).distinct()

            count = qs.count()
            suggestions = qs[:limit]

            return Response({"count": count, "results": suggestions})

        elif field_name == 'inspection':
            suggestions = ["Passed", "Empty", "Not Passed"]
            return Response({"count": 3, "results": suggestions})

        else:
            return super().autocomplete(request)


    def list(self, request):
        limit = request.query_params.get("limit", 50)
        offset = request.query_params.get("offset", 0)
        try:
            limit = int(limit)
            offset = int(offset)
            if limit > 1000:
                raise
        except:
            return Response(status=status.HTTP_400_BAD_REQUEST)

        railcars = self.get_queryset()[offset: limit+offset]
        return Response(RailCarInspectionsSerializer(railcars, many=True).data)

    @action(detail=False, methods=["GET"])
    def count(self, request):
        count = self.get_queryset().count()
        return Response(count)
