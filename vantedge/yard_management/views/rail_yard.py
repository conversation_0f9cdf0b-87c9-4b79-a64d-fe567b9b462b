import logging
import json

from django.core.exceptions import PermissionDenied
from django.contrib.contenttypes.models import ContentType
from django.db import transaction
from django.db.models import <PERSON>, <PERSON>, OuterR<PERSON>, Ex<PERSON>, <PERSON><PERSON><PERSON><PERSON>
from django.db.models.functions import Concat, Cast
from django.db.utils import IntegrityError
from django.http.response import HttpResponse, HttpResponseBadRequest
from django.utils import timezone
from pydantic.error_wrappers import ValidationError
from rest_framework import status
from rest_framework.decorators import action
from rest_framework.pagination import PageNumberPagination
from rest_framework.response import Response
from rest_framework.viewsets import ModelViewSet

from vantedge.util.cars import parse_car_id
from vantedge.wheelhouse.models import HopperCar, LoadedRailCar, TankCar, RailShipment
from vantedge.users.models import UserNote

from ..models import CarPosition, RailYard, Track
from ..models.rail_yard import (
    Move,
    MoveExecution,
    MoveRequest,
)
from ..serializers.rail_yard import (
    CarPositionSerializer,
    CarPositionDetailsSerializer,
    MoveExecutionSerializer,
    RailYardSerializer,
    ScenarioSerializer,
)
from ..utils.export_utils import ExportUtils
from functools import wraps
from rest_framework.views import APIView

logger = logging.getLogger(__name__)

def _update_scenario(request, scenario):
    with transaction.atomic():
        count, _ = scenario.carposition_set.all().delete()
        for carpos in request.data["cars"]:
            CarPosition.objects.create(
                rail_car_id=carpos["rail_car_id"],
                scenario=scenario,
                track=Track.objects.filter(pk=carpos["track_id"]).first(),
                track_position=carpos["current_position"],
            )
        return Response({"update_count": count})

def update_rail_yard_cache(func):
    """
    Decorator to update the cache when the response status code is in the 200 range.
    """
    @wraps(func)
    def wrapper(self, request, *args, **kwargs):
        if request.GET.get("checkOnly"):
            return func(self, request, *args, **kwargs)
        last_update = request.data.get("last_update")
        pk = kwargs.get("pk")
        if last_update != RailYard.get_last_update(pk):
            error_msg = (
                "RAILYARD SYNC ERROR"
                )
            return HttpResponseBadRequest(error_msg)
        response = func(self, request, *args, **kwargs)
        if 200 <= response.status_code < 300:  # noqa: PLR2004
            RailYard.set_last_update(pk)
        return response
    return wrapper

class RailYardCacheView(APIView):

    def get(self, request):
        pk = request.query_params.get("yard_id")
        if not pk:
            return Response({"error": "yard_id is required"}, status=status.HTTP_400_BAD_REQUEST)
        return Response({
            "last_update": RailYard.get_last_update(yard_id=pk),
            "last_update_details": RailYard.get_last_update_details(yard_id=pk),
        })

class YardLayoutEnabledMixin:
    def check_permissions(self, request):
        ym = getattr(request.user.company, 'yard_management', None)
        perm = None
        if ym:
             perm = ym.available_permissions.filter(codename="view_railyard").first()
        if not perm:
             raise PermissionDenied()
        return super().check_permissions(request)

class MoveHistoryPagination(PageNumberPagination):
    max_page_size = 100
    page_size=25
    page_query_param = 'page'
    page_size_query_param = 'rowsPerPage'

class RailYardViewSet(YardLayoutEnabledMixin, ModelViewSet):
    queryset = RailYard.objects.all()
    serializer_class = RailYardSerializer
    car_types = {
        'TANK_CAR': TankCar,
        'HOPPER_CAR': HopperCar,
    }


    @action(detail=True, methods=["GET"], serializer_class=CarPositionSerializer)
    def cars(self, request, pk):
        yard = self.get_object()

        cars = CarPosition.objects.filter(scenario__rail_yard=yard).prefetch_related(
            "rail_car",
        )
        return Response(CarPositionSerializer(cars, many=True).data)

    @action(detail=True, methods=["GET"], serializer_class=CarPositionSerializer)
    def car_details(self, request, pk):
        yard = self.get_object()

        railcar_ct = ContentType.objects.get_for_model(LoadedRailCar)

        cars = CarPosition.objects.filter(scenario__rail_yard=yard).prefetch_related(
        "rail_car",
        "rail_car__incoming_trip",
        "rail_car__rail_shipment",
        ).annotate(
            rail_car_id_str=Cast('rail_car_id', CharField())
        )

        note_exists_subquery = UserNote.objects.filter(
            content_type=railcar_ct,
            object_id=OuterRef('rail_car_id_str'),
        )

        cars = cars.annotate(
            has_notes=Exists(note_exists_subquery)
        )

        return Response(CarPositionDetailsSerializer(cars, many=True).data)

    @action(detail=True, methods=["GET", "PUT"], serializer_class=ScenarioSerializer)
    def current_scenario(self, request, pk):
        yard = self.get_object()
        scn = yard.get_scenario_current()
        if request.method == "GET":
            return Response(ScenarioSerializer(scn).data)
        elif request.method == "PUT":
            return _update_scenario(request, scn)

    @action(detail=True, methods=["GET", "PUT"], serializer_class=ScenarioSerializer)
    def planned_scenario(self, request, pk):
        yard = self.get_object()
        plan = yard.get_scenario_planned()
        if request.method == "GET":
            return Response(ScenarioSerializer(plan).data)
        elif request.method == "PUT":
            if yard.use_autocomplete:
                cur = yard.get_scenario_current()
                _update_scenario(request, cur)
                return _update_scenario(request, plan)
            else:
                plan = yard.get_scenario_planned()
                return _update_scenario(request, plan)

    @action(detail=True, methods=["GET"])
    def switch_list(self, request, pk):
        obj = self.get_object()
        from_scn = obj.get_scenario_current()
        to_scn = obj.get_scenario_planned()
        switch_list = to_scn.switch_list_from(from_scn)
        return Response(switch_list)

    @action(detail=True, methods=["POST"])
    def reset_layout(self, request, pk):
        obj = self.get_object()
        plan = obj.get_scenario_planned()
        #remove all planned car positions
        CarPosition.objects.filter(scenario=plan).delete()
        to_add = []
        for car in obj.get_scenario_current().carposition_set.all():
            to_add.append(CarPosition(scenario=plan, rail_car=car.rail_car, track_position=car.track_position,track=car.track))
        CarPosition.objects.bulk_create(to_add)
        # return the new set of cars
        cars = (
            CarPosition.objects
            .filter(scenario__rail_yard_id=pk)
            .select_related(
                'track',
                'scenario',
                'rail_car',
                'rail_car__product',
            )
        )
        return Response(CarPositionSerializer(cars, many=True).data)


    @action(detail=True, methods=["POST"])
    def add_cars(self, request, pk):
        RailYard.set_last_update(pk)
        railyard = self.get_object()
        car_tags = request.data["car_marks"]
        req_car_type = request.data.get('car_type', None)
        car_class = self.car_types.get(req_car_type, None)
        if not car_class:
            return Response(
                {"error": "bad car_type, must be 'TANK_CAR' or 'HOPPER_CAR'"},
                status=status.HTTP_400_BAD_REQUEST
            )
        logger.debug(f"car_tags: {car_tags}")
        car_ids = [parse_car_id(t) for t in car_tags]

        # Ensure that our cars dont already exist *anywhere* in the layout
        existing_car_positions = CarPosition.objects.filter(scenario__rail_yard=railyard).annotate(
            car=Concat(F("rail_car__car_mark"), F("rail_car__car_number"))
        )
        existing_car_positions = {e.car for e in existing_car_positions}

        last_position=0
        events = []
        for rc in car_ids:
            if rc.car_id in existing_car_positions:
                events.append(dict(action="skipped", car=rc.car_id, reason="Already in Yard"))
                continue
            last_position += 1
            logger.debug(f'Add car {rc.car_id} at pos: {last_position}')
            events.append(dict(action="add", car=rc.car_id, position=last_position))
            try:
                tc = LoadedRailCar.objects.get(
                    ~Q(status=LoadedRailCar.LoadedRailCarStatus.SHIPPED),
                    (Q(rail_shipment__isnull=True) | ~Q(rail_shipment__status__in=RailShipment.CLOSED_STATUSES)),
                    current_location=railyard.facility,
                    car_mark=rc.mark,
                    car_number=rc.number,
                )
                logger.info(f"CarPosition: create: Found existing car id={tc.id}")
            except LoadedRailCar.DoesNotExist:
                tc, created = car_class.objects.get_or_create(
                    car_mark=rc.mark,
                    car_number=rc.number,
                    current_location=railyard.facility,
                    rail_shipment=None,
                    defaults=dict(
                        status=railyard.facility.rail_car_initial_status,
                    )
                )
                logger.info(f"CarPosition: create: Making new LoadedRailCar: {tc} id={tc.id}")
            for scn in railyard.scenario_set.all():
                cp = CarPosition.objects.create(
                    rail_car=tc,
                    track_position=None,
                    scenario=scn,
                    track=None,
                )
                logger.info(f"CarPosition: create: {cp}")

            MoveExecution.objects.create(
                event_type=MoveExecution.EventTypes.ADDED,
                rail_yard=railyard,
                executed_by=request.user,
                executed_at=timezone.now(),
                car_mark=tc.car_mark,
                car_number=tc.car_number,
            )
        return Response(events)


    @staticmethod
    def get_cars_to_shift(neighboring_cars, move, move_count):
        import time
        s1 = time.time()
        initial_positions = set(neighboring_cars.values_list("track_position", flat=True))
        stacked_positions = []
        if move.move_up:
            start = min(initial_positions)
            bump_amount = move_count - (start - move.track_position)
            if bump_amount <= 0:
                return neighboring_cars.none()
            for i in range(start, max(initial_positions) + 1, 1):
                if i in initial_positions:
                    stacked_positions.append(i)
                else:
                    bump_amount -= 1
                if bump_amount == 0:
                    break
        else:
            start = max(initial_positions)
            bump_amount = move_count - (move.track_position - start)
            if bump_amount <= 0:
                return neighboring_cars.none()
            for i in range(start, -1, -1):
                if i in initial_positions:
                    stacked_positions.append(i)
                else:
                    bump_amount -= 1
                if bump_amount == 0:
                    break
        shifted_cars = neighboring_cars.filter(track_position__in=stacked_positions)
        print(f"Time taken to get cars to shift: {time.time() - s1}")
        return shifted_cars

    @update_rail_yard_cache
    @action(detail=True, methods=["POST"])
    def move_cars(self, request, pk):
        try:
            move = MoveRequest(**request.data)
        except ValidationError as e:
            return HttpResponseBadRequest(e)
        # TODO(wnh) ensure the ids are actually in the yard/scenario
        move_count = len(move.railcar_ids)
        yard = self.get_object()
        to_track = Track.objects.get(pk=move.track_id)
        planned = yard.get_scenario_planned()
        move_direction = 1 if move.move_up else -1

        to_move_ids = set(move.railcar_ids)
        all_cars = (
            CarPosition.objects
            .filter(scenario__rail_yard_id=pk)
            .select_related(
                "track",
                "scenario",
                "rail_car",
                "rail_car__product",
            )
        )
        yard_cars = all_cars
        if not yard.use_autocomplete:
            yard_cars = yard_cars.filter(scenario=planned)
        loc = {rc_id: (move.railcar_ids.index(rc_id) * move_direction) for rc_id in move.railcar_ids}
        cars_to_move = [c  for c in yard_cars if c.rail_car_id in to_move_ids]
        cars_to_move_id = {c.id for c in yard_cars if c.rail_car_id in to_move_ids}

        # Get the cars that are above (move up) or below (move down) the car being moved
        position_filter = "track_position__gte" if move.move_up else "track_position__lte"
        ordering = "track_position" if move.move_up else "-track_position"
        neighboring_cars = (
            yard_cars.filter(
                track=move.track_id,
                **{position_filter: move.track_position}
            )
            .exclude(id__in=cars_to_move_id)
            .order_by(ordering)
        )

        if neighboring_cars.exists():
            shifted_cars = RailYardViewSet.get_cars_to_shift(
                neighboring_cars, move, move_count
            )
            # Add shifted cars to the list of cars to move
            shifted_cars_list = list(shifted_cars)
            cars_to_move.extend(shifted_cars_list)
            cars_to_move_id.update(c.id for c in shifted_cars_list)

            # Shift the cars
            next_loc = max(loc.values()) if move.move_up else min(loc.values())
            next_loc += move_direction
            for car in shifted_cars.distinct("track_position"):
                loc[car.rail_car_id] = next_loc
                next_loc += move_direction

        last_car_pos = max(loc.values()) + move.track_position + 1
        if move.move_up and (last_car_pos > to_track.capacity):
            return HttpResponseBadRequest("Not enough room at end of track!")
        if not move.move_up and (min(loc.values()) + move.track_position) < 0:
            return HttpResponseBadRequest("Not enough room at the start of the track")

        pos_to_add = []
        hist_to_add = []
        for old in cars_to_move:
            pos_to_add.append(CarPosition(
                scenario_id=old.scenario_id,
                rail_car_id=old.rail_car_id,
                track_id=move.track_id,
                track_position=move.track_position+loc[old.rail_car_id]
            ))
            # If autocomplete is on we're going to have two CarPositions
            # for every LoadedRailCar, one for the current scenario and one
            # for the planned scenario. We check for the planned scenario
            # ID to ensure we only add a single history record
            if yard.use_autocomplete and old.scenario_id == planned.id:
                hist_to_add.append(MoveExecution(
                    event_type=MoveExecution.EventTypes.MOVE,
                    rail_yard=yard,
                    executed_by=request.user,
                    executed_at=timezone.now(),
                    from_track_id=old.track_id,
                    car_mark=old.rail_car.car_mark,
                    car_number=old.rail_car.car_number,
                    from_position=old.track_position,
                    to_track_id=move.track_id,
                    to_position=move.track_position+loc[old.rail_car_id],
                ))
        CarPosition.objects.filter(pk__in=cars_to_move_id).delete()
        CarPosition.objects.bulk_create(pos_to_add)
        if yard.use_autocomplete:
            MoveExecution.objects.bulk_create(hist_to_add)
        cars = (
            CarPosition.objects
            .filter(scenario__rail_yard_id=pk)
            .select_related(
                "track",
                "scenario",
            ).prefetch_related(
                "rail_car",
                "rail_car__incoming_trip",
                "rail_car__rail_shipment",
                "rail_car__product",
            )
        )
        return Response(CarPositionSerializer(cars, many=True).data)

    @action(detail=True, methods=["POST"], serializer_class=MoveExecutionSerializer)
    def execute_move(self, request, pk):
        RailYard.set_last_update(pk)
        with transaction.atomic():
            yard = self.get_object()
            current = yard.get_scenario_current()
            move = Move(**request.data)
            to_track = yard.track_set.get(pk=move.to_pos.track_id)
            if move.from_pos:
                from_track = yard.track_set.get(pk=move.from_pos.track_id)
            # Validations
            if current.get_carpositions_ordered().filter(track=to_track).count() + 1 > to_track.capacity:
                return HttpResponseBadRequest("Too many cars in track")

            car, created = CarPosition.objects.get_or_create(
                rail_car_id=move.rail_car_id,
                scenario=current,
                defaults=dict(
                    track=to_track,
                    track_position=move.to_pos.position
                )
            )
            if created:
                logger.debug("Executing a move for new car")
            else:
                logger.debug("Didn't make a car")
            logger.debug(f"The Car: {car}")

            try:
                car.track=to_track
                car.track_position=move.to_pos.position
                car.save()
            except IntegrityError:
                return HttpResponseBadRequest("Unable to move car, that location is currently occupied")
            exe = MoveExecution(
                event_type=MoveExecution.EventTypes.MOVE,
                rail_yard=yard,
                executed_by=request.user,
                executed_at=timezone.now(),
                car_mark=car.rail_car.car_mark,
                car_number=car.rail_car.car_number,
            )
            if move.from_pos is not None:
                from_track = Track.objects.get(pk=move.from_pos.track_id)
                exe.from_track=from_track
                exe.from_position=move.from_pos.position

            if move.to_pos is not None:
                exe.to_track=to_track
                exe.to_position=move.to_pos.position
            exe.save()
            logger.debug(f"Move to be executed: {move}")
            logger.debug(f"Car to be moved: {car}")
            return Response(MoveExecutionSerializer(exe).data)

    # TODO(wnh) This should probably be done with a nested viewset but
    # I couldn't find a very clean way of making that work
    @action(detail=True, methods=["GET"], serializer_class=MoveExecutionSerializer)
    def move_history(self, request, pk):
        yard = self.get_object()
        moves = (
            MoveExecution.objects
                .filter(rail_yard=yard)
                .prefetch_related("executed_by")
                .order_by('-executed_at')
        )
        paginator = MoveHistoryPagination()
        page = paginator.paginate_queryset(moves, request)
        if page is not None:
            serializer = MoveExecutionSerializer(page, many=True)
            return paginator.get_paginated_response(serializer.data)
        serializer = MoveExecutionSerializer(moves, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=["GET"])
    def unassigned_cars(self, request, pk):
        yard = self.get_object()
        cars = CarPosition.objects.filter(
            track_position__isnull=True,
            track__isnull=True,
            scenario__rail_yard=yard,
        )
        return Response(CarPositionSerializer(cars, many=True).data)

    @update_rail_yard_cache
    @action(detail=True, methods=["POST"])
    def remove(self, request, pk):
        railyard = self.get_object()
        car_position_ids = request.data.get("car_ids")
        no_delete = []
        car_positions = CarPosition.objects.filter(
            scenario__rail_yard=railyard,
            pk__in=car_position_ids
        )
        for cp  in car_positions:
            if not cp.rail_car.can_delete():
                no_delete.append(dict(carposition_id=cp.id, rail_car_id=cp.rail_car.id))
        if len(no_delete) > 0:
            return HttpResponseBadRequest(json.dumps({
                "message": "Can't remove cars that have a shipment attached",
                "blocking_cars": no_delete,
            }), content_type="application/json")
        if request.GET.get("checkOnly"):
            return Response({})

        railcar_ids = {c.rail_car_id for c in car_positions}
        to_delete = CarPosition.objects.filter(
            scenario__rail_yard=railyard,
            rail_car_id__in=railcar_ids
        )
        resp = to_delete.delete()
        railcars = LoadedRailCar.objects.filter(pk__in=railcar_ids)
        for rc in railcars:
            MoveExecution.objects.create(
                event_type=MoveExecution.EventTypes.REMOVED,
                rail_yard=railyard,
                executed_by=request.user,
                executed_at=timezone.now(),
                car_mark=rc.car_mark,
                car_number=rc.car_number,
            )
        railcars.delete()
        return Response(resp)

    @action(detail=True, methods=["PUT"])
    def set_autocomplete(self, request, pk):
        yard = self.get_object()
        value = request.data.get("use_autocomplete")
        yard.set_autocomplete(value)
        return Response(value)


    @action(detail=True, methods=["GET"])
    def get_excel(self, request, pk):  # noqa: ARG002
        track_ids = request.query_params.get("tracks_ids", "")
        if not track_ids:
            error_message = "Failed to get excel. No tracks are selected"
            return HttpResponse(error_message, status=status.HTTP_404_NOT_FOUND)
        rail_yard = self.get_object()
        excel_file = ExportUtils.generate_excel(
            track_ids = track_ids,
            rail_yard=rail_yard
        )
        response = HttpResponse(
            excel_file.read(),
            content_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
        )
        response["Content-Disposition"] = "attachment;"
        return response

    @action(detail=True, methods=["GET"])
    def get_pdf(self, request, pk):  # noqa: ARG002
        track_ids = request.query_params.get("tracks_ids", "")
        if not track_ids:
            error_message = "Failed to get excel. No tracks are selected"
            return HttpResponse(error_message, status=status.HTTP_404_NOT_FOUND)
        rail_yard = self.get_object()
        pdf = ExportUtils.generate_pdf(
            track_ids = track_ids,
            rail_yard=rail_yard
        )
        return HttpResponse(pdf, content_type="application/pdf")


class CarPositionViewSet(YardLayoutEnabledMixin, ModelViewSet):
    queryset = CarPosition.objects.all()
    serializer_class = CarPositionSerializer
